#!/usr/bin/env python3
import sys
import os
import traceback

# Try to import PyPDF2 with version-specific handling
try:
    import PyPDF2
    print(f"Using PyPDF2 version {PyPDF2.__version__}")

    # Check PyPDF2 version to use the correct class
    if hasattr(PyPDF2, 'PdfMerger'):
        from PyPDF2 import PdfMerger
    elif hasattr(PyPDF2, 'PdfFileMerger'):
        from PyPDF2 import PdfFileMerger as PdfMerger
    else:
        print("ERROR: Could not find PdfMerger or PdfFileMerger in PyPDF2")
        sys.exit(1)
except ImportError as e:
    print(f"ERROR: Failed to import PyPDF2: {str(e)}")
    sys.exit(1)
except Exception as e:
    print(f"ERROR: Unexpected error importing PyPDF2: {str(e)}")
    traceback.print_exc()
    sys.exit(1)

def merge_pdfs(output_path, *input_paths):
    print(f"Merging {len(input_paths)} PDF files to {output_path}")

    # Check if output directory exists
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            print(f"Created output directory: {output_dir}")
        except Exception as e:
            print(f"ERROR creating output directory: {e}")
            return False

    # Verify all input files exist
    valid_paths = []
    for path in input_paths:
        if os.path.exists(path) and os.access(path, os.R_OK):
            valid_paths.append(path)
        else:
            print(f"WARNING: File does not exist or is not readable: {path}")

    if not valid_paths:
        print("ERROR: No valid input files found")
        return False

    try:
        merger = PdfMerger()

        for path in valid_paths:
            try:
                merger.append(path)
            except Exception as e:
                print(f"ERROR adding {path}: {str(e)}")
                continue

        merger.write(output_path)
        merger.close()

        if os.path.exists(output_path):
            print(f"Successfully merged PDFs to {output_path}")
            return True
        else:
            print(f"ERROR: Output file {output_path} was not created")
            return False
    except Exception as e:
        print(f"ERROR merging PDFs: {str(e)}")

        # If merging fails, at least try to copy the first file as fallback
        try:
            print(f"Attempting to copy first file as fallback")
            with open(valid_paths[0], 'rb') as src, open(output_path, 'wb') as dst:
                dst.write(src.read())
            print(f"Successfully copied first file to {output_path}")
            return True
        except Exception as copy_error:
            print(f"ERROR copying first file: {str(copy_error)}")
            return False

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("ERROR: Insufficient arguments")
        print("Usage: python merge_pdfs.py output_path input_path1 input_path2 ...")
        sys.exit(1)

    output_path = sys.argv[1]
    input_paths = sys.argv[2:]

    try:
        success = merge_pdfs(output_path, *input_paths)
        exit_code = 0 if success else 1
        sys.exit(exit_code)
    except Exception as e:
        print(f"UNHANDLED EXCEPTION: {e}")
        traceback.print_exc()
        sys.exit(1)
