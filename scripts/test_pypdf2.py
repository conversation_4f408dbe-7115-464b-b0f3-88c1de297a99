#!/usr/bin/env python3
import sys
import os

try:
    import PyPDF2
    print(f"PyPDF2 version: {PyPDF2.__version__}")
    
    # Check PyPDF2 version to use the correct class
    if hasattr(PyPDF2, 'PdfMerger'):
        print("PdfMerger class is available")
    elif hasattr(PyPDF2, 'PdfFileMerger'):
        print("PdfFileMerger class is available")
    else:
        print("Neither PdfMerger nor PdfFileMerger class is available")
    
    print("PyPDF2 imported successfully!")
    sys.exit(0)
except Exception as e:
    print(f"Error importing PyPDF2: {str(e)}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
