{"private": true, "type": "module", "scripts": {"build": "NODE_OPTIONS=--max_old_space_size=4096 vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.15", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.16", "axios": "^1.7.4", "concurrently": "^9.0.1", "daisyui": "^5.0.37", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.32", "vite": "^6.2.6"}, "dependencies": {"@lezer/highlight": "^1.2.1", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/vite": "^4.1.7", "@tiptap/core": "^2.11.7", "@tiptap/extension-color": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@tiptap/vue-2": "^2.11.7", "browser-image-compression": "^2.0.2", "dropzone": "^6.0.0-beta.2", "flatpickr": "^4.6.13", "monaco-editor": "^0.52.2", "photoswipe": "^5.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "reactdom": "^2.0.0", "tailwindcss": "^4.1.7"}}