<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\TimeClockController;
use App\Http\Controllers\Api\PushNotificationController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Time Clock API routes moved to web.php for better session handling

// Service worker endpoint (no auth required)
Route::post('notifications/dismissed', [PushNotificationController::class, 'markDismissed']);
