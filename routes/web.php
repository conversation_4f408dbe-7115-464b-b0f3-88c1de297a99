<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\InventoryCategoryController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\ChecklistFieldsController;
use App\Http\Controllers\InventoryImageController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\LineItemController;
use App\Http\Controllers\TaxPolicyController;
use App\Http\Controllers\DepartmentController;
use App\Http\Controllers\DiscountController;
use App\Http\Controllers\CustomerDiscountController;
use App\Http\Controllers\InvoiceDiscountController;
use App\Http\Controllers\LineItemDiscountController;
use App\Http\Controllers\UnregisteredImageUploadController;
use App\Http\Controllers\DocumentationController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\GlobalConfigController;
use App\Http\Controllers\ImageController;
use App\Http\Controllers\PayoutController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\UserGroupController;
use App\Http\Controllers\TaskCompletionController;
use App\Http\Controllers\UserController;
use App\Livewire\TaskSummary;
use App\Http\Controllers\CertificateController;
use App\Http\Controllers\DeviceController;
use App\Http\Controllers\SignatureController;
use App\Http\Controllers\ProfilePhotoController;
use App\Http\Controllers\ActivityLogController;
use App\Http\Controllers\EmergencyContactController;
use App\Http\Controllers\FileController;
use App\Http\Controllers\CalendarController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\TimeCardController;
use App\Http\Controllers\TimePunchController;
use App\Http\Controllers\TimeClockController;
use App\Http\Controllers\SidebarController;
use App\Http\Controllers\Api\PushNotificationController;
use App\Http\Controllers\Api\MicrosoftIntegrationController;
use App\Http\Controllers\MicrosoftOAuthController;
use App\Http\Controllers\AdminMicrosoftIntegrationController;
use App\Http\Controllers\ApiIntegrationsController;

/***************************************************************
 * General and Public Routes
 ***************************************************************/

Route::get('/', function () {
    if (Auth::check()) {
        return redirect()->route('dashboard');
    }
    // Pass any errors from the session to the view
    $errors = session()->get('errors') ?: new \Illuminate\Support\ViewErrorBag;
    return view('welcome', ['errors' => $errors]);
});

// Route for AJAX login form
Route::get('/login-form', function () {
    return view('auth.login-ajax');
})->middleware('guest')->name('login.ajax');

// Route for AJAX register form
Route::get('/register-form', function () {
    return view('auth.register-ajax');
})->middleware('guest')->name('register.ajax');

// view_dashboard
Route::middleware(['auth', 'permission:view_dashboard'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
});

// Sidebar state management - authenticated users only
Route::middleware(['auth'])->group(function () {
    Route::post('/sidebar/state', [SidebarController::class, 'updateState'])->name('sidebar.state');
});

// Public Route to upload images
Route::get('/imgup', [UnregisteredImageUploadController::class, 'index']);
Route::post('/imgup', [UnregisteredImageUploadController::class, 'upload']);
Route::get('/qs', [UnregisteredImageUploadController::class, 'index']);

// Guest Pickup Request Routes (Public - No Authentication Required)
Route::get('/pickup-request', [App\Http\Controllers\GuestPickupController::class, 'show'])->name('guest.pickup-request');
Route::post('/pickup-request', [App\Http\Controllers\GuestPickupController::class, 'store'])->name('guest.pickup-request.store');
Route::get('/pickup-request/success/{pickupRequest}', [App\Http\Controllers\GuestPickupController::class, 'success'])->name('guest.pickup-request.success');
Route::get('/pickup-request/available-dates', [App\Http\Controllers\GuestPickupController::class, 'getAvailableSlots'])->name('guest.pickup-request.available-dates');
Route::get('/pickup-request/time-slots', [App\Http\Controllers\GuestPickupController::class, 'getTimeSlotsForDate'])->name('guest.pickup-request.time-slots');
Route::post('/pickup-request/upload-image', [App\Http\Controllers\GuestPickupController::class, 'uploadImage'])->name('guest.pickup-request.upload-image');
Route::delete('/pickup-request/delete-image/{id}', [App\Http\Controllers\GuestPickupController::class, 'deleteImage'])->name('guest.pickup-request.delete-image');

// Appointment Management Routes (Public - No Authentication Required)
Route::get('/pickup/manage/{token}', [App\Http\Controllers\AppointmentManagementController::class, 'show'])->name('pickup.manage');
Route::put('/pickup/manage/{token}', [App\Http\Controllers\AppointmentManagementController::class, 'update'])->name('pickup.manage.update');
Route::get('/pickup/manage/{token}/edit', [App\Http\Controllers\AppointmentManagementController::class, 'edit'])->name('pickup.manage.edit');
Route::put('/pickup/manage/{token}/edit', [App\Http\Controllers\AppointmentManagementController::class, 'updateDetails'])->name('pickup.manage.edit.update');
Route::post('/pickup/manage/{token}/upload-image', [App\Http\Controllers\AppointmentManagementController::class, 'uploadImage'])->name('pickup.manage.upload-image');
Route::delete('/pickup/manage/{token}/delete-image/{id}', [App\Http\Controllers\AppointmentManagementController::class, 'deleteImage'])->name('pickup.manage.delete-image');
Route::get('/pickup/confirm/{token}', [App\Http\Controllers\AppointmentManagementController::class, 'directConfirm'])->name('pickup.confirm');
Route::get('/pickup/confirm-success/{token}', [App\Http\Controllers\AppointmentManagementController::class, 'confirmSuccess'])->name('pickup.confirm.success');
Route::get('/pickup-request/lead-time-info', [App\Http\Controllers\GuestPickupController::class, 'getLeadTimeInfo'])->name('guest.pickup-request.lead-time-info');

// Pickup Request Creation Routes (Authenticated) - Must come before parameterized routes
Route::middleware(['auth', 'permission:manage_pickup_requests'])->group(function () {
    Route::get('/pickup-requests/create', [App\Http\Controllers\PickupRequestController::class, 'create'])->name('pickup-requests.create');
    Route::post('/pickup-requests', [App\Http\Controllers\PickupRequestController::class, 'store'])->name('pickup-requests.store');
    Route::post('/pickup-requests/auto-save', [App\Http\Controllers\PickupRequestController::class, 'autoSave'])->name('pickup-requests.auto-save');
    Route::post('/pickup-requests/clear-draft', [App\Http\Controllers\PickupRequestController::class, 'clearDraft'])->name('pickup-requests.clear-draft');
    Route::get('/pickup-requests/customer/{customer}', [App\Http\Controllers\PickupRequestController::class, 'getCustomerData'])->name('pickup-requests.customer-data');
    Route::get('/pickup-requests/week-view', [App\Http\Controllers\PickupRequestController::class, 'getGeneralWeekView'])->name('pickup-requests.general-week-view');
});

// Pickup Request Management Routes (Authenticated)
Route::middleware(['auth', 'permission:view_pickup_requests'])->group(function () {
    Route::get('/pickup-requests', [App\Http\Controllers\PickupRequestController::class, 'index'])->name('pickup-requests.index');
    // PDF generation route must come before parameterized routes
    Route::get('/pickup-requests/daily-pdf', [App\Http\Controllers\PickupRequestController::class, 'generateDailyPickupPdf'])->name('pickup-requests.daily-pdf');
    Route::get('/pickup-requests/{pickupRequest}', [App\Http\Controllers\PickupRequestController::class, 'show'])->name('pickup-requests.show');
    Route::get('/pickup-requests/{pickupRequest}/week-view', [App\Http\Controllers\PickupRequestController::class, 'getWeekView'])->name('pickup-requests.week-view');
    Route::get('/pickup-requests/{pickupRequest}/distance', [App\Http\Controllers\PickupRequestController::class, 'getDistance'])->name('pickup-requests.distance');
});

// Pickup Request Edit Routes (Authenticated)
Route::middleware(['auth', 'permission:manage_pickup_requests'])->group(function () {
    Route::get('/pickup-requests/{pickupRequest}/edit', [App\Http\Controllers\PickupRequestController::class, 'edit'])->name('pickup-requests.edit');
    Route::put('/pickup-requests/{pickupRequest}', [App\Http\Controllers\PickupRequestController::class, 'update'])->name('pickup-requests.update');
    Route::post('/pickup-requests/{pickupRequest}/auto-save-edit', [App\Http\Controllers\PickupRequestController::class, 'autoSaveEdit'])->name('pickup-requests.auto-save-edit');
});

// Pickup Request Processing Steps (Authenticated)
Route::middleware(['auth', 'permission:manage_pickup_requests'])->group(function () {
    // Step 1: Customer Identification
    Route::get('/pickup-requests/{pickupRequest}/step1', [App\Http\Controllers\PickupRequestCustomerController::class, 'step1'])->name('pickup-requests.step1');
    Route::get('/pickup-requests/{pickupRequest}/search-customers', [App\Http\Controllers\PickupRequestCustomerController::class, 'searchCustomers'])->name('pickup-requests.search-customers');

    // Step 2: Time Selection
    Route::get('/pickup-requests/{pickupRequest}/step2', [App\Http\Controllers\PickupRequestScheduleController::class, 'step2'])->name('pickup-requests.step2');
    Route::post('/pickup-requests/{pickupRequest}/step2', [App\Http\Controllers\PickupRequestScheduleController::class, 'saveStep2'])->name('pickup-requests.saveStep2');

    // Step 3: Event Details
    Route::get('/pickup-requests/{pickupRequest}/step3', [App\Http\Controllers\PickupRequestScheduleController::class, 'step3'])->name('pickup-requests.step3');
    Route::post('/pickup-requests/{pickupRequest}/step3', [App\Http\Controllers\PickupRequestScheduleController::class, 'saveStep3'])->name('pickup-requests.saveStep3');

    // Success Page
    Route::get('/pickup-requests/{pickupRequest}/success', [App\Http\Controllers\PickupRequestScheduleController::class, 'success'])->name('pickup-requests.success');
});

Route::middleware(['auth', 'permission:manage_pickup_requests'])->group(function () {
    Route::post('/pickup-requests/{pickupRequest}/link-customer', [App\Http\Controllers\PickupRequestCustomerController::class, 'linkCustomer'])->name('pickup-requests.link-customer');
    Route::post('/pickup-requests/{pickupRequest}/unlink-customer', [App\Http\Controllers\PickupRequestCustomerController::class, 'unlinkCustomer'])->name('pickup-requests.unlink-customer');
    Route::post('/pickup-requests/{pickupRequest}/finalize', [App\Http\Controllers\PickupRequestController::class, 'finalize'])->name('pickup-requests.finalize');
    Route::post('/pickup-requests/{pickupRequest}/confirm', [App\Http\Controllers\PickupRequestController::class, 'confirm'])->name('pickup-requests.confirm');
    Route::post('/pickup-requests/{pickupRequest}/unconfirm', [App\Http\Controllers\PickupRequestController::class, 'unconfirm'])->name('pickup-requests.unconfirm');
    Route::post('/pickup-requests/{pickupRequest}/complete', [App\Http\Controllers\PickupRequestController::class, 'complete'])->name('pickup-requests.complete');
    Route::post('/pickup-requests/{pickupRequest}/cancel', [App\Http\Controllers\PickupRequestController::class, 'cancel'])->name('pickup-requests.cancel');
    Route::post('/pickup-requests/{pickupRequest}/send-confirmation-email', [App\Http\Controllers\PickupRequestController::class, 'sendConfirmationEmail'])->name('pickup-requests.send-confirmation-email');
    Route::post('/pickup-requests/{pickupRequest}/update-internal-notes', [App\Http\Controllers\PickupRequestController::class, 'updateInternalNotes'])->name('pickup-requests.update-internal-notes');
    Route::post('/pickup-requests/{pickupRequest}/upload-image', [App\Http\Controllers\PickupRequestController::class, 'uploadImage'])->name('pickup-requests.upload-image');
    Route::get('/pickup-requests/{pickupRequest}/images', [App\Http\Controllers\PickupRequestController::class, 'getImages'])->name('pickup-requests.get-images');
    Route::delete('/pickup-requests/{pickupRequest}/delete-image/{image}', [App\Http\Controllers\PickupRequestController::class, 'deleteImage'])->name('pickup-requests.delete-image');
});

/*****************************************************************
 * Documentation Routes
 * ***************************************************************/

// create_document_items
Route::middleware(['auth', 'permission:create_document_items'])->group(function () {
    Route::get('/documentation/create', [DocumentationController::class, 'create'])->name('documentation.create');
    Route::post('/documentation', [DocumentationController::class, 'store'])->name('documentation.store');
});

// view_document_items
Route::middleware(['auth', 'permission:view_document_items'])->group(function () {
    Route::get('/documentation', [DocumentationController::class, 'index'])->name('documentation.index');
    Route::get('/documentation/{documentation}', [DocumentationController::class, 'show'])->name('documentation.show');
});

// edit_document_items
Route::middleware(['auth', 'permission:edit_document_items'])->group(function () {
    Route::get('/documentation/{documentation}/edit', [DocumentationController::class, 'edit'])->name('documentation.edit');
    Route::put('/documentation/{documentation}', [DocumentationController::class, 'update'])->name('documentation.update');
});

// delete_document_items
Route::middleware(['auth', 'permission:delete_document_items'])->group(function () {
    Route::delete('/documentation/{documentation}', [DocumentationController::class, 'destroy'])->name('documentation.destroy');
});
/***************************************************************
 * Inventory Routes
 ***************************************************************/

// **Create Inventory Items**
Route::middleware(['auth', 'permission:create_inventory_items'])->group(function () {
    Route::get('/inventory/create', [InventoryController::class, 'create'])->name('inventory.create');
    Route::post('/inventory', [InventoryController::class, 'store'])->name('inventory.store');
    Route::get('/inventory/{inventory}/clone', [InventoryController::class, 'clone'])->name('inventory.clone');
});

// **View Inventory Items**
Route::middleware(['auth', 'permission:view_inventory_items'])->group(function () {
    Route::get('/inventory', [InventoryController::class, 'index'])->name('inventory.index');
    Route::middleware('auth:sanctum')->get('/inventory/search', [InventoryController::class, 'search']);
    Route::get('/inventory/{id}', [InventoryController::class, 'show'])->name('inventory.show')
        ->where('id', '[0-9]+'); // Restrict to numeric IDs
    Route::get('/inventory/categories/{id}/listed-items', [InventoryController::class, 'getListedItems']);
    Route::get('/inventory/{inventory}/pdf', [InventoryController::class, 'generatePDF'])->name('inventory.pdf');
});

// **Generate Inventory Descriptions**
Route::middleware(['auth', 'permission:generate_inventory_descriptions'])->group(function () {
    Route::get('/inventory/{id}/generateddescription', [InventoryController::class, 'getGeneratedDescription'])->name('inventory.generateddescription');
    Route::post('/inventory/{id}/regenerate-description', [InventoryController::class, 'regenerateDescription'])->name('inventory.regenerateDescription');
});

// **Delete Inventory Items**
Route::middleware(['auth', 'permission:delete_inventory_items'])->group(function () {
    Route::delete('/inventory/{id}', [InventoryController::class, 'destroy'])->name('inventory.destroy');
    Route::post('inventory/{id}/restore', [InventoryController::class, 'restore'])->name('inventory.restore');
});

// **Edit Inventory Items**
Route::middleware(['auth', 'permission:edit_inventory_items'])->group(function () {
    Route::get('/inventory/{id}/images', [App\Http\Controllers\InventoryController::class, 'fetchInventoryImages'])->name('inventory.images');
    Route::get('/inventory/{inventory}/edit', [InventoryController::class, 'edit'])->name('inventory.edit');
    Route::put('/inventory/{inventory}', [InventoryController::class, 'update'])->name('inventory.update');
    Route::post('/inventory/{inventory}', [InventoryController::class, 'update'])->name('inventory.update'); // Optional
    Route::put('/inventory/{inventory}/checklist', [InventoryController::class, 'updateChecklist'])->name('inventory.checklist.update');
    Route::patch('/inventory/{inventory}/field', [InventoryController::class, 'updateField'])->name('inventory.update.field');
});


// **Photo-Related Routes**
Route::middleware(['auth', 'permission:edit_inventory_items'])->group(function () {
    Route::post('/inventory/{inventory}/photos', [InventoryImageController::class, 'store'])->name('inventory.photos.upload');
    Route::post('/inventory/photos/{photo}/reorder', [InventoryImageController::class, 'reorder'])->name('inventory.photos.reorder');
    Route::post('/inventory/{inventory}/photos/reorder', [InventoryImageController::class, 'reorderPhotos'])->name('inventory.photos.reorder');
});

Route::middleware(['auth', 'permission:view_inventory_items'])->group(function () {
    Route::get('/inventory/{inventory}/photos/download', [InventoryImageController::class, 'downloadAll'])->name('inventory.photos.downloadAll');
});


/***************************************************************
 * Invoice Routes
 ***************************************************************/
// create_invoices
Route::middleware(['auth', 'permission:create_invoices'])->group(function () {
    Route::get('/invoices/create', [InvoiceController::class, 'create'])->name('invoices.create');
    Route::post('/invoices', [InvoiceController::class, 'store'])->name('invoices.store');
    Route::post('/invoices/storeGeneral', [InvoiceController::class, 'storeGeneral'])->name('invoices.storeGeneral');
});

// view_invoices
Route::middleware(['auth', 'permission:view_invoices'])->group(function () {
    Route::get('/invoices/search', [InvoiceController::class, 'search'])->name('invoices.search');

    Route::get('/invoices', [InvoiceController::class, 'index'])->name('invoices.index');
    Route::get('/invoices/{invoice}', [InvoiceController::class, 'show'])->name('invoices.show');
    Route::get('/invoices/{invoice}/pdf', [InvoiceController::class, 'generatePDF'])->name('invoices.pdf');
    Route::get('/files/{file}/download', [FileController::class, 'download'])->name('files.download');
    Route::get('/files/{file}/view', [FileController::class, 'view'])->name('files.view');
    Route::get('/files/{file}/thumbnail/{width?}/{height?}', [FileController::class, 'thumbnail'])->name('files.thumbnail');

    //get default tax policy
    Route::get('/inventory/categories/{id}/taxpolicy', [InventoryCategoryController::class, 'getDefaultTaxPolicy'])->name('inventorycategory.defaulttaxpolicy');
    Route::get('/inventory/categories/{id}/json', [InventoryCategoryController::class, 'getJson'])->name('inventorycategory.json');

    // Get json data for an inventory item
    Route::get('/inventory/{id}/json', [InventoryController::class, 'getJson'])->name('inventory.json');
    Route::get('/inventory/{id}/suggestedprice', [InventoryController::class, 'getSuggestedPrice'])->name('inventory.suggestedprice');
});


// edit_invoices
Route::middleware(['auth', 'permission:edit_invoices'])->group(function () {
    Route::get('/invoices/{invoice}/edit', [InvoiceController::class, 'edit'])->name('invoices.edit');
    Route::put('/invoices/{invoice}', [InvoiceController::class, 'update'])->name('invoices.update');
    Route::post('/line-items', [LineItemController::class, 'store'])->name('line-items.store');
    Route::delete('/line-items/{lineItem}', [LineItemController::class, 'destroy'])->name('line-items.destroy');
    Route::get('/invoices/{invoice}/get-used-discounts', [InvoiceController::class, 'getUsedDiscounts']);
    Route::put('/line-items/{lineItem}', [LineItemController::class, 'update'])->name('line-items.update');
    Route::post('/invoices/apply-discount', [InvoiceController::class, 'applyDiscount']);
    Route::post('/invoice-discounts', [InvoiceDiscountController::class, 'store'])->name('invoice-discounts.store');
    Route::put('/invoice-discounts/{invoiceDiscount}', [InvoiceDiscountController::class, 'update'])->name('invoice-discounts.update');
    Route::delete('/invoice-discounts/{invoiceDiscount}', [InvoiceDiscountController::class, 'destroy'])->name('invoice-discounts.destroy');
    Route::post('/invoice-discounts/removeById', [InvoiceDiscountController::class, 'removeById'])->name('invoice-discounts.removeById');
    Route::post('/line-item-discounts', [LineItemDiscountController::class, 'store'])->name('line-item-discounts.store');
    Route::put('/line-item-discounts/{lineItemDiscount}', [LineItemDiscountController::class, 'update'])->name('line-item-discounts.update');
    Route::delete('/line-item-discounts/{lineItemDiscount}', [LineItemDiscountController::class, 'destroy'])->name('line-item-discounts.destroy');

    // Invoice file attachments
    Route::post('/invoices/{invoice}/files', [FileController::class, 'uploadForInvoice'])->name('invoices.files.upload');
    Route::post('/files/{file}/update-notes', [FileController::class, 'updateNotes'])->name('files.updateNotes');
    Route::delete('/files/{file}', [FileController::class, 'destroy'])->name('files.destroy');
});

// delete_invoices
Route::middleware(['auth', 'permission:delete_invoices'])->group(function () {
    Route::delete('/invoices/{invoice}', [InvoiceController::class, 'destroy'])->name('invoices.destroy');
    Route::post('/invoices/{id}/restore', [InvoiceController::class, 'restore'])->name('invoices.restore');
});

// Images
// delete_image_items
Route::middleware(['auth', 'permission:delete_image_items'])->group(function () {
    Route::delete('inventory/photos/{id}', [InventoryImageController::class, 'destroy'])->name('inventory.photos.delete');
});

/***************************************************************
 * Customer Routes
 ***************************************************************/

// Create Customer Accounts
Route::middleware(['auth', 'permission:create_customer_accounts'])->group(function () {
    Route::get('/customers/create', [CustomerController::class, 'create'])->name('customers.create');
    Route::post('/customers', [CustomerController::class, 'store'])->name('customers.store');
    Route::get('/customer-quick-create', [CustomerController::class, 'quickCreate'])->name('customers.quick-create');
    Route::post('/customers/{customer}/discounts/{discount}/use', [CustomerDiscountController::class, 'markAsUsed']);
    Route::post('/customers/{customer}/discounts/{discount}/unuse', [CustomerDiscountController::class, 'unmarkAsUsed']);
});

// View Customer Accounts
Route::middleware(['auth', 'permission:view_customer_accounts'])->group(function () {
    Route::get('/customers', [CustomerController::class, 'index'])->name('customers.index');
    Route::get('/customers/{customer}', [CustomerController::class, 'show'])->name('customers.show');
    Route::get('/customers/{customer}/eligible-discounts', [CustomerController::class, 'eligibleDiscounts']);
    Route::get('/customersearch/{query}', [CustomerController::class, 'search'])->name('customers.search');
    Route::get('/customers/{customer}/name', [CustomerController::class, 'getName'])->name('customers.get-name');
    Route::get('/customers/{customer}/license-status', [CustomerController::class, 'getLicenseStatus'])->name('customers.license-status');
    Route::get('/customers/{customer}/json', [CustomerController::class, 'getJson'])->name('customers.json');
});

// Edit Customer Accounts
Route::middleware(['auth', 'permission:edit_customer_accounts'])->group(function () {
    Route::get('/customers/{customer}/edit', [CustomerController::class, 'edit'])->name('customers.edit');
    Route::put('/customers/{customer}', [CustomerController::class, 'update'])->name('customers.update');
    Route::post('/customers/{customer}/auto-save', [CustomerController::class, 'autoSave'])->name('customers.autoSave');

    // Customer file attachments
    Route::post('/customers/{customer}/files', [FileController::class, 'uploadForCustomer'])->name('customers.files.upload');
});

// Delete Customer Accounts
Route::middleware(['auth', 'permission:delete_customer_accounts'])->group(function () {
    Route::delete('/customers/{customer}', [CustomerController::class, 'destroy'])->name('customers.destroy');
    Route::post('/customers/{customer}/restore', [CustomerController::class, 'restore'])->name('customers.restore');
});




/***************************************************************
 * Discount Routes
 ***************************************************************/
// Create Discounts
Route::middleware(['auth', 'permission:create_discounts'])->group(function () {
    Route::get('/discounts/create', [DiscountController::class, 'create'])->name('discounts.create');
    Route::post('/discounts', [DiscountController::class, 'store'])->name('discounts.store');
    Route::get('/customer_discounts/create', [CustomerDiscountController::class, 'create'])->name('customer_discounts.create');
    Route::post('/customer_discounts', [CustomerDiscountController::class, 'store'])->name('customer_discounts.store');
});

// Edit Discounts
Route::middleware(['auth', 'permission:edit_discounts'])->group(function () {
    Route::get('/discounts/{discount}/edit', [DiscountController::class, 'edit'])->name('discounts.edit');
    Route::put('/discounts/{discount}', [DiscountController::class, 'update'])->name('discounts.update');
    Route::delete('/discounts/{discount}', [DiscountController::class, 'destroy'])->name('discounts.destroy');
    Route::get('/customer_discounts/{customerDiscount}/edit', [CustomerDiscountController::class, 'edit'])->name('customer_discounts.edit');
    Route::put('/customer_discounts/{customerDiscount}', [CustomerDiscountController::class, 'update'])->name('customer_discounts.update');
    Route::delete('/customer_discounts/{customerDiscount}', [CustomerDiscountController::class, 'destroy'])->name('customer_discounts.destroy');
});

// View Discounts
Route::middleware(['auth', 'permission:view_discounts'])->group(function () {
    Route::get('/discounts', [DiscountController::class, 'index'])->name('discounts.index');
    Route::get('/discounts/checker', [DiscountController::class, 'checker'])->name('discounts.checker');
    Route::post('/discounts/checker/fetch', [DiscountController::class, 'fetchDiscounts'])->name('discounts.checker.fetch');

    Route::get('/discounts/checker', [DiscountController::class, 'checker'])->name('discounts.checker');
    Route::get('/discounts/{discount}', [DiscountController::class, 'show'])->name('discounts.show');

    Route::get('/customer_discounts', [CustomerDiscountController::class, 'index'])->name('customer_discounts.index');
    Route::get('/customer_discounts/search', [CustomerDiscountController::class, 'search'])->name('customer_discounts.search');
});



/***************************************************************
 * Reports Routes
 ***************************************************************/

// view_reports
Route::middleware(['auth', 'permission:view_reports'])->group(function () {

    Route::get('/reports/sales-data', [ReportController::class, 'salesData'])->name('reports.sales-data');
    Route::get('/reports/list', [ReportController::class, 'list'])->name('reports.list');
    Route::resource('reports', ReportController::class);
    Route::delete('/reports/{report}', [ReportController::class, 'destroy'])->name('reports.destroy');
});


/***************************************************************
 * Global Configs Routes
 ***************************************************************/

//is_admin
Route::middleware(['auth', 'permission:manage_settings'])->group(function () {
    Route::get('/globalconfig/edit', [GlobalConfigController::class, 'edit'])->name('globalconfig.edit');
    Route::put('/globalconfig', [GlobalConfigController::class, 'update'])->name('globalconfig.update');
    Route::post('/globalconfig/upload-logo', [GlobalConfigController::class, 'uploadLogo'])->name('globalconfig.upload-logo');

    Route::resource('tax_policies', TaxPolicyController::class);
    Route::resource('departments', DepartmentController::class);

    Route::resource('inventory_categories', InventoryCategoryController::class);
    Route::post('inventory_categories/{id}/restore', [InventoryCategoryController::class, 'restore'])
        ->name('inventory_categories.restore');

    // Checklist Field Routes
    Route::post('checklist_fields', [ChecklistFieldsController::class, 'store'])->name('checklist_fields.store');
    Route::delete('checklist_fields/{id}', [ChecklistFieldsController::class, 'destroy'])->name('checklist_fields.destroy');
    Route::post('checklist_fields/updateAll', [ChecklistFieldsController::class, 'updateAll'])->name('checklist_fields.updateAll');
});




/***************************************************************
 * Image Routes
 ***************************************************************/

// view_image_items
Route::middleware(['auth', 'permission:view_image_items'])->group(function () {
    Route::get('/images', [ImageController::class, 'index'])->name('images.index');
    Route::get('/images/json', [App\Http\Controllers\ImageController::class, 'fetchImages'])->name('images.json');
});

// create_image_items
Route::middleware(['auth', 'permission:create_image_items'])->group(function () {
    Route::get('/images/upload', [ImageController::class, 'upload'])->name('images.upload');
    Route::post('/images', [ImageController::class, 'store'])->name('images.store');
});

// view_image_items - specific image routes (must come after /upload)
Route::middleware(['auth', 'permission:view_image_items'])->group(function () {
    Route::get('/images/{image}', [ImageController::class, 'show'])->name('images.show');
    Route::get('/images/{image}/download', [ImageController::class, 'download'])->name('images.download');
});

// edit_image_items
Route::middleware(['auth', 'permission:edit_image_items'])->group(function () {
    Route::get('/images/{image}/edit', [ImageController::class, 'edit'])->name('images.edit');
    Route::post('/images/update', [ImageController::class, 'update'])->name('images.update');

});

// destroy_image_items
Route::middleware(['auth', 'permission:delete_image_items'])->group(function () {
    Route::delete('/images/{image}', [ImageController::class, 'destroy'])->name('images.destroy');
    
    // Context-aware image uploads
    Route::post('/images/context/{contextType}/{contextId}', [ImageController::class, 'storeForContext'])->name('images.store.context');
    Route::post('/images/session/{sessionId}', [ImageController::class, 'storeForSession'])->name('images.store.session');
    Route::post('/images/associate-session', [ImageController::class, 'associateSessionImages'])->name('images.associate.session');
    Route::post('/images/reorder', [ImageController::class, 'reorderImages'])->name('images.reorder');
});



// manage_permissions
Route::middleware(['auth', 'permission:manage_permissions'])->group(function () {
    // Permissions routes
    Route::get('/permissions', [PermissionController::class, 'index'])->name('permissions.index');
    Route::get('/permissions/{permission}/details', [PermissionController::class, 'getDetails']);
    Route::post('/permissions/{permission}/update-groups', [PermissionController::class, 'updateGroups']);

    // User Groups routes
    Route::resource('user_groups', UserGroupController::class);
});


/***************************************************************
 * Task Routes
 */

// Only register task routes if the tasks feature is enabled
if (\App\Models\GlobalConfig::isTasksEnabled()) {
    // create_tasks
    Route::middleware(['auth', 'permission:create_tasks|create_edit_own_tasks'])->group(function () {
        Route::get('/tasks/create', [TaskController::class, 'create'])->name('tasks.create');
        Route::post('/tasks', [TaskController::class, 'store'])->name('tasks.store');
    });

    // edit
    Route::middleware(['auth', 'permission:create_tasks|create_edit_own_tasks'])->group(function () {
        Route::get('/tasks/{task}/edit', [TaskController::class, 'edit'])->name('tasks.edit');
        Route::put('/tasks/{task}', [TaskController::class, 'update'])->name('tasks.update');
    });

    Route::middleware(['auth', 'permission:create_tasks'])->group(function () {
        Route::get('/tasks', [TaskController::class, 'index'])->name('tasks.index');
    });

    // delete_tasks
    Route::middleware(['auth', 'permission:delete_tasks'])->group(function () {
        Route::delete('/tasks/{task}', [TaskController::class, 'destroy'])->name('tasks.destroy');
    });

    // view_tasks
    Route::middleware(['auth', 'permission:view_tasks'])->group(function () {
        Route::get('/my-tasks', [TaskController::class, 'myTasks'])->name('my-tasks');
        Route::post('/tasks/{task}/status', [TaskCompletionController::class, 'updateStatus'])
        ->name('tasks.update-status')
        ->middleware('auth');
        Route::get('/tasks/{id}', [TaskController::class, 'getTask']);
        Route::post('/tasks/{id}/status', [TaskController::class, 'updateTaskStatus']);

        Route::get('/usertasks/{userId}', [TaskController::class, 'userTasks'])->name('user.tasks');
    });

    Route::get('/task-summary', TaskSummary::class);
}

/***************************************************************
 * User Routes
 ***************************************************************/
Route::middleware(['auth', 'permission:view_user_accounts'])->group(function () {
    Route::get('/admin/users', [UserController::class, 'index'])->name('admin.users.index');
});

Route::middleware(['auth', 'permission:edit_user_accounts'])->group(function () {
    Route::get('/admin/users/create', [UserController::class, 'create'])->name('admin.users.create');
    Route::post('/admin/users', [UserController::class, 'store'])->name('admin.users.store');
    Route::get('/admin/users/{user}/edit', [UserController::class, 'edit'])->name('admin.users.edit');
    Route::put('/admin/users/{user}', [UserController::class, 'update'])->name('admin.users.update');
});

// This route must be after the create route to avoid conflicts
Route::middleware(['auth', 'permission:view_user_accounts'])->group(function () {
    Route::get('/admin/users/{user}', [UserController::class, 'show'])->name('admin.users.show');
});

/***************************************************************
 * Profile Photo Routes
 ***************************************************************/
Route::middleware(['auth'])->group(function () {
    Route::get('/profile/photo', [ProfilePhotoController::class, 'edit'])->name('profile.photo.edit');
    Route::put('/profile/photo', [ProfilePhotoController::class, 'update'])->name('profile.photo.update');
    Route::delete('/profile/photo', [ProfilePhotoController::class, 'destroy'])->name('profile.photo.destroy');
});

/***************************************************************
 * Emergency Contact Routes
 ***************************************************************/
Route::middleware(['auth'])->group(function () {
    Route::get('/profile/emergency-contact', [EmergencyContactController::class, 'edit'])->name('profile.emergency-contact.edit');
    Route::post('/profile/emergency-contact', [EmergencyContactController::class, 'update'])->name('profile.emergency-contact.update');
});

/***************************************************************
 * Microsoft 365 Integration Routes
 ***************************************************************/
Route::middleware(['auth'])->group(function () {
    Route::get('/microsoft/connect', [MicrosoftOAuthController::class, 'redirectToMicrosoft'])->name('microsoft.connect');
    Route::get('/microsoft/callback', [MicrosoftOAuthController::class, 'handleCallback'])->name('microsoft.callback');
    Route::post('/microsoft/disconnect', [MicrosoftOAuthController::class, 'disconnect'])->name('microsoft.disconnect');
    Route::get('/microsoft/test-connection', [MicrosoftOAuthController::class, 'testConnection'])->name('microsoft.test');
});

/***************************************************************
 * Admin Tools Routes
 ***************************************************************/
Route::middleware(['auth', 'permission:manage_settings'])->group(function () {
    Route::get('/admin/tools', [\App\Http\Controllers\AdminToolsController::class, 'index'])->name('admin.tools.index');
    Route::get('/admin/tools/phpinfo', [\App\Http\Controllers\AdminToolsController::class, 'phpinfo'])->name('admin.tools.phpinfo');
    Route::get('/admin/tools/system-stats', [\App\Http\Controllers\Admin\SystemStatsController::class, 'index'])->name('admin.tools.system-stats.index');

    // Customer Import Tool Routes
    Route::get('/admin/tools/customer-import', [\App\Http\Controllers\CustomerImportController::class, 'index'])->name('admin.tools.customer-import.index');
    Route::post('/admin/tools/customer-import/upload', [\App\Http\Controllers\CustomerImportController::class, 'upload'])->name('admin.tools.customer-import.upload');
    Route::post('/admin/tools/customer-import/process', [\App\Http\Controllers\CustomerImportController::class, 'process'])->name('admin.tools.customer-import.process');

    // Customer Export Tool Routes
    Route::get('/admin/tools/customer-export', [\App\Http\Controllers\CustomerExportController::class, 'index'])->name('admin.tools.customer-export.index');
    Route::post('/admin/tools/customer-export/excel', [\App\Http\Controllers\CustomerExportController::class, 'exportExcel'])->name('admin.tools.customer-export.excel');
    Route::post('/admin/tools/customer-export/csv', [\App\Http\Controllers\CustomerExportController::class, 'exportCsv'])->name('admin.tools.customer-export.csv');
    Route::get('/admin/tools/customer-export/quick-excel', [\App\Http\Controllers\CustomerExportController::class, 'quickExportExcel'])->name('admin.tools.customer-export.quick-excel');
    Route::get('/admin/tools/customer-export/quick-csv', [\App\Http\Controllers\CustomerExportController::class, 'quickExportCsv'])->name('admin.tools.customer-export.quick-csv');
});

/***************************************************************
 * Email Template Management Routes
 ***************************************************************/
Route::middleware(['auth', 'permission:is_admin'])->group(function () {
    Route::get('/admin/email-templates', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'index'])->name('admin.email-templates.index');
    Route::get('/admin/email-templates/create', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'create'])->name('admin.email-templates.create');
    Route::post('/admin/email-templates', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'store'])->name('admin.email-templates.store');
    Route::get('/admin/email-templates/{emailTemplate}/edit', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'edit'])->name('admin.email-templates.edit');
    Route::put('/admin/email-templates/{emailTemplate}', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'update'])->name('admin.email-templates.update');
    Route::get('/admin/email-templates/{emailTemplate}/preview', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'preview'])->name('admin.email-templates.preview');
    Route::patch('/admin/email-templates/{emailTemplate}/toggle-status', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'toggleStatus'])->name('admin.email-templates.toggle-status');
    Route::delete('/admin/email-templates/{emailTemplate}', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'destroy'])->name('admin.email-templates.destroy');

    // APIs & Integrations Overview
    Route::get('/admin/api-integrations', [ApiIntegrationsController::class, 'index'])->name('admin.api-integrations.index');

    // Microsoft 365 Integration Administration
    Route::get('/admin/microsoft-integration', [AdminMicrosoftIntegrationController::class, 'index'])->name('admin.microsoft-integration.index');
    Route::get('/admin/microsoft-integration/setup', [AdminMicrosoftIntegrationController::class, 'create'])->name('admin.microsoft-integration.create');
    Route::get('/admin/microsoft-integration/connect', [AdminMicrosoftIntegrationController::class, 'redirectToMicrosoft'])->name('admin.microsoft-integration.connect');
    Route::get('/admin/microsoft-integration/calendar-selection', [AdminMicrosoftIntegrationController::class, 'showCalendarSelection'])->name('admin.microsoft-integration.calendar-selection');
    Route::post('/admin/microsoft-integration/calendar-selection', [AdminMicrosoftIntegrationController::class, 'completeSetup'])->name('admin.microsoft-integration.complete-setup');
    Route::get('/admin/microsoft-integration/change-calendar', [AdminMicrosoftIntegrationController::class, 'changeCalendar'])->name('admin.microsoft-integration.change-calendar');
    Route::post('/admin/microsoft-integration/change-calendar', [AdminMicrosoftIntegrationController::class, 'updateCalendar'])->name('admin.microsoft-integration.update-calendar');
    Route::put('/admin/microsoft-integration', [AdminMicrosoftIntegrationController::class, 'update'])->name('admin.microsoft-integration.update');
    Route::post('/admin/microsoft-integration/sync', [AdminMicrosoftIntegrationController::class, 'manualSync'])->name('admin.microsoft-integration.sync');
    Route::post('/admin/microsoft-integration/test', [AdminMicrosoftIntegrationController::class, 'testConnection'])->name('admin.microsoft-integration.test');
    Route::delete('/admin/microsoft-integration', [AdminMicrosoftIntegrationController::class, 'disconnect'])->name('admin.microsoft-integration.disconnect');
});

/***************************************************************
 * Calendar Routes
 ***************************************************************/


 // Create calendars and events
Route::middleware(['auth', 'permission:create_calendars'])->group(function () {
    // Create calendars
    Route::get('/calendars/create', [CalendarController::class, 'create'])->name('calendars.create');
    Route::post('/calendars', [CalendarController::class, 'store'])->name('calendars.store');
});

// View calendars and events
Route::middleware(['auth', 'permission:view_calendars'])->group(function () {
    // View calendars
    Route::get('/calendars', [CalendarController::class, 'index'])->name('calendars.index');
    Route::get('/calendars/{calendar}', [CalendarController::class, 'show'])->name('calendars.show');

    // Pickup calendar shortcut
    Route::get('/pickup-calendar', [CalendarController::class, 'pickupCalendar'])->name('pickup.calendar');
});

Route::middleware(['auth', 'permission:view_events'])->group(function () {
    // View events
    Route::get('/events', [EventController::class, 'index'])->name('events.index');
    Route::get('/events/{event}', [EventController::class, 'show'])->name('events.show');
    Route::get('/event-categories', [EventController::class, 'getCategories'])->name('events.categories');
});



Route::middleware(['auth', 'permission:create_events'])->group(function () {
    // Create events
    Route::get('/events/create', [EventController::class, 'create'])->name('events.create');
    Route::post('/events', [EventController::class, 'store'])->name('events.store');
    Route::post('/event-categories', [EventController::class, 'storeCategory'])->name('events.categories.store');
});

// Edit calendars and events
Route::middleware(['auth', 'permission:edit_calendars|edit_own_calendars'])->group(function () {
    // Edit calendars - the controller will check if the user owns the calendar when edit_own_calendars is used
    Route::get('/calendars/{calendar}/edit', [CalendarController::class, 'edit'])->name('calendars.edit');
    Route::put('/calendars/{calendar}', [CalendarController::class, 'update'])->name('calendars.update');
    Route::patch('/calendars/{calendar}', [CalendarController::class, 'update']);
});

Route::middleware(['auth', 'permission:edit_events|edit_own_events'])->group(function () {
    // Edit events - the controller will check if the user created the event when edit_own_events is used
    Route::get('/events/{event}/edit', [EventController::class, 'edit'])->name('events.edit');
    Route::put('/events/{event}', [EventController::class, 'update'])->name('events.update');
    Route::patch('/events/{event}', [EventController::class, 'update']);
    Route::put('/events/{id}/dates', [EventController::class, 'updateEventDates'])->name('events.updateDates');
});

// Delete calendars and events
Route::middleware(['auth', 'permission:delete_calendars|delete_own_calendars'])->group(function () {
    // Delete calendars - the controller will check if the user owns the calendar when delete_own_calendars is used
    Route::delete('/calendars/{calendar}', [CalendarController::class, 'destroy'])->name('calendars.destroy');
});

Route::middleware(['auth', 'permission:delete_events|delete_own_events'])->group(function () {
    // Delete events - the controller will check if the user created the event when delete_own_events is used
    Route::delete('/events/{event}', [EventController::class, 'destroy'])->name('events.destroy');
});


/***************************************************************
 * Time Clock Routes
 ***************************************************************/

// Time Clock Routes
Route::middleware(['auth', 'mobile.timeclock'])->group(function () {
    Route::get('/time-clock', [App\Http\Controllers\TimeClockController::class, 'index'])->name('time-clock');
    Route::post('/time-clock/action', [App\Http\Controllers\TimeClockController::class, 'action'])->name('time-clock.action');

    // Time Clock API endpoint (moved from api.php for better web integration)
    Route::get('/api/time-clock/current-data', [App\Http\Controllers\Api\TimeClockController::class, 'getCurrentData'])->name('api.time-clock.current-data');

    // Time Card routes with permissions
    Route::middleware(['permission:view_timecards|view_own_timecards'])->group(function () {
        Route::get('/timecards', [TimeCardController::class, 'index'])->name('timecards.index');
    });

    // Create time cards for past dates (admin only) - must be before the {timecard} routes
    Route::middleware(['permission:edit_timecards'])->group(function () {
        Route::get('/timecards/create-for-date', [TimeCardController::class, 'createForDate'])->name('timecards.create-for-date');
        Route::post('/timecards/store-for-date', [TimeCardController::class, 'storeForDate'])->name('timecards.store-for-date');
    });

    // Timesheet generation routes (admin only)
    Route::middleware(['permission:view_timesheets'])->group(function () {
        Route::get('/timesheets', [App\Http\Controllers\TimesheetController::class, 'index'])->name('timesheets.index');
    });

    Route::middleware(['permission:generate_timesheets'])->group(function () {
        Route::post('/timesheets/generate', [App\Http\Controllers\TimesheetController::class, 'generate'])->name('timesheets.generate');
        Route::post('/timesheets/print', [App\Http\Controllers\TimesheetController::class, 'print'])->name('timesheets.print');
    });

    // My timesheet print route (accessible to all authenticated users)
    Route::middleware(['auth'])->group(function () {
        Route::get('/my-timesheet/print', [App\Http\Controllers\TimesheetController::class, 'printMyTimesheet'])->name('my-timesheet.print');
    });

    Route::middleware(['permission:view_timecards|view_own_timecards'])->group(function () {
        Route::get('/timecards/{timecard}', [TimeCardController::class, 'show'])->name('timecards.show');
    });

    Route::middleware(['permission:edit_timecards|edit_own_timecards'])->group(function () {
        Route::get('/timecards/{timecard}/edit', [TimeCardController::class, 'edit'])->name('timecards.edit');
        Route::put('/timecards/{timecard}', [TimeCardController::class, 'update'])->name('timecards.update');
    });

    // Time Punch routes (for API/AJAX)
    Route::post('/time-cards/{timecard}/add-punch', [TimePunchController::class, 'addManualPunch'])->name('timecards.add-punch');
    Route::delete('/time-punches/{punch}/delete', [TimePunchController::class, 'destroy'])->name('timepunches.destroy');

    Route::middleware(['permission:edit_timecards|edit_own_timecards'])->group(function () {
        Route::get('/time-punches/{punch}/edit', [TimePunchController::class, 'edit'])->name('timepunches.edit');
        Route::put('/time-punches/{punch}', [TimePunchController::class, 'update'])->name('timepunches.update');

        // AJAX routes for inline editing
        Route::put('/api/time-punches/{punch}/ajax-update', [TimePunchController::class, 'ajaxUpdate'])->name('timepunches.ajax-update');
    });
});

// Time Card Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/time-clock', [App\Http\Controllers\TimeCardController::class, 'timeClock'])->name('time-clock');
    Route::get('/timecards', [App\Http\Controllers\TimeCardController::class, 'index'])->name('timecards.index');
    Route::post('/timecards/clock-in', [App\Http\Controllers\TimeCardController::class, 'clockIn'])->name('timecards.clock-in');
    Route::post('/timecards/clock-out', [App\Http\Controllers\TimeCardController::class, 'clockOut'])->name('timecards.clock-out');
    Route::post('/timecards/start-break', [App\Http\Controllers\TimeCardController::class, 'startBreak'])->name('timecards.start-break');
    Route::post('/timecards/end-break', [App\Http\Controllers\TimeCardController::class, 'endBreak'])->name('timecards.end-break');
});

/***************************************************************
 * Certificate Routes
 ***************************************************************/

// Contract JSON retrieval (needed for signature component)
Route::middleware(['auth'])->group(function () {
    Route::get('/certificates/contract', [CertificateController::class, 'getContract'])->name('certificates.contract');
});

// create_certificates
Route::middleware(['auth', 'permission:create_certificates'])->group(function () {
    Route::get('/certificates/create', [CertificateController::class, 'create'])->name('certificates.create');
    Route::post('/certificates', [CertificateController::class, 'store'])->name('certificates.store');
});

// view_certificates
Route::middleware(['auth', 'permission:view_certificates'])->group(function () {
    // Certificate search route
    Route::get('/certificatesearch/{query}', [CertificateController::class, 'search'])->name('certificates.search');

    // View certificates
    Route::get('/certificates', [CertificateController::class, 'index'])->name('certificates.index');
    Route::get('/certificates/{certificate}', [CertificateController::class, 'show'])->name('certificates.show');

    // View certificate PDFs
    Route::get('/certificate-pdfs/{pdf}/view', [CertificateController::class, 'viewPdf'])->name('certificates.viewPdf');
    Route::get('/certificates/{certificate}/pdfs', [CertificateController::class, 'viewPdfs'])->name('certificates.viewPdfs');

    // View files
    Route::get('/files/{file}/view', [FileController::class, 'view'])->name('files.view');

    // View devices
    Route::get('/devices/{device}', [DeviceController::class, 'show'])->name('devices.show');
});

// edit_certificates
Route::middleware(['auth', 'permission:edit_certificates'])->group(function () {
    // Edit certificate
    Route::get('/certificates/{certificate}/edit', [CertificateController::class, 'edit'])->name('certificates.edit');
    Route::put('/certificates/{certificate}', [CertificateController::class, 'update'])->name('certificates.update');
    Route::patch('/certificates/{certificate}', [CertificateController::class, 'update']);

    // Auto-save certificate fields
    Route::patch('/certificates/{certificate}/auto-save', [CertificateController::class, 'autoSave'])->name('certificates.autoSave');

    // Save certificate stats
    Route::post('/certificates/{certificate}/stats', [CertificateController::class, 'saveStats'])->name('certificates.saveStats');

    // Manage devices
    Route::post('/devices', [DeviceController::class, 'store'])->name('devices.store');
    Route::post('/devices/{device}/toggle-destruction', [DeviceController::class, 'toggleDestruction'])->name('devices.toggleDestruction');
    Route::post('/devices/{device}/update-notes', [DeviceController::class, 'updateNotes'])->name('devices.updateNotes');

    // Manage signatures
    Route::post('/signatures', [SignatureController::class, 'store'])->name('signatures.store');

    // Manage files
    Route::post('/certificates/{certificate}/files', [FileController::class, 'uploadForCertificate'])->name('certificates.files.upload');
    Route::post('/files/{file}/update-notes', [FileController::class, 'updateNotes'])->name('files.updateNotes');
});

// delete_certificates
Route::middleware(['auth', 'permission:delete_certificates'])->group(function () {
    Route::delete('/certificates/{certificate}', [CertificateController::class, 'destroy'])->name('certificates.destroy');
    Route::delete('/devices/{device}', [DeviceController::class, 'destroy'])->name('devices.destroy');
    Route::delete('/signatures/{signature}', [SignatureController::class, 'destroy'])->name('signatures.destroy');
    Route::post('/certificates/{id}/restore', [CertificateController::class, 'restore'])->name('certificates.restore');
});

// PDF generation and downloads (using view_certificates permission)
Route::middleware(['auth', 'permission:view_certificates'])->group(function () {
    Route::get('/certificates/{certificate}/generate-pdf', [CertificateController::class, 'generatePdf'])->name('certificates.generatePdf');
    Route::get('/certificate-pdfs/{pdf}/download', [CertificateController::class, 'downloadPdf'])->name('certificates.downloadPdf');
    Route::get('/files/{file}/download', [FileController::class, 'download'])->name('files.download');
});

/***************************************************************
 * Mobile Certificate Routes
 ***************************************************************/

// Mobile Certificate Routes (using view_certificates and create_certificates permissions)
Route::middleware(['auth', 'permission:view_certificates|create_certificates'])->group(function () {
    Route::get('/mobile/certificates', [App\Http\Controllers\MobileCertificateController::class, 'index'])->name('mobile.certificates.index');
    Route::post('/mobile/certificates/start', [App\Http\Controllers\MobileCertificateController::class, 'start'])->name('mobile.certificates.start');

    // Step routes
    Route::get('/mobile/certificates/{certificate}/step1', [App\Http\Controllers\MobileCertificateController::class, 'step1'])->name('mobile.certificates.step1');
    Route::post('/mobile/certificates/{certificate}/step1', [App\Http\Controllers\MobileCertificateController::class, 'saveStep1'])->name('mobile.certificates.saveStep1');

    Route::get('/mobile/certificates/{certificate}/step2', [App\Http\Controllers\MobileCertificateController::class, 'step2'])->name('mobile.certificates.step2');
    Route::post('/mobile/certificates/{certificate}/step2', [App\Http\Controllers\MobileCertificateController::class, 'saveStep2'])->name('mobile.certificates.saveStep2');

    Route::get('/mobile/certificates/{certificate}/step3', [App\Http\Controllers\MobileCertificateController::class, 'step3'])->name('mobile.certificates.step3');
    Route::post('/mobile/certificates/{certificate}/step3', [App\Http\Controllers\MobileCertificateController::class, 'saveStep3'])->name('mobile.certificates.saveStep3');

    Route::get('/mobile/certificates/{certificate}/step4', [App\Http\Controllers\MobileCertificateController::class, 'step4'])->name('mobile.certificates.step4');
    Route::post('/mobile/certificates/{certificate}/step4', [App\Http\Controllers\MobileCertificateController::class, 'saveStep4'])->name('mobile.certificates.saveStep4');

    Route::get('/mobile/certificates/{certificate}/step5', [App\Http\Controllers\MobileCertificateController::class, 'step5'])->name('mobile.certificates.step5');
    Route::post('/mobile/certificates/{certificate}/step5', [App\Http\Controllers\MobileCertificateController::class, 'saveStep5'])->name('mobile.certificates.saveStep5');

    Route::get('/mobile/certificates/{certificate}/success', [App\Http\Controllers\MobileCertificateController::class, 'success'])->name('mobile.certificates.success');
});

/***************************************************************
 * Payout Routes
 ***************************************************************/

// Create Payouts
Route::middleware(['auth', 'permission:create_invoices'])->group(function () {
    Route::get('/payouts/create', [PayoutController::class, 'create'])->name('payouts.create');
    Route::post('/payouts', [PayoutController::class, 'store'])->name('payouts.store');
});

// Mobile Payout Routes (EZ Payout System)
Route::middleware(['auth', 'permission:create_invoices'])->group(function () {
    Route::get('/mobile/payouts', [App\Http\Controllers\MobilePayoutController::class, 'index'])->name('mobile.payouts.index');
    Route::post('/mobile/payouts/start', [App\Http\Controllers\MobilePayoutController::class, 'start'])->name('mobile.payouts.start');

    // Step routes
    Route::get('/mobile/payouts/{mobilePayout}/step1', [App\Http\Controllers\MobilePayoutController::class, 'step1'])->name('mobile.payouts.step1');
    Route::post('/mobile/payouts/{mobilePayout}/step1', [App\Http\Controllers\MobilePayoutController::class, 'saveStep1'])->name('mobile.payouts.saveStep1');

    Route::get('/mobile/payouts/{mobilePayout}/step2', [App\Http\Controllers\MobilePayoutController::class, 'step2'])->name('mobile.payouts.step2');
    Route::post('/mobile/payouts/{mobilePayout}/step2', [App\Http\Controllers\MobilePayoutController::class, 'saveStep2'])->name('mobile.payouts.saveStep2');

    Route::get('/mobile/payouts/{mobilePayout}/step3', [App\Http\Controllers\MobilePayoutController::class, 'step3'])->name('mobile.payouts.step3');
    Route::post('/mobile/payouts/{mobilePayout}/step3', [App\Http\Controllers\MobilePayoutController::class, 'saveStep3'])->name('mobile.payouts.saveStep3');



    Route::get('/mobile/payouts/{mobilePayout}/success', [App\Http\Controllers\MobilePayoutController::class, 'success'])->name('mobile.payouts.success');
});

// End of certificate routes

/***************************************************************
 * Activity Log Routes
 ***************************************************************/

// View Activity Logs (Admin only)
Route::middleware(['auth', 'permission:view_activity_logs'])->group(function () {
    Route::get('/activity-logs', [ActivityLogController::class, 'index'])->name('activity-logs.index');
    Route::get('/activity-logs/statistics', [ActivityLogController::class, 'statistics'])->name('activity-logs.statistics');
    Route::get('/activity-logs/export', [ActivityLogController::class, 'export'])->name('activity-logs.export');
    Route::get('/activity-logs/{activityLog}', [ActivityLogController::class, 'show'])->name('activity-logs.show');

    // AJAX endpoints
    Route::get('/api/activity-logs/for-model', [ActivityLogController::class, 'forModel'])->name('api.activity-logs.for-model');
});

// Google Maps API routes (server-side handling)
Route::middleware(['auth'])->prefix('api/google-maps')->group(function () {
    Route::post('/autocomplete', [App\Http\Controllers\Api\GoogleMapsController::class, 'autocomplete'])->name('api.google-maps.autocomplete');
    Route::post('/place-details', [App\Http\Controllers\Api\GoogleMapsController::class, 'placeDetails'])->name('api.google-maps.place-details');
    Route::post('/session-token', [App\Http\Controllers\Api\GoogleMapsController::class, 'generateSessionToken'])->name('api.google-maps.session-token');
});

// Guest-accessible Google Maps API routes (for pickup request form)
Route::prefix('api/google-maps/guest')->group(function () {
    Route::post('/autocomplete', [App\Http\Controllers\Api\GoogleMapsController::class, 'autocomplete'])->name('api.google-maps.guest.autocomplete');
    Route::post('/place-details', [App\Http\Controllers\Api\GoogleMapsController::class, 'placeDetails'])->name('api.google-maps.guest.place-details');
    Route::post('/session-token', [App\Http\Controllers\Api\GoogleMapsController::class, 'generateSessionToken'])->name('api.google-maps.guest.session-token');
});

// Test route for Google Maps autocomplete debugging
Route::middleware(['auth'])->group(function () {
    Route::get('/test-autocomplete', function () {
        return view('test-autocomplete');
    });

    Route::post('/test-autocomplete', function () {
        $data = request()->all();
        return response()->json([
            'message' => 'Form submitted successfully',
            'data' => $data
        ]);
    });

    // Test route for place details debugging
    Route::get('/test-place-details', function () {
        return view('test-place-details');
    });
});

/***************************************************************
 * Notification Routes
 ***************************************************************/

// View notifications (all users can view their own notifications)
Route::middleware(['auth'])->group(function () {
    // API endpoints for notifications
    Route::get('/api/notifications', [App\Http\Controllers\NotificationController::class, 'getUserNotifications'])->name('api.notifications.user');
    Route::get('/api/notifications/count', [App\Http\Controllers\NotificationController::class, 'getNotificationCount'])->name('api.notifications.count');
    Route::post('/api/notifications/{notification}/read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('api.notifications.read');
    Route::post('/api/notifications/{notification}/dismiss', [App\Http\Controllers\NotificationController::class, 'markAsDismissed'])->name('api.notifications.dismiss');
    Route::post('/api/notifications/dismiss-all', [App\Http\Controllers\NotificationController::class, 'dismissAll'])->name('api.notifications.dismiss-all');
    
    // Push notification API endpoints
    Route::get('/api/notifications/vapid-public-key', [App\Http\Controllers\Api\PushNotificationController::class, 'getVapidPublicKey'])->name('api.push-notifications.vapid-key');
    Route::post('/api/notifications/subscribe', [App\Http\Controllers\Api\PushNotificationController::class, 'subscribe'])->name('api.push-notifications.subscribe');
    Route::post('/api/notifications/unsubscribe', [App\Http\Controllers\Api\PushNotificationController::class, 'unsubscribe'])->name('api.push-notifications.unsubscribe');
    Route::post('/api/notifications/test', [App\Http\Controllers\Api\PushNotificationController::class, 'sendTest'])->name('api.push-notifications.test');
    Route::get('/api/notifications/subscriptions', [App\Http\Controllers\Api\PushNotificationController::class, 'getSubscriptions'])->name('api.push-notifications.subscriptions');
    Route::post('/api/notifications/generate-vapid-keys', [App\Http\Controllers\Api\PushNotificationController::class, 'generateVapidKeys'])->name('api.push-notifications.generate-keys');

});

// View notification management (requires permission)
Route::middleware(['auth', 'permission:view_notifications'])->group(function () {
    Route::get('/notifications', [App\Http\Controllers\NotificationController::class, 'index'])->name('notifications.index');
});

// Create notifications (requires permission)
Route::middleware(['auth', 'permission:create_notifications'])->group(function () {
    Route::get('/notifications/create', [App\Http\Controllers\NotificationController::class, 'create'])->name('notifications.create');
    Route::post('/notifications', [App\Http\Controllers\NotificationController::class, 'store'])->name('notifications.store');
});

// Manage notifications (requires permission)
Route::middleware(['auth', 'permission:manage_notifications'])->group(function () {
    Route::delete('/notifications/{notification}', [App\Http\Controllers\NotificationController::class, 'destroy'])->name('notifications.destroy');
});

/***************************************************************
 * Custom Form System Routes
 ***************************************************************/

// Public Form Routes (No Authentication Required)
Route::get('/forms/{slug}', [App\Http\Controllers\PublicFormController::class, 'show'])->name('forms.public.show');
Route::post('/forms/{slug}/submit', [App\Http\Controllers\PublicFormController::class, 'submit'])->name('forms.public.submit');
Route::get('/forms/{slug}/thank-you/{submission}', [App\Http\Controllers\PublicFormController::class, 'thankYou'])->name('forms.public.thank-you');
Route::post('/forms/check-email', [App\Http\Controllers\PublicFormController::class, 'checkEmail'])->name('forms.public.check-email');

// Form Management Routes (Authenticated)
Route::middleware(['auth', 'permission:manage_forms'])->group(function () {
    Route::resource('admin/forms', App\Http\Controllers\FormController::class);
    Route::get('/admin/forms/{form}/builder', [App\Http\Controllers\FormController::class, 'builder'])->name('forms.builder');
    Route::post('/admin/forms/{form}/save-fields', [App\Http\Controllers\FormController::class, 'saveFields'])->name('forms.save-fields');
    Route::get('/admin/forms/{form}/preview', [App\Http\Controllers\FormController::class, 'preview'])->name('forms.preview');
    Route::post('/admin/forms/{form}/clone', [App\Http\Controllers\FormController::class, 'clone'])->name('forms.clone');
});

// Form Submission Management Routes (Authenticated)
Route::middleware(['auth', 'permission:view_form_submissions'])->group(function () {
    Route::get('/admin/form-submissions', [App\Http\Controllers\FormSubmissionController::class, 'index'])->name('form-submissions.index');
    Route::get('/admin/form-submissions/{formSubmission}', [App\Http\Controllers\FormSubmissionController::class, 'show'])->name('form-submissions.show');
    Route::get('/admin/form-submissions/{formSubmission}/export', [App\Http\Controllers\FormSubmissionController::class, 'export'])->name('form-submissions.export');
    Route::get('/admin/form-submissions/{formSubmission}/pdf', [App\Http\Controllers\FormSubmissionController::class, 'generatePdf'])->name('form-submissions.pdf');
    Route::post('/admin/form-submissions/{formSubmission}/email-pdf', [App\Http\Controllers\FormSubmissionController::class, 'emailPdf'])->name('form-submissions.email-pdf');
});

Route::middleware(['auth', 'permission:manage_form_submissions'])->group(function () {
    Route::post('/admin/form-submissions/{formSubmission}/review', [App\Http\Controllers\FormSubmissionController::class, 'review'])->name('form-submissions.review');
    Route::post('/admin/form-submissions/{formSubmission}/approve', [App\Http\Controllers\FormSubmissionController::class, 'approve'])->name('form-submissions.approve');
    Route::post('/admin/form-submissions/{formSubmission}/reject', [App\Http\Controllers\FormSubmissionController::class, 'reject'])->name('form-submissions.reject');
    Route::post('/admin/form-submissions/{formSubmission}/link-customer', [App\Http\Controllers\FormSubmissionController::class, 'linkCustomer'])->name('form-submissions.link-customer');
    Route::post('/admin/form-submissions/bulk-action', [App\Http\Controllers\FormSubmissionController::class, 'bulkAction'])->name('form-submissions.bulk-action');
});

