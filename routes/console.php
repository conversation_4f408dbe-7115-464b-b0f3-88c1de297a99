<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use App\Models\GlobalConfig;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

// Get the configuration values
$reportTime = GlobalConfig::getTimeclockReportTime() ?: '07:00';
$isEnabled = GlobalConfig::isFeatureEnabled('timeclock_daily_report');

// Conditionally schedule the command
if ($isEnabled) {
    Schedule::command('timeclock:send-daily-report')->dailyAt($reportTime);
}

// Auto clock-out scheduling
$autoClockOutEnabled = GlobalConfig::isAutoClockOutEnabled();
$autoClockOutTime = GlobalConfig::getAutoClockOutTime();

if ($autoClockOutEnabled && $autoClockOutTime) {
    // Schedule the auto clock-out command to run 1 minute after the specified time
    $scheduledTime = \Carbon\Carbon::parse($autoClockOutTime)->addMinute()->format('H:i');
    Schedule::command('timeclock:auto-clock-out')->dailyAt($scheduledTime);
}

// Pickup confirmation reminder scheduling
// Run every hour to check for pickup requests that need 48-hour confirmation reminders
Schedule::job(new \App\Jobs\SendPickupConfirmationReminder)->hourly();

// Alternative pickup reminder command (using new database fields)
// Run every hour to send reminder emails 48 hours before pickup
Schedule::command('pickups:send-reminders')->hourly();