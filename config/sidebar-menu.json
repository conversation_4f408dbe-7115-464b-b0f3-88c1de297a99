{"menu_items": [{"type": "item", "route": "dashboard", "title": "Dashboard", "icon": "fa-sharp fa-solid fa-tachometer-alt", "permissions": []}, {"type": "dropdown", "title": "Inventory", "icon": "fa-sharp fa-solid fa-box", "permissions": ["view_inventory_items"], "items": [{"route": "inventory.index", "title": "All Items", "icon": "fa-sharp fa-solid fa-box", "permissions": []}, {"route": "inventory_categories.index", "title": "Categories", "icon": "fa-sharp fa-solid fa-tags", "permissions": ["manage_settings"]}, {"route": "inventory.create", "title": "Add New", "icon": "fa-sharp fa-solid fa-plus-circle", "permissions": ["create_inventory_items"]}]}, {"type": "item", "route": "customers.index", "title": "Customers", "icon": "fa-sharp fa-solid fa-users", "permissions": []}, {"type": "item", "route": "certificates.index", "title": "Certificates", "icon": "fa-sharp fa-solid fa-file-certificate", "permissions": ["view_certificates"]}, {"type": "dropdown", "title": "Calendar", "icon": "fa-sharp fa-solid fa-calendar-alt", "permissions": ["view_calendars"], "items": [{"route": "calendars.index", "title": "All Calendars", "icon": "fa-sharp fa-solid fa-calendar", "permissions": []}, {"route": "pickup.calendar", "title": "Pickup Calendar", "icon": "fa-sharp fa-solid fa-truck", "permissions": []}, {"route": "calendars.create", "title": "Create Calendar", "icon": "fa-sharp fa-solid fa-plus-circle", "permissions": ["create_calendars"]}, {"route": "pickup-requests.index", "title": "Pickup Requests", "icon": "fa-sharp fa-solid fa-clipboard-list", "permissions": ["view_pickup_requests"]}]}, {"type": "dropdown", "title": "Time Clock", "icon": "fa-sharp fa-solid fa-clock", "permissions": [], "items": [{"route": "time-clock", "title": "Clock In/Out", "icon": "fa-sharp fa-solid fa-user-clock", "permissions": []}, {"route": "timecards.index", "title": "Time Cards", "icon": "fa-sharp fa-solid fa-calendar-check", "permissions": ["view_own_timecards"]}, {"route": "timesheets.index", "title": "Timesheets", "icon": "fa-sharp fa-solid fa-file-invoice", "permissions": ["view_timesheets"]}]}, {"type": "dropdown", "title": "Sales", "icon": "fa-sharp fa-solid fa-cash-register", "permissions": ["view_invoices"], "items": [{"route": "invoices.index", "title": "Invoices", "icon": "fa-sharp fa-solid fa-file-invoice", "permissions": []}, {"route": "discounts.index", "title": "Discounts", "icon": "fa-sharp fa-solid fa-percent", "permissions": ["create_discounts"]}, {"route": "customer_discounts.index", "title": "Customer Discounts", "icon": "fa-sharp fa-solid fa-user-tag", "permissions": ["create_discounts"]}, {"route": "discounts.checker", "title": "Discount Checker", "icon": "fa-sharp fa-solid fa-search", "permissions": []}, {"route": "payouts.create", "title": "New Payout", "icon": "fa-sharp fa-solid fa-money-bill-wave", "permissions": []}, {"route": "mobile.payouts.index", "title": "EZ Payout", "icon": "fa-sharp fa-solid fa-mobile-screen-button", "permissions": []}]}, {"type": "item", "route": "documentation.index", "title": "Docs", "icon": "fa-sharp fa-solid fa-book", "permissions": ["view_document_items"]}, {"type": "item", "route": "images.index", "title": "Images", "icon": "fa-sharp fa-solid fa-images", "permissions": ["view_image_gallery"]}, {"type": "dropdown", "title": "Tasks", "icon": "fa-sharp fa-solid fa-tasks", "permissions": ["view_tasks"], "condition": "tasks_enabled", "items": [{"route": "my-tasks", "title": "My Tasks", "icon": "fa-sharp fa-solid fa-tasks", "permissions": []}, {"route": "tasks.index", "title": "All User Tasks", "icon": "fa-sharp fa-solid fa-tasks", "permissions": ["edit_tasks"]}, {"route": "tasks.create", "title": "Add New Task", "icon": "fa-sharp fa-solid fa-plus-circle", "permissions": ["create_tasks", "create_edit_own_tasks"]}]}, {"type": "dropdown", "title": "Reports", "icon": "fa-sharp fa-solid fa-chart-line", "permissions": ["view_reports"], "items": [{"route": "reports.index", "title": "Report Dashboard", "icon": "fa-sharp fa-solid fa-chart-line", "permissions": []}, {"route": "reports.list", "title": "View Reports", "icon": "fa-sharp fa-solid fa-file-alt", "permissions": []}, {"route": "reports.create", "title": "Generate Report", "icon": "fa-sharp fa-solid fa-file-export", "permissions": []}, {"route": "activity-logs.index", "title": "Activity Logs", "icon": "fa-sharp fa-solid fa-history", "permissions": ["view_activity_logs"]}]}, {"type": "dropdown", "title": "Forms", "icon": "fa-sharp fa-solid fa-file-alt", "permissions": ["view_forms"], "items": [{"route": "forms.index", "title": "All Forms", "icon": "fa-sharp fa-solid fa-file-alt", "permissions": []}, {"route": "form-submissions.index", "title": "All Submissions", "icon": "fa-sharp fa-solid fa-paper-plane", "permissions": ["view_form_submissions"]}, {"route": "forms.create", "title": "Create Form", "icon": "fa-sharp fa-solid fa-plus-circle", "permissions": ["manage_forms"]}]}, {"type": "dropdown", "title": "Administrative", "icon": "fa-sharp fa-solid fa-cogs", "permissions": ["manage_settings"], "items": [{"route": "admin.api-integrations.index", "title": "APIs and Integrations", "icon": "fa-sharp fa-solid fa-plug", "permissions": []}, {"route": "globalconfig.edit", "title": "Global Config", "icon": "fa-sharp fa-solid fa-cog", "permissions": []}, {"route": "tax_policies.index", "title": "Tax Policies", "icon": "fa-sharp fa-solid fa-coins", "permissions": []}, {"route": "departments.index", "title": "Departments", "icon": "fa-sharp fa-solid fa-building", "permissions": []}, {"route": "permissions.index", "title": "Permissions", "icon": "fa-sharp fa-solid fa-key", "permissions": []}, {"route": "admin.users.index", "title": "User Manager", "icon": "fa-sharp fa-solid fa-users", "permissions": []}, {"route": "user_groups.index", "title": "User Groups", "icon": "fa-sharp fa-solid fa-users-cog", "permissions": []}, {"route": "admin.tools.index", "title": "<PERSON><PERSON>", "icon": "fa-sharp fa-solid fa-toolbox", "permissions": []}, {"route": "admin.email-templates.index", "title": "Email Templates", "icon": "fa-sharp fa-solid fa-envelope", "permissions": ["manage_settings"]}, {"route": "notifications.index", "title": "Notifications", "icon": "fa-sharp fa-solid fa-bell", "permissions": ["view_notifications"]}]}]}