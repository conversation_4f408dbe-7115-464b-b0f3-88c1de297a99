APP_NAME=ETRFlow
APP_ENV=testing
APP_KEY=base64:gAoduXZIK07AwjKRRcikm/ozAoe8+DQyYxZ6kaD3OvU=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Use SQLite for testing (faster and isolated)
DB_CONNECTION=sqlite
DB_DATABASE=:memory:

# Or use a separate MySQL database for testing
# DB_CONNECTION=mysql
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=etrflow2_test
# DB_USERNAME=your_username
# DB_PASSWORD=your_password

BROADCAST_DRIVER=log
CACHE_DRIVER=array
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=array
SESSION_LIFETIME=120

MAIL_MAILER=array
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
