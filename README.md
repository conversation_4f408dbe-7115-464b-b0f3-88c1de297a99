# ETRFlow

## Setup Instructions

### Composer Dependencies Installation

This project uses Composer to manage PHP dependencies. To install all required packages:

```bash
composer install
```

If you're in a development environment, you may want to include development dependencies:

```bash
composer install --dev
```

To update dependencies to their latest versions according to the composer.json constraints:

```bash
composer update
```

### Environment Configuration

The application uses a `.env` file for environment-specific configuration. To set up your environment:

1. Copy the `.env.example` file to `.env` if you haven't already:

```bash
cp .env.example .env
```

2. Generate an application key:

```bash
php artisan key:generate
```

3. Configure your database connection in the `.env` file:

```
DB_CONNECTION=mariadb
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=etrflow
DB_USERNAME=root
DB_PASSWORD=your_password
```

4. Set any other environment-specific variables as needed.

#### Updating Environment Variables

To update an environment variable in the `.env` file:

1. Open the `.env` file in a text editor
2. Find the variable you want to change or add a new one if it doesn't exist
3. Set the value using the format `VARIABLE_NAME=value`
   - For paths, use forward slashes even on Windows: `PATH_VARIABLE="C:/path/to/file.exe"`
   - Avoid using backslashes in the `.env` file as they can cause parsing errors
4. Save the file
5. Restart your application server if necessary

**Important**: After changing environment variables, you may need to clear the configuration cache:

```bash
php artisan config:clear
```

Some environment variables (like the `PYTHON_PATH` described below) are read directly using the `env()` function and don't require clearing the cache.

### File Storage Setup

The application uses both public and private file storage. To set up the private storage directory with proper permissions, run the following command:

#### Linux/Mac:
```bash
mkdir -p storage/app/private
chmod -R 775 storage/app/private
```

#### Windows (PowerShell):
```powershell
New-Item -ItemType Directory -Force -Path storage/app/private
icacls storage/app/private /grant "Everyone:(OI)(CI)F"
```

This ensures that the application can store and retrieve files from the private storage directory.

### Python Setup for PDF Merging

The application uses Python with PyPDF2 for merging PDF files, especially for handling compressed PDFs that FPDI cannot process. This is necessary for including Chain of Custody documents in the generated PDFs. To set up the Python environment:

1. Make sure Python 3.6+ is installed on your system
2. Setup venv
3. Install the PyPDF2 package:

#### Standard Installation (for development environments)

```bash
# Setup venv
python -m venv .venv
venv\Scripts\activate

# Install PyPDF2 globally
# For Linux/macOS
pip3 install PyPDF2

# For Windows
python -m pip install PyPDF2
```

#### Installation for Shared Hosting or Production Environments

If you're on a shared hosting environment or don't have permission to install packages globally, you can install PyPDF2 in a custom directory within your application:

```bash
# Create a custom directory for Python packages
mkdir -p /path/to/your/app/python_packages

# Install PyPDF2 to the custom directory
pip3 install --target=/path/to/your/app/python_packages PyPDF2

# Make sure the directory has the right permissions
chmod -R 755 /path/to/your/app/python_packages
```

Replace `/path/to/your/app` with the actual path to your application. For example, on a Cloudways server, it might be something like `/home/<USER>/applications/appname/public_html`.

3. Set the path to your Python executable and packages directory in the `.env` file (see [Environment Configuration](#environment-configuration) section):

```
# For Windows (use forward slashes even on Windows)
PYTHON_PATH="C:/Python312/python.exe"

# For Linux/macOS
PYTHON_PATH="/usr/bin/python3"

# If PyPDF2 is installed in a non-standard location, specify the path to the packages directory
# For standard installation (pip install PyPDF2)
PYTHON_PACKAGES="/home/<USER>/.local/lib/python3.9/site-packages"

# For custom installation in shared hosting (pip install --target=...)
PYTHON_PACKAGES="/path/to/your/app/python_packages"
```

The application will use the `PYTHON_PATH` to execute the Python script for PDF merging. If not specified, it will fall back to using the `python` command, which should work if Python is in your system PATH.

The `PYTHON_PACKAGES` variable is used to tell Python where to find installed modules like PyPDF2. This is particularly useful in shared hosting environments where you might not have permission to install packages system-wide.

After updating these variables in your `.env` file, you don't need to clear the configuration cache as they are read directly using the `env()` function.

#### Troubleshooting PDF Merging Issues

If you encounter issues with PDF merging, here are some common problems and solutions:

1. **PyPDF2 Not Found Error**:
   ```
   ERROR: Failed to import PyPDF2: No module named 'PyPDF2'
   ```

   **Solution**: Make sure PyPDF2 is installed and the `PYTHON_PACKAGES` environment variable is set correctly in your `.env` file.

2. **Permission Denied Error**:
   ```
   sh: 1: /path/to/python: Permission denied
   ```

   **Solution**: Make sure the Python executable specified in `PYTHON_PATH` has execute permissions and is accessible by the web server user.

3. **Cannot Write to Cache Directory**:
   ```
   WARNING: The directory '/home/<USER>/.cache/pip' or its parent directory is not owned or is not writable by the current user.
   ```

   **Solution**: Use the custom installation method described above to install PyPDF2 in a directory that the web server user can access.

4. **PDF Compression Issues**:
   If you're having trouble with compressed PDFs, make sure you're using a recent version of PyPDF2 (3.0.0+) which has better support for compressed PDFs.

For more detailed diagnostics, check the Laravel logs in `storage/logs/laravel.log`.

### Public Storage Link

Laravel uses symbolic links to make files in the storage directory accessible from the web. To create this link, run the following Artisan command:

```bash
php artisan storage:link
```

This command creates a symbolic link from `public/storage` to `storage/app/public`, allowing files stored in the public disk to be accessible via the web.

### User Setup

#### Creating an Admin User

To set up an admin user for ETRFlow, follow these steps:

1. First, run the database migrations to create all required tables:

```bash
php artisan migrate --path=database/migrations/2024_12_15_230250_create_customers_table.php
php artisan migrate
```

> **Note**: If you encounter foreign key constraint errors during migration (particularly with the certificates table referencing the customers table), you need to run the customers table migration first as shown above. This is because the certificates table has a foreign key to the customers table, but the migration timestamps would normally run the certificates migration first.

2. Create the first admin user with ID 1 (required for seeders):

```bash
php artisan tinker
```

Then in the Tinker console:

```php
$user = new \App\Models\User();
$user->id = 1; // Important: This must be ID 1 for seeders to work
$user->name = 'Admin User';
$user->email = '<EMAIL>';
$user->password = bcrypt('your_secure_password');
$user->role = 'admin';
$user->enabled = true;
$user->save();
exit;
```

3. Now run the database seeders:

```bash
php artisan db:seed
```

4. Add the admin user to the Admin group:

```bash
php artisan users:add-user-to-group <EMAIL> Admin
```

5. If needed, ensure the user is enabled:

```bash
php artisan users:enable-user
```

When prompted, enter the email address of the admin user.

6. Verify that the user has been created and assigned to the Admin group by logging in with the credentials.

#### User Permissions

ETRFlow uses a permission-based system with user groups. The Admin group has access to all permissions by default. To ensure all permissions are properly synced in the database, run:

```bash
php artisan permissions:sync
```

This command will create all necessary permissions and ensure the Admin group has access to them.

### Browser Push Notifications Setup

ETRFlow2 includes browser push notifications that allow users to receive real-time alerts on their desktop and mobile devices, even when the application is not actively being viewed.

#### Prerequisites

1. **HTTPS Required**: Push notifications only work over HTTPS connections (or localhost for development)
2. **Modern Browser**: Requires Chrome 50+, Firefox 44+, Safari 16+, or Edge 17+

#### Setup Steps

1. **Install WebPush Library** (already included in composer.json):
   ```bash
   composer require minishlink/web-push
   ```

2. **Generate VAPID Keys**:
   ```bash
   php artisan webpush:generate-keys
   ```
   
   This command will output VAPID keys that you need to add to your `.env` file:
   ```env
   # Web Push Notifications
   WEBPUSH_PUBLIC_KEY="your_generated_public_key"
   WEBPUSH_PRIVATE_KEY="your_generated_private_key"
   ```

3. **Clear Configuration Cache**:
   ```bash
   php artisan config:clear
   ```

4. **Run Database Migration** (if not already done):
   The push notification system requires the `notification_subscriptions` table which is included in the standard migrations.

#### User Setup

After the system is configured, users can enable push notifications:

1. **Navigate to Profile**: Go to `/profile` in the application
2. **Browser Notifications Section**: Find the "Browser Notifications" card
3. **Enable Notifications**: Click the "Enable" button
4. **Grant Permission**: Allow notifications in the browser permission dialog
5. **Test**: Use the "Test" button to verify notifications are working

#### Features

- **Automatic Integration**: All existing notifications automatically send push notifications
- **Multi-Device Support**: Users can enable notifications on multiple devices/browsers
- **Targeting Support**: Supports all notification targeting (all users, specific users, user groups)
- **Progressive Enhancement**: System works with or without push notifications enabled

#### Troubleshooting

**Common Issues:**

1. **"Permission denied"**: User needs to grant notification permission in browser
2. **"No subscriptions found"**: User must enable notifications in their profile settings
3. **VAPID key errors**: Ensure keys are properly generated and added to `.env` file
4. **HTTPS requirement**: Push notifications require HTTPS (except on localhost)

**Browser Settings:**
- **Chrome**: Settings → Privacy and Security → Site Settings → Notifications
- **Firefox**: Preferences → Privacy & Security → Permissions → Notifications
- **Safari**: Preferences → Websites → Notifications

**Testing:**
- Use the test button in user profile settings
- Check browser developer console for detailed error messages
- Check Laravel logs at `storage/logs/laravel.log` for server-side issues

#### Development Notes

- Service worker automatically registers at `/service-worker.js`
- JavaScript client loads automatically on all authenticated pages
- API endpoints available at `/api/notifications/*` for subscription management
- All notifications created via `NotificationService` or `NotificationHelper` automatically trigger push notifications

## Complete Setup Checklist

For a fresh ETRFlow2 installation, follow these steps in order:

### 1. Dependencies and Environment
```bash
# Install PHP and Node dependencies
composer install
npm install

# Setup environment
cp .env.example .env
php artisan key:generate
php artisan storage:link
```

### 2. Configure Environment Variables
Edit your `.env` file and configure:
```env
# Database
DB_CONNECTION=mariadb
DB_HOST=127.0.0.1
DB_DATABASE=etrflow
DB_USERNAME=root
DB_PASSWORD=your_password

# Python for PDF operations
PYTHON_PATH="/usr/bin/python3"  # or "C:/Python312/python.exe"
PYTHON_PACKAGES="/path/to/python/packages"

# API Keys (optional but recommended)
GOOGLE_MAPS_API_KEY=your_key
OPENAI_API_KEY=your_key
RECAPTCHA_SITE_KEY=your_key
RECAPTCHA_SECRET_KEY=your_key
```

### 3. Generate VAPID Keys for Push Notifications
```bash
php artisan webpush:generate-keys
```
Add the generated keys to your `.env` file:
```env
WEBPUSH_PUBLIC_KEY="your_generated_public_key"
WEBPUSH_PRIVATE_KEY="your_generated_private_key"
```

### 4. Database Setup
```bash
# Run migrations (customers table first for foreign key constraints)
php artisan migrate --path=database/migrations/2024_12_15_230250_create_customers_table.php
php artisan migrate

# Create admin user (required for seeders)
php artisan tinker
```

In Tinker console:
```php
$user = new \App\Models\User();
$user->id = 1; // Important: Must be ID 1
$user->name = 'Admin User';
$user->email = '<EMAIL>';
$user->password = bcrypt('your_secure_password');
$user->role = 'admin';
$user->enabled = true;
$user->save();
exit;
```

### 5. Final Setup Commands
```bash
# Run seeders
php artisan db:seed

# Setup user groups and permissions
php artisan users:add-user-to-group <EMAIL> Admin
php artisan permissions:sync

# Clear configuration cache
php artisan config:clear
```

### 6. Python Setup (for PDF features)
```bash
# Install PyPDF2
pip3 install PyPDF2

# Or for custom directory installation:
pip3 install --target=/path/to/your/app/python_packages PyPDF2
```

### 7. Development Server
```bash
# Start all development services
composer run dev

# Or start services individually:
php artisan serve
php artisan queue:listen
npm run dev
```

Your ETRFlow2 installation should now be ready! Log in with your admin credentials and configure push notifications in your profile settings.

#### Troubleshooting Database Migrations

If you encounter errors during the migration process, here are some common issues and solutions:

1. **Foreign Key Constraint Errors**:
   - Error: `SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'customers'`
   - Solution: Run the customers table migration first, then run the remaining migrations as shown in the setup instructions.

   - Error: `SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`etrflow`.`departments`, CONSTRAINT `departments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE)`
   - Solution: Create a user with ID 1 before running the seeders. The DepartmentsTableSeeder expects a user with ID 1 to exist.

2. **Table Already Exists**:
   - Error: `SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists`
   - Solution: If you're starting fresh, you can reset the database with `php artisan migrate:fresh` (warning: this will delete all data).

3. **Permission Issues**:
   - If you encounter permission issues with the database, ensure your database user has the necessary privileges.
   - For local development, you may need to grant all privileges to your database user.

4. **Missing Tables After Migration**:
   - If some tables are missing after migration, check the migration files to ensure they are being created.
   - You can run specific migrations with `php artisan migrate --path=database/migrations/specific_migration_file.php`.

### Customer Type Migration

If you need to update customer types in the database (e.g., changing 'Walkin Customer' to 'Residential Customer'), follow these steps:

1. Create a migration file:
```bash
php artisan make:migration update_walkin_customer_to_residential_customer
```

2. Update the migration file with the following code:
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update customer type from 'Walkin Customer' to 'Residential Customer'
        DB::table('customers')
            ->where('type', 'Walkin Customer')
            ->update(['type' => 'Residential Customer']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert customer type from 'Residential Customer' back to 'Walkin Customer'
        DB::table('customers')
            ->where('type', 'Residential Customer')
            ->update(['type' => 'Walkin Customer']);
    }
};
```

3. Run the specific migration to avoid conflicts with other migrations:
```bash
php artisan migrate --path=/database/migrations/yyyy_mm_dd_hhmmss_update_walkin_customer_to_residential_customer.php
```
Replace `yyyy_mm_dd_hhmmss` with the timestamp prefix of your migration file.

4. To handle migration conflicts (e.g., when a migration is already run before), use migration status checking:
```bash
php artisan migrate:status
```
This will show you which migrations have run and which haven't.

# When Updating Production Site...


### Running Vite on Production Server

After updating files on the production server, you need to rebuild the assets using Vite. Run the following command to generate optimized production-ready assets:

```bash
npm run build
```

This will create the production assets in the `dist` directory (or as configured in your Vite setup). Ensure your web server is configured to serve these updated assets.