/*!
 * Font Awesome Pro 6.7.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */
:root, :host {
  --fa-style-family-sharp-duotone: 'Font Awesome 6 Sharp Duotone';
  --fa-font-sharp-duotone-thin: normal 100 1em/1 'Font Awesome 6 Sharp Duotone'; }

@font-face {
  font-family: 'Font Awesome 6 Sharp Duotone';
  font-style: normal;
  font-weight: 100;
  font-display: block;
  src: url("../webfonts/fa-sharp-duotone-thin-100.woff2") format("woff2"), url("../webfonts/fa-sharp-duotone-thin-100.ttf") format("truetype"); }

.fasdt,
.fa-sharp-duotone.fa-thin {
  position: relative;
  font-weight: 100;
  letter-spacing: normal; }

.fasdt::before,
.fa-sharp-duotone.fa-thin::before {
  position: absolute;
  color: var(--fa-primary-color, inherit);
  opacity: var(--fa-primary-opacity, 1); }

.fasdt::after,
.fa-sharp-duotone.fa-thin::after {
  color: var(--fa-secondary-color, inherit);
  opacity: var(--fa-secondary-opacity, 0.4); }

.fa-swap-opacity .fasdt::before,
.fa-swap-opacity .fa-sharp-duotone.fa-thin::before,
.fasdt.fa-swap-opacity::before,
.fa-sharp-duotone.fa-swap-opacity::before,
.fa-sharp-duotone.fa-thin.fa-swap-opacity::before {
  opacity: var(--fa-secondary-opacity, 0.4); }

.fa-swap-opacity .fasdt::after,
.fa-swap-opacity .fa-sharp-duotone.fa-thin::after,
.fasdt.fa-swap-opacity::after,
.fa-sharp-duotone.fa-swap-opacity::after,
.fa-sharp-duotone.fa-thin.fa-swap-opacity::after {
  opacity: var(--fa-primary-opacity, 1); }

.fasdt.fa-inverse,
.fa-sharp-duotone.fa-thin.fa-inverse {
  color: var(--fa-inverse, #fff); }

.fasdt.fa-stack-1x,
.fasdt.fa-stack-2x,
.fa-sharp-duotone.fa-thin.fa-stack-1x,
.fa-sharp-duotone.fa-thin.fa-stack-2x {
  position: absolute; }
