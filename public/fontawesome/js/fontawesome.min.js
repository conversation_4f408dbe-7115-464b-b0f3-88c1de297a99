/*!
 * Font Awesome Pro 6.7.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */
{function he(t,e,a){(e=(t=>"symbol"==typeof(t=((t,e)=>{if("object"!=typeof t||!t)return t;var a=t[Symbol.toPrimitive];if(void 0===a)return("string"===e?String:Number)(t);if("object"!=typeof(a=a.call(t,e||"default")))return a;throw new TypeError("@@toPrimitive must return a primitive value.")})(t,"string"))?t:t+"")(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a}function pe(e,t){var a,r=Object.keys(e);return Object.getOwnPropertySymbols&&(a=Object.getOwnPropertySymbols(e),t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)),r}function zt(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?pe(Object(a),!0).forEach(function(t){he(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):pe(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}let t={},e={},R=null,L={mark:It=()=>{},measure:It};try{"undefined"!=typeof window&&(t=window),"undefined"!=typeof document&&(e=document),"undefined"!=typeof MutationObserver&&(R=MutationObserver),"undefined"!=typeof performance&&(L=performance)}catch(t){}var{userAgent:It=""}=t.navigator||{};let n=t,g=e,T=R;var Ft=L;let Y=!!n.document,f=!!g.documentElement&&!!g.head&&"function"==typeof g.addEventListener&&"function"==typeof g.createElement,W=~It.indexOf("MSIE")||~It.indexOf("Trident/");var It={classic:{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fab:"brands","fa-brands":"brands"},duotone:{fa:"solid",fad:"solid","fa-solid":"solid","fa-duotone":"solid",fadr:"regular","fa-regular":"regular",fadl:"light","fa-light":"light",fadt:"thin","fa-thin":"thin"},sharp:{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"},"sharp-duotone":{fa:"solid",fasds:"solid","fa-solid":"solid",fasdr:"regular","fa-regular":"regular",fasdl:"light","fa-light":"light",fasdt:"thin","fa-thin":"thin"}},ge=["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone"],Dt="classic",Rt="duotone",be=[Dt,Rt,"sharp","sharp-duotone"],ve=new Map([["classic",{defaultShortPrefixId:"fas",defaultStyleId:"solid",styleIds:["solid","regular","light","thin","brands"],futureStyleIds:[],defaultFontWeight:900}],["sharp",{defaultShortPrefixId:"fass",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["duotone",{defaultShortPrefixId:"fad",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["sharp-duotone",{defaultShortPrefixId:"fasds",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}]]),ye=["fak","fa-kit","fakd","fa-kit-duotone"],Lt={fak:"kit","fa-kit":"kit"},Tt={fakd:"kit-duotone","fa-kit-duotone":"kit-duotone"},xe=["fak","fakd"],ke={kit:"fak"},Yt={"kit-duotone":"fakd"},Wt={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},we=["fak","fa-kit","fakd","fa-kit-duotone"],Ht={classic:{fab:"fa-brands",fad:"fa-duotone",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"},duotone:{fadr:"fa-regular",fadl:"fa-light",fadt:"fa-thin"},sharp:{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"},"sharp-duotone":{fasds:"fa-solid",fasdr:"fa-regular",fasdl:"fa-light",fasdt:"fa-thin"}},_t=["fa","fas","far","fal","fat","fad","fadr","fadl","fadt","fab","fass","fasr","fasl","fast","fasds","fasdr","fasdl","fasdt","fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone","fa-solid","fa-regular","fa-light","fa-thin","fa-duotone","fa-brands"],Ut=(Bt=[1,2,3,4,5,6,7,8,9,10]).concat([11,12,13,14,15,16,17,18,19,20]),Wt=[...Object.keys({classic:["fas","far","fal","fat","fad"],duotone:["fadr","fadl","fadt"],sharp:["fass","fasr","fasl","fast"],"sharp-duotone":["fasds","fasdr","fasdl","fasdt"]}),"solid","regular","light","thin","duotone","brands","2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","bounce","flip-both","flip-horizontal","flip-vertical","flip","fw","inverse","layers-counter","layers-text","layers","li","pull-left","pull-right","pulse","rotate-180","rotate-270","rotate-90","rotate-by","shake","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul",Wt.GROUP,Wt.SWAP_OPACITY,Wt.PRIMARY,Wt.SECONDARY].concat(Bt.map(t=>"".concat(t,"x"))).concat(Ut.map(t=>"w-".concat(t))),Bt="___FONT_AWESOME___";let l=16,H="svg-inline--fa",b="data-fa-i2svg",v="data-fa-pseudo-element",_="data-fa-pseudo-element-pending",y="data-prefix",x="data-icon",U="fontawesome-i2svg",B="async",X=["HTML","HEAD","STYLE","SCRIPT"],c=(()=>{try{return!0}catch(t){return!1}})();function Xt(t){return new Proxy(t,{get(t,e){return e in t?t[e]:t[Dt]}})}(Ut=zt({},It))[Dt]=zt(zt(zt(zt({},{"fa-duotone":"duotone"}),It[Dt]),Lt),Tt);let q=Xt(Ut),V=((It=zt({},{classic:{solid:"fas",regular:"far",light:"fal",thin:"fat",brands:"fab"},duotone:{solid:"fad",regular:"fadr",light:"fadl",thin:"fadt"},sharp:{solid:"fass",regular:"fasr",light:"fasl",thin:"fast"},"sharp-duotone":{solid:"fasds",regular:"fasdr",light:"fasdl",thin:"fasdt"}}))[Dt]=zt(zt(zt(zt({},{duotone:"fad"}),It[Dt]),ke),Yt),Xt(It)),i=((Lt=zt({},Ht))[Dt]=zt(zt({},Lt[Dt]),{fak:"fa-kit"}),Xt(Lt)),G=((Tt=zt({},{classic:{"fa-brands":"fab","fa-duotone":"fad","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"},duotone:{"fa-regular":"fadr","fa-light":"fadl","fa-thin":"fadt"},sharp:{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"},"sharp-duotone":{"fa-solid":"fasds","fa-regular":"fasdr","fa-light":"fasdl","fa-thin":"fasdt"}}))[Dt]=zt(zt({},Tt[Dt]),{"fa-kit":"fak"}),Xt(Tt),/fa(s|r|l|t|d|dr|dl|dt|b|k|kd|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\-\ ]/),K="fa-layers-text",J=/Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit)?.*/i,Q=(Xt(zt({},{classic:{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},duotone:{900:"fad",400:"fadr",300:"fadl",100:"fadt"},sharp:{900:"fass",400:"fasr",300:"fasl",100:"fast"},"sharp-duotone":{900:"fasds",400:"fasdr",300:"fasdl",100:"fasdt"}})),["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"]),o={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},Z=["kit",...Wt],a=n.FontAwesomeConfig||{},r=(g&&"function"==typeof g.querySelector&&[["data-family-prefix","familyPrefix"],["data-css-prefix","cssPrefix"],["data-family-default","familyDefault"],["data-style-default","styleDefault"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach(t=>{var[t,e]=t,t=""===(t=(t=>{var e=g.querySelector("script["+t+"]");if(e)return e.getAttribute(t)})(t))||"false"!==t&&("true"===t||t);null!=t&&(a[e]=t)}),Ut={styleDefault:"solid",familyDefault:Dt,cssPrefix:"fa",replacementClass:H,autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0},a.familyPrefix&&(a.cssPrefix=a.familyPrefix),zt(zt({},Ut),a)),k=(r.autoReplaceSvg||(r.observeMutations=!1),{}),s=(Object.keys(Ut).forEach(e=>{Object.defineProperty(k,e,{enumerable:!0,set:function(t){r[e]=t,s.forEach(t=>t(k))},get:function(){return r[e]}})}),Object.defineProperty(k,"familyPrefix",{enumerable:!0,set:function(t){r.cssPrefix=t,s.forEach(t=>t(k))},get:function(){return r.cssPrefix}}),n.FontAwesomeConfig=k,[]),u=l,w={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1},$="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";function qt(){let t=12,e="";for(;0<t--;)e+=$[62*Math.random()|0];return e}function Vt(e){var a=[];for(let t=(e||[]).length>>>0;t--;)a[t]=e[t];return a}function Gt(t){return t.classList?Vt(t.classList):(t.getAttribute("class")||"").split(" ").filter(t=>t)}function Ae(t){return"".concat(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function Kt(a){return Object.keys(a||{}).reduce((t,e)=>t+"".concat(e,": ").concat(a[e].trim(),";"),"")}function Jt(t){return t.size!==w.size||t.x!==w.x||t.y!==w.y||t.rotate!==w.rotate||t.flipX||t.flipY}function Pe(){var t,e,a=H,r=k.cssPrefix,n=k.replacementClass;let i=':host,:root{--fa-font-solid:normal 900 1em/1 "Font Awesome 6 Pro";--fa-font-regular:normal 400 1em/1 "Font Awesome 6 Pro";--fa-font-light:normal 300 1em/1 "Font Awesome 6 Pro";--fa-font-thin:normal 100 1em/1 "Font Awesome 6 Pro";--fa-font-duotone:normal 900 1em/1 "Font Awesome 6 Duotone";--fa-font-duotone-regular:normal 400 1em/1 "Font Awesome 6 Duotone";--fa-font-duotone-light:normal 300 1em/1 "Font Awesome 6 Duotone";--fa-font-duotone-thin:normal 100 1em/1 "Font Awesome 6 Duotone";--fa-font-brands:normal 400 1em/1 "Font Awesome 6 Brands";--fa-font-sharp-solid:normal 900 1em/1 "Font Awesome 6 Sharp";--fa-font-sharp-regular:normal 400 1em/1 "Font Awesome 6 Sharp";--fa-font-sharp-light:normal 300 1em/1 "Font Awesome 6 Sharp";--fa-font-sharp-thin:normal 100 1em/1 "Font Awesome 6 Sharp";--fa-font-sharp-duotone-solid:normal 900 1em/1 "Font Awesome 6 Sharp Duotone";--fa-font-sharp-duotone-regular:normal 400 1em/1 "Font Awesome 6 Sharp Duotone";--fa-font-sharp-duotone-light:normal 300 1em/1 "Font Awesome 6 Sharp Duotone";--fa-font-sharp-duotone-thin:normal 100 1em/1 "Font Awesome 6 Sharp Duotone"}svg:not(:host).svg-inline--fa,svg:not(:root).svg-inline--fa{overflow:visible;box-sizing:content-box}.svg-inline--fa{display:var(--fa-display,inline-block);height:1em;overflow:visible;vertical-align:-.125em}.svg-inline--fa.fa-2xs{vertical-align:.1em}.svg-inline--fa.fa-xs{vertical-align:0}.svg-inline--fa.fa-sm{vertical-align:-.0714285705em}.svg-inline--fa.fa-lg{vertical-align:-.2em}.svg-inline--fa.fa-xl{vertical-align:-.25em}.svg-inline--fa.fa-2xl{vertical-align:-.3125em}.svg-inline--fa.fa-pull-left{margin-right:var(--fa-pull-margin,.3em);width:auto}.svg-inline--fa.fa-pull-right{margin-left:var(--fa-pull-margin,.3em);width:auto}.svg-inline--fa.fa-li{width:var(--fa-li-width,2em);top:.25em}.svg-inline--fa.fa-fw{width:var(--fa-fw-width,1.25em)}.fa-layers svg.svg-inline--fa{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0}.fa-layers-counter,.fa-layers-text{display:inline-block;position:absolute;text-align:center}.fa-layers{display:inline-block;height:1em;position:relative;text-align:center;vertical-align:-.125em;width:1em}.fa-layers svg.svg-inline--fa{transform-origin:center center}.fa-layers-text{left:50%;top:50%;transform:translate(-50%,-50%);transform-origin:center center}.fa-layers-counter{background-color:var(--fa-counter-background-color,#ff253a);border-radius:var(--fa-counter-border-radius,1em);box-sizing:border-box;color:var(--fa-inverse,#fff);line-height:var(--fa-counter-line-height,1);max-width:var(--fa-counter-max-width,5em);min-width:var(--fa-counter-min-width,1.5em);overflow:hidden;padding:var(--fa-counter-padding,.25em .5em);right:var(--fa-right,0);text-overflow:ellipsis;top:var(--fa-top,0);transform:scale(var(--fa-counter-scale,.25));transform-origin:top right}.fa-layers-bottom-right{bottom:var(--fa-bottom,0);right:var(--fa-right,0);top:auto;transform:scale(var(--fa-layers-scale,.25));transform-origin:bottom right}.fa-layers-bottom-left{bottom:var(--fa-bottom,0);left:var(--fa-left,0);right:auto;top:auto;transform:scale(var(--fa-layers-scale,.25));transform-origin:bottom left}.fa-layers-top-right{top:var(--fa-top,0);right:var(--fa-right,0);transform:scale(var(--fa-layers-scale,.25));transform-origin:top right}.fa-layers-top-left{left:var(--fa-left,0);right:auto;top:var(--fa-top,0);transform:scale(var(--fa-layers-scale,.25));transform-origin:top left}.fa-1x{font-size:1em}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-6x{font-size:6em}.fa-7x{font-size:7em}.fa-8x{font-size:8em}.fa-9x{font-size:9em}.fa-10x{font-size:10em}.fa-2xs{font-size:.625em;line-height:.1em;vertical-align:.225em}.fa-xs{font-size:.75em;line-height:.0833333337em;vertical-align:.125em}.fa-sm{font-size:.875em;line-height:.0714285718em;vertical-align:.0535714295em}.fa-lg{font-size:1.25em;line-height:.05em;vertical-align:-.075em}.fa-xl{font-size:1.5em;line-height:.0416666682em;vertical-align:-.125em}.fa-2xl{font-size:2em;line-height:.03125em;vertical-align:-.1875em}.fa-fw{text-align:center;width:1.25em}.fa-ul{list-style-type:none;margin-left:var(--fa-li-margin,2.5em);padding-left:0}.fa-ul>li{position:relative}.fa-li{left:calc(-1 * var(--fa-li-width,2em));position:absolute;text-align:center;width:var(--fa-li-width,2em);line-height:inherit}.fa-border{border-color:var(--fa-border-color,#eee);border-radius:var(--fa-border-radius,.1em);border-style:var(--fa-border-style,solid);border-width:var(--fa-border-width,.08em);padding:var(--fa-border-padding,.2em .25em .15em)}.fa-pull-left{float:left;margin-right:var(--fa-pull-margin,.3em)}.fa-pull-right{float:right;margin-left:var(--fa-pull-margin,.3em)}.fa-beat{animation-name:fa-beat;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,ease-in-out)}.fa-bounce{animation-name:fa-bounce;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,cubic-bezier(.28,.84,.42,1))}.fa-fade{animation-name:fa-fade;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,cubic-bezier(.4,0,.6,1))}.fa-beat-fade{animation-name:fa-beat-fade;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,cubic-bezier(.4,0,.6,1))}.fa-flip{animation-name:fa-flip;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,ease-in-out)}.fa-shake{animation-name:fa-shake;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,linear)}.fa-spin{animation-name:fa-spin;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,2s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,linear)}.fa-spin-reverse{--fa-animation-direction:reverse}.fa-pulse,.fa-spin-pulse{animation-name:fa-spin;animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,steps(8))}@media (prefers-reduced-motion:reduce){.fa-beat,.fa-beat-fade,.fa-bounce,.fa-fade,.fa-flip,.fa-pulse,.fa-shake,.fa-spin,.fa-spin-pulse{animation-delay:-1ms;animation-duration:1ms;animation-iteration-count:1;transition-delay:0s;transition-duration:0s}}@keyframes fa-beat{0%,90%{transform:scale(1)}45%{transform:scale(var(--fa-beat-scale,1.25))}}@keyframes fa-bounce{0%{transform:scale(1,1) translateY(0)}10%{transform:scale(var(--fa-bounce-start-scale-x,1.1),var(--fa-bounce-start-scale-y,.9)) translateY(0)}30%{transform:scale(var(--fa-bounce-jump-scale-x,.9),var(--fa-bounce-jump-scale-y,1.1)) translateY(var(--fa-bounce-height,-.5em))}50%{transform:scale(var(--fa-bounce-land-scale-x,1.05),var(--fa-bounce-land-scale-y,.95)) translateY(0)}57%{transform:scale(1,1) translateY(var(--fa-bounce-rebound,-.125em))}64%{transform:scale(1,1) translateY(0)}100%{transform:scale(1,1) translateY(0)}}@keyframes fa-fade{50%{opacity:var(--fa-fade-opacity,.4)}}@keyframes fa-beat-fade{0%,100%{opacity:var(--fa-beat-fade-opacity,.4);transform:scale(1)}50%{opacity:1;transform:scale(var(--fa-beat-fade-scale,1.125))}}@keyframes fa-flip{50%{transform:rotate3d(var(--fa-flip-x,0),var(--fa-flip-y,1),var(--fa-flip-z,0),var(--fa-flip-angle,-180deg))}}@keyframes fa-shake{0%{transform:rotate(-15deg)}4%{transform:rotate(15deg)}24%,8%{transform:rotate(-18deg)}12%,28%{transform:rotate(18deg)}16%{transform:rotate(-22deg)}20%{transform:rotate(22deg)}32%{transform:rotate(-12deg)}36%{transform:rotate(12deg)}100%,40%{transform:rotate(0)}}@keyframes fa-spin{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}.fa-rotate-90{transform:rotate(90deg)}.fa-rotate-180{transform:rotate(180deg)}.fa-rotate-270{transform:rotate(270deg)}.fa-flip-horizontal{transform:scale(-1,1)}.fa-flip-vertical{transform:scale(1,-1)}.fa-flip-both,.fa-flip-horizontal.fa-flip-vertical{transform:scale(-1,-1)}.fa-rotate-by{transform:rotate(var(--fa-rotate-angle,0))}.fa-stack{display:inline-block;vertical-align:middle;height:2em;position:relative;width:2.5em}.fa-stack-1x,.fa-stack-2x{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;z-index:var(--fa-stack-z-index,auto)}.svg-inline--fa.fa-stack-1x{height:1em;width:1.25em}.svg-inline--fa.fa-stack-2x{height:2em;width:2.5em}.fa-inverse{color:var(--fa-inverse,#fff)}.fa-sr-only,.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.fa-sr-only-focusable:not(:focus),.sr-only-focusable:not(:focus){position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.svg-inline--fa .fa-primary{fill:var(--fa-primary-color,currentColor);opacity:var(--fa-primary-opacity,1)}.svg-inline--fa .fa-secondary{fill:var(--fa-secondary-color,currentColor);opacity:var(--fa-secondary-opacity,.4)}.svg-inline--fa.fa-swap-opacity .fa-primary{opacity:var(--fa-secondary-opacity,.4)}.svg-inline--fa.fa-swap-opacity .fa-secondary{opacity:var(--fa-primary-opacity,1)}.svg-inline--fa mask .fa-primary,.svg-inline--fa mask .fa-secondary{fill:#000}';return"fa"===r&&n===a||(t=new RegExp("\\.".concat("fa","\\-"),"g"),e=new RegExp("\\--".concat("fa","\\-"),"g"),a=new RegExp("\\.".concat(a),"g"),i=i.replace(t,".".concat(r,"-")).replace(e,"--".concat(r,"-")).replace(a,".".concat(n))),i}let tt=!1;function Qt(){if(k.autoAddCss&&!tt){var t=Pe();if(t&&f){var a=g.createElement("style"),r=(a.setAttribute("type","text/css"),a.innerHTML=t,g.head.childNodes);let e=null;for(let t=r.length-1;-1<t;t--){var n=r[t],i=(n.tagName||"").toUpperCase();-1<["STYLE","LINK"].indexOf(i)&&(e=n)}g.head.insertBefore(a,e)}tt=!0}}var ke={mixout(){return{dom:{css:Pe,insertCss:Qt}}},hooks(){return{beforeDOMElementCreation(){Qt()},beforeI2svg(){Qt()}}}},Zt=((Yt=n||{})[Bt]||(Yt[Bt]={}),Yt[Bt].styles||(Yt[Bt].styles={}),Yt[Bt].hooks||(Yt[Bt].hooks={}),Yt[Bt].shims||(Yt[Bt].shims=[]),Yt[Bt]);function Oe(){g.removeEventListener("DOMContentLoaded",Oe),d=1,et.map(t=>t())}let et=[],d=!1;function Ne(t){f&&(d?setTimeout(t,0):et.push(t))}function $t(t){var a,{tag:e,attributes:r={},children:n=[]}=t;return"string"==typeof t?Ae(t):"<".concat(e," ").concat((a=r,Object.keys(a||{}).reduce((t,e)=>t+"".concat(e,'="').concat(Ae(a[e]),'" '),"").trim()),">").concat(n.map($t).join(""),"</").concat(e,">")}function Se(t,e,a){if(t&&t[e]&&t[e][a])return{prefix:e,iconName:a,icon:t[e][a]}}function te(t,e,a,r){for(var n,i,o=Object.keys(t),s=o.length,l=void 0!==r?Ce(e,r):e,f=void 0===a?(n=1,t[o[0]]):(n=0,a);n<s;n++)f=l(f,t[i=o[n]],i,t);return f}f&&!(d=(g.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(g.readyState))&&g.addEventListener("DOMContentLoaded",Oe);var Ce=function(n,i){return function(t,e,a,r){return n.call(i,t,e,a,r)}};function Ee(t){t=(t=>{var e=[];let a=0;for(var r=t.length;a<r;){var n,i=t.charCodeAt(a++);55296<=i&&i<=56319&&a<r?56320==(64512&(n=t.charCodeAt(a++)))?e.push(((1023&i)<<10)+(1023&n)+65536):(e.push(i),a--):e.push(i)}return e})(t);return 1===t.length?t[0].toString(16):null}function Me(r){return Object.keys(r).reduce((t,e)=>{var a=r[e];return!!a.icon?t[a.iconName]=a.icon:t[e]=a,t},{})}function ee(t,e,a){var{skipHooks:a=!1}=2<arguments.length&&void 0!==a?a:{},r=Me(e);"function"!=typeof Zt.hooks.addPack||a?Zt.styles[t]=zt(zt({},Zt.styles[t]||{}),r):Zt.hooks.addPack(t,Me(e)),"fas"===t&&ee("fa",e)}let{styles:m,shims:at}=Zt,rt=Object.keys(i),nt=rt.reduce((t,e)=>(t[e]=Object.keys(i[e]),t),{}),h=null,it={},ot={},st={},lt={},ft={};function je(t,e){var e=e.split("-"),a=e[0],e=e.slice(1).join("-");return a!==t||""===e||(a=e,~Z.indexOf(a))?null:e}let p=()=>{var t=r=>te(m,(t,e,a)=>(t[a]=te(e,r,{}),t),{});it=t((e,t,a)=>(t[3]&&(e[t[3]]=a),t[2]&&t[2].filter(t=>"number"==typeof t).forEach(t=>{e[t.toString(16)]=a}),e)),ot=t((e,t,a)=>(e[a]=a,t[2]&&t[2].filter(t=>"string"==typeof t).forEach(t=>{e[t]=a}),e)),ft=t((e,t,a)=>{t=t[2];return e[a]=a,t.forEach(t=>{e[t]=a}),e});let n="far"in m||k.autoFetchSvg;t=te(at,(t,e)=>{var a=e[0];let r=e[1];e=e[2];return"far"!==r||n||(r="fas"),"string"==typeof a&&(t.names[a]={prefix:r,iconName:e}),"number"==typeof a&&(t.unicodes[a.toString(16)]={prefix:r,iconName:e}),t},{names:{},unicodes:{}});st=t.names,lt=t.unicodes,h=ne(k.styleDefault,{family:k.familyDefault})};function ae(t,e){return(it[t]||{})[e]}function re(t,e){return(ft[t]||{})[e]}function ze(t){return st[t]||{prefix:null,iconName:null}}It=t=>{h=ne(t.styleDefault,{family:k.familyDefault})},s.push(It),p();let ct=()=>({prefix:null,iconName:null,rest:[]});function ne(t,e){var{family:e=Dt}=1<arguments.length&&void 0!==e?e:{},a=q[e][t];return e!==Rt||t?(e=V[e][t]||V[e][a],a=t in Zt.styles?t:null,e||a||null):"fad"}function Ie(t){return t.sort().filter((t,e,a)=>a.indexOf(t)===e)}function ie(t,e){var{skipLookups:e=!1}=1<arguments.length&&void 0!==e?e:{};let a=null,r=_t.concat(we);var n=Ie(t.filter(t=>r.includes(t))),i=Ie(t.filter(t=>!_t.includes(t))),[o=null]=n.filter(t=>(a=t,!ge.includes(t))),n=(t=>{let a=Dt,r=rt.reduce((t,e)=>(t[e]="".concat(k.cssPrefix,"-").concat(e),t),{});return be.forEach(e=>{(t.includes(r[e])||t.some(t=>nt[e].includes(t)))&&(a=e)}),a})(n),i=zt(zt({},(t=>{let a=[],r=null;return t.forEach(t=>{var e=je(k.cssPrefix,t);e?r=e:t&&a.push(t)}),{iconName:r,rest:a}})(i)),{},{prefix:ne(o,{family:n})});return zt(zt(zt({},i),(t=>{var{values:t,family:e,canonical:a,givenPrefix:r="",styles:n={},config:i={}}=t,o=e===Rt,s=t.includes("fa-duotone")||t.includes("fad"),l="duotone"===i.familyDefault,f="fad"===a.prefix||"fa-duotone"===a.prefix;return!o&&(s||l||f)&&(a.prefix="fad"),(t.includes("fa-brands")||t.includes("fab"))&&(a.prefix="fab"),!a.prefix&&ut.includes(e)&&(Object.keys(n).find(t=>dt.includes(t))||i.autoFetchSvg)&&(o=ve.get(e).defaultShortPrefixId,a.prefix=o,a.iconName=re(a.prefix,a.iconName)||a.iconName),"fa"!==a.prefix&&"fa"!==r||(a.prefix=h||"fas"),a})({values:t,family:n,styles:m,config:k,canonical:i,givenPrefix:a})),((t,e,a)=>{let{prefix:r,iconName:n}=a;return!t&&r&&n&&(a="fa"===e?ze(n):{},t=re(r,n),n=a.iconName||t||n,"far"!==(r=a.prefix||r)||m.far||!m.fas||k.autoFetchSvg||(r="fas")),{prefix:r,iconName:n}})(e,a,i))}let ut=be.filter(t=>t!==Dt||t!==Rt),dt=Object.keys(Ht).filter(t=>t!==Dt).map(t=>Object.keys(Ht[t])).flat(),mt=[],A={},P={},ht=Object.keys(P);function oe(t,e){for(var a=arguments.length,r=new Array(2<a?a-2:0),n=2;n<a;n++)r[n-2]=arguments[n];return(A[t]||[]).forEach(t=>{e=t.apply(null,[e,...r])}),e}function se(t){for(var e=arguments.length,a=new Array(1<e?e-1:0),r=1;r<e;r++)a[r-1]=arguments[r];(A[t]||[]).forEach(t=>{t.apply(null,a)})}function le(t){var e=Array.prototype.slice.call(arguments,1);return P[t]?P[t].apply(null,e):void 0}function fe(t){"fa"===t.prefix&&(t.prefix="fas");var e=t.iconName,t=t.prefix||h;if(e)return e=re(t,e)||e,Se(pt.definitions,t,e)||Se(Zt.styles,t,e)}let pt=new class{constructor(){this.definitions={}}add(){for(var t=arguments.length,e=new Array(t),a=0;a<t;a++)e[a]=arguments[a];let r=e.reduce(this._pullDefinitions,{});Object.keys(r).forEach(t=>{this.definitions[t]=zt(zt({},this.definitions[t]||{}),r[t]),ee(t,r[t]);var e=i[Dt][t];e&&ee(e,r[t]),p()})}reset(){this.definitions={}}_pullDefinitions(n,t){let i=t.prefix&&t.iconName&&t.icon?{0:t}:t;return Object.keys(i).map(t=>{let{prefix:e,iconName:a,icon:r}=i[t];t=r[2];n[e]||(n[e]={}),0<t.length&&t.forEach(t=>{"string"==typeof t&&(n[e][t]=r)}),n[e][a]=r}),n}},O={noAuto:()=>{k.autoReplaceSvg=!1,k.observeMutations=!1,se("noAuto")},config:k,dom:{i2svg:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return f?(se("beforeI2svg",t),le("pseudoElements2svg",t),le("i2svg",t)):Promise.reject(new Error("Operation requires a DOM of some kind."))},watch:function(){let t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},e=t.autoReplaceSvgRoot;!1===k.autoReplaceSvg&&(k.autoReplaceSvg=!0),k.observeMutations=!0,Ne(()=>{N({autoReplaceSvgRoot:e}),se("watch",t)})}},parse:{icon:t=>{var e,a;return null===t?null:"object"==typeof t&&t.prefix&&t.iconName?{prefix:t.prefix,iconName:re(t.prefix,t.iconName)||t.iconName}:Array.isArray(t)&&2===t.length?(e=0===t[1].indexOf("fa-")?t[1].slice(3):t[1],{prefix:a=ne(t[0]),iconName:re(a,e)||e}):"string"==typeof t&&(-1<t.indexOf("".concat(k.cssPrefix,"-"))||t.match(G))?{prefix:(a=ie(t.split(" "),{skipLookups:!0})).prefix||h,iconName:re(a.prefix,a.iconName)||a.iconName}:"string"==typeof t?{prefix:h,iconName:re(h,t)||t}:void 0}},library:pt,findIconDefinition:fe,toHtml:$t},N=function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},{autoReplaceSvgRoot:t=g}=t;(0<Object.keys(Zt.styles).length||k.autoFetchSvg)&&f&&k.autoReplaceSvg&&O.dom.i2svg({node:t})};function ce(e,t){return Object.defineProperty(e,"abstract",{get:t}),Object.defineProperty(e,"html",{get:function(){return e.abstract.map(t=>$t(t))}}),Object.defineProperty(e,"node",{get:function(){var t;if(f)return(t=g.createElement("div")).innerHTML=e.html,t.children}}),e}function ue(t){let{icons:{main:e,mask:a},prefix:r,iconName:n,transform:i,symbol:o,title:s,maskId:l,titleId:f,extra:c,watchable:u=!1}=t;var d,m,{width:t,height:h}=a.found?a:e,p=xe.includes(r),g=[k.replacementClass,n?"".concat(k.cssPrefix,"-").concat(n):""].filter(t=>-1===c.classes.indexOf(t)).filter(t=>""!==t||!!t).concat(c.classes).join(" "),g={children:[],attributes:zt(zt({},c.attributes),{},{"data-prefix":r,"data-icon":n,class:g,role:c.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(t," ").concat(h)})},p=p&&!~c.classes.indexOf("fa-fw")?{width:"".concat(t/h*16*.0625,"em")}:{},t=(u&&(g.attributes[b]=""),s&&(g.children.push({tag:"title",attributes:{id:g.attributes["aria-labelledby"]||"title-".concat(f||qt())},children:[s]}),delete g.attributes.title),zt(zt({},g),{},{prefix:r,iconName:n,main:e,mask:a,maskId:l,transform:i,symbol:o,styles:zt(zt({},p),c.styles)})),{children:h,attributes:g}=a.found&&e.found?le("generateAbstractMask",t)||{children:[],attributes:{}}:le("generateAbstractIcon",t)||{children:[],attributes:{}};return t.children=h,t.attributes=g,o?({prefix:p,iconName:h,children:g,attributes:m,symbol:d}=p=t,p=!0===d?"".concat(p,"-").concat(k.cssPrefix,"-").concat(h):d,[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:zt(zt({},m),{},{id:p}),children:g}]}]):({children:h,main:d,mask:m,attributes:p,styles:g,transform:t}=h=t,Jt(t)&&d.found&&!m.found&&({width:m,height:d}=d,m={x:m/d/2,y:.5},p.style=Kt(zt(zt({},g),{},{"transform-origin":"".concat(m.x+t.x/16,"em ").concat(m.y+t.y/16,"em")}))),[{tag:"svg",attributes:p,children:h}])}function Fe(t){var{content:t,width:e,height:a,transform:r,title:n,extra:i,watchable:o=!1}=t,s=zt(zt(zt({},i.attributes),n?{title:n}:{}),{},{class:i.classes.join(" ")}),o=(o&&(s[b]=""),zt({},i.styles)),i=(Jt(r)&&(o.transform=(t=>{var{transform:t,width:e=l,height:a=l,startCentered:r=!1}=t;let n="";return r&&W?n+="translate(".concat(t.x/u-e/2,"em, ").concat(t.y/u-a/2,"em) "):n+=r?"translate(calc(-50% + ".concat(t.x/u,"em), calc(-50% + ").concat(t.y/u,"em)) "):"translate(".concat(t.x/u,"em, ").concat(t.y/u,"em) "),n=(n+="scale(".concat(t.size/u*(t.flipX?-1:1),", ").concat(t.size/u*(t.flipY?-1:1),") "))+"rotate(".concat(t.rotate,"deg) ")})({transform:r,startCentered:!0,width:e,height:a}),o["-webkit-transform"]=o.transform),Kt(o)),r=(0<i.length&&(s.style=i),[]);return r.push({tag:"span",attributes:s,children:[t]}),n&&r.push({tag:"span",attributes:{class:"sr-only"},children:[n]}),r}let S=Zt.styles;function de(t){var e=t[0],a=t[1],[t]=t.slice(4);let r=null;return{found:!0,width:e,height:a,icon:r=Array.isArray(t)?{tag:"g",attributes:{class:"".concat(k.cssPrefix,"-").concat(o.GROUP)},children:[{tag:"path",attributes:{class:"".concat(k.cssPrefix,"-").concat(o.SECONDARY),fill:"currentColor",d:t[0]}},{tag:"path",attributes:{class:"".concat(k.cssPrefix,"-").concat(o.PRIMARY),fill:"currentColor",d:t[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:t}}}}let gt={found:!1,width:512,height:512};function me(n,i){let o=i;return"fa"===i&&null!==k.styleDefault&&(i=h),new Promise((t,e)=>{var a,r;if("fa"===o&&(a=ze(n)||{},n=a.iconName||n,i=a.prefix||i),n&&i&&S[i]&&S[i][n])return t(de(S[i][n]));a=n,r=i,c||k.showMissingIcons||!a||console.error('Icon with name "'.concat(a,'" and prefix "').concat(r,'" is missing.')),t(zt(zt({},gt),{},{icon:k.showMissingIcons&&n&&le("missingIconAbstract")||{}}))})}Lt=()=>{};let C=k.measurePerformance&&Ft&&Ft.mark&&Ft.measure?Ft:{mark:Lt,measure:Lt},E='FA "6.7.1"',bt=t=>{C.mark("".concat(E," ").concat(t," ends")),C.measure("".concat(E," ").concat(t),"".concat(E," ").concat(t," begins"),"".concat(E," ").concat(t," ends"))};var De={begin:t=>(C.mark("".concat(E," ").concat(t," begins")),()=>bt(t)),end:bt};let M=()=>{};function Re(t){return"string"==typeof(t.getAttribute?t.getAttribute(b):null)}function Le(e,t){let{ceFn:a="svg"===e.tag?function(t){return g.createElementNS("http://www.w3.org/2000/svg",t)}:function(t){return g.createElement(t)}}=1<arguments.length&&void 0!==t?t:{};if("string"==typeof e)return g.createTextNode(e);let r=a(e.tag);return Object.keys(e.attributes||[]).forEach(function(t){r.setAttribute(t,e.attributes[t])}),(e.children||[]).forEach(function(t){r.appendChild(Le(t,{ceFn:a}))}),r}let j={replace:function(t){let e=t[0];e.parentNode&&(t[1].forEach(t=>{e.parentNode.insertBefore(Le(t),e)}),null===e.getAttribute(b)&&k.keepOriginalSource?(t=g.createComment((t=e," ".concat(t.outerHTML," "))),e.parentNode.replaceChild(t,e)):e.remove())},nest:function(t){var e=t[0],a=t[1];if(~Gt(e).indexOf(k.replacementClass))return j.replace(t);let r=new RegExp("".concat(k.cssPrefix,"-.*"));delete a[0].attributes.id,a[0].attributes.class&&(t=a[0].attributes.class.split(" ").reduce((t,e)=>((e===k.replacementClass||e.match(r)?t.toSvg:t.toNode).push(e),t),{toNode:[],toSvg:[]}),a[0].attributes.class=t.toSvg.join(" "),0===t.toNode.length?e.removeAttribute("class"):e.setAttribute("class",t.toNode.join(" ")));t=a.map(t=>$t(t)).join("\n");e.setAttribute(b,""),e.innerHTML=t}};function Te(t){t()}function Ye(a,t){let r="function"==typeof t?t:M;if(0===a.length)r();else{let t=Te;(t=k.mutateApproach===B?n.requestAnimationFrame||Te:t)(()=>{var t=!0!==k.autoReplaceSvg&&j[k.autoReplaceSvg]||j.replace,e=De.begin("mutate");a.map(t),e(),r()})}}let z=!1;function We(){z=!0}function He(){z=!1}let I=null;function _e(t){if(!T)return;if(!k.observeMutations)return;let{treeCallback:n=M,nodeCallback:i=M,pseudoElementsCallback:o=M,observeMutationsRoot:e=g}=t;I=new T(t=>{if(!z){let r=h;Vt(t).forEach(t=>{var e,a;"childList"===t.type&&0<t.addedNodes.length&&!Re(t.addedNodes[0])&&(k.searchPseudoElements&&o(t.target),n(t.target)),"attributes"===t.type&&t.target.parentNode&&k.searchPseudoElements&&o(t.target.parentNode),"attributes"===t.type&&Re(t.target)&&~Q.indexOf(t.attributeName)&&("class"===t.attributeName&&(e=t.target,a=e.getAttribute?e.getAttribute(y):null,e=e.getAttribute?e.getAttribute(x):null,a)&&e?({prefix:a,iconName:e}=ie(Gt(t.target)),t.target.setAttribute(y,a||r),e&&t.target.setAttribute(x,e)):(a=t.target)&&a.classList&&a.classList.contains&&a.classList.contains(k.replacementClass)&&i(t.target))})}}),f&&I.observe(e,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}function Ue(t){var e=t.getAttribute("data-prefix"),a=t.getAttribute("data-icon"),r=void 0!==t.innerText?t.innerText.trim():"",n=ie(Gt(t));return n.prefix||(n.prefix=h),e&&a&&(n.prefix=e,n.iconName=a),n.iconName&&n.prefix||(n.prefix&&0<r.length&&(n.iconName=(e=n.prefix,a=t.innerText,(ot[e]||{})[a]||ae(n.prefix,Ee(t.innerText)))),!n.iconName&&k.autoFetchSvg&&t.firstChild&&t.firstChild.nodeType===Node.TEXT_NODE&&(n.iconName=t.firstChild.data)),n}function Be(t,e){var e=1<arguments.length&&void 0!==e?e:{styleParser:!0},{iconName:a,prefix:r,rest:n}=Ue(t),i=(s=Vt((o=t).attributes).reduce((t,e)=>("class"!==t.name&&"style"!==t.name&&(t[e.name]=e.value),t),{}),i=o.getAttribute("title"),o=o.getAttribute("data-fa-title-id"),k.autoA11y&&(i?s["aria-labelledby"]="".concat(k.replacementClass,"-title-").concat(o||qt()):(s["aria-hidden"]="true",s.focusable="false")),s),o=oe("parseNodeAttributes",{},t),s=e.styleParser?(t=>{t=t.getAttribute("style");let e=[];return e=t?t.split(";").reduce((t,e)=>{var e=e.split(":"),a=e[0],e=e.slice(1);return a&&0<e.length&&(t[a]=e.join(":").trim()),t},{}):e})(t):[];return zt({iconName:a,title:t.getAttribute("title"),titleId:t.getAttribute("data-fa-title-id"),prefix:r,transform:w,mask:{iconName:null,prefix:null,rest:[]},maskId:null,symbol:!1,extra:{classes:n,styles:s,attributes:i}},o)}let vt=Zt.styles;function Xe(t){var e="nest"===k.autoReplaceSvg?Be(t,{styleParser:!1}):Be(t);return~e.extra.classes.indexOf(K)?le("generateLayersText",t,e):le("generateSvgReplacementMutation",t,e)}function qe(t){let r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(!f)return Promise.resolve();let e=g.documentElement.classList,n=t=>e.add("".concat(U,"-").concat(t)),i=t=>e.remove("".concat(U,"-").concat(t));var a=k.autoFetchSvg?[...ye,..._t]:ge.concat(Object.keys(vt)),a=(a.includes("fa")||a.push("fa"),[".".concat(K,":not([").concat(b,"])")].concat(a.map(t=>".".concat(t,":not([").concat(b,"])"))).join(", "));if(0===a.length)return Promise.resolve();let o=[];try{o=Vt(t.querySelectorAll(a))}catch(t){}if(!(0<o.length))return Promise.resolve();n("pending"),i("complete");let s=De.begin("onTree"),l=o.reduce((t,e)=>{try{var a=Xe(e);a&&t.push(a)}catch(t){c||"MissingIcon"===t.name&&console.error(t)}return t},[]);return new Promise((e,a)=>{Promise.all(l).then(t=>{Ye(t,()=>{n("active"),n("complete"),i("pending"),"function"==typeof r&&r(),s(),e()})}).catch(t=>{s(),a(t)})})}function Ve(t){let e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;Xe(t).then(t=>{t&&Ye([t],e)})}function Ge(r){let n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},{transform:i=w,symbol:o=!1,mask:s=null,maskId:l=null,title:f=null,titleId:c=null,classes:u=[],attributes:d={},styles:m={}}=n;if(r){let{prefix:t,iconName:e,icon:a}=r;return ce(zt({type:"icon"},r),()=>(se("beforeDOMElementCreation",{iconDefinition:r,params:n}),k.autoA11y&&(f?d["aria-labelledby"]="".concat(k.replacementClass,"-title-").concat(c||qt()):(d["aria-hidden"]="true",d.focusable="false")),ue({icons:{main:de(a),mask:s?de(s.icon):{found:!1,width:null,height:null,icon:{}}},prefix:t,iconName:e,transform:zt(zt({},w),i),symbol:o,title:f,maskId:l,titleId:c,extra:{attributes:d,styles:m,classes:u}})))}}let yt={mixout(){return{icon:(r=Ge,function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},t=(t||{}).icon?t:fe(t||{});let a=e.mask;return a=a&&((a||{}).icon?a:fe(a||{})),r(t,zt(zt({},e),{},{mask:a}))})};var r},hooks(){return{mutationObserverCallbacks(t){return t.treeCallback=qe,t.nodeCallback=Ve,t}}},provides(t){t.i2svg=function(t){var{node:t=g,callback:e=()=>{}}=t;return qe(t,e)},t.generateSvgReplacementMutation=function(r,t){let{iconName:n,title:i,titleId:o,prefix:s,transform:l,symbol:f,mask:e,maskId:c,extra:u}=t;return new Promise((a,t)=>{Promise.all([me(n,s),e.iconName?me(e.iconName,e.prefix):Promise.resolve({found:!1,width:512,height:512,icon:{}})]).then(t=>{var[t,e]=t;a([r,ue({icons:{main:t,mask:e},prefix:s,iconName:n,transform:l,symbol:f,maskId:c,title:i,titleId:o,extra:u,watchable:!0})])}).catch(t)})},t.generateAbstractIcon=function(t){var{children:t,attributes:e,main:a,transform:r,styles:n}=t,n=Kt(n);0<n.length&&(e.style=n);let i;return Jt(r)&&(i=le("generateAbstractTransformGrouping",{main:a,transform:r,containerWidth:a.width,iconWidth:a.width})),t.push(i||a.icon),{children:t,attributes:e}}}},xt={mixout(){return{layer(t){let a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},{classes:r=[]}=a;return ce({type:"layer"},()=>{se("beforeDOMElementCreation",{assembler:t,params:a});let e=[];return t(t=>{Array.isArray(t)?t.map(t=>{e=e.concat(t.abstract)}):e=e.concat(t.abstract)}),[{tag:"span",attributes:{class:["".concat(k.cssPrefix,"-layers"),...r].join(" ")},children:e}]})}}}},kt={mixout(){return{counter(n){let i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},{title:o=null,classes:s=[],attributes:l={},styles:f={}}=i;return ce({type:"counter",content:n},()=>{se("beforeDOMElementCreation",{content:n,params:i});var{content:t,title:e,extra:a}=t={content:n.toString(),title:o,extra:{attributes:l,styles:f,classes:["".concat(k.cssPrefix,"-layers-counter"),...s]}},r=zt(zt(zt({},a.attributes),e?{title:e}:{}),{},{class:a.classes.join(" ")});return 0<(a=Kt(a.styles)).length&&(r.style=a),(a=[]).push({tag:"span",attributes:r,children:[t]}),e&&a.push({tag:"span",attributes:{class:"sr-only"},children:[e]}),a})}}}},wt={mixout(){return{text(t){let e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},{transform:a=w,title:r=null,classes:n=[],attributes:i={},styles:o={}}=e;return ce({type:"text",content:t},()=>(se("beforeDOMElementCreation",{content:t,params:e}),Fe({content:t,transform:zt(zt({},w),a),title:r,extra:{attributes:i,styles:o,classes:["".concat(k.cssPrefix,"-layers-text"),...n]}})))}}},provides(t){t.generateLayersText=function(t,e){var a,r,{title:e,transform:n,extra:i}=e;let o=null,s=null;return W&&(a=parseInt(getComputedStyle(t).fontSize,10),r=t.getBoundingClientRect(),o=r.width/a,s=r.height/a),k.autoA11y&&!e&&(i.attributes["aria-hidden"]="true"),Promise.resolve([t,Fe({content:t.innerHTML,width:o,height:s,transform:n,title:e,extra:i,watchable:!0})])}}},At=new RegExp('"',"ug"),Pt=[1105920,1112319],Ot=zt(zt(zt(zt({},{FontAwesome:{normal:"fas",400:"fas"}}),{"Font Awesome 6 Free":{900:"fas",400:"far"},"Font Awesome 6 Pro":{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},"Font Awesome 6 Brands":{400:"fab",normal:"fab"},"Font Awesome 6 Duotone":{900:"fad",400:"fadr",normal:"fadr",300:"fadl",100:"fadt"},"Font Awesome 6 Sharp":{900:"fass",400:"fasr",normal:"fasr",300:"fasl",100:"fast"},"Font Awesome 6 Sharp Duotone":{900:"fasds",400:"fasdr",normal:"fasdr",300:"fasdl",100:"fasdt"}}),{"Font Awesome 5 Free":{900:"fas",400:"far"},"Font Awesome 5 Pro":{900:"fas",400:"far",normal:"far",300:"fal"},"Font Awesome 5 Brands":{400:"fab",normal:"fab"},"Font Awesome 5 Duotone":{900:"fad"}}),{"Font Awesome Kit":{400:"fak",normal:"fak"},"Font Awesome Kit Duotone":{400:"fakd",normal:"fakd"}}),F=Object.keys(Ot).reduce((t,e)=>(t[e.toLowerCase()]=Ot[e],t),{}),Nt=Object.keys(F).reduce((t,e)=>{var a=F[e];return t[e]=a[900]||[...Object.entries(a)][0][1],t},{});function Ke(m,h){let p="".concat(_).concat(h.replace(":","-"));return new Promise((o,s)=>{if(null!==m.getAttribute(p))return o();var a,l=Vt(m.children).filter(t=>t.getAttribute(v)===h)[0],r=n.getComputedStyle(m,h),f=r.getPropertyValue("font-family"),c=f.match(J),u=r.getPropertyValue("font-weight");let t=r.getPropertyValue("content");if(l&&!c)return m.removeChild(l),o();if(c&&"none"!==t&&""!==t){let t=r.getPropertyValue("content"),n=(r=u,u=(u=f).replace(/^['"]|['"]$/g,"").toLowerCase(),r=parseInt(r),r=isNaN(r)?"normal":r,(F[u]||{})[r]||Nt[u]);f=(f=t).replace(At,""),r=0,a=(u=f).length,u=55296<=(d=u.charCodeAt(r))&&d<=56319&&r+1<a&&56320<=(a=u.charCodeAt(r+1))&&a<=57343?1024*(d-55296)+a-56320+65536:d,r=u>=Pt[0]&&u<=Pt[1];var{value:d,isSecondary:u}={value:Ee((a=2===f.length&&f[0]===f[1])?f[0]:f),isSecondary:r||a},f=c[0].startsWith("FontAwesome");let e=ae(n,d),i=e;if(f&&(r=d,a=lt[r],r=ae("fas",r),(c=a||(r?{prefix:"fas",iconName:r}:null)||{prefix:null,iconName:null}).iconName)&&c.prefix&&(e=c.iconName,n=c.prefix),!e||u||l&&l.getAttribute(y)===n&&l.getAttribute(x)===i)o();else{m.setAttribute(p,i),l&&m.removeChild(l);let a={iconName:null,title:null,titleId:null,prefix:null,transform:w,symbol:!1,mask:{iconName:null,prefix:null,rest:[]},maskId:null,extra:{classes:[],styles:{},attributes:{}}},r=a.extra;r.attributes[v]=h,me(e,n).then(t=>{var t=ue(zt(zt({},a),{},{icons:{main:t,mask:ct()},prefix:n,iconName:i,extra:r,watchable:!0})),e=g.createElementNS("http://www.w3.org/2000/svg","svg");"::before"===h?m.insertBefore(e,m.firstChild):m.appendChild(e),e.outerHTML=t.map(t=>$t(t)).join("\n"),m.removeAttribute(p),o()}).catch(s)}}else o()})}function Je(t){return Promise.all([Ke(t,"::before"),Ke(t,"::after")])}function Qe(t){return!(t.parentNode===document.head||~X.indexOf(t.tagName.toUpperCase())||t.getAttribute(v)||t.parentNode&&"svg"===t.parentNode.tagName)}function Ze(n){if(f)return new Promise((t,e)=>{var a=Vt(n.querySelectorAll("*")).filter(Qe).map(Je);let r=De.begin("searchPseudoElements");We(),Promise.all(a).then(()=>{r(),He(),t()}).catch(()=>{r(),He(),e()})})}let St={hooks(){return{mutationObserverCallbacks(t){return t.pseudoElementsCallback=Ze,t}}},provides(t){t.pseudoElements2svg=function(t){var{node:t=g}=t;k.searchPseudoElements&&Ze(t)}}},Ct=!1,Et={mixout(){return{dom:{unwatch(){We(),Ct=!0}}}},hooks(){return{bootstrap(){_e(oe("mutationObserverCallbacks",{}))},noAuto(){I&&I.disconnect()},watch(t){t=t.observeMutationsRoot;Ct?He():_e(oe("mutationObserverCallbacks",{observeMutationsRoot:t}))}}}},Mt=t=>t.toLowerCase().split(" ").reduce((t,e)=>{var e=e.toLowerCase().split("-"),a=e[0],r=e.slice(1).join("-");if(a&&"h"===r)t.flipX=!0;else if(a&&"v"===r)t.flipY=!0;else if(r=parseFloat(r),!isNaN(r))switch(a){case"grow":t.size=t.size+r;break;case"shrink":t.size=t.size-r;break;case"left":t.x=t.x-r;break;case"right":t.x=t.x+r;break;case"up":t.y=t.y-r;break;case"down":t.y=t.y+r;break;case"rotate":t.rotate=t.rotate+r}return t},{size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0}),jt={mixout(){return{parse:{transform:t=>Mt(t)}}},hooks(){return{parseNodeAttributes(t,e){e=e.getAttribute("data-fa-transform");return e&&(t.transform=Mt(e)),t}}},provides(t){t.generateAbstractTransformGrouping=function(t){var{main:t,transform:e,containerWidth:a,iconWidth:r}=t,a={transform:"translate(".concat(a/2," 256)")},n="translate(".concat(32*e.x,", ").concat(32*e.y,") "),i="scale(".concat(e.size/16*(e.flipX?-1:1),", ").concat(e.size/16*(e.flipY?-1:1),") "),e="rotate(".concat(e.rotate," 0 0)"),a={outer:a,inner:{transform:"".concat(n," ").concat(i," ").concat(e)},path:{transform:"translate(".concat(r/2*-1," -256)")}};return{tag:"g",attributes:zt({},a.outer),children:[{tag:"g",attributes:zt({},a.inner),children:[{tag:t.icon.tag,children:t.icon.children,attributes:zt(zt({},t.icon.attributes),a.path)}]}]}}}},D={x:0,y:0,width:"100%",height:"100%"};function $e(t){return t.attributes&&(t.attributes.fill||(!(1<arguments.length&&void 0!==arguments[1])||arguments[1]))&&(t.attributes.fill="black"),t}Tt=[ke,yt,xt,kt,wt,St,Et,jt,{hooks(){return{parseNodeAttributes(t,e){var a=e.getAttribute("data-fa-mask"),a=a?ie(a.split(" ").map(t=>t.trim())):ct();return a.prefix||(a.prefix=h),t.mask=a,t.maskId=e.getAttribute("data-fa-mask-id"),t}}},provides(t){t.generateAbstractMask=function(t){var{children:t,attributes:e,main:a,mask:r,maskId:n,transform:i}=t,{width:a,icon:o}=a,{width:r,icon:s}=r,i=(t=>{var{transform:t,containerWidth:e,iconWidth:a}=t,e={transform:"translate(".concat(e/2," 256)")},r="translate(".concat(32*t.x,", ").concat(32*t.y,") "),n="scale(".concat(t.size/16*(t.flipX?-1:1),", ").concat(t.size/16*(t.flipY?-1:1),") "),t="rotate(".concat(t.rotate," 0 0)");return{outer:e,inner:{transform:"".concat(r," ").concat(n," ").concat(t)},path:{transform:"translate(".concat(a/2*-1," -256)")}}})({transform:i,containerWidth:r,iconWidth:a}),r={tag:"rect",attributes:zt(zt({},D),{},{fill:"white"})},a=o.children?{children:o.children.map($e)}:{},o={tag:"g",attributes:zt({},i.inner),children:[$e(zt({tag:o.tag,attributes:zt(zt({},o.attributes),i.path)},a))]},a={tag:"g",attributes:zt({},i.outer),children:[o]},i="mask-".concat(n||qt()),o="clip-".concat(n||qt()),n={tag:"mask",attributes:zt(zt({},D),{},{id:i,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[r,a]},a={tag:"defs",children:[{tag:"clipPath",attributes:{id:o},children:"g"===(r=s).tag?r.children:[r]},n]};return t.push(a,{tag:"rect",attributes:zt({fill:"currentColor","clip-path":"url(#".concat(o,")"),mask:"url(#".concat(i,")")},D)}),{children:t,attributes:e}}}},{provides(t){let i=!1;n.matchMedia&&(i=n.matchMedia("(prefers-reduced-motion: reduce)").matches),t.missingIconAbstract=function(){var t=[],e={fill:"currentColor"},a={attributeType:"XML",repeatCount:"indefinite",dur:"2s"},r=(t.push({tag:"path",attributes:zt(zt({},e),{},{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})}),zt(zt({},a),{},{attributeName:"opacity"})),n={tag:"circle",attributes:zt(zt({},e),{},{cx:"256",cy:"364",r:"28"}),children:[]};return i||n.children.push({tag:"animate",attributes:zt(zt({},a),{},{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:zt(zt({},r),{},{values:"1;0;1;1;0;1;"})}),t.push(n),t.push({tag:"path",attributes:zt(zt({},e),{},{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:i?[]:[{tag:"animate",attributes:zt(zt({},r),{},{values:"1;0;0;0;0;1;"})}]}),i||t.push({tag:"path",attributes:zt(zt({},e),{},{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:zt(zt({},r),{},{values:"0;0;1;1;0;0;"})}]}),{tag:"g",attributes:{class:"missing"},children:t}}}},{hooks(){return{parseNodeAttributes(t,e){e=e.getAttribute("data-fa-symbol");return t.symbol=null!==e&&(""===e||e),t}}}}];{Wt=Tt;let r=(Ut={mixoutsTo:O}).mixoutsTo;mt=Wt,A={},Object.keys(P).forEach(t=>{-1===ht.indexOf(t)&&delete P[t]}),mt.forEach(t=>{let a=t.mixout?t.mixout():{};if(Object.keys(a).forEach(e=>{"function"==typeof a[e]&&(r[e]=a[e]),"object"==typeof a[e]&&Object.keys(a[e]).forEach(t=>{r[e]||(r[e]={}),r[e][t]=a[e][t]})}),t.hooks){let e=t.hooks();Object.keys(e).forEach(t=>{A[t]||(A[t]=[]),A[t].push(e[t])})}t.provides&&t.provides(P)}),r}!function(t){try{for(var e=arguments.length,a=new Array(1<e?e-1:0),r=1;r<e;r++)a[r-1]=arguments[r];t(...a)}catch(t){if(!c)throw t}}(function(t){Y&&(n.FontAwesome||(n.FontAwesome=O),Ne(()=>{N(),se("bootstrap")})),Zt.hooks=zt(zt({},Zt.hooks),{},{addPack:(t,e)=>{Zt.styles[t]=zt(zt({},Zt.styles[t]||{}),e),p(),N()},addPacks:t=>{t.forEach(t=>{var[t,e]=t;Zt.styles[t]=zt(zt({},Zt.styles[t]||{}),e)}),p(),N()},addShims:t=>{Zt.shims.push(...t),p(),N()}})})}