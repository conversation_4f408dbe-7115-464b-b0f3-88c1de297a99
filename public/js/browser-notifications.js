// Browser Push Notifications Handler
class BrowserNotifications {
    constructor() {
        this.serviceWorkerRegistration = null;
        this.isSupported = 'Notification' in window && 'serviceWorker' in navigator && 'PushManager' in window;
        this.permissionStatus = this.isSupported ? Notification.permission : 'unsupported';
    }

    // Initialize the notification system
    async init() {
        if (!this.isSupported) {
            console.log('Push notifications are not supported in this browser');
            return false;
        }

        try {
            // Register service worker
            this.serviceWorkerRegistration = await navigator.serviceWorker.register('/service-worker.js');
            console.log('Service Worker registered successfully');

            // Check if user has already granted permission
            if (this.permissionStatus === 'granted') {
                await this.subscribeUser();
            }

            // Listen for permission changes
            if ('permissions' in navigator) {
                const permissionStatus = await navigator.permissions.query({ name: 'notifications' });
                permissionStatus.addEventListener('change', () => {
                    this.permissionStatus = Notification.permission;
                    this.updateUI();
                });
            }

            return true;
        } catch (error) {
            console.error('Failed to initialize notifications:', error);
            return false;
        }
    }

    // Request permission from the user
    async requestPermission() {
        if (!this.isSupported) {
            alert('Push notifications are not supported in your browser');
            return false;
        }

        if (this.permissionStatus === 'granted') {
            console.log('Permission already granted');
            return true;
        }

        if (this.permissionStatus === 'denied') {
            alert('You have blocked notifications. Please enable them in your browser settings.');
            return false;
        }

        try {
            const permission = await Notification.requestPermission();
            this.permissionStatus = permission;
            
            if (permission === 'granted') {
                await this.subscribeUser();
                this.showSuccessNotification();
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('Failed to request notification permission:', error);
            return false;
        }
    }

    // Subscribe user to push notifications
    async subscribeUser() {
        if (!this.serviceWorkerRegistration) {
            console.error('Service Worker not registered');
            return false;
        }

        try {
            console.log('Getting VAPID public key...');
            // Get the server's public VAPID key
            const response = await fetch('/api/notifications/vapid-public-key', {
                credentials: 'same-origin'
            });
            
            if (!response.ok) {
                throw new Error(`Failed to get VAPID key: ${response.status}`);
            }
            
            const { publicKey } = await response.json();
            console.log('Got VAPID key:', publicKey.substring(0, 20) + '...');

            console.log('Subscribing to push manager...');
            const subscription = await this.serviceWorkerRegistration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: this.urlBase64ToUint8Array(publicKey)
            });

            console.log('Subscription created, sending to server...');
            // Send subscription to server
            await this.sendSubscriptionToServer(subscription);
            console.log('Subscription saved successfully');
            this.updateUI();
            return true;
        } catch (error) {
            console.error('Failed to subscribe user:', error);
            alert('Failed to enable notifications: ' + error.message);
            return false;
        }
    }

    // Send subscription details to server
    async sendSubscriptionToServer(subscription) {
        const response = await fetch('/api/notifications/subscribe', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            credentials: 'same-origin',
            body: JSON.stringify({
                subscription: subscription.toJSON(),
                device_info: {
                    userAgent: navigator.userAgent,
                    platform: navigator.platform,
                    language: navigator.language
                }
            })
        });

        if (!response.ok) {
            throw new Error('Failed to save subscription on server');
        }

        return response.json();
    }

    // Show a success notification
    showSuccessNotification() {
        if (this.serviceWorkerRegistration) {
            this.serviceWorkerRegistration.showNotification('Notifications Enabled!', {
                body: 'You will now receive notifications from ETRFlow.',
                icon: '/img/icon-192x192.png',
                badge: '/img/etrflow-icon.webp',
                vibrate: [200, 100, 200]
            });
        }
    }

    // Check if user is subscribed
    async isSubscribed() {
        if (!this.serviceWorkerRegistration) return false;

        try {
            const subscription = await this.serviceWorkerRegistration.pushManager.getSubscription();
            return subscription !== null;
        } catch (error) {
            console.error('Failed to check subscription status:', error);
            return false;
        }
    }

    // Unsubscribe user from push notifications
    async unsubscribe() {
        if (!this.serviceWorkerRegistration) return false;

        try {
            const subscription = await this.serviceWorkerRegistration.pushManager.getSubscription();
            if (subscription) {
                // Notify server
                await fetch('/api/notifications/unsubscribe', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({
                        endpoint: subscription.endpoint
                    })
                });

                // Unsubscribe locally
                await subscription.unsubscribe();
                this.updateUI();
                return true;
            }
            return false;
        } catch (error) {
            console.error('Failed to unsubscribe:', error);
            return false;
        }
    }

    // Update UI based on notification status
    updateUI() {
        const enableBtn = document.getElementById('enable-notifications-btn');
        const disableBtn = document.getElementById('disable-notifications-btn');
        const statusText = document.getElementById('notification-status');

        console.log('Updating UI - Permission:', this.permissionStatus, 'Supported:', this.isSupported);

        if (!this.isSupported) {
            if (statusText) statusText.textContent = 'Not supported';
            if (enableBtn) enableBtn.style.display = 'none';
            if (disableBtn) disableBtn.style.display = 'none';
            return;
        }

        this.isSubscribed().then(subscribed => {
            console.log('Subscription status:', subscribed, 'Permission:', this.permissionStatus);
            
            if (this.permissionStatus === 'granted' && subscribed) {
                if (statusText) statusText.textContent = 'Enabled';
                if (enableBtn) enableBtn.style.display = 'none';
                if (disableBtn) disableBtn.style.display = 'inline-block';
            } else if (this.permissionStatus === 'denied') {
                if (statusText) statusText.textContent = 'Blocked';
                if (enableBtn) enableBtn.style.display = 'none';
                if (disableBtn) disableBtn.style.display = 'none';
            } else {
                if (statusText) statusText.textContent = 'Disabled';
                if (enableBtn) enableBtn.style.display = 'inline-block';
                if (disableBtn) disableBtn.style.display = 'none';
            }
        }).catch(error => {
            console.error('Error checking subscription status:', error);
            if (statusText) statusText.textContent = 'Error';
        });
    }

    // Helper function to convert VAPID key
    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/\-/g, '+')
            .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }

    // Send a test notification
    async sendTestNotification() {
        console.log('Test notification requested - Permission:', this.permissionStatus);
        
        if (this.permissionStatus !== 'granted') {
            alert('Please enable notifications first');
            return;
        }

        const subscribed = await this.isSubscribed();
        console.log('Is subscribed:', subscribed);
        
        if (!subscribed) {
            alert('No active subscription found. Please enable notifications first.');
            return;
        }

        try {
            console.log('Sending test notification request...');
            const response = await fetch('/api/notifications/test', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                credentials: 'same-origin'
            });

            console.log('Test notification response:', response.status);
            
            if (response.ok) {
                const result = await response.json();
                console.log('Test notification result:', result);
                alert('Test notification sent! Check your browser notifications.');
            } else {
                const errorText = await response.text();
                console.error('Failed to send test notification:', response.status, errorText);
                alert('Failed to send test notification: ' + response.status);
            }
        } catch (error) {
            console.error('Error sending test notification:', error);
            alert('Error sending test notification: ' + error.message);
        }
    }
}

// Initialize on page load
let browserNotifications;
document.addEventListener('DOMContentLoaded', async () => {
    browserNotifications = new BrowserNotifications();
    await browserNotifications.init();
    browserNotifications.updateUI();

    // Bind enable button
    const enableBtn = document.getElementById('enable-notifications-btn');
    if (enableBtn) {
        enableBtn.addEventListener('click', async () => {
            await browserNotifications.requestPermission();
        });
    }

    // Bind disable button
    const disableBtn = document.getElementById('disable-notifications-btn');
    if (disableBtn) {
        disableBtn.addEventListener('click', async () => {
            if (confirm('Are you sure you want to disable notifications?')) {
                await browserNotifications.unsubscribe();
            }
        });
    }

    // Bind test button
    const testBtn = document.getElementById('test-notification-btn');
    if (testBtn) {
        testBtn.addEventListener('click', async () => {
            await browserNotifications.sendTestNotification();
        });
    }
});

// Export for use in other modules
window.BrowserNotifications = BrowserNotifications;