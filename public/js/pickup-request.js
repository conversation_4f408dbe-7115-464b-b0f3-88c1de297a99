/**
 * Pickup Request Form Handler
 * Manages multi-step form navigation, validation, and submission
 */

class PickupRequestForm {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 6;
        this.form = document.getElementById('pickup-request-form');
        this.submitBtn = this.form.querySelector('button[type="submit"]');
        this.submitText = this.submitBtn.querySelector('.submit-text');
        this.submitLoading = this.submitBtn.querySelector('.submit-loading');
        this.successMessage = document.getElementById('success-message');
        this.successText = document.getElementById('success-text');
        
        // Step elements
        this.stepContents = {
            1: document.getElementById('step-1'),
            2: document.getElementById('step-2'),
            3: document.getElementById('step-3'),
            4: document.getElementById('step-4'),
            5: document.getElementById('step-5'),
            6: document.getElementById('step-6')
        };

        this.stepIndicators = {
            1: document.getElementById('step-1-indicator'),
            2: document.getElementById('step-2-indicator'),
            3: document.getElementById('step-3-indicator'),
            4: document.getElementById('step-4-indicator'),
            5: document.getElementById('step-5-indicator'),
            6: document.getElementById('step-6-indicator')
        };

        // Time selection variables
        this.currentOffset = 0;
        this.selectedDate = null;
        this.selectedSlot = null;

        this.init();
    }

    init() {
        this.setupNavigation();
        this.setupValidation();
        this.setupFormSubmission();
        this.setupItemTypes();
    }

    setupNavigation() {
        // Step 1 -> Step 2 (Welcome -> Contact Info)
        document.getElementById('next-to-step-2')?.addEventListener('click', () => {
            if (this.validateStep(1)) {
                this.showStep(2);
            } else {
                this.scrollToFirstError(1);
            }
        });

        // Step 2 -> Step 1 (Contact Info -> Welcome)
        document.getElementById('back-to-step-1')?.addEventListener('click', () => {
            this.showStep(1);
        });

        // Step 2 -> Step 3 (Contact Info -> Time Selection)
        document.getElementById('next-to-step-3')?.addEventListener('click', () => {
            if (this.validateStep(2)) {
                this.showStep(3);
            } else {
                this.scrollToFirstError(2);
            }
        });

        // Step 3 -> Step 2 (Time Selection -> Contact Info)
        document.getElementById('back-to-step-2')?.addEventListener('click', () => {
            this.showStep(2);
        });

        // Step 3 -> Step 4 (Time Selection -> Pickup Details)
        document.getElementById('next-to-step-4')?.addEventListener('click', () => {
            if (this.validateStep(3)) {
                this.showStep(4);
            } else {
                this.showTimeSelectionError();
            }
        });

        // Step 4 -> Step 3 (Pickup Details -> Time Selection)
        document.getElementById('back-to-step-3')?.addEventListener('click', () => {
            this.showStep(3);
        });

        // Step 4 -> Step 5 (Pickup Details -> Item Details)
        document.getElementById('continue-to-items')?.addEventListener('click', () => {
            if (this.validateStep(4)) {
                this.showStep(5);
            } else {
                this.scrollToFirstError(4);
            }
        });

        // Step 5 -> Step 4 (Item Details -> Pickup Details)
        document.getElementById('back-to-step-4')?.addEventListener('click', () => {
            this.showStep(4);
        });

        // Step 5 -> Step 6 (Item Details -> Agreement)
        document.getElementById('next-to-step-6')?.addEventListener('click', () => {
            if (this.validateStep(5)) {
                this.showStep(6);
            } else {
                this.scrollToFirstError(5);
            }
        });

        // Step 6 -> Step 5 (Agreement -> Item Details)
        document.getElementById('back-to-step-5')?.addEventListener('click', () => {
            this.showStep(5);
        });
    }

    showStep(stepNumber) {
        // Hide all steps
        Object.values(this.stepContents).forEach(step => {
            step.classList.add('hidden');
        });

        // Show current step
        const currentStepElement = this.stepContents[stepNumber];
        if (currentStepElement) {
            currentStepElement.classList.remove('hidden');
        }

        // Update step indicators
        Object.keys(this.stepIndicators).forEach(step => {
            const stepNum = parseInt(step);
            const indicator = this.stepIndicators[stepNum];

            if (stepNum <= stepNumber) {
                indicator.classList.add('step-primary');
            } else {
                indicator.classList.remove('step-primary');
            }
        });

        // Update mobile progress
        this.updateMobileProgress(stepNumber);

        this.currentStep = stepNumber;

        // Load dates when step 3 (time selection) is shown
        if (stepNumber === 3) {
            this.loadAvailableDates();
        }

        // Reinitialize Google Maps autocomplete when step 4 (pickup details) is shown
        if (stepNumber === 4) {
            setTimeout(() => {
                if (typeof window.reinitializeGoogleMapsAutocomplete_pickup_address === 'function') {
                    window.reinitializeGoogleMapsAutocomplete_pickup_address();
                }
            }, 200);
        }

        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    validateStep(stepNumber) {
        let isValid = true;
        const step = this.stepContents[stepNumber];

        // Clear previous errors
        this.clearStepErrors(step);

        if (stepNumber === 1) {
            // Welcome step - no validation needed
            isValid = true;
        } else if (stepNumber === 2) {
            // Validate contact information
            const requiredFields = ['contact_name', 'email', 'phone'];
            requiredFields.forEach(fieldName => {
                const field = step.querySelector(`[name="${fieldName}"]`);
                if (!field.value.trim()) {
                    this.showFieldError(fieldName, 'This field is required.');
                    isValid = false;
                } else if (fieldName === 'email' && !this.isValidEmail(field.value)) {
                    this.showFieldError(fieldName, 'Please enter a valid email address.');
                    isValid = false;
                }
            });
        } else if (stepNumber === 3) {
            // Validate time selection
            const preferredPickupDate = document.getElementById('preferred_pickup_date');
            if (!preferredPickupDate.value) {
                this.showFieldError('preferred_pickup_date', 'Please select a pickup time slot.');
                isValid = false;
            }
        } else if (stepNumber === 4) {
            // Validate pickup details
            const requiredFields = ['pickup_address', 'property_location_details', 'driver_instructions'];
            requiredFields.forEach(fieldName => {
                const field = step.querySelector(`[name="${fieldName}"]`);
                if (!field.value.trim()) {
                    this.showFieldError(fieldName, 'This field is required.');
                    isValid = false;
                }
            });

            // Validate accessibility level
            const accessibilityLevel = step.querySelector('input[name="accessibility_level"]:checked');
            if (!accessibilityLevel) {
                this.showFieldError('accessibility_level', 'Please select an accessibility level.');
                isValid = false;
            }
        } else if (stepNumber === 5) {
            // Validate item details
            const itemSpecifics = step.querySelector('[name="item_specifics"]');
            if (!itemSpecifics.value.trim()) {
                this.showFieldError('item_specifics', 'This field is required.');
                isValid = false;
            }

            // Validate load size
            const loadSize = step.querySelector('input[name="load_size"]:checked');
            if (!loadSize) {
                this.showFieldError('load_size', 'Please select a load size.');
                isValid = false;
            }

            // Validate item types
            const itemTypes = step.querySelectorAll('input[name="item_types\\[\\]"]:checked');
            if (itemTypes.length === 0) {
                this.showFieldError('item_types[]', 'Please select at least one item type.');
                isValid = false;
            }
        } else if (stepNumber === 6) {
            // Validate agreement
            const termsAgreement = step.querySelector('[name="terms_agreement"]');
            if (!termsAgreement.checked) {
                this.showFieldError('terms_agreement', 'You must agree to the terms and conditions to continue.');
                isValid = false;
            }
        }

        return isValid;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    updateMobileProgress(stepNumber) {
        // Update mobile step counter
        const mobileCurrentStep = document.getElementById('mobile-current-step');
        if (mobileCurrentStep) {
            mobileCurrentStep.textContent = stepNumber;
        }

        // Update mobile progress bar
        const mobileProgressBar = document.getElementById('mobile-progress-bar');
        if (mobileProgressBar) {
            const progressPercentage = (stepNumber / this.totalSteps) * 100;
            mobileProgressBar.style.width = `${progressPercentage}%`;
        }
    }

    clearStepErrors(step) {
        step.querySelectorAll('.error-message').forEach(error => {
            error.classList.add('hidden');
            error.textContent = '';
        });
        step.querySelectorAll('.input, .textarea').forEach(input => {
            input.classList.remove('input-error', 'textarea-error');
        });
    }

    scrollToFirstError(stepNumber) {
        const firstError = this.stepContents[stepNumber].querySelector('.input-error, .textarea-error');
        if (firstError) {
            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstError.focus();
        }
    }

    showTimeSelectionError() {
        const timeError = this.stepContents[2].querySelector('.error-message');
        if (timeError) {
            timeError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    setupValidation() {
        // Add real-time validation for all steps
        for (let stepNum = 1; stepNum <= this.totalSteps; stepNum++) {
            const stepFields = this.stepContents[stepNum].querySelectorAll('input[required], textarea[required]');
            stepFields.forEach(field => {
                field.addEventListener('blur', () => {
                    if (field.value.trim()) {
                        this.clearFieldError(field.name);
                        field.classList.remove('input-error', 'textarea-error');
                        if (field.classList.contains('input')) {
                            field.classList.add('input-success');
                        }
                    }
                });

                field.addEventListener('input', () => {
                    if ((field.classList.contains('input-error') || field.classList.contains('textarea-error')) && field.value.trim()) {
                        this.clearFieldError(field.name);
                        field.classList.remove('input-error', 'textarea-error');
                    }
                });
            });
        }

        // Add validation for radio buttons and checkboxes
        this.setupRadioValidation();
        this.setupCheckboxValidation();
    }

    setupRadioValidation() {
        // Accessibility level validation and visual selection
        const accessibilityRadios = document.querySelectorAll('input[name="accessibility_level"]');
        accessibilityRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                if (radio.checked) {
                    this.clearFieldError('accessibility_level');
                    
                    // Update visual selection for accessibility cards
                    document.querySelectorAll('input[name="accessibility_level"]').forEach(r => {
                        const card = r.closest('label');
                        card.classList.remove('ring-2', 'ring-offset-2');
                        if (card.classList.contains('bg-green-50')) {
                            card.classList.remove('ring-green-500', 'bg-green-100');
                            card.classList.add('bg-green-50');
                        } else if (card.classList.contains('bg-orange-50')) {
                            card.classList.remove('ring-orange-500', 'bg-orange-100');
                            card.classList.add('bg-orange-50');
                        } else if (card.classList.contains('bg-red-50')) {
                            card.classList.remove('ring-red-500', 'bg-red-100');
                            card.classList.add('bg-red-50');
                        }
                    });
                    
                    // Add selected state to current card
                    const selectedCard = radio.closest('label');
                    selectedCard.classList.add('ring-2', 'ring-offset-2');
                    if (selectedCard.classList.contains('bg-green-50')) {
                        selectedCard.classList.add('ring-green-500', 'bg-green-100');
                        selectedCard.classList.remove('bg-green-50');
                    } else if (selectedCard.classList.contains('bg-orange-50')) {
                        selectedCard.classList.add('ring-orange-500', 'bg-orange-100');
                        selectedCard.classList.remove('bg-orange-50');
                    } else if (selectedCard.classList.contains('bg-red-50')) {
                        selectedCard.classList.add('ring-red-500', 'bg-red-100');
                        selectedCard.classList.remove('bg-red-50');
                    }
                }
            });
        });

        // Load size validation and visual selection
        const loadSizeRadios = document.querySelectorAll('input[name="load_size"]');
        loadSizeRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                if (radio.checked) {
                    this.clearFieldError('load_size');
                    
                    // Update visual selection for load size cards
                    const allCards = document.querySelectorAll('input[name="load_size"]').forEach(r => {
                        const card = r.closest('label');
                        card.classList.remove('ring-2', 'ring-offset-2');
                        if (card.classList.contains('bg-green-50')) {
                            card.classList.remove('ring-green-500', 'bg-green-100');
                            card.classList.add('bg-green-50');
                        } else if (card.classList.contains('bg-orange-50')) {
                            card.classList.remove('ring-orange-500', 'bg-orange-100');
                            card.classList.add('bg-orange-50');
                        } else if (card.classList.contains('bg-red-50')) {
                            card.classList.remove('ring-red-500', 'bg-red-100');
                            card.classList.add('bg-red-50');
                        }
                    });
                    
                    // Add selected state to current card
                    const selectedCard = radio.closest('label');
                    selectedCard.classList.add('ring-2', 'ring-offset-2');
                    if (selectedCard.classList.contains('bg-green-50')) {
                        selectedCard.classList.add('ring-green-500', 'bg-green-100');
                        selectedCard.classList.remove('bg-green-50');
                    } else if (selectedCard.classList.contains('bg-orange-50')) {
                        selectedCard.classList.add('ring-orange-500', 'bg-orange-100');
                        selectedCard.classList.remove('bg-orange-50');
                    } else if (selectedCard.classList.contains('bg-red-50')) {
                        selectedCard.classList.add('ring-red-500', 'bg-red-100');
                        selectedCard.classList.remove('bg-red-50');
                    }
                }
            });
        });
    }

    setupCheckboxValidation() {
        // Item types validation - handled by setupItemTypes() now
        const itemTypeCheckboxes = document.querySelectorAll('input[name="item_types\\[\\]"]');
        itemTypeCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                const checkedBoxes = document.querySelectorAll('input[name="item_types\\[\\]"]:checked');
                if (checkedBoxes.length > 0) {
                    this.clearFieldError('item_types[]');
                }
            });
        });
    }

    setupItemTypes() {
        const itemTypeButtons = document.querySelectorAll('.item-type-button');
        const notesDisplay = document.getElementById('item-notes-display');
        const notesList = document.getElementById('item-notes-list');

        const updateNotesDisplay = () => {
            // Get all selected items with notes
            const selectedItemsWithNotes = Array.from(itemTypeButtons)
                .filter(btn => btn.querySelector('.item-type-checkbox').checked && btn.dataset.notes && btn.dataset.notes.trim())
                .map(btn => ({
                    name: btn.querySelector('.label-text').textContent,
                    notes: btn.dataset.notes
                }));

            if (selectedItemsWithNotes.length > 0) {
                // Clear existing notes
                notesList.innerHTML = '';

                // Add each item's notes to the list
                selectedItemsWithNotes.forEach(item => {
                    const listItem = document.createElement('li');
                    listItem.innerHTML = `<strong>${item.name}:</strong> ${item.notes}`;
                    notesList.appendChild(listItem);
                });

                notesDisplay.classList.remove('hidden');
            } else {
                notesDisplay.classList.add('hidden');
            }
        };

        itemTypeButtons.forEach(button => {
            const checkbox = button.querySelector('.item-type-checkbox');
            const checkmark = button.querySelector('.checkmark');

            button.addEventListener('click', (e) => {
                e.preventDefault();

                // Toggle checkbox state
                checkbox.checked = !checkbox.checked;

                // Update visual state
                if (checkbox.checked) {
                    button.classList.add('border-primary', 'bg-primary/10');
                    button.classList.remove('border-base-300');
                    checkmark.classList.remove('hidden');
                    checkmark.classList.add('flex');
                } else {
                    button.classList.remove('border-primary', 'bg-primary/10');
                    button.classList.add('border-base-300');
                    checkmark.classList.add('hidden');
                    checkmark.classList.remove('flex');
                }

                // Update notes display
                updateNotesDisplay();

                // Clear validation errors if any items are selected
                const checkedBoxes = document.querySelectorAll('input[name="item_types\\[\\]"]:checked');
                if (checkedBoxes.length > 0) {
                    this.clearFieldError('item_types[]');
                }

                // Trigger change event for form validation
                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
            });
        });

        // Initialize notes display on page load
        updateNotesDisplay();
    }

    setupPickupExperience() {
        const experienceButtons = document.querySelectorAll('.pickup-experience-btn');
        const firstTimeModal = document.getElementById('first-time-pickup-modal');
        const modalCloseButtons = firstTimeModal?.querySelectorAll('.modal-close');
        const welcomeBackMessage = document.getElementById('welcome-back-message');

        experienceButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                
                const answer = button.dataset.answer;
                
                // Update button states - reset all to neutral first
                experienceButtons.forEach(btn => {
                    btn.classList.remove('btn-primary');
                    btn.classList.add('btn-neutral');
                });
                
                // Make the selected button primary
                button.classList.remove('btn-neutral');
                button.classList.add('btn-primary');
                
                if (answer === 'yes') {
                    // Show welcome back message
                    if (welcomeBackMessage) {
                        welcomeBackMessage.classList.remove('hidden');
                    }
                } else if (answer === 'no') {
                    // Hide welcome back message
                    if (welcomeBackMessage) {
                        welcomeBackMessage.classList.add('hidden');
                    }
                    
                    // Show first-time customer modal
                    if (firstTimeModal) {
                        firstTimeModal.classList.add('modal-open');
                    }
                }
            });
        });

        // Handle modal close
        if (modalCloseButtons) {
            modalCloseButtons.forEach(closeBtn => {
                closeBtn.addEventListener('click', () => {
                    if (firstTimeModal) {
                        firstTimeModal.classList.remove('modal-open');
                    }
                });
            });
        }

        // Handle backdrop click
        if (firstTimeModal) {
            const modalBackdrop = firstTimeModal.querySelector('.modal-backdrop');
            if (modalBackdrop) {
                modalBackdrop.addEventListener('click', () => {
                    firstTimeModal.classList.remove('modal-open');
                });
            }
        }
    }

    showFieldError(fieldName, message) {
        // Escape field name for use in CSS selectors (handle brackets in field names like item_types[])
        const escapedFieldName = fieldName.replace(/\[/g, '\\[').replace(/\]/g, '\\]');
        const input = this.form.querySelector(`[name="${escapedFieldName}"], #${escapedFieldName}`);
        if (input) {
            if (input.classList.contains('textarea')) {
                input.classList.add('textarea-error');
            } else if (input.classList.contains('select')) {
                input.classList.add('select-error');
            } else {
                input.classList.add('input-error');
            }

            const errorDiv = input.parentNode.querySelector('.error-message');
            if (errorDiv) {
                errorDiv.textContent = message;
                errorDiv.classList.remove('hidden');
            }
        }

        // Special handling for radio button groups
        if (fieldName === 'accessibility_level' || fieldName === 'load_size') {
            const fieldset = this.form.querySelector(`input[name="${fieldName}"]`)?.closest('fieldset');
            if (fieldset) {
                const errorDiv = fieldset.querySelector('.error-message');
                if (errorDiv) {
                    errorDiv.textContent = message;
                    errorDiv.classList.remove('hidden');
                }
            }
        }

        // Special handling for checkbox groups
        if (fieldName === 'item_types[]') {
            const fieldset = this.form.querySelector('input[name="item_types\\[\\]"]')?.closest('fieldset');
            if (fieldset) {
                const errorDiv = fieldset.querySelector('.error-message');
                if (errorDiv) {
                    errorDiv.textContent = message;
                    errorDiv.classList.remove('hidden');
                }
            }
        }

        // Special handling for date/time selection
        if (fieldName === 'preferred_pickup_date') {
            const dateSelectionContainer = document.getElementById('date-selection-view');
            if (dateSelectionContainer) {
                const errorDiv = dateSelectionContainer.parentNode.querySelector('.error-message');
                if (errorDiv) {
                    errorDiv.textContent = message;
                    errorDiv.classList.remove('hidden');
                }
            }
        }
    }

    clearFieldError(fieldName) {
        // Escape field name for use in CSS selectors (handle brackets in field names like item_types[])
        const escapedFieldName = fieldName.replace(/\[/g, '\\[').replace(/\]/g, '\\]');
        const input = this.form.querySelector(`[name="${escapedFieldName}"], #${escapedFieldName}`);
        if (input) {
            input.classList.remove('input-error', 'textarea-error', 'select-error');

            const errorDiv = input.parentNode.querySelector('.error-message');
            if (errorDiv) {
                errorDiv.textContent = '';
                errorDiv.classList.add('hidden');
            }
        }

        // Special handling for radio button groups
        if (fieldName === 'accessibility_level' || fieldName === 'load_size') {
            const fieldset = this.form.querySelector(`input[name="${fieldName}"]`)?.closest('fieldset');
            if (fieldset) {
                const errorDiv = fieldset.querySelector('.error-message');
                if (errorDiv) {
                    errorDiv.textContent = '';
                    errorDiv.classList.add('hidden');
                }
            }
        }

        // Special handling for checkbox groups
        if (fieldName === 'item_types[]') {
            const fieldset = this.form.querySelector('input[name="item_types\\[\\]"]')?.closest('fieldset');
            if (fieldset) {
                const errorDiv = fieldset.querySelector('.error-message');
                if (errorDiv) {
                    errorDiv.textContent = '';
                    errorDiv.classList.add('hidden');
                }
            }
        }

        // Special handling for date/time selection
        if (fieldName === 'preferred_pickup_date') {
            const dateSelectionContainer = document.getElementById('date-selection-view');
            if (dateSelectionContainer) {
                const errorDiv = dateSelectionContainer.parentNode.querySelector('.error-message');
                if (errorDiv) {
                    errorDiv.textContent = '';
                    errorDiv.classList.add('hidden');
                }
            }
        }
    }

    async loadLeadTimeMessage() {
        try {
            const response = await fetch('/pickup-request/lead-time-info');
            const data = await response.json();

            if (data.message) {
                document.getElementById('lead-time-message').textContent = data.message;
            }
        } catch (error) {
            console.error('Error loading lead time info:', error);
            document.getElementById('lead-time-notice').style.display = 'none';
        }
    }

    async loadAvailableDates(offset = 0) {
        const dateSelectionView = document.getElementById('date-selection-view');
        if (!dateSelectionView) return;

        try {
            const response = await fetch(`/pickup-request/available-dates?offset=${offset}`);
            const data = await response.json();

            if (data.error) {
                dateSelectionView.innerHTML = `
                    <div class="text-center py-8">
                        <i class="fa-sharp fa-exclamation-triangle text-warning text-2xl mb-2"></i>
                        <p class="text-base-content">${data.error}</p>
                    </div>
                `;
                return;
            }

            if (data.dates && data.dates.length > 0) {
                this.renderAvailableDates(data.dates, data.has_more, data.next_offset);
            } else {
                if (offset === 0) {
                    dateSelectionView.innerHTML = `
                        <div class="text-center py-12">
                            <div class="avatar avatar-placeholder mb-4">
                                <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                    <i class="fa-sharp fa-calendar-times text-2xl"></i>
                                </div>
                            </div>
                            <h3 class="text-lg font-medium text-base-content/80 mb-2">No available pickup dates</h3>
                            <p class="text-sm text-base-content/60">No available pickup dates found in the next 3 weeks. Please contact us directly to schedule your pickup.</p>
                        </div>
                    `;
                }
            }
        } catch (error) {
            console.error('Error loading available dates:', error);
            dateSelectionView.innerHTML = `
                <div class="text-center py-8">
                    <i class="fa-sharp fa-exclamation-triangle text-error text-2xl mb-2"></i>
                    <p class="text-base-content">Error loading dates. Please refresh the page.</p>
                </div>
            `;
        }
    }

    renderAvailableDates(dates, hasMore, nextOffset) {
        const dateSelectionView = document.getElementById('date-selection-view');

        if (this.currentOffset === 0) {
            // Initial load - create the grid structure
            const dateGrid = document.createElement('div');
            dateGrid.className = 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';

            // Add date cards
            dates.forEach(date => {
                const dateCard = this.createDateCard(date);
                dateGrid.appendChild(dateCard);
            });

            // Add "more dates" button if needed
            if (hasMore) {
                const moreDatesBtn = this.createMoreDatesButton(nextOffset);
                dateGrid.appendChild(moreDatesBtn);
            }

            dateSelectionView.innerHTML = '';
            dateSelectionView.appendChild(dateGrid);
        } else {
            // Append to existing dates
            const existingMoreBtn = dateSelectionView.querySelector('[data-offset]');
            if (existingMoreBtn) {
                existingMoreBtn.remove();
            }

            const dateGrid = dateSelectionView.querySelector('.grid');

            dates.forEach(date => {
                const dateCard = this.createDateCard(date);
                dateGrid.appendChild(dateCard);
            });

            if (hasMore) {
                const moreDatesBtn = this.createMoreDatesButton(nextOffset);
                dateGrid.appendChild(moreDatesBtn);
            }
        }
    }

    createDateCard(date) {
        const dateCard = document.createElement('div');
        dateCard.className = 'card bg-base-100 border border-base-300 hover:bg-primary/5 hover:border-primary/30 transition-all duration-200 cursor-pointer shadow-sm hover:shadow-md';
        dateCard.dataset.date = date.date;
        dateCard.innerHTML = `
            <div class="card-body p-4 text-center">
                <div class="text-sm font-medium text-primary">${date.day_name}</div>
                <div class="text-lg font-semibold text-base-content">
                    ${date.is_today ? 'Today, ' : ''}${date.is_tomorrow ? 'Tomorrow, ' : ''}${date.display_date}
                </div>
                <div class="text-sm text-base-content/70">
                    ${date.available_slots} slot${date.available_slots !== 1 ? 's' : ''} available
                </div>
            </div>
        `;

        dateCard.addEventListener('click', () => {
            this.selectedDate = date.date;
            this.loadTimeSlotsForDate(date.date);
        });

        return dateCard;
    }

    createMoreDatesButton(nextOffset) {
        const moreDatesBtn = document.createElement('div');
        moreDatesBtn.className = 'card bg-base-200/50 border border-dashed border-primary/50 hover:bg-primary/5 hover:border-primary/70 transition-all duration-200 cursor-pointer';
        moreDatesBtn.dataset.offset = nextOffset;
        moreDatesBtn.innerHTML = `
            <div class="card-body p-4 text-center">
                <div class="text-primary font-medium">
                    <i class="fa-sharp fa-plus-circle mr-2"></i>
                    Show More Dates
                </div>
            </div>
        `;

        moreDatesBtn.addEventListener('click', () => {
            this.currentOffset = nextOffset;
            this.loadAvailableDates(nextOffset);
        });

        return moreDatesBtn;
    }

    async loadTimeSlotsForDate(date) {
        const dateSelectionView = document.getElementById('date-selection-view');
        const timeSelectionView = document.getElementById('time-selection-view');
        const timeSlotsContainer = document.getElementById('time-slots-container');
        const selectedDateTitle = document.getElementById('selected-date-title');

        // Show loading state
        timeSlotsContainer.innerHTML = `
            <div class="flex items-center justify-center py-8">
                <span class="loading loading-bars loading-sm text-primary"></span>
                <span class="ml-3 text-base-content">Loading available times...</span>
            </div>
        `;

        // Switch to time selection view
        dateSelectionView.classList.add('hidden');
        timeSelectionView.classList.remove('hidden');

        try {
            const response = await fetch(`/pickup-request/time-slots?date=${date}`);
            const data = await response.json();

            if (data.error) {
                timeSlotsContainer.innerHTML = `
                    <div class="text-center py-8">
                        <i class="fa-sharp fa-exclamation-triangle text-warning text-2xl mb-2"></i>
                        <p class="text-base-content">${data.error}</p>
                    </div>
                `;
                return;
            }

            selectedDateTitle.textContent = `Available Times for ${data.display_date}`;

            if (data.slots && data.slots.length > 0) {
                this.renderTimeSlots(data.slots);
            } else {
                timeSlotsContainer.innerHTML = `
                    <div class="text-center py-12">
                        <div class="avatar avatar-placeholder mb-4">
                            <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                <i class="fa-sharp fa-clock text-2xl"></i>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-base-content/80 mb-2">No available times</h3>
                        <p class="text-sm text-base-content/60">No available times for this date. Please try a different date.</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading time slots:', error);
            timeSlotsContainer.innerHTML = `
                <div class="text-center py-8">
                    <i class="fa-sharp fa-exclamation-triangle text-error text-2xl mb-2"></i>
                    <p class="text-base-content">Error loading times. Please try again.</p>
                </div>
            `;
        }
    }

    renderTimeSlots(slots) {
        const timeSlotsContainer = document.getElementById('time-slots-container');
        const preferredPickupDateInput = document.getElementById('preferred_pickup_date');
        const selectedTimeDisplay = document.getElementById('selected-time-display');
        const selectedTimeText = document.getElementById('selected-time-text');
        const nextToStep4Btn = document.getElementById('next-to-step-4');

        const slotsHTML = `
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
                ${slots.map(slot => `
                    <button type="button" class="btn btn-outline btn-lg hover:btn-primary transition-all duration-200" data-datetime="${slot.datetime}">
                        ${slot.display}
                    </button>
                `).join('')}
            </div>
        `;

        timeSlotsContainer.innerHTML = slotsHTML;

        // Add click handlers to time slots
        const timeSlots = timeSlotsContainer.querySelectorAll('button[data-datetime]');
        timeSlots.forEach(slot => {
            slot.addEventListener('click', () => {
                // Remove previous selection
                if (this.selectedSlot) {
                    this.selectedSlot.classList.remove('btn-primary');
                    this.selectedSlot.classList.add('btn-outline');
                }

                // Select new slot
                slot.classList.remove('btn-outline');
                slot.classList.add('btn-primary');
                this.selectedSlot = slot;
                preferredPickupDateInput.value = slot.dataset.datetime;

                // Show selected time
                const selectedDateTime = new Date(slot.dataset.datetime);
                const displayText = selectedDateTime.toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                }) + ' at ' + selectedDateTime.toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit'
                });
                selectedTimeText.textContent = displayText;
                selectedTimeDisplay.classList.remove('hidden');

                // Clear any previous error
                this.clearFieldError('preferred_pickup_date');

                // Enable next button
                nextToStep4Btn.disabled = false;
            });
        });

        // Setup back to dates button (only if not already set up)
        const backToDatesBtn = document.getElementById('back-to-dates-btn');
        if (backToDatesBtn && !backToDatesBtn.hasAttribute('data-listener-added')) {
            backToDatesBtn.setAttribute('data-listener-added', 'true');
            backToDatesBtn.addEventListener('click', () => {
                const dateSelectionView = document.getElementById('date-selection-view');
                const timeSelectionView = document.getElementById('time-selection-view');

                timeSelectionView.classList.add('hidden');
                dateSelectionView.classList.remove('hidden');
                this.selectedDate = null;

                // Clear any selected time slot
                if (this.selectedSlot) {
                    this.selectedSlot.classList.remove('btn-primary');
                    this.selectedSlot.classList.add('btn-outline');
                    this.selectedSlot = null;
                }
                preferredPickupDateInput.value = '';
                selectedTimeDisplay.classList.add('hidden');

                // Disable next button
                nextToStep4Btn.disabled = true;

                // Clear any errors
                this.clearFieldError('preferred_pickup_date');
            });
        }
    }

    setupFormSubmission() {
        this.form.addEventListener('submit', async (e) => {
            e.preventDefault();

            // Clear previous errors
            this.clearAllErrors();

            // Validate all steps before submission
            let allStepsValid = true;
            for (let step = 1; step <= this.totalSteps; step++) {
                if (!this.validateStep(step)) {
                    allStepsValid = false;
                    if (step < this.totalSteps) {
                        this.showStep(step);
                        return;
                    }
                }
            }

            if (!allStepsValid) {
                return;
            }

            // Show loading state
            this.submitBtn.disabled = true;
            this.submitText.style.display = 'none';
            this.submitLoading.classList.remove('hidden');

            try {
                const formData = new FormData(this.form);

                // Add reCAPTCHA token if available
                if (window.executeRecaptcha) {
                    try {
                        const recaptchaToken = await window.executeRecaptcha('pickup_request');
                        formData.append('recaptcha_token', recaptchaToken);
                    } catch (recaptchaError) {
                        console.error('reCAPTCHA generation failed:', recaptchaError);
                        throw new Error('Security verification failed. Please refresh the page and try again.');
                    }
                }

                const response = await fetch(this.form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Check if we have a redirect URL (for success page)
                    if (data.redirect_url) {
                        // Redirect to the success page
                        window.location.href = data.redirect_url;
                        return;
                    }

                    // Fallback: Show success message inline (if no redirect URL)
                    this.successText.textContent = data.message;
                    this.successMessage.classList.remove('hidden');

                    // Hide form
                    this.form.classList.add('hidden');

                    // Hide steps indicator
                    document.querySelector('.steps').classList.add('hidden');

                    // Scroll to top
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                } else {
                    throw new Error(data.message || data.error || 'An error occurred');
                }
            } catch (error) {
                console.error('Submission error:', error);
                alert('An error occurred while submitting your request. Please try again.');
            } finally {
                // Reset button state
                this.submitBtn.disabled = false;
                this.submitText.style.display = 'inline-flex';
                this.submitLoading.classList.add('hidden');
            }
        });
    }

    clearAllErrors() {
        const errorMessages = this.form.querySelectorAll('.error-message');
        errorMessages.forEach(error => {
            error.textContent = '';
            error.classList.add('hidden');
        });

        const inputs = this.form.querySelectorAll('.input, .textarea, .select');
        inputs.forEach(input => {
            input.classList.remove('input-error', 'textarea-error', 'select-error');
        });
    }


}

// Global reference for event handlers
let pickupForm;

// Initialize the form when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    try {
        pickupForm = new PickupRequestForm();
    } catch (error) {
        console.error('Error initializing PickupRequestForm:', error);
    }
});
