/**
 * Image Dropzone Component
 * Reusable image upload component with client-side compression
 */

class ImageDropzone {
    constructor(element) {
        // Prevent duplicate initialization
        if (element._dropzoneInstance) {
            return element._dropzoneInstance;
        }
        
        this.element = element;
        this.dropzone = element.querySelector('[data-config]');
        this.config = JSON.parse(this.dropzone.dataset.config);
        
        // Store reference to this instance on the element
        element._dropzoneInstance = this;
        
        // Elements
        this.fileInput = element.querySelector(`#file-${this.config.id}`);
        this.additionalInput = element.querySelector(`#additional-${this.config.id}`);
        this.previewContainer = element.querySelector(`#preview-${this.config.id}`);
        this.queueContainer = element.querySelector(`#queue-${this.config.id}`);
        this.progressIndicator = element.querySelector(`#progress-${this.config.id}`);
        this.photoCount = element.querySelector('.photo-count');
        this.totalSize = element.querySelector('.total-size');
        this.addMoreBtn = element.querySelector('.add-more-btn');
        this.clearAllBtn = element.querySelector('.clear-all-btn');
        this.errorContainer = element.querySelector('.error-container');
        
        // State
        this.files = [];
        this.uploadedImages = [];
        this.isProcessing = false;
        
        // Initialize
        this.init();
        
        // Add form submission handler to ensure files are set
        this.setupFormSubmissionHandler();
    }
    
    init() {
        this.setupEventListeners();
        this.detectMobile();
        
        // Initialize with existing images if any
        const existingImages = this.previewContainer.querySelectorAll('[data-image-id]');
        existingImages.forEach(el => {
            const id = el.dataset.imageId;
            const img = el.querySelector('img');
            if (id && img) {
                this.uploadedImages.push({
                    id: id,
                    src: img.src,
                    alt: img.alt,
                    size: 0 // Existing images don't have size info, so set to 0
                });
            }
        });
        
        // Generate session ID if needed
        if (this.config.sessionBased && !this.config.sessionId) {
            this.config.sessionId = 'dropzone_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            const sessionInput = this.element.querySelector('input[name="session_id"]');
            if (sessionInput) {
                sessionInput.value = this.config.sessionId;
            }
        }
    }
    
    setupEventListeners() {
        // Dropzone click
        this.dropzone.addEventListener('click', (e) => {
            e.stopPropagation();
            this.fileInput.click();
        });
        
        // Drag and drop
        this.dropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.dropzone.classList.add('border-primary', 'bg-primary/5');
        });
        
        this.dropzone.addEventListener('dragleave', () => {
            this.dropzone.classList.remove('border-primary', 'bg-primary/5');
        });
        
        this.dropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            this.dropzone.classList.remove('border-primary', 'bg-primary/5');
            this.handleFiles(e.dataTransfer.files);
        });
        
        // File input changes
        this.fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });
        
        this.additionalInput?.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });
        
        // Buttons
        this.addMoreBtn?.addEventListener('click', () => {
            // Don't allow add more if additionalInput doesn't exist
            if (!this.additionalInput) {
                return;
            }
            this.additionalInput.click();
        });
        
        this.clearAllBtn?.addEventListener('click', () => {
            if (confirm('Are you sure you want to remove all images?')) {
                this.clearAll();
            }
        });
        
        // Remove image buttons
        this.element.addEventListener('click', (e) => {
            if (e.target.closest('.remove-image-btn')) {
                const btn = e.target.closest('.remove-image-btn');
                const imageId = btn.dataset.imageId;
                this.removeImage(imageId);
            }
        });
    }
    
    detectMobile() {
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        
        if (isMobile && this.config.capture) {
            // For mobile, only remove multiple if maxFiles is 1
            if (this.config.maxFiles === 1) {
                this.fileInput.removeAttribute('multiple');
                this.additionalInput?.removeAttribute('multiple');
            }
            
            // Update button text
            if (this.addMoreBtn) {
                this.addMoreBtn.innerHTML = '<i class="fa-sharp fa-camera"></i> Take Another Photo';
            }
            
            // Only auto-trigger camera if explicitly configured
            if (this.config.triggerCameraUpload === true) {
                // Add click event to automatically trigger camera
                setTimeout(() => {
                    this.fileInput.click();
                }, 100);
            }
        }
    }
    
    async handleFiles(fileList) {
        const files = Array.from(fileList);
        
        // Validate files
        const validFiles = files.filter(file => {
            // Check file type
            const validTypes = this.config.acceptedFormats.split(',');
            if (!validTypes.includes(file.type)) {
                this.showError(`Invalid file type: ${file.name}. Accepted types: ${validTypes.map(t => t.replace('image/', '')).join(', ')}`);
                return false;
            }
            
            // Check file size
            const maxSize = this.config.maxFilesize * 1024 * 1024;
            if (file.size > maxSize) {
                this.showError(`File too large: ${file.name}. Maximum size: ${this.config.maxFilesize}MB`);
                return false;
            }
            
            return true;
        });
        
        // Check max files limit
        const currentCount = this.uploadedImages.length + this.files.length;
        const remainingSlots = this.config.maxFiles - currentCount;
        
        if (validFiles.length > remainingSlots) {
            this.showError(`You can only upload ${remainingSlots} more image(s). Maximum ${this.config.maxFiles} allowed.`);
            validFiles.splice(remainingSlots);
        }
        
        if (validFiles.length === 0) return;
        
        // Dispatch event for file selection tracking with actual valid files count
        this.element.dispatchEvent(new CustomEvent('files-selected', {
            detail: { count: validFiles.length }
        }));
        
        // Process files
        this.setProcessing(true);
        
        for (const file of validFiles) {
            if (this.config.immediateUpload) {
                // Immediate upload mode - upload each file directly
                await this.uploadFileImmediately(file);
            } else if (this.config.sessionBased && this.config.uploadUrl) {
                // Upload to server
                await this.uploadFile(file);
            } else {
                // Add to local queue
                await this.addToQueue(file);
            }
        }
        
        this.setProcessing(false);
        this.updateDisplay();
        
        // Clear file inputs
        this.fileInput.value = '';
        if (this.additionalInput) {
            this.additionalInput.value = '';
        }
    }
    
    async addToQueue(file) {
        let processedFile = file;
        
        // Compress if enabled
        if (this.config.clientResize && (this.config.maxWidth || this.config.maxHeight)) {
            try {
                const options = {
                    maxSizeMB: Math.min(this.config.maxFilesize, 2),
                    maxWidthOrHeight: Math.max(this.config.maxWidth || 0, this.config.maxHeight || 0),
                    useWebWorker: true,
                    fileType: file.type,
                    initialQuality: 0.85
                };
                
                const compressedBlob = await imageCompression(file, options);
                
                // Create a new File object with the original filename
                const originalExtension = file.name.split('.').pop();
                const nameWithoutExtension = file.name.replace('.' + originalExtension, '');
                const newFileName = nameWithoutExtension + '.webp';
                
                processedFile = new File([compressedBlob], newFileName, {
                    type: 'image/webp',
                    lastModified: Date.now()
                });
                
                processedFile.originalName = file.name;
                console.log('Compressed:', file.size / 1024 / 1024, 'MB to', processedFile.size / 1024 / 1024, 'MB');
            } catch (error) {
                console.error('Compression error:', error);
                this.showError('Failed to compress image. Using original file.');
            }
        }
        
        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
            this.files.push({
                id: 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                file: processedFile,
                originalFile: file,
                src: e.target.result,
                name: file.name,
                size: processedFile.size
            });
            this.updateDisplay();
        };
        reader.readAsDataURL(processedFile);
    }
    
    async uploadFile(file) {
        try {
            let processedFile = file;
            
            // Compress if enabled
            if (this.config.clientResize && (this.config.maxWidth || this.config.maxHeight) && window.imageCompression) {
                try {
                    const options = {
                        maxSizeMB: Math.min(this.config.maxFilesize, 2),
                        maxWidthOrHeight: Math.max(this.config.maxWidth || 0, this.config.maxHeight || 0),
                        useWebWorker: true,
                        fileType: file.type,
                        initialQuality: 0.85
                    };
                    
                    const compressedBlob = await imageCompression(file, options);
                    
                    // Create a new File object with the original filename
                    const originalExtension = file.name.split('.').pop();
                    const nameWithoutExtension = file.name.replace('.' + originalExtension, '');
                    const newFileName = nameWithoutExtension + '.webp';
                    
                    processedFile = new File([compressedBlob], newFileName, {
                        type: 'image/webp',
                        lastModified: Date.now()
                    });
                    
                    processedFile.originalName = file.name;
                    console.log('Compressed:', file.size / 1024 / 1024, 'MB to', processedFile.size / 1024 / 1024, 'MB');
                } catch (error) {
                    console.error('Compression error:', error);
                    this.showError('Failed to compress image. Using original file.');
                }
            } else if (this.config.clientResize && (this.config.maxWidth || this.config.maxHeight) && !window.imageCompression) {
                console.warn('Image compression library not loaded. Using original file.');
            }
            
            // Upload to server
            const formData = new FormData();
            formData.append('image', processedFile);
            formData.append('session_id', this.config.sessionId);
            
            const response = await fetch(this.config.uploadUrl, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Add size information to uploaded image
                const uploadedImage = {
                    ...result.image,
                    size: processedFile.size
                };
                this.uploadedImages.push(uploadedImage);
                this.showSuccess('Image uploaded successfully');
            } else {
                throw new Error(result.message || 'Upload failed');
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showError('Failed to upload image. Please try again.');
        }
    }
    
    async uploadFileImmediately(file) {
        try {
            let processedFile = file;
            
            // Compress if enabled
            if (this.config.clientResize && (this.config.maxWidth || this.config.maxHeight) && window.imageCompression) {
                try {
                    const options = {
                        maxSizeMB: Math.min(this.config.maxFilesize, 2),
                        maxWidthOrHeight: Math.max(this.config.maxWidth || 0, this.config.maxHeight || 0),
                        useWebWorker: true,
                        fileType: file.type,
                        initialQuality: 0.85
                    };
                    
                    const compressedBlob = await imageCompression(file, options);
                    
                    // Create a new File object with the original filename
                    const originalExtension = file.name.split('.').pop();
                    const nameWithoutExtension = file.name.replace('.' + originalExtension, '');
                    const newFileName = nameWithoutExtension + '.webp';
                    
                    processedFile = new File([compressedBlob], newFileName, {
                        type: 'image/webp',
                        lastModified: Date.now()
                    });
                    
                    processedFile.originalName = file.name;
                    console.log('Compressed:', file.size / 1024 / 1024, 'MB to', processedFile.size / 1024 / 1024, 'MB');
                } catch (error) {
                    console.error('Compression error:', error);
                    this.showError('Failed to compress image. Using original file.');
                }
            } else if (this.config.clientResize && (this.config.maxWidth || this.config.maxHeight) && !window.imageCompression) {
                console.warn('Image compression library not loaded. Using original file.');
            }
            
            // Upload immediately to server
            const formData = new FormData();
            formData.append('file', processedFile);
            
            if (this.config.contextType && this.config.contextId) {
                formData.append('context_type', this.config.contextType);
                formData.append('context_id', this.config.contextId);
            }
            
            if (this.config.sessionId) {
                formData.append('session_id', this.config.sessionId);
            }
            
            // Determine the upload URL
            let uploadUrl = this.config.uploadUrl;
            if (!uploadUrl) {
                if (this.config.contextType && this.config.contextId) {
                    uploadUrl = `/images/context/${this.config.contextType}/${this.config.contextId}`;
                } else if (this.config.sessionId) {
                    uploadUrl = `/images/session/${this.config.sessionId}`;
                } else {
                    uploadUrl = '/images';
                }
            }
            
            const response = await fetch(uploadUrl, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': formData.get('_token') || document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                },
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.uploadedImages.push({
                    id: result.image.id,
                    src: result.thumbnail_url,
                    fullSrc: result.full_url,
                    name: result.image.og_filename || file.name,
                    alt: result.image.title || file.name,
                    size: processedFile.size
                });
                this.showSuccess('Image uploaded successfully');
                
                // Dispatch custom event for external listeners
                this.element.dispatchEvent(new CustomEvent('image-uploaded', {
                    detail: { image: result.image, thumbnail_url: result.thumbnail_url }
                }));
            } else {
                throw new Error(result.message || 'Upload failed');
            }
        } catch (error) {
            console.error('Immediate upload error:', error);
            this.showError('Failed to upload image. Please try again.');
        }
    }
    
    async removeImage(imageId) {
        if (this.config.sessionBased && this.config.deleteUrl) {
            // Delete from server
            try {
                const response = await fetch(this.config.deleteUrl.replace('{id}', imageId) + '?session_id=' + this.config.sessionId, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.uploadedImages = this.uploadedImages.filter(img => img.id !== imageId);
                    this.showSuccess('Image removed');
                } else {
                    throw new Error(result.message || 'Delete failed');
                }
            } catch (error) {
                console.error('Delete error:', error);
                this.showError('Failed to remove image');
            }
        } else {
            // Remove from local queue
            this.files = this.files.filter(f => f.id !== imageId);
        }
        
        this.updateDisplay();
    }
    
    clearAll() {
        this.files = [];
        this.uploadedImages = [];
        this.updateDisplay();
        this.fileInput.value = '';
        
        // Clear session if session-based
        if (this.config.sessionBased) {
            // Could add server call to clear session
        }
    }
    
    updateDisplay() {
        const totalImages = this.files.length + this.uploadedImages.length;
        
        // Update count
        if (this.photoCount) {
            this.photoCount.textContent = `${totalImages} ${totalImages !== 1 ? 'images' : 'image'} selected`;
        }
        
        // Update total size
        if (this.totalSize) {
            const localBytes = this.files.reduce((sum, f) => sum + (f.size || 0), 0);
            const uploadedBytes = this.uploadedImages.reduce((sum, img) => sum + (img.size || 0), 0);
            const totalBytes = localBytes + uploadedBytes;
            this.totalSize.textContent = `Total size: ${this.formatFileSize(totalBytes)}`;
        }
        
        // Show/hide queue container (only if previews are enabled)
        if (this.config.showPreviews !== false && this.queueContainer) {
            if (totalImages > 0) {
                this.queueContainer.classList.remove('hidden');
                
                // Hide "Add More" button if maxFiles is 1 or we've reached the limit
                if (this.addMoreBtn) {
                    if (this.config.maxFiles === 1 || totalImages >= this.config.maxFiles) {
                        this.addMoreBtn.style.display = 'none';
                    } else {
                        this.addMoreBtn.style.display = '';
                    }
                }
            } else {
                this.queueContainer.classList.add('hidden');
            }
        } else if (this.queueContainer) {
            // Always hide queue container if previews are disabled
            this.queueContainer.classList.add('hidden');
        }
        
        // Update preview grid
        this.updatePreviewGrid();
        
        // Update file input for form submission
        if (!this.config.sessionBased) {
            this.updateFileInput();
        }
    }
    
    updatePreviewGrid() {
        this.previewContainer.innerHTML = '';
        
        // Add uploaded images (from server)
        this.uploadedImages.forEach(image => {
            const div = this.createPreviewElement(image);
            this.previewContainer.appendChild(div);
        });
        
        // Add local files
        this.files.forEach(fileObj => {
            const div = this.createPreviewElement(fileObj);
            this.previewContainer.appendChild(div);
        });
    }
    
    createPreviewElement(item) {
        const div = document.createElement('div');
        div.className = 'relative group';
        div.dataset.imageId = item.id;
        
        const imageSrc = item.src || (item.path ? `/storage/${item.path}` : '');
        const imageName = item.name || item.originalName || 'Image';
        
        div.innerHTML = `
            <div class="aspect-square bg-base-200 rounded-lg overflow-hidden">
                <img src="${imageSrc}" 
                     alt="${imageName}" 
                     class="w-full h-full object-cover hover:scale-105 transition-transform duration-200">
            </div>
            <button type="button" 
                    class="absolute top-1 right-1 btn btn-circle btn-xs btn-error opacity-0 group-hover:opacity-100 transition-opacity remove-image-btn"
                    data-image-id="${item.id}">
                <i class="fa-sharp fa-times text-xs"></i>
            </button>
        `;
        
        return div;
    }
    
    updateFileInput() {
        if (!this.fileInput || this.config.sessionBased) return;
        
        const dataTransfer = new DataTransfer();
        this.files.forEach(fileObj => {
            // Only add if it's a proper File object
            if (fileObj.file instanceof File) {
                dataTransfer.items.add(fileObj.file);
            }
        });
        this.fileInput.files = dataTransfer.files;
    }
    
    setProcessing(processing) {
        this.isProcessing = processing;
        
        if (processing) {
            this.progressIndicator?.classList.remove('hidden');
            this.dropzone.classList.add('opacity-50', 'pointer-events-none');
        } else {
            this.progressIndicator?.classList.add('hidden');
            this.dropzone.classList.remove('opacity-50', 'pointer-events-none');
        }
    }
    
    showError(message) {
        this.showMessage(message, 'error');
    }
    
    showSuccess(message) {
        this.showMessage(message, 'success');
    }
    
    showMessage(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} mt-2`;
        
        const iconMap = {
            'success': 'fa-check-circle',
            'error': 'fa-exclamation-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle'
        };
        
        alertDiv.innerHTML = `
            <i class="fa-sharp ${iconMap[type] || iconMap.info}"></i>
            <span>${message}</span>
        `;
        
        this.errorContainer.appendChild(alertDiv);
        
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
    
    setupFormSubmissionHandler() {
        // Find the parent form and add submission handler
        const form = this.element.closest('form');
        if (form && !this.config.sessionBased) {
            form.addEventListener('submit', (e) => {
                // If we have files, intercept the form submission and use FormData
                if (this.files.length > 0) {
                    e.preventDefault();
                    this.submitFormWithFiles(form);
                    return false;
                }
            });
        }
    }
    
    async submitFormWithFiles(form) {
        // Show processing state
        this.setProcessing(true);
        this.showMessage('Uploading images...', 'info');
        
        // Create FormData from the original form
        const formData = new FormData(form);
        
        // Remove any existing file entries
        formData.delete(this.config.name);
        
        // Add our processed files
        this.files.forEach((fileObj) => {
            formData.append(this.config.name, fileObj.file);
        });
        
        try {
            // Submit the form using fetch with AJAX headers
            const response = await fetch(form.action, {
                method: form.method || 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': formData.get('_token') || document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });
            
            if (response.ok) {
                try {
                    const data = await response.json();
                    if (data.success) {
                        // Reload the page to show new images
                        window.location.reload();
                    } else {
                        console.error('Upload failed:', data.message);
                        alert(data.message || 'Upload failed. Please try again.');
                    }
                } catch (e) {
                    // If not JSON, redirect to the response URL
                    window.location.href = response.url;
                }
            } else {
                const errorText = await response.text();
                console.error('Form submission failed:', response.status, errorText);
                alert('Upload failed. Please try again.');
            }
        } catch (error) {
            console.error('Form submission error:', error);
            alert('Upload failed. Please try again.');
        } finally {
            this.setProcessing(false);
        }
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Initialize all dropzones on page load
document.addEventListener('DOMContentLoaded', () => {
    const dropzones = document.querySelectorAll('.image-dropzone-component');
    dropzones.forEach(element => {
        new ImageDropzone(element);
    });
});

// Export for use in other scripts
window.ImageDropzone = ImageDropzone;