// ETRFlow Time Clock Service Worker
const CACHE_NAME = 'etrflow-timeclock-v2';
const OFFLINE_URL = '/time-clock';

// Files to cache for offline functionality
const urlsToCache = [
    '/time-clock',
    // Note: CSS and JS files are handled by Vite and have dynamic names
    // We'll cache them dynamically when they're requested
];

// Install event - cache resources
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Service Worker: Caching files');
                // Cache files individually to handle failures gracefully
                return Promise.allSettled(
                    urlsToCache.map(url =>
                        cache.add(url).catch(error => {
                            console.warn(`Failed to cache ${url}:`, error);
                            return null; // Continue with other files
                        })
                    )
                );
            })
            .then(() => {
                console.log('Service Worker: Installed');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Installation failed:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Service Worker: Deleting old cache', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker: Activated');
            return self.clients.claim();
        })
    );
});

// Fetch event - handle requests
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);

    // Handle time clock action requests specially
    if (url.pathname === '/time-clock/action' && request.method === 'POST') {
        event.respondWith(handleTimeClockAction(request));
        return;
    }

    // Handle API requests
    if (url.pathname.startsWith('/api/')) {
        event.respondWith(handleApiRequest(request));
        return;
    }

    // Handle navigation requests
    if (request.mode === 'navigate') {
        event.respondWith(handleNavigationRequest(request));
        return;
    }

    // Default caching strategy for other requests (CSS, JS, images, etc.)
    event.respondWith(
        caches.match(request)
            .then(response => {
                if (response) {
                    return response;
                }

                // Fetch and cache the resource
                return fetch(request)
                    .then(response => {
                        // Only cache successful responses for certain file types
                        if (response.ok && shouldCache(request.url)) {
                            const responseClone = response.clone();
                            caches.open(CACHE_NAME)
                                .then(cache => {
                                    cache.put(request, responseClone);
                                })
                                .catch(error => {
                                    console.warn('Failed to cache resource:', request.url, error);
                                });
                        }
                        return response;
                    })
                    .catch(error => {
                        console.warn('Failed to fetch resource:', request.url, error);
                        throw error;
                    });
            })
    );
});

// Helper function to determine if a resource should be cached
function shouldCache(url) {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;

    // Cache CSS, JS, images, and fonts
    return pathname.match(/\.(css|js|png|jpg|jpeg|gif|webp|svg|woff|woff2|ttf|eot)$/i) ||
           pathname.startsWith('/build/') || // Vite build assets
           pathname === '/time-clock';
}

// Handle time clock action requests
async function handleTimeClockAction(request) {
    try {
        // Clone the request to use it multiple times
        const clonedRequest = request.clone();
        
        // Try to make the request
        const response = await fetch(clonedRequest);
        
        // Always return the response, regardless of status
        // Let the application handle error responses
        return response;
        
    } catch (error) {
        // Network error occurred (not HTTP error)
        console.log('Service Worker: Network error for time clock action', error);
        
        // Check if we're actually offline
        if (!navigator.onLine) {
            console.log('Service Worker: Device is offline, queueing action for retry');
            
            try {
                // Store the failed request for retry when online
                const formData = await request.formData();
                const actionData = {
                    action: formData.get('action'),
                    notes: formData.get('notes'),
                    timestamp: Date.now(),
                    url: request.url,
                    method: request.method,
                    headers: Object.fromEntries(request.headers.entries())
                };
                
                // Store for retry
                await storeFailedRequest(actionData);
                
                // Return a response indicating the action was queued
                return new Response(JSON.stringify({
                    success: false,
                    message: 'Action queued for when connection is restored',
                    queued: true
                }), {
                    status: 202,
                    headers: { 'Content-Type': 'application/json' }
                });
            } catch (queueError) {
                console.error('Service Worker: Failed to queue action', queueError);
                // Return offline error response
                return new Response(JSON.stringify({
                    success: false,
                    message: 'No internet connection',
                    offline: true
                }), {
                    status: 503,
                    headers: { 'Content-Type': 'application/json' }
                });
            }
        } else {
            // We're online but network request failed
            // Return a network error response instead of throwing
            console.log('Service Worker: Online but network request failed');
            return new Response(JSON.stringify({
                success: false,
                message: 'Network error occurred. Please try again.',
                network_error: true
            }), {
                status: 503,
                headers: { 'Content-Type': 'application/json' }
            });
        }
    }
}

// Handle API requests
async function handleApiRequest(request) {
    try {
        const response = await fetch(request);
        
        // Cache successful API responses for short time
        if (response.ok && request.method === 'GET' && request.url.startsWith('http')) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, response.clone());
        }
        
        return response;
    } catch (error) {
        console.log('Service Worker: API request failed, trying cache', error);
        
        // Try to serve from cache
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline response
        return new Response(JSON.stringify({
            success: false,
            message: 'No internet connection',
            offline: true
        }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// Handle navigation requests
async function handleNavigationRequest(request) {
    try {
        const response = await fetch(request);
        return response;
    } catch (error) {
        console.log('Service Worker: Navigation failed, serving offline page', error);
        
        // Try to serve cached version
        const cachedResponse = await caches.match(OFFLINE_URL);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Fallback offline page
        return new Response(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>ETRFlow - Offline</title>
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    .offline-message { max-width: 400px; margin: 0 auto; }
                </style>
            </head>
            <body>
                <div class="offline-message">
                    <h1>You're Offline</h1>
                    <p>Please check your internet connection and try again.</p>
                    <button onclick="window.location.reload()">Retry</button>
                </div>
            </body>
            </html>
        `, {
            headers: { 'Content-Type': 'text/html' }
        });
    }
}

// Store failed requests for retry
async function storeFailedRequest(actionData) {
    try {
        // Simple localStorage fallback for failed requests
        const failedRequests = JSON.parse(localStorage.getItem('failedTimeClockRequests') || '[]');
        failedRequests.push(actionData);
        localStorage.setItem('failedTimeClockRequests', JSON.stringify(failedRequests));
    } catch (error) {
        console.error('Service Worker: Failed to store request for retry', error);
    }
}

// Listen for messages from the main thread
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'RETRY_FAILED_REQUESTS') {
        retryFailedRequests();
    }
});

// Retry failed requests when back online
async function retryFailedRequests() {
    try {
        const failedRequests = JSON.parse(localStorage.getItem('failedTimeClockRequests') || '[]');
        
        if (failedRequests.length === 0) {
            return;
        }
        
        console.log(`Service Worker: Retrying ${failedRequests.length} failed requests`);
        
        for (const requestData of failedRequests) {
            try {
                const formData = new FormData();
                formData.append('action', requestData.action);
                formData.append('notes', requestData.notes || '');
                
                // Include necessary headers from original request
                const headers = requestData.headers || {};
                const response = await fetch(requestData.url, {
                    method: requestData.method,
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        ...headers
                    },
                    credentials: 'same-origin'
                });
                
                if (response.ok) {
                    console.log('Service Worker: Successfully retried request', requestData.action);
                } else {
                    console.log('Service Worker: Retry failed', requestData.action, response.status);
                }
            } catch (error) {
                console.log('Service Worker: Retry error', requestData.action, error);
            }
        }
        
        // Clear the failed requests
        localStorage.removeItem('failedTimeClockRequests');
        
        // Notify the main thread
        self.clients.matchAll().then(clients => {
            clients.forEach(client => {
                client.postMessage({
                    type: 'RETRY_COMPLETED',
                    count: failedRequests.length
                });
            });
        });
        
    } catch (error) {
        console.error('Service Worker: Error retrying failed requests', error);
    }
}

// Handle online event
self.addEventListener('online', () => {
    console.log('Service Worker: Back online, retrying failed requests');
    retryFailedRequests();
});
