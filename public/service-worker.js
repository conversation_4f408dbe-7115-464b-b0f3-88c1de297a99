// Service Worker for Push Notifications
self.addEventListener('install', event => {
    self.skipWaiting();
});

self.addEventListener('activate', event => {
    event.waitUntil(clients.claim());
});

// Handle push notifications
self.addEventListener('push', event => {
    if (!event.data) return;

    const data = event.data.json();
    const options = {
        body: data.message || data.body,
        icon: '/img/icon-192x192.png',
        badge: '/img/etrflow-icon.webp',
        vibrate: [200, 100, 200],
        tag: data.tag || 'etrflow-notification',
        renotify: true,
        requireInteraction: data.urgency === 'critical' || data.urgency === 'high',
        data: {
            url: data.url || data.link_url,
            notificationId: data.id
        },
        actions: data.actions || []
    };

    // Add urgency-based styling
    if (data.urgency === 'critical') {
        options.requireInteraction = true;
        options.vibrate = [300, 200, 300, 200, 300];
    }

    event.waitUntil(
        self.registration.showNotification(data.title || 'ETRFlow Notification', options)
    );
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
    event.notification.close();

    const data = event.notification.data;
    if (!data || !data.url) return;

    event.waitUntil(
        clients.matchAll({ type: 'window', includeUncontrolled: true }).then(clientList => {
            // Check if there's already a window/tab open
            for (const client of clientList) {
                if (client.url.includes(self.location.origin) && 'focus' in client) {
                    client.navigate(data.url);
                    return client.focus();
                }
            }
            // If no window is open, open a new one
            if (clients.openWindow) {
                return clients.openWindow(data.url);
            }
        })
    );
});

// Handle notification close
self.addEventListener('notificationclose', event => {
    const data = event.notification.data;
    if (data && data.notificationId) {
        // Send analytics or mark as dismissed
        fetch('/api/notifications/dismissed', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                notification_id: data.notificationId
            })
        }).catch(error => console.error('Failed to mark notification as dismissed:', error));
    }
});

// Handle background sync for offline support
self.addEventListener('sync', event => {
    if (event.tag === 'sync-notifications') {
        event.waitUntil(syncNotifications());
    }
});

async function syncNotifications() {
    try {
        const response = await fetch('/api/notifications/pending');
        const notifications = await response.json();
        
        for (const notification of notifications) {
            await self.registration.showNotification(notification.title, {
                body: notification.message,
                tag: `notification-${notification.id}`,
                data: notification
            });
        }
    } catch (error) {
        console.error('Failed to sync notifications:', error);
    }
}