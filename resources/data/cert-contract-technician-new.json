{"title": "Technician Certification Agreement", "section": "Section 4: Technical Certification and Destruction Verification", "version": "1.0", "last_updated": "2023-11-15", "key_points": ["[SIGNATORY_NAME] certifies proper destruction of all drives", "Technician must verify serial numbers against client manifest", "If no manifest provided, must record serial numbers", "Total drives found: [TECHNICIAN_TOTAL_DRIVES_FOUND]", "This count serves as official and final count", "Devices need scannable barcode, serial number, or QR code", "Devices without serials documented by type/manufacturer/model", "All required protocols were followed during destruction"], "text": "<strong>Contractor Requirements:</strong> The certifying technician must verify all serial numbers against the client's manifest. If no manifest is provided, they must record the serial numbers. Verification during the destruction process is essential.<br><br><strong>Destruction Certification:</strong> Drives are certified during the destruction process to ensure proper and complete destruction.<br><br><strong>Drive Count and Disclaimer:</strong> The total number of drives found during the destruction process is <strong>[TECHNICIAN_TOTAL_DRIVES_FOUND]</strong>. This count serves as the official and final count, as agreed with the client in Section 1 of the Client Certification Agreement. Any discrepancies will be documented and addressed per established procedures.<br><br><strong>Device Serialization Protocol:</strong> Devices must have a scannable barcode, a legible serial number printed, or a QR code containing the serial number in order to be serialized. Some devices, such as USB flash drives, do not have serial numbers. Unless they have been previously serialized by the client with printed labels, E-Tech cannot record their serials. In cases where a serial cannot be identified, the device type, manufacturer (if known), and model number (if known) will be documented in lieu of a serial. The same certification rates apply for devices with no obtainable serial.<br><br>By signing below, <em>[SIGNATORY_NAME]</em> certifies that all drives have been properly destroyed and that all required protocols were followed.", "placeholders": {"SIGNATORY_NAME": "Will be replaced with the name of the person signing", "TECHNICIAN_TOTAL_DRIVES_FOUND": "Will be replaced with total drives found"}}