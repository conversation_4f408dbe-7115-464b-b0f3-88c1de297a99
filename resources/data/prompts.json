{"Laptops": {"title": "Manufacturer Model, CPU, XGB RAM, XGB SSD, Win11 Pro/Home  -- The CPU should be formatted in the title shortly, like i7-12700K, not Intel Core i7-12700K. Include the GPU in the title if it's Nvidia, eg Nvidia GTX 1050ti. Convert GB to TB is applicable. If it has touch screen, try to mention in title. If it has 4K screen, mention in title. The character limit is 80 for the title.", "description": "Create a short intro explaining the product and what it's good for. Do not overembellish. List the specifications and ports as unordered lists. Combine specs if appropriate, like make screen size and res the same item. Mention if it has a touch screen but don't mention if it doesn't. Screen size in inches. Note that all windows installs are activated and updated. Always mention the power supply is included. End saying items ship fast from Colorado Springs, CO or local warehouse pickup is available. Contact us with any shipping questions. Don't list the feature items as provided but use them for generating the intro text. Make it look and sound nice and professional.", "exclude": ["Serial Number", "Did you check BIOS?", "Is the new OS installed?", "<PERSON>ce Physically Opened", "Physical and Sellable Condition Review", "Is Windows Activated", "Update Process", "Battery Health", "Device Manager, HWiNFO", "PassMark Score", "Device Reset to OOBE", "Device Ready To Sell (INITIAL)", "Hardware Check"]}, "Desktops and AIOs": {"title": "Manufacturer Model, Form Factor, CPU, XGB RAM, XGB SSD, Win11 Pro/Home  -- The CPU should be formatted in the title shortly, like i7-12700K, not Intel Core i7-12700K. Only list GPU if it is a good discrete gpu. Try to keep the title under 80 characters. Convert GB to TB is applicable.", "description": "Create a short intro explaining the product and what it's good for. List the specifications and ports as unordered lists. Combine specs if appropriate. If it's an all in one mention what its screen size and resolution is. If the specs list a touch screen, include that data. If there is no mention of a touch screen in the specs, do not mention it. Mention that windows is activated with a digital license and updated. Power supply always included. End saying items ship fast from Colorado Springs, CO or local warehouse pickup is available. Exclude the shipping part if the item is a desktop or all in one. We only ship micro form factors, small form factor pcs, and mid towers. Always mention the power supply is included. Don't list the features section as is but use them for generating the intro text. Make it look and sound nice and professional. Note if it has integrated wifi or not. If it does not have wifi, clarify that an ethernet cable or wireless usb adapter must be used. If the device is an all-in-one, it is LOCAL PICKUP ONLY at our warehouse in Colorado Springs.", "exclude": ["Serial Number", "Did you check BIOS?", "Is the new OS installed?", "<PERSON>ce Physically Opened", "Physical and Sellable Condition Review", "Is Windows Activated?", "Update Process", "Device Manager, HWiNFO", "PassMark Score", "Device Reset to OOBE", "Device Ready To Sell (INITIAL)", "Battery Health", "Hardware Check"]}, "LOT Laptops": {"title": "Lot of QUANTITY Manufacturer Model Laptops, For Parts/Repair, Processor Info (if present), Memory Info (if present), ALL POST (if all post)", "description": "Include an intro explaining this is a lot of laptops, what they are, how many they are, etc. List details about if they POST, turn on, are bios locked, etc.  If they were all tested say they were all tested, not randomly tested. If they have RAM, say that RAM capacity may vary from 4 to 16GB, unless the exact capacities were mentioned. Put it in bold if they are bios locked, missing batteries, or chargers are not included. Mention if SSDs are included or not. They are being sold as-is for parts or repair. Mention that we are an electronics recycler, these likely came from a corporate environment, school, or other organization are in fair to good condition. Do NOT say the word refurbish or refurbishment in the listing. Since they are sold as is, there are no returns or exchanges available. If not enough details were provided (like CPU types, Memory capacity), put a heading at the start saying PROVIDE MORE DETAILS. Items ship 1-2 business days via UPS Ground.", "exclude": ["Random Tests Completed"]}, "LOT Desktops": {"title": "Lot of QUANTITY Manufacturer Model FormFactor PCs, For Parts/Repair, Processor Info (if present), Memory Info (if present), ALL POST (if all post)", "description": "Include an intro explaining this is a lot of PCs, what they are, how many they are, etc. List details about if they POST, turn on, are bios locked, etc.  If they were all tested say they were all tested, not randomly tested. If they have RAM, say that RAM capacity may vary from 4 to 16GB, unless it says they're all the same capacity, or all capacities were listed for each device. Clearly state if power supplies are included. Put it in bold if they are bios locked. Mention if they have SSDs or not. They are being sold as-is for parts or repair. Mention that we are an electronics recycler, these were sourced from a corporate environment or school. Do NOT say the word refurbish or refurbishment in the listing. Since they are sold as is, there are no returns or exchanges available. If it does not specify power supplies being included, make sure to note that power supplies / adapters are NOT included. If not enough details were provided (like CPU types, Memory capacity), put a heading at the start saying PROVIDE MORE DETAILS. Items ship 1-2 business days via UPS Ground.", "exclude": ["Random Tests Completed"]}, "Bulk Refurb Sales": {"title": "Create a title based on the specs described", "description": "Create a short intro explaining the product and what it's good for. List any provided specs and describe the condition of the item if provided. Mention that it's available for local pickup at our warehouse in Colorado Springs, or arrange shipment.", "exclude": []}, "Other Electronics": {"title": "Create a title based on the specs described", "description": "Create a short intro explaining the product and what it's good for. List any provided specs and describe the condition of the item if provided. Mention that it's available for local pickup at our warehouse in Colorado Springs, or arrange shipment.", "exclude": []}, "STUFF": {"title": "Create a title based on the specs described", "description": "Create a short intro explaining the product and what it's good for. List any provided specs and describe the condition of the item if provided. Mention that it's available for local pickup at our warehouse in Colorado Springs, or arrange shipment.", "exclude": []}}