<!-- Step 2: Contact Information -->
<div id="step-2" class="step-content hidden">
    <div class="text-center mb-6">
        <h2 class="text-2xl font-bold text-primary mb-2">Contact Information</h2>
        <p class="text-base-content/70">Let us know how to reach you about your pickup request.</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-user text-primary mr-2"></i>
                Contact Name *
            </legend>
            <input type="text" name="contact_name" class="input input-bordered w-full"
                   placeholder="Your full name" required>
            <div class="error-message text-error text-sm mt-1 hidden"></div>
        </fieldset>

        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-building text-primary mr-2"></i>
                Business Name
            </legend>
            <input type="text" name="business_name" class="input input-bordered w-full"
                   placeholder="Optional business name">
            <div class="error-message text-error text-sm mt-1 hidden"></div>
        </fieldset>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-envelope text-primary mr-2"></i>
                Email Address *
            </legend>
            <input type="email" name="email" class="input input-bordered w-full"
                   placeholder="<EMAIL>" required>
            <div class="error-message text-error text-sm mt-1 hidden"></div>
        </fieldset>

        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-phone text-primary mr-2"></i>
                Phone Number *
            </legend>
            <input type="tel" name="phone" class="input input-bordered w-full"
                   placeholder="(*************" required>
            <div class="error-message text-error text-sm mt-1 hidden"></div>
        </fieldset>
    </div>

    <!-- Step 2 Navigation -->
    <div class="flex justify-between mt-8">
        <button type="button" id="back-to-step-1" class="btn btn-outline btn-md">
            <i class="fa-sharp fa-arrow-left mr-2"></i>
            <span class="hidden sm:inline">Back to Welcome</span>
            <span class="sm:hidden">Back</span>
        </button>
        <button type="button" id="next-to-step-3" class="btn btn-primary btn-md">
            <span class="hidden sm:inline">Next: Select Pickup Time</span>
            <span class="sm:hidden">Next</span>
            <i class="fa-sharp fa-arrow-right ml-2"></i>
        </button>
    </div>
</div>
