<!-- Step 4: Pickup Details -->
<div id="step-4" class="step-content hidden">
    <div class="text-center mb-6">
        <h2 class="text-2xl font-bold text-primary mb-2">Pickup Details</h2>
        <p class="text-base-content/70">Tell us about your pickup location and items.</p>
    </div>

    <div class="space-y-8">
        <!-- Pickup Address -->
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-map-marker-alt text-primary mr-2"></i>
                Pickup Address *
            </legend>
            <x-google-maps-autocomplete-guest
                id="pickup_address"
                name="pickup_address"
                placeholder="Start typing to search for an address..."
                required="true"
                type="textarea"
                rows="3"
                :restrictions="['us', 'ca']"
                icon="fa-map-marker-alt"
                iconColor="text-primary"
            />
            <div class="text-sm text-base-content/60 mt-2">
                <i class="fa-sharp fa-info-circle mr-1"></i>
                Start typing to search for an address or business name
            </div>
        </fieldset>


                <!-- Accessibility Questions -->
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-universal-access text-primary mr-2"></i>
                Are the items easily accessible? *
            </legend>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <label class="card bg-green-50 hover:bg-green-100 border-2 border-green-200 hover:border-green-300 cursor-pointer transition-all duration-200 p-4">
                    <input type="radio" name="accessibility_level" value="easy" class="radio radio-success sr-only" required>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-green-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa-sharp fa-face-smile text-xl"></i>
                        </div>
                        <div class="font-bold text-green-800 mb-2">Easy Access</div>
                        <div class="text-sm text-green-700">A first level room, storage unit, garage, hallway, already outside</div>
                    </div>
                </label>
                <label class="card bg-orange-50 hover:bg-orange-100 border-2 border-orange-200 hover:border-orange-300 cursor-pointer transition-all duration-200 p-4">
                    <input type="radio" name="accessibility_level" value="moderate" class="radio radio-warning sr-only" required>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa-sharp fa-face-meh text-xl"></i>
                        </div>
                        <div class="font-bold text-orange-800 mb-2">Moderate Access</div>
                        <div class="text-sm text-orange-700">More than 4 steps, or other mild obstacle</div>
                    </div>
                </label>
                <label class="card bg-red-50 hover:bg-red-100 border-2 border-red-200 hover:border-red-300 cursor-pointer transition-all duration-200 p-4">
                    <input type="radio" name="accessibility_level" value="difficult" class="radio radio-error sr-only" required>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-red-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa-sharp fa-face-tired text-xl"></i>
                        </div>
                        <div class="font-bold text-red-800 mb-2">Difficult Access</div>
                        <div class="text-sm text-red-700">Full flight(s) of stairs, tight spaces, multiple obstacles</div>
                    </div>
                </label>
            </div>
            <div class="error-message text-error text-sm mt-1 hidden"></div>
            <div class="alert alert-info mt-4">
                <i class="fa-sharp fa-info-circle"></i>
                <div class="text-sm">
                    Additional fees will apply if pickup team has difficulty accessing items. Please be honest about accessibility to avoid extra charges.
                </div>
            </div>
        </fieldset>


        <!-- Property Location Details -->
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-home text-primary mr-2"></i>
                Where on the property are the items located? Are they easy to access? Any stairs? *
            </legend>
            <textarea name="property_location_details" class="textarea textarea-bordered h-32 w-full"
                      placeholder="Please describe the location (e.g., garage, basement, second floor, storage room, etc.)" required></textarea>
            <div class="error-message text-error text-sm mt-1 hidden"></div>
        </fieldset>



        <!-- Driver Instructions -->
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-clipboard-list text-primary mr-2"></i>
                What should our driver do on arrival? *
            </legend>
            <textarea name="driver_instructions" class="textarea textarea-bordered h-24 w-full"
                      placeholder="e.g., Call someone, ring the doorbell, enter security code, go to back entrance, etc." required></textarea>
            <div class="error-message text-error text-sm mt-1 hidden"></div>
        </fieldset>
    </div>

    <!-- Step 4 Navigation -->
    <div class="flex justify-between mt-8">
        <button type="button" id="back-to-step-3" class="btn btn-outline btn-md">
            <i class="fa-sharp fa-arrow-left mr-2"></i>
            <span class="hidden sm:inline">Back to Pickup Time</span>
            <span class="sm:hidden">Back</span>
        </button>
        <button type="button" id="continue-to-items" class="btn btn-primary btn-md">
            <span class="hidden sm:inline">Continue: What are we picking up?</span>
            <span class="sm:hidden">Continue</span>
            <i class="fa-sharp fa-arrow-right ml-2"></i>
        </button>
    </div>
</div>
