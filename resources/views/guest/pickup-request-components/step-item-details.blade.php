<!-- Step 5: Item <PERSON> -->
<div id="step-5" class="step-content hidden">
    <div class="text-center mb-6">
        <h2 class="text-2xl font-bold text-primary mb-2">What are we picking up?</h2>
        <p class="text-base-content/70">Tell us about the items you need us to collect.</p>
    </div>

    <!-- Item Details Message -->
    @php
        $itemDetailsMessage = \App\Models\HTMLField::getPickupItemDetailsMessage();
    @endphp
    @if($itemDetailsMessage)
        <div class="card  border border-base-300 mb-6">
            <div class="card-body prose prose-sm max-w-none">
                {!! $itemDetailsMessage !!}
            </div>
        </div>
    @endif

    <div class="space-y-8">
        <!-- Load Size Selection -->
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-truck-loading text-primary mr-2"></i>
                How much are we picking up? *
            </legend>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <label class="card bg-green-50 hover:bg-green-100 border-2 border-green-200 hover:border-green-300 cursor-pointer transition-all duration-200 p-4">
                    <input type="radio" name="load_size" value="small" class="radio radio-success sr-only" required>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa-sharp fa-solid fa-person text-2xl"></i>
                        </div>
                        <div class="font-bold text-green-800 mb-2">Small Load</div>
                        <div class="text-sm text-green-700 mb-3">1-2 TVs, computers, a few boxes</div>
                        <div class="badge bg-green-600 text-white badge-sm">Fits In Midsize SUV</div>
                    </div>
                </label>
                <label class="card bg-orange-50 hover:bg-orange-100 border-2 border-orange-200 hover:border-orange-300 cursor-pointer transition-all duration-200 p-4">
                    <input type="radio" name="load_size" value="medium" class="radio radio-warning sr-only" required>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa-sharp fa-solid fa-person-dolly text-2xl"></i>
                        </div>
                        <div class="font-bold text-orange-800 mb-2">Medium Load</div>
                        <div class="text-sm text-orange-700 mb-3">Pallets, totes, 1-person movable with equipment</div>
                        <div class="badge bg-orange-600 text-white badge-sm">Box Truck Required</div>
                    </div>
                </label>
                <label class="card bg-red-50 hover:bg-red-100 border-2 border-red-200 hover:border-red-300 cursor-pointer transition-all duration-200 p-4">
                    <input type="radio" name="load_size" value="large" class="radio radio-error sr-only" required>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa-sharp fa-solid fa-people-carry-box text-2xl"></i>
                        </div>
                        <div class="font-bold text-red-800 mb-2">Large/Heavy Load</div>
                        <div class="text-sm text-red-700 mb-3">2+ people likely needed, or specialized handling</div>
                        <div class="badge bg-red-600 text-white badge-sm">Box Truck Required</div>
                    </div>
                </label>
            </div>
            <div class="error-message text-error text-sm mt-1 hidden"></div>
        </fieldset>

        <!-- Item Types -->
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-boxes text-primary mr-2"></i>
                What types of items are we picking up? *
            </legend>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                @foreach($pickupItemTypes ?? [] as $itemType)
                    <div class="form-control">
                        <label class="item-type-button cursor-pointer p-3 border-2 border-base-300 rounded-lg hover:border-primary hover:bg-primary/5 transition-all duration-200 flex flex-col items-center text-center"
                               data-value="{{ $itemType['value'] }}"
                               data-notes="{{ $itemType['notes'] ?? '' }}">
                            <input type="checkbox" name="item_types[]" value="{{ $itemType['value'] }}" class="hidden item-type-checkbox">
                            <div class="relative">
                                <i class="{{ $itemType['icon'] ?? 'fa-sharp fa-question' }} text-2xl text-base-content mb-2"></i>
                                <div class="checkmark absolute -top-1 -right-1 w-5 h-5 bg-primary text-primary-content rounded-full items-center justify-center text-xs hidden">
                                    <i class="fa-sharp fa-check"></i>
                                </div>
                            </div>
                            <span class="label-text text-xs font-medium">{{ $itemType['name'] }}</span>
                        </label>
                    </div>
                @endforeach
            </div>

            <!-- Notes Display -->
            <div id="item-notes-display" class="mt-4 hidden">
                <div class="alert alert-info">
                    <i class="fa-sharp fa-info-circle"></i>
                    <div>
                        <strong>Important Information for Selected Items:</strong>
                        <ul id="item-notes-list" class="list-disc list-inside mt-2 space-y-1"></ul>
                    </div>
                </div>
            </div>

            <div class="error-message text-error text-sm mt-1 hidden"></div>
            <div class="text-sm text-base-content/60 mt-2">
                <i class="fa-sharp fa-info-circle mr-1"></i>
                Select all that apply. Click an item to see additional information.
            </div>
        </fieldset>

        <!-- Item Specifics -->
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-list-ul text-primary mr-2"></i>
                Additional Item Specifics *
            </legend>
            <textarea name="item_specifics" class="textarea textarea-bordered h-32 w-full"
                      placeholder="Please provide additional details about the items (e.g., swap out 4 totes of computers, one 55&quot; flatscreen, roughly 30 desktop PCs, etc.)" required></textarea>
            <div class="error-message text-error text-sm mt-1 hidden"></div>
        </fieldset>


        <!-- Image Upload -->
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-camera text-primary mr-2"></i>
                Upload Photos (Optional)
            </legend>
            
            <x-image-dropzone
                id="pickup-images"
                name="images[]"
                :max-files="10"
                :max-filesize="10"
                :max-width="1250"
                :max-height="1250"
                :client-resize="true"
                accepted-formats="image/jpeg,image/jpg,image/png,image/webp"
                :session-based="true"
                session-id="{{ session()->getId() . '_pickup' }}"
                upload-url="{{ route('guest.pickup-request.upload-image') }}"
                delete-url="/pickup-request/delete-image/{id}"
                :multiple="true"
                label=""
                help-text="Drop images here or click to upload"
            />
            
            <div class="text-sm text-base-content/60 mt-2">
                <i class="fa-sharp fa-info-circle mr-1"></i>
                Photos help us better understand your pickup needs and prepare accordingly.
            </div>
        </fieldset>
    </div>

    <!-- Step 5 Navigation -->
    <div class="flex justify-between mt-8">
        <button type="button" id="back-to-step-4" class="btn btn-outline btn-md">
            <i class="fa-sharp fa-arrow-left mr-2"></i>
            <span class="hidden sm:inline">Back to Pickup Details</span>
            <span class="sm:hidden">Back</span>
        </button>
        <button type="button" id="next-to-step-6" class="btn btn-primary btn-md">
            <span class="hidden sm:inline">Next: Terms & Agreement</span>
            <span class="sm:hidden">Next</span>
            <i class="fa-sharp fa-arrow-right ml-2"></i>
        </button>
    </div>
</div>
