<!-- Step 3: Pickup Time Selection -->
<div id="step-3" class="step-content hidden">
    <div class="text-center mb-6">
        <h2 class="text-2xl font-bold text-primary mb-2">Select Pickup Time</h2>
        <p class="text-base-content/70">Choose your preferred date and time for pickup.</p>
    </div>

    <fieldset class="fieldset">
        <legend class="fieldset-legend">
            <i class="fa-sharp fa-clock text-primary mr-2"></i>
            Choose your preferred pickup time *
        </legend>

        <!-- Date Selection View -->
        <div id="date-selection-view" class="bg-base-200 rounded-lg p-6">
            <div class="flex items-center justify-center py-8">
                <span class="loading loading-bars loading-sm text-primary"></span>
                <span class="ml-3 text-base-content">Loading available dates...</span>
            </div>
        </div>

        <!-- Time Selection View -->
        <div id="time-selection-view" class="bg-base-200 rounded-lg p-6 hidden">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold" id="selected-date-title">Select a Time</h3>
                <button type="button" id="back-to-dates-btn" class="btn btn-sm btn-ghost">
                    <i class="fa-sharp fa-arrow-left mr-1"></i>
                    Back to Dates
                </button>
            </div>
            <div id="time-slots-container">
                <!-- Time slots will be loaded here -->
            </div>
        </div>

        <div class="error-message text-error text-sm mt-1 hidden"></div>
        <div class="text-sm text-base-content/60 mt-2">
            <i class="fa-sharp fa-info-circle mr-1"></i>
            Select a date first, then choose your preferred time
        </div>
        <div id="selected-time-display" class="text-sm text-primary font-medium mt-2 hidden">
            <i class="fa-sharp fa-check mr-1"></i>
            Selected: <span id="selected-time-text"></span>
        </div>
    </fieldset>

    <!-- Hidden field for selected datetime -->
    <input type="hidden" name="preferred_pickup_date" id="preferred_pickup_date" required>

    <!-- Step 3 Navigation -->
    <div class="flex justify-between mt-8">
        <button type="button" id="back-to-step-2" class="btn btn-outline btn-md">
            <i class="fa-sharp fa-arrow-left mr-2"></i>
            <span class="hidden sm:inline">Back to Contact Info</span>
            <span class="sm:hidden">Back</span>
        </button>
        <button type="button" id="next-to-step-4" class="btn btn-primary btn-md" disabled>
            <span class="hidden sm:inline">Next: Pickup Details</span>
            <span class="sm:hidden">Next</span>
            <i class="fa-sharp fa-arrow-right ml-2"></i>
        </button>
    </div>
</div>
