<!-- Step 6: Agreement -->
<div id="step-6" class="step-content hidden">
    <div class="text-center mb-6">
        <h2 class="text-2xl font-bold text-primary mb-2">Terms & Agreement</h2>
        <p class="text-base-content/70">Please review and agree to our terms before submitting your pickup request.</p>
    </div>

    <!-- Agreement Message -->
    @php
        $agreementMessage = \App\Models\HTMLField::getPickupAgreementMessage();
    @endphp
    @if($agreementMessage)
        <div class="card bg-base-100 border border-base-300 mb-6">
            <div class="card-body prose prose-sm max-w-none">
                {!! $agreementMessage !!}
            </div>
        </div>
    @else
        <div class="card bg-base-100 border border-base-300 mb-6">
            <div class="card-body">
                <h3 class="card-title text-primary mb-4">
                    <i class="fa-sharp fa-handshake mr-2"></i>
                    Service Agreement
                </h3>
                <div class="prose prose-sm max-w-none">
                    <p>By submitting this pickup request, you acknowledge and agree to the following:</p>
                    <ul>
                        <li>You understand that there is a pickup fee of $75 or more</li>
                        <li>You agree to pay all associated weight-based item fees as listed in our fee schedule or standard residential rates</li>
                        <li>You confirm that all items are yours to dispose of</li>
                        <li>You understand that our team will contact you to confirm pickup details and schedule</li>
                    </ul>
                    <p>For complete pricing information, please visit our <a href="https://etechrecyclers.com/rates-and-pricing/" target="_blank" class="link link-primary">rates and pricing page</a>.</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Agreement Checkbox -->
    <fieldset class="fieldset">
        <div class="form-control">
            <label class="cursor-pointer justify-start gap-4">
                <input type="checkbox" id="terms_agreement" name="terms_agreement" class="checkbox checkbox-primary" required>
                <p class="label-text font-medium">
                    I have read and agree to the terms and conditions above
                </p>
            </label>
            <div class="error-message text-error text-sm mt-1 hidden"></div>
        </div>
    </fieldset>

    <!-- Step 6 Navigation -->
    <div class="flex justify-between mt-8">
        <button type="button" id="back-to-step-5" class="btn btn-outline btn-md">
            <i class="fa-sharp fa-arrow-left mr-2"></i>
            <span class="hidden sm:inline">Back to Item Details</span>
            <span class="sm:hidden">Back</span>
        </button>
        <button type="submit" class="btn btn-primary btn-md">
            <span class="submit-text">
                <i class="fa-sharp fa-paper-plane mr-2"></i>
                <span class="hidden sm:inline">Submit Pickup Request</span>
                <span class="sm:hidden">Submit</span>
            </span>
            <span class="submit-loading hidden">
                <span class="loading loading-bars loading-sm"></span>
                <span class="hidden sm:inline">Submitting...</span>
                <span class="sm:hidden">...</span>
            </span>
        </button>
    </div>
</div>