<x-guest-layout title="Request Pickup Service">
    <div class="min-h-screen bg-gradient-to-br from-base-200 to-base-300 flex items-center justify-center p-4">
        <div class="card w-full max-w-4xl shadow-2xl bg-base-100 border border-base-300">
            <div class="card-body">
                <!-- Header -->
                <div class="text-center mb-6">
                    <div class="avatar avatar-placeholder mb-4">
                        <div class="bg-primary text-primary-content w-16 rounded-xl">
                            <i class="fa-sharp fa-truck text-2xl"></i>
                        </div>
                    </div>
                    <h1 class="text-3xl font-bold text-base-content">Request Pickup Service</h1>
                    <p class="text-base-content/70 mt-2">Complete the steps below to schedule your pickup service.</p>
                </div>

                <!-- Steps Progress -->
                <div class="mb-8">
                    <!-- Mobile Step Counter -->
                    <div class="block sm:hidden text-center mb-4">
                        <div class="text-sm font-medium text-base-content/70">
                            Step <span id="mobile-current-step">1</span> of 6
                        </div>
                        <div class="w-full bg-base-300 rounded-full h-2 mt-2">
                            <div id="mobile-progress-bar" class="bg-primary h-2 rounded-full transition-all duration-300" style="width: 16.67%"></div>
                        </div>
                    </div>

                    <!-- Desktop/Tablet Step Indicators -->
                    <ul class="steps steps-horizontal w-full hidden sm:flex justify-center px-4">
                        <li class="step step-primary flex-1 max-w-32" id="step-1-indicator">
                            <div class="flex flex-col items-center text-center px-2 py-3">
                                <span class="font-medium text-sm">Welcome</span>
                                <span class="text-xs opacity-70 mt-1">Get started</span>
                            </div>
                        </li>
                        <li class="step flex-1 max-w-32" id="step-2-indicator">
                            <div class="flex flex-col items-center text-center px-2 py-3">
                                <span class="font-medium text-sm">Contact Info</span>
                                <span class="text-xs opacity-70 mt-1">Your details</span>
                            </div>
                        </li>
                        <li class="step flex-1 max-w-32" id="step-3-indicator">
                            <div class="flex flex-col items-center text-center px-2 py-3">
                                <span class="font-medium text-sm">Pickup Time</span>
                                <span class="text-xs opacity-70 mt-1">Schedule</span>
                            </div>
                        </li>
                        <li class="step flex-1 max-w-32" id="step-4-indicator">
                            <div class="flex flex-col items-center text-center px-2 py-3">
                                <span class="font-medium text-sm">Pickup Details</span>
                                <span class="text-xs opacity-70 mt-1">Location</span>
                            </div>
                        </li>
                        <li class="step flex-1 max-w-32" id="step-5-indicator">
                            <div class="flex flex-col items-center text-center px-2 py-3">
                                <span class="font-medium text-sm">Item Details</span>
                                <span class="text-xs opacity-70 mt-1">What we're getting</span>
                            </div>
                        </li>
                        <li class="step flex-1 max-w-32" id="step-6-indicator">
                            <div class="flex flex-col items-center text-center px-2 py-3">
                                <span class="font-medium text-sm">Agreement</span>
                                <span class="text-xs opacity-70 mt-1">Terms & submit</span>
                            </div>
                        </li>
                    </ul>
                </div>

                <!-- Success Message -->
                <div class="alert alert-success mb-6 hidden" id="success-message">
                    <i class="fa-sharp fa-check-circle"></i>
                    <span id="success-text"></span>
                </div>

                <!-- Multi-step Form -->
                <form id="pickup-request-form" action="{{ route('guest.pickup-request.store') }}" method="POST" class="space-y-8">
                    @csrf

                    @include('guest.pickup-request-components.step-welcome')
                    @include('guest.pickup-request-components.step-contact-info')
                    @include('guest.pickup-request-components.step-time-selection')
                    @include('guest.pickup-request-components.step-pickup-details')
                    @include('guest.pickup-request-components.step-item-details')
                    @include('guest.pickup-request-components.step-agreement')
                </form>

                <!-- Footer -->
                <div class="text-center mt-6 pt-6 border-t border-base-300">
                    <p class="text-base-content/60 text-sm">
                        <i class="fa-sharp fa-clock mr-1"></i>
                        We'll send you a confirmation email once your request is processed.
                    </p>
                    
                    @php
                        $siteLogo = \App\Models\GlobalConfig::getValue('site_logo_wide');
                    @endphp
                    @if($siteLogo)
                        <div class="mt-4">
                            <img src="{{ $siteLogo }}" alt="{{ config('app.name') }}" class="mx-auto max-h-12 opacity-70">
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Pickup Request JavaScript -->
    <script src="{{ asset('js/pickup-request.js') }}?v={{ time() }}"></script>
</x-guest-layout>
