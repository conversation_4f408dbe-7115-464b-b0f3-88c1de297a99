<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            Tax Policies
        </h2>
    </x-slot>

    <div class="py-12 bg-base-100">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-200 shadow-xl">
                <div class="card-body">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="card-title text-2xl text-primary">Manage Tax Policies</h3>
                        <a href="{{ route('tax_policies.create') }}" 
                            class="btn btn-primary">
                            <i class="fa-sharp fa-solid fa-plus mr-2"></i>
                            Create New Policy
                        </a>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="table table-zebra w-full">
                            <thead>
                                <tr class="bg-base-300">
                                    <th class="text-base-content">Name</th>
                                    <th class="text-base-content">Rate (%)</th>
                                    <th class="text-base-content">Description</th>
                                    <th class="text-right text-base-content">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($taxPolicies as $policy)
                                    <tr class="hover">
                                        <td class="font-medium">{{ $policy->name }}</td>
                                        <td>{{ $policy->rate }}</td>
                                        <td class="text-base-content/80">{{ $policy->description }}</td>
                                        <td class="text-right space-x-2">
                                            <a href="{{ route('tax_policies.edit', $policy) }}" 
                                               class="btn btn-sm btn-ghost">
                                                <i class="fa-sharp fa-solid fa-pen-to-square text-warning"></i>
                                            </a>
                                            <form action="{{ route('tax_policies.destroy', $policy) }}" method="POST" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-ghost"
                                                    onclick="return confirm('Are you sure you want to delete this policy?');">
                                                    <i class="fa-sharp fa-solid fa-trash text-error"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center py-8">
                                            <div class="flex flex-col items-center text-base-content/70">
                                                <i class="fa-sharp fa-solid fa-clipboard-list text-3xl mb-2"></i>
                                                <p>No tax policies found.</p>
                                                <a href="{{ route('tax_policies.create') }}" class="link link-primary mt-2">Create your first policy</a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
