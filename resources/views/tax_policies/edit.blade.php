<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            Edit Tax Policy
        </h2>
    </x-slot>

    <div class="py-12 bg-base-100">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-200 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title text-2xl text-primary mb-6">Edit Tax Policy</h2>
                    
                    <form action="{{ route('tax_policies.update', $taxPolicy) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Name Field -->
                        <div class="form-control mb-6">
                            <label for="name" class="label">
                                <span class="label-text font-medium">Name</span>
                            </label>
                            <input type="text" name="name" id="name" 
                                class="input input-bordered w-full"
                                value="{{ old('name', $taxPolicy->name) }}" required>
                            @error('name')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Rate Field -->
                        <div class="form-control mb-4">
                            <label for="rate" class="label">
                                <span class="label-text font-medium">Rate (%)</span>
                            </label>
                            <input type="number" name="rate" id="rate" step="0.01" 
                                class="input input-bordered w-full"
                                value="{{ old('rate', $taxPolicy->rate) }}" disabled>
                        </div>
                        <div class="alert alert-info mb-6">
                            <i class="fa-sharp fa-solid fa-circle-info"></i>
                            <span>Tax rates cannot be changed after creation. To update a tax rate, create a new tax rate for new sales.</span>
                        </div>

                        <!-- Description Field -->
                        <div class="form-control mb-6">
                            <label for="description" class="label">
                                <span class="label-text font-medium">Description</span>
                            </label>
                            <textarea name="description" id="description" rows="4" 
                                class="textarea textarea-bordered w-full"
                                >{{ old('description', $taxPolicy->description) }}</textarea>
                            @error('description')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <div class="card-actions justify-end">
                            <a href="{{ route('tax_policies.index') }}" class="btn btn-ghost">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fa-sharp fa-solid fa-save mr-2"></i>
                                Update Policy
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
