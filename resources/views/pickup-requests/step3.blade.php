<x-app-layout
    page-title="Step 3: Event Details"
    page-icon="fa-sharp fa-clipboard-list"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Pickup Requests', 'route' => 'pickup-requests.index', 'icon' => 'fa-truck-pickup'],
        ['name' => 'Request #' . $pickupRequest->id, 'route' => 'pickup-requests.show', 'params' => [$pickupRequest], 'icon' => 'fa-file-lines'],
        ['name' => 'Schedule', 'icon' => 'fa-calendar-clock']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Progress Steps -->
            <div class="card bg-base-100 shadow-md">
                <div class="p-6">
                    <ul class="steps steps-horizontal w-full">
                        <li class="step step-primary">Step 1: Customer</li>
                        <li class="step step-primary">Step 2: Time</li>
                        <li class="step step-primary">Step 3: Details</li>
                    </ul>
                </div>
            </div>

            <!-- Selected Time Information -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-success text-success-content w-8 rounded-lg">
                                <i class="fa-sharp fa-check-circle text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Selected Pickup Time</h4>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-calendar text-primary"></i>
                            <span class="font-medium">Date:</span>
                            <span>{{ \Carbon\Carbon::parse($scheduleData['pickup_date'])->format('l, F j, Y') }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-clock text-secondary"></i>
                            <span class="font-medium">Time:</span>
                            <span>{{ \Carbon\Carbon::parse($scheduleData['pickup_time'])->format('g:i A') }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user text-accent"></i>
                            <span class="font-medium">Customer:</span>
                            <span>{{ $pickupRequest->customer->name }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Event Details Form -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-clipboard-list text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Step 3: Event Details</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    <form action="{{ route('pickup-requests.saveStep3', $pickupRequest) }}" method="POST" class="space-y-6">
                        @csrf

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Staff Needed -->
                            <div>
                                <label class="label" for="staff_needed">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-users text-primary mr-2"></i>
                                        Staff Needed <span class="text-error font-bold">*</span>
                                    </span>
                                </label>
                                <input type="number"
                                       name="staff_needed"
                                       id="staff_needed"
                                       class="input input-bordered w-full"
                                       min="1"
                                       max="10"
                                       value="{{ old('staff_needed', 1) }}"
                                       placeholder="Number of staff members"
                                       required>
                                <div class="label">
                                    <span class="label-text-alt text-base-content/60">
                                        How many staff members are needed for this pickup?
                                    </span>
                                </div>
                                @error('staff_needed')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>

                            <!-- Assigned Driver -->
                            <div>
                                <label class="label" for="assigned_driver_id">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-user-tie text-primary mr-2"></i>
                                        Assigned Driver
                                    </span>
                                </label>
                                <select name="assigned_driver_id" id="assigned_driver_id" class="select select-bordered w-full">
                                    <option value="">Select a driver (optional)</option>
                                    @foreach($availableDrivers as $driver)
                                        <option value="{{ $driver->id }}" {{ old('assigned_driver_id') == $driver->id ? 'selected' : '' }}>
                                            {{ $driver->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <div class="label">
                                    <span class="label-text-alt text-base-content/60">
                                        Optional: Assign a specific driver to this pickup
                                    </span>
                                </div>
                                @error('assigned_driver_id')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>
                        </div>

                        <!-- Internal Pickup Notes -->
                        <div>
                            <label class="label" for="internal_pickup_notes">
                                <span class="label-text">
                                    <i class="fa-sharp fa-sticky-note text-primary mr-2"></i>
                                    Internal Pickup Notes
                                </span>
                            </label>
                            <textarea name="internal_pickup_notes"
                                      id="internal_pickup_notes"
                                      class="textarea textarea-bordered w-full h-24"
                                      placeholder="Add internal notes about this pickup (e.g., special instructions, access codes, staff reminders)..."
                                      maxlength="1000">{{ old('internal_pickup_notes') }}</textarea>
                            <div class="label">
                                <span class="label-text-alt text-base-content/60">
                                    Internal notes only visible to staff
                                </span>
                                <span class="label-text-alt text-base-content/60">
                                    <span id="notesCounter">0</span>/1000 characters
                                </span>
                            </div>
                            @error('internal_pickup_notes')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>

                        <!-- Customer Information Summary -->
                        <div class="bg-base-200/50 rounded-lg p-4">
                            <h5 class="font-semibold text-base-content mb-3 flex items-center gap-2">
                                <i class="fa-sharp fa-info-circle text-info"></i>
                                Customer Information Summary
                            </h5>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="font-medium text-base-content/70">Contact:</span>
                                    <p>{{ $pickupRequest->contact_name }}</p>
                                    <p>{{ $pickupRequest->phone }}</p>
                                    <p>{{ $pickupRequest->email }}</p>
                                </div>
                                <div>
                                    <span class="font-medium text-base-content/70">Pickup Details:</span>
                                    <p><strong>Items:</strong> {{ $pickupRequest->pickup_items }}</p>
                                    <p><strong>Quantity:</strong> {{ $pickupRequest->pickup_quantity }}</p>
                                    @if($pickupRequest->business_name)
                                        <p><strong>Business:</strong> {{ $pickupRequest->business_name }}</p>
                                    @endif
                                </div>
                            </div>
                            @if($pickupRequest->pickup_address)
                                <div class="mt-3">
                                    <span class="font-medium text-base-content/70">Address:</span>
                                    <p>{{ $pickupRequest->pickup_address }}</p>
                                </div>
                            @endif
                            @if($pickupRequest->property_location_details)
                                <div class="mt-3">
                                    <span class="font-medium text-base-content/70">Location Details:</span>
                                    <p>{{ $pickupRequest->property_location_details }}</p>
                                </div>
                            @endif
                            @if($pickupRequest->other_notes)
                                <div class="mt-3">
                                    <span class="font-medium text-base-content/70">Customer Notes:</span>
                                    <p>{{ $pickupRequest->other_notes }}</p>
                                </div>
                            @endif
                        </div>

                        <!-- Email Confirmation Option -->
                        <div class="bg-info/10 rounded-lg p-4 border border-info/20">
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="checkbox" 
                                           name="send_confirmation_email" 
                                           class="checkbox checkbox-info"
                                           value="1"
                                           checked>
                                    <div class="flex items-center gap-2">
                                        <i class="fa-sharp fa-envelope text-info"></i>
                                        <span class="label-text font-medium">
                                            Automatically Send Pickup Details to Customer
                                        </span>
                                    </div>
                                </label>
                                <div class="label">
                                    <span class="label-text-alt text-base-content/60 ml-8">
                                        Send an email to the customer with pickup details and confirmation options
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation -->
                        <div class="divider my-8">
                            <span class="text-base-content/50 font-medium">Create pickup event</span>
                        </div>

                        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                            <a href="{{ route('pickup-requests.step2', $pickupRequest) }}" class="btn btn-ghost gap-2">
                                <i class="fa-sharp fa-arrow-left"></i>
                                Back to Step 2
                            </a>

                            <button type="submit" class="btn btn-success btn-lg gap-2 shadow-lg w-full sm:w-auto">
                                <i class="fa-sharp fa-calendar-plus"></i>
                                Create Pickup Event
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Character counter for notes
            const notesTextarea = document.getElementById('internal_pickup_notes');
            const notesCounter = document.getElementById('notesCounter');
            
            if (notesTextarea && notesCounter) {
                function updateCounter() {
                    const length = notesTextarea.value.length;
                    notesCounter.textContent = length;
                    
                    // Change color based on length
                    if (length > 900) {
                        notesCounter.className = 'text-error';
                    } else if (length > 750) {
                        notesCounter.className = 'text-warning';
                    } else {
                        notesCounter.className = 'text-base-content/60';
                    }
                }
                
                // Update counter on input
                notesTextarea.addEventListener('input', updateCounter);
                
                // Initialize counter
                updateCounter();
            }
        });
    </script>
</x-app-layout>
