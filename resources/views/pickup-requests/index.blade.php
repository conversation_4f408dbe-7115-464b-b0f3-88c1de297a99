@php
    // Build action buttons array based on pickup request status and permissions
    $actionButtons = [];

    if (auth()->user()->hasPermission('view_pickup_requests')) {
        // View Pickup Calendar Route
        $actionButtons[] = [
            'name' => 'Pickup Calendar',
            'route' => route('pickup.calendar'),
            'icon' => 'fa-sharp fa-calendar',
            'class' => 'btn btn-secondary btn-xs lg:btn-sm gap-1 lg:gap-2',
            'permission' => 'view_pickup_requests'
        ];
    }

    if (auth()->user()->hasPermission('manage_pickup_requests')) {
        // Create Pickup Request Route
        $actionButtons[] = [
            'name' => 'Create Request',
            'route' => route('pickup-requests.create'),
            'icon' => 'fa-sharp fa-plus',
            'class' => 'btn btn-primary btn-xs lg:btn-sm gap-2 shadow-lg',
            'permission' => 'manage_pickup_requests'
        ];
    }

@endphp


<x-app-layout
    page-title="Pickup Requests"
    page-icon="fa-sharp fa-clipboard-list"
    :action-buttons="$actionButtons"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Pickup Requests', 'route' => 'pickup-requests.index', 'icon' => 'fa-truck-pickup'],
    ]"
    >

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto sm:px-2 lg:px-4 space-y-6">
            <!-- Header Card -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h1 class="text-2xl font-bold text-base-content">Pickup Requests</h1>
                            <p class="text-base-content/70 mt-1">Manage customer pickup requests and convert them to scheduled events</p>
                        </div>
                        <div class="flex items-center gap-4">
                            @perms('view_pickup_requests')
                                <!-- Daily PDF Generation Button -->
                                <div class="dropdown dropdown-end">
                                    <div tabindex="0" role="button" class="btn btn-outline btn-info gap-2 shadow-lg">
                                        <i class="fa-sharp fa-file-pdf"></i>
                                        Daily PDF
                                        <i class="fa-sharp fa-chevron-down text-xs"></i>
                                    </div>
                                    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-64 p-2 shadow-xl border border-base-200">
                                        <li class="menu-title">
                                            <span>Generate Daily Pickup PDF</span>
                                        </li>
                                        <li>
                                            <a href="{{ route('pickup-requests.daily-pdf', ['date' => now()->format('Y-m-d')]) }}" target="_blank" class="gap-2">
                                                <i class="fa-sharp fa-calendar-day text-info"></i>
                                                Today ({{ now()->format('M j') }})
                                                <small class="text-xs opacity-70">Includes pending</small>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="{{ route('pickup-requests.daily-pdf', ['date' => now()->addDay()->format('Y-m-d')]) }}" target="_blank" class="gap-2">
                                                <i class="fa-sharp fa-calendar-plus text-warning"></i>
                                                Tomorrow ({{ now()->addDay()->format('M j') }})
                                            </a>
                                        </li>
                                        <li class="divider"></li>
                                        <li>
                                            <a href="{{ route('pickup-requests.daily-pdf', ['date' => now()->format('Y-m-d'), 'include_upcoming_unconfirmed' => 'true']) }}" target="_blank" class="gap-2">
                                                <i class="fa-sharp fa-exclamation-triangle text-warning"></i>
                                                Today + Upcoming Unconfirmed
                                            </a>
                                        </li>
                                        <li class="divider"></li>
                                        <li>
                                            <a href="#" onclick="openDatePicker()" class="gap-2">
                                                <i class="fa-sharp fa-calendar-range text-primary"></i>
                                                Custom Date...
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            @endperms

                            <div class="hidden lg:flex items-center gap-4 text-base-content/60">
                                <div class="stat">
                                    <div class="stat-title text-xs">Total Active</div>
                                    <div class="stat-value text-xl">{{ $statusCounts['incoming'] + $statusCounts['pending'] + $statusCounts['confirmed'] }}</div>
                                </div>
                                <div class="stat">
                                    <div class="stat-title text-xs">Total Today</div>
                                    <div class="stat-value text-xl">{{ $todayCount }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search & Filter Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-4 py-3 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-7 rounded-lg">
                                <i class="fa-sharp fa-magnifying-glass text-xs"></i>
                            </div>
                        </div>
                        <h4 class="text-base font-semibold text-base-content">Search & Filter</h4>
                    </div>
                </div>
                <div class="p-4 space-y-4">
                    <!-- Search Form -->
                    <form method="GET" action="{{ route('pickup-requests.index') }}" class="flex gap-3">
                        <input type="hidden" name="status" value="{{ request('status') }}">
                        <div class="flex-1">
                            <input type="text"
                                   name="search"
                                   value="{{ request('search') }}"
                                   placeholder="Search by name, email, phone, or address..."
                                   class="input input-bordered w-full">
                        </div>
                        <button type="submit" class="btn btn-primary gap-2">
                            <i class="fa-sharp fa-magnifying-glass"></i>
                            Search
                        </button>
                        @if(request('search'))
                            <a href="{{ route('pickup-requests.index', ['status' => request('status')]) }}"
                               class="btn btn-outline gap-2">
                                <i class="fa-sharp fa-times"></i>
                                Clear
                            </a>
                        @endif
                    </form>

                    <!-- Status Filter Badges -->
                    <div>
                        <label class="text-sm font-medium text-base-content/70 mb-2 block">Filter by Status:</label>
                        <div class="flex flex-wrap gap-2">
                            <a href="{{ route('pickup-requests.index') }}"
                               class="badge {{ !request('status') ? 'badge-primary' : 'badge-outline' }} badge-lg">
                                Active ({{ $statusCounts['incoming'] + $statusCounts['pending'] + $statusCounts['confirmed'] }})
                            </a>
                            <a href="{{ route('pickup-requests.index', ['status' => 'incoming']) }}"
                               class="badge {{ request('status') === 'incoming' ? 'badge-warning' : 'badge-outline' }} badge-lg">
                                Incoming ({{ $statusCounts['incoming'] }})
                            </a>
                            <a href="{{ route('pickup-requests.index', ['status' => 'pending']) }}"
                               class="badge {{ request('status') === 'pending' ? 'badge-info' : 'badge-outline' }} badge-lg">
                                Pending ({{ $statusCounts['pending'] }})
                            </a>
                            <a href="{{ route('pickup-requests.index', ['status' => 'confirmed']) }}"
                               class="badge {{ request('status') === 'confirmed' ? 'badge-success' : 'badge-outline' }} badge-lg">
                                Confirmed ({{ $statusCounts['confirmed'] }})
                            </a>
                            <a href="{{ route('pickup-requests.index', ['status' => 'completed']) }}"
                               class="badge {{ request('status') === 'completed' ? 'badge-neutral' : 'badge-outline' }} badge-lg">
                                Completed ({{ $statusCounts['completed'] }})
                            </a>
                            <a href="{{ route('pickup-requests.index', ['status' => 'cancelled']) }}"
                               class="badge {{ request('status') === 'cancelled' ? 'badge-error' : 'badge-outline' }} badge-lg">
                                Cancelled ({{ $statusCounts['cancelled'] }})
                            </a>
                        </div>
                    </div>

                    <!-- Date Filter Options -->
                    <div>
                        <label class="text-sm font-medium text-base-content/70 mb-2 block">Date Filter:</label>
                        <div class="flex flex-wrap gap-2">
                            @php
                                $currentParams = request()->except('show_past');
                                $showPastParams = array_merge($currentParams, ['show_past' => 'true']);
                            @endphp
                            @if(!request('show_past'))
                                <span class="badge badge-primary badge-lg">Today & Future</span>
                                <a href="{{ route('pickup-requests.index', $showPastParams) }}"
                                   class="badge badge-outline badge-lg">
                                    <i class="fa-sharp fa-history mr-1"></i>
                                    Include Past
                                </a>
                            @else
                                <a href="{{ route('pickup-requests.index', $currentParams) }}"
                                   class="badge badge-outline badge-lg">
                                    Today & Future
                                </a>
                                <span class="badge badge-primary badge-lg">
                                    <i class="fa-sharp fa-history mr-1"></i>
                                    All Dates
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pickup Requests List -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-4 py-3 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-accent text-accent-content w-7 rounded-lg">
                                <i class="fa-sharp fa-clipboard-list text-xs"></i>
                            </div>
                        </div>
                        <h4 class="text-base font-semibold text-base-content">Pickup Requests</h4>
                    </div>
                </div>
                @if($pickupRequests->count() > 0)
                    <!-- Sorting Controls -->
                    <div class="px-4 py-3 border-b border-base-300/50 bg-base-50">
                        <form method="GET" class="flex flex-wrap items-center gap-4">
                            <input type="hidden" name="status" value="{{ request('status') }}">
                            <input type="hidden" name="search" value="{{ request('search') }}">
                            <input type="hidden" name="show_past" value="{{ request('show_past') }}">
                            <div class="flex items-center gap-2">
                                <label class="text-sm font-medium text-base-content/70">Sort by:</label>
                                <select name="sort" class="select select-bordered select-sm w-auto min-w-32" onchange="this.form.submit()">
                                    <option value="preferred_pickup_date" {{ request('sort', 'preferred_pickup_date') === 'preferred_pickup_date' ? 'selected' : '' }}>Pickup Date</option>
                                    <option value="contact_name" {{ request('sort') === 'contact_name' ? 'selected' : '' }}>Contact Name</option>
                                    <option value="submitted_at" {{ request('sort') === 'submitted_at' ? 'selected' : '' }}>Submitted Date</option>
                                    <option value="id" {{ request('sort') === 'id' ? 'selected' : '' }}>ID</option>
                                    <option value="status" {{ request('sort') === 'status' ? 'selected' : '' }}>Status</option>
                                </select>
                            </div>
                        </form>
                    </div>

                    <!-- Desktop Table Headers -->
                    <div class="hidden lg:block px-4 py-3 border-b border-base-300/50 bg-base-100">
                        <div class="grid grid-cols-12 gap-3 text-sm font-medium text-base-content/70">
                            <div class="col-span-3">Contact Info</div>
                            <div class="col-span-4">Pickup Details</div>
                            <div class="col-span-2">Pickup Date</div>
                            <div class="col-span-2">Customer</div>
                            <div class="col-span-1 text-center">Submitted</div>
                        </div>
                    </div>

                    <!-- Data Cards -->
                    <div class="divide-y divide-base-200/50">
                        @foreach($pickupRequests as $request)
                            @php
                                $statusClasses = [
                                    'incoming' => 'badge-warning',
                                    'pending' => 'badge-info',
                                    'confirmed' => 'badge-success',
                                    'completed' => 'badge-neutral',
                                    'cancelled' => 'badge-error'
                                ];
                                $statusLabels = [
                                    'incoming' => 'Incoming',
                                    'pending' => 'Pending',
                                    'confirmed' => 'Confirmed',
                                    'completed' => 'Completed',
                                    'cancelled' => 'Cancelled'
                                ];

                                // Check if pickup is today or tomorrow
                                $timezone = \App\Models\GlobalConfig::getTimeZone();
                                $today = now()->setTimezone($timezone)->startOfDay();
                                $tomorrow = $today->copy()->addDay();

                                $pickupDate = null;
                                $isToday = false;
                                $isTomorrow = false;

                                if ($request->preferred_pickup_date) {
                                    $pickupDate = $request->preferred_pickup_date->setTimezone($timezone)->startOfDay();
                                    $isToday = $pickupDate->equalTo($today);
                                    $isTomorrow = $pickupDate->equalTo($tomorrow);
                                } elseif ($request->event && $request->event->start_date) {
                                    $pickupDate = $request->event->start_date->setTimezone($timezone)->startOfDay();
                                    $isToday = $pickupDate->equalTo($today);
                                    $isTomorrow = $pickupDate->equalTo($tomorrow);
                                }
                            @endphp
                            <div class="p-3 hover:bg-base-200/50 transition-all duration-200 {{ $loop->even ? 'bg-base-100' : 'bg-base-200/30' }}">
                                <!-- Mobile Layout -->
                                <div class="lg:hidden">
                                    <!-- Prominent Header with Request Number and Customer Name -->
                                    <div class="mb-3">
                                        <a href="{{ route('pickup-requests.show', $request) }}" class="block hover:bg-base-200/30 -mx-1 px-1 py-1 rounded transition-colors">
                                            <div class="flex items-center justify-between gap-2 mb-1">
                                                <div class="flex items-center gap-2">
                                                    <div class="text-lg font-bold text-primary">Request #{{ $request->id }}</div>
                                                    @if($isToday)
                                                        <div class="badge badge-accent badge-sm">Today</div>
                                                    @elseif($isTomorrow)
                                                        <div class="badge badge-secondary badge-sm">Tomorrow</div>
                                                    @endif
                                                </div>
                                                <div class="badge {{ $statusClasses[$request->status] ?? 'badge-outline' }} badge-lg">
                                                    {{ $statusLabels[$request->status] ?? ucfirst($request->status) }}
                                                </div>
                                            </div>
                                            <div class="text-xl font-semibold text-base-content">
                                                {{ $request->business_name ?: $request->contact_name }}
                                            </div>
                                            @if($request->business_name)
                                                <div class="text-base text-base-content/70">{{ $request->contact_name }}</div>
                                            @endif
                                        </a>
                                    </div>
                                    <!-- Compact Details Grid -->
                                    <div class="grid grid-cols-2 gap-2 text-xs">
                                        <div class="space-y-1">
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-envelope text-primary"></i>
                                                <span class="text-base-content/70">{{ Str::limit($request->email, 18) }}</span>
                                            </div>
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-phone text-primary"></i>
                                                <span class="text-base-content/70">{{ $request->phone }}</span>
                                            </div>
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-calendar text-primary"></i>
                                                <span class="text-base-content/70">
                                                    Submitted: {{ $request->submitted_at ? $request->submitted_at->format('M j, Y') : $request->created_at->format('M j, Y') }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="space-y-1">
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-box text-primary"></i>
                                                <span class="text-base-content/70">{{ Str::limit($request->pickup_items, 14) }}</span>
                                            </div>
   
                                            @if($request->customer)
                                                <div class="flex items-center gap-1">
                                                    <i class="fa-sharp fa-user text-success"></i>
                                                    <span class="text-success text-xs">Linked</span>
                                                </div>
                                            @else
                                                <div class="flex items-center gap-1">
                                                    <i class="fa-sharp fa-user-slash text-warning"></i>
                                                    <span class="text-warning text-xs">Not linked</span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>

                                    @perms('manage_pickup_requests')
                                        @if($request->status === 'incoming' && !$request->customer_id)
                                            <div class="mt-2">
                                                <div class="badge badge-warning badge-lg">Needs Customer Assignment</div>
                                            </div>
                                        @elseif($request->status === 'incoming' && $request->customer_id)
                                            <div class="mt-2">
                                                <div class="badge badge-info badge-lg">Ready to Schedule</div>
                                            </div>
                                        @elseif($request->status === 'pending')
                                            <div class="mt-2">
                                                <div class="badge badge-info badge-lg">Awaiting Customer Confirmation</div>
                                            </div>
                                        @endif
                                    @endperms
                                </div>

                                <!-- Desktop Layout -->
                                <div class="hidden lg:block">
                                    <!-- Prominent Header Row -->
                                    <div class="mb-2">
                                        <a href="{{ route('pickup-requests.show', $request) }}" class="block hover:bg-base-200/30 -mx-2 px-2 py-1 rounded transition-colors">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center gap-4">
                                                    <div class="flex items-center gap-2">
                                                        <div class="text-lg font-bold text-primary">Request #{{ $request->id }}</div>
                                                        @if($isToday)
                                                            <div class="badge badge-accent badge-sm">Today</div>
                                                        @elseif($isTomorrow)
                                                            <div class="badge badge-secondary badge-sm">Tomorrow</div>
                                                        @endif
                                                    </div>
                                                    <div class="text-lg font-semibold text-base-content">
                                                        {{ $request->business_name ?: $request->contact_name }}
                                                    </div>
                                                    @if($request->business_name)
                                                        <div class="text-sm text-base-content/70">{{ $request->contact_name }}</div>
                                                    @endif
                                                </div>
                                                <div class="badge {{ $statusClasses[$request->status] ?? 'badge-outline' }} badge-lg">
                                                    {{ $statusLabels[$request->status] ?? ucfirst($request->status) }}
                                                </div>
                                            </div>
                                        </a>
                                    </div>

                                    <!-- Details Grid -->
                                    <div class="grid grid-cols-12 gap-3 items-center text-sm">
                                        <!-- Contact Info (3 cols) -->
                                        <div class="col-span-3">
                                            <div class="text-base-content/70 text-xs">{{ $request->email }}</div>
                                            <div class="text-base-content/70 text-xs">{{ $request->phone }}</div>
                                        </div>
                                        <!-- Pickup Details (4 cols) -->
                                        <div class="col-span-4">
                                            <div class="font-medium">{{ Str::limit($request->pickup_items, 50) }}</div>
                                  
                                            <div class="text-base-content/70 text-xs">{{ Str::limit($request->pickup_address, 50) }}</div>
                                        </div>
                                        <!-- Preferred Date (2 cols) -->
                                        <div class="col-span-2 text-xs">
                                            <div class="text-base-content/50 text-xs mb-1">Pickup Time:</div>
                                            {{ $request->preferred_pickup_date ? $request->preferred_pickup_date->setTimezone(\App\Models\GlobalConfig::getTimeZone())->format('M j, Y g:i A') : 'Not specified' }}
                                        </div>
                                        <!-- Customer (2 cols) -->
                                        <div class="col-span-2 text-xs">
                                            @if($request->customer)
                                                <a href="{{ route('customers.show', $request->customer) }}"
                                                   class="link link-primary">
                                                    {{ Str::limit($request->customer->name, 20) }}
                                                </a>
                                            @else
                                                <span class="text-base-content/50">Not linked</span>
                                            @endif
                                        </div>
                                        <!-- Submitted (1 col) -->
                                        <div class="col-span-1 text-xs text-center">
                                            <div class="text-base-content/50 text-xs mb-1">Submitted:</div>
                                            {{ $request->submitted_at ? $request->submitted_at->format('M j') : $request->created_at->format('M j') }}
                                        </div>
                                    </div>

                                    @perms('manage_pickup_requests')
                                        @if($request->status === 'incoming' && !$request->customer_id)
                                            <div class="mt-2">
                                                <div class="badge badge-warning badge-lg">Needs Customer Assignment</div>
                                            </div>
                                        @elseif($request->status === 'incoming' && $request->customer_id)
                                            <div class="mt-2">
                                                <div class="badge badge-info badge-lg">Ready to Schedule</div>
                                            </div>
                                        @elseif($request->status === 'pending')
                                            <div class="mt-2">
                                                <div class="badge badge-info badge-lg">Awaiting Customer Confirmation</div>
                                            </div>
                                        @endif
                                    @endperms
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="p-3 border-t border-base-300">
                        <x-pagination :paginator="$pickupRequests" :pagination="$pagination" />
                    </div>
                @else
                    <div class="p-4">
                        <div class="text-center text-base-content/70 py-8">
                            <div class="flex flex-col items-center gap-4">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-12 h-12">
                                        <i class="fa-sharp fa-clipboard-list text-lg"></i>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <h3 class="text-base font-medium text-base-content/80">No pickup requests found</h3>
                                    <p class="text-sm text-base-content/60 mt-1">
                                        @if(request('search'))
                                            Try adjusting your search criteria or clearing filters.
                                        @else
                                            Pickup requests will appear here when customers submit them through the guest form or when you create them.
                                        @endif
                                    </p>
                                </div>
                                <div class="flex gap-3 justify-center">
                                    @if(request('search') || request('status'))
                                        <a href="{{ route('pickup-requests.index') }}" class="btn btn-outline btn-sm gap-2">
                                            <i class="fa-sharp fa-refresh"></i>
                                            Clear All Filters
                                        </a>
                                    @endif
                                    @perms('manage_pickup_requests')
                                        <a href="{{ route('pickup-requests.create') }}" class="btn btn-primary btn-sm gap-2">
                                            <i class="fa-sharp fa-plus"></i>
                                            Create Request
                                        </a>
                                    @endperms
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function openDatePicker() {
            // Create a simple date input dialog
            const date = prompt('Enter date (YYYY-MM-DD):', '{{ now()->format("Y-m-d") }}');
            if (date && /^\d{4}-\d{2}-\d{2}$/.test(date)) {
                const url = '{{ route("pickup-requests.daily-pdf") }}?date=' + date;
                window.open(url, '_blank');
            } else if (date) {
                alert('Please enter a valid date in YYYY-MM-DD format');
            }
        }

        // Alternative: Use a more sophisticated date picker if available
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we have a better date picker available
            if (typeof flatpickr !== 'undefined') {
                // Replace the simple prompt with flatpickr if available
                window.openDatePicker = function() {
                    const input = document.createElement('input');
                    input.type = 'date';
                    input.value = '{{ now()->format("Y-m-d") }}';
                    input.style.position = 'absolute';
                    input.style.left = '-9999px';
                    document.body.appendChild(input);

                    input.addEventListener('change', function() {
                        if (this.value) {
                            const url = '{{ route("pickup-requests.daily-pdf") }}?date=' + this.value;
                            window.open(url, '_blank');
                        }
                        document.body.removeChild(this);
                    });

                    input.click();
                };
            }
        });
    </script>
    @endpush
</x-app-layout>
