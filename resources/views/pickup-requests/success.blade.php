<x-app-layout
    page-title="Pickup Event Created"
    page-icon="fa-sharp fa-check-circle"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Pickup Requests', 'route' => 'pickup-requests.index', 'icon' => 'fa-truck-pickup'],
        ['name' => 'Request #' . $pickupRequest->id, 'route' => 'pickup-requests.show', 'params' => [$pickupRequest], 'icon' => 'fa-file-lines'],
        ['name' => 'Complete', 'icon' => 'fa-check-circle']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Success Message -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-8 text-center">
                    <div class="avatar avatar-placeholder mb-4">
                        <div class="bg-success text-success-content w-16 rounded-full">
                            <i class="fa-sharp fa-check-circle text-3xl"></i>
                        </div>
                    </div>
                    <h2 class="text-2xl font-bold text-base-content mb-2">Pickup Event Created Successfully!</h2>
                    <p class="text-base-content/70">The pickup request has been converted to a scheduled pickup event.</p>
                </div>
            </div>

            <!-- Event Details -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-calendar-check text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Pickup Event Details</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    <!-- Event Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-base-200/50 rounded-lg p-4">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fa-sharp fa-hashtag text-primary"></i>
                                <span class="font-medium text-sm">Event ID</span>
                            </div>
                            <p class="font-mono text-lg">{{ $pickupRequest->event->id }}</p>
                        </div>

                        <div class="bg-base-200/50 rounded-lg p-4">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fa-sharp fa-calendar text-secondary"></i>
                                <span class="font-medium text-sm">Pickup Date</span>
                            </div>
                            <p class="text-lg">{{ $pickupRequest->event->start_date->format('l, F j, Y') }}</p>
                        </div>

                        <div class="bg-base-200/50 rounded-lg p-4">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fa-sharp fa-clock text-accent"></i>
                                <span class="font-medium text-sm">Pickup Time</span>
                            </div>
                            <p class="text-lg">{{ $pickupRequest->event->start_date->format('g:i A') }} - {{ $pickupRequest->event->end_date->format('g:i A') }}</p>
                        </div>

                        <div class="bg-base-200/50 rounded-lg p-4">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fa-sharp fa-user text-info"></i>
                                <span class="font-medium text-sm">Customer</span>
                            </div>
                            <p class="text-lg">{{ $pickupRequest->customer->name }}</p>
                        </div>

                        <div class="bg-base-200/50 rounded-lg p-4">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fa-sharp fa-users text-warning"></i>
                                <span class="font-medium text-sm">Staff Needed</span>
                            </div>
                            <p class="text-lg">{{ $pickupRequest->event->staff_needed ?? 1 }}</p>
                        </div>

                        @if($pickupRequest->event->assigned_driver_id)
                        <div class="bg-base-200/50 rounded-lg p-4">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fa-sharp fa-user-tie text-success"></i>
                                <span class="font-medium text-sm">Assigned Driver</span>
                            </div>
                            <p class="text-lg">{{ $pickupRequest->event->assignedDriver->name ?? 'N/A' }}</p>
                        </div>
                        @endif
                    </div>

                    <!-- Customer Information -->
                    <div class="bg-base-200/30 rounded-lg p-4">
                        <h5 class="font-semibold text-base-content mb-3 flex items-center gap-2">
                            <i class="fa-sharp fa-address-card text-primary"></i>
                            Customer Information
                        </h5>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-base-content/70">Contact:</span>
                                <p>{{ $pickupRequest->contact_name }}</p>
                                <p>{{ $pickupRequest->phone }}</p>
                                <p>{{ $pickupRequest->email }}</p>
                            </div>
                            <div>
                                <span class="font-medium text-base-content/70">Pickup Details:</span>
                                <p><strong>Items:</strong> {{ $pickupRequest->pickup_items }}</p>
                                <p><strong>Quantity:</strong> {{ $pickupRequest->pickup_quantity }}</p>
                                @if($pickupRequest->business_name)
                                    <p><strong>Business:</strong> {{ $pickupRequest->business_name }}</p>
                                @endif
                            </div>
                        </div>
                        @if($pickupRequest->pickup_address)
                            <div class="mt-3">
                                <span class="font-medium text-base-content/70">Address:</span>
                                <p>{{ $pickupRequest->pickup_address }}</p>
                            </div>
                        @endif
                    </div>

                    @if($pickupRequest->event->pickup_notes)
                    <!-- Staff Notes -->
                    <div class="bg-base-200/30 rounded-lg p-4">
                        <h5 class="font-semibold text-base-content mb-2 flex items-center gap-2">
                            <i class="fa-sharp fa-sticky-note text-warning"></i>
                            Staff Notes
                        </h5>
                        <p class="text-sm">{{ $pickupRequest->event->pickup_notes }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card bg-base-100 shadow-md">
                <div class="p-6">
                    <div class="flex flex-col sm:flex-row justify-center items-center gap-4">
                        <!-- Send Confirmation Email Button -->
                        @if($pickupRequest->status === 'pending' && $pickupRequest->event && !$pickupRequest->hasConfirmationEmailBeenSent())
                        <button type="button" id="sendConfirmationEmailBtn" class="btn btn-success gap-2 w-full sm:w-auto">
                            <i class="fa-sharp fa-envelope"></i>
                            Send Confirmation Email
                        </button>
                        @elseif($pickupRequest->hasConfirmationEmailBeenSent())
                        <div class="bg-success/10 border border-success/20 rounded-lg p-3 w-full sm:w-auto">
                            <div class="flex items-center gap-2 text-success text-sm">
                                <i class="fa-sharp fa-check-circle"></i>
                                <span>Confirmation email already sent</span>
                            </div>
                        </div>
                        @endif

                        <a href="{{ route('pickup-requests.show', $pickupRequest) }}" class="btn btn-primary gap-2 w-full sm:w-auto">
                            <i class="fa-sharp fa-eye"></i>
                            View Pickup Request
                        </a>

                        <a href="{{ route('events.show', $pickupRequest->event) }}" class="btn btn-secondary gap-2 w-full sm:w-auto">
                            <i class="fa-sharp fa-calendar-check"></i>
                            View Calendar Event
                        </a>

                        <a href="{{ route('pickup-requests.index') }}" class="btn btn-ghost gap-2 w-full sm:w-auto">
                            <i class="fa-sharp fa-list"></i>
                            Back to Pickup Requests
                        </a>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-info text-info-content w-8 rounded-lg">
                                <i class="fa-sharp fa-lightbulb text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Next Steps</h4>
                    </div>
                </div>

                <div class="p-6">
                    <div class="space-y-3 text-sm">
                        <div class="flex items-start gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content w-6 rounded">
                                    <span class="text-xs font-bold">1</span>
                                </div>
                            </div>
                            <div>
                                <p class="font-medium">Event is now on the calendar</p>
                                <p class="text-base-content/70">The pickup event has been added to the pickup calendar and is visible to all staff.</p>
                            </div>
                        </div>

                        <div class="flex items-start gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-6 rounded">
                                    <span class="text-xs font-bold">2</span>
                                </div>
                            </div>
                            <div>
                                <p class="font-medium">Pickup request status updated</p>
                                <p class="text-base-content/70">The pickup request status has been changed to "Pending" and is linked to the calendar event. Staff can confirm customer response when received.</p>
                            </div>
                        </div>

                        <div class="flex items-start gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-accent text-accent-content w-6 rounded">
                                    <span class="text-xs font-bold">3</span>
                                </div>
                            </div>
                            <div>
                                <p class="font-medium">Ready for pickup day</p>
                                <p class="text-base-content/70">Staff can now prepare for the pickup and update the event as needed.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const sendEmailBtn = document.getElementById('sendConfirmationEmailBtn');
        
        if (sendEmailBtn) {
            sendEmailBtn.addEventListener('click', async function() {
                // Disable button and show loading state
                sendEmailBtn.disabled = true;
                const originalText = sendEmailBtn.innerHTML;
                sendEmailBtn.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Sending...';
                
                try {
                    const response = await fetch('{{ route("pickup-requests.send-confirmation-email", $pickupRequest) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'Accept': 'application/json'
                        }
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        // Success - replace button with success message
                        sendEmailBtn.outerHTML = `
                            <div class="bg-success/10 border border-success/20 rounded-lg p-3 w-full sm:w-auto">
                                <div class="flex items-center gap-2 text-success text-sm">
                                    <i class="fa-sharp fa-check-circle"></i>
                                    <span>Confirmation email sent to ${data.sent_to}</span>
                                </div>
                            </div>
                        `;
                    } else {
                        // Error - show error message and restore button
                        alert('Error: ' + data.message);
                        sendEmailBtn.disabled = false;
                        sendEmailBtn.innerHTML = originalText;
                    }
                } catch (error) {
                    console.error('Error sending confirmation email:', error);
                    alert('An error occurred while sending the confirmation email. Please try again.');
                    sendEmailBtn.disabled = false;
                    sendEmailBtn.innerHTML = originalText;
                }
            });
        }
    });
    </script>
</x-app-layout>
