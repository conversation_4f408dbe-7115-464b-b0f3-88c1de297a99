<x-app-layout
    page-title="Create Pickup Request"
    page-icon="fa-sharp fa-truck-pickup"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('pickup-requests.index'),
            'text' => 'Back to Requests'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Pickup Requests', 'route' => 'pickup-requests.index', 'icon' => 'fa-truck-pickup'],
        ['name' => 'Create Request', 'icon' => 'fa-plus']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Introduction Card -->
            <div class="bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
                <!-- Decorative Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 left-0 w-32 h-32 bg-primary rounded-full -translate-x-16 -translate-y-16"></div>
                    <div class="absolute bottom-0 right-0 w-24 h-24 bg-secondary rounded-full translate-x-12 translate-y-12"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-start gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-gradient-to-br from-primary to-primary-focus text-primary-content w-12 rounded-xl shadow-lg">
                                <i class="fa-sharp fa-truck-pickup text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-2xl font-bold text-base-content mb-2">Create New Pickup Request</h3>
                            <p class="text-base-content/70 text-base leading-relaxed">
                                Create a pickup request for an existing customer. The pickup event will be created automatically and scheduled on the calendar.
                            </p>
                            <!-- Auto-save status indicator -->
                            <div id="autoSaveStatus" class="mt-3 hidden">
                                <div class="flex items-center gap-2 text-sm">
                                    <i class="fa-sharp fa-cloud-check text-success"></i>
                                    <span class="text-success">Draft saved automatically</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Calendar Time Selection Notification -->
            @if(request()->has('start') && request()->has('end'))
                <div class="alert alert-success mb-6">
                    <div class="flex items-center gap-3">
                        <i class="fa-sharp fa-calendar-check text-lg"></i>
                        <div>
                            <h4 class="font-bold">Time slot selected from calendar</h4>
                            <p class="text-sm">The pickup time has been pre-filled from your calendar selection. You can adjust it if needed.</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Draft Loaded Notification -->
            @if(!empty($draftData) && count(array_filter($draftData)) > 0 && !request()->has('start'))
                <div class="alert alert-info mb-6">
                    <div class="flex items-center justify-between w-full gap-4">
                        <div class="flex items-center gap-3 flex-1">
                            <i class="fa-sharp fa-cloud-arrow-down text-lg"></i>
                            <div>
                                <h4 class="font-bold">Saved draft loaded</h4>
                                <p class="text-sm">Your previously saved form data has been restored. All fields remain editable.</p>
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <button type="button" class="btn btn-outline btn-sm gap-2" onclick="clearDraftData()">
                                <i class="fa-sharp fa-trash"></i>
                                Clear Draft
                            </button>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Error Display -->
            @if($errors->any())
                <div class="alert alert-error mb-6">
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-exclamation-triangle"></i>
                        <div>
                            <h4 class="font-bold">There were some errors with your submission:</h4>
                            <ul class="list-disc list-inside mt-2">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-error mb-6">
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-exclamation-triangle"></i>
                        <span>{{ session('error') }}</span>
                    </div>
                </div>
            @endif

            <!-- Pickup Request Form -->
            <form action="{{ route('pickup-requests.store') }}" method="POST" id="pickup-request-form" class="space-y-6">
                @csrf

                @include('pickup-requests.partials.customer-selection', [
                    'selectedCustomerId' => $draftData['customer_id'] ?? null,
                    'selectedCustomerName' => $selectedCustomerName ?? null
                ])

                @include('pickup-requests.partials.contact-information', [
                    'contactName' => $draftData['contact_name'] ?? '',
                    'businessName' => $draftData['business_name'] ?? '',
                    'email' => $draftData['email'] ?? '',
                    'phone' => $draftData['phone'] ?? ''
                ])

                <!-- Pickup Date & Time Selection Card -->
                <div class="card bg-base-100 shadow-md">
                    <!-- Header Section -->
                    <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-accent text-accent-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-calendar text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Pickup Date & Time</h4>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="p-6">
                        @if(!empty($nextAvailableSlot) && empty($draftData['preferred_pickup_date']) && !request()->has('start'))
                            <div class="alert alert-success mb-4">
                                <div class="flex items-center gap-2">
                                    <i class="fa-sharp fa-calendar-check"></i>
                                    <div>
                                        <h4 class="font-bold">Next available slot selected</h4>
                                        <p class="text-sm">We've automatically selected the earliest available pickup time. You can change this if needed.</p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <x-pickup-time-selector
                            name="preferred_pickup_date"
                            :value="old('preferred_pickup_date', $draftData['preferred_pickup_date'] ?? $nextAvailableSlot ?? '')"
                            :required="true"
                            label="Pickup Date & Time"
                            icon="fa-clock"
                            iconColor="text-accent"
                            :autoSave="true"
                            autoSaveField="preferred_pickup_date"
                        />
                    </div>
                </div>

                <!-- Pickup Address & Location Details Card -->
                <div class="card bg-base-100 shadow-md">
                    <!-- Header Section -->
                    <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-map-marker-alt text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Pickup Location</h4>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="p-6 space-y-4">
                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-map-marker-alt text-success mr-2"></i>
                                Pickup Address *
                            </legend>

                            <x-google-maps-autocomplete
                                id="pickup_address"
                                name="pickup_address"
                                :value="old('pickup_address', $draftData['pickup_address'] ?? '')"
                                placeholder="Start typing to search for an address..."
                                type="textarea"
                                label=""
                                icon="fa-map-marker-alt"
                                iconColor="text-success"
                                :restrictions="['us', 'ca']"
                                :showDisplayMode="true"
                                displayLabel="Pickup Address"
                                changeButtonText="Change"
                                :autoSave="true"
                                autoSaveField="pickup_address"
                                :required="true"
                            />

                            @error('pickup_address')
                                <div class="text-error text-sm mt-1">{{ $message }}</div>
                            @enderror
                        </fieldset>

                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-home text-success mr-2"></i>
                                Where on the property are the items located? *
                            </legend>
                            <div class="relative">
                                <textarea name="property_location_details" id="property_location_details"
                                          class="textarea textarea-bordered h-24 w-full auto-save"
                                          data-field="property_location_details"
                                          placeholder="Please describe the location and accessibility (e.g., garage, basement, second floor, stairs involved, etc.). Note: Extra fees may apply for moving large items from difficult locations."
                                          required>{{ old('property_location_details', $draftData['property_location_details'] ?? '') }}</textarea>
                                <span class="saving-indicator absolute right-3 top-3 hidden">
                                    <i class="fa-sharp fa-spinner fa-spin text-success"></i>
                                </span>
                            </div>
                            @error('property_location_details')
                                <div class="text-error text-sm mt-1">{{ $message }}</div>
                            @enderror
                        </fieldset>

                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-sticky-note text-success mr-2"></i>
                                Other things to know
                            </legend>
                            <div class="relative">
                                <textarea name="other_notes" id="other_notes"
                                          class="textarea textarea-bordered h-24 w-full auto-save"
                                          data-field="other_notes"
                                          placeholder="Any security codes, special instructions, who to call when we arrive, or other important details">{{ old('other_notes', $draftData['other_notes'] ?? '') }}</textarea>
                                <span class="saving-indicator absolute right-3 top-3 hidden">
                                    <i class="fa-sharp fa-spinner fa-spin text-success"></i>
                                </span>
                            </div>
                            <div class="text-sm text-base-content/60 mt-1">Optional</div>
                            @error('other_notes')
                                <div class="text-error text-sm mt-1">{{ $message }}</div>
                            @enderror
                        </fieldset>
                    </div>
                </div>

                @include('pickup-requests.partials.item-details', [
                    'loadSize' => old('load_size', $draftData['load_size'] ?? ''),
                    'itemTypes' => old('item_types', $draftData['item_types'] ?? []),
                    'itemSpecifics' => old('item_specifics', $draftData['item_specifics'] ?? ''),
                    'driverInstructions' => old('driver_instructions', $draftData['driver_instructions'] ?? ''),
                    'accessibilityLevel' => old('accessibility_level', $draftData['accessibility_level'] ?? '')
                ])

                @include('pickup-requests.partials.event-details', [
                    'staffNeeded' => $draftData['staff_needed'] ?? '1',
                    'assignedDriverId' => $draftData['assigned_driver_id'] ?? '',
                    'internalPickupNotes' => $draftData['internal_pickup_notes'] ?? '',
                    'drivers' => $drivers ?? []
                ])

                <!-- Submit Button Card -->
                <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg p-6">
                    <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content w-10 rounded-lg">
                                    <i class="fa-sharp fa-paper-plane text-sm"></i>
                                </div>
                            </div>
                            <div class="text-center sm:text-left">
                                <p class="font-medium text-base-content">Create Pickup Request & Event</p>
                                <p class="text-sm text-base-content/60">The pickup event will be created automatically</p>
                            </div>
                        </div>
                        <div class="flex gap-3 w-full sm:w-auto">
                            <a href="{{ route('pickup-requests.index') }}" class="btn btn-outline btn-lg gap-2 flex-1 sm:flex-none">
                                <i class="fa-sharp fa-times"></i>
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                                <i class="fa-sharp fa-calendar-plus"></i>
                                Create Request & Event
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-save functionality
            const autoSaveInputs = document.querySelectorAll('.auto-save');
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            let saveTimeout;

            console.log('Found', autoSaveInputs.length, 'auto-save inputs:', Array.from(autoSaveInputs).map(input => ({ id: input.id, field: input.dataset.field })));

            // Find the customer selection hidden input and add auto-save functionality to it
            const customerHiddenInput = document.querySelector('input[name="customer_id"]');
            console.log('Customer hidden input found:', !!customerHiddenInput, 'value:', customerHiddenInput?.value);

            // Debug draft data restoration
            console.log('Draft data from server:', @json($draftData ?? []));
            console.log('Selected customer name from server:', @json($selectedCustomerName ?? null));

            // Set up a MutationObserver to watch for changes to the customer hidden input value
            if (customerHiddenInput) {
                let lastCustomerValue = customerHiddenInput.value;

                // Watch for attribute changes (value changes)
                const customerObserver = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                            const newValue = customerHiddenInput.value;
                            if (newValue !== lastCustomerValue) {
                                console.log('Customer hidden input value changed via mutation:', lastCustomerValue, '->', newValue);
                                lastCustomerValue = newValue;
                                saveField('customer_id', newValue);
                            }
                        }
                    });
                });

                // Start observing
                customerObserver.observe(customerHiddenInput, {
                    attributes: true,
                    attributeFilter: ['value']
                });

                // Also listen for direct property changes
                let originalValueSetter = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value').set;
                Object.defineProperty(customerHiddenInput, 'value', {
                    get: function() {
                        return this.getAttribute('value') || '';
                    },
                    set: function(newValue) {
                        const oldValue = this.value;
                        originalValueSetter.call(this, newValue);
                        this.setAttribute('value', newValue);
                        if (newValue !== oldValue) {
                            console.log('Customer hidden input value changed via property:', oldValue, '->', newValue);
                            saveField('customer_id', newValue);
                        }
                    }
                });
            }

            // Address elements
            const pickupAddressInput = document.getElementById('pickup_address');

            let selectedCustomer = null;

            // Auto-save function (make it global for use by item details partial)
            window.saveField = function saveField(field, value, indicator) {
                console.log('saveField called:', { field, value, hasIndicator: !!indicator });

                if (indicator) {
                    indicator.classList.remove('hidden');
                }

                fetch('{{ route("pickup-requests.auto-save") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'X-Auto-Save': 'true'
                    },
                    body: JSON.stringify({
                        field: field,
                        value: value
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Auto-save response:', data);
                    if (indicator) {
                        indicator.classList.add('hidden');
                    }
                    if (data.success) {
                        // Show auto-save status
                        const statusIndicator = document.getElementById('autoSaveStatus');
                        if (statusIndicator) {
                            statusIndicator.classList.remove('hidden');

                            // Hide after 3 seconds
                            setTimeout(() => {
                                statusIndicator.classList.add('hidden');
                            }, 3000);
                        }
                        console.log('Auto-save successful for field:', field);
                    } else {
                        console.error('Auto-save failed:', data.message);
                    }
                })
                .catch(error => {
                    if (indicator) {
                        indicator.classList.add('hidden');
                    }
                    console.error('Auto-save error:', error);
                });
            }

            // Function to handle auto-save for an input
            function handleAutoSave(input) {
                const field = input.dataset.field;
                const value = input.value;
                const indicator = input.parentNode.querySelector('.saving-indicator');

                console.log('handleAutoSave called:', {
                    field,
                    value,
                    valueLength: value.length,
                    hasIndicator: !!indicator,
                    inputId: input.id
                });

                // Clear any existing timeout
                clearTimeout(saveTimeout);

                // Set a new timeout to save after 500ms of inactivity
                saveTimeout = setTimeout(() => {
                    saveField(field, value, indicator);
                }, 500);
            }

            // Add event listeners to auto-save inputs
            autoSaveInputs.forEach(input => {
                console.log('Adding auto-save listeners to:', input.id, 'field:', input.dataset.field);

                // Listen for input events (typing)
                input.addEventListener('input', function(e) {
                    console.log('Input event triggered on:', this.id, 'value:', this.value);
                    handleAutoSave(this);
                });

                // Listen for change events (field loses focus, paste, delete all content, etc.)
                input.addEventListener('change', function(e) {
                    console.log('Change event triggered on:', this.id, 'value:', this.value);
                    handleAutoSave(this);
                });

                // Listen for paste events
                input.addEventListener('paste', function(e) {
                    console.log('Paste event triggered on:', this.id);
                    // Use setTimeout to ensure pasted content is processed
                    setTimeout(() => {
                        handleAutoSave(this);
                    }, 10);
                });

                // Listen for keyup events to catch delete/backspace
                input.addEventListener('keyup', function(e) {
                    if (e.key === 'Delete' || e.key === 'Backspace') {
                        console.log('Delete/Backspace event triggered on:', this.id, 'value:', this.value);
                        handleAutoSave(this);
                    }
                });
            });





            // Customer selection handling
            function handleCustomerSelection(customerId) {
                console.log('handleCustomerSelection called with customerId:', customerId);

                // Show loading state
                showAutoPopulateNotification('Loading customer information...', 'loading');

                // Fetch full customer data from API
                fetch(`/pickup-requests/customer/${customerId}`)
                    .then(response => response.json())
                    .then(data => {
                        console.log('Customer data received:', data);
                        if (data.success) {
                            const customer = data.customer;
                            selectedCustomer = customer;

                            // Auto-save customer selection immediately
                            console.log('Auto-saving customer selection:', customer.id);
                            saveField('customer_id', customer.id);

                            // Track which fields were auto-populated
                            const autoPopulatedFields = [];

                            // Import customer data to contact fields
                            const contactNameField = document.getElementById('contact_name');
                            console.log('contactNameField found:', !!contactNameField);
                            if (contactNameField && customer.contact) {
                                contactNameField.value = customer.contact;
                                saveField('contact_name', customer.contact);
                                autoPopulatedFields.push('Contact Name');
                                console.log('Populated contact name with customer.contact:', customer.contact);
                            } else if (contactNameField && customer.name) {
                                contactNameField.value = customer.name;
                                saveField('contact_name', customer.name);
                                autoPopulatedFields.push('Contact Name');
                                console.log('Populated contact name with customer.name:', customer.name);
                            }

                            const businessNameField = document.getElementById('business_name');
                            console.log('businessNameField found:', !!businessNameField, 'customer.type:', customer.type);
                            if (businessNameField && customer.name && customer.type === 'Business') {
                                businessNameField.value = customer.name;
                                saveField('business_name', customer.name);
                                autoPopulatedFields.push('Business Name');
                                console.log('Populated business name:', customer.name);
                            }

                            const emailField = document.getElementById('email');
                            console.log('emailField found:', !!emailField, 'customer.email:', customer.email);
                            if (emailField && customer.email) {
                                emailField.value = customer.email;
                                saveField('email', customer.email);
                                autoPopulatedFields.push('Email');
                                console.log('Populated email:', customer.email);
                            }

                            const phoneField = document.getElementById('phone');
                            console.log('phoneField found:', !!phoneField, 'customer.phone:', customer.phone);
                            if (phoneField && customer.phone) {
                                phoneField.value = customer.phone;
                                saveField('phone', customer.phone);
                                autoPopulatedFields.push('Phone');
                                console.log('Populated phone:', customer.phone);
                            }

                            // Handle address - simplified approach
                            console.log('pickupAddressInput found:', !!pickupAddressInput, 'customer.address:', customer.address);
                            if (customer.address) {
                                // Try to find the Google Maps autocomplete container
                                const addressContainer = document.querySelector('[id*="pickup_address_"][id$="_container"]');
                                console.log('addressContainer found:', !!addressContainer);

                                if (addressContainer) {
                                    // Find the actual input element
                                    const actualInput = addressContainer.querySelector('input[name="pickup_address"], textarea[name="pickup_address"]');
                                    console.log('actualInput found:', !!actualInput);

                                    if (actualInput) {
                                        // Set the value on the input
                                        actualInput.value = customer.address;

                                        // Try to trigger the component's built-in display mode conversion
                                        // by dispatching the addressSelected event that the component listens for
                                        const addressSelectedEvent = new CustomEvent('addressSelected', {
                                            detail: {
                                                formattedAddress: customer.address,
                                                input: actualInput,
                                                place: {
                                                    formattedAddress: customer.address,
                                                    displayName: customer.address
                                                }
                                            }
                                        });

                                        // Dispatch the event to trigger the component's display mode
                                        document.dispatchEvent(addressSelectedEvent);

                                        // Also try to find and update existing display elements
                                        const displayText = addressContainer.querySelector('[id*="_display_text"]');
                                        if (displayText) {
                                            displayText.textContent = customer.address;
                                            console.log('Updated existing display text:', customer.address);
                                        }

                                        // Try to show display mode if elements exist
                                        const displayElement = addressContainer.querySelector('[id*="_display"]');
                                        const inputContainer = addressContainer.querySelector('[id*="_input_container"]');

                                        if (displayElement && inputContainer) {
                                            displayElement.style.display = '';
                                            inputContainer.style.display = 'none';
                                            console.log('Switched to existing display mode');
                                        }

                                        // Auto-save the address
                                        saveField('pickup_address', customer.address);
                                        autoPopulatedFields.push('Pickup Address');
                                        console.log('Populated address:', customer.address);
                                    }
                                } else if (pickupAddressInput) {
                                    // Fallback to direct input manipulation
                                    pickupAddressInput.value = customer.address;

                                    // Try to trigger the addressSelected event for the fallback input too
                                    const addressSelectedEvent = new CustomEvent('addressSelected', {
                                        detail: {
                                            formattedAddress: customer.address,
                                            input: pickupAddressInput,
                                            place: {
                                                formattedAddress: customer.address,
                                                displayName: customer.address
                                            }
                                        }
                                    });
                                    document.dispatchEvent(addressSelectedEvent);

                                    saveField('pickup_address', customer.address);
                                    autoPopulatedFields.push('Pickup Address');
                                    console.log('Populated address (fallback):', customer.address);
                                }
                            }

                            // Show success notification with populated fields
                            const customerDisplayName = customer.contact || customer.name;
                            const fieldsText = autoPopulatedFields.length > 0 ?
                                ` Auto-populated: ${autoPopulatedFields.join(', ')}.` : '';

                            showAutoPopulateNotification(
                                `Customer "${customerDisplayName}" selected.${fieldsText} All fields remain editable.`,
                                'success'
                            );

                            // Add customer notes as helpful context if available
                            if (customer.notes) {
                                showCustomerNotesInfo(customer.notes);
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching customer data:', error);
                        console.error('Error stack:', error.stack);
                        showAutoPopulateNotification('Error loading customer information. Please try again.', 'error');
                    });
            }

            // Show auto-populate notification
            function showAutoPopulateNotification(message, type = 'info') {
                // Remove any existing notification
                const existingNotification = document.getElementById('autoPopulateNotification');
                if (existingNotification) {
                    existingNotification.remove();
                }

                // Create notification element
                const notification = document.createElement('div');
                notification.id = 'autoPopulateNotification';
                notification.className = `alert ${type === 'success' ? 'alert-success' : type === 'error' ? 'alert-error' : type === 'loading' ? 'alert-info' : 'alert-info'} mb-4`;

                const icon = type === 'success' ? 'fa-check-circle' :
                           type === 'error' ? 'fa-exclamation-triangle' :
                           type === 'loading' ? 'fa-spinner fa-spin' : 'fa-info-circle';

                notification.innerHTML = `
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp ${icon}"></i>
                        <span>${message}</span>
                    </div>
                `;

                // Insert after customer selection card
                const customerCard = document.querySelector('.card');
                customerCard.parentNode.insertBefore(notification, customerCard.nextSibling);

                // Auto-remove success/error notifications after 5 seconds
                if (type === 'success' || type === 'error') {
                    setTimeout(() => {
                        if (notification && notification.parentNode) {
                            notification.remove();
                        }
                    }, 5000);
                }
            }

            // Show customer notes as helpful context
            function showCustomerNotesInfo(notes) {
                // Check if notes info already exists
                let notesInfo = document.getElementById('customerNotesInfo');

                if (!notesInfo) {
                    // Create notes info element
                    notesInfo = document.createElement('div');
                    notesInfo.id = 'customerNotesInfo';
                    notesInfo.className = 'alert alert-info mb-4';

                    // Insert after auto-populate notification or customer card
                    const notification = document.getElementById('autoPopulateNotification');
                    const insertAfter = notification || document.querySelector('.card');
                    insertAfter.parentNode.insertBefore(notesInfo, insertAfter.nextSibling);
                }

                notesInfo.innerHTML = `
                    <div class="flex items-start gap-2">
                        <i class="fa-sharp fa-sticky-note text-info mt-1"></i>
                        <div>
                            <div class="font-medium">Customer Notes:</div>
                            <div class="text-sm mt-1">${notes}</div>
                        </div>
                        <button type="button" class="btn btn-ghost btn-xs ml-auto" onclick="this.parentElement.parentElement.remove()">
                            <i class="fa-sharp fa-times"></i>
                        </button>
                    </div>
                `;
            }

            // Customer deselection handling
            function handleCustomerDeselection() {
                console.log('handleCustomerDeselection called');
                selectedCustomer = null;

                // Auto-save the cleared customer selection
                console.log('Auto-saving customer deselection (empty value)');
                saveField('customer_id', '');

                // Clear any auto-populate notifications
                const existingNotification = document.getElementById('autoPopulateNotification');
                if (existingNotification) {
                    existingNotification.remove();
                }

                // Clear customer notes info
                const notesInfo = document.getElementById('customerNotesInfo');
                if (notesInfo) {
                    notesInfo.remove();
                }

                console.log('Customer deselected and auto-saved');
            }

            // Listen for customer selection from the dynamic search component
            document.addEventListener('customerSelected', function(event) {
                console.log('customerSelected event received:', event.detail);
                handleCustomerSelection(event.detail.customerId);
            });

            // Listen for customer deselection from the dynamic search component
            document.addEventListener('customerDeselected', function(event) {
                console.log('customerDeselected event received');
                handleCustomerDeselection();
            });

            // Listen for address selection from the Google Maps component
            document.addEventListener('addressSelected', function(event) {
                const formattedAddress = event.detail.formattedAddress;
                if (formattedAddress && event.detail.input === pickupAddressInput) {
                    saveField('pickup_address', formattedAddress);
                }
            });










            // Clear draft function (make it global)
            window.clearDraftData = async function() {
                const confirmed = await showConfirm({
                    title: 'Clear Draft Data',
                    message: 'Are you sure you want to clear all saved draft data? This action cannot be undone and all form fields will be reset.',
                    confirmText: 'Clear Draft',
                    confirmClass: 'btn-error',
                    icon: 'fa-trash',
                    iconColor: 'bg-error/10 text-error'
                });

                if (!confirmed) return;

                try {
                    const response = await fetch('{{ route("pickup-requests.clear-draft") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken
                        }
                    });

                    const data = await response.json();

                    if (data.success) {
                        // Reload the page to clear all form data
                        window.location.reload();
                    } else {
                        console.error('Failed to clear draft:', data.message);
                        alert('Failed to clear draft data. Please try again.');
                    }
                } catch (error) {
                    console.error('Error clearing draft:', error);
                    alert('An error occurred while clearing draft data. Please try again.');
                }
            };

        });
    </script>

    <!-- Include the app dialog component for confirmations -->
    <x-app-dialog />

</x-app-layout>
