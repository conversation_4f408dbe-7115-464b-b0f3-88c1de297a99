<x-app-layout
    page-title="Step 2: Select Pickup Time"
    page-icon="fa-sharp fa-calendar-clock"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Pickup Requests', 'route' => 'pickup-requests.index', 'icon' => 'fa-truck-pickup'],
        ['name' => 'Request #' . $pickupRequest->id, 'route' => 'pickup-requests.show', 'params' => [$pickupRequest], 'icon' => 'fa-file-lines'],
        ['name' => 'Schedule', 'icon' => 'fa-calendar-clock']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Progress Steps -->
            <div class="card bg-base-100 shadow-md">
                <div class="p-6">
                    <ul class="steps steps-horizontal w-full">
                        <li class="step step-primary">Step 1: Customer</li>
                        <li class="step step-primary">Step 2: Time</li>
                        <li class="step">Step 3: Details</li>
                    </ul>
                </div>
            </div>

            <!-- Pickup Request Information -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-truck-pickup text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Pickup Request Details</h4>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-hashtag text-primary"></i>
                            <span class="font-medium">Request #:</span>
                            <span class="font-mono">{{ $pickupRequest->id }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user text-secondary"></i>
                            <span class="font-medium">Customer:</span>
                            <span>{{ $pickupRequest->customer->name }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-phone text-accent"></i>
                            <span class="font-medium">Phone:</span>
                            <span>{{ $pickupRequest->phone }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-location-dot text-info"></i>
                            <span class="font-medium">Address:</span>
                            <span class="truncate" title="{{ $pickupRequest->pickup_address }}">{{ $pickupRequest->pickup_address }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-boxes text-warning"></i>
                            <span class="font-medium">Items:</span>
                            <span class="truncate" title="{{ $pickupRequest->pickup_items }}">{{ $pickupRequest->pickup_items }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-hashtag text-success"></i>
                            <span class="font-medium">Quantity:</span>
                            <span>{{ $pickupRequest->pickup_quantity }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Time Selection Form -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-calendar-clock text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Step 2: Select Pickup Time</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    @if($pickupRequest->preferred_pickup_date)
                        <div class="bg-info/10 border border-info/20 rounded-lg p-4">
                            <div class="flex items-start gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-info text-info-content w-6 rounded">
                                        <i class="fa-sharp fa-info-circle text-xs"></i>
                                    </div>
                                </div>
                                <div class="text-sm text-base-content/80">
                                    <p class="font-medium mb-1">Customer's Preferred Time</p>
                                    <p>{{ $pickupRequest->preferred_pickup_date->format('l, F j, Y \a\t g:i A') }}</p>
                                    <p class="text-xs text-base-content/60 mt-1">You can use this time or select a different one below.</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <form action="{{ route('pickup-requests.saveStep2', $pickupRequest) }}" method="POST" class="space-y-6">
                        @csrf

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Pickup Date -->
                            <div>
                                <label class="label" for="pickup_date">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-calendar text-primary mr-2"></i>
                                        Pickup Date <span class="text-error font-bold">*</span>
                                    </span>
                                </label>
                                <input type="date"
                                       name="pickup_date"
                                       id="pickup_date"
                                       class="input input-bordered w-full"
                                       value="{{ old('pickup_date', $pickupRequest->preferred_pickup_date ? $pickupRequest->preferred_pickup_date->format('Y-m-d') : '') }}"
                                       min="{{ date('Y-m-d') }}"
                                       onchange="updateWeekView()"
                                       required>
                                @error('pickup_date')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>

                            <!-- Pickup Time -->
                            <div>
                                <label class="label" for="pickup_time">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-clock text-primary mr-2"></i>
                                        Pickup Time <span class="text-error font-bold">*</span>
                                    </span>
                                </label>
                                <input type="time"
                                       name="pickup_time"
                                       id="pickup_time"
                                       class="input input-bordered w-full"
                                       value="{{ old('pickup_time', $pickupRequest->preferred_pickup_date ? $pickupRequest->preferred_pickup_date->format('H:i') : '09:00') }}"
                                       onchange="updateWeekView()"
                                       required>
                                @error('pickup_time')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>
                        </div>

                        <!-- Week View Calendar -->
                        <div>
                            <label class="label">
                                <span class="label-text">
                                    <i class="fa-sharp fa-calendar-week text-primary mr-2"></i>
                                    Week Schedule Preview
                                </span>
                            </label>
                            
                            <div id="weekViewContainer" class="border border-base-300 rounded-lg bg-base-50 min-h-[500px]">
                                <div id="weekCalendar" class="h-[420px]"></div>
                                <div id="weekCalendarPlaceholder" class="text-center text-base-content/50 py-8">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                            <i class="fa-sharp fa-calendar text-2xl"></i>
                                        </div>
                                    </div>
                                    <p class="mt-2">Select date and time to preview week schedule</p>
                                </div>
                            </div>

                            <div class="mt-3 text-xs text-base-content/70">
                                <div class="grid grid-cols-2 gap-2">
                                    <div class="flex items-center gap-2">
                                        <div class="w-3 h-3 rounded" style="background-color: #10b981;"></div>
                                        <span>Proposed Pickup</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-3 h-3 rounded" style="background-color: #3b82f6;"></div>
                                        <span>Existing Pickups</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-3 h-3 rounded" style="background-color: #ef4444;"></div>
                                        <span>Blocked Times</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-3 h-3 rounded" style="background-color: #8b5cf6;"></div>
                                        <span>Available Slots (click to select)</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation -->
                        <div class="divider my-8">
                            <span class="text-base-content/50 font-medium">Continue to event details</span>
                        </div>

                        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                            <a href="{{ route('pickup-requests.step1', $pickupRequest) }}" class="btn btn-ghost gap-2">
                                <i class="fa-sharp fa-arrow-left"></i>
                                Back to Step 1
                            </a>

                            <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg w-full sm:w-auto">
                                Continue to Step 3
                                <i class="fa-sharp fa-arrow-right"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Include FullCalendar library -->
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css' rel='stylesheet' />
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js'></script>
    
    <style>
        /* Custom styles for the week calendar */
        #weekCalendar .fc-toolbar {
            margin-bottom: 0.5rem;
        }
        
        #weekCalendar .fc-toolbar-title {
            font-size: 1rem;
            font-weight: 600;
        }
        
        #weekCalendar .fc-button {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        #weekCalendar .fc-timegrid-slot {
            height: 1.5rem;
        }
        
        #weekCalendar .fc-event {
            font-size: 0.7rem;
            border-radius: 0.25rem;
            padding: 1px 2px;
            line-height: 1.2;
        }
        
        #weekCalendar .fc-event-title {
            font-weight: 500;
        }
        
        #weekCalendar .available-slot {
            cursor: pointer !important;
            transition: opacity 0.2s ease;
        }
        
        #weekCalendar .available-slot:hover {
            opacity: 0.8 !important;
        }
        
        #weekCalendar .proposed-event {
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
            animation: proposedGlow 2s ease-in-out infinite alternate;
            border: 2px solid rgba(16, 185, 129, 0.6) !important;
        }

        @keyframes proposedGlow {
            from {
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
            }
            to {
                box-shadow: 0 6px 20px rgba(16, 185, 129, 0.7);
            }
        }
    </style>

    <script>
        let weekCalendar = null;

        async function updateWeekView() {
            const pickupDate = document.getElementById('pickup_date').value;
            const pickupTime = document.getElementById('pickup_time').value;

            if (!pickupDate || !pickupTime) {
                // Show placeholder and hide calendar
                document.getElementById('weekCalendar').style.display = 'none';
                document.getElementById('weekCalendarPlaceholder').style.display = 'block';
                if (weekCalendar) {
                    weekCalendar.destroy();
                    weekCalendar = null;
                }
                return;
            }

            // Hide placeholder and show calendar
            document.getElementById('weekCalendarPlaceholder').style.display = 'none';
            document.getElementById('weekCalendar').style.display = 'block';

            try {
                const response = await fetch(`/pickup-requests/{{ $pickupRequest->id }}/week-view?pickup_date=${pickupDate}&pickup_time=${pickupTime}`);
                const data = await response.json();

                if (data.error) {
                    document.getElementById('weekCalendar').style.display = 'none';
                    document.getElementById('weekCalendarPlaceholder').style.display = 'block';
                    document.getElementById('weekCalendarPlaceholder').innerHTML = `
                        <div class="text-center py-8 text-error">
                            <i class="fa-sharp fa-exclamation-triangle text-2xl mb-2"></i>
                            <p>${data.error}</p>
                        </div>
                    `;
                    return;
                }

                // Destroy existing calendar if it exists
                if (weekCalendar) {
                    weekCalendar.destroy();
                }

                // Initialize FullCalendar with the week view
                const calendarEl = document.getElementById('weekCalendar');
                const selectedDate = new Date(pickupDate + 'T' + pickupTime);
                
                weekCalendar = new FullCalendar.Calendar(calendarEl, {
                    initialView: 'timeGridWeek',
                    initialDate: selectedDate,
                    headerToolbar: {
                        left: 'prev,next',
                        center: 'title',
                        right: ''
                    },
                    height: 'auto',
                    timeZone: 'local',
                    slotMinTime: '09:00:00',
                    slotMaxTime: '17:00:00',
                    slotDuration: '00:30:00',
                    slotLabelInterval: '01:00:00',
                    allDaySlot: false,
                    displayEventTime: false,
                    events: data.events.map(event => {
                        let backgroundColor = '#3b82f6'; // Primary blue
                        let borderColor = '#3b82f6';
                        let textColor = '#ffffff';
                        let classNames = [];
                        let eventStart = event.start;
                        let eventEnd = event.end;
                        let eventTitle = event.title;

                        if (event.is_proposed) {
                            backgroundColor = '#10b981'; // Success green
                            borderColor = '#10b981';
                            classNames.push('proposed-event');
                            // Make proposed time take up a full hour
                            const startTime = new Date(event.start);
                            const endTime = new Date(startTime.getTime() + (60 * 60 * 1000)); // Add 1 hour
                            // Format end time in local format for FullCalendar
                            eventEnd = endTime.getFullYear() + '-' + 
                                      String(endTime.getMonth() + 1).padStart(2, '0') + '-' + 
                                      String(endTime.getDate()).padStart(2, '0') + 'T' + 
                                      String(endTime.getHours()).padStart(2, '0') + ':' + 
                                      String(endTime.getMinutes()).padStart(2, '0') + ':' + 
                                      String(endTime.getSeconds()).padStart(2, '0');
                        } else if (event.is_blockout_event) {
                            backgroundColor = '#ef4444'; // Error red
                            borderColor = '#ef4444';
                            classNames.push('blockout-event');
                        } else if (event.is_available_slot) {
                            backgroundColor = '#8b5cf6'; // Accent purple
                            borderColor = '#8b5cf6';
                            classNames.push('available-slot', 'cursor-pointer');
                            // For available slots, show only start time
                            const startTime = new Date(event.start);
                            const timeString = startTime.toLocaleTimeString('en-US', {
                                hour: 'numeric',
                                minute: '2-digit',
                                hour12: true
                            });
                            eventTitle = timeString;
                        } else {
                            // For already scheduled events, show only start time in title
                            const startTime = new Date(event.start);
                            const timeString = startTime.toLocaleTimeString('en-US', {
                                hour: 'numeric',
                                minute: '2-digit',
                                hour12: true
                            });
                            eventTitle = `${timeString} - ${event.title}`;
                            // Keep the actual event duration (use actual_end if available)
                            if (event.actual_end) {
                                eventEnd = event.actual_end;
                            }
                        }

                        return {
                            id: event.id,
                            title: eventTitle,
                            start: eventStart,
                            end: eventEnd,
                            backgroundColor: backgroundColor,
                            borderColor: borderColor,
                            textColor: textColor,
                            classNames: classNames,
                            extendedProps: {
                                isAvailableSlot: event.is_available_slot,
                                timeSlot: event.time_slot,
                                date: event.date,
                                isProposed: event.is_proposed,
                                isBlockout: event.is_blockout_event,
                                actualEnd: event.actual_end
                            }
                        };
                    }),
                    eventClick: function(info) {
                        if (info.event.extendedProps.isAvailableSlot) {
                            selectTimeSlot(info.event.extendedProps.date, info.event.extendedProps.timeSlot);
                        }
                    },
                    eventMouseEnter: function(info) {
                        if (info.event.extendedProps.isAvailableSlot) {
                            info.el.style.cursor = 'pointer';
                            info.el.style.opacity = '0.8';
                        }
                    },
                    eventMouseLeave: function(info) {
                        if (info.event.extendedProps.isAvailableSlot) {
                            info.el.style.opacity = '1';
                        }
                    }
                });

                weekCalendar.render();

            } catch (error) {
                console.error('Error loading week view:', error);
                document.getElementById('weekCalendar').style.display = 'none';
                document.getElementById('weekCalendarPlaceholder').style.display = 'block';
                document.getElementById('weekCalendarPlaceholder').innerHTML = `
                    <div class="text-center py-8 text-error">
                        <i class="fa-sharp fa-exclamation-triangle text-2xl mb-2"></i>
                        <p>Error loading week view</p>
                    </div>
                `;
            }
        }

        function selectTimeSlot(date, timeSlot) {
            // Update the form fields with the selected date and time
            document.getElementById('pickup_date').value = date;
            document.getElementById('pickup_time').value = timeSlot;
            
            // Refresh the week view to show the new proposed time
            updateWeekView();
            
            // Show a brief confirmation
            const toast = document.createElement('div');
            toast.className = 'toast toast-top toast-end z-50';
            toast.innerHTML = `
                <div class="alert alert-success">
                    <i class="fa-sharp fa-check-circle"></i>
                    <span>Time slot selected: ${new Date(date + ' ' + timeSlot).toLocaleString('en-US', {
                        weekday: 'short',
                        month: 'short', 
                        day: 'numeric',
                        hour: 'numeric',
                        minute: '2-digit',
                        hour12: true
                    })}</span>
                </div>
            `;
            document.body.appendChild(toast);
            
            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // Initialize calendar on page load if date/time are already set
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const pickupDate = document.getElementById('pickup_date').value;
                const pickupTime = document.getElementById('pickup_time').value;
                if (pickupDate && pickupTime) {
                    updateWeekView();
                }
            }, 100);
        });
    </script>
</x-app-layout>
