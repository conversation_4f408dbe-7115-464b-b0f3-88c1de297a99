<x-app-layout
    page-title="Step 1: Customer Identification"
    page-icon="fa-sharp fa-user-check"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Pickup Requests', 'route' => 'pickup-requests.index', 'icon' => 'fa-truck-pickup'],
        ['name' => 'Request #' . $pickupRequest->id, 'route' => 'pickup-requests.show', 'params' => [$pickupRequest], 'icon' => 'fa-file-lines'],
        ['name' => 'Customer Identification', 'icon' => 'fa-user-check']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Progress Steps -->
            <div class="card bg-base-100 shadow-md">
                <div class="p-6">
                    <ul class="steps steps-horizontal w-full">
                        <li class="step step-primary">Step 1: Customer</li>
                        <li class="step">Step 2: Time</li>
                        <li class="step">Step 3: Details</li>
                    </ul>
                </div>
            </div>

            <!-- Pickup Request Summary -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-info text-info-content w-8 rounded-lg">
                                <i class="fa-sharp fa-clipboard-list text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Pickup Request Summary</h4>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user text-primary"></i>
                            <span class="font-medium">Contact:</span>
                            <span>{{ $pickupRequest->contact_name }}</span>
                        </div>
                        @if($pickupRequest->business_name)
                            <div class="flex items-center gap-2">
                                <i class="fa-sharp fa-building text-secondary"></i>
                                <span class="font-medium">Business:</span>
                                <span>{{ $pickupRequest->business_name }}</span>
                            </div>
                        @endif
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-envelope text-accent"></i>
                            <span class="font-medium">Email:</span>
                            <span>{{ $pickupRequest->email }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-phone text-primary"></i>
                            <span class="font-medium">Phone:</span>
                            <span>{{ $pickupRequest->phone }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-box text-secondary"></i>
                            <span class="font-medium">Items:</span>
                            <span>{{ $pickupRequest->pickup_items }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-hashtag text-accent"></i>
                            <span class="font-medium">Quantity:</span>
                            <span>{{ $pickupRequest->pickup_quantity }}</span>
                        </div>
                    </div>
                    @if($pickupRequest->pickup_address)
                        <div class="mt-4 pt-4 border-t border-base-300/50">
                            <div class="flex items-start gap-2">
                                <i class="fa-sharp fa-map-marker-alt text-primary mt-0.5"></i>
                                <div>
                                    <span class="font-medium">Pickup Address:</span>
                                    <div class="text-base-content">{{ $pickupRequest->pickup_address }}</div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Customer Suggestions -->
            @if($customerSuggestions->count() > 0)
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-users text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Suggested Customer Matches</h4>
                        </div>
                    </div>

                    <div class="p-6 space-y-4">
                        <p class="text-sm text-base-content/70 text-center">
                            Found {{ $customerSuggestions->count() }} potential matches based on the pickup request information. Click to select a customer.
                        </p>

                        <div class="grid grid-cols-1 gap-4">
                            @foreach($customerSuggestions as $index => $customer)
                                <div class="card bg-base-200/50 border border-base-300 hover:border-primary/50 hover:bg-primary/5 cursor-pointer transition-all duration-200"
                                     onclick="selectCustomer({{ $customer->id }}, '{{ addslashes($customer->name) }}')">
                                    <div class="card-body p-4">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <div class="flex items-center gap-3 mb-2">
                                                    <h5 class="font-semibold text-lg text-base-content">{{ $customer->name }}</h5>
                                                    <div class="badge badge-primary badge-sm">Match {{ $index + 1 }}</div>
                                                    @if($customer->type)
                                                        <div class="badge badge-outline badge-sm">{{ $customer->type }}</div>
                                                    @endif
                                                    @if($customer->hasLicenseOnFile)
                                                        <div class="tooltip" data-tip="Photo ID on file">
                                                            <i class="fa-sharp fa-id-card text-success"></i>
                                                        </div>
                                                    @endif
                                                </div>
                                                
                                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                                    @if($customer->contact)
                                                        <div class="flex items-center gap-2">
                                                            <i class="fa-sharp fa-user text-primary"></i>
                                                            <span class="font-medium">Contact:</span>
                                                            <span>{{ $customer->contact }}</span>
                                                        </div>
                                                    @endif
                                                    @if($customer->email)
                                                        <div class="flex items-center gap-2">
                                                            <i class="fa-sharp fa-envelope text-secondary"></i>
                                                            <span class="font-medium">Email:</span>
                                                            <span>{{ $customer->email }}</span>
                                                        </div>
                                                    @endif
                                                    @if($customer->phone)
                                                        <div class="flex items-center gap-2">
                                                            <i class="fa-sharp fa-phone text-accent"></i>
                                                            <span class="font-medium">Phone:</span>
                                                            <span>{{ $customer->phone }}</span>
                                                        </div>
                                                    @endif
                                                    @if($customer->address)
                                                        <div class="flex items-start gap-2 md:col-span-2">
                                                            <i class="fa-sharp fa-map-marker-alt text-primary mt-0.5"></i>
                                                            <div>
                                                                <span class="font-medium">Address:</span>
                                                                <span>{{ Str::limit($customer->address, 80) }}</span>
                                                            </div>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fa-sharp fa-chevron-right text-base-content/30"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

            @else
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-warning/10 to-warning/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-warning text-warning-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-users text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">No Default Matches Found</h4>
                        </div>
                    </div>

                    <div class="p-6 space-y-4">
                        <p class="text-sm text-base-content/70 text-center">
                            No customers found matching the provided pickup request information. Please search for other customers or create a new one.
                        </p>
                    </div>
                </div>
            @endif

            <!-- Search for Other Customers -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-warning/10 to-warning/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-warning text-warning-content w-8 rounded-lg">
                                <i class="fa-sharp fa-magnifying-glass text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Search Other Customers</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">

                    <!-- Search Input -->
                    <div class="space-y-4">
                        <div>
                            <label class="label" for="customerSearch">
                                <span class="label-text">
                                    <i class="fa-sharp fa-search text-primary mr-2"></i>
                                    Search by name, business, email, or phone
                                </span>
                            </label>
                            <input type="text" 
                                   id="customerSearch" 
                                   class="input input-bordered w-full" 
                                   placeholder="Type to search for customers..."
                                   autocomplete="off">
                        </div>

                        <!-- Search Results -->
                        <div id="searchResults" class="space-y-2 hidden">
                            <!-- Dynamically populated -->
                        </div>
                    </div>

                    <!-- Create New Customer -->
                    <div class="divider">
                        <span class="text-base-content/50 font-medium">Or create new customer</span>
                    </div>

                    <div class="text-center">
                        <button onclick="openQuickCreateCustomer()" class="btn btn-primary gap-2">
                            <i class="fa-sharp fa-user-plus"></i>
                            Create New Customer
                        </button>
                        <p class="text-xs text-base-content/50 mt-2">
                            Pickup request details will be automatically filled in
                        </p>
                    </div>
                </div>
            </div>

            <!-- Selected Customer & Link Action -->
            <div class="card bg-base-100 shadow-md" id="linkSection" style="display: none;">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-link text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Link Customer</h4>
                    </div>
                </div>

                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-10 rounded-lg">
                                    <i class="fa-sharp fa-user text-sm"></i>
                                </div>
                            </div>
                            <div>
                                <p class="font-medium text-base-content" id="selectedCustomerName">Customer Selected</p>
                                <p class="text-sm text-base-content/60">Ready to link to pickup request</p>
                            </div>
                        </div>
                        <div class="flex gap-3">
                            <button onclick="clearSelection()" class="btn btn-outline gap-2">
                                <i class="fa-sharp fa-times"></i>
                                Change
                            </button>
                            <button onclick="linkCustomer()" class="btn btn-success gap-2" id="linkCustomerBtn">
                                <i class="fa-sharp fa-link"></i>
                                Link Customer
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="divider my-8">
                <span class="text-base-content/50 font-medium">Continue to scheduling</span>
            </div>

            <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                <a href="{{ route('pickup-requests.show', $pickupRequest) }}" class="btn btn-ghost gap-2">
                    <i class="fa-sharp fa-arrow-left"></i>
                    Back to Request
                </a>

                <div class="text-center">
                    <p class="text-sm text-base-content/60">
                        Select a customer above to continue to scheduling
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Create Customer Modal -->
    <x-pickup-quick-create-customer
        id="customerSearch"
        name="customer_id"
        :pickupRequest="$pickupRequest"
    />

    <script>
        let selectedCustomerId = null;
        let searchTimeout = null;

        function selectCustomer(customerId, customerName) {
            console.log('Selecting customer:', customerId, customerName);
            selectedCustomerId = customerId;
            document.getElementById('selectedCustomerName').textContent = customerName;
            document.getElementById('linkSection').style.display = 'block';

            console.log('Selected customer ID set to:', selectedCustomerId);

            // Scroll to link section
            document.getElementById('linkSection').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }

        function clearSelection() {
            selectedCustomerId = null;
            document.getElementById('linkSection').style.display = 'none';
        }

        async function linkCustomer() {
            if (!selectedCustomerId) {
                alert('Please select a customer first.');
                return;
            }

            console.log('Linking customer with ID:', selectedCustomerId);

            const linkBtn = document.getElementById('linkCustomerBtn');
            const originalContent = linkBtn.innerHTML;
            linkBtn.disabled = true;
            linkBtn.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Linking...';

            // Check for CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (!csrfToken) {
                console.error('CSRF token not found');
                alert('CSRF token not found. Please refresh the page and try again.');
                linkBtn.disabled = false;
                linkBtn.innerHTML = originalContent;
                return;
            }

            try {
                const url = `/pickup-requests/{{ $pickupRequest->id }}/link-customer`;
                console.log('Making request to:', url);
                console.log('Customer ID:', selectedCustomerId);
                console.log('CSRF Token:', csrfToken.getAttribute('content'));

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ customer_id: parseInt(selectedCustomerId) })
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Response not OK:', response.status, errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                console.log('Response data:', data);

                if (data.success) {
                    // Show success and redirect
                    const alert = document.createElement('div');
                    alert.className = 'alert alert-success fixed top-4 right-4 w-auto z-50 shadow-lg';
                    alert.innerHTML = `
                        <i class="fa-sharp fa-check-circle"></i>
                        <span>${data.message}</span>
                    `;
                    document.body.appendChild(alert);

                    setTimeout(() => {
                        window.location.href = '{{ route("pickup-requests.step2", $pickupRequest) }}';
                    }, 2000);
                } else {
                    console.error('Server returned error:', data);
                    alert('Error: ' + (data.message || 'Unknown error occurred'));
                    linkBtn.disabled = false;
                    linkBtn.innerHTML = originalContent;
                }
            } catch (error) {
                console.error('Error linking customer:', error);
                alert('An error occurred while linking the customer: ' + error.message);
                linkBtn.disabled = false;
                linkBtn.innerHTML = originalContent;
            }
        }

        // Search functionality
        document.getElementById('customerSearch').addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length === 0) {
                document.getElementById('searchResults').classList.add('hidden');
                return;
            }

            searchTimeout = setTimeout(async () => {
                try {
                    const response = await fetch(`/pickup-requests/{{ $pickupRequest->id }}/search-customers?query=${encodeURIComponent(query)}`);
                    const customers = await response.json();

                    const resultsContainer = document.getElementById('searchResults');

                    if (customers.length > 0) {
                        resultsContainer.innerHTML = customers.map(customer => `
                            <div class="card bg-base-200/50 border border-base-300 hover:border-primary/50 hover:bg-primary/5 cursor-pointer transition-all duration-200"
                                 onclick="selectCustomer(${customer.id}, '${customer.name.replace(/'/g, "\\'")}')">
                                <div class="card-body p-3">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h6 class="font-medium">${customer.name}</h6>
                                            <p class="text-sm text-base-content/60">${customer.email || 'No email'} • ${customer.phone || 'No phone'}</p>
                                        </div>
                                        <i class="fa-sharp fa-chevron-right text-base-content/30"></i>
                                    </div>
                                </div>
                            </div>
                        `).join('');
                        resultsContainer.classList.remove('hidden');
                    } else {
                        resultsContainer.innerHTML = `
                            <div class="text-center p-4 text-base-content/60">
                                <p>No customers found matching "${query}"</p>
                            </div>
                        `;
                        resultsContainer.classList.remove('hidden');
                    }
                } catch (error) {
                    console.error('Search error:', error);
                }
            }, 300);
        });
    </script>
</x-app-layout>
