<!-- Customer Selection Card -->
<div class="card bg-base-100 shadow-md">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
        <div class="flex items-center gap-3">
            <div class="avatar avatar-placeholder">
                <div class="bg-primary text-primary-content w-8 rounded-lg">
                    <i class="fa-sharp fa-user text-sm"></i>
                </div>
            </div>
            <h4 class="text-lg font-semibold text-base-content">Customer Selection</h4>
        </div>
    </div>

    <!-- Content Section -->
    <div class="p-6">
        <div class="flex items-center gap-2 mb-4">
            <i class="fa-sharp fa-circle-info text-primary"></i>
            <p class="text-sm text-base-content/70">
                @if(isset($isEdit) && $isEdit)
                    Change the customer for this pickup request. If the customer doesn't exist, you can create them first.
                @else
                    Search for and select an existing customer. If the customer doesn't exist, you can create them first.
                @endif
            </p>
        </div>

        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-search text-primary mr-2"></i>
                Select Customer *
            </legend>
            <x-dynamic-customer-search
                id="customerSearch"
                name="customer_id"
                placeholder="Search by business name, contact name, email, or phone..."
                action="form"
                quickCreateButtonId="quickCreateCustomerBtn"
                :selectedId="$selectedCustomerId ?? null"
                :selectedName="$selectedCustomerName ?? null"
            />
            @error('customer_id')
                <div class="text-error text-sm mt-1">{{ $message }}</div>
            @enderror
        </fieldset>

        <!-- Quick Create Button -->
        <div class="mt-4">
            <x-quick-create-customer
                id="customerSearch"
                name="customer_id"
            />
        </div>
    </div>
</div>
