<!-- Contact Information Card -->
<div class="card bg-base-100 shadow-md">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
        <div class="flex items-center gap-3">
            <div class="avatar avatar-placeholder">
                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                    <i class="fa-sharp fa-address-card text-sm"></i>
                </div>
            </div>
            <h4 class="text-lg font-semibold text-base-content">Contact Information</h4>
        </div>
    </div>

    <!-- Content Section -->
    <div class="p-6">
        <div class="flex items-center gap-2 mb-4">
            <i class="fa-sharp fa-circle-info text-secondary"></i>
            <p class="text-sm text-base-content/70">This information will be used for pickup coordination and may differ from the customer's profile.</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <fieldset class="fieldset">
                <legend class="fieldset-legend">
                    <i class="fa-sharp fa-user text-secondary mr-2"></i>
                    Contact Name *
                </legend>
                <div class="relative">
                    <input type="text" name="contact_name" id="contact_name"
                           class="input input-bordered w-full auto-save"
                           data-field="contact_name"
                           placeholder="Contact person's full name"
                           value="{{ old('contact_name', $contactName ?? '') }}" required>
                    <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                        <i class="fa-sharp fa-spinner fa-spin text-secondary"></i>
                    </span>
                </div>
                @error('contact_name')
                    <div class="text-error text-sm mt-1">{{ $message }}</div>
                @enderror
            </fieldset>

            <fieldset class="fieldset">
                <legend class="fieldset-legend">
                    <i class="fa-sharp fa-building text-secondary mr-2"></i>
                    Business Name
                </legend>
                <div class="relative">
                    <input type="text" name="business_name" id="business_name"
                           class="input input-bordered w-full auto-save"
                           data-field="business_name"
                           placeholder="Optional business name"
                           value="{{ old('business_name', $businessName ?? '') }}">
                    <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                        <i class="fa-sharp fa-spinner fa-spin text-secondary"></i>
                    </span>
                </div>
                @error('business_name')
                    <div class="text-error text-sm mt-1">{{ $message }}</div>
                @enderror
            </fieldset>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <fieldset class="fieldset">
                <legend class="fieldset-legend">
                    <i class="fa-sharp fa-envelope text-secondary mr-2"></i>
                    Email Address *
                </legend>
                <div class="relative">
                    <input type="email" name="email" id="email"
                           class="input input-bordered w-full auto-save"
                           data-field="email"
                           placeholder="<EMAIL>"
                           value="{{ old('email', $email ?? '') }}" required>
                    <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                        <i class="fa-sharp fa-spinner fa-spin text-secondary"></i>
                    </span>
                </div>
                @error('email')
                    <div class="text-error text-sm mt-1">{{ $message }}</div>
                @enderror
            </fieldset>

            <fieldset class="fieldset">
                <legend class="fieldset-legend">
                    <i class="fa-sharp fa-phone text-secondary mr-2"></i>
                    Phone Number *
                </legend>
                <div class="relative">
                    <input type="tel" name="phone" id="phone"
                           class="input input-bordered w-full auto-save"
                           data-field="phone"
                           placeholder="(*************"
                           value="{{ old('phone', $phone ?? '') }}" required>
                    <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                        <i class="fa-sharp fa-spinner fa-spin text-secondary"></i>
                    </span>
                </div>
                @error('phone')
                    <div class="text-error text-sm mt-1">{{ $message }}</div>
                @enderror
            </fieldset>
        </div>
    </div>
</div>
