<!-- Event Details Card -->
<div class="card bg-base-100 shadow-md">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-warning/10 to-warning/5 px-6 py-4 border-b border-base-300/50">
        <div class="flex items-center gap-3">
            <div class="avatar avatar-placeholder">
                <div class="bg-warning text-warning-content w-8 rounded-lg">
                    <i class="fa-sharp fa-users text-sm"></i>
                </div>
            </div>
            <h4 class="text-lg font-semibold text-base-content">Event Details</h4>
        </div>
    </div>

    <!-- Content Section -->
    <div class="p-6 space-y-4">
        <div class="flex items-center gap-2 mb-4">
            <i class="fa-sharp fa-circle-info text-warning"></i>
            <p class="text-sm text-base-content/70">
                @if(isset($isEdit) && $isEdit)
                    Update the pickup event details. Changes will be applied to the linked calendar event.
                @else
                    Configure the pickup event details. The event will be created automatically when you submit this request.
                @endif
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <fieldset class="fieldset">
                <legend class="fieldset-legend">
                    <i class="fa-sharp fa-users text-warning mr-2"></i>
                    Staff Needed
                </legend>
                <div class="relative">
                    <select name="staff_needed" id="staff_needed"
                            class="select select-bordered w-full auto-save"
                            data-field="staff_needed">
                        @for($i = 1; $i <= 5; $i++)
                            <option value="{{ $i }}" {{ old('staff_needed', $staffNeeded ?? '1') == $i ? 'selected' : '' }}>
                                {{ $i }} {{ $i == 1 ? 'person' : 'people' }}
                            </option>
                        @endfor
                    </select>
                    <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                        <i class="fa-sharp fa-spinner fa-spin text-warning"></i>
                    </span>
                </div>
                @error('staff_needed')
                    <div class="text-error text-sm mt-1">{{ $message }}</div>
                @enderror
            </fieldset>

            <fieldset class="fieldset">
                <legend class="fieldset-legend">
                    <i class="fa-sharp fa-user-tie text-warning mr-2"></i>
                    Assigned Driver
                </legend>
                <div class="relative">
                    <select name="assigned_driver_id" id="assigned_driver_id"
                            class="select select-bordered w-full auto-save"
                            data-field="assigned_driver_id">
                        <option value="">No specific driver assigned</option>
                        @foreach($drivers ?? [] as $driver)
                            <option value="{{ $driver->id }}" {{ old('assigned_driver_id', $assignedDriverId ?? '') == $driver->id ? 'selected' : '' }}>
                                {{ $driver->name }}
                            </option>
                        @endforeach
                    </select>
                    <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                        <i class="fa-sharp fa-spinner fa-spin text-warning"></i>
                    </span>
                </div>
                <div class="text-sm text-base-content/60 mt-1">Optional</div>
                @error('assigned_driver_id')
                    <div class="text-error text-sm mt-1">{{ $message }}</div>
                @enderror
            </fieldset>
        </div>

        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-sticky-note text-warning mr-2"></i>
                Internal Pickup Notes
            </legend>
            <div class="relative">
                <textarea name="internal_pickup_notes" id="internal_pickup_notes"
                          class="textarea textarea-bordered h-24 w-full auto-save"
                          data-field="internal_pickup_notes"
                          placeholder="Add internal notes about this pickup (e.g., special instructions, access codes, staff reminders)">{{ old('internal_pickup_notes', $internalPickupNotes ?? '') }}</textarea>
                <span class="saving-indicator absolute right-3 top-3 hidden">
                    <i class="fa-sharp fa-spinner fa-spin text-warning"></i>
                </span>
            </div>
            <div class="text-sm text-base-content/60 mt-1">Internal notes only visible to staff</div>
            @error('internal_pickup_notes')
                <div class="text-error text-sm mt-1">{{ $message }}</div>
            @enderror
        </fieldset>
    </div>
</div>
