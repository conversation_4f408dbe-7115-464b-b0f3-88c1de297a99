<!-- Item Details Card -->
<div class="card bg-base-100 shadow-md">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-warning/10 to-warning/5 px-6 py-4 border-b border-base-300/50">
        <div class="flex items-center gap-3">
            <div class="avatar avatar-placeholder">
                <div class="bg-warning text-warning-content w-8 rounded-lg">
                    <i class="fa-sharp fa-boxes text-sm"></i>
                </div>
            </div>
            <h4 class="text-lg font-semibold text-base-content">Item Details</h4>
        </div>
    </div>

    <!-- Content Section -->
    <div class="p-6 space-y-6">
        <!-- Load Size Selection -->
        <fieldset class="fieldset bg-base-100 border-base-300 rounded-box border p-4 relative">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-truck-loading text-warning mr-2"></i>
                How much are we picking up? *
            </legend>
            <div class="space-y-4">
                <div class="form-control">
                    <label class="label cursor-pointer justify-start gap-3">
                        <input type="radio" name="load_size" value="small" class="radio radio-primary auto-save"
                               data-field="load_size" {{ old('load_size', $loadSize ?? '') == 'small' ? 'checked' : '' }} required>
                        <div class="flex-1">
                            <div class="font-bold text-base-content">Small Load</div>
                            <p class="text-sm text-base-content/80 leading-relaxed mt-1 text-wrap">
                                One or two flatscreen TVs, a few computers, small appliances, microwave, etc. (fits in midsize SUV or less)
                            </p>
                            <p class="text-xs text-base-content/60 mt-1">
                                Most residential pickups are this size
                            </p>
                        </div>
                    </label>
                </div>

                <div class="form-control">
                    <label class="label cursor-pointer justify-start gap-3">
                        <input type="radio" name="load_size" value="medium" class="radio radio-primary auto-save"
                               data-field="load_size" {{ old('load_size', $loadSize ?? '') == 'medium' ? 'checked' : '' }} required>
                        <div class="flex-1">
                            <div class="font-bold text-base-content">Medium Load</div>
                            <p class="text-sm text-base-content/80 leading-relaxed mt-1 text-wrap">
                                A few pallets, totes, gaylords that are ready to be picked up, but can still be reasonably moved by 1 person with handtruck, pallet jack, carts, etc.
                            </p>
                        </div>
                    </label>
                </div>

                <div class="form-control">
                    <label class="label cursor-pointer justify-start gap-3">
                        <input type="radio" name="load_size" value="large" class="radio radio-primary auto-save"
                               data-field="load_size" {{ old('load_size', $loadSize ?? '') == 'large' ? 'checked' : '' }} required>
                        <div class="flex-1">
                            <div class="font-bold text-base-content">Large or Heavy Load</div>
                            <p class="text-sm text-base-content/80 leading-relaxed mt-1 text-wrap">
                                Heavy objects like a piano, conference system, slot machines, etc. that likely require 2 or more people to pickup, or large loads like storage unit cleanouts, overflowing totes, or any other service requiring specialized handling
                            </p>
                        </div>
                    </label>
                </div>
            </div>

            <span class="saving-indicator absolute right-3 top-3 hidden">
                <i class="fa-sharp fa-spinner fa-spin text-warning"></i>
            </span>
            @error('load_size')
                <div class="text-error text-sm mt-2">{{ $message }}</div>
            @enderror
        </fieldset>

        <!-- Item Types -->
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-boxes text-warning mr-2"></i>
                What types of items are we picking up? *
            </legend>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                @php
                    $itemTypes = old('item_types', $itemTypes ?? []);
                    if (is_string($itemTypes)) {
                        $itemTypes = json_decode($itemTypes, true) ?? [];
                    }
                @endphp

                @foreach($pickupItemTypes ?? [] as $itemType)
                    <div class="form-control">
                        <label class="item-type-button cursor-pointer p-3 border-2 border-base-300 rounded-lg hover:border-primary hover:bg-primary/5 transition-all duration-200 flex flex-col items-center text-center auto-save-checkbox"
                               data-value="{{ $itemType['value'] }}"
                               data-notes="{{ $itemType['notes'] ?? '' }}"
                               data-field="item_types">
                            <input type="checkbox" name="item_types[]" value="{{ $itemType['value'] }}"
                                   class="hidden item-type-checkbox auto-save-checkbox"
                                   data-field="item_types"
                                   {{ in_array($itemType['value'], $itemTypes) ? 'checked' : '' }}>
                            <div class="relative">
                                <i class="{{ $itemType['icon'] ?? 'fa-sharp fa-question' }} text-2xl text-base-content mb-2"></i>
                                <div class="checkmark absolute -top-1 -right-1 w-5 h-5 bg-primary text-primary-content rounded-full items-center justify-center text-xs {{ in_array($itemType['value'], $itemTypes) ? 'flex' : 'hidden' }}">
                                    <i class="fa-sharp fa-check"></i>
                                </div>
                            </div>
                            <span class="label-text text-xs font-medium">{{ $itemType['name'] }}</span>
                        </label>
                    </div>
                @endforeach
            </div>

            <!-- Notes Display -->
            <div id="item-notes-display" class="mt-4 hidden">
                <div class="alert alert-info">
                    <i class="fa-sharp fa-info-circle"></i>
                    <div>
                        <strong>Important Information for Selected Items:</strong>
                        <ul id="item-notes-list" class="list-disc list-inside mt-2 space-y-1"></ul>
                    </div>
                </div>
            </div>
            <span class="saving-indicator absolute right-3 top-3 hidden">
                <i class="fa-sharp fa-spinner fa-spin text-warning"></i>
            </span>
            @error('item_types')
                <div class="text-error text-sm mt-1">{{ $message }}</div>
            @enderror
            <div class="text-sm text-base-content/60 mt-2">
                <i class="fa-sharp fa-info-circle mr-1"></i>
                Select all that apply
            </div>
        </fieldset>

        <!-- Item Specifics -->
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-list-ul text-warning mr-2"></i>
                Additional Item Specifics *
            </legend>
            <div class="relative">
                <textarea name="item_specifics" id="item_specifics"
                          class="textarea textarea-bordered h-32 w-full auto-save"
                          data-field="item_specifics"
                          placeholder="Please provide additional details about the items (e.g., swap out 4 totes of computers, one 55&quot; flatscreen, roughly 30 desktop PCs, etc.)" 
                          required>{{ old('item_specifics', $itemSpecifics ?? '') }}</textarea>
                <span class="saving-indicator absolute right-3 top-3 hidden">
                    <i class="fa-sharp fa-spinner fa-spin text-warning"></i>
                </span>
            </div>
            @error('item_specifics')
                <div class="text-error text-sm mt-1">{{ $message }}</div>
            @enderror
        </fieldset>

        <!-- Accessibility Level -->
        <fieldset class="fieldset bg-base-100 border-base-300 rounded-box border p-4 relative">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-universal-access text-warning mr-2"></i>
                Are the items easily accessible? *
            </legend>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <label class="card bg-green-50 hover:bg-green-100 border-2 border-green-200 hover:border-green-300 cursor-pointer transition-all duration-200 p-4">
                    <input type="radio" name="accessibility_level" value="easy" class="radio radio-success auto-save sr-only"
                           data-field="accessibility_level" {{ old('accessibility_level', $accessibilityLevel ?? '') == 'easy' ? 'checked' : '' }} required>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-green-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa-sharp fa-face-smile text-xl"></i>
                        </div>
                        <div class="font-bold text-green-800 mb-2">Easy Access</div>
                        <p class="text-sm text-green-700 leading-relaxed">
                            First level storage room, garage, outside, front room, etc.
                        </p>
                    </div>
                </label>

                <label class="card bg-orange-50 hover:bg-orange-100 border-2 border-orange-200 hover:border-orange-300 cursor-pointer transition-all duration-200 p-4">
                    <input type="radio" name="accessibility_level" value="moderate" class="radio radio-warning auto-save sr-only"
                           data-field="accessibility_level" {{ old('accessibility_level', $accessibilityLevel ?? '') == 'moderate' ? 'checked' : '' }} required>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa-sharp fa-face-meh text-xl"></i>
                        </div>
                        <div class="font-bold text-orange-800 mb-2">Moderate Access</div>
                        <p class="text-sm text-orange-700 leading-relaxed">
                            Some stairs or obstacles, but manageable
                        </p>
                    </div>
                </label>

                <label class="card bg-red-50 hover:bg-red-100 border-2 border-red-200 hover:border-red-300 cursor-pointer transition-all duration-200 p-4">
                    <input type="radio" name="accessibility_level" value="difficult" class="radio radio-error auto-save sr-only"
                           data-field="accessibility_level" {{ old('accessibility_level', $accessibilityLevel ?? '') == 'difficult' ? 'checked' : '' }} required>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-red-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa-sharp fa-face-tired text-xl"></i>
                        </div>
                        <div class="font-bold text-red-800 mb-2">Difficult Access</div>
                        <p class="text-sm text-red-700 leading-relaxed">
                            Multiple stairs, tight spaces, or heavy obstacles
                        </p>
                    </div>
                </label>
            </div>

            <span class="saving-indicator absolute right-3 top-3 hidden">
                <i class="fa-sharp fa-spinner fa-spin text-warning"></i>
            </span>
            @error('accessibility_level')
                <div class="text-error text-sm mt-2">{{ $message }}</div>
            @enderror
            <div class="text-sm text-warning mt-4">
                <i class="fa-sharp fa-exclamation-triangle mr-1"></i>
                Extra fees may apply for difficult access locations
            </div>
        </fieldset>

        <!-- Driver Instructions -->
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-route text-warning mr-2"></i>
                Driver Instructions
            </legend>
            <div class="relative">
                <textarea name="driver_instructions" id="driver_instructions"
                          class="textarea textarea-bordered h-24 w-full auto-save"
                          data-field="driver_instructions"
                          placeholder="Any special instructions for the driver (e.g., call when arriving, use back entrance, etc.)">{{ old('driver_instructions', $driverInstructions ?? '') }}</textarea>
                <span class="saving-indicator absolute right-3 top-3 hidden">
                    <i class="fa-sharp fa-spinner fa-spin text-warning"></i>
                </span>
            </div>
            <div class="text-sm text-base-content/60 mt-1">Optional</div>
            @error('driver_instructions')
                <div class="text-error text-sm mt-1">{{ $message }}</div>
            @enderror
        </fieldset>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Setup item types with icon buttons
    const itemTypeButtons = document.querySelectorAll('.item-type-button');
    const notesDisplay = document.getElementById('item-notes-display');
    const notesList = document.getElementById('item-notes-list');

    const updateNotesDisplay = () => {
        // Get all selected items with notes
        const selectedItemsWithNotes = Array.from(itemTypeButtons)
            .filter(btn => btn.querySelector('.item-type-checkbox').checked && btn.dataset.notes && btn.dataset.notes.trim())
            .map(btn => ({
                name: btn.querySelector('.label-text').textContent,
                notes: btn.dataset.notes
            }));

        if (selectedItemsWithNotes.length > 0) {
            // Clear existing notes
            notesList.innerHTML = '';

            // Add each item's notes to the list
            selectedItemsWithNotes.forEach(item => {
                const listItem = document.createElement('li');
                listItem.innerHTML = `<strong>${item.name}:</strong> ${item.notes}`;
                notesList.appendChild(listItem);
            });

            notesDisplay.classList.remove('hidden');
        } else {
            notesDisplay.classList.add('hidden');
        }
    };

    itemTypeButtons.forEach(button => {
        const checkbox = button.querySelector('.item-type-checkbox');
        const checkmark = button.querySelector('.checkmark');

        // Set initial state
        if (checkbox.checked) {
            button.classList.add('border-primary', 'bg-primary/10');
            button.classList.remove('border-base-300');
            checkmark.classList.remove('hidden');
            checkmark.classList.add('flex');
        }

        button.addEventListener('click', (e) => {
            e.preventDefault();

            // Toggle checkbox state
            checkbox.checked = !checkbox.checked;

            // Update visual state
            if (checkbox.checked) {
                button.classList.add('border-primary', 'bg-primary/10');
                button.classList.remove('border-base-300');
                checkmark.classList.remove('hidden');
                checkmark.classList.add('flex');
            } else {
                button.classList.remove('border-primary', 'bg-primary/10');
                button.classList.add('border-base-300');
                checkmark.classList.add('hidden');
                checkmark.classList.remove('flex');
            }

            // Update notes display
            updateNotesDisplay();

            // Handle auto-save for item types
            const fieldName = 'item_types';
            const allCheckboxes = document.querySelectorAll('input[name="item_types[]"]:checked');
            const values = Array.from(allCheckboxes).map(cb => cb.value);

            // Find the saving indicator for this fieldset
            const fieldset = button.closest('fieldset');
            const indicator = fieldset ? fieldset.querySelector('.saving-indicator') : null;

            // Call the global saveField function if it exists
            if (typeof saveField === 'function') {
                saveField(fieldName, values, indicator);
            }

            // Trigger change event for form validation
            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
        });
    });

    // Initialize notes display on page load
    updateNotesDisplay();

    // Handle auto-save for other checkboxes (non-item-type)
    const otherCheckboxes = document.querySelectorAll('.auto-save-checkbox:not(.item-type-checkbox)');
    otherCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // Get all checked values for this field
            const fieldName = this.dataset.field;
            const allCheckboxes = document.querySelectorAll(`input[name="${fieldName}[]"]:checked`);
            const values = Array.from(allCheckboxes).map(cb => cb.value);

            // Find the saving indicator for this fieldset
            const fieldset = this.closest('fieldset');
            const indicator = fieldset ? fieldset.querySelector('.saving-indicator') : null;

            // Call the global saveField function if it exists
            if (typeof saveField === 'function') {
                saveField(fieldName, values, indicator);
            }
        });
    });

    // Handle auto-save for radio buttons (load_size and accessibility_level)
    const radioButtons = document.querySelectorAll('input[type="radio"].auto-save');
    radioButtons.forEach(radio => {
        radio.addEventListener('change', function() {
            const fieldName = this.dataset.field;
            const value = this.value;

            // Find the saving indicator for this fieldset
            const fieldset = this.closest('fieldset');
            const indicator = fieldset ? fieldset.querySelector('.saving-indicator') : null;

            // Update visual selection for accessibility cards
            if (fieldName === 'accessibility_level') {
                // Remove selected state from all cards
                fieldset.querySelectorAll('label').forEach(label => {
                    label.classList.remove('ring-2', 'ring-offset-2');
                    if (label.classList.contains('bg-green-50')) {
                        label.classList.remove('ring-green-500', 'bg-green-100');
                        label.classList.add('bg-green-50');
                    } else if (label.classList.contains('bg-orange-50')) {
                        label.classList.remove('ring-orange-500', 'bg-orange-100');
                        label.classList.add('bg-orange-50');
                    } else if (label.classList.contains('bg-red-50')) {
                        label.classList.remove('ring-red-500', 'bg-red-100');
                        label.classList.add('bg-red-50');
                    }
                });
                
                // Add selected state to current card
                const selectedLabel = this.closest('label');
                selectedLabel.classList.add('ring-2', 'ring-offset-2');
                if (selectedLabel.classList.contains('bg-green-50')) {
                    selectedLabel.classList.add('ring-green-500', 'bg-green-100');
                    selectedLabel.classList.remove('bg-green-50');
                } else if (selectedLabel.classList.contains('bg-orange-50')) {
                    selectedLabel.classList.add('ring-orange-500', 'bg-orange-100');
                    selectedLabel.classList.remove('bg-orange-50');
                } else if (selectedLabel.classList.contains('bg-red-50')) {
                    selectedLabel.classList.add('ring-red-500', 'bg-red-100');
                    selectedLabel.classList.remove('bg-red-50');
                }
            }

            // Call the global saveField function if it exists
            if (typeof saveField === 'function') {
                saveField(fieldName, value, indicator);
            }
        });
        
        // Set initial selected state for accessibility cards
        if (radio.checked && radio.dataset.field === 'accessibility_level') {
            const selectedLabel = radio.closest('label');
            selectedLabel.classList.add('ring-2', 'ring-offset-2');
            if (selectedLabel.classList.contains('bg-green-50')) {
                selectedLabel.classList.add('ring-green-500', 'bg-green-100');
                selectedLabel.classList.remove('bg-green-50');
            } else if (selectedLabel.classList.contains('bg-orange-50')) {
                selectedLabel.classList.add('ring-orange-500', 'bg-orange-100');
                selectedLabel.classList.remove('bg-orange-50');
            } else if (selectedLabel.classList.contains('bg-red-50')) {
                selectedLabel.classList.add('ring-red-500', 'bg-red-100');
                selectedLabel.classList.remove('bg-red-50');
            }
        }
    });
});
</script>
