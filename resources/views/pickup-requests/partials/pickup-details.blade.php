<!-- Pickup Details Card -->
<div class="card bg-base-100 shadow-md">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
        <div class="flex items-center gap-3">
            <div class="avatar avatar-placeholder">
                <div class="bg-success text-success-content w-8 rounded-lg">
                    <i class="fa-sharp fa-map-marker-alt text-sm"></i>
                </div>
            </div>
            <h4 class="text-lg font-semibold text-base-content">Pickup Details</h4>
        </div>
    </div>

    <!-- Content Section -->
    <div class="p-6 space-y-4">
        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-map-marker-alt text-success mr-2"></i>
                Pickup Address *
            </legend>

            <x-google-maps-autocomplete
                id="pickup_address"
                name="pickup_address"
                :value="old('pickup_address', $pickupAddress ?? '')"
                placeholder="Start typing to search for an address..."
                type="textarea"
                label=""
                icon="fa-map-marker-alt"
                iconColor="text-success"
                :restrictions="['us', 'ca']"
                :showDisplayMode="true"
                displayLabel="Pickup Address"
                changeButtonText="Change"
                :autoSave="true"
                autoSaveField="pickup_address"
                :required="true"
            />

            @error('pickup_address')
                <div class="text-error text-sm mt-1">{{ $message }}</div>
            @enderror
        </fieldset>

        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-boxes text-success mr-2"></i>
                What are we picking up? *
            </legend>
            <div class="relative">
                <textarea name="pickup_items" id="pickup_items"
                          class="textarea textarea-bordered h-24 w-full auto-save"
                          data-field="pickup_items"
                          placeholder="Describe the items to be picked up (e.g., electronics, furniture, appliances, etc.)"
                          required>{{ old('pickup_items', $pickupItems ?? '') }}</textarea>
                <span class="saving-indicator absolute right-3 top-3 hidden">
                    <i class="fa-sharp fa-spinner fa-spin text-success"></i>
                </span>
            </div>
            @error('pickup_items')
                <div class="text-error text-sm mt-1">{{ $message }}</div>
            @enderror
        </fieldset>

        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-weight-hanging text-success mr-2"></i>
                How much are we picking up? *
            </legend>
            <div class="relative">
                <textarea name="pickup_quantity" id="pickup_quantity"
                          class="textarea textarea-bordered h-20 w-full auto-save"
                          data-field="pickup_quantity"
                          placeholder="Estimated quantity, weight, or size (e.g., 5 boxes, 1 large desk, approximately 50 lbs)"
                          required>{{ old('pickup_quantity', $pickupQuantity ?? '') }}</textarea>
                <span class="saving-indicator absolute right-3 top-3 hidden">
                    <i class="fa-sharp fa-spinner fa-spin text-success"></i>
                </span>
            </div>
            @error('pickup_quantity')
                <div class="text-error text-sm mt-1">{{ $message }}</div>
            @enderror
        </fieldset>

        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-home text-success mr-2"></i>
                Where on the property are the items located? *
            </legend>
            <div class="relative">
                <textarea name="property_location_details" id="property_location_details"
                          class="textarea textarea-bordered h-24 w-full auto-save"
                          data-field="property_location_details"
                          placeholder="Please describe the location and accessibility (e.g., garage, basement, second floor, stairs involved, etc.). Note: Extra fees may apply for moving large items from difficult locations."
                          required>{{ old('property_location_details', $propertyLocationDetails ?? '') }}</textarea>
                <span class="saving-indicator absolute right-3 top-3 hidden">
                    <i class="fa-sharp fa-spinner fa-spin text-success"></i>
                </span>
            </div>
            @error('property_location_details')
                <div class="text-error text-sm mt-1">{{ $message }}</div>
            @enderror
        </fieldset>

        <fieldset class="fieldset">
            <legend class="fieldset-legend">
                <i class="fa-sharp fa-sticky-note text-success mr-2"></i>
                Other things to know
            </legend>
            <div class="relative">
                <textarea name="other_notes" id="other_notes"
                          class="textarea textarea-bordered h-24 w-full auto-save"
                          data-field="other_notes"
                          placeholder="Any security codes, special instructions, who to call when we arrive, or other important details">{{ old('other_notes', $otherNotes ?? '') }}</textarea>
                <span class="saving-indicator absolute right-3 top-3 hidden">
                    <i class="fa-sharp fa-spinner fa-spin text-success"></i>
                </span>
            </div>
            <div class="text-sm text-base-content/60 mt-1">Optional</div>
            @error('other_notes')
                <div class="text-error text-sm mt-1">{{ $message }}</div>
            @enderror
        </fieldset>
    </div>
</div>
