<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Daily Pickup Schedule - {{ $dateFormatted }}</title>
    <style>
        @page {
            margin: 0.5in;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
        }
        .header {
            border-bottom: 3px solid #3B82F6;
            padding-bottom: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            color: #1F2937;
            font-size: 20pt;
            font-weight: bold;
        }
        .header .subtitle {
            margin-top: 8px;
            color: #6B7280;
            font-size: 16pt;
        }
        .header .meta-info {
            margin-top: 15px;
            font-size: 10pt;
            color: #9CA3AF;
        }
        .summary-stats {
            background-color: #F3F4F6;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        .summary-stats .stat {
            display: inline-block;
            margin: 0 20px;
            font-size: 14pt;
        }
        .summary-stats .stat-number {
            font-weight: bold;
            color: #3B82F6;
            font-size: 18pt;
        }
        .pickup-item {
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
            page-break-inside: avoid;
        }
        .pickup-item.pending {
            border-left: 4px solid #DC2626;
            background-color: #FFFFFF;
        }
        .pickup-header {
            display: table;
            width: 100%;
            margin-bottom: 15px;
            border-bottom: 1px solid #E5E7EB;
            padding-bottom: 10px;
        }
        .pickup-time {
            display: table-cell;
            width: 25%;
            vertical-align: top;
            font-weight: bold;
            color: #3B82F6;
            font-size: 14pt;
        }
        .pickup-status {
            display: table-cell;
            width: 15%;
            vertical-align: top;
            text-align: center;
        }
        .pickup-customer {
            display: table-cell;
            width: 60%;
            vertical-align: top;
            text-align: right;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 9pt;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-confirmed {
            background-color: #D1FAE5;
            color: #065F46;
        }
        .status-pending {
            background-color: #FEE2E2;
            color: #991B1B;
        }
        .status-incoming {
            background-color: #F3E8FF;
            color: #6B21A8;
        }
        .status-cancelled {
            background-color: #FEE2E2;
            color: #991B1B;
        }
        .pickup-details {
            display: table;
            width: 100%;
        }
        .detail-section {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding-right: 15px;
        }
        .detail-section:last-child {
            padding-right: 0;
            padding-left: 15px;
        }
        .detail-label {
            font-weight: bold;
            color: #374151;
            margin-bottom: 5px;
            font-size: 10pt;
        }
        .detail-value {
            margin-bottom: 15px;
            color: #6B7280;
            font-size: 10pt;
            line-height: 1.3;
        }
        .address-block {
            background-color: #F9FAFB;
            padding: 10px;
            border-radius: 4px;
            border-left: 3px solid #3B82F6;
        }
        .items-block {
            background-color: #FFFBEB;
            padding: 10px;
            border-radius: 4px;
            border-left: 3px solid #F59E0B;
        }
        .travel-block {
            background-color: #F0F9FF;
            padding: 10px;
            border-radius: 4px;
            border-left: 3px solid #0EA5E9;
        }
        .leave-time {
            font-weight: bold;
            color: #0EA5E9;
            font-size: 11pt;
        }
        .no-pickups {
            text-align: center;
            padding: 40px;
            color: #6B7280;
            font-style: italic;
            font-size: 14pt;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #E5E7EB;
            text-align: center;
            font-size: 9pt;
            color: #9CA3AF;
        }
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Daily Pickup Schedule</h1>
        <div class="subtitle">{{ $dateFormatted }}</div>
        <div class="meta-info">
            Generated on {{ $generatedAt->format('M j, Y \a\t g:i A T') }} by {{ $generatedBy }}
            @if($statusFilter)
                <br>Filtered by status: {{ ucfirst($statusFilter) }}
            @endif
        </div>
    </div>

    <div class="summary-stats">
        <div class="stat">
            <div class="stat-number">{{ $totalPickups }}</div>
            <div>Total Pickups</div>
        </div>
        @if($pickupRequests->count() > 0)
            <div class="stat">
                <div class="stat-number">{{ $pickupRequests->where('status', 'confirmed')->count() }}</div>
                <div>Confirmed</div>
            </div>
            @if($pickupRequests->where('status', 'pending')->count() > 0)
                <div class="stat">
                    <div class="stat-number" style="color: #DC2626;">{{ $pickupRequests->where('status', 'pending')->count() }}</div>
                    <div style="color: #DC2626;">Pending</div>
                </div>
            @endif
        @endif
        @if($includeUpcoming && $upcomingUnconfirmedCount > 0)
            <div class="stat">
                <div class="stat-number">{{ $upcomingUnconfirmedCount }}</div>
                <div>Upcoming Unconfirmed</div>
            </div>
        @endif
    </div>

    @if($pickupRequests->count() > 0)
        @foreach($pickupRequests as $pickup)
            <div class="pickup-item {{ $pickup->status === 'pending' ? 'pending' : '' }}">
                <div class="pickup-header">
                    <div class="pickup-time">
                        @if($pickup->status === 'pending')
                            <span style="color: #DC2626; margin-right: 5px;">🚨</span>
                        @endif
                        @if($pickup->event && $pickup->event->start_date)
                            {{ $pickup->event->start_date->format('g:i A') }}
                        @else
                            {{ $pickup->preferred_pickup_date->format('g:i A') }}
                        @endif
                        @if($pickup->status === 'pending')
                            <br><small style="color: #DC2626; font-weight: normal;">NEEDS CONFIRMATION</small>
                        @endif
                    </div>
                    <div class="pickup-status">
                        <span class="status-badge status-{{ $pickup->status }}">
                            {{ ucfirst($pickup->status) }}
                        </span>
                    </div>
                    <div class="pickup-customer">
                        @if($pickup->business_name)
                            <strong>{{ $pickup->business_name }}</strong>
                            <br><em>Contact: {{ $pickup->contact_name }}</em>
                        @else
                            <strong>{{ $pickup->contact_name }}</strong>
                        @endif
                        @if($pickup->customer)
                            <br><small>Customer: {{ $pickup->customer->name }}</small>
                        @endif
                    </div>
                </div>

                <div class="pickup-details">
                    <div class="detail-section">
                        <div class="detail-label">Contact Information</div>
                        <div class="detail-value">
                            <strong>Phone:</strong> {{ $pickup->phone }}<br>
                            <strong>Email:</strong> {{ $pickup->email }}
                        </div>

                        <div class="detail-label">Pickup Address</div>
                        <div class="detail-value address-block">
                            {{ $pickup->pickup_address }}
                            @if($pickup->property_location_details)
                                <br><strong>Location Details:</strong> {{ $pickup->property_location_details }}
                            @endif
                        </div>

                        @if($pickup->driver_instructions)
                            <div class="detail-label">Driver Instructions</div>
                            <div class="detail-value">
                                {{ $pickup->driver_instructions }}
                            </div>
                        @endif
                    </div>

                    <div class="detail-section">
                        <div class="detail-label">Items & Details</div>
                        <div class="detail-value items-block">
                            @if($pickup->load_size)
                                <strong>Load Size:</strong> {{ ucfirst($pickup->load_size) }}<br>
                            @endif
                            @if($pickup->accessibility_level)
                                <strong>Accessibility:</strong> {{ ucfirst($pickup->accessibility_level) }}<br>
                            @endif
                            @if($pickup->item_types && is_array($pickup->item_types))
                                <strong>Item Types:</strong>
                                @php
                                    $humanReadableTypes = [];
                                    foreach($pickup->item_types as $itemType) {
                                        $humanReadableTypes[] = $itemTypeLookup[$itemType] ?? ucfirst(str_replace('_', ' ', $itemType));
                                    }
                                @endphp
                                {{ implode(', ', $humanReadableTypes) }}<br>
                            @endif
                            @if($pickup->item_specifics)
                                <strong>Specifics:</strong> {{ $pickup->item_specifics }}<br>
                            @endif
                            @if($pickup->pickup_items)
                                <strong>Items (Legacy):</strong> {{ $pickup->pickup_items }}<br>
                            @endif
                            @if($pickup->pickup_quantity)
                                <strong>Quantity (Legacy):</strong> {{ $pickup->pickup_quantity }}
                            @endif
                        </div>

                        @if($pickup->other_notes)
                            <div class="detail-label">Additional Notes</div>
                            <div class="detail-value">
                                {{ $pickup->other_notes }}
                            </div>
                        @endif

                        @if($pickup->event && $pickup->event->assignedDriver)
                            <div class="detail-label">Assigned Driver</div>
                            <div class="detail-value">
                                {{ $pickup->event->assignedDriver->name }}
                            </div>
                        @endif

                        @if(isset($distanceDataMap[$pickup->id]) && $warehouseLocation)
                            @php
                                $distanceData = $distanceDataMap[$pickup->id];
                                $leaveTime = null;

                                // Calculate suggested leave time if we have duration and pickup time
                                if ($distanceData['success'] && isset($distanceData['duration']) && $pickup->event && $pickup->event->start_date) {
                                    $pickupTime = $pickup->event->start_date;
                                    $travelTimeMinutes = ceil($distanceData['duration'] / 60); // Convert seconds to minutes, round up
                                    $bufferMinutes = 15; // Add 15 minute buffer
                                    $totalMinutes = $travelTimeMinutes + $bufferMinutes;
                                    $leaveTime = $pickupTime->copy()->subMinutes($totalMinutes);
                                } elseif ($distanceData['success'] && isset($distanceData['duration']) && $pickup->preferred_pickup_date) {
                                    $pickupTime = $pickup->preferred_pickup_date;
                                    $travelTimeMinutes = ceil($distanceData['duration'] / 60);
                                    $bufferMinutes = 15;
                                    $totalMinutes = $travelTimeMinutes + $bufferMinutes;
                                    $leaveTime = $pickupTime->copy()->subMinutes($totalMinutes);
                                }
                            @endphp

                            <div class="detail-label">Travel Information</div>
                            <div class="detail-value travel-block">
                                @if($distanceData['success'])
                                    <strong>Distance from Warehouse:</strong> {{ $distanceData['distance_text'] }}<br>
                                    <strong>Estimated Travel Time:</strong> {{ $distanceData['duration_text'] }}<br>
                                    @if($leaveTime)
                                        <span class="leave-time">Suggested Leave Time: {{ $leaveTime->format('g:i A') }}</span>
                                        <br><small style="color: #6B7280;">(Includes 15-minute buffer)</small>
                                    @endif
                                @else
                                    <strong>Distance:</strong> <span style="color: #DC2626;">{{ $distanceData['distance_text'] ?? 'Could not calculate' }}</span><br>
                                    <strong>Travel Time:</strong> <span style="color: #DC2626;">{{ $distanceData['duration_text'] ?? 'Could not calculate' }}</span>
                                @endif
                            </div>
                        @elseif(!$warehouseLocation)
                            <div class="detail-label">Travel Information</div>
                            <div class="detail-value" style="color: #6B7280; font-style: italic;">
                                Warehouse location not configured
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    @else
        <div class="no-pickups">
            No pickup requests found for {{ $dateFormatted }}
            @if($statusFilter)
                with status "{{ ucfirst($statusFilter) }}"
            @endif
        </div>
    @endif

    @if($includeUpcoming && $upcomingUnconfirmed->count() > 0)
        <div class="page-break"></div>

        <div style="margin-top: 40px;">
            <h2 style="color: #F59E0B; font-size: 20pt; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 2px solid #F59E0B;">
                <i class="fa-sharp fa-exclamation-triangle" style="margin-right: 10px;"></i>
                Upcoming Unconfirmed Pickups (Next 2 Days)
            </h2>

            <div style="background-color: #FEF3C7; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #F59E0B;">
                <strong>! Action Required:</strong> The following pickup requests need confirmation before their scheduled dates. Please call the customers to confirm. Unconfirmed appointments may be canceled!!
            </div>

            @foreach($upcomingUnconfirmed as $pickup)
                <div class="pickup-item" style="border-left: 4px solid #F59E0B;">
                    <div class="pickup-header">
                        <div class="pickup-time">
                            @if($pickup->event && $pickup->event->start_date)
                                {{ $pickup->event->start_date->format('M j, g:i A') }}
                            @else
                                {{ $pickup->preferred_pickup_date->format('M j, g:i A') }}
                            @endif
                        </div>
                        <div class="pickup-status">
                            <span class="status-badge status-{{ $pickup->status }}">
                                {{ ucfirst($pickup->status) }}
                            </span>
                        </div>
                        <div class="pickup-customer">
                            @if($pickup->business_name)
                                <strong>{{ $pickup->business_name }}</strong>
                                <br><em>Contact: {{ $pickup->contact_name }}</em>
                            @else
                                <strong>{{ $pickup->contact_name }}</strong>
                            @endif
                            @if($pickup->customer)
                                <br><small>Customer: {{ $pickup->customer->name }}</small>
                            @endif
                        </div>
                    </div>

                    <div class="pickup-details">
                        <div class="detail-section">
                            <div class="detail-label">Contact Information</div>
                            <div class="detail-value">
                                <strong>Phone:</strong> {{ $pickup->phone }}<br>
                                <strong>Email:</strong> {{ $pickup->email }}
                            </div>

                            <div class="detail-label">Pickup Address</div>
                            <div class="detail-value address-block">
                                {{ $pickup->pickup_address }}
                                @if($pickup->property_location_details)
                                    <br><strong>Location Details:</strong> {{ $pickup->property_location_details }}
                                @endif
                            </div>
                        </div>

                        <div class="detail-section">
                            <div class="detail-label">Items & Details</div>
                            <div class="detail-value items-block">
                                @if($pickup->load_size)
                                    <strong>Load Size:</strong> {{ ucfirst($pickup->load_size) }}<br>
                                @endif
                                @if($pickup->item_types && is_array($pickup->item_types))
                                    <strong>Item Types:</strong>
                                    @php
                                        $humanReadableTypes = [];
                                        foreach($pickup->item_types as $itemType) {
                                            $humanReadableTypes[] = $itemTypeLookup[$itemType] ?? ucfirst(str_replace('_', ' ', $itemType));
                                        }
                                    @endphp
                                    {{ implode(', ', $humanReadableTypes) }}<br>
                                @endif
                                @if($pickup->item_specifics)
                                    <strong>Specifics:</strong> {{ $pickup->item_specifics }}<br>
                                @endif
                            </div>

                            @if($pickup->other_notes)
                                <div class="detail-label">Additional Notes</div>
                                <div class="detail-value">
                                    {{ $pickup->other_notes }}
                                </div>
                            @endif

                            @if(isset($distanceDataMap[$pickup->id]) && $warehouseLocation)
                                @php
                                    $distanceData = $distanceDataMap[$pickup->id];
                                    $leaveTime = null;

                                    // Calculate suggested leave time if we have duration and pickup time
                                    if ($distanceData['success'] && isset($distanceData['duration']) && $pickup->event && $pickup->event->start_date) {
                                        $pickupTime = $pickup->event->start_date;
                                        $travelTimeMinutes = ceil($distanceData['duration'] / 60); // Convert seconds to minutes, round up
                                        $bufferMinutes = 15; // Add 15 minute buffer
                                        $totalMinutes = $travelTimeMinutes + $bufferMinutes;
                                        $leaveTime = $pickupTime->copy()->subMinutes($totalMinutes);
                                    } elseif ($distanceData['success'] && isset($distanceData['duration']) && $pickup->preferred_pickup_date) {
                                        $pickupTime = $pickup->preferred_pickup_date;
                                        $travelTimeMinutes = ceil($distanceData['duration'] / 60);
                                        $bufferMinutes = 15;
                                        $totalMinutes = $travelTimeMinutes + $bufferMinutes;
                                        $leaveTime = $pickupTime->copy()->subMinutes($totalMinutes);
                                    }
                                @endphp

                                <div class="detail-label">Travel Information</div>
                                <div class="detail-value travel-block">
                                    @if($distanceData['success'])
                                        <strong>Distance from Warehouse:</strong> {{ $distanceData['distance_text'] }}<br>
                                        <strong>Estimated Travel Time:</strong> {{ $distanceData['duration_text'] }}<br>
                                        @if($leaveTime)
                                            <span class="leave-time">🚗 Suggested Leave Time: {{ $leaveTime->format('g:i A') }}</span>
                                            <br><small style="color: #6B7280;">(Includes 15-minute buffer)</small>
                                        @endif
                                    @else
                                        <strong>Distance:</strong> <span style="color: #DC2626;">{{ $distanceData['distance_text'] ?? 'Could not calculate' }}</span><br>
                                        <strong>Travel Time:</strong> <span style="color: #DC2626;">{{ $distanceData['duration_text'] ?? 'Could not calculate' }}</span>
                                    @endif
                                </div>
                            @elseif(!$warehouseLocation)
                                <div class="detail-label">Travel Information</div>
                                <div class="detail-value" style="color: #6B7280; font-style: italic;">
                                    Warehouse location not configured
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @endif

    <div class="footer">
        This report was generated automatically from the pickup request system.
        <br>For questions or updates, please contact the operations team.
    </div>
</body>
</html>
