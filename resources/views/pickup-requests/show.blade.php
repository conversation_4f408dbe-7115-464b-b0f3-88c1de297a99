@php
    // Build action buttons array based on pickup request status and permissions
    $actionButtons = [];

    if (auth()->user()->hasPermission('manage_pickup_requests')) {
        // Edit Request button
        $actionButtons[] = [
            'name' => 'Edit Request',
            'route' => route('pickup-requests.edit', $pickupRequest),
            'icon' => 'fa-sharp fa-pen-to-square',
            'class' => 'btn btn-primary btn-xs lg:btn-sm gap-1 lg:gap-2'
        ];

        // Status-based action buttons
        if ($pickupRequest->status === 'pending' && $pickupRequest->event) {
            $actionButtons[] = [
                'name' => 'Confirm Response',
                'onclick' => 'confirmRequest()',
                'icon' => 'fa-sharp fa-check',
                'class' => 'btn btn-success btn-xs lg:btn-sm gap-1 lg:gap-2'
            ];
        }

        if ($pickupRequest->status === 'confirmed') {
            $actionButtons[] = [
                'name' => 'Mark Complete',
                'onclick' => 'completeRequest()',
                'icon' => 'fa-sharp fa-check-circle',
                'class' => 'btn btn-neutral btn-xs lg:btn-sm gap-1 lg:gap-2'
            ];

            $actionButtons[] = [
                'name' => 'Mark Not Confirmed',
                'onclick' => 'unconfirmRequest()',
                'icon' => 'fa-sharp fa-undo',
                'class' => 'btn btn-warning btn-xs lg:btn-sm gap-1 lg:gap-2'
            ];
        }

        // Send Pickup Details button - Only show if status is pending/confirmed and event exists
        if (in_array($pickupRequest->status, ['pending', 'confirmed']) && $pickupRequest->event) {
            $actionButtons[] = [
                'name' => 'Send Pickup Details',
                'onclick' => 'sendPickupDetails()',
                'icon' => 'fa-sharp fa-envelope',
                'class' => 'btn btn-info btn-xs lg:btn-sm gap-1 lg:gap-2'
            ];
        }

        // Cancel button for applicable statuses
        if (in_array($pickupRequest->status, ['incoming', 'pending', 'confirmed'])) {
            $actionButtons[] = [
                'name' => 'Cancel Request',
                'onclick' => 'cancelRequest()',
                'icon' => 'fa-sharp fa-times',
                'class' => 'btn btn-error btn-xs lg:btn-sm gap-1 lg:gap-2'
            ];
        }

        // Customer and scheduling actions
        if (!$pickupRequest->customer) {
            $actionButtons[] = [
                'name' => 'Link Customer',
                'route' => route('pickup-requests.step1', $pickupRequest),
                'icon' => 'fa-sharp fa-user-check',
                'class' => 'btn btn-primary btn-xs lg:btn-sm gap-1 lg:gap-2'
            ];
        } elseif (!$pickupRequest->event) {
            $actionButtons[] = [
                'name' => 'Schedule Pickup',
                'route' => route('pickup-requests.step2', $pickupRequest),
                'icon' => 'fa-sharp fa-calendar-check',
                'class' => 'btn btn-success btn-xs lg:btn-sm gap-1 lg:gap-2'
            ];
        }
    }
@endphp

<x-app-layout
    page-title="Pickup Request #{{ $pickupRequest->id }}"
    page-icon="fa-sharp fa-truck-pickup"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('pickup-requests.index'),
            'text' => 'Back to Requests'
        ]
    ]"
    :action-buttons="$actionButtons"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Pickup Requests', 'route' => 'pickup-requests.index', 'icon' => 'fa-truck-pickup'],
        ['name' => 'Request #' . $pickupRequest->id, 'icon' => 'fa-file-lines']
    ]">

    <div class="py-6 lg:py-8">
        <!-- Page Content -->
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            @php
                $timezone = \App\Models\GlobalConfig::getTimeZone();
            @endphp

            <!-- Introduction Card -->
            <div class="bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
                <!-- Decorative Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 left-0 w-32 h-32 bg-primary rounded-full -translate-x-16 -translate-y-16"></div>
                    <div class="absolute bottom-0 right-0 w-24 h-24 bg-secondary rounded-full translate-x-12 translate-y-12"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-start gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-gradient-to-br from-primary to-primary-focus text-primary-content w-12 rounded-xl shadow-lg">
                                <i class="fa-sharp fa-truck-pickup text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-2">
                                <h3 class="text-2xl font-bold text-base-content">Pickup Request #{{ $pickupRequest->id }}</h3>
                                @php
                                    $statusClasses = [
                                        'incoming' => 'badge-warning',
                                        'pending' => 'badge-info',
                                        'confirmed' => 'badge-success',
                                        'completed' => 'badge-success',
                                        'cancelled' => 'badge-error'
                                    ];
                                @endphp
                                <div class="badge {{ $statusClasses[$pickupRequest->status] ?? 'badge-neutral' }} badge-lg">
                                    {{ $pickupRequest->getFormattedStatus() }}
                                </div>
                            </div>
                            <p class="text-base-content/70 text-base leading-relaxed">
                                @php
                                    $submittedDate = $pickupRequest->submitted_at ? $pickupRequest->submitted_at->setTimezone($timezone) : $pickupRequest->created_at->setTimezone($timezone);
                                @endphp
                                Submitted {{ $submittedDate->format('M j, Y \a\t g:i A') }} by {{ $pickupRequest->contact_name }}
                                @if($pickupRequest->business_name)
                                    from {{ $pickupRequest->business_name }}
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Communication Status Card -->
            <div class="card bg-base-100 shadow-md mb-6">
                <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-info text-info-content w-8 rounded-lg">
                                <i class="fa-sharp fa-bell text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Customer Communication Status</h4>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Reminder Status -->
                        <div class="border-l-4 border-info/30 pl-4">
                            <h5 class="font-medium text-base-content mb-2">Pickup Reminder</h5>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                @if($pickupRequest->reminder_sent_at)
                                    <div class="flex items-center gap-2">
                                        <i class="fa-sharp fa-clock text-info"></i>
                                        <span class="font-medium">Reminder Sent:</span>
                                        <span>{{ $pickupRequest->reminder_sent_at->setTimezone($timezone)->format('M j, Y \a\t g:i A') }}</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <i class="fa-sharp fa-envelope text-info"></i>
                                        <span class="font-medium">Sent to:</span>
                                        <span>{{ $pickupRequest->email }}</span>
                                    </div>
                                @else
                                    <div class="flex items-center gap-2">
                                        <i class="fa-sharp fa-clock text-base-content/50"></i>
                                        <span class="font-medium">Reminder Status:</span>
                                        <span class="text-base-content/60">Not sent</span>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Pickup Details Email Status -->
                        @if($pickupRequest->hasConfirmationEmailBeenSent())
                            @php
                                $emailDetails = $pickupRequest->getConfirmationEmailDetails();
                            @endphp
                            <div class="border-l-4 border-success/30 pl-4">
                                <h5 class="font-medium text-base-content mb-2">Pickup Details Email</h5>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div class="flex items-center gap-2">
                                        <i class="fa-sharp fa-envelope-check text-success"></i>
                                        <span class="font-medium">Email Sent:</span>
                                        <span>{{ \Carbon\Carbon::parse($emailDetails['sent_at'])->setTimezone($timezone)->format('M j, Y \a\t g:i A') }}</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <i class="fa-sharp fa-envelope text-success"></i>
                                        <span class="font-medium">Sent to:</span>
                                        <span>{{ $emailDetails['sent_to'] }}</span>
                                    </div>
                                    @if($pickupRequest->management_token)
                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-link text-success"></i>
                                            <span class="font-medium">Management link:</span>
                                            <a href="{{ route('pickup.manage', ['token' => $pickupRequest->management_token]) }}"
                                               target="_blank" class="link link-success">
                                                View customer portal
                                            </a>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-clock text-success"></i>
                                            <span class="font-medium">Link expires:</span>
                                            <span>{{ $pickupRequest->management_token_expires_at ? $pickupRequest->management_token_expires_at->format('M j, Y \a\t g:i A') : 'Never' }}</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Customer Response Status -->
                        @if($pickupRequest->reminder_sent_at || $pickupRequest->hasConfirmationEmailBeenSent() || $pickupRequest->confirmed_at)
                            <div class="border-l-4 {{ $pickupRequest->confirmed_at ? 'border-success/30' : ($pickupRequest->cancelled_at ? 'border-error/30' : 'border-warning/30') }} pl-4">
                                <h5 class="font-medium text-base-content mb-2">Customer Response</h5>
                                <div class="grid grid-cols-1 gap-4 text-sm">
                                    @if($pickupRequest->confirmed_at)
                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-check-circle text-success"></i>
                                            @if($pickupRequest->hasCustomerSelfConfirmed())
                                                <span class="font-medium">Customer Confirmed:</span>
                                            @else
                                                <span class="font-medium">Staff Confirmed:</span>
                                            @endif
                                            <span>{{ $pickupRequest->confirmed_at->setTimezone($timezone)->format('M j, Y \a\t g:i A') }}</span>
                                        </div>
                                    @elseif($pickupRequest->cancelled_at)
                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-times-circle text-error"></i>
                                            <span class="font-medium">Customer Cancelled:</span>
                                            <span>{{ $pickupRequest->cancelled_at->setTimezone($timezone)->format('M j, Y \a\t g:i A') }}</span>
                                        </div>
                                        @if($pickupRequest->cancellation_reason)
                                            <div class="flex items-start gap-2">
                                                <i class="fa-sharp fa-comment-dots text-error mt-0.5"></i>
                                                <div>
                                                    <span class="font-medium">Cancellation Reason:</span>
                                                    <div class="text-base-content/80 mt-1">{{ $pickupRequest->cancellation_reason }}</div>
                                                </div>
                                            </div>
                                        @endif
                                    @else
                                        <div class="flex items-center gap-2 bg-warning p-2">
                                            <i class="fa-sharp fa-hourglass-half text-warning-content"></i>
                                            <span class="font-medium">Status:</span>
                                            <span class="text-warning-content">Awaiting customer response</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Contact Information Card -->
            <div class="card bg-base-100 shadow-md">
                <!-- Header Section -->
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-user text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Contact Information</h4>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="p-6">
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fa-sharp fa-circle-info text-primary"></i>
                    <p class="text-sm text-base-content/70 my-2">This is information the customer provided when they submitted the request. This may or may not match the information in our customer database.</p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user text-primary"></i>
                            <span class="font-medium">Contact Name:</span>
                            <span>{{ $pickupRequest->contact_name }}</span>
                        </div>
                        @if($pickupRequest->business_name)
                            <div class="flex items-center gap-2">
                                <i class="fa-sharp fa-building text-primary"></i>
                                <span class="font-medium">Business:</span>
                                <span>{{ $pickupRequest->business_name }}</span>
                            </div>
                        @endif
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-envelope text-primary"></i>
                            <span class="font-medium">Email:</span>
                            <a href="mailto:{{ $pickupRequest->email }}" class="link link-primary">
                                {{ $pickupRequest->email }}
                            </a>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-phone text-primary"></i>
                            <span class="font-medium">Phone:</span>
                            <a href="tel:{{ $pickupRequest->phone }}" class="link link-primary">
                                {{ $pickupRequest->phone }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pickup Details Card -->
            @include('partials.pickup-details', [
                'pickupData' => $pickupRequest->getPickupDetailsArray(),
                'title' => 'Pickup Details',
                'showMetadata' => true,
                'distanceData' => $distanceData ?? null,
                'warehouseLocation' => $warehouseLocation ?? null
            ])

            <!-- Pickup Images Card -->
            @if($pickupRequest->images->count() > 0)
                <div class="card bg-base-100 shadow-md">
                    <!-- Header Section -->
                    <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-info text-info-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-images text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Pickup Images</h4>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="p-6">
                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                            @foreach($pickupRequest->images as $image)
                                <div class="relative group cursor-pointer" onclick="openImageModal('{{ $image->getImageSrc() }}', '{{ $image->title }}')">
                                    <div class="aspect-square bg-base-200 rounded-lg overflow-hidden">
                                        <img src="{{ $image->getImageSrc('md') }}" 
                                             alt="{{ $image->alt_text }}" 
                                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-200">
                                    </div>
                                    <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                        <i class="fa-sharp fa-magnifying-glass-plus text-white text-2xl"></i>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        <p class="text-sm text-base-content/60 mt-4">
                            <i class="fa-sharp fa-info-circle mr-1"></i>
                            {{ $pickupRequest->images->count() }} {{ Str::plural('image', $pickupRequest->images->count()) }} total
                            @php
                                $customerImages = $pickupRequest->images->where('uploaded_by', null)->count();
                                $staffImages = $pickupRequest->images->where('uploaded_by', '!=', null)->count();
                            @endphp
                            @if($customerImages > 0 && $staffImages > 0)
                                ({{ $customerImages }} by customer, {{ $staffImages }} by staff)
                            @elseif($customerImages > 0)
                                ({{ $customerImages }} by customer)
                            @elseif($staffImages > 0)
                                ({{ $staffImages }} by staff)
                            @endif
                        </p>
                    </div>
                </div>

                <!-- Image Modal -->
                <dialog id="imageModal" class="modal">
                    <div class="modal-box max-w-4xl">
                        <h3 class="font-bold text-lg mb-4" id="modalImageTitle">Image Preview</h3>
                        <img id="modalImage" src="" alt="" class="w-full rounded-lg">
                        <div class="modal-action">
                            <form method="dialog">
                                <button class="btn">Close</button>
                            </form>
                        </div>
                    </div>
                    <form method="dialog" class="modal-backdrop">
                        <button>close</button>
                    </form>
                </dialog>

                <script>
                    function openImageModal(src, title) {
                        document.getElementById('modalImage').src = src;
                        document.getElementById('modalImageTitle').textContent = title || 'Image Preview';
                        document.getElementById('imageModal').showModal();
                    }
                </script>
            @endif

            @perms('manage_pickup_requests')
                <script>
                    // Initialize staff image dropzone
                    document.addEventListener('DOMContentLoaded', function() {
                        const staffDropzoneComponent = document.querySelector('#staff-pickup-images').closest('.image-dropzone-component');
                        if (staffDropzoneComponent) {
                            const staffDropzone = new ImageDropzone(staffDropzoneComponent);
                            
                            // Listen for successful uploads and reload page to show new images
                            staffDropzoneComponent.addEventListener('image-uploaded', function(event) {
                                console.log('Staff image uploaded successfully:', event.detail);
                                
                                // Show success message briefly then reload
                                showSuccessMessage('Image uploaded successfully! Refreshing page to display new image...');
                                
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1500);
                            });
                        }
                    });

                    function showSuccessMessage(message) {
                        // Create temporary success message
                        const successDiv = document.createElement('div');
                        successDiv.className = 'alert alert-success mb-4 max-w-5xl mx-auto';
                        successDiv.innerHTML = `
                            <div class="flex items-center gap-2">
                                <i class="fa-sharp fa-check-circle"></i>
                                <span>${message}</span>
                            </div>
                        `;
                        
                        // Insert at top of container
                        const container = document.querySelector('.max-w-5xl.mx-auto');
                        if (container) {
                            container.insertBefore(successDiv, container.firstChild);
                            
                            // Remove after 3 seconds
                            setTimeout(() => {
                                successDiv.remove();
                            }, 3000);
                        }
                    }
                </script>
            @endperms

            <!-- Staff Image Upload Card -->
            @perms('manage_pickup_requests')
                <div class="card bg-base-100 shadow-md">
                    <!-- Header Section -->
                    <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-camera text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Add Staff Photos</h4>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="p-6">
                        <div class="flex items-center gap-2 mb-4">
                            <i class="fa-sharp fa-info-circle text-secondary"></i>
                            <p class="text-sm text-base-content/70">Upload additional photos related to this pickup request. These will be visible to both staff and customers.</p>
                        </div>
                        
                        <x-image-dropzone
                            id="staff-pickup-images"
                            name="images[]"
                            :max-files="10"
                            :max-filesize="10"
                            :max-width="1250"
                            :max-height="1250"
                            :client-resize="true"
                            accepted-formats="image/jpeg,image/jpg,image/png,image/webp"
                            :session-based="false"
                            upload-url="{{ route('pickup-requests.upload-image', $pickupRequest) }}"
                            delete-url="/pickup-requests/{{ $pickupRequest->id }}/delete-image/{id}"
                            :multiple="true"
                            :immediate-upload="true"
                            :show-previews="true"
                            label="Staff Photos"
                            help-text="Drop images here or click to upload"
                        />
                    </div>
                </div>
            @endperms

            <!-- Internal Pickup Notes Card -->
            @perms('manage_pickup_requests')
                <div class="card bg-base-100 shadow-md">
                    <!-- Header Section -->
                    <div class="bg-gradient-to-r from-warning/10 to-warning/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-warning text-warning-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-sticky-note text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Internal Pickup Notes</h4>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="p-6">
                        <div class="flex items-center gap-2 mb-4">
                            <i class="fa-sharp fa-info-circle text-warning"></i>
                            <p class="text-sm text-base-content/70">These notes are only visible to staff members and are not shared with customers.</p>
                        </div>

                        <div class="space-y-4">
                            <div class="form-control">
                                <textarea
                                    id="internal-pickup-notes"
                                    class="textarea textarea-bordered w-full h-32 resize-none"
                                    placeholder="Add internal notes about this pickup (e.g., special instructions, access codes, staff reminders)..."
                                    data-pickup-request-id="{{ $pickupRequest->id }}"
                                >{{ $pickupRequest->getInternalStaffNotes() }}</textarea>
                            </div>

                            <div class="flex items-center justify-between">
                                <div  class="text-sm text-base-content/60">
                                    <span class="hidden" id="notes-save-status">
                                    <i class="fa-sharp fa-check-circle text-success mr-1"></i>
                                    Notes saved automatically
                                    </span>
                                </div>
                                <button
                                    type="button"
                                    id="save-notes-btn"
                                    class="btn btn-warning btn-sm gap-2"
                                    onclick="saveInternalNotes()"
                                >
                                    <i class="fa-sharp fa-save"></i>
                                    Save Notes
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @endperms

            <!-- Customer Status Card -->
            @if($pickupRequest->customer)
                <div class="card bg-base-100 shadow-md">
                    <!-- Header Section -->
                    <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-user-check text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Linked Customer</h4>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h5 class="font-semibold text-lg text-base-content">{{ $pickupRequest->customer->name }}</h5>
                                @if($pickupRequest->customer->type)
                                    <div class="badge badge-outline badge-sm mt-1">{{ $pickupRequest->customer->type }}</div>
                                @endif
                                <div class="grid grid-cols-1 gap-2 mt-3 text-sm">
                                    @if($pickupRequest->customer->email)
                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-envelope text-success"></i>
                                            <span>{{ $pickupRequest->customer->email }}</span>
                                        </div>
                                    @endif
                                    @if($pickupRequest->customer->phone)
                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-phone text-success"></i>
                                            <span>{{ $pickupRequest->customer->phone }}</span>
                                        </div>
                                    @endif
                                    @if($pickupRequest->customer->address)
                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-map-marker-alt text-success"></i>
                                            <span>{{ Str::limit($pickupRequest->customer->address, 50) }}</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="flex gap-2 mt-4">
                            <a href="{{ route('customers.show', $pickupRequest->customer) }}"
                               class="btn btn-sm btn-outline flex-1 gap-1">
                                <i class="fa-sharp fa-eye"></i>
                                View Customer
                            </a>
                            <button onclick="unlinkCustomer()"
                                    class="btn btn-sm btn-error btn-outline gap-1">
                                <i class="fa-sharp fa-unlink"></i>
                                Change Customer
                            </button>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Event Status Card -->
            @if($pickupRequest->event)
                <div class="card bg-base-100 shadow-md">
                    <!-- Header Section -->
                    <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-accent text-accent-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-calendar text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Scheduled Event</h4>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="p-6">
                        <div class="grid grid-cols-1 gap-3 text-sm">
                            <div class="flex items-center gap-2">
                                <i class="fa-sharp fa-calendar text-accent"></i>
                                <span class="font-medium">Date:</span>
                                @php
                                    $timezone = \App\Models\GlobalConfig::getTimeZone();
                                @endphp
                                <span>{{ $pickupRequest->event->start_date->setTimezone($timezone)->format('M j, Y g:i A') }}</span>
                            </div>
                            @if($pickupRequest->event->assigned_driver_id && $pickupRequest->event->assignedDriver)
                                <div class="flex items-center gap-2">
                                    <i class="fa-sharp fa-user text-accent"></i>
                                    <span class="font-medium">Driver:</span>
                                    <span>{{ $pickupRequest->event->assignedDriver->name }}</span>
                                </div>
                            @endif
                        </div>
                        @php
                            // Get the pickup calendar URL with date navigation
                            $calendarUrl = route('pickup.calendar');
                            if ($pickupRequest->event && $pickupRequest->event->start_date) {
                                $eventDate = $pickupRequest->event->start_date->format('Y-m-d');
                                $calendarUrl .= '?date=' . $eventDate;
                            }
                        @endphp
                        <a href="{{ $calendarUrl }}" class="btn btn-outline w-full gap-2 mt-4">
                            <i class="fa-sharp fa-calendar"></i>
                            View in Calendar
                        </a>
                    </div>
                </div>
            @endif


            <!-- Action Buttons Card -->
            @perms('manage_pickup_requests')
                <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg p-6">
                    <div class="flex flex-col gap-4">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-base-300 text-base-content w-10 rounded-lg">
                                    <i class="fa-sharp fa-cogs text-sm"></i>
                                </div>
                            </div>
                            <div>
                                <p class="font-medium text-base-content">Manage Pickup Request</p>
                                <p class="text-sm text-base-content/60">Take action on this pickup request</p>
                            </div>
                        </div>
                        
                        {{-- Primary Actions Row --}}
                        <div class="flex flex-wrap gap-2">
                            {{-- Edit Pickup Request --}}
                            <a href="{{ route('pickup-requests.edit', $pickupRequest) }}" class="btn btn-primary btn-sm gap-2">
                                <i class="fa-sharp fa-pen-to-square"></i>
                                Edit Request
                            </a>

                            {{-- Customer and Scheduling Actions --}}
                            @if(!$pickupRequest->customer)
                                <a href="{{ route('pickup-requests.step1', $pickupRequest) }}" class="btn btn-primary btn-sm gap-2 shadow-lg">
                                    <i class="fa-sharp fa-user-check"></i>
                                    Link Customer
                                </a>
                            @elseif(!$pickupRequest->event)
                                <a href="{{ route('pickup-requests.step2', $pickupRequest) }}" class="btn btn-success btn-sm gap-2 shadow-lg">
                                    <i class="fa-sharp fa-calendar-check"></i>
                                    Schedule Pickup
                                </a>
                            @endif

                            {{-- Confirm Response - Only visible when status is pending AND pickup is scheduled --}}
                            @if($pickupRequest->status === 'pending' && $pickupRequest->event)
                                <button onclick="confirmRequest()" class="btn btn-success btn-sm gap-2">
                                    <i class="fa-sharp fa-check"></i>
                                    Confirm Response
                                </button>
                            @endif

                            {{-- Mark Complete - Only visible when status is confirmed --}}
                            @if($pickupRequest->status === 'confirmed')
                                <button onclick="completeRequest()" class="btn btn-neutral btn-sm gap-2">
                                    <i class="fa-sharp fa-check-circle"></i>
                                    Mark Complete
                                </button>
                            @endif
                        </div>

                        {{-- Secondary Actions Row --}}
                        <div class="flex flex-wrap gap-2">
                            {{-- Send Pickup Details - Only visible when status is pending/confirmed and event exists --}}
                            @if(in_array($pickupRequest->status, ['pending', 'confirmed']) && $pickupRequest->event)
                                <button onclick="sendPickupDetails()" class="btn btn-info btn-sm gap-2">
                                    <i class="fa-sharp fa-envelope"></i>
                                    Send Pickup Details
                                </button>
                            @endif

                            {{-- Mark Not Confirmed - Only visible when status is confirmed --}}
                            @if($pickupRequest->status === 'confirmed')
                                <button onclick="unconfirmRequest()" class="btn btn-warning btn-sm gap-2">
                                    <i class="fa-sharp fa-undo"></i>
                                    Mark Not Confirmed
                                </button>
                            @endif

                            {{-- Cancel Request - Only visible when status is incoming, pending, or confirmed --}}
                            @if(in_array($pickupRequest->status, ['incoming', 'pending', 'confirmed']))
                                <button onclick="cancelRequest()" class="btn btn-error btn-sm gap-2">
                                    <i class="fa-sharp fa-times"></i>
                                    Cancel Request
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            @endperms
                        <!-- Activity Log Card -->
            @perms('manage_pickup_requests')
                <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg">
                    <x-activity-log-widget :model="$pickupRequest" :limit="15" />
                </div>
            @endperms
        </div>
    </div>








        </div>
    </div>

    <script>
        // Internal Notes Auto-save functionality
        let notesTimeout;

        document.addEventListener('DOMContentLoaded', function() {
            const notesTextarea = document.getElementById('internal-pickup-notes');
            const saveStatusLabel = document.getElementById('notes-save-status');

            if (notesTextarea) {
                // Auto-save on input with debounce
                notesTextarea.addEventListener('input', function() {
                    clearTimeout(notesTimeout);
                    notesTimeout = setTimeout(() => {
                        saveInternalNotes(true); // true for auto-save
                    }, 1000); // 1 second delay
                });
            }
        });

        async function saveInternalNotes(isAutoSave = false) {
            const textarea = document.getElementById('internal-pickup-notes');
            const saveStatusLabel = document.getElementById('notes-save-status');
            const saveBtn = document.getElementById('save-notes-btn');

            if (!textarea) return;

            const notes = textarea.value;
            const pickupRequestId = textarea.dataset.pickupRequestId;

            try {
                if (!isAutoSave) {
                    saveBtn.disabled = true;
                    saveBtn.innerHTML = '<i class="fa-sharp fa-spinner fa-spin"></i> Saving...';
                }

                const response = await fetch(`/pickup-requests/${pickupRequestId}/update-internal-notes`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ notes: notes })
                });

                const data = await response.json();

                if (data.success) {
                    saveStatusLabel.classList.remove('hidden');
                    setTimeout(() => {
                        saveStatusLabel.classList.add('hidden');
                    }, 3000);
                } else {
                    if (!isAutoSave) {
                        showError({
                            title: 'Error Saving Notes',
                            message: data.message
                        });
                    }
                }
            } catch (error) {
                console.error('Error saving internal notes:', error);
                if (!isAutoSave) {
                    showError({
                        title: 'Error Saving Notes',
                        message: 'An error occurred while saving notes.'
                    });
                }
            } finally {
                if (!isAutoSave) {
                    saveBtn.disabled = false;
                    saveBtn.innerHTML = '<i class="fa-sharp fa-save"></i> Save Notes';
                }
            }
        }

        async function confirmRequest() {
            const confirmed = await showConfirm({
                title: 'Confirm Pickup Request',
                message: 'Are you sure you want to confirm this pickup request? This indicates the customer has confirmed the appointment.',
                confirmText: 'Confirm Request',
                confirmClass: 'btn-success',
                icon: 'fa-check-circle',
                iconColor: 'bg-success/10 text-success'
            });

            if (!confirmed) return;

            try {
                console.log('Confirming request...');
                const response = await fetch(`/pickup-requests/{{ $pickupRequest->id }}/confirm`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                if (data.success) {
                    await showSuccess({
                        title: 'Request Confirmed',
                        message: data.message
                    });
                    window.location.reload();
                } else {
                    await showError({
                        title: 'Error',
                        message: data.message
                    });
                }
            } catch (error) {
                console.error('Error confirming request:', error);
                await showError({
                    title: 'Error',
                    message: 'An error occurred while confirming the request.'
                });
            }
        }

        async function completeRequest() {
            const confirmed = await showConfirm({
                title: 'Complete Pickup Request',
                message: 'Are you sure you want to mark this pickup request as completed?',
                confirmText: 'Mark Complete',
                confirmClass: 'btn-success',
                icon: 'fa-check-circle',
                iconColor: 'bg-success/10 text-success'
            });

            if (!confirmed) return;

            try {
                const response = await fetch(`/pickup-requests/{{ $pickupRequest->id }}/complete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                if (data.success) {
                    await showSuccess({
                        title: 'Request Completed',
                        message: data.message
                    });
                    window.location.reload();
                } else {
                    // Check if this is a timing-related error (pickup not yet due)
                    if (response.status === 400 && data.pickup_time) {
                        await showWarning({
                            title: 'Pickup Not Yet Due',
                            message: data.message
                        });
                    } else {
                        await showError({
                            title: 'Error',
                            message: data.message
                        });
                    }
                }
            } catch (error) {
                console.error('Error completing request:', error);
                await showError({
                    title: 'Error',
                    message: 'An error occurred while completing the request.'
                });
            }
        }

        async function cancelRequest() {
            // First show confirmation dialog
            const confirmed = await showConfirm({
                title: 'Cancel Pickup Request',
                message: 'Are you sure you want to cancel this pickup request? You will be asked to provide a reason for the cancellation.',
                confirmText: 'Continue to Cancel',
                confirmClass: 'btn-error',
                cancelText: 'Keep Request',
                icon: 'fa-times-circle',
                iconColor: 'bg-error/10 text-error'
            });

            if (!confirmed) return;

            // Show cancellation reason dialog
            const reasonDialog = document.createElement('dialog');
            reasonDialog.className = 'modal';
            reasonDialog.innerHTML = `
                <div class="modal-box">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-error text-error-content w-10 rounded-lg">
                                <i class="fa-sharp fa-times text-lg"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">Cancel Pickup Request</h3>
                            <p class="text-base-content/70">Please provide a reason for the cancellation</p>
                        </div>
                    </div>
                    
                    <div class="form-control mb-6">
                        <label class="label">
                            <span class="label-text font-medium">Cancellation Reason *</span>
                        </label>
                        <textarea 
                            id="cancellation-reason" 
                            class="textarea textarea-bordered h-24 resize-none" 
                            placeholder="Please explain why this pickup is being cancelled..."
                            required
                        ></textarea>
                        <div class="label">
                            <span class="label-text-alt text-base-content/60">This reason will be included in the email to the customer</span>
                        </div>
                    </div>
                    
                    <div class="modal-action">
                        <button type="button" class="btn btn-outline" onclick="this.closest('dialog').close()">
                            <i class="fa-sharp fa-times"></i>
                            Cancel
                        </button>
                        <button type="button" class="btn btn-error gap-2" id="confirm-cancellation">
                            <i class="fa-sharp fa-times-circle"></i>
                            Cancel Pickup
                        </button>
                    </div>
                </div>
                <form method="dialog" class="modal-backdrop">
                    <button>close</button>
                </form>
            `;
            
            document.body.appendChild(reasonDialog);
            reasonDialog.showModal();

            // Handle confirm cancellation
            const confirmButton = reasonDialog.querySelector('#confirm-cancellation');
            const reasonTextarea = reasonDialog.querySelector('#cancellation-reason');
            
            confirmButton.addEventListener('click', async () => {
                const reason = reasonTextarea.value.trim();
                
                if (!reason) {
                    reasonTextarea.focus();
                    reasonTextarea.classList.add('textarea-error');
                    return;
                }

                confirmButton.disabled = true;
                confirmButton.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Cancelling...';

                try {
                    const response = await fetch(`/pickup-requests/{{ $pickupRequest->id }}/cancel`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            reason: reason
                        })
                    });

                    const data = await response.json();
                    if (data.success) {
                        reasonDialog.close();
                        await showSuccess({
                            title: 'Request Cancelled',
                            message: data.message
                        });
                        window.location.reload();
                    } else {
                        await showError({
                            title: 'Error',
                            message: data.message
                        });
                        confirmButton.disabled = false;
                        confirmButton.innerHTML = '<i class="fa-sharp fa-times-circle"></i> Cancel Pickup';
                    }
                } catch (error) {
                    console.error('Error cancelling request:', error);
                    await showError({
                        title: 'Error',
                        message: 'An error occurred while cancelling the request.'
                    });
                    confirmButton.disabled = false;
                    confirmButton.innerHTML = '<i class="fa-sharp fa-times-circle"></i> Cancel Pickup';
                }
            });

            // Remove dialog when closed
            reasonDialog.addEventListener('close', () => {
                document.body.removeChild(reasonDialog);
            });
        }

        async function unconfirmRequest() {
            const confirmed = await showConfirm({
                title: 'Mark Not Confirmed',
                message: 'Are you sure you want to mark this pickup request as not confirmed? This will change the status back to pending.',
                confirmText: 'Mark Not Confirmed',
                confirmClass: 'btn-warning',
                icon: 'fa-undo',
                iconColor: 'bg-warning/10 text-warning'
            });

            if (!confirmed) return;

            try {
                const response = await fetch(`/pickup-requests/{{ $pickupRequest->id }}/unconfirm`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                if (data.success) {
                    await showSuccess({
                        title: 'Request Unconfirmed',
                        message: data.message
                    });
                    window.location.reload();
                } else {
                    await showError({
                        title: 'Error',
                        message: data.message
                    });
                }
            } catch (error) {
                console.error('Error unconfirming request:', error);
                await showError({
                    title: 'Error',
                    message: 'An error occurred while unconfirming the request.'
                });
            }
        }

        async function unlinkCustomer() {
            const confirmed = await showConfirm({
                title: 'Change Customer',
                message: 'Are you sure you want to change the customer? This will reset the request status to pending and redirect you to customer identification.',
                confirmText: 'Change Customer',
                confirmClass: 'btn-warning',
                icon: 'fa-user-times',
                iconColor: 'bg-warning/10 text-warning'
            });

            if (!confirmed) return;

            try {
                const response = await fetch(`/pickup-requests/{{ $pickupRequest->id }}/unlink-customer`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    await showSuccess({
                        title: 'Customer Changed',
                        message: data.message
                    });
                    window.location.href = '{{ route("pickup-requests.step1", $pickupRequest) }}';
                } else {
                    await showError({
                        title: 'Error',
                        message: data.message
                    });
                }
            } catch (error) {
                console.error('Error unlinking customer:', error);
                await showError({
                    title: 'Error',
                    message: 'An error occurred while unlinking the customer.'
                });
            }
        }

        async function sendPickupDetails() {
            @if($pickupRequest->hasConfirmationEmailBeenSent())
                @php
                    $emailDetails = $pickupRequest->getConfirmationEmailDetails();
                @endphp
                // Email has already been sent - show confirmation dialog
                const confirmed = await showConfirm({
                    title: 'Resend Pickup Details',
                    message: 'Pickup details were already sent to {{ $emailDetails["sent_to"] }} on {{ \Carbon\Carbon::parse($emailDetails["sent_at"])->setTimezone(\App\Models\GlobalConfig::getTimeZone())->format("M j, Y \\a\\t g:i A") }}.\n\nWould you like to send the pickup details again?',
                    confirmText: 'Send Again',
                    confirmClass: 'btn-warning',
                    cancelText: 'Cancel',
                    icon: 'fa-envelope',
                    iconColor: 'bg-warning/10 text-warning'
                });
            @else
                // First time sending - normal confirmation
                const confirmed = await showConfirm({
                    title: 'Send Pickup Details',
                    message: 'Are you sure you want to send pickup details to {{ $pickupRequest->email }}? This will include the scheduled pickup time and a unique link for the customer to manage their appointment.',
                    confirmText: 'Send Details',
                    confirmClass: 'btn-info',
                    icon: 'fa-envelope',
                    iconColor: 'bg-info/10 text-info'
                });
            @endif

            if (!confirmed) return;

            try {
                const response = await fetch(`/pickup-requests/{{ $pickupRequest->id }}/send-confirmation-email`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        @if($pickupRequest->hasConfirmationEmailBeenSent())
                        , 'X-Force-Resend': 'true'  // Flag to indicate this is an intentional resend
                        @endif
                    }
                });

                const data = await response.json();
                if (data.success) {
                    await showSuccess({
                        title: 'Email Sent',
                        message: data.message
                    });
                    window.location.reload();
                } else {
                    await showError({
                        title: 'Error',
                        message: data.message
                    });
                }
            } catch (error) {
                console.error('Error sending pickup details:', error);
                await showError({
                    title: 'Error',
                    message: 'An error occurred while sending the pickup details.'
                });
            }
        }
    </script>
</x-app-layout>
