<x-app-layout
    page-title="Step 2: Select Pickup Time"
    page-icon="fa-sharp fa-calendar-clock"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Pickup Requests', 'route' => 'pickup-requests.index', 'icon' => 'fa-truck-pickup'],
        ['name' => 'Request #' . $pickupRequest->id, 'route' => 'pickup-requests.show', 'params' => [$pickupRequest], 'icon' => 'fa-file-lines'],
        ['name' => 'Schedule', 'icon' => 'fa-calendar-clock']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Progress Steps -->
            <div class="card bg-base-100 shadow-md">
                <div class="p-6">
                    <ul class="steps steps-horizontal w-full">
                        <li class="step step-primary">Step 1: Customer</li>
                        <li class="step step-primary">Step 2: Time</li>
                        <li class="step">Step 3: Details</li>
                    </ul>
                </div>
            </div>

            <!-- Pickup Request Information -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-truck-pickup text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Pickup Request Details</h4>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-hashtag text-primary"></i>
                            <span class="font-medium">Request #:</span>
                            <span class="font-mono">{{ $pickupRequest->id }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user text-secondary"></i>
                            <span class="font-medium">Customer:</span>
                            <span>{{ $pickupRequest->customer->name }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-phone text-accent"></i>
                            <span class="font-medium">Phone:</span>
                            <span>{{ $pickupRequest->phone }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-location-dot text-info"></i>
                            <span class="font-medium">Address:</span>
                            <span class="truncate" title="{{ $pickupRequest->pickup_address }}">{{ $pickupRequest->pickup_address }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-boxes text-warning"></i>
                            <span class="font-medium">Items:</span>
                            <span class="truncate" title="{{ $pickupRequest->pickup_items }}">{{ $pickupRequest->pickup_items }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-hashtag text-success"></i>
                            <span class="font-medium">Quantity:</span>
                            <span>{{ $pickupRequest->pickup_quantity }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Time Selection Form -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-calendar-clock text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Step 2: Select Pickup Time</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    @if($pickupRequest->preferred_pickup_date)
                        <div class="bg-info/10 border border-info/20 rounded-lg p-4">
                            <div class="flex items-start gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-info text-info-content w-6 rounded">
                                        <i class="fa-sharp fa-info-circle text-xs"></i>
                                    </div>
                                </div>
                                <div class="text-sm text-base-content/80">
                                    <p class="font-medium mb-1">Customer's Preferred Time</p>
                                    <p>{{ $pickupRequest->preferred_pickup_date->format('l, F j, Y \a\t g:i A') }}</p>
                                    <p class="text-xs text-base-content/60 mt-1">You can use this time or select a different one below.</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <form action="{{ route('pickup-requests.saveStep2', $pickupRequest) }}" method="POST" class="space-y-6">
                        @csrf

                        <!-- Use the pickup time selector component -->
                        <x-pickup-time-selector
                            name="pickup_datetime"
                            :value="old('pickup_datetime', $pickupRequest->preferred_pickup_date ? $pickupRequest->preferred_pickup_date->format('Y-m-d\TH:i:s') : '')"
                            :required="true"
                            label="Pickup Date & Time"
                            icon="fa-calendar-clock"
                            iconColor="text-secondary"
                            :autoSave="false"
                            :showWeekView="true"
                            :pickupRequestId="$pickupRequest->id"
                        />

                        <!-- Legacy fields for backward compatibility with form processing -->
                        <input type="hidden" name="pickup_date" id="legacy_pickup_date">
                        <input type="hidden" name="pickup_time" id="legacy_pickup_time">

                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                // Get the component's hidden input
                                const datetimeInput = document.querySelector('input[name="pickup_datetime"]');
                                const legacyDateInput = document.getElementById('legacy_pickup_date');
                                const legacyTimeInput = document.getElementById('legacy_pickup_time');

                                // Function to update legacy fields when datetime changes
                                function updateLegacyFields() {
                                    if (datetimeInput.value) {
                                        const datetime = new Date(datetimeInput.value);
                                        // Extract date in local timezone
                                        const year = datetime.getFullYear();
                                        const month = String(datetime.getMonth() + 1).padStart(2, '0');
                                        const day = String(datetime.getDate()).padStart(2, '0');
                                        legacyDateInput.value = `${year}-${month}-${day}`;
                                        
                                        // Extract time in local timezone
                                        const hours = String(datetime.getHours()).padStart(2, '0');
                                        const minutes = String(datetime.getMinutes()).padStart(2, '0');
                                        legacyTimeInput.value = `${hours}:${minutes}`;
                                    }
                                }

                                // Watch for changes to the datetime input
                                const observer = new MutationObserver(function(mutations) {
                                    mutations.forEach(function(mutation) {
                                        if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                                            updateLegacyFields();
                                        }
                                    });
                                });

                                observer.observe(datetimeInput, {
                                    attributes: true,
                                    attributeFilter: ['value']
                                });

                                // Also listen for input events
                                datetimeInput.addEventListener('input', updateLegacyFields);
                                datetimeInput.addEventListener('change', updateLegacyFields);

                                // Initialize if there's already a value
                                updateLegacyFields();
                            });
                        </script>

                        <!-- Navigation -->
                        <div class="divider my-8">
                            <span class="text-base-content/50 font-medium">Continue to event details</span>
                        </div>

                        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                            <a href="{{ route('pickup-requests.step1', $pickupRequest) }}" class="btn btn-ghost gap-2">
                                <i class="fa-sharp fa-arrow-left"></i>
                                Back to Step 1
                            </a>

                            <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg w-full sm:w-auto">
                                Continue to Step 3
                                <i class="fa-sharp fa-arrow-right"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>


</x-app-layout>
