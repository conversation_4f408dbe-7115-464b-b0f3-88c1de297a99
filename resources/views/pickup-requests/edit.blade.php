<x-app-layout
    page-title="Edit Pickup Request #{{ $pickupRequest->id }}"
    page-icon="fa-sharp fa-pen-to-square"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('pickup-requests.show', $pickupRequest),
            'text' => 'Back to Request'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'View Request',
            'route' => route('pickup-requests.show', $pickupRequest),
            'icon' => 'fa-sharp fa-eye',
            'class' => 'btn btn-info btn-sm gap-2'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Pickup Requests', 'route' => 'pickup-requests.index', 'icon' => 'fa-truck-pickup'],
        ['name' => 'Request #' . $pickupRequest->id, 'route' => 'pickup-requests.show', 'params' => $pickupRequest, 'icon' => 'fa-eye'],
        ['name' => 'Edit', 'icon' => 'fa-pen-to-square']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Introduction Card -->
            <div class="bg-gradient-to-br from-warning/5 via-primary/5 to-secondary/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
                <!-- Decorative Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 left-0 w-32 h-32 bg-warning rounded-full -translate-x-16 -translate-y-16"></div>
                    <div class="absolute bottom-0 right-0 w-24 h-24 bg-primary rounded-full translate-x-12 translate-y-12"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-start gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-gradient-to-br from-warning to-warning-focus text-warning-content w-12 rounded-xl shadow-lg">
                                <i class="fa-sharp fa-pen-to-square text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-2xl font-bold text-base-content mb-2">Edit Pickup Request #{{ $pickupRequest->id }}</h3>
                            <p class="text-base-content/70 text-base leading-relaxed">
                                Update the pickup request details. 
                                @if($pickupRequest->event)
                                    Changes will be automatically applied to the linked calendar event.
                                @endif
                            </p>
                            <!-- Auto-save status indicator -->
                            <div id="autoSaveStatus" class="mt-3 hidden">
                                <div class="flex items-center gap-2 text-sm">
                                    <i class="fa-sharp fa-cloud-check text-success"></i>
                                    <span class="text-success">Changes saved automatically</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Info -->
            @if($pickupRequest->status)
                <div class="alert alert-info">
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-info-circle"></i>
                        <div>
                            <strong>Current Status:</strong> {{ ucfirst($pickupRequest->status) }}
                            @if($pickupRequest->event)
                                | <strong>Calendar Event:</strong> Linked to event #{{ $pickupRequest->event->id }}
                            @endif
                        </div>
                    </div>
                </div>
            @endif

            <!-- Error Display -->
            @if($errors->any())
                <div class="alert alert-error mb-6">
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-exclamation-triangle"></i>
                        <div>
                            <h4 class="font-bold">There were some errors with your submission:</h4>
                            <ul class="list-disc list-inside mt-2">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-error mb-6">
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-exclamation-triangle"></i>
                        <span>{{ session('error') }}</span>
                    </div>
                </div>
            @endif

            <!-- Pickup Request Edit Form -->
            <form action="{{ route('pickup-requests.update', $pickupRequest) }}" method="POST" id="pickup-request-edit-form" class="space-y-6">
                @csrf
                @method('PUT')

                @include('pickup-requests.partials.customer-selection', [
                    'isEdit' => true,
                    'selectedCustomerId' => old('customer_id', $draftData['customer_id'] ?? $pickupRequest->customer_id),
                    'selectedCustomerName' => $selectedCustomerName
                ])

                @include('pickup-requests.partials.contact-information', [
                    'contactName' => old('contact_name', $draftData['contact_name'] ?? $pickupRequest->contact_name),
                    'businessName' => old('business_name', $draftData['business_name'] ?? $pickupRequest->business_name),
                    'email' => old('email', $draftData['email'] ?? $pickupRequest->email),
                    'phone' => old('phone', $draftData['phone'] ?? $pickupRequest->phone)
                ])

                <!-- Pickup Date & Time Selection Card -->
                <div class="card bg-base-100 shadow-md">
                    <!-- Header Section -->
                    <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-accent text-accent-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-calendar text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Pickup Date & Time</h4>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="p-6">
                        <x-pickup-time-selector
                            name="preferred_pickup_date"
                            :value="old('preferred_pickup_date', $draftData['preferred_pickup_date'] ?? ($pickupRequest->preferred_pickup_date ? $pickupRequest->preferred_pickup_date->setTimezone(\App\Models\GlobalConfig::getTimeZone())->format('Y-m-d\TH:i:s') : ''))"
                            :required="true"
                            label="Pickup Date & Time"
                            icon="fa-clock"
                            iconColor="text-accent"
                            :autoSave="true"
                            autoSaveField="preferred_pickup_date"
                        />
                    </div>
                </div>

                <!-- Pickup Address & Location Details Card -->
                <div class="card bg-base-100 shadow-md">
                    <!-- Header Section -->
                    <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-map-marker-alt text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Pickup Location</h4>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="p-6 space-y-4">
                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-map-marker-alt text-success mr-2"></i>
                                Pickup Address *
                            </legend>

                            <x-google-maps-autocomplete
                                id="pickup_address"
                                name="pickup_address"
                                :value="old('pickup_address', $draftData['pickup_address'] ?? $pickupRequest->pickup_address)"
                                placeholder="Start typing to search for an address..."
                                type="textarea"
                                label=""
                                icon="fa-map-marker-alt"
                                iconColor="text-success"
                                :restrictions="['us', 'ca']"
                                :showDisplayMode="true"
                                displayLabel="Pickup Address"
                                changeButtonText="Change"
                                :autoSave="true"
                                autoSaveField="pickup_address"
                                :required="true"
                            />

                            @error('pickup_address')
                                <div class="text-error text-sm mt-1">{{ $message }}</div>
                            @enderror
                        </fieldset>

                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-home text-success mr-2"></i>
                                Where on the property are the items located? *
                            </legend>
                            <div class="relative">
                                <textarea name="property_location_details" id="property_location_details"
                                          class="textarea textarea-bordered h-24 w-full auto-save"
                                          data-field="property_location_details"
                                          placeholder="Please describe the location and accessibility (e.g., garage, basement, second floor, stairs involved, etc.). Note: Extra fees may apply for moving large items from difficult locations."
                                          required>{{ old('property_location_details', $draftData['property_location_details'] ?? $pickupRequest->property_location_details) }}</textarea>
                                <span class="saving-indicator absolute right-3 top-3 hidden">
                                    <i class="fa-sharp fa-spinner fa-spin text-success"></i>
                                </span>
                            </div>
                            @error('property_location_details')
                                <div class="text-error text-sm mt-1">{{ $message }}</div>
                            @enderror
                        </fieldset>

                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-sticky-note text-success mr-2"></i>
                                Other things to know
                            </legend>
                            <div class="relative">
                                <textarea name="other_notes" id="other_notes"
                                          class="textarea textarea-bordered h-24 w-full auto-save"
                                          data-field="other_notes"
                                          placeholder="Any security codes, special instructions, who to call when we arrive, or other important details">{{ old('other_notes', $draftData['other_notes'] ?? $pickupRequest->other_notes) }}</textarea>
                                <span class="saving-indicator absolute right-3 top-3 hidden">
                                    <i class="fa-sharp fa-spinner fa-spin text-success"></i>
                                </span>
                            </div>
                            <div class="text-sm text-base-content/60 mt-1">Optional</div>
                            @error('other_notes')
                                <div class="text-error text-sm mt-1">{{ $message }}</div>
                            @enderror
                        </fieldset>
                    </div>
                </div>

                @include('pickup-requests.partials.item-details', [
                    'loadSize' => old('load_size', $draftData['load_size'] ?? $pickupRequest->load_size),
                    'itemTypes' => old('item_types', $draftData['item_types'] ?? $pickupRequest->item_types),
                    'itemSpecifics' => old('item_specifics', $draftData['item_specifics'] ?? $pickupRequest->item_specifics),
                    'driverInstructions' => old('driver_instructions', $draftData['driver_instructions'] ?? $pickupRequest->driver_instructions),
                    'accessibilityLevel' => old('accessibility_level', $draftData['accessibility_level'] ?? $pickupRequest->accessibility_level)
                ])

                @include('pickup-requests.partials.event-details', [
                    'isEdit' => true,
                    'staffNeeded' => old('staff_needed', $draftData['staff_needed'] ?? ($pickupRequest->event ? $pickupRequest->event->staff_needed : null)),
                    'assignedDriverId' => old('assigned_driver_id', $draftData['assigned_driver_id'] ?? ($pickupRequest->event ? $pickupRequest->event->assigned_driver_id : null)),
                    'internalPickupNotes' => old('internal_pickup_notes', $draftData['internal_pickup_notes'] ?? $pickupRequest->getInternalStaffNotes()),
                    'drivers' => $drivers
                ])



                <!-- Submit Button Card -->
                <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg p-6">
                    <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-warning text-warning-content w-10 rounded-lg">
                                    <i class="fa-sharp fa-save text-sm"></i>
                                </div>
                            </div>
                            <div class="text-center sm:text-left">
                                <p class="font-medium text-base-content">Update Pickup Request</p>
                                <p class="text-sm text-base-content/60">
                                    @if($pickupRequest->event)
                                        Changes will be applied to the linked calendar event
                                    @else
                                        Request details will be updated
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="flex gap-3 w-full sm:w-auto">
                            <a href="{{ route('pickup-requests.show', $pickupRequest) }}" class="btn btn-outline btn-lg gap-2 flex-1 sm:flex-none">
                                <i class="fa-sharp fa-times"></i>
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-warning btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                                <i class="fa-sharp fa-save"></i>
                                Update Request
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-save functionality
            const autoSaveInputs = document.querySelectorAll('.auto-save');
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            let saveTimeout;

            console.log('Found', autoSaveInputs.length, 'auto-save inputs:', Array.from(autoSaveInputs).map(input => ({ id: input.id, field: input.dataset.field })));

            // Find the customer selection hidden input and add auto-save functionality to it
            const customerHiddenInput = document.querySelector('input[name="customer_id"]');
            console.log('Customer hidden input found:', !!customerHiddenInput, 'value:', customerHiddenInput?.value);

            // Debug draft data restoration
            console.log('Draft data from server:', @json($draftData ?? []));
            console.log('Selected customer name from server:', @json($selectedCustomerName ?? null));

            // Set up a MutationObserver to watch for changes to the customer hidden input value
            if (customerHiddenInput) {
                let lastCustomerValue = customerHiddenInput.value;

                // Watch for attribute changes (value changes)
                const customerObserver = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                            const newValue = customerHiddenInput.value;
                            if (newValue !== lastCustomerValue) {
                                console.log('Customer hidden input value changed via mutation:', lastCustomerValue, '->', newValue);
                                lastCustomerValue = newValue;
                                saveField('customer_id', newValue);
                            }
                        }
                    });
                });

                // Start observing
                customerObserver.observe(customerHiddenInput, {
                    attributes: true,
                    attributeFilter: ['value']
                });

                // Also listen for direct property changes
                let originalValueSetter = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value').set;
                Object.defineProperty(customerHiddenInput, 'value', {
                    get: function() {
                        return this.getAttribute('value') || '';
                    },
                    set: function(newValue) {
                        const oldValue = this.value;
                        originalValueSetter.call(this, newValue);
                        this.setAttribute('value', newValue);
                        if (newValue !== oldValue) {
                            console.log('Customer hidden input value changed via property:', oldValue, '->', newValue);
                            saveField('customer_id', newValue);
                        }
                    }
                });
            }

            // Address elements
            const pickupAddressInput = document.getElementById('pickup_address');

            let selectedCustomer = null;

            // Auto-save function (make it global for use by item details partial)
            window.saveField = function saveField(field, value, indicator) {
                console.log('saveField called:', { field, value, hasIndicator: !!indicator });

                if (indicator) {
                    indicator.classList.remove('hidden');
                }

                fetch('{{ route("pickup-requests.auto-save-edit", $pickupRequest) }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'X-Auto-Save': 'true'
                    },
                    body: JSON.stringify({
                        field: field,
                        value: value
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Auto-save response:', data);
                    if (indicator) {
                        indicator.classList.add('hidden');
                    }
                    if (data.success) {
                        // Show auto-save status
                        const statusIndicator = document.getElementById('autoSaveStatus');
                        if (statusIndicator) {
                            statusIndicator.classList.remove('hidden');

                            // Hide after 3 seconds
                            setTimeout(() => {
                                statusIndicator.classList.add('hidden');
                            }, 3000);
                        }
                        console.log('Auto-save successful for field:', field);
                    } else {
                        console.error('Auto-save failed:', data.message);
                    }
                })
                .catch(error => {
                    if (indicator) {
                        indicator.classList.add('hidden');
                    }
                    console.error('Auto-save error:', error);
                });
            }

            // Function to handle auto-save for an input
            function handleAutoSave(input) {
                const field = input.dataset.field;
                const value = input.value;
                const indicator = input.parentNode.querySelector('.saving-indicator');

                console.log('handleAutoSave called:', {
                    field,
                    value,
                    valueLength: value.length,
                    hasIndicator: !!indicator,
                    inputId: input.id
                });

                // Clear any existing timeout
                clearTimeout(saveTimeout);

                // Set a new timeout to save after 500ms of inactivity
                saveTimeout = setTimeout(() => {
                    saveField(field, value, indicator);
                }, 500);
            }

            // Add event listeners to auto-save inputs
            autoSaveInputs.forEach(input => {
                console.log('Adding auto-save listeners to:', input.id, 'field:', input.dataset.field);

                // Listen for input events (typing)
                input.addEventListener('input', function(e) {
                    console.log('Input event triggered on:', this.id, 'value:', this.value);
                    handleAutoSave(this);
                });

                // Listen for change events (field loses focus, paste, delete all content, etc.)
                input.addEventListener('change', function(e) {
                    console.log('Change event triggered on:', this.id, 'value:', this.value);
                    handleAutoSave(this);
                });

                // Listen for paste events
                input.addEventListener('paste', function(e) {
                    console.log('Paste event triggered on:', this.id);
                    // Use setTimeout to ensure pasted content is processed
                    setTimeout(() => {
                        handleAutoSave(this);
                    }, 10);
                });

                // Listen for keyup events to catch delete/backspace
                input.addEventListener('keyup', function(e) {
                    if (e.key === 'Delete' || e.key === 'Backspace') {
                        console.log('Delete/Backspace event triggered on:', this.id, 'value:', this.value);
                        handleAutoSave(this);
                    }
                });
            });

            // Customer selection handling (simplified for edit mode)
            function handleCustomerSelection(customerId) {
                console.log('handleCustomerSelection called with customerId:', customerId);

                // Auto-save customer selection immediately
                console.log('Auto-saving customer selection:', customerId);
                saveField('customer_id', customerId);
            }

            // Listen for customer selection changes
            document.addEventListener('customerSelected', function(event) {
                const customerId = event.detail.customerId;
                handleCustomerSelection(customerId);
            });
        });
    </script>

</x-app-layout>
