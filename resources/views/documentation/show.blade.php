<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ $documentation->title }}
            </h2>
            <a href="{{ route('documentation.index') }}" class="btn btn-neutral">
                <i class="fa-sharp fa-arrow-left"></i> Back to All Documents
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">

            <!-- Details Card -->
            <div class="card bg-base-100 shadow-md p-6">
                <!-- Title -->
                <h1 class="text-2xl font-bold text-gray-800 mb-4">{{ $documentation->title }}</h1>

                <!-- Metadata -->
                <div class="text-sm text-gray-500 mb-6 space-y-2">
                    <p class="flex items-center gap-2">
                        <i class="fa-sharp fa-calendar-alt"></i>
                        <span><strong>Created:</strong> {{ $documentation->created_at->format('Y-m-d H:i') }}</span>
                    </p>
                    <p class="flex items-center gap-2">
                        <i class="fa-sharp fa-pencil-alt"></i>
                        <span><strong>Last Updated:</strong> {{ $documentation->updated_at->format('Y-m-d H:i') }}</span>
                    </p>
                </div>

                <!-- Short Description -->
                <div class="text-gray-600 mb-4">
                    <p class="italic">{{ $documentation->short_desc }}</p>
                </div>
            </div>

            <!-- Content Card -->
            <div class="card bg-base-100 shadow-md p-6">
                <div class="prose prose-indigo max-w-none">
                    {!! $documentation->content !!}
                </div>
            </div>

            <!-- Admin Actions -->
            @if (auth()->user()->isAdmin())
                <div class="card bg-base-100 shadow-md p-4 flex justify-between items-center">
                    <p class="text-gray-600 italic">Manage this document:</p>
                    <div class="flex gap-4">
                        <a href="{{ route('documentation.edit', $documentation) }}" class="btn btn-warning flex items-center gap-2">
                            <i class="fa-sharp fa-edit"></i> Edit
                        </a>
                        <form action="{{ route('documentation.destroy', $documentation) }}" method="POST">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-error flex items-center gap-2">
                                <i class="fa-sharp fa-trash"></i> Delete
                            </button>
                        </form>
                    </div>
                </div>
            @endif

        </div>
    </div>
</x-app-layout>
