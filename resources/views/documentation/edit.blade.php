<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Edit Documentation
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white p-6 rounded-lg shadow">
                <form action="{{ route('documentation.update', $documentation->id) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <!-- Title Input -->
                    <div class="mb-4">
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                        <input
                            type="text"
                            name="title"
                            id="title"
                            class="input input-bordered w-full"
                            value="{{ old('title', $documentation->title) }}"
                            required
                        />
                        @error('title')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Short Description Input -->
                    <div class="mb-4">
                        <label for="short_desc" class="block text-sm font-medium text-gray-700 mb-2">Short Description</label>
                        <textarea
                            name="short_desc"
                            id="short_desc"
                            class="textarea textarea-bordered w-full"
                            rows="3"
                            required
                        >{{ old('short_desc', $documentation->short_desc) }}</textarea>
                        @error('short_desc')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Jodit Editor -->
                    <div class="mb-4">
                        <label for="content" class="block text-sm font-medium text-gray-700 mb-2">Content</label>
                        <textarea
                            name="content"
                            id="editor"
                            class="textarea textarea-bordered w-full prose"
                            required
                        >{{ old('content', $documentation->content) }}</textarea>
                        @error('content')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Save Button -->
                    <div class="flex justify-end">
                        <button type="submit" class="btn btn-primary">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Include Jodit via CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jodit/4.2.47/es2015/jodit.fat.min.js" integrity="sha512-lO8LIfs1jytwt9MbX5/EBq5z6fN/2Hmiyrb+TdkOTKrhUQp/mmkrp1+Wyw9Tj8kokW6zU8KlH60MPZ1Xmt4JAQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jodit/4.2.47/es2015/jodit.fat.min.css" integrity="sha512-2cfnJ8ZMBqkaNXsi/6ucIcFRvKVFKW69HEP5S7L2fQtAaPrVg5XLkkUgl46kkaN5PPArXwLPCOqhbsAAiHQiXA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const editor = Jodit.make('#editor', {
                height: 400,
                toolbarSticky: false,
                events: {
                    afterInit: function (editorInstance) {
                        // Add the TailwindCSS `prose` class to the editor content area
                        editorInstance.container.classList.add('prose');
                    },
                },
            });
        });
    </script>
    
</x-app-layout>
