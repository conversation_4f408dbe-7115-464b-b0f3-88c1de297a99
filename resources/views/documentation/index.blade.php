<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Documentation
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Create New Button (Admin Only) -->
            @perms('create_document_items')
                <div class="flex justify-end mb-6">
                    <a href="{{ route('documentation.create') }}" class="btn btn-primary">
                        <i class="fa-sharp fa-plus mr-2"></i>Create New
                    </a>
                </div>
            @endperms

            <!-- Documentation List -->
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <ul class="divide-y divide-gray-200">
                    @forelse ($documentation as $doc)
                        <li class="p-6 hover:bg-gray-50 transition">
                            <div class="flex flex-col md:flex-row md:justify-between items-start md:items-center">
                                <!-- Title and Description -->
                                <div>
                                    <a href="{{ route('documentation.show', $doc) }}" class="text-xl font-bold text-blue-600 hover:underline">
                                        {{ $doc->title }}
                                    </a>
                                    <p class="text-sm text-gray-600 mt-1">
                                        {{ $doc->short_desc }}
                                    </p>
                                </div>

                                <!-- Actions (Admin Only) -->
                                @if (auth()->user()->isAdmin())
                                    <div class="mt-3 md:mt-0 flex items-center gap-2">
                                        <a href="{{ route('documentation.edit', $doc) }}" class="btn btn-sm btn-warning flex items-center gap-2">
                                            <i class="fa-sharp fa-edit"></i> Edit
                                        </a>
                                        <form action="{{ route('documentation.destroy', $doc) }}" method="POST">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-error flex items-center gap-2">
                                                <i class="fa-sharp fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </div>
                                @endif
                            </div>

                            <!-- Dates -->
                            <div class="mt-3 md:mt-0 text-sm text-gray-500">
                                <p><strong>Created:</strong> {{ $doc->created_at->format('Y-m-d H:i') }}</p>
                                <p><strong>Updated:</strong> {{ $doc->updated_at->format('Y-m-d H:i') }}</p>
                            </div>
                        </li>
                    @empty
                        <li class="p-6 text-gray-500 text-center">
                            No documentation available.
                        </li>
                    @endforelse
                </ul>
            </div>
        </div>
    </div>
</x-app-layout>
