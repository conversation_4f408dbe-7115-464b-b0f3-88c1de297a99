<div tabindex="0" class="collapse collapse-arrow border border-base-300 mb-6 shadow-lg {{ $isToday ? 'bg-base-100' : 'bg-base-200' }}">
    <input type="checkbox" {{ $isToday ? 'checked' : '' }} />
    <div class="collapse-title text-lg font-semibold flex justify-between items-center">
        <span>{{ $formattedDate }}</span>
        <div class="badge text-white px-2 py-1" style="background-color: {{ $this->getGradientColor($completed, $total) }};">
            <span id="completed-{{ $date }}">{{ $completed }}</span>/<span id="total-{{ $date }}">{{ $total }}</span>
            <span class="ps-1">Tasks Completed</span>
        </div>
    </div>
    <div class="collapse-content space-y-4">
        @foreach ($tasks['tasks'] ?? [] as $task)
            <livewire:task-item :task="$task" :date="$date" :key="$task['task_id']" />
        @endforeach
    </div>
</div>
