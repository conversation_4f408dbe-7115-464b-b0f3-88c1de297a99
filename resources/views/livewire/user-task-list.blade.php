<div class="bg-base-100 p-3 rounded-lg shadow-md">
    @php
        $completed = $tasksForToday->sum('completed_count');
        $total = $tasksForToday->sum('total_count');
        $gradientColor = $this->getGradientColorForToday($completed, $total);
    @endphp

    <div class="mb-2">
        <div class="text-white px-2 py-1 text-sm font-semibold" id="tasksCompletedHeader" style="background-color: {{ $gradientColor }};">
            <span id="today-completed">{{ $completed }}</span>/<span id="today-total">{{ $total }}</span> <span class="ms-1">Tasks Completed Today</span>
        </div>
    </div>

    @if ($tasksForToday->isEmpty())
        <p class="text-base-content/60 text-sm">No tasks for today!</p>
    @else
        <ul class="space-y-2">
            @foreach ($tasksForToday as $task)
                <li 
                    class="flex justify-between items-center p-1 border-b border-base-200"
                    data-task-id="{{ $task['task_id'] }}"
                >
                    <div class="flex-1">
                        <h3 class="task-title text-sm font-medium {{ $task['completed_count'] > 0 ? 'line-through text-base-content/40' : '' }}">
                            {{ $task['title'] }}
                        </h3>
                        <p class="text-xs text-base-content/60">
                            Due: {{ $task['due_time'] === 'Any time' ? 'Any time' : \Carbon\Carbon::parse($task['due_time'])->format('h:i A') }}
                        </p>
                    </div>
                    <button 
                        class="btn btn-ghost btn-circle toggle-task-btn"
                        wire:click="toggleTaskCompletion({{ $task['task_id'] }})"
                    >
                        <i class="fa-sharp fa-circle-check {{ $task['completed_count'] > 0 ? 'text-success' : 'text-base-content/40' }}"></i>
                    </button>
                </li>
            @endforeach
        </ul>
    @endif
</div>

