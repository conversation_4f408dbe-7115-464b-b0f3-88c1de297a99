<x-form-section submit="updateProfileInformation">
    <x-slot name="title">
        {{ __('Profile Information') }}
    </x-slot>

    <x-slot name="description">
        {{ __('Update your account\'s profile information and email address.') }}
    </x-slot>

    <x-slot name="form">
        @if (session()->has('message'))
            <div class="col-span-6 sm:col-span-4 mb-4">
                <div class="alert alert-success" role="alert">
                    <span>{{ session('message') }}</span>
                </div>
            </div>
        @endif

        <!-- Profile Photo -->
        <div class="col-span-6 sm:col-span-4">
            <div class="form-control">
                <label class="label">
                    <span class="label-text">{{ __('Profile Photo') }}</span>
                </label>

                <!-- Current Profile Photo -->
                <div class="avatar mb-4">
                    <div class="w-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                        @if ($photo)
                            <!-- Preview of new photo -->
                            <img src="{{ $photo->temporaryUrl() }}" alt="New profile photo">
                        @else
                            <!-- Current photo -->
                            <img src="{{ $user->profilePhotoUrl }}" alt="{{ $user->name }}">
                        @endif
                    </div>
                </div>

                <!-- New Profile Photo -->
                <input type="file" class="file-input file-input-bordered w-full" wire:model.live="photo" accept="image/jpeg,image/png,image/webp">

                <div wire:loading wire:target="photo" class="mt-2">
                    <div class="alert alert-info">
                        <i class="fa-sharp fa-solid fa-spinner fa-spin"></i>
                        <span>{{ __('Uploading...') }}</span>
                    </div>
                </div>

                <x-input-error for="photo" class="mt-2" />
            </div>
        </div>

        <!-- Name -->
        <div class="col-span-6 sm:col-span-4">
            <div class="form-control w-full">
                <label for="name" class="label">
                    <span class="label-text">{{ __('Name') }}</span>
                </label>
                <input id="name" type="text" class="input input-bordered w-full" wire:model="state.name" required autocomplete="name" />
                <x-input-error for="name" class="mt-2" />
            </div>
        </div>

        <!-- Email -->
        <div class="col-span-6 sm:col-span-4">
            <div class="form-control w-full">
                <label for="email" class="label">
                    <span class="label-text">{{ __('Email') }}</span>
                </label>
                <input id="email" type="email" class="input input-bordered w-full" wire:model="state.email" required autocomplete="username" />
                <x-input-error for="email" class="mt-2" />

                @if (Laravel\Fortify\Features::enabled(Laravel\Fortify\Features::emailVerification()) && ! $user->hasVerifiedEmail())
                    <div class="alert alert-warning mt-4">
                        <i class="fa-sharp fa-exclamation-triangle"></i>
                        <div>
                            <span>{{ __('Your email address is unverified.') }}</span>
                            <button type="button" class="btn btn-xs btn-ghost" wire:click.prevent="sendEmailVerification">
                                {{ __('Click here to re-send the verification email.') }}
                            </button>
                        </div>
                    </div>

                    @if ($this->verificationLinkSent)
                        <div class="alert alert-success mt-2">
                            <i class="fa-sharp fa-check-circle"></i>
                            <span>{{ __('A new verification link has been sent to your email address.') }}</span>
                        </div>
                    @endif
                @endif
            </div>
        </div>
    </x-slot>

    <x-slot name="actions">
        <x-action-message class="me-3" on="saved">
            {{ __('Saved.') }}
        </x-action-message>

        <button type="submit" class="btn btn-primary" wire:loading.attr="disabled" wire:target="photo">
            {{ __('Save') }}
        </button>
    </x-slot>
</x-form-section>
