<div>
    @php
    // Ensure $occurrence is an array and contains the expected keys
    $isCompleted = isset($occurrence['completed_count']) && $occurrence['completed_count'] > 0;
    $dueTimeFormatted = isset($occurrence['due_time']) && $occurrence['due_time'] === 'Any time'
        ? 'Any time'
        : (isset($occurrence['due_time']) ? \Carbon\Carbon::parse($occurrence['due_time'])->format('h:i A') : 'No Time Set');
@endphp
<li class="flex justify-between items-center p-4 bg-base-100 rounded-lg shadow-md hover:bg-gray-200 transition-all" data-task-id="{{ $task['task_id'] }}">
    <div class="flex-1 cursor-pointer" onclick="showTaskDetails('{{ $task['task_id'] }}')">
        <h3 class="task-title font-semibold text-lg {{ $task['completed_count'] > 0 ? 'line-through text-gray-400' : '' }}">
            {{ $task['title'] }}
        </h3>
        <p class="text-sm text-gray-500">
            Due: {{ $task['due_time'] === 'Any time' ? 'Any time' : \Carbon\Carbon::parse($task['due_time'])->format('h:i A') }}
        </p>
        @if (!empty($task['description']))
            <p class="text-sm text-gray-600">{{ $task['description'] }}</p>
        @endif
    </div>
    @perms('create_edit_own_tasks')
        @if (auth()->id() === $task['created_by'])
            <a href="{{ route('tasks.edit', $task['task_id']) }}" class="btn btn-ghost btn-circle">
                <i class="fa-sharp fa-calendar-pen text-gray-500"></i>
            </a>
        @endif
    @endperms
    <div class="tooltip" data-tip="{{ $task['completed_count'] > 0 ? 'Mark Incomplete' : 'Mark Complete' }}">
        <button class="btn btn-ghost btn-circle toggle-task-btn" wire:click="toggleTaskCompletion('{{ $task['task_id'] }}')">
            <i class="fa-sharp fa-circle-check {{ $task['completed_count'] > 0 ? 'text-green-500' : 'text-gray-500' }}"></i>
        </button>
    </div>
</li>
</div>