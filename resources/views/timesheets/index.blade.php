<x-app-layout
    page-title="Generate Timesheet"
    page-icon="fa-sharp fa-file-invoice"
    :action-buttons="[
        [
            'name' => 'Time Cards',
            'route' => route('timecards.index'),
            'icon' => 'fa-sharp fa-calendar-days',
            'class' => 'btn btn-secondary btn-sm gap-2'
        ],
        [
            'name' => 'Time Clock',
            'route' => route('time-clock'),
            'icon' => 'fa-sharp fa-clock',
            'class' => 'btn btn-primary btn-sm gap-2'
        ]
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumbs -->
            <x-breadcrumbs :items="[
                ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
                ['name' => 'Timesheets', 'icon' => 'fa-file-invoice']
            ]" />
        </div>
            </div>
    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100 overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-base-content mb-4">Generate Timesheet for Accounting</h3>
                    
                    <form action="{{ route('timesheets.generate') }}" method="POST">
                        @csrf
                        
                        <!-- Quick Date Filters -->
                        <div class="flex flex-wrap gap-1 mb-4">
                            <button type="button" class="btn btn-xs btn-outline date-shortcut" 
                                data-start="{{ now()->startOfMonth()->format('Y-m-d') }}" 
                                data-end="{{ now()->endOfMonth()->format('Y-m-d') }}">
                                This Month
                            </button>
                            <button type="button" class="btn btn-xs btn-outline date-shortcut" 
                                data-start="{{ now()->subMonth()->startOfMonth()->format('Y-m-d') }}" 
                                data-end="{{ now()->subMonth()->endOfMonth()->format('Y-m-d') }}">
                                Last Month
                            </button>
                            <button type="button" class="btn btn-xs btn-outline date-shortcut" 
                                data-start="{{ now()->startOfMonth()->format('Y-m-d') }}" 
                                data-end="{{ now()->startOfMonth()->addDays(14)->format('Y-m-d') }}">
                                1st-15th
                            </button>
                            <button type="button" class="btn btn-xs btn-outline date-shortcut" 
                                data-start="{{ now()->startOfMonth()->addDays(15)->format('Y-m-d') }}" 
                                data-end="{{ now()->endOfMonth()->format('Y-m-d') }}">
                                16th-End
                            </button>
                            <button type="button" class="btn btn-xs btn-outline date-shortcut" 
                                data-start="{{ now()->subDays(90)->format('Y-m-d') }}" 
                                data-end="{{ now()->format('Y-m-d') }}">
                                Last 90 Days
                            </button>
                            <button type="button" class="btn btn-xs btn-outline date-shortcut" 
                                data-start="{{ now()->startOfQuarter()->format('Y-m-d') }}" 
                                data-end="{{ now()->endOfQuarter()->format('Y-m-d') }}">
                                This Quarter
                            </button>
                            <button type="button" class="btn btn-xs btn-outline date-shortcut" 
                                data-start="{{ now()->startOfYear()->format('Y-m-d') }}" 
                                data-end="{{ now()->endOfYear()->format('Y-m-d') }}">
                                This Year
                            </button>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label for="start_date" class="block text-sm font-medium text-base-content mb-1">Start Date</label>
                                <input type="date" name="start_date" id="start_date" 
                                    class="input input-bordered w-full" 
                                    value="{{ $startDate }}" required>
                            </div>
                            
                            <div>
                                <label for="end_date" class="block text-sm font-medium text-base-content mb-1">End Date</label>
                                <input type="date" name="end_date" id="end_date" 
                                    class="input input-bordered w-full" 
                                    value="{{ $endDate }}" required>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-base-content mb-1">Select Employees</label>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach($users as $user)
                                <div class="form-control">
                                    <label class="cursor-pointer label justify-start gap-2">
                                        <input type="checkbox" name="user_ids[]" value="{{ $user->id }}" class="checkbox checkbox-primary">
                                        <span class="label-text">{{ $user->name }}</span>
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div>
                                <button type="button" id="select-all-btn" class="btn btn-sm btn-outline">Select All</button>
                                <button type="button" id="deselect-all-btn" class="btn btn-sm btn-outline ml-2">Deselect All</button>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fa-sharp fa-solid fa-file-invoice mr-2"></i>
                                Generate Timesheet
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllBtn = document.getElementById('select-all-btn');
            const deselectAllBtn = document.getElementById('deselect-all-btn');
            const checkboxes = document.querySelectorAll('input[name="user_ids[]"]');
            const dateShortcuts = document.querySelectorAll('.date-shortcut');
            const startDateInput = document.getElementById('start_date');
            const endDateInput = document.getElementById('end_date');
            
            // Handle date shortcut buttons
            dateShortcuts.forEach(button => {
                button.addEventListener('click', function() {
                    const startDate = this.getAttribute('data-start');
                    const endDate = this.getAttribute('data-end');
                    
                    startDateInput.value = startDate;
                    endDateInput.value = endDate;
                    
                    // Add visual feedback
                    dateShortcuts.forEach(btn => btn.classList.remove('btn-primary'));
                    this.classList.remove('btn-outline');
                    this.classList.add('btn-primary');
                    
                    // After a short delay, reset the button appearance
                    setTimeout(() => {
                        this.classList.remove('btn-primary');
                        this.classList.add('btn-outline');
                    }, 500);
                });
            });
            
            selectAllBtn.addEventListener('click', function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = true;
                });
            });
            
            deselectAllBtn.addEventListener('click', function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
            });
            
            // Validate form on submit
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                const checkedBoxes = document.querySelectorAll('input[name="user_ids[]"]:checked');
                if (checkedBoxes.length === 0) {
                    e.preventDefault();
                    alert('Please select at least one employee.');
                }
                
                const startDate = new Date(document.getElementById('start_date').value);
                const endDate = new Date(document.getElementById('end_date').value);
                
                if (startDate > endDate) {
                    e.preventDefault();
                    alert('Start date cannot be after end date.');
                }
                
                const dayDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
                if (dayDiff > 90) {
                    e.preventDefault();
                    alert('Date range cannot exceed 90 days.');
                }
            });
        });
    </script>
    @endpush
</x-app-layout>
