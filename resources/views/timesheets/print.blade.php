<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timesheet Report - {{ $startDate->format('M d, Y') }} to {{ $endDate->format('M d, Y') }}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #fafafa;
        }

        h1 {
            font-size: 18px;
            margin-bottom: 5px;
            color: #1976d2;
        }

        h2 {
            font-size: 16px;
            margin-top: 20px;
            margin-bottom: 10px;
            border-bottom: 2px solid #7b1fa2;
            padding-bottom: 5px;
            color: #4a148c;
        }

        h3 {
            font-size: 14px;
            margin-top: 15px;
            margin-bottom: 5px;
            color: #0288d1;
        }

        p {
            margin: 5px 0;
        }

        .header {
            margin-bottom: 20px;
            border-bottom: 2px solid #2196f3;
            padding-bottom: 10px;
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #2196f3;
        }

        .employee-section {
            margin-bottom: 30px;
            page-break-after: always;
            border: 1px solid #2196f3;
            padding: 15px;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .employee-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            background-color: #e3f2fd;
            padding: 12px;
            border: 1px solid #2196f3;
            border-radius: 6px;
        }

        .summary-stats {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 15px;
            gap: 10px;
        }

        .stat-box {
            border: 1px solid #dee2e6;
            padding: 12px;
            flex: 1;
            min-width: 140px;
            border-radius: 6px;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .stat-icon {
            font-size: 24px;
            color: #6c757d;
            width: 32px;
            text-align: center;
            flex-shrink: 0;
        }

        .stat-content {
            flex: 1;
        }

        .stat-title {
            font-weight: bold;
            font-size: 11px;
            color: #555;
            margin-bottom: 2px;
        }

        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 2px;
        }

        .stat-desc {
            font-size: 9px;
            color: #666;
            line-height: 1.2;
        }

        /* Highlight billable hours with accent color */
        .stat-box.billable {
            background-color: #e8f5e8;
            border-color: #28a745;
        }

        .stat-box.billable .stat-icon {
            color: #28a745;
        }

        .stat-box.billable .stat-value {
            color: #155724;
        }



        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #e3f2fd;
            font-weight: bold;
            color: #1976d2;
            border-bottom: 2px solid #2196f3;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .week-header {
            background-color: #e3f2fd;
            padding: 10px;
            margin-top: 15px;
            margin-bottom: 10px;
            border: 1px solid #2196f3;
            border-radius: 6px;
            display: flex;
            justify-content: space-between;
            color: #1976d2;
        }

        .week-breakdown {
        }

        .week-total-row {
            background-color: #e3f2fd;
            font-weight: bold;
            border-top: 2px solid #2196f3;
        }

        .billable-highlight {
            font-weight: bold;
            color: #28a745;
        }

        /* Weekend row styling */
        tr:has(td:contains("Weekend")) {
            background-color: #fff3e0;
        }

        .page-break {
            page-break-after: always;
        }

        .footer {
            margin-top: 20px;
            border-top: 1px solid #2196f3;
            padding-top: 10px;
            font-size: 10px;
            text-align: center;
            background-color: #e3f2fd;
            padding: 10px;
            border-radius: 6px;
            color: #1976d2;
        }



        /* Text summary styling */
        .text-summary {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px;
            margin: 15px 0;
            font-style: italic;
            color: #495057;
            line-height: 1.5;
        }

        /* Add some hover effects for screen viewing */
        @media screen {
            .stat-box:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                transition: all 0.2s ease;
            }
        }

        /* Print optimizations */
        @media print {
            body {
                background-color: white !important;
            }

            .employee-section {
                box-shadow: none;
            }

            table {
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Timesheet Report</h1>
        <p>Period: {{ $startDate->format('M d, Y') }} - {{ $endDate->format('M d, Y') }}</p>
    </div>

    @foreach($timesheetData as $userData)
    <div class="employee-section">
        <div class="employee-header">
            <div>
                <h2>{{ $userData['user']->name }}</h2>
            </div>
            <div>
                <span style="font-weight: bold; color: #28a745;"><i class="fas fa-calculator"></i> Total Billable Hours:</span>
                <span style="font-size: 16px; font-weight: bold; color: #155724; background-color: #e8f5e8; padding: 4px 8px; border-radius: 4px; border: 1px solid #28a745;">{{ number_format($userData['total_billable_hours'], 2) }}</span>
            </div>
        </div>

        <h3>Summary</h3>
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-title">Regular Hours</div>
                    <div class="stat-value">{{ number_format($userData['total_regular_hours'], 2) }}</div>
                    <div class="stat-desc">Work hours (≤ 40/week)</div>
                </div>
            </div>

            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-title">Overtime</div>
                    <div class="stat-value">{{ number_format($userData['total_overtime_hours'], 2) }}</div>
                    <div class="stat-desc">Hours > 40/week</div>
                </div>
            </div>

            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-user-injured"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-title">Sick Time</div>
                    <div class="stat-value">{{ number_format($userData['total_sick_time'], 2) }}</div>
                    <div class="stat-desc">Paid sick leave</div>
                </div>
            </div>

            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-umbrella-beach"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-title">Vacation</div>
                    <div class="stat-value">{{ number_format($userData['total_vacation_time'], 2) }}</div>
                    <div class="stat-desc">Paid vacation time</div>
                </div>
            </div>
        </div>

        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-briefcase"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-title">Total Work Hours</div>
                    <div class="stat-value">{{ number_format($userData['total_hours'], 2) }}</div>
                </div>
            </div>

            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-coffee"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-title">Total Break Hours</div>
                    <div class="stat-value">{{ number_format($userData['total_break_hours'], 2) }}</div>
                </div>
            </div>

            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-title">Total PTO Hours</div>
                    <div class="stat-value">{{ number_format($userData['total_pto'], 2) }}</div>
                </div>
            </div>

            <div class="stat-box billable">
                <div class="stat-icon">
                    <i class="fas fa-calculator"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-title">Total Billable</div>
                    <div class="stat-value">{{ number_format($userData['total_billable_hours'], 2) }}</div>
                </div>
            </div>
        </div>

        <!-- Text Summary -->
        <div class="text-summary">
            <strong>{{ $userData['user']->name }}</strong> has <strong>{{ number_format($userData['total_billable_hours'], 2) }} total billable hours</strong> from {{ $startDate->format('M d, Y') }} to {{ $endDate->format('M d, Y') }}.
            <strong>{{ number_format($userData['total_regular_hours'], 2) }} hours</strong> were billable at their regular rate and
            <strong>{{ number_format($userData['total_overtime_hours'], 2) }} hours</strong> need overtime pay (1.5x).
            They used <strong>{{ number_format($userData['total_vacation_time'], 2) }} hours of vacation</strong> and
            <strong>{{ number_format($userData['total_sick_time'], 2) }} hours of sick leave</strong>.
        </div>

        <h3 style="margin-top: 30px;">Weekly Breakdown</h3>

        @foreach($userData['weeks'] as $weekData)
        @if($weekData['in_range'] && ($weekData['total_hours_numeric'] > 0 || $weekData['total_pto_numeric'] > 0 || $weekData['total_break_hours_numeric'] > 0))
        <div class="week-header">
            <div>
                <strong><i class="fas fa-calendar-week"></i> Week: {{ $weekData['start_date']->format('M d') }} - {{ $weekData['end_date']->format('M d, Y') }}</strong>
            </div>
            <div>
                <strong><i class="fas fa-calculator"></i> Billable: {{ number_format($weekData['total_billable_hours_numeric'], 2) }} hrs</strong>
            </div>
        </div>

        <div class="summary-stats week-breakdown">
            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-title">Regular Hours</div>
                    <div class="stat-value">{{ number_format($weekData['total_regular_hours_numeric'], 2) }}</div>
                </div>
            </div>

            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-title">Overtime</div>
                    <div class="stat-value">{{ number_format($weekData['total_overtime_hours_numeric'], 2) }}</div>
                </div>
            </div>

            <div class="stat-box">
                <div class="stat-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-title">PTO</div>
                    <div class="stat-value">{{ number_format($weekData['total_pto_numeric'], 2) }}</div>
                </div>
            </div>

            <div class="stat-box billable">
                <div class="stat-icon">
                    <i class="fas fa-calculator"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-title">Billable</div>
                    <div class="stat-value">{{ number_format($weekData['total_billable_hours_numeric'], 2) }}</div>
                </div>
            </div>
        </div>

        <table class="week-breakdown">
            <thead>
                <tr>
                    <th>Date</th>
                    <th class="text-right">Work Hours</th>
                    <th class="text-right">Break</th>
                    <th class="text-right">Sick</th>
                    <th class="text-right">Vacation</th>
                    <th class="text-right">Billable</th>
                </tr>
            </thead>
            <tbody>
                @foreach($weekData['days'] as $date => $dayData)
                @if($dayData['in_range'])
                <tr>
                    <td>
                        {{ $dayData['date']->format('D, M d') }}
                        @if($dayData['date']->isWeekend())
                            <small>(Weekend)</small>
                        @endif
                    </td>
                    <td class="text-right">
                        @php
                            $workHours = $dayData['total_seconds'] / 3600;
                        @endphp
                        {{ number_format($workHours, 2) }}
                    </td>
                    <td class="text-right">
                        {{ number_format($dayData['break_seconds'] / 3600, 2) }}
                    </td>
                    <td class="text-right">
                        {{ number_format($dayData['sick_seconds'] / 3600, 2) }}
                    </td>
                    <td class="text-right">
                        {{ number_format($dayData['vacation_seconds'] / 3600, 2) }}
                    </td>
                    <td class="text-right billable-highlight">
                        @php
                            $totalBillable = $workHours + ($dayData['sick_seconds'] / 3600) + ($dayData['vacation_seconds'] / 3600);
                        @endphp
                        {{ number_format($totalBillable, 2) }}
                    </td>
                </tr>
                @endif
                @endforeach
                <tr class="week-total-row">
                    <td>Weekly Total</td>
                    <td class="text-right">{{ number_format($weekData['total_hours_numeric'], 2) }}</td>
                    <td class="text-right">{{ number_format($weekData['total_break_hours_numeric'], 2) }}</td>
                    <td class="text-right">{{ number_format($weekData['total_sick_time_numeric'], 2) }}</td>
                    <td class="text-right">{{ number_format($weekData['total_vacation_time_numeric'], 2) }}</td>
                    <td class="text-right billable-highlight">{{ number_format($weekData['total_billable_hours_numeric'], 2) }}</td>
                </tr>
            </tbody>
        </table>
        @endif
        @endforeach
    </div>
    @endforeach

    <div class="footer">
        <p>Generated on {{ now()->format('M d, Y h:i A') }}</p>
    </div>
</body>
</html>
