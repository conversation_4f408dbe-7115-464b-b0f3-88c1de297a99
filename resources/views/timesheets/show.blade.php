@php
    // Build action buttons array dynamically
    $actionButtons = [
        [
            'name' => 'Print Friendly View',
            'route' => '#',
            'icon' => 'fa-sharp fa-print',
            'class' => 'btn btn-primary btn-sm gap-2',
            'id' => 'print-friendly-btn'
        ],
        [
            'name' => 'Print Current View',
            'route' => '#',
            'icon' => 'fa-sharp fa-print',
            'class' => 'btn btn-secondary btn-sm gap-2',
            'id' => 'print-current-btn'
        ]
    ];
@endphp

<x-app-layout
    page-title="Timesheet Report: {{ $startDate->format('M d, Y') }} - {{ $endDate->format('M d, Y') }}"
    page-icon="fa-sharp fa-file-invoice"
    :primary-buttons="[
        [
            'type' => 'back',
            'route' => route('timesheets.index'),
            'text' => 'Back to Generator'
        ]
    ]"
    :action-buttons="$actionButtons"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Timesheets', 'route' => 'timesheets.index', 'icon' => 'fa-file-invoice'],
        ['name' => 'Report: ' . $startDate->format('M d') . ' - ' . $endDate->format('M d'), 'icon' => 'fa-file-invoice']
    ]">

    <!-- Hidden form for print friendly view -->
    <form id="print-friendly-form" action="{{ route('timesheets.print') }}" method="POST" style="display: none;">
        @csrf
        <input type="hidden" name="start_date" value="{{ $startDate->format('Y-m-d') }}">
        <input type="hidden" name="end_date" value="{{ $endDate->format('Y-m-d') }}">
        @foreach ($users as $user)
            <input type="hidden" name="user_ids[]" value="{{ $user->id }}">
        @endforeach
    </form>

    <div class="py-6 lg:py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
            <!-- Timesheet Header -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-calendar-week text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Pay Period Summary</h4>
                    </div>
                </div>
                <div class="p-6 space-y-6">
                    <div class="flex flex-col md:flex-row justify-between items-center gap-4">
                        <div class="flex items-center gap-3">
                            <span class="bg-primary text-primary-content px-4 py-2 rounded-lg shadow-sm flex items-center">
                                <i class="fa-sharp fa-calendar-days mr-2"></i>
                                {{ $startDate->format('M d, Y') }} - {{ $endDate->format('M d, Y') }}
                            </span>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-base-200 p-4 rounded-lg shadow-sm">
                                <div class="flex justify-between items-center mb-1">
                                    <span class="font-medium text-sm">Total Employees</span>
                                    <i class="fa-sharp fa-users text-primary"></i>
                                </div>
                                <div class="text-3xl font-bold text-primary">{{ count($timesheetData) }}</div>
                            </div>
                            <div class="bg-base-200 p-4 rounded-lg shadow-sm">
                                <div class="flex justify-between items-center mb-1">
                                    <span class="font-medium text-sm">Period Length</span>
                                    <i class="fa-sharp fa-calendar-day text-secondary"></i>
                                </div>
                                <div class="text-3xl font-bold text-secondary">
                                    {{ $startDate->diffInDays($endDate) + 1 }} <span
                                        class="text-sm font-normal">days</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Employee Timesheets -->
            @foreach ($timesheetData as $userData)
                <div class="card bg-base-100 shadow-md print:page-break-after">
                    <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-user text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">{{ $userData['user']->name }} - Employee Timesheet</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-6">
                        <!-- Employee Header with Billable Hours -->
                        <div class="flex flex-col md:flex-row justify-between items-center bg-base-200 p-6 rounded-xl shadow-sm">
                            <div class="flex items-center gap-4">
                                <div class="avatar online">
                                    <div class="w-16 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                                        <img src="{{ $userData['user']->profilePhotoUrl }}" alt="{{ $userData['user']->name }}" />
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-2xl font-bold text-base-content">{{ $userData['user']->name }}</h4>
                                    <p class="text-sm bg-base-300 px-3 py-1 rounded-full inline-block mt-1">
                                        <i class="fa-sharp fa-calendar-week mr-1 text-primary"></i>
                                        {{ $startDate->format('M d') }} - {{ $endDate->format('M d, Y') }}
                                    </p>
                                </div>
                            </div>
                            <div class="mt-4 md:mt-0 bg-neutral text-neutral-content p-4 rounded-lg shadow-md">
                                <div class="text-xs uppercase font-semibold flex items-center gap-1">
                                    <i class="fa-sharp fa-calculator"></i> Total Billable Hours
                                </div>
                                <div class="text-3xl font-bold">
                                    {{ number_format($userData['total_billable_hours'], 2) }}</div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                            <!-- Row 1 -->
                            <div class="stat bg-secondary text-secondary-content shadow-sm rounded-lg p-4">
                                <div class="flex justify-between">
                                    <div class="stat-title font-medium text-secondary-content opacity-90">Regular Hours</div>
                                    <div class="stat-figure text-secondary-content">
                                        <i class="fa-sharp fa-clock text-2xl"></i>
                                    </div>
                                </div>
                                <div class="stat-value text-2xl text-secondary-content">
                                    {{ number_format($userData['total_regular_hours'], 2) }}</div>
                                <div class="stat-desc mt-1 text-secondary-content opacity-75">Work hours (≤ 40/week)</div>
                            </div>

                            <div class="stat bg-accent text-accent-content shadow-sm rounded-lg p-4">
                                <div class="flex justify-between">
                                    <div class="stat-title font-medium text-accent-content opacity-90">Overtime</div>
                                    <div class="stat-figure text-accent-content">
                                        <i class="fa-sharp fa-bolt text-2xl"></i>
                                    </div>
                                </div>
                                <div class="stat-value text-2xl text-accent-content">
                                    {{ number_format($userData['total_overtime_hours'], 2) }}</div>
                                <div class="stat-desc mt-1 text-accent-content opacity-75">Hours > 40/week</div>
                            </div>

                            <div class="stat bg-error text-error-content shadow-sm rounded-lg p-4">
                                <div class="flex justify-between">
                                    <div class="stat-title font-medium text-error-content opacity-90">Sick Time</div>
                                    <div class="stat-figure text-error-content">
                                        <i class="fa-sharp fa-head-side-mask text-2xl"></i>
                                    </div>
                                </div>
                                <div class="stat-value text-2xl text-error-content">
                                    {{ number_format($userData['total_sick_time'], 2) }}</div>
                                <div class="stat-desc mt-1 text-error-content opacity-75">Paid sick leave</div>
                            </div>

                            <div class="stat bg-info text-info-content shadow-sm rounded-lg p-4">
                                <div class="flex justify-between">
                                    <div class="stat-title font-medium text-info-content opacity-90">Vacation</div>
                                    <div class="stat-figure text-info-content">
                                        <i class="fa-sharp fa-umbrella-beach text-2xl"></i>
                                    </div>
                                </div>
                                <div class="stat-value text-2xl text-info-content">
                                    {{ number_format($userData['total_vacation_time'], 2) }}</div>
                                <div class="stat-desc mt-1 text-info-content opacity-75">Paid vacation time</div>
                            </div>

                            <!-- Row 2 -->
                            <div class="stat bg-primary text-primary-content shadow-sm rounded-lg p-4">
                                <div class="flex justify-between">
                                    <div class="stat-title font-medium text-primary-content opacity-90">Total Work Hours</div>
                                    <div class="stat-figure text-primary-content">
                                        <i class="fa-sharp fa-business-time text-2xl"></i>
                                    </div>
                                </div>
                                <div class="stat-value text-2xl text-primary-content">
                                    {{ number_format($userData['total_hours'], 2) }}</div>
                                <div class="stat-desc mt-1 text-primary-content opacity-75">All logged work time</div>
                            </div>

                            <div class="stat bg-base-300 text-base-content shadow-sm rounded-lg p-4">
                                <div class="flex justify-between">
                                    <div class="stat-title font-medium text-base-content opacity-90">Total Break Hours</div>
                                    <div class="stat-figure text-base-content">
                                        <i class="fa-sharp fa-mug-hot text-2xl"></i>
                                    </div>
                                </div>
                                <div class="stat-value text-2xl text-base-content">
                                    {{ number_format($userData['total_break_hours'], 2) }}</div>
                                <div class="stat-desc mt-1 text-base-content opacity-75">Non-working time</div>
                            </div>

                            <div class="stat bg-warning text-warning-content shadow-sm rounded-lg p-4">
                                <div class="flex justify-between">
                                    <div class="stat-title font-medium text-warning-content opacity-90">Total PTO Hours</div>
                                    <div class="stat-figure text-warning-content">
                                        <i class="fa-sharp fa-umbrella-beach text-2xl"></i>
                                    </div>
                                </div>
                                <div class="stat-value text-2xl text-warning-content">
                                    {{ number_format($userData['total_pto'], 2) }}</div>
                                <div class="stat-desc mt-1 text-warning-content opacity-75">Sick + Vacation time</div>
                            </div>

                            <div class="stat bg-success text-success-content shadow-sm rounded-lg p-4">
                                <div class="flex justify-between">
                                    <div class="stat-title font-medium text-success-content opacity-90">Total Billable</div>
                                    <div class="stat-figure text-success-content">
                                        <i class="fa-sharp fa-calculator text-2xl"></i>
                                    </div>
                                </div>
                                <div class="stat-value text-2xl text-success-content">
                                    {{ number_format($userData['total_billable_hours'], 2) }}</div>
                                <div class="stat-desc mt-1 text-success-content opacity-75">Work + PTO hours</div>
                            </div>
                        </div>


                        <!-- Weekly Breakdown -->
                        <div class="flex items-center gap-3">
                            <div class="flex-grow h-0.5 bg-base-300"></div>
                            <div
                                class="bg-base-300 text-base-content py-2 px-4 rounded-lg shadow-sm flex items-center gap-2">
                                <i class="fa-sharp fa-calendar-week"></i>
                                <span class="text-base font-semibold">Weekly Breakdown</span>
                            </div>
                            <div class="flex-grow h-0.5 bg-base-300"></div>
                        </div>

                        <div class="space-y-6">
                            @foreach ($userData['weeks'] as $weekData)
                                @if ($weekData['in_range'])
                                    <div class="card bg-base-100 shadow-md">
                                        <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                                            <div class="flex flex-col md:flex-row justify-between items-center">
                                                <div class="flex items-center gap-3">
                                                    <div class="avatar avatar-placeholder">
                                                        <div class="bg-info text-info-content w-8 rounded-lg">
                                                            <i class="fa-sharp fa-calendar-week text-sm"></i>
                                                        </div>
                                                    </div>
                                                    <h5 class="text-lg font-semibold text-base-content">
                                                        Week: {{ $weekData['start_date']->format('M d') }} -
                                                        {{ $weekData['end_date']->format('M d, Y') }}
                                                    </h5>
                                                </div>
                                                <div class="flex items-center gap-3 mt-3 md:mt-0">
                                                    <div
                                                        class="bg-neutral text-neutral-content px-3 py-2 rounded-md gap-1 flex items-center shadow-sm">
                                                        <i class="fa-sharp fa-calculator mr-1"></i>
                                                        <span class="font-medium">Billable:
                                                            {{ number_format($weekData['total_billable_hours'], 2) }}
                                                            hrs</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="p-6 space-y-6">

                                            <!-- Week Stats -->
                                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                                <div class="bg-secondary text-secondary-content rounded-lg p-3 shadow-sm">
                                                    <div class="text-xs font-medium flex items-center justify-between text-secondary-content opacity-90">
                                                        <span>Regular Hours</span>
                                                        <i class="fa-sharp fa-clock text-secondary-content"></i>
                                                    </div>
                                                    <div class="text-lg font-bold text-secondary-content mt-1">
                                                        {{ number_format($weekData['total_regular_hours_numeric'], 2) }}
                                                    </div>
                                                </div>

                                                <div class="bg-accent text-accent-content rounded-lg p-3 shadow-sm">
                                                    <div class="text-xs font-medium flex items-center justify-between text-accent-content opacity-90">
                                                        <span>Overtime</span>
                                                        <i class="fa-sharp fa-bolt text-accent-content"></i>
                                                    </div>
                                                    <div class="text-lg font-bold text-accent-content mt-1">
                                                        {{ number_format($weekData['total_overtime_hours_numeric'], 2) }}
                                                    </div>
                                                </div>

                                                <div class="bg-warning text-warning-content rounded-lg p-3 shadow-sm">
                                                    <div class="text-xs font-medium flex items-center justify-between text-warning-content opacity-90">
                                                        <span>PTO Hours</span>
                                                        <i class="fa-sharp fa-umbrella-beach text-warning-content"></i>
                                                    </div>
                                                    <div class="text-lg font-bold text-warning-content mt-1">
                                                        {{ number_format($weekData['total_pto_numeric'], 2) }}
                                                    </div>
                                                </div>

                                                <div class="bg-success text-success-content rounded-lg p-3 shadow-sm">
                                                    <div class="text-xs font-medium flex items-center justify-between text-success-content opacity-90">
                                                        <span>Billable Hours</span>
                                                        <i class="fa-sharp fa-calculator text-success-content"></i>
                                                    </div>
                                                    <div class="text-lg font-bold text-success-content mt-1">
                                                        {{ number_format($weekData['total_billable_hours_numeric'], 2) }}
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Daily Breakdown -->
                                            <div class="overflow-x-auto rounded-lg border border-base-200">
                                                <table class="table table-zebra table-sm w-full">
                                                    <thead class="bg-base-200 text-xs uppercase">
                                                        <tr>
                                                            <th class="text-left bg-base-200">Date</th>
                                                            <th class="text-right bg-base-200 text-secondary">Work
                                                                Hours</th>
                                                            <th class="text-right bg-base-200 text-accent">Break</th>
                                                            <th class="text-right bg-base-200 text-error">Sick</th>
                                                            <th class="text-right bg-base-200 text-info">Vacation</th>
                                                            <th
                                                                class="text-right bg-base-200 text-success font-medium">
                                                                Billable</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody class="text-sm bg-base-100">
                                                        @foreach ($weekData['days'] as $date => $dayData)
                                                            @if ($dayData['in_range'])
                                                                <tr>
                                                                    <td class="text-left">
                                                                        <div class="flex items-center gap-1">
                                                                            <span
                                                                                class="{{ $dayData['date']->isWeekend() ? 'text-accent font-medium' : '' }}">
                                                                                {{ $dayData['date']->format('D, M d') }}
                                                                            </span>
                                                                            @if ($dayData['date']->isWeekend())
                                                                                <span
                                                                                    class="badge badge-xs badge-accent text-accent-content">Weekend</span>
                                                                            @endif
                                                                            @if ($dayData['date']->isToday())
                                                                                <span
                                                                                    class="badge badge-xs badge-primary">Today</span>
                                                                            @endif
                                                                        </div>
                                                                    </td>
                                                                    <td class="text-right font-medium">
                                                                        @php
                                                                            // Work hours are already calculated correctly in the time card
                                                                            $workHours =
                                                                                $dayData['total_seconds'] / 3600;
                                                                        @endphp
                                                                        {{ number_format($workHours, 2) }}
                                                                    </td>
                                                                    <td class="text-right">
                                                                        {{ number_format($dayData['break_seconds'] / 3600, 2) }}
                                                                    </td>
                                                                    <td
                                                                        class="text-right {{ $dayData['sick_seconds'] > 0 ? 'text-error font-medium' : '' }}">
                                                                        {{ number_format($dayData['sick_seconds'] / 3600, 2) }}
                                                                    </td>
                                                                    <td
                                                                        class="text-right {{ $dayData['vacation_seconds'] > 0 ? 'text-info font-medium' : '' }}">
                                                                        {{ number_format($dayData['vacation_seconds'] / 3600, 2) }}
                                                                    </td>
                                                                    <td class="text-right font-medium text-success">
                                                                        @php
                                                                            // Billable hours = Work hours + PTO
                                                                            $totalBillable =
                                                                                $workHours +
                                                                                $dayData['sick_seconds'] / 3600 +
                                                                                $dayData['vacation_seconds'] / 3600;
                                                                        @endphp
                                                                        {{ number_format($totalBillable, 2) }}
                                                                    </td>
                                                                </tr>
                                                            @endif
                                                        @endforeach
                                                        <!-- Weekly Total Row -->
                                                        <tr class="bg-base-200 font-medium border-t-2 border-base-300">
                                                            <td class="text-left"><strong>Weekly Total</strong></td>
                                                            <td class="text-right text-secondary">
                                                                {{ number_format($weekData['total_hours_numeric'], 2) }}
                                                            </td>
                                                            <td class="text-right text-accent">
                                                                {{ number_format($weekData['total_break_hours_numeric'], 2) }}
                                                            </td>
                                                            <td class="text-right text-error">
                                                                {{ number_format($weekData['total_sick_time_numeric'], 2) }}
                                                            </td>
                                                            <td class="text-right text-info">
                                                                {{ number_format($weekData['total_vacation_time_numeric'], 2) }}
                                                            </td>
                                                            <td class="text-right text-success font-bold">
                                                                {{ number_format($weekData['total_billable_hours_numeric'], 2) }}
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <style>
        @media print {

            header,
            nav,
            footer,
            .btn,
            .divider {
                display: none !important;
            }

            body {
                background-color: white !important;
                font-size: 12px !important;
            }

            .shadow-sm,
            .shadow-md {
                box-shadow: none !important;
            }

            .py-6 {
                padding-top: 0.5rem !important;
                padding-bottom: 0.5rem !important;
            }

            .card {
                border: 1px solid #e5e7eb !important;
                margin-bottom: 1rem !important;
            }

            .card-body {
                padding: 1rem !important;
            }

            .page-break-after {
                page-break-after: always;
            }

            .grid {
                display: grid !important;
            }

            .badge {
                border: 1px solid #333 !important;
                color: #333 !important;
                background-color: white !important;
            }

            .badge-primary,
            .badge-secondary,
            .badge-accent,
            .badge-info,
            .badge-success {
                border-width: 2px !important;
            }

            .border-l-4 {
                border-left-width: 8px !important;
            }

            table {
                border-collapse: collapse !important;
            }

            table th,
            table td {
                border: 1px solid #ddd !important;
                padding: 4px 8px !important;
            }

            table th {
                background-color: #f8f9fa !important;
                color: #495057 !important;
            }

            /* Add a title at the top of each page */
            @page {
                margin: 0.5cm;
            }

            /* Ensure weekly sections don't break across pages */
            .card .card {
                page-break-inside: avoid;
            }

            /* Enhanced Print Colors - Subtle but visible */

            /* Card Headers */
            .bg-gradient-to-r {
                background: linear-gradient(to right, #f8f9fa, #e9ecef) !important;
                border-bottom: 2px solid #6c757d !important;
            }

            /* Primary header */
            .from-primary\/10 {
                background: linear-gradient(to right, #e3f2fd, #f3e5f5) !important;
                border-bottom: 2px solid #1976d2 !important;
            }

            /* Secondary header */
            .from-secondary\/10 {
                background: linear-gradient(to right, #f3e5f5, #e8f5e8) !important;
                border-bottom: 2px solid #7b1fa2 !important;
            }

            /* Info header */
            .from-info\/10 {
                background: linear-gradient(to right, #e1f5fe, #f1f8e9) !important;
                border-bottom: 2px solid #0288d1 !important;
            }

            /* Stat Cards with Subtle Colors */
            .bg-secondary {
                background-color: #f3e5f5 !important;
                border: 1px solid #7b1fa2 !important;
            }
            .text-secondary-content {
                color: #4a148c !important;
            }

            .bg-accent {
                background-color: #fce4ec !important;
                border: 1px solid #c2185b !important;
            }
            .text-accent-content {
                color: #880e4f !important;
            }

            .bg-error {
                background-color: #ffebee !important;
                border: 1px solid #d32f2f !important;
            }
            .text-error-content {
                color: #b71c1c !important;
            }

            .bg-info {
                background-color: #e1f5fe !important;
                border: 1px solid #0288d1 !important;
            }
            .text-info-content {
                color: #01579b !important;
            }

            .bg-primary {
                background-color: #e3f2fd !important;
                border: 1px solid #1976d2 !important;
            }
            .text-primary-content {
                color: #0d47a1 !important;
            }

            .bg-warning {
                background-color: #fff8e1 !important;
                border: 1px solid #f57c00 !important;
            }
            .text-warning-content {
                color: #e65100 !important;
            }

            .bg-success {
                background-color: #e8f5e8 !important;
                border: 1px solid #388e3c !important;
            }
            .text-success-content {
                color: #1b5e20 !important;
            }

            .bg-base-300 {
                background-color: #f5f5f5 !important;
                border: 1px solid #9e9e9e !important;
            }

            /* Table Colors */
            .text-secondary {
                color: #7b1fa2 !important;
            }
            .text-accent {
                color: #c2185b !important;
            }
            .text-error {
                color: #d32f2f !important;
            }
            .text-info {
                color: #0288d1 !important;
            }
            .text-success {
                color: #388e3c !important;
            }
            .text-primary {
                color: #1976d2 !important;
            }

            /* Avatar rings */
            .ring {
                border: 2px solid #1976d2 !important;
            }

            /* Neutral backgrounds */
            .bg-neutral {
                background-color: #37474f !important;
                color: white !important;
            }

            .bg-base-200 {
                background-color: #f8f9fa !important;
            }

            /* Badge colors for weekends/today */
            .badge-accent {
                background-color: #fce4ec !important;
                color: #880e4f !important;
                border: 1px solid #c2185b !important;
            }

            .badge-primary {
                background-color: #e3f2fd !important;
                color: #0d47a1 !important;
                border: 1px solid #1976d2 !important;
            }
        }
    </style>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle print friendly view button
            const printFriendlyBtn = document.getElementById('print-friendly-btn');
            if (printFriendlyBtn) {
                printFriendlyBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById('print-friendly-form').submit();
                });
            }

            // Handle print current view button
            const printCurrentBtn = document.getElementById('print-current-btn');
            if (printCurrentBtn) {
                printCurrentBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.print();
                });
            }
        });
    </script>
    @endpush
</x-app-layout>
