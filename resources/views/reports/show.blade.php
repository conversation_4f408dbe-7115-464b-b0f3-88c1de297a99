<!-- resources/views/reports/show.blade.php -->
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Report: {{ $report->name }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <a href="{{ route('reports.index') }}" class="btn btn-primary mb-4">
                <i class="fa-sharp fa-arrow-left"></i>
                Back to Reports</a>
            <div class="card bg-base-100 shadow-lg p-6">
                <h3 class="text-lg font-semibold mb-4">Report Details</h3>
                <div class="mb-4">
                    <strong>Name:</strong> {{ $report->name }}
                </div>
                <div class="mb-4">
                    <strong>Generated By:</strong> {{ $report->generatedBy->name ?? 'Unknown' }}
                </div>
                <div class="mb-4">
                    <strong>Date Created:</strong> {{ $report->created_at->format('Y-m-d H:i:s') }}
                </div>
                <div id="generated_report" class="mb-4">
                    <strong>Report Data:</strong>
                    <div id="report_table_container" class="mt-4"></div>
                </div>
                <canvas id="revenueChart" class="mt-6"></canvas>
                <details class="collapse bg-base-200 my-4">
                    <summary class="collapse-title text-xl font-medium">View Raw Data</summary>
                    <div class="collapse-content">
                        <pre class="bg-gray-100 p-4 rounded">{{ json_encode($report->data, JSON_PRETTY_PRINT) }}</pre>
                    </div>
                </details>


            </div>
        </div>
    </div>

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>

const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
};

            document.addEventListener('DOMContentLoaded', function() {
                // Load the JSON data for the report
                const reportData = @json($report->data);

                if (reportData && reportData.type === 'invoices_summary') {
                    const container = document.getElementById('report_table_container');
                    const table = document.createElement('table');
                    table.className = 'table table-zebra w-full';

                    // Create table header
                    const thead = document.createElement('thead');
                    const headerRow = document.createElement('tr');
                    ['Invoice Number', 'Invoice Date', 'Customer Name', 'Total Price', 'Total Tax', 'Total Discount']
                    .forEach(header => {
                        const th = document.createElement('th');
                        th.textContent = header;
                        headerRow.appendChild(th);
                    });
                    thead.appendChild(headerRow);
                    table.appendChild(thead);

                    // Create table body
                    const tbody = document.createElement('tbody');
                    reportData.invoices.forEach(invoice => {
                        const row = document.createElement('tr');
                        ['invoice_number', 'invoice_date', 'customer_name', 'total_price', 'total_tax',
                            'total_discount'
                        ].forEach(key => {
                            const td = document.createElement('td');
                            td.textContent = invoice[key];
                            row.appendChild(td);
                        });
                        tbody.appendChild(row);
                    });
                    table.appendChild(tbody);

                    // Append table to container
                    container.appendChild(table);

                    // Add summary
                    const summary = document.createElement('div');

                    // find total unique customers
                    const uniqueCustomers = new Set();
                    reportData.invoices.forEach(invoice => {
                        uniqueCustomers.add(invoice.customer_id);
                    });


                    summary.className = 'mt-4 p-4 bg-gray-100 rounded';
                    summary.innerHTML = `
                    <strong>Summary:</strong>
                    <p>Total Invoices: ${reportData.totals.count}</p>
                    <p>Total Price: $${reportData.totals.total_price.toFixed(2)}</p>
                    <p>Total Tax: $${reportData.totals.total_tax.toFixed(2)}</p>
                    <p>Total Discount: $${reportData.totals.total_discount.toFixed(2)}</p>
                    <p>Total Unique Customers: ${uniqueCustomers.size}</p>
                `;
                    container.appendChild(summary);

                    // Prepare data for chart
                    // Prepare full range of dates between start_date and end_date
                    const startDate = new Date(reportData.start_date);
                    const endDate = new Date(reportData.end_date);

                    const allDates = [];
                    for (let dt = new Date(startDate); dt <= endDate; dt.setDate(dt.getDate() + 1)) {
                        // Format date in local timezone to avoid shifting
                        const year = dt.getFullYear();
                        const month = String(dt.getMonth() + 1).padStart(2, '0');
                        const day = String(dt.getDate()).padStart(2, '0');
                        allDates.push(`${year}-${month}-${day}`); // Format as YYYY-MM-DD
                    }

                    // Aggregate total price per date
                    const dateTotals = {};
                    reportData.invoices.forEach(invoice => {
                        dateTotals[invoice.invoice_date] = (dateTotals[invoice.invoice_date] || 0) + parseFloat(
                            invoice.total_price);
                    });

                    // Fill in missing dates with 0 revenue
                    const chartData = allDates.map(date => ({
                        date,
                        total: dateTotals[date] || 0,
                    }));

                    // Extract labels and data for the chart
                    const labels = chartData.map(entry => entry.date);
                    const data = chartData.map(entry => entry.total);

                    // Render chart
                    const ctx = document.getElementById('revenueChart').getContext('2d');
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'Total Revenue ($)',
                                data: data,
                                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                                borderColor: 'rgba(75, 192, 192, 1)',
                                borderWidth: 1,
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'top',
                                },
                                title: {
                                    display: true,
                                    text: 'Total Revenue by Day'
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });


                } else if (reportData && reportData.type === 'transactions_summary') {
    const container = document.getElementById('report_table_container');

    // Consolidate transactions by item name
    const consolidatedItems = {};
    const departmentTotals = {}; // To store total sales by department

    reportData.line_items.forEach(item => {
        const itemKey = item.item_name || 'Unknown';

        // Consolidate item data
        if (!consolidatedItems[itemKey]) {
            consolidatedItems[itemKey] = {
                item_name: itemKey,
                total_sales: 0,
                total_quantity: 0,
                total_tax: 0,
                total_discount: 0,
            };
        }
        consolidatedItems[itemKey].total_sales += parseFloat(item.subtotal);
        consolidatedItems[itemKey].total_quantity += parseInt(item.quantity, 10);
        consolidatedItems[itemKey].total_tax += parseFloat(item.tax);
        consolidatedItems[itemKey].total_discount += item.discount ? parseFloat(item.discount) : 0;

        // Consolidate department totals
        const department = item.department_name || 'N/A';
        departmentTotals[department] = 
            (departmentTotals[department] || 0) + parseFloat(item.subtotal);
    });

    // Convert consolidated items to an array for iteration
    const consolidatedData = Object.values(consolidatedItems);

    // Create table
    const table = document.createElement('table');
    table.className = 'table table-zebra w-full';

    // Table header
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    ['Item Name', 'Total Sales', 'Total Quantity', 'Total Tax', 'Total Discount'].forEach(header => {
        const th = document.createElement('th');
        th.textContent = header;
        headerRow.appendChild(th);
    });
    thead.appendChild(headerRow);
    table.appendChild(thead);

    // Table body
    const tbody = document.createElement('tbody');
    consolidatedData.forEach(item => {
        const row = document.createElement('tr');

        // Item Name
        const tdName = document.createElement('td');
        tdName.textContent = item.item_name;
        row.appendChild(tdName);

        // Total Sales
        const tdSales = document.createElement('td');
        tdSales.textContent = `$${item.total_sales.toFixed(2)}`;
        row.appendChild(tdSales);

        // Total Quantity
        const tdQuantity = document.createElement('td');
        tdQuantity.textContent = item.total_quantity;
        row.appendChild(tdQuantity);

        // Total Tax
        const tdTax = document.createElement('td');
        tdTax.textContent = `$${item.total_tax.toFixed(2)}`;
        row.appendChild(tdTax);

        // Total Discount
        const tdDiscount = document.createElement('td');
        tdDiscount.textContent = `$${item.total_discount.toFixed(2)}`;
        row.appendChild(tdDiscount);

        tbody.appendChild(row);
    });
    table.appendChild(tbody);
    container.appendChild(table);

    // Summary
    const summary = document.createElement('div');
    summary.className = 'mt-4 p-4 bg-gray-100 rounded';

    // Department totals
    const departmentSummary = Object.entries(departmentTotals)
        .map(([dept, total]) => `<p>${dept}: $${total.toFixed(2)}</p>`)
        .join('');

    summary.innerHTML = `
        <strong>Summary:</strong>
        <p>Total Line Items: ${reportData.totals.count}</p>
        <p>Total Revenue: $${reportData.totals.total_revenue.toFixed(2)}</p>
        
        <p>Total Discount: $${reportData.totals.total_discount.toFixed(2)}</p>
        <p>Total Tax: $${reportData.totals.total_tax.toFixed(2)}</p>
        <p><strong>Gross Profit: </strong>$${reportData.totals.gross_profit.toFixed(2)}</p>


        <p>Date Range: ${formatDate(reportData.start_date)} through ${formatDate(reportData.end_date)}</p>
        <p><strong>Sales by Department:</strong></p>
        ${departmentSummary}
    `;
    container.appendChild(summary);

    // Bar Chart for Total Sales by Item
    const ctx = document.getElementById('revenueChart').getContext('2d');
    const barLabels = consolidatedData.map(item => item.item_name);
    const barData = consolidatedData.map(item => item.total_sales);

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: barLabels,
            datasets: [{
                label: 'Total Sales ($)',
                data: barData,
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: 'Total Sales by Item'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}


                else {
                    document.getElementById('report_table_container').textContent = 'No valid report data found.';
                }
            });
        </script>
    @endpush
</x-app-layout>
