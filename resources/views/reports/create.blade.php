<!-- resources/views/reports/generate.blade.php -->
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Generate Report
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-lg p-6">
                <h3 class="text-lg font-semibold mb-4">Generate Report</h3>
                <form action="{{ route('reports.store') }}" method="POST">
                    @csrf
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-group mb-4">
                            <label for="report_type" class="block text-sm font-medium text-gray-700">Report
                                Type*</label>
                            <select name="report_type" id="report_type" class="select w-full select-bordered" required>
                                <option value="invoices_summary">Invoices Summary</option>
                                <option value="transactions_summary">Transactions Summary</option>
                            </select>
                        </div>

                        <div class="form-group mb-4">
                            <label for="name" class="block text-sm font-medium text-gray-700">Name*</label>
                            <input type="text" name="name" id="name" class="input w-full input-bordered"
                                required>
                        </div>
                        <div class="form-group mb-4">
                            <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                            <input type="date" name="start_date" id="start_date" class="input w-full input-bordered"
                                required>
                                <button type="button" class="btn bg-gray-300 hover:bg-gray-400 btn-xs" id="range_today">Today</button>
                                <button type="button" class="btn bg-gray-300 hover:bg-gray-400 btn-xs" id="range_yesterday">Yesterday</button>
                                <button type="button" class="btn bg-gray-300 hover:bg-gray-400 btn-xs" id="range_this_week">This Week</button>
                                <button type="button" class="btn bg-gray-300 hover:bg-gray-400 btn-xs" id="range_last_week">Last Week</button>
                                <button type="button" class="btn bg-gray-300 hover:bg-gray-400 btn-xs" id="range_this_month">This Month</button>
                                <button type="button" class="btn bg-gray-300 hover:bg-gray-400 btn-xs" id="range_last_month">Last Month</button>
                                <button type="button" class="btn bg-gray-300 hover:bg-gray-400 btn-xs" id="range_last_30_days">Last 30 Days</button>
                        </div>
                        <div class="form-group mb-4">
                            <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                            <input type="date" name="end_date" id="end_date" class="input w-full input-bordered"
                                required>
                        </div>
                        <div class="form-group mb-4 hidden" id="departmentSelect">
                            <label for="department" class="block text-sm font-medium text-gray-700">Department</label>
                            <select name="department" id="department" class="select w-full select-bordered">
                                <option value="">All Departments</option>
                                @foreach ($departments as $department)
                                    <option value="{{ $department->id }}">{{ $department->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Generate Report</button>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const reportType = document.getElementById('report_type');
                const departmentSelect = document.getElementById('departmentSelect');

                const name = document.getElementById('name');
                
                // Helper function to format date in local timezone
                function formatLocalDate(date) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                }
                
                // Format datetime in local timezone
                const now = new Date();
                const formatted_datetime_now = now.getFullYear() + '-' + 
                    String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                    String(now.getDate()).padStart(2, '0') + ' ' + 
                    String(now.getHours()).padStart(2, '0') + ':' + 
                    String(now.getMinutes()).padStart(2, '0') + ':' + 
                    String(now.getSeconds()).padStart(2, '0');
                document.getElementById('report_type').addEventListener('change', function(e) {
                    autoFillName();
                    if (reportType.value === 'invoices_summary') {
                        departmentSelect.classList.add('hidden');
                    }
                    if (reportType.value === 'transactions_summary') {
                        departmentSelect.classList.remove('hidden');
                    }
                });

                autoFillName = () => {

                    if (reportType.value === 'invoices_summary') {
                        name.value = 'Invoices Summary Report Generated at ' + formatted_datetime_now;
                    }
                    if (reportType.value === 'transactions_summary') {
                        name.value = 'Transactions Summary Report Generated at ' + formatted_datetime_now;
                    }

                }

                autoFillName();


                // Range Buttons
                document.getElementById('range_today').addEventListener('click', function(e) {
                    const today = new Date();
                    document.getElementById('start_date').value = formatLocalDate(today);
                    document.getElementById('end_date').value = formatLocalDate(today);
                });

                document.getElementById('range_yesterday').addEventListener('click', function(e) {
                    const yesterday = new Date(new Date().setDate(new Date().getDate() - 1));
                    document.getElementById('start_date').value = formatLocalDate(yesterday);
                    document.getElementById('end_date').value = formatLocalDate(yesterday);
                });

                document.getElementById('range_this_week').addEventListener('click', function(e) {
                    const today = new Date();
                    const firstDay = new Date(today.setDate(today.getDate() - today.getDay()));
                    const lastDay = new Date(today.setDate(today.getDate() - today.getDay() + 6));
                    document.getElementById('start_date').value = formatLocalDate(firstDay);
                    document.getElementById('end_date').value = formatLocalDate(lastDay);
                });

                document.getElementById('range_last_week').addEventListener('click', function(e) {
                    const today = new Date();
                    const firstDay = new Date(today.setDate(today.getDate() - today.getDay() - 7));
                    const lastDay = new Date(today.setDate(today.getDate() - today.getDay() + 6));
                    document.getElementById('start_date').value = formatLocalDate(firstDay);
                    document.getElementById('end_date').value = formatLocalDate(lastDay);
                });

                document.getElementById('range_this_month').addEventListener('click', function(e) {
                    const today = new Date();
                    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
                    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                    document.getElementById('start_date').value = formatLocalDate(firstDay);
                    document.getElementById('end_date').value = formatLocalDate(lastDay);
                });

                document.getElementById('range_last_month').addEventListener('click', function(e) {
                    const today = new Date();
                    const firstDay = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                    const lastDay = new Date(today.getFullYear(), today.getMonth(), 0);
                    document.getElementById('start_date').value = formatLocalDate(firstDay);
                    document.getElementById('end_date').value = formatLocalDate(lastDay);
                });

                document.getElementById('range_last_30_days').addEventListener('click', function(e) {
                    const today = new Date();
                    const lastDay = new Date(today);
                    const firstDay = new Date(today.setDate(today.getDate() - 30));
                    document.getElementById('start_date').value = formatLocalDate(firstDay);
                    document.getElementById('end_date').value = formatLocalDate(lastDay);
                });

            });
        </script>
    @endpush
</x-app-layout>
