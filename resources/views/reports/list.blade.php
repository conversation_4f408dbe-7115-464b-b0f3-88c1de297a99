<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Reports
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <a href="{{ route('reports.index') }}" class="btn btn-primary mb-4"><i class="fa-sharp fa-arrow-left"></i> Dashboard</a>
            <a href="{{ route('reports.create') }}" class="btn btn-secondary mb-4">
                <i class="fa-sharp fa-plus"></i>
                Generate Report</a>
            <div class="card bg-base-100 shadow-lg p-6">
                <h3 class="text-lg font-semibold mb-4">Reports</h3>
                <div class="overflow-x-auto">
                    <table class="table table-zebra w-full">
                        <thead>
                            <tr>
                                @php
                                    $columns = [
                                        'id' => 'ID',
                                        'name' => 'Name',
                                        'type' => 'Type',
                                        'generated_by' => 'Generated By',
                                        'created_at' => 'Date Created',
                                    ];
                                @endphp
                                @foreach ($columns as $key => $label)
                                    <th>
                                        <a href="{{ route('reports.list', array_merge(request()->except(['page', 'sort', 'order']), [
                                            'sort' => $key,
                                            'order' => $sort === $key && $order === 'asc' ? 'desc' : 'asc',
                                            'pagination' => $pagination, // Preserve the current pagination value
                                        ])) }}" class="flex items-center">
                                            {{ $label }}
                                            @if ($sort === $key)
                                                <i class="fa-sharp {{ $order === 'asc' ? 'fa-sort-up' : 'fa-sort-down' }} ml-2"></i>
                                            @else
                                                <i class="fa-sharp fa-sort ml-2"></i>
                                            @endif
                                        </a>
                                    </th>
                                @endforeach
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($reports as $report)
                                <tr>
                                    <td>{{ $report->id }}</td>
                                    <td>
                                        <a href="{{ route('reports.show', $report->id) }}">{{ $report->name }}</a>
                                    </td>
                                    <td>{{ $report->type }}</td>
                                    <td>{{ $report->generatedBy->name ?? 'Unknown' }}</td>
                                    <td>{{ $report->created_at->format('Y-m-d H:i:s') }}</td>
                                    <td>
                                        <a href="{{ route('reports.show', $report->id) }}" class="btn btn-sm btn-primary">
                                            <i class="fa-sharp fa-eye"></i> View
                                        </a>
                                        <form action="{{ route('reports.destroy', $report->id) }}" method="POST" class="inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-error"
                                                onclick="return confirm('Are you sure you want to delete this report?')">
                                                <i class="fa-sharp fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center">No reports found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                
                <x-pagination :paginator="$reports" :pagination="$pagination" />
                
            </div>
        </div>
    </div>
</x-app-layout>
