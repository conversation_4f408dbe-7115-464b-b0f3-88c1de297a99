<div class="mb-4">
    <h4 class="font-medium mb-2">{{ $title ?? 'Upload Files' }}</h4>
    <p class="text-sm text-gray-600 mb-3">{{ $description ?? 'Upload PDF documents or images related to this record.' }}</p>

    <form action="{{ $action }}" method="POST" enctype="multipart/form-data" class="space-y-3" id="fileUploadForm">
        @csrf

        <div class="flex flex-col md:flex-row gap-3">
            <div class="form-control flex-1">
                <input type="file" name="file" id="file" class="file-input file-input-bordered file-input-sm w-full" accept="{{ $accept ?? '.pdf,.jpg,.jpeg,.png,.gif' }}" required />
                <div class="text-xs text-gray-500 mt-1">
                    Max: 10MB. Types: {{ $acceptText ?? 'PDF, JPG, PNG, GIF' }}
                </div>
            </div>

            <div class="form-control flex-1">
                <input type="text" name="description" class="input input-bordered input-sm w-full" placeholder="Description (optional)" />
            </div>

            <div class="form-control">
                <button type="submit" class="btn btn-primary btn-sm">
                    <i class="fa-sharp fa-upload mr-1"></i> Upload
                </button>
            </div>
        </div>

        @if(isset($showCocOption) && $showCocOption)
            @php
                $cocFileId = isset($certificate) ? ($certificate->stats['coc_file_id'] ?? null) : null;
                $cocFileExists = $cocFileId && isset($certificate) && $certificate->files->firstWhere('id', $cocFileId);
            @endphp

            @if(!$cocFileExists)
            <div class="form-control">
                <label class="label cursor-pointer justify-start gap-2 py-0">
                    <input type="checkbox" name="is_coc" class="checkbox checkbox-sm checkbox-primary" value="1" />
                    <span class="label-text text-xs font-medium">Mark as Chain of Custody document</span>
                </label>
                <div class="text-xs text-gray-500 ml-6">
                    This document will be used as the official Chain of Custody record and will be automatically included in the generated PDF certificate. Only one file can be marked as COC.
                </div>
            </div>
            @else
            <div class="text-xs text-gray-500 italic">
                <i class="fa-sharp fa-circle-info mr-1"></i>
                A Chain of Custody document has already been uploaded. Delete the existing document to mark a new one.
            </div>
            @endif
        @endif
    </form>
</div>
