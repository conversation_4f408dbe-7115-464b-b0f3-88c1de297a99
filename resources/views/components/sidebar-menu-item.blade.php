@props(['route', 'title', 'icon'])

@php
  $isActive = request()->routeIs($route);
@endphp

<li class="relative">
  <div class="tooltip tooltip-right" data-tip="{{ $title }}">
    <a href="{{ route($route) }}" class="menu-item px-4 py-2 rounded flex items-center space-x-2
      {{ $isActive ? 'bg-primary text-neutral-content' : 'hover:bg-secondary text-secondary-content' }}">
      <i class="{{ $icon }} text-neutral-content flex-shrink-0"></i>
      <span class="menu-text text-neutral-content">{{ $title }}</span>
    </a>
  </div>
</li>
