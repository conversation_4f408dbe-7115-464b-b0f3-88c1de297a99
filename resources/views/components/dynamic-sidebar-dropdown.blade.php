@props(['title', 'icon', 'items'])

@php
    $dropdownId = Str::uuid(); // Unique ID for the dropdown
    $flyoutId = 'flyout-' . $dropdownId; // Unique ID for the flyout
@endphp

<li class="relative dropdown-item">
  <!-- Dropdown Toggle -->
  <button
    class="dropdown-toggle w-full px-4 py-2 text-left flex items-center justify-between hover:bg-secondary text-neutral-content cursor-pointer rounded focus:outline-none focus:bg-gray-700"
    onclick="
      const sidebar = document.getElementById('sidebar');
      const isCollapsed = sidebar.getAttribute('data-collapsed') === 'true';

      if (!isCollapsed) {
        // Normal dropdown behavior
        document.getElementById('{{ $dropdownId }}').classList.toggle('hidden');
        const chevron = this.querySelector('.dropdown-chevron');
        chevron.classList.toggle('fa-chevron-down');
        chevron.classList.toggle('fa-chevron-up');
      } else {
        // Collapsed sidebar behavior - navigate to first menu item
        @if(count($items) > 0)
          window.location.href = '{{ route($items[0]['route']) }}';
        @endif
      }
    ">
    <div class="flex items-center space-x-2">
      <i class="{{ $icon }} flex-shrink-0"></i>
      <span class="menu-text text-primary-content">{{ $title }}</span>
    </div>
    <i class="fa-sharp fa-solid fa-chevron-down dropdown-chevron"></i>
    <!-- Flyout chevron for collapsed state -->
    <i class="fa-sharp fa-solid fa-chevron-right flyout-chevron hidden"></i>
  </button>

  <!-- Normal Dropdown Content -->
  <ul id="{{ $dropdownId }}" class="hidden pl-4 space-y-2 bg-neutral rounded-md mt-1 py-2">
    @foreach($items as $item)
      <x-dynamic-sidebar-menu-item
        :route="$item['route']"
        :title="$item['title']"
        :icon="$item['icon']"
      />
    @endforeach
  </ul>

  <!-- Flyout Menu for Collapsed State -->
  <div class="flyout-menu !bg-primary border border-base-300 rounded-lg shadow-lg" id="{{ $flyoutId }}">
    <div class="p-2">
      <h3 class="text-primary-content font-semibold mb-2 px-2 border-b border-base-300 pb-2">{{ $title }}</h3>
      <ul class="space-y-1">
        @foreach($items as $item)
          <x-dynamic-sidebar-menu-item
            :route="$item['route']"
            :title="$item['title']"
            :icon="$item['icon']"
          />
        @endforeach
      </ul>
    </div>
  </div>
</li>
