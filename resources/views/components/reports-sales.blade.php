<h3 class="text-xl font-semibold mb-4">
    MTD Sales Totals By {{ $filterType === 'category' ? 'Category' : 'Customer Type' }}
</h3>

@php
    // Calculate total sales from the existing $salesData
    $totalSales = collect($salesData)->sum(fn($data) => $data['netRevenue'] ?? 0);
    $monthlyGoal = 20000;
    $progressPercentage = min(100, round(($totalSales / $monthlyGoal) * 100));
@endphp

<!-- MTD Total Sales -->

<div class="mb-6">
    <p class="text-lg font-semibold">Total Sales MTD: ${{ number_format($totalSales, 2) }}</p>
    <div class="flex items-center gap-4">
        <progress class="progress progress-accent w-56" value="{{ $progressPercentage }}" max="100"></progress>
        <span>{{ $progressPercentage }}% of $20,000 goal</span>
    </div>
</div>

<!-- Filter Selector -->
<div class="mb-6">
    <label for="filterType" class="block text-sm font-medium text-base-content">Select Filter</label>
    <select id="filterType" class="select select-bordered w-full" onchange="updateReport(this.value)">
        <option value="category" {{ $filterType === 'category' ? 'selected' : '' }}>Category</option>
        <option value="customerType" {{ $filterType === 'customerType' ? 'selected' : '' }}>Customer Type</option>
    </select>
</div>

    @if (collect($salesData)->isEmpty())
        <p class="text-base-content/60">No sales data available for the selected filter.</p>
    @else
        <!-- Sales Table -->
        <table class="table w-full table-zebra">
            <thead>
                <tr>
                    <th id="headerType">{{ $filterType === 'category' ? 'Category' : 'Customer Type' }}</th>
                    <th>Net Revenue</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($salesData as $key => $data)
                    <tr>
                        <td>{{ $key }}</td>
                        <td>${{ number_format($data['netRevenue'] ?? 0, 2) }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>

        <!-- Sales Chart -->
        <div class="mt-6">
            <canvas id="salesChart"></canvas>
        </div>
    @endif

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    let salesChart;

    function updateReport(filter) {
        fetch(`/reports/sales-data?filterType=${filter}`)
            .then(response => response.json())
            .then(data => {
                const headerType = filter === 'category' ? 'Category' : 'Customer Type';
                document.querySelector('h3').textContent = `MTD Sales Totals By ${headerType}`;
                document.getElementById('headerType').textContent = headerType;
                renderTable(data.salesData, filter);
                renderChart(data.salesData, filter);

                // Update progress bar
                const totalSales = Object.values(data.salesData).reduce((sum, item) => sum + (parseFloat(item.netRevenue) || 0), 0);
                const progressPercentage = Math.min(100, Math.round((totalSales / 20000) * 100));
                document.querySelector('.progress').value = progressPercentage;
                document.querySelector('.progress + span').textContent = `${progressPercentage}% of $20,000 goal`;
                document.querySelector('.mb-6 p').textContent = `Total Sales MTD: $${totalSales.toFixed(2)}`;
            })
            .catch(error => console.error("Error fetching sales data:", error));
    }

    function renderTable(data, filter) {
        const tableBody = document.querySelector('tbody');
        tableBody.innerHTML = '';

        Object.entries(data).forEach(([key, value]) => {
            const row = `
                <tr>
                    <td>${key}</td>
                    <td>$${parseFloat(value.netRevenue ?? 0).toFixed(2)}</td>
                </tr>`;
            tableBody.insertAdjacentHTML('beforeend', row);
        });
    }

    function renderChart(data, filter) {
        const ctx = document.getElementById('salesChart').getContext('2d');
        const labels = Object.keys(data);
        const chartData = Object.values(data).map(item => parseFloat(item.netRevenue ?? 0));

        if (salesChart) {
            salesChart.destroy();
        }

        salesChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels,
                datasets: [{
                    label: 'Net Revenue',
                    data: chartData,
                    backgroundColor: 'hsl(var(--p) / 0.2)',
                    borderColor: 'hsl(var(--p))',
                    borderWidth: 1,
                }]
            },
            options: {
                indexAxis: 'y',
                scales: {
                    x: { beginAtZero: true },
                },
            },
        });
    }

    document.addEventListener('DOMContentLoaded', function () {
        renderChart(@json($salesData), '{{ $filterType }}');
    });
</script>
@endpush
