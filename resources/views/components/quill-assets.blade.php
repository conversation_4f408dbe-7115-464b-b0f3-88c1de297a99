{{-- Quill Editor Assets Component --}}
@once
@push('styles')
    <!-- Include Quill Library CSS -->
    <link href="https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.snow.css" rel="stylesheet" />
    <style>
        .ql-toolbar.ql-snow {
            background-color: var(--color-base-100);
            border: 1px solid var(--color-base-300);
        }

        .ql-snow .ql-stroke {
            stroke: var(--color-base-content) !important;
        }

        .ql-snow .ql-fill {
            fill: var(--color-base-content) !important;
        }

        .ql-editor{
            background: var(--color-base-100) !important;
            color: var(--color-base-content) !important;
            min-height: 80px !important;
            max-height: 80px !important;
            overflow-y: auto !important;
        }

        #editor {
            border: 1px solid var(--color-base-300) !important;
        }

        #editor .ql-container {
            height: calc(100% - 42px) !important; /* Subtract toolbar height */
        }

        #editor .ql-toolbar {
            border-bottom: 1px solid var(--color-base-300) !important;
        }

        .ql-snow .ql-picker.ql-expanded .ql-picker-label {
            color: var(--color-base-content) !important;
        }

        .ql-snow .ql-picker{
            color: var(--color-base-content) !important;
            background-color: var(--color-base-300) !important;
        }

        .ql-picker-options{
            background-color: var(--color-base-200) !important;
            border-color: var(--color-base-300) !important;
            color: var(--color-base-content) !important;
        }



    </style>
@endpush

@push('scripts')
    <!-- Include Quill Library JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.js"></script>
@endpush
@endonce
