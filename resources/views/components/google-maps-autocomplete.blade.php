@props([
    'id' => 'address',
    'name' => 'address',
    'value' => '',
    'placeholder' => 'Enter address...',
    'required' => false,
    'class' => '',
    'rows' => 3,
    'type' => 'textarea', // 'input' or 'textarea'
    'label' => 'Address',
    'icon' => 'fa-map-marker-alt',
    'iconColor' => 'text-primary',
    'restrictions' => null, // Country restrictions, e.g., ['us', 'ca']
    'showDisplayMode' => true, // Whether to show address display with change button when value exists
    'displayLabel' => 'Current Address', // Label for the address display
    'changeButtonText' => 'Change', // Text for the change button
    'autoSave' => false, // Whether this field should auto-save
    'autoSaveField' => null, // Field name for auto-save (defaults to $name)
])

@php
    $inputClass = $type === 'textarea'
        ? 'textarea textarea-bordered w-full ' . $class
        : 'input input-bordered w-full ' . $class;

    $uniqueId = $id . '_' . uniqid();
    $currentValue = old($name, $value);
    $autoSaveFieldName = $autoSaveField ?? $name;
@endphp

<div id="{{ $uniqueId }}_container">


    <!-- Address Container -->
    <div id="{{ $uniqueId }}_address_container" class="relative">
        @if($showDisplayMode && $currentValue)
            <!-- Address Display Mode -->
            <div id="{{ $uniqueId }}_display" class="card bg-base-200 border border-base-300 p-4">
                <div class="flex items-start justify-between gap-3">
                    <div class="flex-1">
                        <div class="flex items-center gap-2 mb-2">
                            <i class="fa-sharp {{ $icon }} {{ $iconColor }}"></i>
                            <span class="text-sm font-medium text-base-content/70">{{ $displayLabel }}</span>
                        </div>
                        <p id="{{ $uniqueId }}_display_text" class="text-base-content whitespace-pre-line">{{ $currentValue }}</p>
                    </div>
                    <button type="button" id="{{ $uniqueId }}_change_btn" class="btn btn-outline btn-sm gap-2">
                        <i class="fa-sharp fa-edit"></i>
                        {{ $changeButtonText }}
                    </button>
                </div>
            </div>

            <!-- Hidden input for form submission -->
            <input type="hidden" name="{{ $name }}" id="{{ $uniqueId }}" value="{{ $currentValue }}"
                   @if($autoSave) class="auto-save" data-field="{{ $autoSaveFieldName }}" @endif
                   @if($required) required @endif />
        @else
            <!-- Address Input Mode -->
            <div id="{{ $uniqueId }}_input_container" class="relative">
                @if($type === 'textarea')
                    <textarea
                        name="{{ $name }}"
                        id="{{ $uniqueId }}"
                        rows="{{ $rows }}"
                        class="{{ $inputClass }}@if($autoSave) auto-save @endif"
                        @if($autoSave) data-field="{{ $autoSaveFieldName }}" @endif
                        placeholder="{{ $placeholder }}"
                        @if($required) required @endif
                    >{{ $currentValue }}</textarea>
                @else
                    <label class="input input-bordered w-full {{ $class }}@if($autoSave) auto-save @endif flex items-center gap-2">
                        <i class="fa-sharp fa-location-crosshairs text-primary"></i>
                        <input
                            type="text"
                            name="{{ $name }}"
                            id="{{ $uniqueId }}"
                            class="grow"
                            @if($autoSave) data-field="{{ $autoSaveFieldName }}" @endif
                            value="{{ $currentValue }}"
                            placeholder="{{ $placeholder }}"
                            @if($required) required @endif
                        />
                        <!-- Loading indicator -->
                        <span id="{{ $uniqueId }}_loading" class="loading loading-spinner loading-sm hidden"></span>
                    </label>
                @endif
            </div>
        @endif
    </div>

    @error($name)
    <div class="label">
        <span class="label-text-alt text-error flex items-center gap-1">
            <i class="fa-sharp fa-exclamation-triangle"></i>
            {{ $message }}
        </span>
    </div>
    @enderror
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const container = document.getElementById('{{ $uniqueId }}_container');
    const addressContainer = document.getElementById('{{ $uniqueId }}_address_container');
    let displayElement = document.getElementById('{{ $uniqueId }}_display');
    let displayText = document.getElementById('{{ $uniqueId }}_display_text');
    let changeBtn = document.getElementById('{{ $uniqueId }}_change_btn');
    let inputContainer = document.getElementById('{{ $uniqueId }}_input_container');
    let input = document.getElementById('{{ $uniqueId }}');
    let loadingIndicator = document.getElementById('{{ $uniqueId }}_loading');
    let autocompleteWrapper = null;

    if (!input) return;

    // Store session token for billing optimization
    let sessionToken = null;

    // Function to convert to display mode
    function convertToDisplayMode(address) {
        if (!address || address.trim() === '') return;

        // Always check for existing display elements in the DOM to prevent duplicates
        let existingDisplay = document.getElementById('{{ $uniqueId }}_display');
        let existingDisplayText = document.getElementById('{{ $uniqueId }}_display_text');

        // Remove any duplicate display elements that might exist
        const allDisplayElements = addressContainer.querySelectorAll('[id="{{ $uniqueId }}_display"]');
        if (allDisplayElements.length > 1) {
            // Keep only the first one, remove the rest
            for (let i = 1; i < allDisplayElements.length; i++) {
                allDisplayElements[i].remove();
            }
            // Update references to the remaining element
            existingDisplay = allDisplayElements[0];
            existingDisplayText = existingDisplay.querySelector('#{{ $uniqueId }}_display_text');
        }

        // Update or create display element
        if (existingDisplay && existingDisplayText) {
            // Update existing display element
            existingDisplayText.textContent = address;
            existingDisplay.style.display = '';

            // Update global references
            displayElement = existingDisplay;
            displayText = existingDisplayText;
        } else {
            // Create display element if it doesn't exist
            const newDisplay = document.createElement('div');
            newDisplay.id = '{{ $uniqueId }}_display';
            newDisplay.className = 'card bg-base-200/50 border border-base-300 p-4';
            newDisplay.innerHTML = `
                <div class="flex items-start justify-between gap-3">
                    <div class="flex-1">
                        <div class="flex items-center gap-2 mb-2">
                            <i class="fa-sharp {{ $icon }} {{ $iconColor }}"></i>
                            <span class="text-sm font-medium text-base-content/70">{{ $displayLabel }}</span>
                        </div>
                        <p id="{{ $uniqueId }}_display_text" class="text-base-content whitespace-pre-line">${address}</p>
                    </div>
                    <button type="button" id="{{ $uniqueId }}_change_btn" class="btn btn-outline btn-sm gap-2">
                        <i class="fa-sharp fa-edit"></i>
                        {{ $changeButtonText }}
                    </button>
                </div>
            `;
            addressContainer.appendChild(newDisplay);

            // Update global references
            displayElement = newDisplay;
            displayText = newDisplay.querySelector('#{{ $uniqueId }}_display_text');
            changeBtn = newDisplay.querySelector('#{{ $uniqueId }}_change_btn');

            // Re-attach change button listener
            attachChangeButtonListener();
        }

        // Hide input container
        if (inputContainer) {
            inputContainer.style.display = 'none';
        }

        // Hide the autocomplete wrapper if it exists
        if (autocompleteWrapper) {
            autocompleteWrapper.style.display = 'none';
        }

        // Also hide any other autocomplete wrappers that might exist
        const autocompleteWrappers = addressContainer.querySelectorAll('.autocomplete-wrapper');
        autocompleteWrappers.forEach(wrapper => {
            wrapper.style.display = 'none';
        });

        // Force hide any gmp-place-autocomplete elements directly
        const autocompleteElements = addressContainer.querySelectorAll('gmp-place-autocomplete');
        autocompleteElements.forEach(element => {
            element.style.display = 'none';
        });

        // Update input to hidden type and set value
        if (input) {
            input.type = 'hidden';
            input.value = address;

            // Only trigger change events if not using auto-save (parent will handle auto-save)
            @if(!$autoSave)
                input.dispatchEvent(new Event('change', { bubbles: true }));
                input.dispatchEvent(new Event('input', { bubbles: true }));
            @endif
        }
    }

    // Function to convert to input mode
    function convertToInputMode() {
        // Hide all display elements (in case there are duplicates)
        const allDisplayElements = addressContainer.querySelectorAll('[id="{{ $uniqueId }}_display"]');
        allDisplayElements.forEach(element => {
            element.style.display = 'none';
        });

        // Update global reference to the first display element (if any)
        if (allDisplayElements.length > 0) {
            displayElement = allDisplayElements[0];
        }

        // Clean up existing event listeners if input exists
        if (input && input.clickOutsideHandler) {
            document.removeEventListener('click', input.clickOutsideHandler);
        }
        if (input && input.repositionHandler) {
            window.removeEventListener('resize', input.repositionHandler);
            window.removeEventListener('scroll', input.repositionHandler, true);
        }

        // Remove any existing autocomplete wrappers to start fresh
        const existingWrappers = addressContainer.querySelectorAll('.autocomplete-wrapper');
        existingWrappers.forEach(wrapper => {
            wrapper.remove();
        });

        // Clear the autocomplete wrapper reference
        autocompleteWrapper = null;

        // Remove old input container if it exists
        if (inputContainer) {
            inputContainer.remove();
        }

        // Create a fresh input container
        const newInputContainer = document.createElement('div');
        newInputContainer.id = '{{ $uniqueId }}_input_container';
        newInputContainer.className = 'relative';

        const currentValue = input ? input.value : '';

        // Create the new input element directly
        @if($type === 'textarea')
            let newInput = document.createElement('textarea');
            newInput.rows = {{ $rows }};
            newInput.textContent = currentValue;
            newInput.name = '{{ $name }}';
            newInput.id = '{{ $uniqueId }}';
            newInput.className = '{{ $inputClass }}@if($autoSave) auto-save @endif';
            newInput.placeholder = '{{ $placeholder }}';
            @if($autoSave)
            newInput.setAttribute('data-field', '{{ $autoSaveFieldName }}');
            @endif
            @if($required)
            newInput.required = true;
            @endif

            // Create loading indicator for textarea
            const loadingDiv = document.createElement('div');
            loadingDiv.id = '{{ $uniqueId }}_loading';
            loadingDiv.className = 'absolute right-2 top-2 hidden z-10 bg-base-100/90 backdrop-blur-sm rounded-lg p-2 shadow-lg border border-base-300/50';
            loadingDiv.innerHTML = '<div class="flex items-center gap-2"><span class="loading loading-spinner loading-sm text-primary"></span><span class="text-xs text-base-content/70">Searching...</span></div>';

            // Add elements to container
            newInputContainer.appendChild(newInput);
            newInputContainer.appendChild(loadingDiv);
        @else
            // Create DaisyUI label wrapper for text input
            const labelWrapper = document.createElement('label');
            labelWrapper.className = 'input input-bordered w-full {{ $class }}@if($autoSave) auto-save @endif flex items-center gap-2';

            // Create location icon
            const locationIcon = document.createElement('i');
            locationIcon.className = 'fa-sharp fa-location-crosshairs text-primary';

            // Create the input element
            let newInput = document.createElement('input');
            newInput.type = 'text';
            newInput.value = currentValue;
            newInput.name = '{{ $name }}';
            newInput.id = '{{ $uniqueId }}';
            newInput.className = 'grow';
            newInput.placeholder = '{{ $placeholder }}';
            @if($autoSave)
            newInput.setAttribute('data-field', '{{ $autoSaveFieldName }}');
            @endif
            @if($required)
            newInput.required = true;
            @endif

            // Create loading indicator
            const loadingSpan = document.createElement('span');
            loadingSpan.id = '{{ $uniqueId }}_loading';
            loadingSpan.className = 'loading loading-spinner loading-sm hidden text-primary';

            // Assemble the label structure
            labelWrapper.appendChild(locationIcon);
            labelWrapper.appendChild(newInput);
            labelWrapper.appendChild(loadingSpan);

            // Add label wrapper to container
            newInputContainer.appendChild(labelWrapper);
        @endif

        // Add the new input container to the address container
        addressContainer.appendChild(newInputContainer);

        // Update references
        if (input && input.parentNode) {
            input.remove();
        }
        input = newInput;
        inputContainer = newInputContainer;
        loadingIndicator = newInputContainer.querySelector('#{{ $uniqueId }}_loading');

        // Clear any autocomplete initialization flags
        newInput.removeAttribute('data-autocomplete-initialized');

        // Initialize autocomplete on the fresh input after a short delay
        setTimeout(() => {
            initializeNewAutocomplete();
        }, 100);
    }

    // Function to attach change button listener
    function attachChangeButtonListener() {
        // Remove any existing listeners from all change buttons to prevent duplicates
        const allChangeButtons = addressContainer.querySelectorAll('[id="{{ $uniqueId }}_change_btn"]');
        allChangeButtons.forEach(button => {
            // Clone the button to remove all event listeners
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            // Add the click listener to the new button
            newButton.addEventListener('click', function() {
                convertToInputMode();
            });
        });

        // Update global reference to the first change button (if any)
        if (allChangeButtons.length > 0) {
            changeBtn = addressContainer.querySelector('#{{ $uniqueId }}_change_btn');
        }
    }

    // Attach initial change button listener if it exists
    if (changeBtn) {
        attachChangeButtonListener();
    }

    async function initializeNewAutocomplete() {
        // Get the current input element (might have been recreated)
        let currentInput = document.getElementById('{{ $uniqueId }}');

        if (!currentInput || currentInput.type === 'hidden') {
            return;
        }

        // Update the global input reference to the current input
        input = currentInput;

        // Check if this input already has autocomplete initialized
        if (currentInput.dataset.autocompleteInitialized === 'true') {
            return;
        }

        try {

            // Generate initial session token
            if (!sessionToken) {
                await generateSessionToken();
            }

            // Create a dropdown container for suggestions
            const suggestionsContainer = document.createElement('div');
            suggestionsContainer.id = '{{ $uniqueId }}_suggestions';
            suggestionsContainer.className = 'google-maps-autocomplete-suggestions absolute top-full left-0 right-0 hidden';
            suggestionsContainer.style.zIndex = '99999';

            // Make sure the input container has relative positioning
            const inputContainer = currentInput.parentNode;
            if (inputContainer) {
                inputContainer.classList.add('google-maps-autocomplete-container');
                if (!inputContainer.style.position) {
                    inputContainer.style.position = 'relative';
                }
            }

            // Add CSS class to input for styling
            currentInput.classList.add('google-maps-autocomplete-input');

            // Check if there's a Quill editor on the page that might cause stacking issues
            const hasQuillEditor = document.querySelector('.ql-editor, .ql-container, .ql-toolbar');

            // Check if we're inside a modal that might cause stacking issues
            // Check for both regular modals and dialog elements
            const modalParent = currentInput.closest('.modal') ||
                               currentInput.closest('dialog') ||
                               currentInput.closest('dialog.modal');
            const hasModal = modalParent !== null;
            const isDialog = modalParent && modalParent.tagName.toLowerCase() === 'dialog';

            // Always insert suggestions container immediately after the input element
            // This ensures it appears right below the input and inherits proper stacking context
            currentInput.parentNode.insertBefore(suggestionsContainer, currentInput.nextSibling);

            // Set appropriate z-index based on context
            let zIndexValue = '99999'; // Default high value
            if (hasModal) {
                zIndexValue = isDialog ? '999999' : '100020'; // Higher for dialogs

                // Add CSS classes to identify context
                suggestionsContainer.classList.add('google-maps-autocomplete-modal-context');
                if (isDialog) {
                    suggestionsContainer.classList.add('google-maps-autocomplete-dialog-context');
                }
            }
            suggestionsContainer.style.zIndex = zIndexValue;

            // Function to position suggestions container (now always relative to input)
            function positionSuggestionsContainer() {
                // Since suggestions are now positioned immediately after the input, no manual positioning needed
                // The CSS handles the positioning with absolute/relative

                // DEBUG: Log positioning details
                console.log('DEBUG - Positioning suggestions:', {
                    hasQuillEditor: !!hasQuillEditor,
                    hasModal: hasModal,
                    position: window.getComputedStyle(suggestionsContainer).position,
                    zIndex: suggestionsContainer.style.zIndex,
                    suggestionsRect: suggestionsContainer.getBoundingClientRect(),
                    suggestionsElement: suggestionsContainer,
                    inputRect: currentInput.getBoundingClientRect(),
                    parentNode: currentInput.parentNode
                });
            }

            // Variables for managing autocomplete state
            let currentSuggestions = [];
            let selectedIndex = -1;
            let requestId = 0;
            let isSettingValueProgrammatically = false;

            console.log('initializeNewAutocomplete: Setting up input event listeners');

            // Function to construct a complete address from place details
            function constructCompleteAddress(place) {
                console.log('Constructing address for place:', place);

                // Check if this is for a textarea input (multi-line format)
                const isTextarea = currentInput.tagName.toLowerCase() === 'textarea';
                console.log('Input type:', isTextarea ? 'textarea' : 'input');

                // Always prioritize building a street address from components
                if (place.addressComponents && place.addressComponents.length > 0) {
                    const components = place.addressComponents;

                    console.log('Raw address components:', components);

                    // Extract address components
                    let streetNumber = '';
                    let route = '';
                    let establishment = '';
                    let subpremise = '';
                    let locality = '';
                    let administrativeAreaLevel1 = '';
                    let postalCode = '';
                    let country = '';

                    components.forEach((component, index) => {
                        console.log(`Component ${index}:`, component);

                        const types = component.types || [];
                        // New API uses longText and shortText instead of longName and shortName
                        const longName = component.longText || component.longName || '';
                        const shortName = component.shortText || component.shortName || '';

                        console.log(`  Types: ${types.join(', ')}, Long: "${longName}", Short: "${shortName}"`);

                        if (types.includes('street_number')) {
                            streetNumber = longName;
                        } else if (types.includes('route')) {
                            route = longName;
                        } else if (types.includes('establishment')) {
                            establishment = longName;
                        } else if (types.includes('subpremise')) {
                            subpremise = longName;
                        } else if (types.includes('locality')) {
                            locality = longName;
                        } else if (types.includes('administrative_area_level_1')) {
                            administrativeAreaLevel1 = shortName;
                        } else if (types.includes('postal_code')) {
                            postalCode = longName;
                        } else if (types.includes('country')) {
                            country = shortName;
                        }
                    });

                    console.log('Extracted components:', {
                        streetNumber, route, establishment, locality, administrativeAreaLevel1, postalCode
                    });

                    // Build address based on input type
                    if (isTextarea) {
                        // Multi-line format for textarea
                        let addressLines = [];

                        // Add establishment name if available from components
                        if (establishment) {
                            addressLines.push(establishment);
                        } else {
                            // Fallback to displayName if no establishment in components
                            let displayText = '';
                            if (place.displayName && place.displayName.text) {
                                displayText = place.displayName.text;
                            } else if (typeof place.displayName === 'string') {
                                displayText = place.displayName;
                            }

                            console.log('Display name for establishment:', displayText);

                            // Add display name if it looks like a business name (not just an address)
                            if (displayText && !displayText.match(/^\d+\s+\w+\s+(St|Street|Ave|Avenue|Rd|Road|Dr|Drive|Blvd|Boulevard)/i)) {
                                addressLines.push(displayText);
                                console.log('Added display name to address lines:', displayText);
                            }
                        }

                        // Add street address
                        if (streetNumber || route) {
                            let streetLine = '';
                            if (streetNumber && route) {
                                streetLine = streetNumber + ' ' + route;
                            } else if (route) {
                                streetLine = route;
                            }

                            // Add subpremise (suite, apt, etc.) if available
                            if (subpremise) {
                                streetLine += ' ' + subpremise;
                            }

                            if (streetLine) {
                                addressLines.push(streetLine);
                            }
                        }

                        // Add city, state, zip line
                        let cityStateLine = '';
                        if (locality) {
                            cityStateLine = locality;
                        }
                        if (administrativeAreaLevel1) {
                            cityStateLine += (cityStateLine ? ', ' : '') + administrativeAreaLevel1;
                        }
                        if (postalCode) {
                            cityStateLine += ' ' + postalCode;
                        }

                        if (cityStateLine) {
                            addressLines.push(cityStateLine);
                        }

                        const multiLineAddress = addressLines.join('\n');
                        console.log('Built multi-line address:', multiLineAddress);
                        return multiLineAddress;

                    } else {
                        // Single-line format for input (existing logic)
                        if (streetNumber || route) {
                            let streetAddress = '';

                            // Build the street address part
                            if (streetNumber && route) {
                                streetAddress = streetNumber + ' ' + route;
                            } else if (route) {
                                streetAddress = route;
                            }

                            // Add subpremise (suite, apt, etc.) if available
                            if (subpremise) {
                                streetAddress += ' ' + subpremise;
                            }

                            // Add city, state, zip
                            if (locality) {
                                streetAddress += ', ' + locality;
                            }
                            if (administrativeAreaLevel1) {
                                streetAddress += ', ' + administrativeAreaLevel1;
                            }
                            if (postalCode) {
                                streetAddress += ' ' + postalCode;
                            }

                            console.log('Built single-line street address:', streetAddress);
                            return streetAddress;
                        }

                        // If no street info but we have locality info, build what we can
                        if (locality || administrativeAreaLevel1) {
                            let partialAddress = '';

                            if (locality) {
                                partialAddress = locality;
                            }
                            if (administrativeAreaLevel1) {
                                partialAddress += (partialAddress ? ', ' : '') + administrativeAreaLevel1;
                            }
                            if (postalCode) {
                                partialAddress += ' ' + postalCode;
                            }

                            console.log('Built partial address from locality:', partialAddress);
                            return partialAddress;
                        }
                    }
                }

                // Fallback: use formatted addresses and try to construct multi-line format
                console.log('Using fallback address construction');
                console.log('Available address formats:', {
                    shortFormattedAddress: place.shortFormattedAddress,
                    formattedAddress: place.formattedAddress,
                    displayName: place.displayName
                });

                if (isTextarea && place.displayName && place.formattedAddress) {
                    // Try to construct multi-line format from available data
                    let addressLines = [];
                    let isBusinessLocation = false;

                    // Get display name text
                    let displayText = '';
                    if (place.displayName && place.displayName.text) {
                        displayText = place.displayName.text;
                    } else if (typeof place.displayName === 'string') {
                        displayText = place.displayName;
                    }

                    // Check if display name looks like a business name (not just an address)
                    if (displayText && !displayText.match(/^\d+\s+\w+\s+(St|Street|Ave|Avenue|Rd|Road|Dr|Drive|Blvd|Boulevard|Ln|Lane|Ct|Court|Cir|Circle|Way|Pl|Place)/i)) {
                        addressLines.push(displayText);
                        isBusinessLocation = true;
                    }

                    // Always add the full formatted address (includes zip and state)
                    if (place.formattedAddress) {
                        addressLines.push(place.formattedAddress);
                    }

                    // For business locations, return business name + full address
                    // For regular addresses, return only the full address
                    return isBusinessLocation ? addressLines.join('\n') : (place.formattedAddress || displayText || '');

                } else if (isTextarea) {
                    // For textarea without full data, just use formatted address
                    return place.formattedAddress || place.shortFormattedAddress || place.displayName || '';
                } else {
                    // For single-line input, use full formatted address (includes zip and state)  
                    return place.formattedAddress || place.displayName || '';
                }

                // Final fallback if nothing else worked
                const fallbackAddress = place.formattedAddress || place.shortFormattedAddress || place.displayName || '';
                console.log('Using final fallback address:', fallbackAddress);
                return fallbackAddress;
            }

            // Function to generate a new session token
            async function generateSessionToken() {
                try {
                    const response = await fetch('/api/google-maps/session-token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            sessionToken = data.sessionToken;
                            console.log('Generated new session token');
                        }
                    }
                } catch (error) {
                    console.error('Failed to generate session token:', error);
                }
            }

            // Function to make autocomplete requests using server-side API
            async function makeAutocompleteRequest(inputValue) {
                if (!inputValue || inputValue.length < 2) {
                    suggestionsContainer.classList.add('hidden');
                    currentSuggestions = [];
                    return;
                }

                const currentRequestId = ++requestId;

                // Show loading indicator
                const loadingIndicator = document.getElementById('{{ $uniqueId }}_loading');
                if (loadingIndicator) {
                    loadingIndicator.classList.remove('hidden');
                }

                // Prepare the request for server-side API
                const requestData = {
                    input: inputValue.trim(),
                    sessionToken: sessionToken,
                    @if($restrictions)
                    restrictions: @json($restrictions),
                    @endif
                    language: 'en-US',
                    region: 'us'
                };

                // Add location bias if available
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        async (position) => {
                            requestData.origin = {
                                lat: position.coords.latitude,
                                lng: position.coords.longitude
                            };
                            await makeRequest();
                        },
                        async () => {
                            // Geolocation failed, make request without location bias
                            await makeRequest();
                        },
                        { timeout: 2000 }
                    );
                } else {
                    await makeRequest();
                }

                async function makeRequest() {
                    try {
                        console.log('Making server-side autocomplete request:', requestData);

                        // Make request to our server-side API
                        const response = await fetch('/api/google-maps/autocomplete', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                            },
                            body: JSON.stringify(requestData)
                        });

                        const data = await response.json();

                        // Hide loading indicator
                        if (loadingIndicator) {
                            loadingIndicator.classList.add('hidden');
                        }

                        // Check if this request is still the latest
                        if (currentRequestId !== requestId) {
                            console.log('Request superseded, ignoring results');
                            return;
                        }

                        if (data.success && data.suggestions && data.suggestions.length > 0) {
                            console.log('Received suggestions:', data.suggestions);
                            currentSuggestions = data.suggestions;
                            selectedIndex = -1;
                            displaySuggestions(data.suggestions);

                            // Update session token if provided
                            if (data.sessionToken) {
                                sessionToken = data.sessionToken;
                            }
                        } else {
                            console.log('No suggestions found');
                            currentSuggestions = [];
                            suggestionsContainer.innerHTML = `
                                <div class="px-4 py-3 text-sm text-base-content/60">
                                    <i class="fa-sharp fa-search mr-2"></i>
                                    No addresses found for "${inputValue}"
                                </div>
                            `;
                            positionSuggestionsContainer();
                            suggestionsContainer.classList.remove('hidden');
                        }
                    } catch (error) {
                        console.error('Autocomplete request failed:', error);

                        // Hide loading indicator
                        if (loadingIndicator) {
                            loadingIndicator.classList.add('hidden');
                        }

                        suggestionsContainer.innerHTML = `
                            <div class="px-4 py-3 text-sm text-error">
                                <i class="fa-sharp fa-exclamation-triangle mr-2"></i>
                                Unable to load suggestions. Please try again.
                            </div>
                        `;
                        positionSuggestionsContainer();
                        suggestionsContainer.classList.remove('hidden');

                        // Hide error after 3 seconds
                        setTimeout(() => {
                            suggestionsContainer.classList.add('hidden');
                        }, 3000);
                    }
                }
            }

            // Function to display suggestions from NEW API
            function displaySuggestions(suggestions) {
                suggestionsContainer.innerHTML = '';

                if (!suggestions || suggestions.length === 0) {
                    suggestionsContainer.classList.add('hidden');
                    return;
                }

                // Position the container if it's fixed positioned
                positionSuggestionsContainer();

                suggestions.forEach((suggestion, index) => {
                    const placePrediction = suggestion.placePrediction;
                    if (!placePrediction) return;

                    const suggestionElement = document.createElement('div');
                    suggestionElement.className = 'px-4 py-3 cursor-pointer hover:bg-base-200 border-b border-base-300 last:border-b-0 flex items-center gap-3 transition-colors duration-150';

                    // Add location icon
                    const icon = document.createElement('i');
                    icon.className = 'fa-sharp fa-location-dot text-primary text-sm flex-shrink-0';

                    // Add text content
                    const textContainer = document.createElement('div');
                    textContainer.className = 'flex-1 min-w-0';

                    const mainText = document.createElement('div');
                    mainText.className = 'text-sm font-medium text-base-content truncate';
                    mainText.textContent = placePrediction.text.toString();

                    textContainer.appendChild(mainText);

                    // Add place type info if available
                    if (placePrediction.types && placePrediction.types.length > 0) {
                        const typeInfo = document.createElement('div');
                        typeInfo.className = 'text-xs text-base-content/60';
                        const primaryType = placePrediction.types[0].replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                        typeInfo.textContent = primaryType;
                        textContainer.appendChild(typeInfo);
                    }

                    suggestionElement.appendChild(icon);
                    suggestionElement.appendChild(textContainer);

                    suggestionElement.addEventListener('click', async (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        await selectSuggestion(suggestion, index);
                    });

                    suggestionsContainer.appendChild(suggestionElement);
                });

                positionSuggestionsContainer();
                suggestionsContainer.classList.remove('hidden');
            }

            // Mark this input as having autocomplete initialized
            currentInput.dataset.autocompleteInitialized = 'true';

            console.log('initializeNewAutocomplete: Setup complete');

            // DEBUG: Add a debug button to manually show suggestions
            if (window.location.search.includes('debug=autocomplete')) {
                const debugBtn = document.createElement('button');
                debugBtn.textContent = 'DEBUG: Show Suggestions';
                debugBtn.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 999999; background: red; color: white; padding: 5px;';
                debugBtn.onclick = function() {
                    // Create fake suggestions for testing
                    const fakeSuggestions = [
                        {
                            placePrediction: {
                                text: 'Test Address 1, City, State',
                                placeId: 'test1',
                                types: ['establishment']
                            }
                        },
                        {
                            placePrediction: {
                                text: 'Test Address 2, City, State',
                                placeId: 'test2',
                                types: ['street_address']
                            }
                        }
                    ];
                    displaySuggestions(fakeSuggestions);
                };
                document.body.appendChild(debugBtn);
            }

            // Function to select a suggestion using server-side API
            async function selectSuggestion(suggestion, index) {
                try {
                    console.log('Selecting suggestion:', suggestion);

                    const placePrediction = suggestion.placePrediction;
                    if (!placePrediction || !placePrediction.placeId) return;

                    // Fetch place details from server-side API
                    console.log('Fetching place details for placeId:', placePrediction.placeId);

                    let formattedAddress;
                    let place = null;

                    try {
                        const placeDetailsResponse = await fetch('/api/google-maps/place-details', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                            },
                            body: JSON.stringify({
                                placeId: placePrediction.placeId,
                                sessionToken: sessionToken,
                                fields: ['id', 'displayName', 'formattedAddress', 'shortFormattedAddress', 'location', 'addressComponents', 'types', 'primaryType', 'primaryTypeDisplayName']
                            })
                        });

                        console.log('Place details response status:', placeDetailsResponse.status);

                        if (!placeDetailsResponse.ok) {
                            console.error('Place details API error:', placeDetailsResponse.status, placeDetailsResponse.statusText);
                            const errorText = await placeDetailsResponse.text();
                            console.error('Error response body:', errorText);
                            throw new Error(`HTTP ${placeDetailsResponse.status}: ${placeDetailsResponse.statusText}`);
                        }

                        const placeDetailsData = await placeDetailsResponse.json();
                        console.log('Place details response data:', placeDetailsData);

                        if (placeDetailsData.success && placeDetailsData.place) {
                            place = placeDetailsData.place;
                            console.log('Place details received:', place);

                            // Try to construct a complete street address using address components
                            formattedAddress = constructCompleteAddress(place);

                            console.log('Selected place address from details:', formattedAddress);
                        } else {
                            // API returned success=false
                            console.log('Place details API returned success=false:', placeDetailsData.error || 'Unknown error');
                            throw new Error(placeDetailsData.error || 'Place details API returned success=false');
                        }
                    } catch (error) {
                        // Handle any errors (network, API, etc.)
                        console.error('Error fetching place details:', error);

                        // Fallback to using the prediction text
                        formattedAddress = placePrediction.text;
                        console.log('Using fallback address from prediction due to error:', formattedAddress);
                    }

                    // Set flag to prevent autocomplete triggering
                    isSettingValueProgrammatically = true;

                    // Update input value
                    currentInput.value = formattedAddress;

                    // Hide suggestions
                    suggestionsContainer.classList.add('hidden');
                    currentSuggestions = [];
                    selectedIndex = -1;

                    // Trigger change events
                    currentInput.dispatchEvent(new Event('change', { bubbles: true }));
                    currentInput.dispatchEvent(new Event('input', { bubbles: true }));

                    // Reset flag after a short delay
                    setTimeout(() => {
                        isSettingValueProgrammatically = false;
                        console.log('Reset isSettingValueProgrammatically to false');
                    }, 100);

                    // Store place data with new API fields
                    if (place) {
                        currentInput.dataset.placeData = JSON.stringify({
                            formatted_address: formattedAddress,
                            short_formatted_address: place.shortFormattedAddress,
                            display_name: place.displayName,
                            location: place.location,
                            address_components: place.addressComponents,
                            place_id: place.id,
                            types: place.types,
                            primary_type: place.primaryType,
                            primary_type_display_name: place.primaryTypeDisplayName
                        });
                    }

                    // Dispatch custom event
                    const customEvent = new CustomEvent('addressSelected', {
                        detail: {
                            place: place,
                            input: currentInput,
                            formattedAddress: formattedAddress
                        }
                    });
                    document.dispatchEvent(customEvent);

                    // Convert to display mode if enabled
                    @if($showDisplayMode)
                        setTimeout(() => {
                            convertToDisplayMode(formattedAddress);
                        }, 200);
                    @endif

                    // Generate new session token for next session
                    await generateSessionToken();

                } catch (error) {
                    console.error('Error selecting suggestion:', error);

                    // Fallback to using the prediction text
                    const placePrediction = suggestion.placePrediction;
                    const fallbackAddress = placePrediction ? placePrediction.text : '';

                    if (fallbackAddress) {
                        // Set flag to prevent autocomplete triggering
                        isSettingValueProgrammatically = true;

                        currentInput.value = fallbackAddress;

                        // Hide suggestions
                        suggestionsContainer.classList.add('hidden');
                        currentSuggestions = [];
                        selectedIndex = -1;

                        // Trigger change events
                        currentInput.dispatchEvent(new Event('change', { bubbles: true }));
                        currentInput.dispatchEvent(new Event('input', { bubbles: true }));

                        // Reset flag after a short delay
                        setTimeout(() => {
                            isSettingValueProgrammatically = false;
                            console.log('Reset isSettingValueProgrammatically to false (fallback)');
                        }, 100);

                        // Convert to display mode if enabled
                        @if($showDisplayMode)
                            setTimeout(() => {
                                convertToDisplayMode(fallbackAddress);
                            }, 200);
                        @endif
                    }
                }
            }

            // Add input event listener for typing
            currentInput.addEventListener('input', function(e) {
                const inputValue = e.target.value.trim();
                console.log('Input changed to:', inputValue);
                console.log('isSettingValueProgrammatically:', isSettingValueProgrammatically);

                // Skip autocomplete if we're setting the value programmatically
                if (isSettingValueProgrammatically) {
                    console.log('Skipping autocomplete - value set programmatically');
                    return;
                }

                // Clear previous timeout
                clearTimeout(currentInput.autocompleteTimeout);

                // Hide suggestions immediately if input is too short
                if (inputValue.length < 2) {
                    suggestionsContainer.classList.add('hidden');
                    currentSuggestions = [];
                    selectedIndex = -1;
                    return;
                }

                // Show loading indicator immediately for better UX
                const loadingIndicator = document.getElementById('{{ $uniqueId }}_loading');
                if (loadingIndicator && inputValue.length >= 2) {
                    loadingIndicator.classList.remove('hidden');
                }

                // Debounce the autocomplete requests
                currentInput.autocompleteTimeout = setTimeout(async () => {
                    await makeAutocompleteRequest(inputValue);
                }, 300);
            });

            // Add focus event to show suggestions if they exist
            currentInput.addEventListener('focus', function() {
                if (currentSuggestions.length > 0 && currentInput.value.trim().length >= 2) {
                    positionSuggestionsContainer();
                    suggestionsContainer.classList.remove('hidden');
                }
            });

            // Add blur event to hide suggestions (with delay to allow clicks)
            // DEBUG MODE: Comment out blur event to keep suggestions open for debugging
            /*
            currentInput.addEventListener('blur', function() {
                setTimeout(() => {
                    suggestionsContainer.classList.add('hidden');
                    selectedIndex = -1;
                }, 150);
            });
            */

            // Add keyboard navigation
            currentInput.addEventListener('keydown', async function(e) {
                if (!currentSuggestions.length || suggestionsContainer.classList.contains('hidden')) {
                    return;
                }

                switch (e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        selectedIndex = Math.min(selectedIndex + 1, currentSuggestions.length - 1);
                        updateSelectedSuggestion();
                        scrollToSelectedSuggestion();
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        selectedIndex = Math.max(selectedIndex - 1, -1);
                        updateSelectedSuggestion();
                        scrollToSelectedSuggestion();
                        break;
                    case 'Enter':
                        e.preventDefault();
                        if (selectedIndex >= 0 && selectedIndex < currentSuggestions.length) {
                            await selectSuggestion(currentSuggestions[selectedIndex], selectedIndex);
                        }
                        break;
                    case 'Escape':
                        e.preventDefault();
                        suggestionsContainer.classList.add('hidden');
                        currentSuggestions = [];
                        selectedIndex = -1;
                        currentInput.blur();
                        break;
                    case 'Tab':
                        e.preventDefault();
                        // Select the first suggestion if none is selected, or move to next
                        if (selectedIndex === -1) {
                            selectedIndex = 0;
                        } else {
                            selectedIndex = Math.min(selectedIndex + 1, currentSuggestions.length - 1);
                        }
                        updateSelectedSuggestion();
                        scrollToSelectedSuggestion();
                        break;
                }
            });

            // Function to update visual selection
            function updateSelectedSuggestion() {
                const suggestionElements = suggestionsContainer.children;
                for (let i = 0; i < suggestionElements.length; i++) {
                    const element = suggestionElements[i];
                    if (i === selectedIndex) {
                        element.classList.add('bg-primary', 'text-primary-content');
                        element.classList.remove('hover:bg-base-200');
                        // Update icon color for selected item
                        const icon = element.querySelector('i');
                        if (icon) {
                            icon.classList.remove('text-primary');
                            icon.classList.add('text-primary-content');
                        }
                    } else {
                        element.classList.remove('bg-primary', 'text-primary-content');
                        element.classList.add('hover:bg-base-200');
                        // Reset icon color for unselected items
                        const icon = element.querySelector('i');
                        if (icon) {
                            icon.classList.add('text-primary');
                            icon.classList.remove('text-primary-content');
                        }
                    }
                }
            }

            // Function to scroll to selected suggestion
            function scrollToSelectedSuggestion() {
                if (selectedIndex >= 0) {
                    const selectedElement = suggestionsContainer.children[selectedIndex];
                    if (selectedElement) {
                        selectedElement.scrollIntoView({
                            block: 'nearest',
                            behavior: 'smooth'
                        });
                    }
                }
            }

            // Hide suggestions when clicking outside
            const clickOutsideHandler = function(e) {
                // Check if currentInput and its parent still exist
                if (!currentInput || !currentInput.parentNode) {
                    return;
                }

                const inputContainer = currentInput.parentNode;
                if (!inputContainer.contains(e.target)) {
                    suggestionsContainer.classList.add('hidden');
                    selectedIndex = -1;
                }
            };

            document.addEventListener('click', clickOutsideHandler);

            // Store the handler for cleanup if needed
            currentInput.clickOutsideHandler = clickOutsideHandler;

            // Add window resize and scroll listeners to reposition suggestions if needed
            if (hasQuillEditor || hasModal) {
                const repositionHandler = function() {
                    if (!suggestionsContainer.classList.contains('hidden')) {
                        positionSuggestionsContainer();
                    }
                };

                window.addEventListener('resize', repositionHandler);
                window.addEventListener('scroll', repositionHandler, true);

                // Store handlers for cleanup
                currentInput.repositionHandler = repositionHandler;
            }



        } catch (error) {
            console.error('Error initializing new autocomplete:', error);
        }
    }

    // Listen for external addressSelected events (e.g., from customer selection)
    document.addEventListener('addressSelected', function(event) {
        // Check if this event is for our current input
        const currentInput = document.getElementById('{{ $uniqueId }}');
        if (event.detail.input === currentInput && event.detail.formattedAddress) {
            @if($showDisplayMode)
                convertToDisplayMode(event.detail.formattedAddress);
            @endif
        }
    });

    // Initialize autocomplete immediately (no Google Maps API dependency)
    setTimeout(initializeNewAutocomplete, 100);
});
</script>
@endpush

@once
@push('scripts')
<!-- No Google Maps API loading needed - using server-side API -->
@endpush

@push('styles')
<style>
/* Custom autocomplete suggestions dropdown */
.google-maps-autocomplete-suggestions {
    background-color: var(--color-base-100);
    border: 1px solid hsl(var(--bc) / 0.2);
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    font-family: inherit;
    z-index: 99999 !important;
    position: absolute;
    max-height: 300px;
    overflow-y: auto;
    width: 100%;
}

/* Suggestion items */
.google-maps-autocomplete-suggestions > div {
    transition: all 0.15s ease-in-out;
}

.google-maps-autocomplete-suggestions > div:hover {
    background-color: hsl(var(--b2)) !important;
}

.google-maps-autocomplete-suggestions > div.selected {
    background-color: hsl(var(--p)) !important;
    color: hsl(var(--pc)) !important;
}

/* Ensure suggestions appear above modals */
.modal .google-maps-autocomplete-suggestions,
.google-maps-autocomplete-suggestions.google-maps-autocomplete-modal-context {
    z-index: 100020 !important;
}

/* Force autocomplete above all Quill editor elements */
.ql-toolbar,
.ql-container,
.ql-editor {
    z-index: 1000 !important;
    position: relative;
}

/* Scrollbar styling for suggestions */
.google-maps-autocomplete-suggestions::-webkit-scrollbar {
    width: 6px;
}

.google-maps-autocomplete-suggestions::-webkit-scrollbar-track {
    background: hsl(var(--b2));
    border-radius: 3px;
}

.google-maps-autocomplete-suggestions::-webkit-scrollbar-thumb {
    background: hsl(var(--bc) / 0.3);
    border-radius: 3px;
}

.google-maps-autocomplete-suggestions::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--bc) / 0.5);
}

/* Loading state styling */
.google-maps-autocomplete-loading {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

/* Focus ring for accessibility */
.google-maps-autocomplete-input:focus {
    outline: 2px solid hsl(var(--p));
    outline-offset: 2px;
}

/* Ensure input container has proper positioning */
.google-maps-autocomplete-container {
    position: relative;
}
</style>
@endpush
@endonce
