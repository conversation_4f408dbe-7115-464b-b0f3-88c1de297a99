@props([
    'id' => 'image-dropzone-' . uniqid(),
    'name' => 'images[]',
    'maxFiles' => 5,
    'maxFilesize' => 10, // MB
    'maxWidth' => null,
    'maxHeight' => null,
    'clientResize' => true,
    'acceptedFormats' => 'image/jpeg,image/jpg,image/png,image/webp',
    'existingImages' => [],
    'sessionBased' => false,
    'sessionId' => null,
    'uploadUrl' => null,
    'deleteUrl' => null,
    'reorderUrl' => null,
    'multiple' => true,
    'capture' => 'environment',
    'triggerCameraUpload' => false,
    'immediateUpload' => false,
    'contextType' => null,
    'contextId' => null,
    'label' => 'Upload Photos',
    'helpText' => 'Drop images here or click to upload',
    'subText' => null,
    'showPreviews' => true,
])

@php
    $dropzoneId = 'dropzone-' . $id;
    $fileInputId = 'file-' . $id;
    $additionalInputId = 'additional-' . $id;
    $previewContainerId = 'preview-' . $id;
    $queueId = 'queue-' . $id;
    $progressId = 'progress-' . $id;
    
    $config = [
        'id' => $id,
        'name' => $name,
        'maxFiles' => $maxFiles,
        'maxFilesize' => $maxFilesize,
        'maxWidth' => $maxWidth,
        'maxHeight' => $maxHeight,
        'clientResize' => $clientResize,
        'acceptedFormats' => $acceptedFormats,
        'multiple' => $multiple,
        'sessionBased' => $sessionBased,
        'sessionId' => $sessionId,
        'uploadUrl' => $uploadUrl,
        'deleteUrl' => $deleteUrl,
        'reorderUrl' => $reorderUrl,
        'capture' => $capture,
        'triggerCameraUpload' => $triggerCameraUpload,
        'immediateUpload' => $immediateUpload,
        'contextType' => $contextType,
        'contextId' => $contextId,
        'showPreviews' => $showPreviews
    ];
@endphp

<div class="image-dropzone-component" data-dropzone-id="{{ $dropzoneId }}">
    @if($label)
        <label class="label">
            <span class="label-text">
                <i class="fa-sharp fa-images text-primary mr-2"></i>
                {{ $label }}
            </span>
        </label>
    @endif
    
    @if($helpText || $subText)
        <p class="text-base-content/70 text-sm mb-3">
            {{ $helpText }}
            @if($subText)
                <br><span class="text-info font-medium">{{ $subText }}</span>
            @endif
        </p>
    @endif

    <!-- Dropzone -->
    <div id="{{ $dropzoneId }}" 
         class="border-2 border-dashed border-base-300 rounded-lg p-8 text-center hover:border-primary transition-colors cursor-pointer"
         data-config="{{ json_encode($config) }}">
        <i class="fa-sharp fa-cloud-upload text-4xl text-base-content/50 mb-4"></i>
        <p class="text-base-content/70 mb-2">{{ $helpText }}</p>
        <p class="text-sm text-base-content/50">
            Maximum {{ $maxFiles }} {{ Str::plural('image', $maxFiles) }} • 
            {{ implode(', ', array_map(fn($f) => strtoupper(str_replace('image/', '', $f)), explode(',', $acceptedFormats))) }}
        </p>
        <input type="file" 
               id="{{ $fileInputId }}" 
               name="{{ $name }}"
               accept="{{ $acceptedFormats }}" 
               @if($multiple) multiple @endif
               @if($capture) capture="{{ $capture }}" @endif
               class="hidden" />
    </div>
    
    <!-- Hidden input for session ID if session-based -->
    @if($sessionBased && $sessionId)
        <input type="hidden" name="session_id" value="{{ $sessionId }}" />
    @endif
    
    <!-- Additional file input for adding more -->
    @if($maxFiles > 1)
        <input type="file" 
               id="{{ $additionalInputId }}" 
               accept="{{ $acceptedFormats }}"
               @if($multiple) multiple @endif
               @if($capture) capture="{{ $capture }}" @endif
               class="hidden" />
    @endif
    
    <!-- Upload Progress -->
    <div id="{{ $progressId }}" class="hidden mt-4">
        <div class="flex items-center gap-3">
            <span class="loading loading-spinner loading-sm"></span>
            <span class="text-sm">Processing image...</span>
        </div>
    </div>
    
    <!-- Image Preview/Queue Container -->
    @if($showPreviews)
    <div id="{{ $queueId }}" class="mt-4 @if(empty($existingImages)) hidden @endif">
        <!-- Queue Header -->
        <div class="flex items-center justify-between mb-3">
            <h5 class="font-semibold text-base-content">Selected Images:</h5>
            <div class="flex items-center gap-4">
                <span class="photo-count text-sm text-base-content/70">
                    {{ count($existingImages) }} {{ Str::plural('image', count($existingImages)) }} selected
                </span>
                <button type="button" class="add-more-btn btn btn-outline btn-sm gap-2">
                    <i class="fa-sharp fa-plus"></i>
                    Add More
                </button>
            </div>
        </div>
        
        <!-- Preview Grid -->
        <div id="{{ $previewContainerId }}" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
            @foreach($existingImages as $image)
                <div class="relative group" data-image-id="{{ $image->id ?? $image['id'] ?? '' }}">
                    <div class="aspect-square bg-base-200 rounded-lg overflow-hidden">
                        <img src="{{ $image->src ?? $image['src'] ?? '' }}" 
                             alt="{{ $image->alt ?? $image['alt'] ?? 'Image' }}" 
                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-200">
                    </div>
                    <button type="button" 
                            class="absolute top-1 right-1 btn btn-circle btn-xs btn-error opacity-0 group-hover:opacity-100 transition-opacity remove-image-btn"
                            data-image-id="{{ $image->id ?? $image['id'] ?? '' }}">
                        <i class="fa-sharp fa-times text-xs"></i>
                    </button>
                </div>
            @endforeach
        </div>
        
        <!-- Queue Actions -->
        <div class="flex items-center justify-between pt-3 mt-3 border-t border-base-300">
            <button type="button" class="clear-all-btn btn btn-ghost btn-sm text-error gap-2">
                <i class="fa-sharp fa-trash"></i>
                Clear All
            </button>
            <span class="total-size text-xs text-base-content/60"></span>
        </div>
    </div>
    @endif
    
    <!-- Error Container -->
    <div class="error-container mt-2"></div>
</div>

@once
    @push('scripts')
    <!-- Include browser-image-compression from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/browser-image-compression@2.0.2/dist/browser-image-compression.min.js"></script>
    <script src="{{ asset('js/image-dropzone.js') }}?v={{ time() }}"></script>
    @endpush
@endonce