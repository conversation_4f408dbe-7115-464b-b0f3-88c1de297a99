<div class="grid mt-6 grid-cols-1 md:grid-cols-4 gap-4 w-full">
    {{-- Pagination Controls - 3 cols on md+ screens --}}
    <div class="md:col-span-3 flex justify-center md:justify-start">
        <div class="join">
            {{-- Previous Page Link --}}
            @if ($paginator->onFirstPage())
                <button class="join-item btn btn-disabled">«</button>
            @else
                <a href="{{ $paginator->previousPageUrl() . '&' . http_build_query(array_merge(request()->except('page'), ['pagination' => $pagination])) }}" class="join-item btn btn-neutral">«</a>
            @endif

            {{-- Pagination Elements --}}
            @php
                $lastPage = $paginator->lastPage();
                $currentPage = $paginator->currentPage();
                $window = 3; // Number of pages to show around the current page
                
                // Determine the range of pages to display
                if ($lastPage <= 7) {
                    // Show all pages if there are 7 or fewer
                    $startPage = 1;
                    $endPage = $lastPage;
                } elseif ($currentPage <= $window + 1) {
                    // Near the beginning
                    $startPage = 1;
                    $endPage = 7;
                } elseif ($currentPage >= $lastPage - $window) {
                    // Near the end
                    $startPage = $lastPage - 6;
                    $endPage = $lastPage;
                } else {
                    // Middle pages
                    $startPage = $currentPage - $window;
                    $endPage = $currentPage + $window;
                }
            @endphp

            {{-- First page and ellipsis if needed --}}
            @if ($startPage > 1)
                <a href="{{ $paginator->url(1) . '&' . http_build_query(array_merge(request()->except('page'), ['pagination' => $pagination])) }}" class="join-item btn btn-neutral">1</a>
                @if ($startPage > 2)
                    <button class="join-item btn btn-neutral" disabled>...</button>
                @endif
            @endif

            {{-- Page numbers --}}
            @for ($page = $startPage; $page <= $endPage; $page++)
                @if ($page == $currentPage)
                    <button class="join-item btn btn-primary">{{ $page }}</button>
                @else
                    <a href="{{ $paginator->url($page) . '&' . http_build_query(array_merge(request()->except('page'), ['pagination' => $pagination])) }}" class="join-item btn btn-neutral">{{ $page }}</a>
                @endif
            @endfor

            {{-- Last page and ellipsis if needed --}}
            @if ($endPage < $lastPage)
                @if ($endPage < $lastPage - 1)
                    <button class="join-item btn btn-neutral" disabled>...</button>
                @endif
                <a href="{{ $paginator->url($lastPage) . '&' . http_build_query(array_merge(request()->except('page'), ['pagination' => $pagination])) }}" class="join-item btn btn-neutral">{{ $lastPage }}</a>
            @endif

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())
                <a href="{{ $paginator->nextPageUrl() . '&' . http_build_query(array_merge(request()->except('page'), ['pagination' => $pagination])) }}" class="join-item btn btn-neutral">»</a>
            @else
                <button class="join-item btn btn-disabled">»</button>
            @endif
        </div>
    </div>

    {{-- Pagination Amount Selector - 1 col on md+ screens --}}
    <div class="md:col-span-1 flex justify-center md:justify-end items-center">
        <label for="pagination-select" class="mr-2 text-sm text-base-content">Items per page:</label>
        <select id="pagination-select" name="pagination" class="select select-bordered select-sm" onchange="updatePagination(this.value)">
            @foreach([5, 10, 25, 50, 100] as $value)
                <option value="{{ $value }}" {{ $pagination == $value ? 'selected' : '' }}>
                    {{ $value }}
                </option>
            @endforeach
        </select>
    </div>

    {{-- Showing x of x results --}}
    <div class="col-span-full text-base-content text-sm text-center md:text-left">
        Showing {{ $paginator->firstItem() ?? 0 }} to {{ $paginator->lastItem() ?? 0 }} of {{ $paginator->total() }} results.
    </div>
</div>

<script>
    function updatePagination(itemsPerPage) {
        const url = new URL(window.location.href);
        url.searchParams.set('pagination', itemsPerPage);
        window.location.href = url.toString();
    }
</script>
