<div>
    <h4 class="font-medium mb-2">{{ $title ?? 'Uploaded Files' }}</h4>

    @if($files->isEmpty())
        <div class="text-sm text-gray-500 italic">
            <i class="fa-sharp fa-circle-info mr-1"></i>
            <span>No files have been uploaded yet.</span>
        </div>
    @else
        <div class="overflow-x-auto">
            <table class="table table-sm w-full">
                <thead>
                    <tr>
                        <th>File</th>
                        <th>Notes</th>
                        <th>Size</th>
                        <th>Uploaded</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($files as $file)
                        <tr>
                            <td>
                                <div class="flex items-center gap-2">
                                    <i class="fa-sharp {{ $file->icon }} text-primary"></i>
                                    <span class="font-medium">{{ Str::limit($file->original_filename, 20) }}</span>
                                    @if(isset($certificate) && isset($certificate->stats['coc_file_id']) && $certificate->stats['coc_file_id'] == $file->id)
                                        <span class="badge badge-sm badge-primary">Chain of Custody</span>
                                    @elseif(isset($file->metadata['is_coc']) && $file->metadata['is_coc'])
                                        <span class="badge badge-sm badge-primary">Chain of Custody</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <button type="button" class="btn btn-xs btn-ghost {{ $file->description ? 'text-primary' : 'text-gray-400' }}"
                                        onclick="openFileNotesModal('{{ $file->id }}', '{{ addslashes($file->original_filename) }}', '{{ addslashes($file->description ?? '') }}')"
                                        title="{{ $file->description ? 'Edit notes' : 'Add notes' }}"
                                        data-file-id="{{ $file->id }}"
                                        data-description="{{ addslashes($file->description ?? '') }}">
                                    <i class="fa-sharp fa-note-sticky"></i>
                                </button>
                            </td>
                            <td class="text-xs">{{ $file->human_readable_size }}</td>
                            <td class="text-xs">
                                {{ $file->created_at->format('M j, Y') }}
                                <span class="text-gray-500">by {{ $file->uploader->name ?? 'Unknown' }}</span>
                            </td>
                            <td>
                                <div class="flex gap-1">
                                    <a href="{{ route('files.view', $file) }}" class="btn btn-xs btn-ghost text-info" target="_blank" title="View">
                                        <i class="fa-sharp fa-eye"></i>
                                    </a>
                                    <a href="{{ route('files.download', $file) }}" class="btn btn-xs btn-ghost text-primary" title="Download">
                                        <i class="fa-sharp fa-download"></i>
                                    </a>
                                    @if(Auth::id() === $file->uploaded_by || Auth::user()->isAdmin())
                                        <button type="button" class="btn btn-xs btn-ghost text-error" title="Delete"
                                                onclick="deleteFile('{{ $file->id }}', '{{ isset($certificate) && isset($certificate->stats['coc_file_id']) && $certificate->stats['coc_file_id'] == $file->id }}')">
                                            <i class="fa-sharp fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @endif
</div>

<!-- File Notes Modal -->
<dialog id="fileNotesModal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">File Notes</h3>
        <p id="fileNameDisplay" class="text-sm mb-4"></p>

        <form id="fileNotesForm">
            <div class="form-control">
                <textarea id="fileNotesInput" class="textarea textarea-bordered h-24" placeholder="Enter notes about this file"></textarea>
            </div>

            <div class="modal-action">
                <input type="hidden" id="fileIdInput" value="">
                <button type="button" class="btn btn-primary" onclick="saveFileNotes()">Save</button>
                <button type="button" class="btn" onclick="closeFileNotesModal()">Cancel</button>
            </div>
        </form>
    </div>
    <form method="dialog" class="modal-backdrop">
        <button onclick="closeFileNotesModal()">close</button>
    </form>
</dialog>

<script>
    function openFileNotesModal(fileId, fileName, notes) {
        document.getElementById('fileIdInput').value = fileId;
        document.getElementById('fileNameDisplay').textContent = fileName;
        document.getElementById('fileNotesInput').value = notes;
        document.getElementById('fileNotesModal').showModal();
    }

    function closeFileNotesModal() {
        document.getElementById('fileNotesModal').close();
    }

    function deleteFile(fileId, isCocDocument) {
        if (!confirm('Are you sure you want to delete this file?')) {
            return;
        }

        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        fetch(`/files/${fileId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // If this was a CoC document or the API tells us it was, reload the page
                // This ensures the UI is updated correctly with the CoC checkbox showing again
                if (isCocDocument === 'true' || data.was_coc_document) {
                    window.location.reload();
                } else {
                    // Just remove the row from the table
                    const row = document.querySelector(`button[data-file-id="${fileId}"]`).closest('tr');
                    if (row) {
                        row.remove();
                    }
                }
            } else {
                alert('Error deleting file: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting file. Please try again.');
        });
    }

    function saveFileNotes() {
        const fileId = document.getElementById('fileIdInput').value;
        const notes = document.getElementById('fileNotesInput').value;
        const fileName = document.getElementById('fileNameDisplay').textContent;
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Show saving indicator
        const saveButton = document.querySelector('#fileNotesForm .btn-primary');
        const originalText = saveButton.innerHTML;
        saveButton.innerHTML = '<i class="fa-sharp fa-spinner fa-spin"></i> Saving...';
        saveButton.disabled = true;

        fetch(`/files/${fileId}/update-notes`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({
                description: notes
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the button color and onclick attribute in the list
                const noteButton = document.querySelector(`button[data-file-id="${fileId}"]`);
                if (notes) {
                    noteButton.classList.remove('text-gray-400');
                    noteButton.classList.add('text-primary');
                    noteButton.title = 'Edit notes';
                } else {
                    noteButton.classList.remove('text-primary');
                    noteButton.classList.add('text-gray-400');
                    noteButton.title = 'Add notes';
                }

                // Update the onclick attribute to include the new description
                const fileName = document.getElementById('fileNameDisplay').textContent;
                const escapedNotes = notes.replace(/'/g, "\\'");
                noteButton.setAttribute('onclick', `openFileNotesModal('${fileId}', '${fileName}', '${escapedNotes}')`);

                // If we're in a table, also update any data attributes for future reference
                noteButton.setAttribute('data-description', escapedNotes);

                closeFileNotesModal();
            } else {
                alert('Error saving notes: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error saving notes. Please try again.');
        })
        .finally(() => {
            // Restore button state
            saveButton.innerHTML = originalText;
            saveButton.disabled = false;
        });
    }
</script>
