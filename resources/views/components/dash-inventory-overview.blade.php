<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Inventory Overview -->
    <div class="p-6 lg:p-8">
        <h2 class="text-2xl font-semibold text-base-content mb-4">Inventory Overview</h2>
        <table class="table w-full border-collapse">
            <thead class="bg-neutral text-neutral-content uppercase text-sm">
                <tr>
                    <th class="px-6 py-3">Status</th>
                    <th class="px-6 py-3 text-center">Count</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-base-200 bg-base-300">
                <tr class="hover:bg-base-200">
                    <td class="px-6 py-4 flex items-center gap-3 text-base-content">
                        <i class="fa-sharp fa-solid fa-box-open text-base-content"></i>
                        <a href="{{ route('inventory.index', ['status' => 'intake']) }}" class="link link-base-content hover:underline">
                            Intake
                        </a>
                    </td>
                    <td class="px-6 py-4 text-center text-base-content font-semibold">{{ $statusCounts['intake'] ?? 0 }}</td>
                </tr>
                <tr class="hover:bg-base-200">
                    <td class="px-6 py-4 flex items-center gap-3 text-base-content">
                        <i class="fa-sharp fa-solid fa-tools text-base-content"></i>
                        <a href="{{ route('inventory.index', ['status' => 'refurbishing']) }}" class="link link-base-content hover:underline">
                            Refurbishing
                        </a>
                    </td>
                    <td class="px-6 py-4 text-center text-base-content font-semibold">{{ $statusCounts['refurbishing'] ?? 0 }}</td>
                </tr>
                <tr class="hover:bg-base-200">
                    <td class="px-6 py-4 flex items-center gap-3 text-base-content">
                        <i class="fa-sharp fa-solid fa-broom text-base-content"></i>
                        <a href="{{ route('inventory.index', ['status' => 'cleaning']) }}" class="link link-base-content hover:underline">
                            Cleaning
                        </a>
                    </td>
                    <td class="px-6 py-4 text-center text-base-content font-semibold">{{ $statusCounts['cleaning'] ?? 0 }}</td>
                </tr>
                <tr class="hover:bg-base-200">
                    <td class="px-6 py-4 flex items-center gap-3 text-base-content">
                        <i class="fa-sharp fa-solid fa-check-circle text-base-content"></i>
                        <a href="{{ route('inventory.index', ['status' => 'ready-to-list']) }}" class="link link-base-content hover:underline">
                            Ready to List
                        </a>
                    </td>
                    <td class="px-6 py-4 text-center text-base-content font-semibold">{{ $statusCounts['ready-to-list'] ?? 0 }}</td>
                </tr>
                <tr class="hover:bg-base-200">
                    <td class="px-6 py-4 flex items-center gap-3 text-base-content">
                        <i class="fa-sharp fa-solid fa-store text-base-content"></i>
                        <a href="{{ route('inventory.index', ['status' => 'forsale']) }}" class="link link-base-content hover:underline">
                            For Sale
                        </a>
                    </td>
                    <td class="px-6 py-4 text-center text-base-content font-semibold">{{ $statusCounts['forsale'] ?? 0 }}</td>
                </tr>
                <tr class="hover:bg-base-200">
                    <td class="px-6 py-4 flex items-center gap-3 text-base-content">
                        <i class="fa-sharp fa-solid fa-dollar-sign text-base-content me-2"></i>
                        <a href="{{ route('inventory.index', ['status' => 'sold']) }}" class="link link-base-content hover:underline">
                            Sold
                        </a>
                    </td>
                    <td class="px-6 py-4 text-center text-base-content font-semibold">{{ $statusCounts['sold'] ?? 0 }}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Status Count Chart -->
    <div class="p-6">
        <h2 class="text-2xl font-semibold text-base-content mb-4">Status Count Chart</h2>
        <p class="text-base-content/70 mb-4 text-sm">A chart of the current inventory status counts excluding sold items.</p>
        <!-- Pie Chart Container -->
        <div class="flex justify-center items-center aspect-square w-full max-w-md mx-auto">
            <canvas id="inventoryPieChart"></canvas>
        </div>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const ctx = document.getElementById('inventoryPieChart').getContext('2d');

        // Data from PHP
        const labels = {!! json_encode(array_keys($statusCounts)) !!};
        const dataCounts = {!! json_encode(array_values($statusCounts)) !!};

        // Remove "sold" from the data
        const soldIndex = labels.indexOf('sold');
        if (soldIndex > -1) {
            labels.splice(soldIndex, 1);
            dataCounts.splice(soldIndex, 1);
        }

 
        // Colors for statuses
        const colors = {
            "intake": "#3B82F6", // Blue
            "refurbishing": "#F97316", // Orange
            "cleaning": "#06B6D4", // Cyan
            "ready-to-list": "#8B5CF6", // Purple
            "forsale": "#22C55E", // Green
            "commodity": "#EF4444" // Red
        };

        // Create color sets with opacity
        const backgroundColors = {};
        const hoverColors = {};
        
        for (const [key, value] of Object.entries(colors)) {
            // Convert hex to rgba for background (80% opacity)
            backgroundColors[key] = value + "CC"; // CC = 80% opacity
            hoverColors[key] = value;
        }

        const chartBackgroundColors = labels.map(label => backgroundColors[label] || '#6B7280'); // Default to gray
        const chartHoverColors = labels.map(label => hoverColors[label] || '#4B5563'); // Default to darker gray

        // Chart.js configuration
        const data = {
            labels: labels,
            datasets: [{
                data: dataCounts,
                backgroundColor: chartBackgroundColors,
                hoverBackgroundColor: chartHoverColors
            }]
        };

        const config = {
            type: 'pie',
            data: data,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            padding: 20,
                            font: {
                                size: 14
                            },
                            color: '#FFFFFF' // White text
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#111',
                        bodyColor: '#111',
                        borderWidth: 1,
                        borderColor: '#ddd',
                        padding: 10,
                        cornerRadius: 4,
                        callbacks: {
                            label: function (context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                return `${label}: ${value}`;
                            }
                        }
                    }
                }
            }
        };

        // Create the chart
        new Chart(ctx, config);
    });
</script>
