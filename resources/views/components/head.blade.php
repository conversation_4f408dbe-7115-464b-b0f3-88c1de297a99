<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- PWA Meta Tags -->
    <meta name="application-name" content="ETRFlow">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="ETRFlow">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#3b82f6">

    <!-- PWA Manifest -->
    <link rel="manifest" href="{{ asset('manifest.json') }}">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="{{ asset('etrflow-circlepadded.webp') }}">
    <link rel="apple-touch-icon" sizes="152x152" href="{{ asset('etrflow-circlepadded.webp') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('etrflow-circlepadded.webp') }}">
    <link rel="apple-touch-icon" sizes="167x167" href="{{ asset('etrflow-circlepadded.webp') }}">

    <title>{{ $title  }}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/webp" href="{{ asset('etrflow-circlepadded.webp') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <link href="{{ asset('fontawesome/css/fontawesome.min.css') }}" rel="stylesheet">
    <link href="{{ asset('fontawesome/css/sharp-solid.min.css') }}" rel="stylesheet">

    <script src="https://cdnjs.cloudflare.com/ajax/libs/awesomplete/1.1.7/awesomplete.min.js" integrity="sha512-Pc3/aEr2FIVZhHxe0RAC9SFrd+pxBJHN3pNJfJNTKc2XAFnXUjgQGIh6X935ePSXNMN6rFa3yftxSnZfJE8ZAg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/awesomplete/1.1.7/awesomplete.base.min.css" integrity="sha512-NozyuE047CUbwhtOgj/mo3K0NFmVSrHNVm9l1/1qUMc2CuU2na8cWYPk/ksYX4tMoRwWJ+bLFKsdO8OHM3X+yA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>



    @viteReactRefresh
    @vite(['resources/css/app.css', 'resources/js/app.jsx'])
    @stack('styles')

    <script>
        /**
 * Shows a toast message with the given type and message.
 * @param {string} type - The type of toast ('success', 'error', 'info', 'warning').
 * @param {string} message - The message to display.
 * @param {number} timeout - Time in milliseconds before the toast disappears. Default is 5000ms.
 */
function showToast(type = 'success', message = '', timeout = 5000) {
    const toast = document.getElementById('dynamicToast');
    const toastMessage = document.getElementById('dynamicToastMessage');

    if (!toast || !toastMessage) {
        console.error('Toast element not found.');
        return;
    }

    // Update the message
    toastMessage.textContent = message;

    // Update the toast type
    toast.className = `toast toast-end`; // Reset classes
    const alertClass = `alert alert-${type} shadow-lg`;
    const iconClass = {
        success: 'fa-check-circle text-green-500',
        error: 'fa-times-circle text-red-500',
        info: 'fa-info-circle text-blue-500',
        warning: 'fa-exclamation-circle text-yellow-500',
    };

    const alertDiv = toast.querySelector('.alert');
    const icon = toast.querySelector('i');

    if (alertDiv && icon) {
        alertDiv.className = alertClass;
        icon.className = `fa-sharp ${iconClass[type] || iconClass.info}`;
    }

    // Show the toast
    toast.classList.remove('hidden');

    // Hide the toast after the timeout
    setTimeout(() => {
        toast.classList.add('hidden');
    }, timeout);
}

// Example usage:
// showToast('success', 'This is a success message!', 5000);
// showToast('error', 'Something went wrong!', 3000);
// showToast('info', 'This is an informational message.', 4000);

</script>

    <!-- Styles -->
    <style>
        /* Mobile header title styles */
        @media (max-width: 1023px) {
            header h2 {
                font-size: 1rem !important;
                line-height: 1.25 !important;
                text-align: center !important;
                width: 100% !important;
                color: white !important;
            }

            header .flex-1 {
                display: flex !important;
                justify-content: center !important;
            }
        }

        /* Global header text color */
        header h2,
        header .text-gray-800,
        header .font-semibold {
            color: white !important;
        }
    </style>

</head>