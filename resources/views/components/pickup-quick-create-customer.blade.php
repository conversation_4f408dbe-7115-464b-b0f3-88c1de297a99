{{-- Enhanced Quick Create Customer Component for Pickup Requests --}}
@props([
    'id' => 'pickupCustomerSearch',
    'name' => 'customer_id',
    'pickupRequest' => null
])

@push('modals')
<dialog id="pickupQuickCreateCustomerModal" class="modal">
    <div class="modal-box max-w-4xl max-h-[90vh] overflow-y-auto">
        <!-- <PERSON>dal Header -->
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center gap-3">
                <div class="avatar avatar-placeholder">
                    <div class="bg-primary text-primary-content w-10 rounded-lg">
                        <i class="fa-sharp fa-user-plus text-lg"></i>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-base-content">Create New Customer</h3>
                    <p class="text-sm text-base-content/70">Customer details will be pre-filled from pickup request</p>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-circle btn-ghost" onclick="pickupQuickCreateCustomerModal.close()">
                <i class="fa-sharp fa-times"></i>
            </button>
        </div>

        <!-- Pre-filled Data Notice -->
        @if($pickupRequest)
        <div class="alert alert-info mb-6">
            <i class="fa-sharp fa-info-circle"></i>
            <div>
                <div class="font-medium">Pickup Request Data Available</div>
                <div class="text-sm">Contact information from the pickup request has been pre-filled below. You can modify any details as needed.</div>
            </div>
        </div>
        @endif

        <!-- Form Content -->
        <form id="pickupQuickCreateCustomerForm" onsubmit="return false;" class="space-y-6">
            @csrf
            
            <!-- Customer Form Fields -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Name Field -->
                <div>
                    <label class="label" for="pickup_name">
                        <span class="label-text">
                            <i class="fa-sharp fa-solid fa-building text-primary mr-2" id="pickup_nameIcon"></i>
                            <span id="pickup_nameLabel">Business Name</span> <span class="text-error font-bold">*</span>
                        </span>
                    </label>
                    <input type="text" name="name" id="pickup_name" class="input input-bordered w-full"
                           placeholder="Enter business or customer name..." required>
                </div>

                <!-- Type Field -->
                <div>
                    <label class="label">
                        <span class="label-text">
                            <i class="fa-sharp fa-tag text-primary mr-2"></i>
                            Customer Type <span class="text-error font-bold">*</span>
                        </span>
                    </label>
                    <div class="grid grid-cols-2 gap-2">
                        <label class="flex items-center gap-2 p-3 bg-base-200/50 border border-base-300 rounded-lg hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                            <input type="radio" name="type" value="Business" class="radio radio-primary radio-sm" checked required>
                            <div class="flex items-center gap-2">
                                <i class="fa-sharp fa-building text-primary text-sm"></i>
                                <span class="font-medium text-base-content">Business</span>
                            </div>
                        </label>

                        <label class="flex items-center gap-2 p-3 bg-base-200/50 border border-base-300 rounded-lg hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                            <input type="radio" name="type" value="Residential Customer" class="radio radio-primary radio-sm">
                            <div class="flex items-center gap-2">
                                <i class="fa-sharp fa-home text-primary text-sm"></i>
                                <span class="font-medium text-base-content">Residential</span>
                            </div>
                        </label>

                        <label class="flex items-center gap-2 p-3 bg-base-200/50 border border-base-300 rounded-lg hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                            <input type="radio" name="type" value="Online Customer" class="radio radio-primary radio-sm">
                            <div class="flex items-center gap-2">
                                <i class="fa-sharp fa-globe text-primary text-sm"></i>
                                <span class="font-medium text-base-content">Online</span>
                            </div>
                        </label>

                        <label class="flex items-center gap-2 p-3 bg-base-200/50 border border-base-300 rounded-lg hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                            <input type="radio" name="type" value="Bulk Buyer" class="radio radio-primary radio-sm">
                            <div class="flex items-center gap-2">
                                <i class="fa-sharp fa-boxes text-primary text-sm"></i>
                                <span class="font-medium text-base-content">Bulk Buyer</span>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Primary Contact Field -->
                <div id="pickup_contactField">
                    <label class="label" for="pickup_contact">
                        <span class="label-text">
                            <i class="fa-sharp fa-user text-primary mr-2"></i>
                            Primary Contact
                        </span>
                    </label>
                    <input type="text" name="contact" id="pickup_contact" class="input input-bordered input-sm w-full"
                           placeholder="Enter primary contact person...">
                </div>

                <!-- Website Field -->
                <div id="pickup_websiteField">
                    <label class="label" for="pickup_website">
                        <span class="label-text">
                            <i class="fa-sharp fa-globe text-primary mr-2"></i>
                            Website
                        </span>
                    </label>
                    <input type="text" name="website" id="pickup_website" class="input input-bordered input-sm w-full"
                           placeholder="https://example.com">
                </div>

                <!-- Email Field -->
                <div>
                    <label class="label" for="pickup_email">
                        <span class="label-text">
                            <i class="fa-sharp fa-envelope text-primary mr-2"></i>
                            Email Address
                        </span>
                    </label>
                    <input type="email" name="email" id="pickup_email" class="input input-bordered input-sm w-full"
                           placeholder="Enter email address...">
                </div>

                <!-- Phone Field -->
                <div>
                    <label class="label" for="pickup_phone">
                        <span class="label-text">
                            <i class="fa-sharp fa-phone text-primary mr-2"></i>
                            Phone Number
                        </span>
                    </label>
                    <input type="text" name="phone" id="pickup_phone" class="input input-bordered input-sm w-full"
                           placeholder="Enter phone number (any format)">
                </div>

                <!-- Address Field with Display/Change Pattern -->
                <div class="md:col-span-2">
                    <label class="label">
                        <span class="label-text">
                            <i class="fa-sharp fa-map-marker-alt text-primary mr-2"></i>
                            Address
                        </span>
                    </label>

                    <!-- Address Display (shown when address exists) -->
                    <div id="pickup_addressDisplay" class="hidden">
                        <div class="bg-base-200/50 border border-base-300 rounded-lg p-3">
                            <div class="flex items-start justify-between gap-3">
                                <div class="flex-1">
                                    <div class="text-sm text-base-content/70 mb-1">Current Address:</div>
                                    <div id="pickup_addressText" class="text-base-content"></div>
                                </div>
                                <button type="button" id="pickup_changeAddressBtn" class="btn btn-outline btn-sm gap-2">
                                    <i class="fa-sharp fa-edit"></i>
                                    Change
                                </button>
                            </div>
                        </div>
                        <!-- Hidden input for form submission -->
                        <input type="hidden" name="address" id="pickup_addressHidden">
                    </div>

                    <!-- Address Autocomplete (shown when no address or changing) -->
                    <div id="pickup_addressAutocomplete">
                        <x-google-maps-autocomplete
                            id="pickup_address"
                            name="address"
                            placeholder="Start typing to search for an address..."
                            type="textarea"
                            rows="2"
                            class="textarea-sm"
                            label=""
                            icon=""
                            :restrictions="['us', 'ca']"
                        />
                    </div>
                </div>
            </div>

            <!-- Notes Field -->
            <div>
                <label class="label" for="pickup_notes">
                    <span class="label-text">
                        <i class="fa-sharp fa-sticky-note text-primary mr-2"></i>
                        Additional Notes
                    </span>
                </label>
                <textarea name="notes" id="pickup_notes" rows="3" class="textarea textarea-bordered w-full"
                          placeholder="Enter any additional notes about this customer..."></textarea>
            </div>
        </form>

        <div class="flex flex-col sm:flex-row justify-end gap-3 mt-6">
            <button type="button" class="btn btn-outline btn-lg gap-2 flex-1 sm:flex-none" onclick="pickupQuickCreateCustomerModal.close()">
                <i class="fa-sharp fa-times"></i>
                Cancel
            </button>
            <button type="button" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none" id="pickupSaveCustomerBtn">
                <i class="fa-sharp fa-save"></i>
                Save Customer
            </button>
        </div>
    </div>
</dialog>
@endpush

<script>
// Global function to open the pickup quick create modal
window.openQuickCreateCustomer = function() {
    const modal = document.getElementById("pickupQuickCreateCustomerModal");
    
    @if($pickupRequest)
    // Pre-populate form with pickup request data
    populateFormFromPickupRequest();
    @endif
    
    modal.showModal();
};

document.addEventListener("DOMContentLoaded", function () {
    const modal = document.getElementById("pickupQuickCreateCustomerModal");
    const form = document.getElementById("pickupQuickCreateCustomerForm");
    const saveButton = document.getElementById("pickupSaveCustomerBtn");
    
    @if($pickupRequest)
    function populateFormFromPickupRequest() {
        // Pre-populate fields from pickup request
        const pickupData = @json($pickupRequest);

        // Determine if this looks like a business or individual
        const isBusinessLikely = pickupData.business_name && pickupData.business_name.trim() !== '';

        // Helper function to safely set element value
        function safeSetValue(elementId, value) {
            const element = document.getElementById(elementId);
            if (element) {
                element.value = value || '';
            } else {
                console.warn('Element not found:', elementId);
            }
        }

        // Helper function to safely set element checked state
        function safeSetChecked(selector, checked) {
            const element = document.querySelector(selector);
            if (element) {
                element.checked = checked;
            } else {
                console.warn('Element not found:', selector);
            }
        }

        if (isBusinessLikely) {
            safeSetValue('pickup_name', pickupData.business_name);
            safeSetValue('pickup_contact', pickupData.contact_name);
            safeSetChecked('input[name="type"][value="Business"]', true);
        } else {
            safeSetValue('pickup_name', pickupData.contact_name);
            safeSetChecked('input[name="type"][value="Residential Customer"]', true);
        }

        safeSetValue('pickup_email', pickupData.email);
        safeSetValue('pickup_phone', pickupData.phone);

        // Handle address field with display/change pattern
        handleAddressField(pickupData.pickup_address);

        // Add pickup request details to notes
        let notes = 'Created from pickup request #' + pickupData.id + '\n\n';
        if (pickupData.pickup_items) {
            notes += 'Pickup Items: ' + pickupData.pickup_items + '\n';
        }
        if (pickupData.pickup_quantity) {
            notes += 'Quantity: ' + pickupData.pickup_quantity + '\n';
        }
        if (pickupData.property_location_details) {
            notes += 'Property Details: ' + pickupData.property_location_details + '\n';
        }
        if (pickupData.other_notes) {
            notes += 'Additional Notes: ' + pickupData.other_notes + '\n';
        }

        safeSetValue('pickup_notes', notes);

        // Update field visibility based on type
        updateFieldsForType(isBusinessLikely ? 'Business' : 'Residential Customer');
    }
    
    // Make function available globally
    window.populateFormFromPickupRequest = populateFormFromPickupRequest;

    // Address field handling function
    function handleAddressField(address) {
        const addressDisplay = document.getElementById('pickup_addressDisplay');
        const addressAutocomplete = document.getElementById('pickup_addressAutocomplete');
        const addressText = document.getElementById('pickup_addressText');
        const addressHidden = document.getElementById('pickup_addressHidden');
        const changeAddressBtn = document.getElementById('pickup_changeAddressBtn');

        if (address && address.trim() !== '') {
            // Show address display, hide autocomplete
            addressText.textContent = address;
            addressHidden.value = address;
            addressDisplay.classList.remove('hidden');
            addressAutocomplete.classList.add('hidden');
            console.log('Showing address display with:', address);
        } else {
            // Show autocomplete, hide display
            addressDisplay.classList.add('hidden');
            addressAutocomplete.classList.remove('hidden');
            console.log('Showing address autocomplete field');
        }
    }

    // Make address handling function available globally
    window.handleAddressField = handleAddressField;
    @endif

    // Address field handling function (available even without pickup request)
    @if(!$pickupRequest)
    function handleAddressField(address) {
        const addressDisplay = document.getElementById('pickup_addressDisplay');
        const addressAutocomplete = document.getElementById('pickup_addressAutocomplete');
        const addressText = document.getElementById('pickup_addressText');
        const addressHidden = document.getElementById('pickup_addressHidden');

        if (address && address.trim() !== '') {
            // Show address display, hide autocomplete
            addressText.textContent = address;
            addressHidden.value = address;
            addressDisplay.classList.remove('hidden');
            addressAutocomplete.classList.add('hidden');
            console.log('Showing address display with:', address);
        } else {
            // Show autocomplete, hide display
            addressDisplay.classList.add('hidden');
            addressAutocomplete.classList.remove('hidden');
            console.log('Showing address autocomplete field');
        }
    }

    // Make address handling function available globally
    window.handleAddressField = handleAddressField;
    @endif
    
    // Customer type change handling
    const typeFields = document.querySelectorAll('input[name="type"]');
    typeFields.forEach((radio) => {
        radio.addEventListener('change', function() {
            updateFieldsForType(this.value);
        });
    });

    // Change address button handling
    const changeAddressBtn = document.getElementById('pickup_changeAddressBtn');
    if (changeAddressBtn) {
        changeAddressBtn.addEventListener('click', function() {
            const addressDisplay = document.getElementById('pickup_addressDisplay');
            const addressAutocomplete = document.getElementById('pickup_addressAutocomplete');

            // Hide display, show autocomplete
            addressDisplay.classList.add('hidden');
            addressAutocomplete.classList.remove('hidden');

            // Clear any existing value in the autocomplete field
            setTimeout(() => {
                const addressInput = document.querySelector('input[name="address"]') || document.querySelector('textarea[name="address"]');
                if (addressInput) {
                    addressInput.value = '';
                }

                // Clear autocomplete element if it exists
                const autocompleteElement = document.querySelector('gmp-place-autocomplete');
                if (autocompleteElement) {
                    autocompleteElement.value = '';
                    const innerInput = autocompleteElement.querySelector('input');
                    if (innerInput) {
                        innerInput.value = '';
                        innerInput.focus(); // Focus the input for user convenience
                    }
                }
            }, 100);

            console.log('Switched to address autocomplete mode');
        });
    }
    
    function updateFieldsForType(type) {
        const contactField = document.getElementById('pickup_contactField');
        const websiteField = document.getElementById('pickup_websiteField');
        const nameIcon = document.getElementById('pickup_nameIcon');
        const nameLabel = document.getElementById('pickup_nameLabel');
        
        if (type === 'Business') {
            contactField.classList.remove('hidden');
            websiteField.classList.remove('hidden');
            nameIcon.classList.remove('fa-user');
            nameIcon.classList.add('fa-building');
            nameLabel.textContent = 'Business Name';
        } else {
            contactField.classList.add('hidden');
            websiteField.classList.add('hidden');
            nameIcon.classList.remove('fa-building');
            nameIcon.classList.add('fa-user');
            nameLabel.textContent = 'Customer Name';
        }
    }
    
    // Save customer functionality
    saveButton.addEventListener("click", async function () {
        const formData = new FormData(form);
        
        // Disable button during save
        saveButton.disabled = true;
        saveButton.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Saving...';

        try {
            const response = await fetch("{{ route('customers.store') }}", {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content,
                    "Accept": "application/json",
                },
                body: formData,
            });

            if (response.ok) {
                const data = await response.json();

                if (data.success) {
                    const customer = data.customer;

                    // Close the modal first
                    modal.close();

                    // Automatically select the newly created customer using the selectCustomer function
                    if (typeof selectCustomer === 'function') {
                        selectCustomer(customer.id, customer.name);
                    }

                    // Show success message
                    const alert = document.createElement('div');
                    alert.className = 'alert alert-success fixed top-4 right-4 w-auto z-50 shadow-lg';
                    alert.innerHTML = `
                        <i class="fa-sharp fa-check-circle"></i>
                        <span>Customer created successfully and automatically selected!</span>
                    `;
                    document.body.appendChild(alert);

                    // Remove alert after 3 seconds
                    setTimeout(() => {
                        alert.remove();
                    }, 3000);

                } else {
                    alert("Failed to create customer. Please try again.");
                }
            } else {
                const errorData = await response.json();
                alert("Failed to create customer: " + (errorData.message || "An error occurred."));
            }
        } catch (error) {
            console.error("Error creating customer:", error);
            alert("An unexpected error occurred. Please try again.");
        } finally {
            // Re-enable button
            saveButton.disabled = false;
            saveButton.innerHTML = '<i class="fa-sharp fa-save"></i> Save Customer';
        }
    });
    
    // Clear form when modal is closed
    modal.addEventListener('close', function() {
        form.reset();
        // Reset to business type by default
        document.querySelector('input[name="type"][value="Business"]').checked = true;
        updateFieldsForType('Business');

        // Reset address field to autocomplete mode (no address)
        if (typeof handleAddressField === 'function') {
            handleAddressField('');
        } else if (typeof window.handleAddressField === 'function') {
            window.handleAddressField('');
        }
    });
});
</script>
