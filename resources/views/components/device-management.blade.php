<div>
    <!-- Add Device Section -->
    <div class="card bg-base-100 shadow-lg">
        <div class="card-body p-6">
            <div class="flex items-center mb-4">
                <i class="fa-sharp fa-hard-drive text-primary mr-2 text-xl"></i>
                <h3 class="card-title text-xl font-bold">Add Device</h3>
            </div>

            <div class="form-control w-full mb-4">
                <label class="label">
                    <span class="label-text font-medium">Serial Number</span>
                </label>
                <div class="flex">
                    <input type="text" id="serial_number" class="input input-bordered w-full"
                        placeholder="Scan device serial here" onkeypress="handleKeyPress(event)" tabindex="1">
                    <div class="tooltip" data-tip="Add device to list">
                        <button type="button" class="btn btn-primary ml-2 gap-2" onclick="addDevice()" tabindex="2">
                            <i class="fa-sharp fa-plus"></i>
                            Add Device
                        </button>
                    </div>
                </div>
            </div>

            <div class="bg-base-200 p-4 rounded-lg border border-base-300 mb-2">
                <h4 class="font-medium text-base-content mb-3">Device Details</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="form-control w-full">
                        <label class="label">
                            <span class="label-text font-medium">Device Type</span>
                            <div class="tooltip" data-tip="Lock/unlock device type">
                                <button type="button" class="btn btn-ghost btn-xs" onclick="toggleLock('device_type')" tabindex="-1">
                                    <i id="device_type_lock" class="fa-sharp fa-unlock text-green-500"></i>
                                </button>
                            </div>
                        </label>
                        <select id="device_type" class="w-full" tabindex="3">
                            <option value="">Select Device Type</option>
                            @foreach ($deviceTypes as $key => $value)
                                <option value="{{ $key }}">{{ $value }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-control w-full">
                        <label class="label">
                            <span class="label-text font-medium">Manufacturer</span>
                            <div class="tooltip" data-tip="Lock/unlock manufacturer">
                                <button type="button" class="btn btn-ghost btn-xs" onclick="toggleLock('manufacturer')" tabindex="-1">
                                    <i id="manufacturer_lock" class="fa-sharp fa-unlock text-green-500"></i>
                                </button>
                            </div>
                        </label>
                        <select id="manufacturer" class="w-full" placeholder="Manufacturer (Optional)" tabindex="4">
                            <option value="">Select or Type Manufacturer</option>
                            @foreach ($manufacturers as $manufacturer)
                                <option value="{{ $manufacturer }}">{{ $manufacturer }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-control w-full">
                        <label class="label">
                            <span class="label-text font-medium">Model</span>
                            <div class="tooltip" data-tip="Lock/unlock model">
                                <button type="button" class="btn btn-ghost btn-xs" onclick="toggleLock('model')" tabindex="-1">
                                    <i id="model_lock" class="fa-sharp fa-unlock text-green-500"></i>
                                </button>
                            </div>
                        </label>
                        <select id="model" class="w-full" placeholder="Model (Optional)" tabindex="5">
                            <option value="">Select or Type Model</option>
                            @foreach ($models as $model)
                                <option value="{{ $model }}">{{ $model }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>

            <div class="bg-base-200 p-4 rounded-lg border border-base-300 mb-2">
                <h4 class="font-medium text-base-content mb-3">Origin PC Details (Optional)</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="form-control w-full">
                        <label class="label">
                            <span class="label-text font-medium">PC Manufacturer</span>
                            <div class="tooltip" data-tip="Lock/unlock PC manufacturer">
                                <button type="button" class="btn btn-ghost btn-xs" onclick="toggleLock('pc_manufacturer')" tabindex="-1">
                                    <i id="pc_manufacturer_lock" class="fa-sharp fa-unlock text-green-500"></i>
                                </button>
                            </div>
                        </label>
                        <select id="pc_manufacturer" class="w-full" placeholder="PC Manufacturer (Optional)" tabindex="6">
                            <option value="">Select or Type PC Manufacturer</option>
                            @foreach ($manufacturers as $manufacturer)
                                <option value="{{ $manufacturer }}">{{ $manufacturer }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-control w-full">
                        <label class="label">
                            <span class="label-text font-medium">PC Serial / Asset Tag</span>
                            <div class="tooltip" data-tip="Lock/unlock PC Serial / Asset Tag">
                                <button type="button" class="btn btn-ghost btn-xs" onclick="toggleLock('pc_serial_number')" tabindex="-1">
                                    <i id="pc_serial_number_lock" class="fa-sharp fa-unlock text-green-500"></i>
                                </button>
                            </div>
                        </label>
                        <input type="text" id="pc_serial_number" class="input input-bordered w-full" placeholder="PC Serial or Asset Tag" tabindex="7">
                    </div>

                    <div class="form-control w-full">
                        <label class="label">
                            <span class="label-text font-medium">PC Form Factor</span>
                            <div class="tooltip" data-tip="Lock/unlock PC Form Factor">
                                <button type="button" class="btn btn-ghost btn-xs" onclick="toggleLock('pc_form_factor')" tabindex="-1">
                                    <i id="pc_form_factor_lock" class="fa-sharp fa-unlock text-green-500"></i>
                                </button>
                            </div>
                        </label>
                        <select id="pc_form_factor" class="select select-bordered w-full" tabindex="8">
                            <option value="">Select Form Factor</option>
                            <option value="Laptop">Laptop</option>
                            <option value="Desktop">Desktop</option>
                            <option value="Tablet">Tablet</option>
                            <option value="Micro Form Factor">Micro Form Factor</option>
                            <option value="Small Form Factor">Small Form Factor</option>
                            <option value="Server">Server</option>
                        </select>
                    </div>
                </div>
            </div>

            
        </div>
    </div>

    <!-- Attached Devices Section -->
    <div class="card bg-base-100 shadow-lg mt-6">
        <div class="card-body p-6">
            <div class="flex justify-between items-center mb-4">
                <div class="flex items-center">
                    <i class="fa-sharp fa-list-check text-primary mr-2 text-xl"></i>
                    <h3 class="card-title text-xl font-bold">Attached Devices</h3>
                </div>
                <div class="badge badge-lg badge-primary gap-2 p-3">
                    <i class="fa-sharp fa-barcode mr-1"></i>
                    <span id="scanned-count">{{ count($devices) }}</span> devices scanned of
                    <span id="total-count">{{ $certificate->stats['technician_total_drives_found'] ?? $certificate->stats['warehouse_device_count'] ?? '?' }}</span> Total
                </div>
            </div>

            <!-- Device List Container -->
            <div id="devices-container" class="mt-2">
                @php
                    // Sort devices by created_at in descending order (newest first)
                    $sortedDevices = $devices->sortByDesc('created_at');
                    $totalDevices = count($sortedDevices);
                @endphp
                
                @if(count($devices) == 0)
                    <div class="text-center py-8" id="no-devices-message">
                        <i class="fa-sharp fa-hard-drive text-base-content/50 text-4xl mb-3 block"></i>
                        <p class="text-base-content/70">No devices have been scanned yet. Use the form above to add devices.</p>
                    </div>
                @else

                    <div id="devices-list" class="space-y-4">
                        @foreach ($sortedDevices as $index => $device)
                            <div id="device-{{ $device->id }}" class="device-item bg-base-200 rounded-lg p-4 shadow-md hover:shadow-lg transition-shadow duration-300 {{ $device->is_destroyed ? 'opacity-50' : '' }}">
                                <div class="grid grid-cols-12 gap-4 items-center">
                                    <div class="col-span-12 md:col-span-1 text-center">
                                        <span class="device-number font-bold text-xl text-base-content/50">#{{ $totalDevices - $index }}</span>
                                    </div>

                                    <div class="col-span-12 md:col-span-5">
                                        <div class="flex items-center font-mono text-lg">
                                            @if($device->is_data_storage_device)
                                                <i class="fa-sharp fa-database text-primary mr-3" title="Data Storage Device"></i>
                                            @else
                                                <i class="fa-sharp fa-hard-drive text-base-content/30 mr-3" title="Non-Data Storage Device"></i>
                                            @endif
                                            <span class="break-all">{{ $device->serial_number }}</span>
                                        </div>
                                        <div class="text-sm text-base-content/70 ml-8">
                                            {{ \App\Models\Device::DEVICE_TYPES[$device->device_type] ?? ($device->device_type ?: 'N/A') }} | {{ $device->manufacturer ?: 'N/A' }} | {{ $device->model ?: 'N/A' }}
                                        </div>
                                    </div>

                                    <div class="col-span-12 md:col-span-4 text-sm">
                                        @if($device->pc_manufacturer || $device->pc_serial_number || $device->pc_form_factor)
                                            <div class="flex items-center">
                                                <i class="fa-sharp fa-computer text-base-content/50 mr-3"></i>
                                                <div>
                                                    <span class="font-bold">Origin PC:</span> {{ $device->pc_serial_number ?: 'N/A' }}
                                                    <div class="text-xs text-base-content/70">{{ $device->pc_manufacturer ?: 'N/A' }} | {{ $device->pc_form_factor ?: 'N/A' }}</div>
                                                </div>
                                            </div>
                                        @endif
                                    </div>

                                    <div class="col-span-12 md:col-span-2 flex items-center justify-end space-x-2">
                                        <div class="text-center">
                                            <div class="tooltip" id="tooltip-{{ $device->id }}" data-tip="{{ $device->is_destroyed && $device->destroyed_at && $device->destroyedByUser ? 'Destroyed by ' . $device->destroyedByUser->name . ' on ' . $device->destroyed_at->format('M j, Y g:i A') : ($device->is_destroyed ? 'Mark as not destroyed' : 'Mark as destroyed') }}">
                                                <input type="checkbox" class="checkbox checkbox-success" 
                                                    {{ $device->is_destroyed ? 'checked' : '' }} 
                                                    onchange="toggleDestruction({{ $device->id }}, this.checked)" />
                                            </div>
                                        </div>
                                        <div class="tooltip" data-tip="{{ !empty($device->notes) ? 'Edit notes' : 'Add notes' }}">
                                            <button class="btn {{ !empty($device->notes) ? 'btn-info' : 'btn-ghost' }} btn-circle"
                                                onclick="openNotesModal({{ $device->id }}, '{{ $device->serial_number }}', `{{ $device->notes }}`)">
                                                <i class="fa-sharp fa-sticky-note"></i>
                                            </button>
                                        </div>
                                        <div class="tooltip" data-tip="Delete device">
                                            <button class="btn btn-error btn-circle" onclick="openDeleteModal({{ $device->id }})">
                                                <i class="fa-sharp fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/tom-select@2.2.0/dist/js/tom-select.complete.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tom-select@2.2.0/dist/css/tom-select.css" rel="stylesheet">
    <script>
        // Use window.lockedFields to avoid duplicate declarations
        if (typeof window.lockedFields === 'undefined') {
            window.lockedFields = {
                device_type: false,
                manufacturer: false,
                model: false,
                pc_manufacturer: false,
                pc_serial_number: false,
                pc_form_factor: false
            };
        }

        function toggleLock(field) {
            window.lockedFields[field] = !window.lockedFields[field];
            const lockIcon = document.getElementById(field + '_lock');
            lockIcon.classList.toggle('fa-lock');
            lockIcon.classList.toggle('fa-unlock');
            lockIcon.classList.toggle('text-red-500');
            lockIcon.classList.toggle('text-green-500');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize device numbers
            updateDeviceNumbers();
            new TomSelect('#device_type', {
                create: false,
                sortField: {
                    field: 'text',
                    direction: 'asc'
                }
            });

            new TomSelect('#manufacturer', {
                create: true,
                sortField: {
                    field: 'text',
                    direction: 'asc'
                }
            });

            new TomSelect('#model', {
                create: true,
                sortField: {
                    field: 'text',
                    direction: 'asc'
                }
            });

            new TomSelect('#pc_manufacturer', {
                create: true,
                sortField: {
                    field: 'text',
                    direction: 'asc'
                }
            });

            new TomSelect('#pc_form_factor', {
                create: false,
                sortField: {
                    field: 'text',
                    direction: 'asc'
                }
            });

            // Listen for changes to the technician total drives found
            const techTotalDrivesFoundInput = document.getElementById('technician_total_drives_found');
            if (techTotalDrivesFoundInput) {
                techTotalDrivesFoundInput.addEventListener('change', function() {
                    const totalCountElement = document.getElementById('total-count');
                    if (totalCountElement) {
                        totalCountElement.textContent = this.value || '?';
                    }
                });

                // Also listen for input events for real-time updates
                techTotalDrivesFoundInput.addEventListener('input', function() {
                    const totalCountElement = document.getElementById('total-count');
                    if (totalCountElement) {
                        totalCountElement.textContent = this.value || '?';
                    }
                });
            }

            // Also listen for changes to the warehouse device count as a fallback
            const warehouseDeviceCountInput = document.getElementById('warehouse_device_count');
            if (warehouseDeviceCountInput && (!techTotalDrivesFoundInput || !techTotalDrivesFoundInput.value)) {
                warehouseDeviceCountInput.addEventListener('change', function() {
                    const totalCountElement = document.getElementById('total-count');
                    // Only update if technician value is not set
                    if (totalCountElement && (!techTotalDrivesFoundInput || !techTotalDrivesFoundInput.value)) {
                        totalCountElement.textContent = this.value || '?';
                    }
                });
            }
        });

        function handleKeyPress(event) {
            if (event.key === "Enter") {
                event.preventDefault();
                addDevice();
            }
        }

        function addDevice() {
            const serial = document.getElementById('serial_number').value.trim();
            const deviceType = document.getElementById('device_type').value.trim();
            const manufacturer = document.getElementById('manufacturer').value.trim();
            const model = document.getElementById('model').value.trim();
            const pcManufacturer = document.getElementById('pc_manufacturer').value.trim();
            const pcSerialNumber = document.getElementById('pc_serial_number').value.trim();
            const pcFormFactor = document.getElementById('pc_form_factor').value.trim();

            if (serial !== '') {
                const payload = {
                    certificate_id: {{ $certificate->id }},
                    serial_number: serial,
                    device_type: deviceType,
                    manufacturer: manufacturer,
                    model: model,
                    pc_manufacturer: pcManufacturer,
                    pc_serial_number: pcSerialNumber,
                    pc_form_factor: pcFormFactor
                };

                fetch('{{ route('devices.store') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify(payload)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const devicesContainer = document.getElementById('devices-container');
                        const noDevicesMessage = document.getElementById('no-devices-message');
                        const devicesList = document.getElementById('devices-list');

                        // Check if we need to replace the "no devices" message
                        if (document.getElementById('scanned-count').textContent === '0' && noDevicesMessage) {
                            noDevicesMessage.remove();
                            const listDiv = document.createElement('div');
                            listDiv.id = 'devices-list';
                            listDiv.className = 'space-y-4';
                            devicesContainer.appendChild(listDiv);
                        }

                        // Get device types mapping
                        const deviceTypes = {
                            'hdd': 'Hard Disk Drive',
                            'ssd': 'Solid State Drive',
                            'm2-ssd': 'M.2 SSD',
                            'nvme-ssd': 'NVMe SSD',
                            'sd-card': 'SD Card',
                            'eMMC': 'eMMC',
                            'soldered': 'Soldered Storage',
                            'entire-device': 'Entire Device',
                            'usb': 'USB Drive',
                            'tape': 'Tape Drive',
                            'optical': 'Optical Media',
                            'other': 'Other Storage Media',
                            'mSATA': 'mSATA SSD',
                            'pc-device': 'PC Device Serial Used',
                        };

                        // Create new device item
                        const deviceItem = document.createElement('div');
                        deviceItem.id = 'device-' + data.device.id;
                        deviceItem.className = 'device-item bg-base-200 rounded-lg p-4 shadow-md hover:shadow-lg transition-shadow duration-300';
                        
                        const device = data.device;

                        // Use server response if available, otherwise use form values.
                        const finalPcManufacturer = device.pc_manufacturer || pcManufacturer;
                        const finalPcSerialNumber = device.pc_serial_number || pcSerialNumber;
                        const finalPcFormFactor = device.pc_form_factor || pcFormFactor;

                        let originPcHtml = '';
                        if (finalPcManufacturer || finalPcSerialNumber || finalPcFormFactor) {
                            originPcHtml = `
                                <div class="flex items-center">
                                    <i class="fa-sharp fa-computer text-base-content/50 mr-3"></i>
                                    <div>
                                        <span class="font-bold">Origin PC:</span> ${finalPcSerialNumber || 'N/A'}
                                        <div class="text-xs text-base-content/70">${finalPcManufacturer || 'N/A'} | ${finalPcFormFactor || 'N/A'}</div>
                                    </div>
                                </div>
                            `;
                        }

                        deviceItem.innerHTML = `
                            <div class="grid grid-cols-12 gap-4 items-center">
                                <div class="col-span-12 md:col-span-1 text-center">
                                    <span class="device-number font-bold text-xl text-base-content/50"></span>
                                </div>
                                <div class="col-span-12 md:col-span-5">
                                    <div class="flex items-center font-mono text-lg">
                                        ${device.is_data_storage_device ? '<i class="fa-sharp fa-database text-primary mr-3" title="Data Storage Device"></i>' : '<i class="fa-sharp fa-hard-drive text-base-content/30 mr-3" title="Non-Data Storage Device"></i>'}
                                        <span class="break-all">${device.serial_number}</span>
                                    </div>
                                    <div class="text-sm text-base-content/70 ml-8">
                                        ${deviceTypes[device.device_type] || device.device_type || 'N/A'} | ${device.manufacturer || 'N/A'} | ${device.model || 'N/A'}
                                    </div>
                                </div>
                                <div class="col-span-12 md:col-span-4 text-sm">
                                    ${originPcHtml}
                                </div>
                                <div class="col-span-12 md:col-span-2 flex items-center justify-end space-x-2">
                                    <div class="text-center">
                                        <div class="tooltip" data-tip="Mark as destroyed">
                                            <input type="checkbox" class="checkbox checkbox-success" 
                                                onchange="toggleDestruction(${device.id}, this.checked)" />
                                        </div>
                                    </div>
                                    <div class="tooltip" data-tip="Add notes">
                                        <button class="btn btn-ghost btn-circle"
                                            onclick="openNotesModal(${device.id}, '${device.serial_number}', '')">
                                            <i class="fa-sharp fa-sticky-note"></i>
                                        </button>
                                    </div>
                                    <div class="tooltip" data-tip="Delete device">
                                        <button class="btn btn-error btn-circle" onclick="openDeleteModal(${device.id})">
                                            <i class="fa-sharp fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;

                        // Insert at the beginning of the devices list
                        const currentDevicesList = document.getElementById('devices-list');
                        if (currentDevicesList) {
                            currentDevicesList.insertBefore(deviceItem, currentDevicesList.firstChild);
                        }

                        // Update the scanned count badge
                        const scannedCountElement = document.getElementById('scanned-count');
                        const currentCount = parseInt(scannedCountElement.textContent);
                        scannedCountElement.textContent = currentCount + 1;

                        // Update all device numbers
                        updateDeviceNumbers();

                        // Clear form fields
                        document.getElementById('serial_number').value = '';
                        if (!window.lockedFields.device_type) {
                            const deviceTypeSelect = document.getElementById('device_type');
                            deviceTypeSelect.value = '';
                            if (deviceTypeSelect.tomselect) deviceTypeSelect.tomselect.clear();
                        }
                        if (!window.lockedFields.manufacturer) {
                            const manufacturerSelect = document.getElementById('manufacturer');
                            manufacturerSelect.value = '';
                            if (manufacturerSelect.tomselect) manufacturerSelect.tomselect.clear();
                        }
                        if (!window.lockedFields.model) {
                            const modelSelect = document.getElementById('model');
                            modelSelect.value = '';
                            if (modelSelect.tomselect) modelSelect.tomselect.clear();
                        }
                        if (!window.lockedFields.pc_manufacturer) {
                            const pcManufacturerSelect = document.getElementById('pc_manufacturer');
                            pcManufacturerSelect.value = '';
                            if (pcManufacturerSelect.tomselect) pcManufacturerSelect.tomselect.clear();
                        }
                        if (!window.lockedFields.pc_serial_number) {
                            document.getElementById('pc_serial_number').value = '';
                        }
                        if (!window.lockedFields.pc_serial_number) {
                            document.getElementById('pc_serial_number').value = '';
                        }
                        if (!window.lockedFields.pc_serial_number) {
                            document.getElementById('pc_serial_number').value = '';
                        }
                        if (!window.lockedFields.pc_form_factor) {
                            const pcFormFactorSelect = document.getElementById('pc_form_factor');
                            pcFormFactorSelect.value = '';
                            if (pcFormFactorSelect.tomselect) pcFormFactorSelect.tomselect.clear();
                        }
                    } else {
                        alert('Error: ' + data.message);
                    }
                });
            }
        }

        let deviceIdToDelete = null;

        function openDeleteModal(deviceId) {
            console.log('openDeleteModal called with deviceId:', deviceId);
            deviceIdToDelete = deviceId;
            const card = document.getElementById('device-' + deviceId);
            if (card) {
                card.classList.add('bg-error', 'text-white');
            } else {
                console.error('Device card not found:', 'device-' + deviceId);
            }

            const baseUrl = '{{ url('/') }}'; // Get the base URL of the application
            const url = `${baseUrl}/devices/${deviceId}`;
            console.log('Fetching device details from:', url);

            // Fetch device details
            fetch(url)
                .then(response => {
                    console.log('Device fetch response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(device => {
                    console.log('Device details received:', device);
                    let details = `<strong>Serial Number:</strong> ${device.serial_number || 'N/A'}<br>`;
                    details += `<strong>Device Type:</strong> ${device.device_type || 'N/A'}<br>`;
                    details += `<strong>Manufacturer:</strong> ${device.manufacturer || 'N/A'}<br>`;
                    details += `<strong>Model:</strong> ${device.model || 'N/A'}<br>`;
                    document.getElementById('deviceDetails').innerHTML = details;
                })
                .catch(error => {
                    console.error("Error fetching device details:", error);
                    document.getElementById('deviceDetails').innerHTML = "Error fetching device details: " + error.message;
                });

            const modal = document.getElementById('deviceDeleteModal');
            if (modal) {
                console.log('Opening delete modal');
                modal.showModal();
            } else {
                console.error('Device delete modal not found');
                alert('Error: Delete modal not found. Please refresh the page and try again.');
            }
        }

        function closeDeleteModal() {
            if (deviceIdToDelete) {
                const card = document.getElementById('device-' + deviceIdToDelete);
                if (card) {
                    card.classList.remove('bg-error', 'text-white');
                }
            }
            document.getElementById('deviceDeleteModal').close();
            deviceIdToDelete = null;
            document.getElementById('deviceDetails').innerHTML = "";
        }

        function confirmDeleteDevice() {
            console.log('confirmDeleteDevice called with deviceId:', deviceIdToDelete);
            const deviceId = deviceIdToDelete;
            const deviceItem = document.getElementById('device-' + deviceId);

            if (!deviceId) {
                console.error('No device ID to delete');
                alert('Error: No device selected for deletion.');
                return;
            }

            if (!deviceItem) {
                console.error('Device item not found:', 'device-' + deviceId);
                alert('Error: Device not found in the list.');
                return;
            }

            console.log('Sending DELETE request to:', `/devices/${deviceId}`);
            fetch(`/devices/${deviceId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                }
            })
            .then(response => {
                console.log('Delete response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Delete response data:', data);
                if (data.success) {
                    console.log('Device deleted successfully, removing from DOM');
                    deviceItem.remove();

                    // Update the scanned count badge
                    const scannedCountElement = document.getElementById('scanned-count');
                    const currentCount = parseInt(scannedCountElement.textContent);
                    scannedCountElement.textContent = Math.max(0, currentCount - 1);

                    // Update device numbers after deletion
                    updateDeviceNumbers();

                    // Check if we need to show the "no devices" message
                    const devicesList = document.getElementById('devices-list');
                    if (devicesList && devicesList.children.length === 0) {
                        const devicesContainer = document.getElementById('devices-container');
                        devicesContainer.innerHTML = `
                            <div class="text-center py-8" id="no-devices-message">
                                <i class="fa-sharp fa-hard-drive text-base-content/50 text-4xl mb-3 block"></i>
                                <p class="text-base-content/70">No devices have been scanned yet. Use the form above to add devices.</p>
                            </div>
                        `;
                    }
                } else {
                    console.error('Delete failed:', data.message || 'Unknown error');
                    alert('Error deleting device: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Delete request failed:', error);
                alert('Error deleting device: ' + error.message);
            })
            .finally(() => {
                const modal = document.getElementById('deviceDeleteModal');
                if (modal) {
                    modal.close();
                }
                deviceIdToDelete = null;
                document.getElementById('deviceDetails').innerHTML = "";
            });
        }

        function deleteDevice(deviceId) {
            openDeleteModal(deviceId);
        }

        // Function to toggle the destruction status of a device
        function toggleDestruction(deviceId, isDestroyed) {
            // Get the notes for this device
            const row = document.getElementById('device-' + deviceId);
            const notesTextarea = row.querySelector('textarea');
            const notes = notesTextarea ? notesTextarea.value : '';

            fetch(`/devices/${deviceId}/toggle-destruction`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    is_destroyed: isDestroyed,
                    notes: notes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the row styling
                    if (isDestroyed) {
                        row.classList.add('bg-success/10', 'text-base-content');

                        // Update the tooltip text if destruction info is available
                        if (data.device.destroyed_at && data.device.destroyed_by_name) {
                            const tooltipElement = document.getElementById(`tooltip-${deviceId}`);
                            if (tooltipElement) {
                                const formattedDate = new Date(data.device.destroyed_at).toLocaleString();
                                tooltipElement.setAttribute('data-tip', `Destroyed by ${data.device.destroyed_by_name} on ${formattedDate}`);
                            }

                            // Update the destroyed date cell (8th cell, index 7)
                            const cells = row.getElementsByTagName('td');
                            if (cells.length > 7) {
                                cells[7].innerHTML = `
                                    ${data.device.destroyed_at}
                                    <div class="text-xs text-base-content">by ${data.device.destroyed_by_name}</div>
                                `;
                            }
                        }
                    } else {
                        row.classList.remove('bg-success/10', 'text-base-content');

                        // Reset the tooltip text
                        const tooltipElement = document.getElementById(`tooltip-${deviceId}`);
                        if (tooltipElement) {
                            tooltipElement.setAttribute('data-tip', 'Mark as destroyed');
                        }

                        // Reset the destroyed date cell
                        const cells = row.getElementsByTagName('td');
                        if (cells.length > 7) {
                            cells[7].innerHTML = 'Not destroyed';
                        }
                    }
                } else {
                    alert('Error updating destruction status: ' + data.message);
                    // Revert the checkbox
                    const checkbox = row.querySelector('input[type="checkbox"]');
                    if (checkbox) {
                        checkbox.checked = !isDestroyed;
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the destruction status.');
                // Revert the checkbox
                const checkbox = row.querySelector('input[type="checkbox"]');
                if (checkbox) {
                    checkbox.checked = !isDestroyed;
                }
            });
        }

        // Variables for notes modal
        let currentDeviceId = null;
        let notesDebounceTimer = null;
        let lastSavedNotes = '';
        let isNotesSaving = false;
        let closeRequested = false;

        // Function to open the notes modal
        function openNotesModal(deviceId, serialNumber, notes) {
            currentDeviceId = deviceId;
            isNotesSaving = false;
            closeRequested = false;

            const modal = document.getElementById('deviceNotesModal');
            const closeBtn = document.getElementById('closeNotesModalBtn');
            const textarea = document.getElementById('deviceNotesTextarea');

            // Set the modal title
            document.getElementById('notesModalTitle').textContent = `Device Notes: ${serialNumber}`;

            // Reset state
            closeBtn.disabled = false;
            textarea.value = 'Loading notes...';
            textarea.disabled = true;
            updateSaveStatus('Loading...');

            // Add event listener for cleanup when modal is closed
            modal.addEventListener('close', handleCloseModal, { once: true });

            // Fetch the latest notes from the server
            fetch(`/devices/${deviceId}`, { method: 'GET', headers: { 'Accept': 'application/json' } })
                .then(response => response.json())
                .then(device => {
                    textarea.value = device.notes || '';
                    lastSavedNotes = device.notes || '';
                    textarea.disabled = false;
                    updateSaveStatus('Changes will be saved automatically');
                    textarea.addEventListener('input', debounceNotesUpdate);
                    setTimeout(() => textarea.focus(), 100);
                })
                .catch(error => {
                    console.error('Error fetching device notes:', error);
                    textarea.value = notes || '';
                    lastSavedNotes = notes || '';
                    textarea.disabled = false;
                    updateSaveStatus('Error loading notes. Changes will still be saved automatically.');
                    textarea.addEventListener('input', debounceNotesUpdate);
                });

            modal.showModal();
        }

        // This function is the single source of truth for closing the modal
        function requestCloseNotesModal() {
            if (isNotesSaving) {
                closeRequested = true;
                updateSaveStatus('Saving... will close when done.');
                document.getElementById('closeNotesModalBtn').disabled = true;
                return; // Don't close yet
            }
            document.getElementById('deviceNotesModal').close();
        }

        // Cleanup logic that runs after the modal has been closed
        function handleCloseModal() {
            const textarea = document.getElementById('deviceNotesTextarea');
            textarea.removeEventListener('input', debounceNotesUpdate);
            if (notesDebounceTimer) {
                clearTimeout(notesDebounceTimer);
            }
            currentDeviceId = null;
            isNotesSaving = false;
            closeRequested = false;
        }

        // Function to debounce notes updates
        function debounceNotesUpdate() {
            updateSaveStatus('Saving...');
            isNotesSaving = true;
            document.getElementById('closeNotesModalBtn').disabled = true;

            if (notesDebounceTimer) {
                clearTimeout(notesDebounceTimer);
            }

            notesDebounceTimer = setTimeout(() => {
                const notes = document.getElementById('deviceNotesTextarea').value;
                updateNotes(currentDeviceId, notes);
            }, 1500); // 1.5 second debounce
        }

        // Function to update the save status text
        function updateSaveStatus(message) {
            document.getElementById('notesSaveStatus').textContent = message;
        }

        // Function to update the notes for a device via fetch
        function updateNotes(deviceId, notes) {
            if (notes === lastSavedNotes) {
                updateSaveStatus('No changes to save.');
                isNotesSaving = false;
                document.getElementById('closeNotesModalBtn').disabled = false;
                if (closeRequested) {
                    requestCloseNotesModal();
                }
                return;
            }

            fetch(`/devices/${deviceId}/update-notes`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({ notes: notes })
            })
            .then(response => {
                if (!response.ok) {
                    // If we get a non-JSON error page, this will catch it
                    return response.text().then(text => { 
                        throw new Error(`Server returned ${response.status}: ${text.substring(0, 100)}...`); 
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    lastSavedNotes = notes;
                    updateSaveStatus('Saved!');
                    updateNotesButtonColor(deviceId, notes);
                } else {
                    updateSaveStatus(`Error: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error updating notes:', error);
                updateSaveStatus('Error saving notes.');
            })
            .finally(() => {
                isNotesSaving = false;
                document.getElementById('closeNotesModalBtn').disabled = false;
                if (closeRequested) {
                    requestCloseNotesModal();
                }
            });
        }

        // Function to update the notes button color
        function updateNotesButtonColor(deviceId, notes) {
            const row = document.getElementById('device-' + deviceId);
            if (!row) return;

            const notesButton = row.querySelector('div:nth-child(8) button');
            if (!notesButton) return;

            // Update tooltip text based on notes content
            const tooltipDiv = row.querySelector('div:nth-child(8) .tooltip');
            if (tooltipDiv) {
                tooltipDiv.setAttribute('data-tip', notes && notes.trim() !== '' ? 'Edit notes' : 'Add notes');
            }

            if (notes && notes.trim() !== '') {
                notesButton.classList.remove('btn-ghost');
                notesButton.classList.add('btn-info');
            } else {
                notesButton.classList.remove('btn-info');
                notesButton.classList.add('btn-ghost');
            }
        }

        // Function to update device numbers in the list layout
        function updateDeviceNumbers() {
            const devicesList = document.getElementById('devices-list');
            if (!devicesList) return;

            const deviceItems = devicesList.querySelectorAll('.device-item');
            const totalDevices = deviceItems.length;

            // Update device numbers (newest = highest number)
            deviceItems.forEach((item, index) => {
                const deviceNumber = totalDevices - index;
                const numberElement = item.querySelector('.device-number');
                if (numberElement) {
                    numberElement.textContent = `#${deviceNumber}`;
                }
            });
        }
    </script>
    @endpush

    @push('modals')
    <!-- Device Notes Modal -->
    <dialog id="deviceNotesModal" class="modal">
        <div class="modal-box">
            <div class="flex items-center mb-4">
                <i class="fa-sharp fa-sticky-note text-info mr-2 text-xl"></i>
                <h3 class="font-bold text-lg" id="notesModalTitle">Device Notes</h3>
            </div>
            <div class="form-control">
                <textarea id="deviceNotesTextarea" class="textarea textarea-bordered w-full h-32"
                    placeholder="Add notes about this device here..."></textarea>
                <div class="mt-2 text-sm text-gray-500" id="notesSaveStatus">Changes will be saved automatically</div>
            </div>
            <div class="modal-action">
                <button type="button" id="closeNotesModalBtn" class="btn btn-ghost" onclick="requestCloseNotesModal()">Close</button>
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button type="button" onclick="requestCloseNotesModal()">close</button>
        </form>
    </dialog>

    <!-- Device Delete Modal -->
    <dialog id="deviceDeleteModal" class="modal">
        <div class="modal-box">
            <div class="flex items-center mb-4">
                <i class="fa-sharp fa-trash text-error mr-2 text-xl"></i>
                <h3 class="font-bold text-lg">Confirm Device Deletion</h3>
            </div>
            <div class="bg-base-200 p-4 rounded-lg border border-base-300 mb-4" id="deviceDetails"></div>
            <div class="modal-action">
                <button type="button" class="btn btn-ghost" onclick="closeDeleteModal()">Cancel</button>
                <button type="button" class="btn btn-error gap-2" onclick="confirmDeleteDevice()">
                    <i class="fa-sharp fa-trash"></i>
                    Delete
                </button>
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button type="button" onclick="closeDeleteModal()">close</button>
        </form>
    </dialog>
    @endpush
</div>
