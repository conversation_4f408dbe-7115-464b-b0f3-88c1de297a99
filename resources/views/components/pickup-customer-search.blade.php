{{-- Enhanced Customer Search Component for Pickup Requests --}}
@props([
    'id' => 'pickupCustomerSearch',
    'name' => 'customer_id',
    'placeholder' => 'Search by business name, contact name, email, or phone...',
    'selectedId' => null,
    'selectedName' => null,
    'pickupRequest' => null
])

<div class="relative">
    <!-- Search Input -->
    <input 
        type="text" 
        id="{{ $id }}" 
        class="input input-bordered w-full pr-10 {{ $selectedId ? 'input-success' : '' }}"
        placeholder="{{ $placeholder }}"
        value="{{ $selectedName }}"
        autocomplete="off"
        {{ $selectedId ? 'readonly' : '' }}
    >
    
    <!-- Hidden Input for Customer ID -->
    <input type="hidden" name="{{ $name }}" value="{{ $selectedId }}">
    
    <!-- Search Icon or Selected Indicator -->
    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
        @if($selectedId)
            <i class="fa-sharp fa-check-circle text-success"></i>
        @else
            <i class="fa-sharp fa-magnifying-glass text-base-content/50"></i>
        @endif
    </div>
    
    <!-- Selected Customer Display -->
    @if($selectedId)
        <div class="mt-2 p-3 bg-success/10 border border-success/20 rounded-lg">
            <div class="flex items-center justify-between">
                <div class="text-sm">
                    <div class="font-medium text-success">Selected Customer</div>
                    <div class="text-base-content">{{ $selectedName }}</div>
                </div>
                <button type="button" onclick="clearSelectedCustomer()" class="btn btn-sm btn-outline btn-error">
                    <i class="fa-sharp fa-times"></i>
                    Change
                </button>
            </div>
        </div>
    @endif

    <!-- Enhanced Results Container -->
    <div id="{{ $id }}_resultsContainer" class="absolute w-full bg-base-100 shadow-xl border border-base-200 rounded-lg z-50 hidden max-h-80 overflow-y-auto transition-all duration-200 mt-1">
        <div id="{{ $id }}_results" class="p-2">
            <!-- Dynamically filled by JS -->
        </div>
        <div id="{{ $id }}_noResults" class="text-center p-6 text-base-content/60 hidden">
            <div class="space-y-3">
                <i class="fa-sharp fa-user-slash text-2xl text-base-content/30"></i>
                <p class="font-medium">No matching customers found</p>
                <p class="text-sm">Would you like to create a new customer?</p>
                <button type="button" class="btn btn-primary btn-sm gap-2" id="{{ $id }}_createNewBtn">
                    <i class="fa-sharp fa-user-plus"></i>
                    Create New Customer
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('{{ $id }}');
    const hiddenInput = document.querySelector('input[name="{{ $name }}"]');
    const resultsContainer = document.getElementById('{{ $id }}_resultsContainer');
    const resultsList = document.getElementById('{{ $id }}_results');
    const noResults = document.getElementById('{{ $id }}_noResults');
    const createNewBtn = document.getElementById('{{ $id }}_createNewBtn');
    
    let searchTimeout;
    let selectedResultIndex = -1;
    let resultElements = [];
    
    // Clear selected customer function
    window.clearSelectedCustomer = function() {
        searchInput.value = '';
        searchInput.readOnly = false;
        searchInput.classList.remove('input-success');
        hiddenInput.value = '';
        
        // Remove selected customer display
        const selectedDisplay = searchInput.parentNode.querySelector('.bg-success\\/10');
        if (selectedDisplay) {
            selectedDisplay.remove();
        }
        
        // Update search icon
        const icon = searchInput.parentNode.querySelector('.fa-check-circle, .fa-magnifying-glass');
        if (icon) {
            icon.className = 'fa-sharp fa-magnifying-glass text-base-content/50';
        }
        
        searchInput.focus();
    };
    
    // Set selected customer function
    function setSelectedCustomer(customer) {
        searchInput.value = customer.name;
        searchInput.readOnly = true;
        searchInput.classList.add('input-success');
        hiddenInput.value = customer.id;
        
        // Update icon
        const icon = searchInput.parentNode.querySelector('.fa-magnifying-glass, .fa-check-circle');
        if (icon) {
            icon.className = 'fa-sharp fa-check-circle text-success';
        }
        
        // Create selected customer display
        const selectedDisplay = document.createElement('div');
        selectedDisplay.className = 'mt-2 p-3 bg-success/10 border border-success/20 rounded-lg';
        selectedDisplay.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="text-sm">
                    <div class="font-medium text-success">Selected Customer</div>
                    <div class="text-base-content">${customer.name}</div>
                </div>
                <button type="button" onclick="clearSelectedCustomer()" class="btn btn-sm btn-outline btn-error">
                    <i class="fa-sharp fa-times"></i>
                    Change
                </button>
            </div>
        `;
        
        // Insert after the input container
        searchInput.parentNode.appendChild(selectedDisplay);
        hideResultsContainer();
    }
    
    function showResultsContainer() {
        resultsContainer.classList.remove('hidden');
    }
    
    function hideResultsContainer() {
        resultsContainer.classList.add('hidden');
        selectedResultIndex = -1;
        resultElements = [];
    }
    
    // Search functionality
    searchInput.addEventListener('input', function() {
        if (searchInput.readOnly) return;
        
        const query = searchInput.value.trim();
        
        clearTimeout(searchTimeout);
        
        if (query.length === 0) {
            hideResultsContainer();
            return;
        }
        
        searchTimeout = setTimeout(() => {
            fetch(`/customersearch/${encodeURIComponent(query)}?pickup_request=true`)
                .then(response => response.json())
                .then(data => {
                    resultsList.innerHTML = '';
                    resultElements = [];
                    
                    if (data.length > 0) {
                        noResults.classList.add('hidden');
                        showResultsContainer();
                        
                        data.forEach((customer, index) => {
                            const resultItem = createCustomerResultItem(customer, index);
                            resultsList.appendChild(resultItem);
                            resultElements.push(resultItem);
                        });
                    } else {
                        noResults.classList.remove('hidden');
                        showResultsContainer();
                        resultElements.push(createNewBtn);
                    }
                })
                .catch(error => {
                    console.error('Error fetching customers:', error);
                    hideResultsContainer();
                });
        }, 300);
    });
    
    function createCustomerResultItem(customer, index) {
        const item = document.createElement('div');
        item.className = 'p-3 hover:bg-base-200 cursor-pointer rounded-lg border border-transparent hover:border-primary/20 transition-all duration-200';
        item.setAttribute('data-customer-id', customer.id);
        
        // Format address for display
        const addressPreview = customer.address 
            ? (customer.address.length > 50 ? customer.address.substring(0, 50) + '...' : customer.address)
            : 'No address on file';
            
        // Determine display name and contact
        const displayName = customer.name || 'Unnamed Customer';
        const contactInfo = customer.contact ? `Contact: ${customer.contact}` : '';
        const typeInfo = customer.type ? `(${customer.type})` : '';
        
        item.innerHTML = `
            <div class="space-y-2">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="font-semibold text-base-content">${displayName} ${typeInfo}</div>
                        ${contactInfo ? `<div class="text-sm text-base-content/70">${contactInfo}</div>` : ''}
                    </div>
                    <div class="text-xs text-primary font-medium">Match ${index + 1}</div>
                </div>
                <div class="grid grid-cols-1 gap-1 text-sm text-base-content/60">
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-envelope text-xs"></i>
                        <span>${customer.email || 'No email on file'}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-phone text-xs"></i>
                        <span>${customer.phone || 'No phone on file'}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-map-marker-alt text-xs"></i>
                        <span>${addressPreview}</span>
                    </div>
                </div>
            </div>
        `;
        
        item.addEventListener('click', () => {
            setSelectedCustomer(customer);

            // Scroll to the link button after selection
            setTimeout(() => {
                const linkBtn = document.getElementById('linkCustomerBtn');
                if (linkBtn) {
                    linkBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    // Add a subtle highlight effect
                    linkBtn.classList.add('animate-pulse');
                    setTimeout(() => {
                        linkBtn.classList.remove('animate-pulse');
                    }, 2000);
                }
            }, 100);
        });
        
        return item;
    }
    
    // Create new customer button functionality
    if (createNewBtn) {
        createNewBtn.addEventListener('click', function() {
            hideResultsContainer();
            // Trigger the quick create customer modal with pre-populated data
            if (typeof openQuickCreateCustomer === 'function') {
                openQuickCreateCustomer();
            }
        });
    }
    
    // Close results when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !resultsContainer.contains(e.target)) {
            hideResultsContainer();
        }
    });
    
    // Keyboard navigation
    searchInput.addEventListener('keydown', function(e) {
        if (resultsContainer.classList.contains('hidden') || resultElements.length === 0) return;
        
        switch(e.key) {
            case 'ArrowDown':
                e.preventDefault();
                selectedResultIndex = Math.min(selectedResultIndex + 1, resultElements.length - 1);
                highlightResult();
                break;
            case 'ArrowUp':
                e.preventDefault();
                selectedResultIndex = Math.max(selectedResultIndex - 1, -1);
                highlightResult();
                break;
            case 'Enter':
                e.preventDefault();
                if (selectedResultIndex >= 0 && resultElements[selectedResultIndex]) {
                    resultElements[selectedResultIndex].click();
                }
                break;
            case 'Escape':
                hideResultsContainer();
                break;
        }
    });
    
    function highlightResult() {
        resultElements.forEach((el, index) => {
            if (index === selectedResultIndex) {
                el.classList.add('bg-primary/10', 'border-primary/30');
            } else {
                el.classList.remove('bg-primary/10', 'border-primary/30');
            }
        });
    }
});
</script>
