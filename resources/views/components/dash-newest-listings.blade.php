<div class="grid grid-cols-2 gap-2">
    @forelse ($newestListings as $listing)
        <div class="relative bg-base-200 rounded-lg overflow-hidden hover:scale-105 transition-transform duration-300 border border-base-300">
            <a href="{{ route('inventory.show', $listing->id) }}">
                <img src="{{ $listing->images->isNotEmpty() && $listing->images->first()->image ? $listing->images->first()->image->getImageSrc('sm') : asset('img/placeholder.jpg') }}"
                    class="w-full h-56 object-cover">
                <div class="absolute bottom-0 left-0 right-0 bg-neutral bg-opacity-50 p-2">
                    <h3 class="text-sm font-medium text-neutral-content truncate">
                        {{ $listing->name }}
                    </h3>
                </div>
                <div class="absolute top-2 left-2">
                    <span class="badge badge-primary">
                        $ {{ $listing->suggested_price ?: 'N/A' }}
                    </span>
                </div>
                <div class="absolute top-2 right-2">
                    <span class="badge badge-secondary">
                        {{ $listing->location }}
                    </span>
                </div>
            </a>
        </div>
    @empty
        <div class="col-span-2 text-center text-base-content/60">No listings available.</div>
    @endforelse
</div>
