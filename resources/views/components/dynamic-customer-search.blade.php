<div class="form-control w-full mt-4 relative">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Search State (default) -->
    <div id="{{ $id }}_searchState">
        <input type="text" id="{{ $id }}"
               class="input input-bordered w-full mb-4 focus:ring-2 focus:ring-primary focus:border-primary"
               placeholder="{{ $placeholder }}"
               value="{{ $selectedName ?? '' }}">
    </div>

    <!-- Selected State (hidden by default) -->
    <div id="{{ $id }}_selectedState" class="hidden">
        <label class="input input-bordered w-full mb-4 bg-base-200 text-base-content/70">
            <span class="label-text font-medium text-primary">Selected Customer:</span>
            <input type="text" id="{{ $id }}_selectedDisplay"
                   class="bg-transparent border-none outline-none cursor-not-allowed"
                   readonly disabled>
            <button type="button" id="{{ $id }}_deselectBtn"
                    class="btn btn-ghost btn-xs text-error hover:bg-error hover:text-error-content"
                    title="Change customer selection">
                <i class="fa-sharp fa-times"></i>
            </button>
        </label>
    </div>

    <!-- Floating Results Window -->
    <div id="{{ $id }}_resultsContainer" class="absolute w-full bg-base-100 shadow-lg border rounded-lg z-50 hidden max-h-60 overflow-y-auto transition-all duration-200">
        <ul id="{{ $id }}_results" class="list-none p-4 text-sm text-base-content">
            <!-- Dynamically filled by JS -->
        </ul>
        <div id="{{ $id }}_noResults" class="text-center p-4 text-base-content/60 hidden">
            <p>
                No customers found...
                @if($quickCreateButtonId)
                    <button type="button" class="link link-primary" id="{{ $id }}_createNewBtn">Create new?</button>
                @else
                    <a href="{{ route('customers.create') }}" class="link link-primary" id="{{ $id }}_createNewLink">Create new?</a>
                @endif
            </p>
        </div>
    </div>

    <!-- Hidden Input for Selected Customer ID -->
    <input type="hidden" name="{{ $name }}" value="{{ $selectedId ?? '' }}">
</div>

<script>
document.addEventListener("DOMContentLoaded", function () {
    const searchInput = document.getElementById("{{ $id }}");
    const resultsContainer = document.getElementById("{{ $id }}_resultsContainer");
    const resultsList = document.getElementById("{{ $id }}_results");
    const noResults = document.getElementById("{{ $id }}_noResults");
    const searchState = document.getElementById("{{ $id }}_searchState");
    const selectedState = document.getElementById("{{ $id }}_selectedState");
    const selectedDisplay = document.getElementById("{{ $id }}_selectedDisplay");
    const deselectBtn = document.getElementById("{{ $id }}_deselectBtn");
    const hiddenInput = document.querySelector(`input[name="{{ $name }}"]`);
    const action = "{{ $action }}"; // Pass the action (e.g., "navigate" or "form")

    let currentCustomer = null;
    let lastSearchText = "";
    let selectedResultIndex = -1; // Track which result is currently selected via keyboard
    let resultElements = []; // Cache of result elements for keyboard navigation

    // Function to position results container based on available space
    function positionResultsContainer() {
        if (resultsContainer.classList.contains('hidden')) return;
        
        const inputRect = searchInput.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const containerHeight = Math.min(240, resultsContainer.scrollHeight); // max-h-60 = 240px
        
        // Calculate space above and below the input
        const spaceBelow = viewportHeight - inputRect.bottom;
        const spaceAbove = inputRect.top;
        
        // Check if we're likely on mobile (viewport height significantly reduced)
        const isMobileKeyboard = viewportHeight < 500;
        
        // Position above if there's not enough space below or if mobile keyboard is active
        if (spaceBelow < containerHeight + 20 && (spaceAbove > containerHeight + 20 || isMobileKeyboard)) {
            // Position above the input
            resultsContainer.style.top = 'auto';
            resultsContainer.style.bottom = `${inputRect.height + 8}px`;
            resultsContainer.style.maxHeight = `${Math.min(240, spaceAbove - 20)}px`;
        } else {
            // Position below the input (default)
            resultsContainer.style.top = `${inputRect.height + 8}px`;
            resultsContainer.style.bottom = 'auto';
            resultsContainer.style.maxHeight = `${Math.min(240, spaceBelow - 20)}px`;
        }
    }

    // Function to show results container with proper positioning
    function showResultsContainer() {
        resultsContainer.classList.remove("hidden");
        // Use setTimeout to ensure DOM has updated before positioning
        setTimeout(() => {
            positionResultsContainer();
        }, 0);
    }

    // Function to hide results container
    function hideResultsContainer() {
        resultsContainer.classList.add("hidden");
        selectedResultIndex = -1;
        resultElements = [];
        clearResultHighlight();
    }

    // Function to highlight a specific result
    function highlightResult(index) {
        clearResultHighlight();
        if (index >= 0 && index < resultElements.length) {
            selectedResultIndex = index;
            const element = resultElements[index];
            element.classList.add("bg-primary", "text-primary-content");
            // Add focus ring for accessibility
            element.style.outline = "2px solid";
            element.style.outlineColor = "#3b82f6";
            element.style.outlineOffset = "1px";
            // Scroll the highlighted result into view
            element.scrollIntoView({ block: 'nearest' });
        }
    }

    // Function to clear result highlighting
    function clearResultHighlight() {
        resultElements.forEach(element => {
            element.classList.remove("bg-primary", "text-primary-content");
            element.style.outline = "";
            element.style.outlineColor = "";
            element.style.outlineOffset = "";
        });
    }

    // Function to navigate through results with keyboard
    function navigateResults(direction) {
        if (resultElements.length === 0) return;
        
        let newIndex = selectedResultIndex + direction;
        
        // Wrap around navigation
        if (newIndex >= resultElements.length) {
            newIndex = 0;
        } else if (newIndex < 0) {
            newIndex = resultElements.length - 1;
        }
        
        highlightResult(newIndex);
    }

    // Function to select the currently highlighted result
    function selectHighlightedResult() {
        if (selectedResultIndex >= 0 && selectedResultIndex < resultElements.length) {
            resultElements[selectedResultIndex].click();
            return true;
        }
        return false;
    }

    // Function to handle Enter key - create new customer or select highlighted result
    function handleEnterKey() {
        // If a result is highlighted, select it
        if (selectHighlightedResult()) {
            return;
        }
        
        // If no result is highlighted but we have search text, trigger create new customer
        const query = searchInput.value.trim();
        if (query.length > 0) {
            storeSearchTextForQuickCreate();
            
            // Trigger the appropriate create new action
            @if($quickCreateButtonId)
                const quickCreateBtn = document.getElementById('{{ $quickCreateButtonId }}');
                if (quickCreateBtn) {
                    quickCreateBtn.click();
                }
            @else
                // Navigate to create new customer page
                window.location.href = "{{ route('customers.create') }}";
            @endif
        }
    }

    // Function to store search text for quick create
    function storeSearchTextForQuickCreate() {
        lastSearchText = searchInput.value.trim();
        // Store globally for standalone quick-create page
        window.lastSearchText = lastSearchText;

        // Also populate the modal form if it exists
        const modalNameField = document.querySelector('#quickCreateCustomerForm #name');
        if (modalNameField) {
            modalNameField.value = lastSearchText;
        }
    }

    // Function to set selected customer and switch to selected state
    function setSelectedCustomer(customer) {
        currentCustomer = customer;
        selectedDisplay.value = customer.name;
        hiddenInput.value = customer.id;

        // Switch to selected state
        searchState.classList.add("hidden");
        selectedState.classList.remove("hidden");
        hideResultsContainer();

        // Trigger customer selection event for payout page
        if (typeof window.handleCustomerSelection === 'function') {
            window.handleCustomerSelection(customer);
        }

        // Dispatch customerSelected event for pickup request page
        const customerSelectedEvent = new CustomEvent('customerSelected', {
            detail: {
                customerId: customer.id,
                customer: customer
            }
        });
        document.dispatchEvent(customerSelectedEvent);
    }

    // Function to clear selection and return to search state
    function clearSelection() {
        currentCustomer = null;
        searchInput.value = "";
        selectedDisplay.value = "";
        hiddenInput.value = "";

        // Switch to search state
        selectedState.classList.add("hidden");
        searchState.classList.remove("hidden");
        hideResultsContainer();

        // Dispatch customerDeselected event for pickup request page
        const customerDeselectedEvent = new CustomEvent('customerDeselected', {
            detail: {
                customerId: null,
                customer: null
            }
        });
        document.dispatchEvent(customerDeselectedEvent);

        // Focus the search input
        searchInput.focus();
    }

    // Check if we have a pre-selected customer on load
    @if($selectedId && $selectedName)
        setSelectedCustomer({
            id: {{ $selectedId }},
            name: "{{ addslashes($selectedName) }}"
        });
    @endif

    // Deselect button event listener
    deselectBtn.addEventListener("click", clearSelection);

    // Make setSelectedCustomer globally accessible for quick-create component
    window["setSelectedCustomer_{{ $id }}"] = setSelectedCustomer;

    // Add event listeners for create new buttons
    @if($quickCreateButtonId)
        const createNewBtn = document.getElementById("{{ $id }}_createNewBtn");
        if (createNewBtn) {
            createNewBtn.addEventListener("click", function() {
                storeSearchTextForQuickCreate();
                document.getElementById('{{ $quickCreateButtonId }}').click();
            });
        }
    @else
        const createNewLink = document.getElementById("{{ $id }}_createNewLink");
        if (createNewLink) {
            createNewLink.addEventListener("click", function() {
                storeSearchTextForQuickCreate();
                // The link will navigate naturally
            });
        }
    @endif

    searchInput.addEventListener("input", function () {
        const query = searchInput.value.trim();

        if (query.length === 0) {
            hideResultsContainer();
            resultsList.innerHTML = "";
            noResults.classList.add("hidden");
            return;
        }

        fetch(`/customersearch/${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                resultsList.innerHTML = "";
                resultElements = []; // Reset result elements cache

                if (data.length > 0) {
                    noResults.classList.add("hidden");
                    showResultsContainer();

                    data.forEach(customer => {
                        const listItem = document.createElement("li");
                        listItem.className = "py-2 px-4 hover:bg-base-200 cursor-pointer rounded";
                        listItem.setAttribute("data-customer-id", customer.id);

                        // Create the contract star HTML
                        const contractStar = customer.hasActiveContract
                            ? `<span class="text-warning" title="Customer has an active contract"><i class="fa-sharp fa-solid fa-star"></i></span>`
                            : `<span class="text-base-300" title="No active contract"><i class="fa-sharp fa-regular fa-star"></i></span>`;

                        // Create the license status HTML
                        const licenseStatus = customer.hasLicenseOnFile
                            ? `<span class="text-success" title="Customer has photo ID on file"><i class="fa-sharp fa-solid fa-id-card"></i></span>`
                            : `<span class="text-base-300" title="No photo ID on file"><i class="fa-sharp fa-regular fa-id-card"></i></span>`;

                        listItem.innerHTML = `
                            <div class="flex justify-between items-start">
                                <div>
                                    <strong>${customer.name}</strong> (${customer.email || "No email"})<br>
                                    ${customer.phone ? `<span class="text-base-content/70">Phone: ${customer.phone}</span><br>` : ""}
                                    ${customer.nickname ? `<span class="text-base-content/60">Nickname: ${customer.nickname}</span>` : ""}
                                </div>
                                <div class="flex gap-1">
                                    ${contractStar}
                                    ${licenseStatus}
                                </div>
                            </div>
                        `;

                        listItem.addEventListener("click", () => {
                            if (action === "navigate") {
                                // Navigate to customer view route
                                window.location.href = `/customers/${customer.id}`;
                            } else if (action === "form") {
                                // Use the new setSelectedCustomer function
                                setSelectedCustomer(customer);
                            }
                        });

                        // Add mouse hover support that works with keyboard navigation
                        listItem.addEventListener("mouseenter", () => {
                            const index = resultElements.indexOf(listItem);
                            if (index !== -1) {
                                highlightResult(index);
                            }
                        });

                        // Clear highlight when mouse leaves, but not if we're using keyboard
                        listItem.addEventListener("mouseleave", () => {
                            // Only clear if no keyboard navigation is active
                            setTimeout(() => {
                                if (document.activeElement === searchInput) {
                                    clearResultHighlight();
                                    selectedResultIndex = -1;
                                }
                            }, 50);
                        });

                        resultsList.appendChild(listItem);
                        resultElements.push(listItem); // Add to cache for keyboard navigation
                    });
                } else {
                    noResults.classList.remove("hidden");
                    showResultsContainer();
                    
                    // Add the "Create new?" button to keyboard navigation if it exists
                    const createNewBtn = document.getElementById("{{ $id }}_createNewBtn");
                    const createNewLink = document.getElementById("{{ $id }}_createNewLink");
                    if (createNewBtn) {
                        resultElements.push(createNewBtn);
                    } else if (createNewLink) {
                        resultElements.push(createNewLink);
                    }
                }
            })
            .catch(error => console.error("Error fetching customers:", error));
    });

    // Add keyboard navigation support
    searchInput.addEventListener("keydown", function (e) {
        switch (e.key) {
            case 'Tab':
                // Tab to first result if results are visible and no result is selected
                if (!resultsContainer.classList.contains('hidden') && resultElements.length > 0) {
                    e.preventDefault();
                    if (selectedResultIndex === -1) {
                        highlightResult(0);
                    } else {
                        navigateResults(e.shiftKey ? -1 : 1);
                    }
                }
                break;
                
            case 'ArrowDown':
                // Navigate down through results
                if (!resultsContainer.classList.contains('hidden') && resultElements.length > 0) {
                    e.preventDefault();
                    if (selectedResultIndex === -1) {
                        highlightResult(0);
                    } else {
                        navigateResults(1);
                    }
                }
                break;
                
            case 'ArrowUp':
                // Navigate up through results
                if (!resultsContainer.classList.contains('hidden') && resultElements.length > 0) {
                    e.preventDefault();
                    navigateResults(-1);
                }
                break;
                
            case 'Enter':
                // Select highlighted result or create new customer
                if (!resultsContainer.classList.contains('hidden')) {
                    e.preventDefault();
                    handleEnterKey();
                }
                break;
                
            case 'Escape':
                // Close results
                if (!resultsContainer.classList.contains('hidden')) {
                    e.preventDefault();
                    hideResultsContainer();
                }
                break;
        }
    });

    document.addEventListener("click", (e) => {
        if (!resultsContainer.contains(e.target) && e.target !== searchInput) {
            hideResultsContainer();
        }
    });

    // Listen for viewport changes (mobile keyboard show/hide)
    window.addEventListener('resize', () => {
        positionResultsContainer();
    });

    // Listen for input focus to reposition if needed
    searchInput.addEventListener('focus', () => {
        if (!resultsContainer.classList.contains('hidden')) {
            setTimeout(() => {
                positionResultsContainer();
            }, 300); // Delay for keyboard animation
        }
    });

    // Add mobile-specific input handling
    searchInput.addEventListener('touchstart', () => {
        // On mobile, ensure proper positioning when user starts typing
        setTimeout(() => {
            positionResultsContainer();
        }, 100);
    });

    // Handle input blur to maintain visibility
    searchInput.addEventListener('blur', (e) => {
        // Don't hide results if user clicked on a result item
        if (!resultsContainer.contains(e.relatedTarget)) {
            setTimeout(() => {
                hideResultsContainer();
            }, 150); // Small delay to allow for result selection
        }
    });

    // Listen for scroll events that might affect positioning
    window.addEventListener('scroll', () => {
        positionResultsContainer();
    }, { passive: true });
});
</script>
