<div>
    <!-- <PERSON><PERSON> to Open Modal -->
    <button type="button" class="btn btn-primary btn-sm gap-2" id="quickCreateCustomerBtn">
        <i class="fa-sharp fa-user-plus"></i>
        Create New Customer
    </button>
</div>

@push('modals')
<dialog id="quickCreateCustomerModal" class="modal">
    <div class="modal-box max-w-4xl max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center gap-3">
                <div class="avatar avatar-placeholder">
                    <div class="bg-primary text-primary-content w-10 rounded-lg">
                        <i class="fa-sharp fa-user-plus text-lg"></i>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-base-content">Create New Customer</h3>
                    <p class="text-sm text-base-content/70">Add a new customer to the system</p>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-circle btn-ghost" id="quickCreateCustomerCloseBtn">
                <i class="fa-sharp fa-times"></i>
            </button>
        </div>

        <!-- Form Content -->
        <form id="quickCreateCustomerForm" onsubmit="return false;" class="space-y-4">
            @csrf
            <!-- Include the partial customer form -->
            @include('customers.partials.form')
        </form>


        <div class="flex flex-col sm:flex-row justify-end gap-3">
            <button type="button" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none" id="saveCustomerBtn">
                <i class="fa-sharp fa-save"></i>
                Save Customer
            </button>
        </div>
    </div>
</dialog>

<script>
document.addEventListener("DOMContentLoaded", function () {
    const modal = document.getElementById("quickCreateCustomerModal");
    const form = document.getElementById("quickCreateCustomerForm");
    const saveButton = document.getElementById("saveCustomerBtn");
    const nameField = document.querySelector("#{{ $id }}"); // The visible search input with ID "customerSearch"
    const idField = document.querySelector("input[name='{{ $name }}']"); // The hidden input with name "customer_id"
    const modalNameField = document.querySelector('#quickCreateCustomerForm #name'); // The name field in the modal form

    // Track modal and form state for navigation prevention
    let isModalOpen = false;
    let hasUnsavedChanges = false;
    let originalFormData = '';

    // Function to check if form has changes
    function checkForChanges() {
        if (!isModalOpen) return false;

        const currentFormData = new FormData(form);
        const currentDataString = Array.from(currentFormData.entries())
            .map(([key, value]) => `${key}=${value}`)
            .sort()
            .join('&');

        hasUnsavedChanges = currentDataString !== originalFormData && currentDataString !== '';
        return hasUnsavedChanges;
    }

    // Function to capture initial form state
    function captureInitialFormState() {
        const formData = new FormData(form);
        originalFormData = Array.from(formData.entries())
            .map(([key, value]) => `${key}=${value}`)
            .sort()
            .join('&');
    }

    // Navigation prevention functions
    function preventNavigation(event) {
        if (isModalOpen && checkForChanges()) {
            event.preventDefault();
            event.returnValue = 'You have unsaved changes in the customer creation form. Are you sure you want to leave?';
            return event.returnValue;
        }
    }

    function handlePopState(event) {
        if (isModalOpen && checkForChanges()) {
            const confirmLeave = confirm('You have unsaved changes in the customer creation form. Are you sure you want to leave without saving?');
            if (!confirmLeave) {
                // Push the current state back to prevent navigation
                history.pushState(null, null, window.location.href);
                return;
            }
        }
        // If user confirms or no changes, allow navigation by closing modal
        if (isModalOpen) {
            modal.close();
        }
    }

    // Listen for modal opening to populate the name field
    const quickCreateBtn = document.getElementById("quickCreateCustomerBtn");
    if (quickCreateBtn && modalNameField) {
        quickCreateBtn.addEventListener("click", function() {
            // Capture current search text from the associated search input
            const searchInput = document.querySelector("#{{ $id }}");
            if (searchInput && searchInput.value.trim()) {
                window.lastSearchText = searchInput.value.trim();
                modalNameField.value = window.lastSearchText;
            } else if (window.lastSearchText) {
                // Use previously stored search text if available
                modalNameField.value = window.lastSearchText;
            }

            // Set modal state and capture initial form data
            isModalOpen = true;
            hasUnsavedChanges = false;

            // Open the modal
            modal.showModal();

            // Capture initial state after modal opens and form is populated
            setTimeout(() => {
                captureInitialFormState();
            }, 100);

            // Add navigation prevention
            window.addEventListener('beforeunload', preventNavigation);
            window.addEventListener('popstate', handlePopState);

            // Push a new state to handle back button
            history.pushState(null, null, window.location.href);
        });
    }

    // Monitor form changes
    if (form) {
        form.addEventListener('input', checkForChanges);
        form.addEventListener('change', checkForChanges);
    }

    // Handle close button with unsaved changes check
    const closeBtn = document.getElementById("quickCreateCustomerCloseBtn");
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            if (isModalOpen && checkForChanges()) {
                const confirmClose = confirm('You have unsaved changes in the customer creation form. Are you sure you want to close without saving?');
                if (!confirmClose) {
                    return;
                }
            }
            modal.close();
        });
    }

    // Clear form when modal is closed
    if (modal) {
        modal.addEventListener('close', function() {
            // Reset state
            isModalOpen = false;
            hasUnsavedChanges = false;
            originalFormData = '';

            // Remove navigation prevention
            window.removeEventListener('beforeunload', preventNavigation);
            window.removeEventListener('popstate', handlePopState);

            // Clear form
            if (modalNameField) {
                modalNameField.value = '';
            }
            form.reset();
        });
    }

    saveButton.addEventListener("click", async function () {
        const formData = new FormData(form);

        // Disable button during save
        saveButton.disabled = true;
        saveButton.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Saving...';

        try {
            const response = await fetch("{{ route('customers.store') }}", {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content,
                    "Accept": "application/json", // Ensure JSON response
                },
                body: formData,
            });

            if (response.ok) {
                const data = await response.json();

                if (data.success) {
                    const customer = data.customer;

                    // Mark as saved to prevent navigation warning
                    hasUnsavedChanges = false;

                    // Try to use the new selection function first
                    const setSelectedCustomerFunc = window["setSelectedCustomer_{{ $id }}"];
                    if (setSelectedCustomerFunc) {
                        setSelectedCustomerFunc(customer);
                    } else {
                        // Fallback to old method for backward compatibility
                        if (nameField && idField) {
                            nameField.value = customer.name;
                            idField.value = customer.id;
                        }
                    }

                    // Clear the stored search text since we've used it
                    window.lastSearchText = "";

                    // Close the modal
                    modal.close();

                } else {
                    alert("Failed to create customer. Please try again.");
                }
            } else {
                const errorData = await response.json();
                alert("Failed to create customer: " + (errorData.message || "An error occurred."));
            }
        } catch (error) {
            console.error("Error creating customer:", error);
            alert("An unexpected error occurred. Please try again.");
        } finally {
            // Re-enable button
            saveButton.disabled = false;
            saveButton.innerHTML = '<i class="fa-sharp fa-save"></i> Save Customer';
        }
    });
});
</script>
@endpush
