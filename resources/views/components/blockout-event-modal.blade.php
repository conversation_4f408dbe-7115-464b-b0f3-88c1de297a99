<!-- Blockout Event Modal -->
<div id="blockout-event-modal" class="modal">
    <div class="modal-box max-w-3xl">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-error/10 to-error/5 -m-6 mb-6 px-6 py-4 border-b border-base-300/50">
            <div class="flex items-center gap-3">
                <div class="avatar avatar-placeholder">
                    <div class="bg-error text-error-content w-10 rounded-lg">
                        <i class="fa-sharp fa-ban text-lg"></i>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-base-content" id="blockout-event-modal-title">Add Blockout</h3>
                    <p class="text-base-content/70 text-sm">Block out time to prevent scheduling</p>
                </div>
            </div>
        </div>

        <form id="blockout-event-form" class="space-y-5">
            <!-- Title and Scheduling Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <!-- Title Section -->
                <div class="bg-base-50 rounded-lg p-3 border border-base-200">
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">
                                <i class="fa-sharp fa-tag text-error mr-1"></i>
                                Title <span class="text-error">*</span>
                            </span>
                        </label>
                        <input type="text" id="blockout-event-title" class="input input-bordered w-full" 
                               placeholder="e.g., Staff Meeting, Maintenance, Closed" required>
                        <p class="text-sm opacity-70 mt-1">Brief description of why this time is blocked.</p>
                    </div>
                </div>

                <!-- Scheduling Section -->
                <div class="bg-base-50 rounded-lg p-3 border border-base-200">
                    <div class="flex items-center gap-2 mb-3">
                        <i class="fa-sharp fa-calendar-clock text-error"></i>
                        <h4 class="font-semibold text-base-content">Scheduling</h4>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <label class="label">
                                <span class="label-text font-medium">Start Date & Time <span class="text-error">*</span></span>
                            </label>
                            <input type="datetime-local" id="blockout-event-start" class="input input-bordered w-full" required>
                        </div>
                        <div>
                            <label class="label">
                                <span class="label-text font-medium">End Date & Time</span>
                            </label>
                            <input type="datetime-local" id="blockout-event-end" class="input input-bordered w-full">
                        </div>
                    </div>

                    <div class="mt-3">
                        <label class="cursor-pointer label justify-start">
                            <input type="checkbox" id="blockout-event-all-day" class="checkbox checkbox-error mr-2">
                            <span class="label-text font-medium">All Day Event</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Description Section -->
            <div class="bg-base-50 rounded-lg p-3 border border-base-200">
                <div class="flex items-center gap-2 mb-3">
                    <i class="fa-sharp fa-sticky-note text-warning"></i>
                    <h4 class="font-semibold text-base-content">Additional Details</h4>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">
                                <i class="fa-sharp fa-align-left text-primary mr-1"></i>
                                Description
                            </span>
                        </label>
                        <textarea id="blockout-event-description" class="textarea textarea-bordered w-full" rows="3"
                                  placeholder="Additional details about this blockout..."></textarea>
                    </div>
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">
                                <i class="fa-sharp fa-map-marker-alt text-primary mr-1"></i>
                                Location
                            </span>
                        </label>
                        <input type="text" id="blockout-event-location" class="input input-bordered w-full"
                               placeholder="Location (optional)">
                        <p class="text-sm opacity-70 mt-1">Where this blockout applies.</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-between items-center pt-4 border-t border-base-200">
                <button type="button" id="delete-blockout-event-btn"
                        class="btn btn-error gap-2" style="display: none;">
                    <i class="fa-sharp fa-trash"></i> Delete Blockout
                </button>
                <div class="flex gap-3">
                    <button type="button" class="btn btn-outline gap-2" onclick="closeBlockoutEventModal()">
                        <i class="fa-sharp fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-error gap-2" id="save-blockout-event-btn">
                        <i class="fa-sharp fa-ban"></i> Add Blockout
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Blockout Event Modal Functions
function openBlockoutEventModal(action = 'add', eventData = null) {
    const modal = document.getElementById('blockout-event-modal');
    const title = document.getElementById('blockout-event-modal-title');
    const description = title.nextElementSibling;
    const deleteBtn = document.getElementById('delete-blockout-event-btn');
    const saveBtn = document.getElementById('save-blockout-event-btn');

    // Set modal title and show/hide delete button
    if (action === 'edit') {
        title.textContent = 'Edit Blockout';
        description.textContent = 'Update blockout details and scheduling information';
        deleteBtn.style.display = 'inline-flex';
        saveBtn.innerHTML = '<i class="fa-sharp fa-save"></i> Update Blockout';

        // Store event ID for editing
        window.currentBlockoutEventId = eventData.id;

        // Populate form with event data
        if (eventData) {
            console.log('Editing blockout event with data:', eventData);

            document.getElementById('blockout-event-title').value = eventData.title || '';
            document.getElementById('blockout-event-description').value = eventData.description || '';
            document.getElementById('blockout-event-location').value = eventData.location || '';
            document.getElementById('blockout-event-start').value = formatDateForInput(eventData.start);
            document.getElementById('blockout-event-end').value = eventData.end ? formatDateForInput(eventData.end) : '';
            document.getElementById('blockout-event-all-day').checked = eventData.allDay || false;
        }
    } else {
        title.textContent = 'Add Blockout';
        description.textContent = 'Block out time to prevent scheduling';
        deleteBtn.style.display = 'none';
        saveBtn.innerHTML = '<i class="fa-sharp fa-ban"></i> Add Blockout';

        // Clear event ID for new blockout
        window.currentBlockoutEventId = undefined;

        // Reset form first
        document.getElementById('blockout-event-form').reset();

        // Set default values for new blockout
        if (eventData && eventData.start) {
            console.log('Setting blockout modal dates:', {
                start: eventData.start,
                end: eventData.end,
                allDay: eventData.allDay
            });

            document.getElementById('blockout-event-start').value = formatDateForInput(eventData.start);

            // If end time is provided (from drag), use it; otherwise default to configured duration
            if (eventData.end && eventData.end.getTime() !== eventData.start.getTime()) {
                // Use provided end time (from drag selection)
                document.getElementById('blockout-event-end').value = formatDateForInput(eventData.end);
                console.log('Using provided end time from drag:', eventData.end);
            } else {
                // Use configured default duration
                const defaultDuration = {{ \App\Models\GlobalConfig::getPickupEventDuration() }}; // minutes
                const endTime = new Date(eventData.start.getTime() + (defaultDuration * 60 * 1000));
                document.getElementById('blockout-event-end').value = formatDateForInput(endTime);
                console.log('Using configured default duration:', defaultDuration, 'minutes, end time:', endTime);
            }

            document.getElementById('blockout-event-all-day').checked = eventData.allDay || false;
        }
    }
    
    modal.classList.add('modal-open');
}

function closeBlockoutEventModal() {
    const modal = document.getElementById('blockout-event-modal');
    modal.classList.remove('modal-open');
    document.getElementById('blockout-event-form').reset();
}

function formatDateForInput(date) {
    if (!date) return '';

    // Convert to local time for the datetime-local input
    const localDate = new Date(date);

    // Log for debugging
    console.log('Blockout event - formatting date for input:', {
        input: date,
        localDate: localDate,
        localComponents: {
            year: localDate.getFullYear(),
            month: localDate.getMonth() + 1,
            day: localDate.getDate(),
            hours: localDate.getHours(),
            minutes: localDate.getMinutes()
        }
    });

    const year = localDate.getFullYear();
    const month = String(localDate.getMonth() + 1).padStart(2, '0');
    const day = String(localDate.getDate()).padStart(2, '0');
    const hours = String(localDate.getHours()).padStart(2, '0');
    const minutes = String(localDate.getMinutes()).padStart(2, '0');

    const formatted = `${year}-${month}-${day}T${hours}:${minutes}`;
    console.log('Blockout event - formatted for input:', formatted);

    return formatted;
}

// Handle blockout event form submission
document.getElementById('blockout-event-form').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData();
    const isEditing = window.currentBlockoutEventId !== undefined;

    // Get datetime values and convert to UTC
    const startDateStr = document.getElementById('blockout-event-start').value;
    const endDateStr = document.getElementById('blockout-event-end').value;

    // No timezone conversion needed - send local datetime strings
    // Laravel handles timezone conversion automatically
    const startDateLocal = startDateStr;
    const endDateLocal = endDateStr;

    console.log('Blockout event - dates to send:', {
        start: startDateLocal,
        end: endDateLocal
    });

    // Basic event fields
    formData.append('calendar_id', window.currentCalendarId || '');
    formData.append('title', document.getElementById('blockout-event-title').value);
    formData.append('description', document.getElementById('blockout-event-description').value);
    formData.append('location', document.getElementById('blockout-event-location').value);
    formData.append('start_date', startDateLocal || '');
    formData.append('end_date', endDateLocal || '');
    formData.append('all_day', document.getElementById('blockout-event-all-day').checked ? '1' : '0');
    formData.append('is_blockout_event', '1');
    formData.append('color', '#dc2626'); // Red color for blockouts

    console.log('Blockout event - form data being sent:', {
        calendar_id: window.currentCalendarId || '',
        title: document.getElementById('blockout-event-title').value,
        is_blockout_event: '1',
        color: '#dc2626'
    });

    const url = isEditing ? `/events/${window.currentBlockoutEventId}` : '/events';
    const method = isEditing ? 'PUT' : 'POST';

    if (isEditing) {
        // For PUT requests, we need to send as JSON
        const jsonData = {};
        for (let [key, value] of formData.entries()) {
            jsonData[key] = value;
        }

        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(jsonData)
        })
        .then(response => response.json())
        .then(data => {
            console.log('Blockout event update response:', data);
            handleBlockoutEventResponse(data);
        })
        .catch(error => {
            console.error('Error updating blockout event:', error);
            alert('Error updating blockout event. Please try again.');
        });
    } else {
        fetch(url, {
            method: method,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            console.log('Blockout event creation response:', data);
            handleBlockoutEventResponse(data);
        })
        .catch(error => {
            console.error('Error creating blockout event:', error);
            alert('Error creating blockout event. Please try again.');
        });
    }
});

function handleBlockoutEventResponse(data) {
    // Check for success - either data.success, data.id, data.event.id, or data.message indicating success
    if (data.success || data.id || (data.event && data.event.id) || (data.message && data.message.includes('successfully'))) {
        closeBlockoutEventModal();
        // Refresh the calendar
        if (typeof calendar !== 'undefined' && calendar && typeof calendar.refetchEvents === 'function') {
            calendar.refetchEvents();
        } else if (window.calendar && typeof window.calendar.refetchEvents === 'function') {
            window.calendar.refetchEvents();
        } else {
            // Fallback: reload the page if calendar is not accessible
            console.warn('Calendar object not found, reloading page to refresh events');
            window.location.reload();
        }
    } else {
        console.error('Blockout event operation failed:', data);
        if (data.errors) {
            let errorMessage = 'Validation errors:\n';
            for (const [field, messages] of Object.entries(data.errors)) {
                errorMessage += `${field}: ${messages.join(', ')}\n`;
            }
            alert(errorMessage);
        } else {
            alert(data.error || data.message || 'An error occurred. Please try again.');
        }
    }
}

// Delete blockout event
document.getElementById('delete-blockout-event-btn').addEventListener('click', function() {
    if (!window.currentBlockoutEventId) {
        alert('No blockout event selected for deletion.');
        return;
    }

    if (confirm('Are you sure you want to delete this blockout? This action cannot be undone.')) {
        fetch(`/events/${window.currentBlockoutEventId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log('Blockout event deletion response:', data);
            if (data.message && data.message.includes('successfully')) {
                closeBlockoutEventModal();
                // Refresh the calendar
                if (typeof calendar !== 'undefined' && calendar && typeof calendar.refetchEvents === 'function') {
                    calendar.refetchEvents();
                } else if (window.calendar && typeof window.calendar.refetchEvents === 'function') {
                    window.calendar.refetchEvents();
                } else {
                    console.warn('Calendar object not found, reloading page to refresh events');
                    window.location.reload();
                }
            } else {
                alert(data.error || 'Error deleting blockout event. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error deleting blockout event:', error);
            alert('Error deleting blockout event. Please try again.');
        });
    }
});
</script>
