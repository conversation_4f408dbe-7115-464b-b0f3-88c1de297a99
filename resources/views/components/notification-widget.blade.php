@props([
    'limit' => 5,
    'showHeader' => true,
    'showViewAll' => true
])

@php
    $notificationService = app(\App\Services\NotificationService::class);
    $user = auth()->user();
    $notifications = $notificationService->getNotificationsForUser($user, false, true)->take($limit);
    $unreadCount = $notificationService->getUnreadCountForUser($user);
@endphp

<div class="bg-base-100 rounded-lg shadow-sm border border-base-300">
    @if($showHeader)
        <div class="p-4 border-b border-base-300 flex items-center justify-between">
            <h3 class="font-semibold text-base-content flex items-center gap-2">
                <i class="fa-sharp fa-solid fa-bell text-primary"></i>
                Recent Notifications
                @if($unreadCount > 0)
                    <span class="badge badge-primary badge-sm">{{ $unreadCount }}</span>
                @endif
            </h3>
            @if($showViewAll && auth()->user()->hasPermission('view_notifications'))
                <a href="{{ route('notifications.index') }}" class="btn btn-ghost btn-xs text-primary">
                    <i class="fa-sharp fa-solid fa-cog text-xs"></i>
                    Manage
                </a>
            @endif
        </div>
    @endif
    
    <div class="max-h-80 overflow-y-auto">
        @forelse($notifications as $notification)
            <div class="notification-widget-item p-3 border-b border-base-300 last:border-b-0 hover:bg-base-200 transition-colors cursor-pointer {{ $notification->isReadByUser($user) ? 'opacity-75' : '' }}" 
                 data-notification-id="{{ $notification->id }}"
                 @if($notification->link_url) onclick="handleNotificationClick({{ $notification->id }}, '{{ $notification->link_url }}')" @endif>
                <div class="flex items-start gap-3">
                    <div class="flex-shrink-0 mt-1">
                        <i class="fa-sharp {{ $notification->getUrgencyIcon() }} text-{{ $notification->urgency === 'critical' ? 'error' : ($notification->urgency === 'high' ? 'warning' : ($notification->urgency === 'normal' ? 'success' : 'info')) }}"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between">
                            <h4 class="font-medium text-sm text-base-content truncate">{{ $notification->title }}</h4>
                            <div class="flex items-center gap-1 ml-2">
                                @if(!$notification->isReadByUser($user))
                                    <div class="w-2 h-2 bg-primary rounded-full"></div>
                                @endif
                                @if($notification->is_dismissible)
                                    <button class="btn btn-ghost btn-xs text-base-content/50 hover:text-base-content dismiss-widget-btn" 
                                            data-notification-id="{{ $notification->id }}"
                                            onclick="event.stopPropagation(); dismissWidgetNotification({{ $notification->id }})">
                                        <i class="fa-sharp fa-solid fa-times text-xs"></i>
                                    </button>
                                @endif
                            </div>
                        </div>
                        <p class="text-xs text-base-content/70 mt-1 line-clamp-2">{{ $notification->message }}</p>
                        <div class="flex items-center justify-between mt-2">
                            <span class="text-xs text-base-content/50">{{ $notification->created_at->diffForHumans() }}</span>
                            @if($notification->link_url)
                                <span class="text-xs text-primary">{{ $notification->link_text ?: 'View' }}</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="p-6 text-center text-base-content/70">
                <i class="fa-sharp fa-bell text-2xl mb-2"></i>
                <p>No notifications</p>
                <p class="text-sm">You're all caught up!</p>
            </div>
        @endforelse
    </div>
    
    @if($notifications->count() >= $limit && $showViewAll)
        <div class="p-3 border-t border-base-300 text-center">
            <button onclick="openNotificationModal()" class="btn btn-ghost btn-sm w-full">
                <i class="fa-sharp fa-solid fa-eye"></i>
                View All Notifications
            </button>
        </div>
    @endif
</div>

@push('styles')
<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
@endpush

@push('scripts')
<script>
    function handleNotificationClick(notificationId, linkUrl) {
        // Mark as read
        fetch(`/api/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the notification item appearance
                const item = document.querySelector(`[data-notification-id="${notificationId}"]`);
                if (item) {
                    item.classList.add('opacity-75');
                    const unreadDot = item.querySelector('.w-2.h-2.bg-primary');
                    if (unreadDot) {
                        unreadDot.remove();
                    }
                }
                
                // Update global notification counts
                if (typeof updateGlobalNotificationCounts === 'function') {
                    // This function should be available from the notification system component
                    loadNotificationCount();
                }
            }
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);
        });
        
        // Navigate to the link
        if (linkUrl) {
            window.location.href = linkUrl;
        }
    }
    
    function dismissWidgetNotification(notificationId) {
        fetch(`/api/notifications/${notificationId}/dismiss`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the notification item
                const item = document.querySelector(`[data-notification-id="${notificationId}"]`);
                if (item) {
                    item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    item.style.opacity = '0';
                    item.style.transform = 'translateX(-100%)';
                    setTimeout(() => {
                        item.remove();
                        
                        // Check if widget is now empty
                        const remainingItems = document.querySelectorAll('.notification-widget-item');
                        if (remainingItems.length === 0) {
                            // Reload the page to show the empty state
                            window.location.reload();
                        }
                    }, 300);
                }
                
                // Update global notification counts
                if (typeof loadNotificationCount === 'function') {
                    loadNotificationCount();
                }
            }
        })
        .catch(error => {
            console.error('Error dismissing notification:', error);
        });
    }
    
    function openNotificationModal() {
        // This could open a modal with all notifications or navigate to the notifications page
        // For now, let's just navigate to the notifications management page if user has permission
        @if(auth()->user()->hasPermission('view_notifications'))
            window.location.href = '{{ route("notifications.index") }}';
        @else
            // Open a simple modal showing all user notifications
            alert('Full notification view coming soon!');
        @endif
    }
</script>
@endpush
