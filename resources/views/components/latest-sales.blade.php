<h3 class="font-semibold text-lg mb-4 text-base-content">Latest Items Sold</h3>
<div class="space-y-0">
    @foreach ($latestSales as $sale)
        @php
            $cardClass = 'bg-gray-700';
            if ($sale->net_revenue >= 100 && $sale->net_revenue < 150) {
                $cardClass = 'bg-blue-800';
            } elseif ($sale->net_revenue >= 150 && $sale->net_revenue < 300) {
                $cardClass = 'bg-green-800';
            
            } elseif ($sale->net_revenue >= 300) {
                $cardClass = 'bg-gradient-to-b from-yellow-700 via-yellow-600 to-yellow-900';
            }
        @endphp
        <a href="{{ route('invoices.show', $sale->invoice_id) }}"
            class="text-white {{ $cardClass }}  p-2 hover:scale-105 transition duration-300 block border-b border-gray-800">
            <div class="flex justify-between">
                <span class="font-semibold">{{ $sale->inventory->name }}</span>
                <span class="font-bold text-lg">${{ number_format($sale->net_revenue, 2) }}</span>
            </div>
            <div class="flex justify-between">
            <span>Sold on {{ $sale->invoice->created_at->format('M d, Y') }}</span>
            <span>Sold to {{ $sale->invoice->customer->name }}</span>
            </div>
        </a>
    @endforeach
</div>
