<div class="grid grid-cols-1 gap-6">
    @forelse ($salesData as $index => $data)
        <div class="card shadow-lg bg-base-100 p-6">
            <h2 class="card-title mb-4">{{ $data['name'] }} Revenue</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                @foreach ([
                    'mtd' => ['title' => 'Month-to-Date (MTD)', 'chartId' => 'chart-mtd'],
                    'lastMonth' => ['title' => 'Last Month', 'chartId' => 'chart-lastMonth'],
                    'ytd' => ['title' => 'Year-to-Date (YTD)', 'chartId' => 'chart-ytd'],
                ] as $key => $details)
                    @php
                        $hasData = $data[$key]['netRevenue'] > 0 || $data[$key]['totalDiscounts'] > 0 || $data[$key]['totalTaxes'] > 0;
                    @endphp
                    <div class="flex flex-col items-center">
                        <h3 class="text-lg font-semibold">{{ $details['title'] }}</h3>
                        <ul class="list-disc list-inside text-sm text-base-content mb-4">
                            <li><strong>Gross Revenue:</strong> ${{ number_format($data[$key]['grossRevenue'], 2) }}</li>
                            <li><strong>Net Revenue:</strong> ${{ number_format($data[$key]['netRevenue'], 2) }}</li>
                            <li><strong>Total Taxes:</strong> ${{ number_format($data[$key]['totalTaxes'], 2) }}</li>
                            <li><strong>Total Discounts:</strong> ${{ number_format($data[$key]['totalDiscounts'], 2) }}</li>
                        </ul>
                        <p class="text-sm text-base-content/60">
                            <strong>Date Range:</strong> {{ $data[$key]['dateRange']['start'] }} - {{ $data[$key]['dateRange']['end'] ?? 'NOW' }}
                        </p>
                        @if ($hasData)
                            <div class="chart-container">
                                <canvas id="{{ $details['chartId'] }}-{{ $index }}"></canvas>
                            </div>
                        @else
                            <p class="text-base-content/60 mt-4">No Chart Data Available</p>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
    @empty
        <p class="text-center text-base-content/60">No sales data available.</p>
    @endforelse
</div>

<script>
document.addEventListener("DOMContentLoaded", function () {
    @foreach ($salesData as $index => $data)
        (() => {
            const chartConfigs = [
                { id: 'mtd', data: @json($data['mtd']) },
                { id: 'lastMonth', data: @json($data['lastMonth']) },
                { id: 'ytd', data: @json($data['ytd']) },
            ];

            chartConfigs.forEach(({ id, data }) => {
                const hasData = data.netRevenue > 0 || data.totalDiscounts > 0 || data.totalTaxes > 0;
                if (hasData) {
                    const ctx = document.getElementById(`chart-${id}-{{ $index }}`).getContext('2d');

                    new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['Net Revenue', 'Total Discounts', 'Total Taxes'],
                            datasets: [{
                                data: [data.netRevenue, data.totalDiscounts, data.totalTaxes],
                                backgroundColor: [
                                    'hsl(var(--p))', // Net Revenue - primary color
                                    'hsl(var(--er))', // Total Discounts - error color
                                    'hsl(var(--wa))', // Total Taxes - warning color
                                ],
                                hoverOffset: 4
                            }]
                        },
                        options: {
                            plugins: {
                                legend: { display: true },
                            }
                        }
                    });
                }
            });
        })();
    @endforeach
});
</script>

@push('styles')
<style>
.chart-container {
    max-width: 300px;
    max-height: 300px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
@endpush
