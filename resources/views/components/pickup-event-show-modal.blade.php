<!-- Pickup Event Show Modal -->
<div id="pickup-event-show-modal" class="modal">
    <div class="modal-box max-w-5xl">
        <!-- Modal Header with Request Info -->
        <div class="bg-gradient-to-r from-primary/10 to-primary/5 -m-6 mb-4 px-4 py-3 border-b border-base-300/50">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="avatar avatar-placeholder">
                        <div class="bg-primary text-primary-content w-9 rounded-lg">
                            <i class="fa-sharp fa-truck text-base"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center gap-4">
                            <h3 class="text-lg font-bold text-base-content" id="pickup-event-show-title">Pickup: Customer Name Details</h3>
                            <div class="flex items-center gap-2 flex-wrap" id="pickup-event-header-info">
                                <span id="header-request-id" class="badge badge-neutral badge-sm gap-1" style="display: none;">
                                    <i class="fa-sharp fa-hashtag text-[10px]"></i>
                                    Request #<span id="header-request-id-value"></span>
                                </span>
                                <span id="header-business-name" class="badge badge-info badge-sm gap-1" style="display: none;">
                                    <i class="fa-sharp fa-building text-[10px]"></i>
                                    <span id="header-business-name-value"></span>
                                </span>
                                <span id="header-submitted-date" class="badge badge-secondary badge-sm gap-1" style="display: none;">
                                    <i class="fa-sharp fa-calendar-check text-[10px]"></i>
                                    <span id="header-submitted-date-value"></span>
                                </span>
                                <span id="header-source" class="badge badge-success badge-sm gap-1" style="display: none;">
                                    <i class="fa-sharp fa-globe text-[10px]"></i>
                                    <span id="header-source-value"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-circle btn-ghost" onclick="closePickupEventShowModal()">
                    <i class="fa-sharp fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Top Priority Info - Compact Bento Grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
            <!-- Primary Card - Customer & Schedule -->
            <div class="md:col-span-2 card bg-gradient-to-br from-primary/5 to-primary/10 shadow border border-primary/20">
                <div class="card-body p-4">

                    
                    <div class="space-y-3">
                        <div>
                            <div class="text-xs text-base-content/70">Customer</div>
                            <div class="text-base font-bold text-base-content" id="pickup-event-show-customer-name">Customer Name</div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <div class="text-xs text-base-content/70">
                                    <i class="fa-sharp fa-play text-success mr-1 text-[10px]"></i>
                                    Start Time
                                </div>
                                <div class="text-sm font-semibold text-base-content" id="pickup-event-show-start-date"></div>
                            </div>
                            <div id="pickup-event-show-end-container">
                                <div class="text-xs text-base-content/70">
                                    <i class="fa-sharp fa-stop text-error mr-1 text-[10px]"></i>
                                    End Time
                                </div>
                                <div class="text-sm font-semibold text-base-content" id="pickup-event-show-end-date"></div>
                            </div>
                        </div>
                        
                        <!-- Pickup Address moved here from separate card -->
                        <div id="pickup-event-show-address-section">
                            <div class="text-xs text-base-content/70 mb-1">
                                <i class="fa-sharp fa-map-marker-alt text-primary mr-1 text-[10px]"></i>
                                Pickup Address
                            </div>
                            <div class="flex items-start gap-2">
                                <div class="text-sm text-base-content flex-1" id="pickup-event-show-address">Address will be shown here</div>
                                <div class="flex gap-1">
                                    <button type="button"
                                            class="btn btn-sm btn-primary gap-1 text-xs"
                                            id="pickup-event-show-maps-btn"
                                            onclick="openAddressInMaps()"
                                            title="Open in Google Maps">
                                        <i class="fa-sharp fa-map text-xs"></i>
                                        Maps
                                    </button>
                                    <button type="button"
                                            class="btn btn-sm btn-primary gap-1 text-xs"
                                            id="pickup-event-show-directions-btn"
                                            onclick="openDirections()"
                                            title="Get Directions"
                                            style="display: none;">
                                        <i class="fa-sharp fa-route text-xs"></i>
                                        Directions
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Distance and Travel Time (if available) -->
                            <div id="pickup-event-show-distance-info" class="mt-2 pt-2 border-t border-base-300/50 space-y-1" style="display: none;">
                                <div class="flex items-center gap-2 text-xs">
                                    <i class="fa-sharp fa-location-dot text-primary text-[10px]"></i>
                                    <span class="text-base-content/70">Distance:</span>
                                    <span class="text-base-content font-medium" id="pickup-event-show-distance-text">Calculating...</span>
                                </div>
                                <div class="flex items-center gap-2 text-xs">
                                    <i class="fa-sharp fa-clock text-primary text-[10px]"></i>
                                    <span class="text-base-content/70">Travel Time:</span>
                                    <span class="text-base-content font-medium" id="pickup-event-show-duration-text">Calculating...</span>
                                </div>
                            </div>
                        </div>
                        
                        <div id="pickup-event-show-all-day-badge" class="badge badge-info badge-sm gap-1" style="display: none;">
                            <i class="fa-sharp fa-sun text-xs"></i>
                            All Day Event
                        </div>
                    </div>
                </div>
            </div>

            <!-- Staff & Contact Card -->
            <div class="card bg-gradient-to-br from-secondary/5 to-secondary/10 shadow border border-secondary/20">
                <div class="card-body p-4">

                    
                    <div class="space-y-2">
                        <div>
                            <div class="text-xs text-base-content/70">Staff Needed</div>
                            <div class="badge badge-primary" id="pickup-event-show-staff-needed">1</div>
                        </div>
                        
                        <div id="pickup-event-show-driver-container" style="display: none;">
                            <div class="text-xs text-base-content/70">Assigned Driver</div>
                            <div class="text-sm font-semibold text-base-content" id="pickup-event-show-driver">Driver Name</div>
                        </div>
                        
                        <div id="pickup-event-show-contact-name-container" style="display: none;">
                            <div class="text-xs text-base-content/70">Contact</div>
                            <div class="text-sm font-semibold text-base-content" id="pickup-event-show-contact-name">Contact Name</div>
                        </div>
                        
                        <div class="space-y-1">
                            <div id="pickup-event-show-contact-phone-container" style="display: none;">
                                <a href="#" class="link link-primary text-xs font-medium flex items-center gap-1" id="pickup-event-show-contact-phone">
                                    <i class="fa-sharp fa-phone text-success text-[10px]"></i>
                                    Phone Number
                                </a>
                            </div>
                            
                            <div id="pickup-event-show-contact-email-container" style="display: none;">
                                <a href="#" class="link link-primary text-xs font-medium flex items-center gap-1" id="pickup-event-show-contact-email">
                                    <i class="fa-sharp fa-envelope text-info text-[10px]"></i>
                                    Email Address
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Secondary Information Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-3">
            <!-- Left Column: Pickup Details (moved from right) -->
            <div class="space-y-3">
                <!-- Pickup Details Card -->
                <div class="card bg-gradient-to-br from-warning/5 to-warning/10 shadow border border-warning/20">
                    <div class="card-body p-4">

                        
                        <div class="space-y-3">
                            <!-- Load Size -->
                            <div id="pickup-event-show-load-size-container" class="flex items-center justify-between" style="display: none;">
                                <div class="flex items-center gap-1">
                                    <i class="fa-sharp fa-truck-loading text-primary text-xs"></i>
                                    <span class="text-sm font-medium text-base-content">Load Size</span>
                                </div>
                                <div class="badge badge-primary badge-sm" id="pickup-event-show-load-size">Load size will be shown here</div>
                            </div>

                            <!-- Item Types -->
                            <div id="pickup-event-show-item-types-container" style="display: none;">
                                <div class="flex items-center gap-1 mb-1">
                                    <i class="fa-sharp fa-tags text-secondary text-xs"></i>
                                    <span class="text-sm font-medium text-base-content">Item Types</span>
                                </div>
                                <div class="flex flex-wrap gap-1" id="pickup-event-show-item-types">Item types will be shown here</div>
                            </div>

                            <!-- Item Specifics -->
                            <div id="pickup-event-show-item-specifics-container" style="display: none;">
                                <div class="flex items-center gap-1 mb-1">
                                    <i class="fa-sharp fa-list-ul text-info text-xs"></i>
                                    <span class="text-sm font-medium text-base-content">Item Specifics</span>
                                </div>
                                <div class="text-xs text-base-content bg-base-100 p-2 rounded" id="pickup-event-show-item-specifics">Item specifics will be shown here</div>
                            </div>

                            <!-- Driver Instructions -->
                            <div id="pickup-event-show-driver-instructions-container" style="display: none;">
                                <div class="flex items-center gap-1 mb-1">
                                    <i class="fa-sharp fa-route text-warning text-xs"></i>
                                    <span class="text-sm font-medium text-base-content">Driver Instructions</span>
                                </div>
                                <div class="text-xs text-base-content bg-base-100 p-2 rounded" id="pickup-event-show-driver-instructions">Driver instructions will be shown here</div>
                            </div>

                            <!-- Legacy Items to Pickup (fallback) -->
                            <div id="pickup-event-show-legacy-items-container" style="display: none;">
                                <div class="flex items-center gap-1 mb-1">
                                    <i class="fa-sharp fa-boxes text-warning text-xs"></i>
                                    <span class="text-sm font-medium text-base-content">Items to Pickup</span>
                                </div>
                                <div class="text-xs text-base-content bg-base-100 p-2 rounded" id="pickup-event-show-legacy-items">Items will be shown here</div>
                            </div>

                            <!-- Legacy Estimated Quantity (fallback) -->
                            <div id="pickup-event-show-legacy-quantity-container" style="display: none;">
                                <div class="flex items-center gap-1 mb-1">
                                    <i class="fa-sharp fa-hashtag text-success text-xs"></i>
                                    <span class="text-sm font-medium text-base-content">Estimated Quantity</span>
                                </div>
                                <div class="text-xs text-base-content bg-base-100 p-2 rounded" id="pickup-event-show-legacy-quantity">Quantity will be shown here</div>
                            </div>

                            <!-- Property Location Details -->
                            <div id="pickup-event-show-location-details-container" style="display: none;">
                                <div class="flex items-center gap-1 mb-1">
                                    <i class="fa-sharp fa-map-location-dot text-info text-xs"></i>
                                    <span class="text-sm font-medium text-base-content">Location Details</span>
                                </div>
                                <div class="text-xs text-base-content bg-base-100 p-2 rounded" id="pickup-event-show-location-details">Location details will be shown here</div>
                            </div>

                            <!-- Customer Notes -->
                            <div id="pickup-event-show-customer-notes-container" style="display: none;">
                                <div class="flex items-center gap-1 mb-1">
                                    <i class="fa-sharp fa-comment-dots text-secondary text-xs"></i>
                                    <span class="text-sm font-medium text-base-content">Customer Notes</span>
                                </div>
                                <div class="text-xs text-base-content bg-base-100 p-2 rounded" id="pickup-event-show-customer-notes">Customer notes will be shown here</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column: Notes (moved from left) -->
            <div class="space-y-3">
                <!-- Combined Notes Section -->
                <div id="pickup-event-show-combined-notes-container" class="card bg-gradient-to-br from-info/5 to-info/10 shadow border border-info/20" style="display: none;">
                    <div class="card-body p-4">
                        <div class="flex items-center gap-2 mb-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-info text-info-content w-6 rounded">
                                    <i class="fa-sharp fa-sticky-note text-xs"></i>
                                </div>
                            </div>
                            <h4 class="text-sm font-semibold text-base-content">Notes</h4>
                        </div>
                        
                        <!-- Customer Provided Notes -->
                        <div id="customer-notes-section" style="display: none;">
                            <div class="text-xs font-medium text-base-content/70 mb-1">Customer Provided Notes</div>
                            <div class="text-sm text-base-content bg-base-100/50 p-2 rounded mb-3" id="pickup-event-show-notes"></div>
                        </div>
                        
                        <!-- Internal Notes -->
                        <div id="internal-notes-section" style="display: none;">
                            <div class="flex items-center justify-between mb-1">
                                <div class="text-xs font-medium text-base-content/70">Internal Notes</div>
                                <button
                                    type="button"
                                    id="edit-internal-notes-btn"
                                    class="btn btn-xs btn-info gap-1"
                                    onclick="toggleInternalNotesEdit()"
                                >
                                    <i class="fa-sharp fa-pen text-xs"></i>
                                    Edit
                                </button>
                            </div>
                            
                            <!-- Display Mode -->
                            <div id="internal-notes-display" class="text-sm text-base-content bg-base-100 p-2 rounded"></div>

                            <!-- Edit Mode -->
                            <div id="internal-notes-edit" class="space-y-2" style="display: none;">
                                <textarea
                                    id="internal-notes-textarea"
                                    class="textarea textarea-bordered textarea-sm w-full h-20 resize-none"
                                    placeholder="Add internal notes about this pickup..."
                                ></textarea>
                                <div class="flex items-center justify-between">
                                    <div id="internal-notes-save-status" class="text-xs text-success flex items-center gap-1 hidden">
                                        <i class="fa-sharp fa-check-circle text-xs"></i>
                                        Saved
                                    </div>
                                    <div class="flex gap-1">
                                        <button
                                            type="button"
                                            class="btn btn-xs btn-ghost gap-1"
                                            onclick="cancelInternalNotesEdit()"
                                        >
                                            <i class="fa-sharp fa-times text-xs"></i>
                                            Cancel
                                        </button>
                                        <button
                                            type="button"
                                            id="save-internal-notes-btn"
                                            class="btn btn-xs btn-info gap-1"
                                            onclick="saveInternalNotesFromModal()"
                                        >
                                            <i class="fa-sharp fa-save text-xs"></i>
                                            Save
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information - Full Width Stack -->
        <div class="space-y-3 mt-4">

            <!-- Pickup Images - Full Width -->
            <div id="pickup-event-show-images-container" class="card bg-gradient-to-br from-success/5 to-success/10 shadow border border-success/20" style="display: none;">
                <div class="card-body p-3">
                    <div class="flex items-center gap-2 mb-2">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-success text-success-content w-5 rounded">
                                <i class="fa-sharp fa-images text-xs"></i>
                            </div>
                        </div>
                        <h4 class="text-sm font-semibold text-base-content">Pickup Images</h4>
                    </div>
                    <div id="pickup-event-show-images-grid" class="grid grid-cols-4 sm:grid-cols-6 lg:grid-cols-8 gap-2 mb-2"></div>
                    <p class="text-xs text-base-content/70 flex items-center gap-1" id="pickup-event-show-images-count">
                        <i class="fa-sharp fa-info-circle text-xs"></i>
                        <span id="pickup-event-show-images-count-text">No images</span>
                    </p>
                </div>
            </div>

        </div>

        <!-- Status Management Section -->
        <div id="pickup-event-status-management" class="card bg-gradient-to-br from-base-200/30 to-base-300/30 shadow border border-base-300/50 mt-4" style="display: none;">
            <div class="card-body p-4">
                <div class="flex flex-col sm:flex-row justify-between items-center gap-3">
                    <div class="flex items-center gap-2">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-neutral text-neutral-content w-7 rounded">
                                <i class="fa-sharp fa-cogs text-xs"></i>
                            </div>
                        </div>
                        <div class="text-center sm:text-left">
                            <p class="font-semibold text-base-content text-sm">Manage Pickup Request</p>
                            <p class="text-xs text-base-content/70">Take action on this pickup request</p>
                        </div>
                    </div>
                    <div class="flex flex-wrap gap-2 w-full sm:w-auto">
                        <!-- Confirm Response - Only visible when status is pending -->
                        <button id="pickup-event-confirm-btn" onclick="confirmPickupEventRequest()" class="btn btn-success btn-sm gap-1 flex-1 sm:flex-none" style="display: none;">
                            <i class="fa-sharp fa-check text-xs"></i>
                            Confirm
                        </button>

                        <!-- Mark Complete - Only visible when status is confirmed -->
                        <button id="pickup-event-complete-btn" onclick="completePickupEventRequest()" class="btn btn-neutral btn-sm gap-1 flex-1 sm:flex-none" style="display: none;">
                            <i class="fa-sharp fa-check-circle text-xs"></i>
                            Complete
                        </button>

                        <!-- Mark Not Confirmed - Only visible when status is confirmed -->
                        <button id="pickup-event-unconfirm-btn" onclick="unconfirmPickupEventRequest()" class="btn btn-warning btn-sm gap-1 flex-1 sm:flex-none" style="display: none;">
                            <i class="fa-sharp fa-undo text-xs"></i>
                            Unconfirm
                        </button>

                        <!-- Cancel Request - Only visible when status is incoming, pending, or confirmed -->
                        <button id="pickup-event-cancel-btn" onclick="cancelPickupEventRequest()" class="btn btn-error btn-sm gap-1 flex-1 sm:flex-none" style="display: none;">
                            <i class="fa-sharp fa-times text-xs"></i>
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Actions -->
        <div class="modal-action mt-6">
            <div class="flex flex-col sm:flex-row gap-2 w-full justify-between">
                <div class="flex gap-2">
                    <button type="button" class="btn btn-neutral btn-sm gap-1" id="pickup-event-show-details-btn" onclick="viewPickupRequestDetails()" style="display: none;">
                        <i class="fa-sharp fa-file-lines text-xs"></i>
                        View Details
                    </button>
                </div>
                <div class="flex gap-2">
                    <button type="button" class="btn btn-ghost btn-sm gap-1" onclick="closePickupEventShowModal()">
                        <i class="fa-sharp fa-times text-xs"></i>
                        Close
                    </button>
                    <button type="button" class="btn btn-primary btn-sm gap-1 shadow" id="pickup-event-show-edit-btn" onclick="editPickupEventFromShow()" style="display: none;">
                        <i class="fa-sharp fa-pen-to-square text-xs"></i>
                        Edit Pickup Request
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal for Calendar Modal -->
<dialog id="pickupEventImageModal" class="modal">
    <div class="modal-box max-w-4xl">
        <h3 class="font-bold text-lg mb-4" id="pickupEventModalImageTitle">Image Preview</h3>
        <img id="pickupEventModalImage" src="" alt="" class="w-full rounded-lg">
        <div class="modal-action">
            <form method="dialog">
                <button class="btn">Close</button>
            </form>
        </div>
    </div>
    <form method="dialog" class="modal-backdrop">
        <button>close</button>
    </form>
</dialog>

<script>
// Pickup Event Show Modal Functions
let currentPickupEventShowData = null;
let currentPickupAddress = null;
let currentWarehouseLocation = null;

async function openPickupEventShowModal(eventData) {
    const modal = document.getElementById('pickup-event-show-modal');

    // Store event data for edit functionality
    currentPickupEventShowData = eventData;

    // Set modal title with customer name
    const customerName = eventData.customer_name || 'Unknown Customer';
    document.getElementById('pickup-event-show-title').textContent = `Pickup: ${customerName} Details`;

    // Set customer information
    document.getElementById('pickup-event-show-customer-name').textContent = customerName;

    // Format and set dates
    const formatDate = (date, allDay = false) => {
        if (!date) return '';

        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };

        if (!allDay) {
            options.hour = '2-digit';
            options.minute = '2-digit';
            options.hour12 = true;
        }

        return date.toLocaleDateString('en-US', options);
    };

    document.getElementById('pickup-event-show-start-date').textContent = formatDate(eventData.start, eventData.allDay);

    const endContainer = document.getElementById('pickup-event-show-end-container');
    if (eventData.end && !eventData.allDay) {
        document.getElementById('pickup-event-show-end-date').textContent = formatDate(eventData.end, eventData.allDay);
        endContainer.style.display = 'grid';
    } else {
        endContainer.style.display = 'none';
    }

    // Show all day badge if applicable
    const allDayBadge = document.getElementById('pickup-event-show-all-day-badge');
    if (eventData.allDay) {
        allDayBadge.style.display = 'inline-flex';
    } else {
        allDayBadge.style.display = 'none';
    }

    // Get pickup details from pickup request data
    const pickupDetails = eventData.pickup_details || {};

    // Set pickup address
    const addressText = pickupDetails.pickup?.address || eventData.pickup_address || 'No address specified';
    document.getElementById('pickup-event-show-address').textContent = addressText;

    // Store address for Maps functionality
    currentPickupAddress = addressText;
    
    // Show/hide Maps button in customer card
    const mapsBtn = document.getElementById('pickup-event-show-maps-btn');
    const directionsBtn = document.getElementById('pickup-event-show-directions-btn');
    
    if (addressText && addressText.trim() && addressText !== 'No address specified') {
        mapsBtn.style.display = 'inline-flex';
    } else {
        mapsBtn.style.display = 'none';
    }

    // Description section has been removed

    // Get guided pickup details
    const guidedDetails = pickupDetails.guided_details || {};

    // Check if we have new guided pickup data or should fall back to legacy fields
    const hasGuidedData = guidedDetails.load_size || guidedDetails.item_types || guidedDetails.item_specifics;

    if (hasGuidedData) {
        // Show guided pickup fields

        // Set load size
        const loadSizeContainer = document.getElementById('pickup-event-show-load-size-container');
        if (guidedDetails.load_size) {
            const loadSizeLabels = {
                'small': 'Small Load',
                'medium': 'Medium Load',
                'large': 'Large or Heavy Load'
            };
            const loadSizeLabel = loadSizeLabels[guidedDetails.load_size] || guidedDetails.load_size;
            document.getElementById('pickup-event-show-load-size').textContent = loadSizeLabel;
            loadSizeContainer.style.display = 'grid';
        } else {
            loadSizeContainer.style.display = 'none';
        }

        // Set item types
        const itemTypesContainer = document.getElementById('pickup-event-show-item-types-container');
        if (guidedDetails.item_types && Array.isArray(guidedDetails.item_types) && guidedDetails.item_types.length > 0) {
            const itemTypeLabels = {
                'small_electronics': 'Small Electronics',
                'appliances': 'Appliances',
                'peripheral_devices': 'Peripheral Devices',
                'batteries': 'Batteries',
                'crt_tvs': 'CRT TVs',
                'flatscreen_tvs': 'Flatscreen TV(s)',
                'tote_swap': 'Tote Swap',
                'gaylord_swap': 'Gaylord Swap',
                'servers': 'Servers',
                'laptops': 'Laptops',
                'desktops': 'Desktops',
                'large_appliances': 'Large Appliances',
                'other': 'Other'
            };

            const typeBadges = guidedDetails.item_types.map(type => {
                const label = itemTypeLabels[type] || type;
                return `<span class="badge badge-secondary badge-xs">${label}</span>`;
            }).join(' ');

            document.getElementById('pickup-event-show-item-types').innerHTML = typeBadges;
            itemTypesContainer.style.display = 'grid';
        } else {
            itemTypesContainer.style.display = 'none';
        }

        // Set item specifics
        const itemSpecificsContainer = document.getElementById('pickup-event-show-item-specifics-container');
        if (guidedDetails.item_specifics && guidedDetails.item_specifics.trim()) {
            document.getElementById('pickup-event-show-item-specifics').textContent = guidedDetails.item_specifics;
            itemSpecificsContainer.style.display = 'grid';
        } else {
            itemSpecificsContainer.style.display = 'none';
        }

        // Set driver instructions
        const driverInstructionsContainer = document.getElementById('pickup-event-show-driver-instructions-container');
        if (guidedDetails.driver_instructions && guidedDetails.driver_instructions.trim()) {
            document.getElementById('pickup-event-show-driver-instructions').textContent = guidedDetails.driver_instructions;
            driverInstructionsContainer.style.display = 'grid';
        } else {
            driverInstructionsContainer.style.display = 'none';
        }

        // Hide legacy fields when guided data is available
        document.getElementById('pickup-event-show-legacy-items-container').style.display = 'none';
        document.getElementById('pickup-event-show-legacy-quantity-container').style.display = 'none';

    } else {
        // Fall back to legacy fields if no guided data

        // Set legacy pickup items
        const legacyItemsContainer = document.getElementById('pickup-event-show-legacy-items-container');
        const pickupItems = pickupDetails.pickup?.items || eventData.pickup_items;
        if (pickupItems && pickupItems.trim()) {
            document.getElementById('pickup-event-show-legacy-items').textContent = pickupItems;
            legacyItemsContainer.style.display = 'grid';
        } else {
            legacyItemsContainer.style.display = 'none';
        }

        // Set legacy pickup quantity
        const legacyQuantityContainer = document.getElementById('pickup-event-show-legacy-quantity-container');
        const pickupQuantity = pickupDetails.pickup?.quantity;
        if (pickupQuantity && pickupQuantity.trim()) {
            document.getElementById('pickup-event-show-legacy-quantity').textContent = pickupQuantity;
            legacyQuantityContainer.style.display = 'grid';
        } else {
            legacyQuantityContainer.style.display = 'none';
        }

        // Hide guided fields when using legacy data
        document.getElementById('pickup-event-show-load-size-container').style.display = 'none';
        document.getElementById('pickup-event-show-item-types-container').style.display = 'none';
        document.getElementById('pickup-event-show-item-specifics-container').style.display = 'none';
        document.getElementById('pickup-event-show-driver-instructions-container').style.display = 'none';
    }

    // Set property location details
    const locationDetailsContainer = document.getElementById('pickup-event-show-location-details-container');
    const locationDetails = pickupDetails.pickup?.property_location_details;
    if (locationDetails && locationDetails.trim()) {
        document.getElementById('pickup-event-show-location-details').textContent = locationDetails;
        locationDetailsContainer.style.display = 'grid';
    } else {
        locationDetailsContainer.style.display = 'none';
    }

    // Set customer notes (separate from pickup notes)
    const customerNotesContainer = document.getElementById('pickup-event-show-customer-notes-container');
    const customerNotes = pickupDetails.pickup?.other_notes;
    if (customerNotes && customerNotes.trim()) {
        document.getElementById('pickup-event-show-customer-notes').textContent = customerNotes;
        customerNotesContainer.style.display = 'grid';
    } else {
        customerNotesContainer.style.display = 'none';
    }

    // Set staff needed (this would come from event data, not pickup request)
    document.getElementById('pickup-event-show-staff-needed').textContent = eventData.staff_needed || '1';

    // Set assigned driver (this would come from event data, not pickup request)
    const driverContainer = document.getElementById('pickup-event-show-driver-container');
    if (eventData.assigned_driver && eventData.assigned_driver.name) {
        document.getElementById('pickup-event-show-driver').textContent = eventData.assigned_driver.name;
        driverContainer.style.display = 'grid';
    } else {
        driverContainer.style.display = 'none';
    }

    // Set contact information from pickup request
    const contactName = pickupDetails.contact?.name || eventData.contact_name;
    const contactPhone = pickupDetails.contact?.phone || eventData.contact_phone;
    const contactEmail = pickupDetails.contact?.email || eventData.contact_email;

    const contactNameContainer = document.getElementById('pickup-event-show-contact-name-container');
    if (contactName && contactName.trim()) {
        document.getElementById('pickup-event-show-contact-name').textContent = contactName;
        contactNameContainer.style.display = 'grid';
    } else {
        contactNameContainer.style.display = 'none';
    }

    const contactPhoneContainer = document.getElementById('pickup-event-show-contact-phone-container');
    if (contactPhone && contactPhone.trim()) {
        const phoneElement = document.getElementById('pickup-event-show-contact-phone');
        phoneElement.textContent = contactPhone;
        phoneElement.href = `tel:${contactPhone}`;
        contactPhoneContainer.style.display = 'grid';
    } else {
        contactPhoneContainer.style.display = 'none';
    }

    const contactEmailContainer = document.getElementById('pickup-event-show-contact-email-container');
    if (contactEmail && contactEmail.trim()) {
        const emailElement = document.getElementById('pickup-event-show-contact-email');
        emailElement.textContent = contactEmail;
        emailElement.href = `mailto:${contactEmail}`;
        contactEmailContainer.style.display = 'grid';
    } else {
        contactEmailContainer.style.display = 'none';
    }

    // Handle combined notes section
    const combinedNotesContainer = document.getElementById('pickup-event-show-combined-notes-container');
    const customerNotesSection = document.getElementById('customer-notes-section');
    const internalNotesSection = document.getElementById('internal-notes-section');
    
    const pickupNotes = pickupDetails.pickup?.other_notes;
    const internalNotes = pickupDetails.internal?.staff_notes;
    
    let showCombinedNotes = false;
    
    // Set customer notes if available
    if (pickupNotes && pickupNotes.trim()) {
        document.getElementById('pickup-event-show-notes').textContent = pickupNotes;
        customerNotesSection.style.display = 'block';
        showCombinedNotes = true;
    } else {
        customerNotesSection.style.display = 'none';
    }
    
    // Set internal notes if user has permissions
    if (window.canEditEvents && eventData.pickup_request_id) {
        const internalNotesDisplay = document.getElementById('internal-notes-display');
        const internalNotesTextarea = document.getElementById('internal-notes-textarea');
        
        if (internalNotes && internalNotes.trim()) {
            internalNotesDisplay.textContent = internalNotes;
            internalNotesTextarea.value = internalNotes;
        } else {
            internalNotesDisplay.innerHTML = '<em class="text-base-content/50">No internal notes added yet.</em>';
            internalNotesTextarea.value = '';
        }
        internalNotesSection.style.display = 'block';
        showCombinedNotes = true;
    } else {
        internalNotesSection.style.display = 'none';
    }
    
    // Show or hide the combined container based on whether either section has content
    combinedNotesContainer.style.display = showCombinedNotes ? 'block' : 'none';

    // Set header request info
    const headerRequestId = document.getElementById('header-request-id');
    const headerRequestIdValue = document.getElementById('header-request-id-value');
    const headerBusinessName = document.getElementById('header-business-name');
    const headerBusinessNameValue = document.getElementById('header-business-name-value');
    const headerSubmittedDate = document.getElementById('header-submitted-date');
    const headerSubmittedDateValue = document.getElementById('header-submitted-date-value');
    const headerSource = document.getElementById('header-source');
    const headerSourceValue = document.getElementById('header-source-value');

    if (pickupDetails) {
        // Show request ID if available
        if (pickupDetails.meta && pickupDetails.meta.request_id) {
            headerRequestIdValue.textContent = pickupDetails.meta.request_id;
            headerRequestId.style.display = 'inline-flex';
        } else {
            headerRequestId.style.display = 'none';
        }

        // Show business name if available
        if (pickupDetails.contact && pickupDetails.contact.business_name) {
            headerBusinessNameValue.textContent = pickupDetails.contact.business_name;
            headerBusinessName.style.display = 'inline-flex';
        } else {
            headerBusinessName.style.display = 'none';
        }

        // Show submission date if available
        if (pickupDetails.meta && pickupDetails.meta.submitted_at) {
            const submittedDate = new Date(pickupDetails.meta.submitted_at);
            headerSubmittedDateValue.textContent = submittedDate.toLocaleDateString();
            headerSubmittedDate.style.display = 'inline-flex';
        } else {
            headerSubmittedDate.style.display = 'none';
        }

        // Show submission source if available
        if (pickupDetails.submission && pickupDetails.submission.source) {
            const sourceText = pickupDetails.submission.source.replace(/_/g, ' ').split(' ').map(word => 
                word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
            ).join(' ');
            headerSourceValue.textContent = sourceText;
            headerSource.style.display = 'inline-flex';
        } else {
            headerSource.style.display = 'none';
        }
    } else {
        // Hide all header info if no pickup details
        headerRequestId.style.display = 'none';
        headerBusinessName.style.display = 'none';
        headerSubmittedDate.style.display = 'none';
        headerSource.style.display = 'none';
    }

    // The Request Info card has been removed since we moved the info to header

    // Show edit button if user has permissions and pickup request exists
    const editBtn = document.getElementById('pickup-event-show-edit-btn');
    if (window.canEditEvents && eventData.pickup_request_id) {
        editBtn.style.display = 'inline-flex';
        // Update the onclick to redirect to pickup request edit
        editBtn.onclick = function() {
            window.location.href = `/pickup-requests/${eventData.pickup_request_id}/edit`;
        };
    } else {
        editBtn.style.display = 'none';
    }

    // Show/hide View Details button
    const detailsBtn = document.getElementById('pickup-event-show-details-btn');
    if (eventData.pickup_request_id) {
        detailsBtn.style.display = 'inline-flex';
    } else {
        detailsBtn.style.display = 'none';
    }

    // Show/hide status management section and configure buttons
    const statusManagementSection = document.getElementById('pickup-event-status-management');
    const confirmBtn = document.getElementById('pickup-event-confirm-btn');
    const completeBtn = document.getElementById('pickup-event-complete-btn');
    const unconfirmBtn = document.getElementById('pickup-event-unconfirm-btn');
    const cancelBtn = document.getElementById('pickup-event-cancel-btn');

    if (window.canEditEvents && eventData.pickup_request_id && eventData.pickup_request_status) {
        statusManagementSection.style.display = 'block';

        // Hide all buttons first
        confirmBtn.style.display = 'none';
        completeBtn.style.display = 'none';
        unconfirmBtn.style.display = 'none';
        cancelBtn.style.display = 'none';

        const status = eventData.pickup_request_status;

        // Show appropriate buttons based on status
        if (status === 'pending') {
            confirmBtn.style.display = 'inline-flex';
        }

        if (status === 'confirmed') {
            completeBtn.style.display = 'inline-flex';
            unconfirmBtn.style.display = 'inline-flex';
        }

        if (['incoming', 'pending', 'confirmed'].includes(status)) {
            cancelBtn.style.display = 'inline-flex';
        }
    } else {
        statusManagementSection.style.display = 'none';
    }

    // Load distance and travel time data if this is a pickup event
    console.log('=== DISTANCE CALCULATION DEBUG ===');
    console.log('eventData:', eventData);
    console.log('is_pickup_event:', eventData.is_pickup_event);
    console.log('pickup_request_id:', eventData.pickup_request_id);
    console.log('addressText:', addressText);
    
    const distanceInfo = document.getElementById('pickup-event-show-distance-info');
    const distanceText = document.getElementById('pickup-event-show-distance-text');
    const durationText = document.getElementById('pickup-event-show-duration-text');
    
    // Check if we have a pickup_request_id - that's the main indicator this is a pickup event
    if (eventData.pickup_request_id && addressText && addressText !== 'No address specified') {
        console.log('All conditions met, fetching distance data...');
        
        // Show the distance info section
        distanceInfo.style.display = 'block';
        
        // Reset to loading state
        distanceText.textContent = 'Loading...';
        durationText.textContent = 'Loading...';
        
        try {
            const distanceUrl = `/pickup-requests/${eventData.pickup_request_id}/distance`;
            console.log('Fetching distance from URL:', distanceUrl);
            
            // Fetch distance data for the pickup request
            const response = await fetch(distanceUrl, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });
            
            console.log('Distance API response status:', response.status);
            console.log('Distance API response ok:', response.ok);
            
            if (response.ok) {
                const distanceResponse = await response.json();
                console.log('Distance API response data:', distanceResponse);
                
                // Check if we have distance_data in the response
                if (distanceResponse.distance_data) {
                    console.log('distance_data found:', distanceResponse.distance_data);
                    
                    if (distanceResponse.distance_data.success) {
                        console.log('Distance calculation successful');
                        // Successfully calculated distance
                        distanceText.textContent = distanceResponse.distance_data.distance_text || 'Unavailable';
                        durationText.textContent = distanceResponse.distance_data.duration_text || 'Unavailable';
                        console.log('Set distance text to:', distanceText.textContent);
                        console.log('Set duration text to:', durationText.textContent);
                        
                        // Show directions button if we have warehouse location
                        if (distanceResponse.warehouse_location) {
                            console.log('Warehouse location:', distanceResponse.warehouse_location);
                            currentWarehouseLocation = distanceResponse.warehouse_location;
                            
                            // Show directions button in the customer card
                            const directionsBtn = document.getElementById('pickup-event-show-directions-btn');
                            if (directionsBtn) {
                                directionsBtn.style.display = 'inline-flex';
                            }
                        }
                    } else {
                        console.log('Distance calculation failed');
                        // Failed to calculate distance
                        distanceText.textContent = distanceResponse.distance_data.distance_text || 'Could not calculate';
                        durationText.textContent = distanceResponse.distance_data.duration_text || 'Could not calculate';
                    }
                } else if (distanceResponse.warehouse_location !== undefined && distanceResponse.warehouse_location === '') {
                    console.log('Warehouse location not configured');
                    // Warehouse location not configured
                    distanceText.textContent = 'Warehouse location not configured';
                    durationText.textContent = 'Warehouse location not configured';
                    distanceText.classList.add('text-base-content/50');
                    durationText.classList.add('text-base-content/50');
                } else {
                    console.log('No distance data available in response');
                    // No distance data available
                    distanceText.textContent = 'Not available';
                    durationText.textContent = 'Not available';
                }
            } else {
                console.log('Distance API response not ok, status:', response.status);
                distanceText.textContent = 'Could not load';
                durationText.textContent = 'Could not load';
            }
        } catch (error) {
            console.error('Error fetching distance data:', error);
            console.error('Error details:', error.message, error.stack);
            distanceText.textContent = 'Error loading';
            durationText.textContent = 'Error loading';
        }
    } else {
        console.log('Conditions not met for showing distance:');
        console.log('- pickup_request_id:', eventData.pickup_request_id);
        console.log('- addressText:', addressText);
        console.log('- addressText !== "No address specified":', addressText !== 'No address specified');
        // Hide the distance info if not applicable
        distanceInfo.style.display = 'none';
    }

    // Load and display pickup images
    await loadPickupImages(eventData.pickup_request_id);

    // Show modal
    modal.classList.add('modal-open');
}

// Load pickup images for the modal
async function loadPickupImages(pickupRequestId) {
    const imagesContainer = document.getElementById('pickup-event-show-images-container');
    const imagesGrid = document.getElementById('pickup-event-show-images-grid');
    const imagesCountText = document.getElementById('pickup-event-show-images-count-text');
    
    // Clear existing images
    imagesGrid.innerHTML = '';
    
    if (!pickupRequestId) {
        imagesContainer.style.display = 'none';
        return;
    }
    
    try {
        const response = await fetch(`/pickup-requests/${pickupRequestId}/images`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        });
        
        if (!response.ok) {
            imagesContainer.style.display = 'none';
            return;
        }
        
        const data = await response.json();
        
        if (data.success && data.images && data.images.length > 0) {
            // Display images
            const imageElements = data.images.map(image => {
                const isStaffImage = image.uploaded_by !== null;
                const borderClass = isStaffImage ? 'border-warning' : 'border-base-300';
                const iconClass = isStaffImage ? 'fa-user-tie text-warning' : 'fa-user text-base-content/60';
                
                return `
                    <div class="relative group cursor-pointer border ${borderClass} rounded overflow-hidden" onclick="openPickupEventImageModal('${image.src}', '${image.title}')">
                        <div class="aspect-square bg-base-200 overflow-hidden">
                            <img src="${image.thumbnail}" 
                                 alt="${image.alt_text}" 
                                 class="w-full h-full object-cover hover:scale-105 transition-transform duration-200">
                        </div>
                        <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                            <i class="fa-sharp fa-magnifying-glass-plus text-white text-lg"></i>
                        </div>
                    </div>
                `;
            }).join('');
            
            imagesGrid.innerHTML = imageElements;
            
            // Update count text
            const customerCount = data.images.filter(img => img.uploaded_by === null).length;
            const staffCount = data.images.filter(img => img.uploaded_by !== null).length;
            
            let countText = `${data.images.length} ${data.images.length === 1 ? 'image' : 'images'} total`;
            if (customerCount > 0 && staffCount > 0) {
                countText += ` (${customerCount} by customer, ${staffCount} by staff)`;
            } else if (customerCount > 0) {
                countText += ` (${customerCount} by customer)`;
            } else if (staffCount > 0) {
                countText += ` (${staffCount} by staff)`;
            }
            
            imagesCountText.textContent = countText;
            imagesContainer.style.display = 'block';
        } else {
            imagesContainer.style.display = 'none';
        }
    } catch (error) {
        console.error('Error loading pickup images:', error);
        imagesContainer.style.display = 'none';
    }
}

// Open image modal for calendar modal
function openPickupEventImageModal(src, title) {
    document.getElementById('pickupEventModalImage').src = src;
    document.getElementById('pickupEventModalImageTitle').textContent = title || 'Image Preview';
    document.getElementById('pickupEventImageModal').showModal();
}

function closePickupEventShowModal() {
    const modal = document.getElementById('pickup-event-show-modal');
    modal.classList.remove('modal-open');
    currentPickupEventShowData = null;
    currentPickupAddress = null;
    currentWarehouseLocation = null;
    
    // Hide directions button for next time
    const directionsBtn = document.getElementById('pickup-event-show-directions-btn');
    if (directionsBtn) {
        directionsBtn.style.display = 'none';
    }
}

function editPickupEventFromShow() {
    if (currentPickupEventShowData) {
        // Close show modal
        closePickupEventShowModal();

        // Open edit modal with the stored event data
        openPickupEventModal('edit', currentPickupEventShowData);
    }
}

function viewPickupRequestDetails() {
    if (currentPickupEventShowData && currentPickupEventShowData.pickup_request_id) {
        // Navigate to the pickup request show page
        window.location.href = `/pickup-requests/${currentPickupEventShowData.pickup_request_id}`;
    }
}

function openAddressInMaps() {
    if (currentPickupAddress && currentPickupAddress.trim()) {
        // Encode the address for URL
        const encodedAddress = encodeURIComponent(currentPickupAddress.trim());

        // Create Google Maps URL
        const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;

        // Open in new tab
        window.open(mapsUrl, '_blank');
    }
}

function openDirections() {
    if (currentPickupAddress && currentPickupAddress.trim() && currentWarehouseLocation) {
        const directionsUrl = `https://www.google.com/maps/dir/${encodeURIComponent(currentWarehouseLocation)}/${encodeURIComponent(currentPickupAddress)}`;
        window.open(directionsUrl, '_blank');
    }
}

// Status change functions for pickup event modal
async function confirmPickupEventRequest() {
    if (!currentPickupEventShowData || !currentPickupEventShowData.pickup_request_id) return;

    const confirmed = await showConfirm({
        title: 'Confirm Pickup Request',
        message: 'Are you sure you want to confirm this pickup request? This indicates the customer has confirmed the appointment.',
        confirmText: 'Confirm Request',
        confirmClass: 'btn-success',
        icon: 'fa-check',
        iconColor: 'bg-success/10 text-success'
    });

    if (!confirmed) return;

    try {
        const response = await fetch(`/pickup-requests/${currentPickupEventShowData.pickup_request_id}/confirm`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();
        if (data.success) {
            await showSuccess({
                title: 'Request Confirmed',
                message: data.message
            });
            // Update the current event data and refresh the modal
            currentPickupEventShowData.pickup_request_status = 'confirmed';
            openPickupEventShowModal(currentPickupEventShowData);
            // Refresh calendar if available
            if (window.calendar) {
                window.calendar.refetchEvents();
            }
        } else {
            await showError({
                title: 'Error',
                message: data.message
            });
        }
    } catch (error) {
        console.error('Error confirming pickup request:', error);
        await showError({
            title: 'Error',
            message: 'An error occurred while confirming the pickup request.'
        });
    }
}

async function completePickupEventRequest() {
    if (!currentPickupEventShowData || !currentPickupEventShowData.pickup_request_id) return;

    const confirmed = await showConfirm({
        title: 'Mark Pickup Complete',
        message: 'Are you sure you want to mark this pickup as completed?',
        confirmText: 'Mark Complete',
        confirmClass: 'btn-neutral',
        icon: 'fa-check-circle',
        iconColor: 'bg-neutral/10 text-neutral'
    });

    if (!confirmed) return;

    try {
        const response = await fetch(`/pickup-requests/${currentPickupEventShowData.pickup_request_id}/complete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();
        if (data.success) {
            await showSuccess({
                title: 'Request Completed',
                message: data.message
            });
            // Update the current event data and refresh the modal
            currentPickupEventShowData.pickup_request_status = 'completed';
            openPickupEventShowModal(currentPickupEventShowData);
            // Refresh calendar if available
            if (window.calendar) {
                window.calendar.refetchEvents();
            }
        } else {
            // Check if this is a timing-related error (pickup not yet due)
            if (response.status === 400 && data.pickup_time) {
                await showWarning({
                    title: 'Pickup Not Yet Due',
                    message: data.message
                });
            } else {
                await showError({
                    title: 'Error',
                    message: data.message
                });
            }
        }
    } catch (error) {
        console.error('Error completing pickup request:', error);
        await showError({
            title: 'Error',
            message: 'An error occurred while completing the pickup request.'
        });
    }
}

async function unconfirmPickupEventRequest() {
    if (!currentPickupEventShowData || !currentPickupEventShowData.pickup_request_id) return;

    const confirmed = await showConfirm({
        title: 'Mark Not Confirmed',
        message: 'Are you sure you want to change this pickup request back to pending status? This will remove the customer confirmation.',
        confirmText: 'Mark Not Confirmed',
        confirmClass: 'btn-warning',
        icon: 'fa-undo',
        iconColor: 'bg-warning/10 text-warning'
    });

    if (!confirmed) return;

    try {
        const response = await fetch(`/pickup-requests/${currentPickupEventShowData.pickup_request_id}/unconfirm`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();
        if (data.success) {
            await showSuccess({
                title: 'Status Changed',
                message: data.message
            });
            // Update the current event data and refresh the modal
            currentPickupEventShowData.pickup_request_status = 'pending';
            openPickupEventShowModal(currentPickupEventShowData);
            // Refresh calendar if available
            if (window.calendar) {
                window.calendar.refetchEvents();
            }
        } else {
            await showError({
                title: 'Error',
                message: data.message
            });
        }
    } catch (error) {
        console.error('Error unconfirming pickup request:', error);
        await showError({
            title: 'Error',
            message: 'An error occurred while changing the pickup request status.'
        });
    }
}

async function cancelPickupEventRequest() {
    if (!currentPickupEventShowData || !currentPickupEventShowData.pickup_request_id) return;

    const confirmed = await showConfirm({
        title: 'Cancel Pickup Request',
        message: 'Are you sure you want to cancel this pickup request? This action cannot be undone.',
        confirmText: 'Cancel Request',
        confirmClass: 'btn-error',
        icon: 'fa-times',
        iconColor: 'bg-error/10 text-error'
    });

    if (!confirmed) return;

    try {
        const response = await fetch(`/pickup-requests/${currentPickupEventShowData.pickup_request_id}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();
        if (data.success) {
            await showSuccess({
                title: 'Request Cancelled',
                message: data.message
            });
            // Update the current event data and refresh the modal
            currentPickupEventShowData.pickup_request_status = 'cancelled';
            openPickupEventShowModal(currentPickupEventShowData);
            // Refresh calendar if available
            if (window.calendar) {
                window.calendar.refetchEvents();
            }
        } else {
            await showError({
                title: 'Error',
                message: data.message
            });
        }
    } catch (error) {
        console.error('Error cancelling pickup request:', error);
        await showError({
            title: 'Error',
            message: 'An error occurred while cancelling the pickup request.'
        });
    }
}



// Internal Notes Modal Functions
let originalInternalNotes = '';
let notesTimeout;

function toggleInternalNotesEdit() {
    const displayDiv = document.getElementById('internal-notes-display');
    const editDiv = document.getElementById('internal-notes-edit');
    const textarea = document.getElementById('internal-notes-textarea');
    const editBtn = document.getElementById('edit-internal-notes-btn');

    // Store original notes
    originalInternalNotes = textarea.value = displayDiv.textContent.replace('No internal notes added yet.', '');

    // Toggle visibility
    displayDiv.style.display = 'none';
    editDiv.style.display = 'block';
    editBtn.style.display = 'none';

    // Focus textarea
    textarea.focus();

    // Add auto-save functionality
    textarea.addEventListener('input', function() {
        clearTimeout(notesTimeout);
        notesTimeout = setTimeout(() => {
            saveInternalNotesFromModal(true); // true for auto-save
        }, 1000); // 1 second delay
    });
}

function cancelInternalNotesEdit() {
    const displayDiv = document.getElementById('internal-notes-display');
    const editDiv = document.getElementById('internal-notes-edit');
    const textarea = document.getElementById('internal-notes-textarea');
    const editBtn = document.getElementById('edit-internal-notes-btn');

    // Restore original notes
    textarea.value = originalInternalNotes;

    // Toggle visibility
    displayDiv.style.display = 'block';
    editDiv.style.display = 'none';
    editBtn.style.display = 'inline-flex';
}

async function saveInternalNotesFromModal(isAutoSave = false) {
    const textarea = document.getElementById('internal-notes-textarea');
    const saveBtn = document.getElementById('save-internal-notes-btn');
    const saveStatus = document.getElementById('internal-notes-save-status');
    const displayDiv = document.getElementById('internal-notes-display');
    const editDiv = document.getElementById('internal-notes-edit');
    const editBtn = document.getElementById('edit-internal-notes-btn');

    if (!currentPickupEventShowData || !currentPickupEventShowData.pickup_request_id) {
        if (!isAutoSave) alert('No pickup request ID found');
        return;
    }

    const notes = textarea.value;
    const pickupRequestId = currentPickupEventShowData.pickup_request_id;

    try {
        if (!isAutoSave) {
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fa-sharp fa-spinner fa-spin text-xs"></i> Saving...';
        }

        const response = await fetch(`/pickup-requests/${pickupRequestId}/update-internal-notes`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ notes: notes })
        });

        const data = await response.json();

        if (data.success) {
            // Update display
            if (notes.trim()) {
                displayDiv.textContent = notes;
            } else {
                displayDiv.innerHTML = '<em class="text-base-content/50">No internal pickup notes added yet.</em>';
            }

            // Show success status
            saveStatus.classList.remove('hidden');
            setTimeout(() => {
                saveStatus.classList.add('hidden');
            }, 3000);

            // Switch back to display mode only if not auto-saving
            if (!isAutoSave) {
                displayDiv.style.display = 'block';
                editDiv.style.display = 'none';
                editBtn.style.display = 'inline-flex';
            }

        } else {
            if (!isAutoSave) alert('Error saving notes: ' + data.message);
        }
    } catch (error) {
        console.error('Error saving internal notes:', error);
        if (!isAutoSave) alert('An error occurred while saving notes.');
    } finally {
        if (!isAutoSave) {
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fa-sharp fa-save text-xs"></i> Save';
        }
    }
}
</script>
