@props([
    'pageTitle' => null,
    'pageIcon' => null,
    'actionButtons' => [],
    'primaryButtons' => [],
    'breadcrumbs' => [],
    'title' => null // Keep for backward compatibility
])

@php
    use App\Helpers\HeaderButtons;

    // Helper function to check if button should be displayed
    $shouldDisplayButton = function($button) {
        // Check permission if specified
        if (isset($button['permission'])) {
            if (is_array($button['permission'])) {
                // Multiple permissions - check if user has any of them
                $hasPermission = collect($button['permission'])->some(fn($perm) => auth()->user()->hasPermission(trim($perm)));
                if (!$hasPermission) return false;
            } else {
                // Single permission
                if (!auth()->user()->hasPermission($button['permission'])) return false;
            }
        }

        // Check custom condition if specified
        if (isset($button['condition'])) {
            if (!$button['condition']) return false;
        }

        // Check if button should be hidden
        if (isset($button['hidden']) && $button['hidden']) return false;

        return true;
    };

    // Process primary buttons with permission filtering
    $processedPrimaryButtons = [];
    if (!empty($primaryButtons)) {
        $allPrimaryButtons = HeaderButtons::getPrimaryButtons($primaryButtons);
        $processedPrimaryButtons = array_filter($allPrimaryButtons, $shouldDisplayButton);
    }

    // Filter action buttons based on permissions and conditions
    $filteredActionButtons = [];
    if (!empty($actionButtons)) {
        $filteredActionButtons = array_filter($actionButtons, $shouldDisplayButton);
    }
@endphp

<header class="bg-neutral shadow w-full z-50 transition-transform duration-300 lg:relative lg:z-30" id="stickyHeader">
    <div class="flex items-center">

        <!-- Mobile menu toggle button - hidden, now in mobile dock -->
        <div class="hidden">
            <button
                id="headerSidebarToggle"
                aria-label="Toggle Sidebar"
                class="px-2 py-1 text-primary rounded text-xl cursor-pointer"
            >
                <i class="fa-sharp fa-solid fa-bars"></i>
            </button>
        </div>

        <!-- Header content aligned with main content -->
        <div class="flex-1 py-3 px-4 lg:px-6 bg-neutral-focus text-neutral-content flex items-center">
            <!-- Page title with optional icon - centered on mobile, left-aligned on desktop -->
            <div class="flex-1 flex items-center gap-2 lg:gap-3 text-center md:text-left">
                @if($pageIcon)
                    <div class="avatar avatar-placeholder hidden md:block">
                        <div class="bg-primary text-primary-content w-8 lg:w-10 rounded-lg">
                            <i class="{{ $pageIcon }} text-sm lg:text-lg"></i>
                        </div>
                    </div>
                @endif

                <div>
                    @if($pageTitle)
                        <h2 class="font-semibold text-lg lg:text-xl text-neutral-content leading-tight">
                            {{ $pageTitle }}
                        </h2>
                    @elseif(isset($title))
                        <h2 class="font-semibold text-lg lg:text-xl text-neutral-content leading-tight">
                            {{ $title }}
                        </h2>
                    @else
                        {{ $slot }}
                    @endif
                </div>
            </div>

            <!-- Desktop Notifications Button - hidden on mobile -->
            <div class="hidden lg:flex items-center mr-4 relative">
                <button
                    id="desktopNotificationsToggle"
                    aria-label="Notifications"
                    class="btn btn-ghost btn-circle text-neutral-content hover:bg-neutral-focus relative"
                >
                    <i class="fa-sharp fa-solid fa-bell text-lg"></i>
                    <!-- Notification badge -->
                    <div class="absolute -top-1 -right-1 w-4 h-4 bg-error rounded-full hidden items-center justify-center" id="notificationBadge">
                        <span class="text-xs text-error-content font-bold" id="notificationCount">0</span>
                    </div>
                </button>

                <!-- Notifications Dropdown -->
                <div id="notificationsDropdown" class="absolute top-full right-0 mt-2 w-96 bg-base-100 rounded-lg shadow-lg border border-base-300 hidden z-50 max-h-96 overflow-hidden">
                    <div class="p-4 border-b border-base-300 flex justify-between items-center">
                        <h3 class="font-semibold text-base-content">Notifications</h3>
                        <button id="dismissAllBtn" class="btn btn-ghost btn-xs text-base-content/70 hover:text-base-content">
                            <i class="fa-sharp fa-solid fa-check-double text-xs"></i>
                            Dismiss All
                        </button>
                    </div>
                    <div id="notificationsContent" class="max-h-80 overflow-y-auto">
                        <!-- Notifications will be loaded here -->
                        <div class="p-4 text-center text-base-content/70" id="loadingNotifications">
                            <span class="loading loading-spinner loading-sm"></span>
                            <p class="mt-2">Loading notifications...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Account Dropdown -->
            <div class="relative">
                <x-user-dropdown align="right" width="48">
                    <x-slot name="trigger">
                        <div class="flex items-center hover:opacity-80 transition-opacity duration-150 cursor-pointer p-1 rounded-md hover:bg-neutral-focus">
                            <!-- User name (hidden on mobile) -->
                            <span class="hidden md:inline-flex mr-2 text-neutral-content font-medium">{{ Auth::user()->name }}</span>

                            <!-- Profile photo -->
                            <div class="h-8 w-8 rounded-full overflow-hidden ring-2 ring-neutral-content ring-opacity-50">
                                <img class="h-full w-full object-cover"
                                     src="{{ Auth::user()->profilePhotoUrl }}"
                                     alt="{{ Auth::user()->name }}" />
                            </div>


                        </div>
                    </x-slot>

                    <x-slot name="content" class="bg-base-200">
                        <div class="px-4 py-3 text-sm text-base-content border-b bg-base-200 border-base-300">
                            <div class="font-medium  !text-base-content">{{ Auth::user()->name }}</div>
                            <div class="text-xs">{{ Auth::user()->email }}</div>
                        </div>

                        <!-- Profile link -->
                        <a href="{{ route('profile.show') }}" class="block px-4 py-2 text-sm text-base-content bg-base-200 hover:bg-base-300">
                            <div class="flex items-center">
                                <i class="fa-sharp fa-solid fa-user mr-2 text-base-content opacity-60"></i>
                                <span>Profile</span>
                            </div>
                        </a>

                        <!-- Emergency Contact link -->
                        <a href="{{ route('profile.emergency-contact.edit') }}" class="block px-4 py-2 text-sm text-base-content bg-base-200 hover:bg-base-300">
                            <div class="flex items-center">
                                <i class="fa-sharp fa-solid fa-phone mr-2 text-base-content opacity-60"></i>
                                <span>Emergency Contact</span>
                            </div>
                        </a>

                        <!-- Logout -->
                        <form method="POST" action="{{ route('logout') }}" class="border-t border-base-300">
                            @csrf
                            <button type="submit" class="w-full text-left block px-4 py-2 text-sm text-base-content bg-base-200 hover:bg-base-300">
                                <div class="flex items-center">
                                    <i class="fa-sharp fa-solid fa-sign-out-alt mr-2 text-base-content opacity-60"></i>
                                    <span>Log Out</span>
                                </div>
                            </button>
                        </form>
                    </x-slot>
                </x-user-dropdown>
            </div>
        </div>
    </div>
    <!-- Breadcrumbs Row -->
    @if(!empty($breadcrumbs))
        <div class="bg-base-100 border-t border-base-300" id="breadcrumbsRow">
            <div class="px-4 lg:px-6">
                <div class="breadcrumbs text-sm">
                    <ul>
                        @foreach($breadcrumbs as $item)
                            <li>
                                @if(isset($item['route']) && $item['route'])
                                    @php
                                        $routeUrl = isset($item['params'])
                                            ? route($item['route'], $item['params'])
                                            : route($item['route']);
                                    @endphp
                                    <a href="{{ $routeUrl }}" class="text-primary hover:text-primary-focus transition-colors duration-200 flex items-center gap-2">
                                        @if(isset($item['icon']))
                                            <i class="fa-sharp {{ $item['icon'] }} text-xs"></i>
                                        @endif
                                        {{ $item['name'] }}
                                    </a>
                                @else
                                    <span class="text-base-content/70 flex items-center gap-2">
                                        @if(isset($item['icon']))
                                            <i class="fa-sharp {{ $item['icon'] }} text-xs"></i>
                                        @endif
                                        {{ $item['name'] }}
                                    </span>
                                @endif
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
    @endif
    <!-- Action Buttons Row - Both Desktop and Mobile -->
    @if(!empty($filteredActionButtons) || !empty($processedPrimaryButtons))
        <div class="bg-gradient-to-b from-base-100 to-base-300 border-t border-base-300" id="actionButtonsRow">
            <div class="px-4 py-2 lg:px-6">
                <div class="flex gap-2 overflow-x-auto scrollbar-hide mobile-action-buttons justify-start">
                    <!-- Primary Buttons -->
                    @foreach($processedPrimaryButtons as $button)
                        @if(isset($button['type']) && $button['type'] === 'submit')
                            <button type="submit" class="{{ str_replace(['btn-md', 'btn-lg'], 'btn-sm', str_replace('btn-sm', 'btn-xs lg:btn-sm', $button['class'] ?? 'btn btn-primary btn-xs lg:btn-sm gap-1 lg:gap-2')) }} flex-shrink-0">
                                @if(isset($button['icon']))
                                    <i class="{{ $button['icon'] }} text-xs lg:text-sm"></i>
                                @endif
                                <span class="text-xs lg:text-sm">{{ $button['text'] ?? $button['name'] }}</span>
                            </button>
                        @else
                            <a href="{{ $button['route'] }}"
                               class="{{ str_replace(['btn-md', 'btn-lg'], 'btn-sm', str_replace('btn-sm', 'btn-xs lg:btn-sm', $button['class'] ?? 'btn btn-primary btn-xs lg:btn-sm gap-1 lg:gap-2')) }} flex-shrink-0"
                               @if(isset($button['target']))
                                   target="{{ $button['target'] }}"
                               @endif
                               @if(isset($button['confirm']))
                                   onclick="return confirm('{{ $button['confirm'] }}')"
                               @endif>
                                @if(isset($button['icon']))
                                    <i class="{{ $button['icon'] }} text-xs lg:text-sm"></i>
                                @endif
                                <span class="text-xs lg:text-sm">{{ $button['text'] ?? $button['name'] }}</span>
                            </a>
                        @endif
                    @endforeach

                    <!-- Custom Action Buttons -->
                    @foreach($filteredActionButtons as $button)
                        @if(isset($button['onclick']) || isset($button['id']))
                            <button
                               @if(isset($button['id']))
                                   id="{{ $button['id'] }}"
                               @endif
                               class="{{ str_replace(['btn-md', 'btn-lg'], 'btn-sm', str_replace('btn-sm', 'btn-xs lg:btn-sm', $button['class'] ?? 'btn btn-primary btn-xs lg:btn-sm gap-1 lg:gap-2')) }} flex-shrink-0"
                               @if(isset($button['onclick']))
                                   onclick="{{ $button['onclick'] }}"
                               @elseif(isset($button['confirm']))
                                   onclick="return confirm('{{ $button['confirm'] }}')"
                               @endif>
                                @if(isset($button['icon']))
                                    <i class="{{ $button['icon'] }} text-xs lg:text-sm"></i>
                                @endif
                                <span class="text-xs lg:text-sm">{{ $button['name'] }}</span>
                            </button>
                        @else
                            <a href="{{ $button['route'] }}"
                               class="{{ str_replace(['btn-md', 'btn-lg'], 'btn-sm', str_replace('btn-sm', 'btn-xs lg:btn-sm', $button['class'] ?? 'btn btn-primary btn-xs lg:btn-sm gap-1 lg:gap-2')) }} flex-shrink-0"
                               @if(isset($button['target']))
                                   target="{{ $button['target'] }}"
                               @endif
                               @if(isset($button['confirm']))
                                   onclick="return confirm('{{ $button['confirm'] }}')"
                               @endif>
                                @if(isset($button['icon']))
                                    <i class="{{ $button['icon'] }} text-xs lg:text-sm"></i>
                                @endif
                                <span class="text-xs lg:text-sm">{{ $button['name'] }}</span>
                            </a>
                        @endif
                    @endforeach
                </div>
            </div>
        </div>
    @endif


</header>

@push('styles')
<style>
    /* Sticky header styles */
    #stickyHeader {
        position: sticky;
        top: 0;
    }

    /* Hide header on scroll down, show on scroll up */
    .header-hidden {
        transform: translateY(-100%);
    }

    /* Mobile dropdown menu styles */
    @media (max-width: 1023px) {
        #stickyHeader {
            position: sticky;
            top: 0;
        }
    }

    /* Hide scrollbar for mobile action buttons */
    .scrollbar-hide {
        -ms-overflow-style: none;  /* Internet Explorer 10+ */
        scrollbar-width: none;  /* Firefox */
    }
    .scrollbar-hide::-webkit-scrollbar {
        display: none;  /* Safari and Chrome */
    }

    /* Ensure smooth scrolling for mobile buttons */
    .scrollbar-hide {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    /* Mobile action buttons container adjustments */
    @media (max-width: 1023px) {
        /* Ensure mobile action buttons have proper spacing and sizing */
        .mobile-action-buttons .btn-xs {
            white-space: nowrap;
            min-height: 1.75rem;
            height: 1.75rem;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
            font-size: 0.75rem;
            line-height: 1rem;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Connect the header toggle button to the sidebar
        const headerToggleBtn = document.getElementById('headerSidebarToggle');
        if (headerToggleBtn) {
            // This will be handled by the drawer-menu component's script
            // We just need to make sure the button exists and is accessible
        }

        // Desktop notifications functionality
        const notificationsToggle = document.getElementById('desktopNotificationsToggle');
        const notificationsDropdown = document.getElementById('notificationsDropdown');
        const notificationBadge = document.getElementById('notificationBadge');
        const notificationCount = document.getElementById('notificationCount');
        const notificationsContent = document.getElementById('notificationsContent');
        const dismissAllBtn = document.getElementById('dismissAllBtn');

        if (notificationsToggle && notificationsDropdown) {
            // Load notifications on page load
            loadNotifications();

            // Load notification count periodically
            setInterval(loadNotificationCount, 30000); // Every 30 seconds

            notificationsToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                const isHidden = notificationsDropdown.classList.contains('hidden');
                notificationsDropdown.classList.toggle('hidden');

                // Load notifications when opening dropdown
                if (isHidden) {
                    loadNotifications();
                }
            });

            // Close notifications dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!notificationsToggle.contains(e.target) && !notificationsDropdown.contains(e.target)) {
                    notificationsDropdown.classList.add('hidden');
                }
            });

            // Dismiss all notifications
            if (dismissAllBtn) {
                dismissAllBtn.addEventListener('click', function() {
                    dismissAllNotifications();
                });
            }
        }

        // Load notification count
        function loadNotificationCount() {
            fetch('/api/notifications/count', {
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                updateNotificationBadge(data.unread_count);
            })
            .catch(error => {
                console.error('Error loading notification count:', error);
            });
        }

        // Load notifications
        function loadNotifications() {
            if (!notificationsContent) {
                console.error('Notifications content container not found');
                return;
            }

            notificationsContent.innerHTML = `
                <div class="p-4 text-center text-base-content/70">
                    <span class="loading loading-spinner loading-sm"></span>
                    <p class="mt-2">Loading notifications...</p>
                </div>
            `;

            fetch('/api/notifications', {
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data && typeof data === 'object') {
                    updateNotificationBadge(data.unread_count || 0);
                    renderNotifications(data.notifications || []);
                } else {
                    throw new Error('Invalid response format');
                }
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
                notificationsContent.innerHTML = `
                    <div class="p-4 text-center text-error">
                        <i class="fa-sharp fa-exclamation-triangle text-2xl mb-2"></i>
                        <p>Failed to load notifications</p>
                        <button class="btn btn-xs btn-ghost mt-2" onclick="loadNotifications()">Try Again</button>
                    </div>
                `;
            });
        }

        // Update notification badge
        function updateNotificationBadge(count) {
            if (count > 0) {
                notificationBadge.classList.remove('hidden');
                notificationBadge.classList.add('flex');
                notificationCount.textContent = count > 99 ? '99+' : count;
            } else {
                notificationBadge.classList.add('hidden');
                notificationBadge.classList.remove('flex');
            }
        }

        // Render notifications
        function renderNotifications(notifications) {
            if (!Array.isArray(notifications)) {
                console.error('Invalid notifications data:', notifications);
                notificationsContent.innerHTML = `
                    <div class="p-4 text-center text-error">
                        <i class="fa-sharp fa-exclamation-triangle text-2xl mb-2"></i>
                        <p>Error processing notifications</p>
                    </div>
                `;
                return;
            }

            if (notifications.length === 0) {
                notificationsContent.innerHTML = `
                    <div class="p-4 text-center text-base-content/70">
                        <i class="fa-sharp fa-bell text-2xl mb-2"></i>
                        <p>No new notifications</p>
                        <p class="text-sm">You're all caught up!</p>
                    </div>
                `;
                return;
            }

            try {
                const notificationsHtml = notifications.map(notification => {
                    // Validate notification object
                    if (!notification || typeof notification !== 'object') {
                        console.warn('Invalid notification object:', notification);
                        return '';
                    }

                    // Provide default values for missing properties
                    const id = notification.id || '';
                    const title = notification.title || 'Untitled';
                    const message = notification.message || '';
                    const urgencyIcon = notification.urgency_icon || 'fa-info-circle';
                    const urgency = notification.urgency || 'normal';
                    const isRead = notification.is_read || false;
                    const isDismissible = notification.is_dismissible !== undefined ? notification.is_dismissible : true;
                    const createdAt = notification.created_at || 'Unknown';
                    const linkUrl = notification.link_url || '';
                    const linkText = notification.link_text || 'View';

                    const urgencyColor = urgency === 'critical' ? 'error' : 
                                       (urgency === 'high' ? 'warning' : 
                                       (urgency === 'normal' ? 'success' : 'info'));

                    return `
                        <div class="notification-item border-b border-base-300 p-3 hover:bg-base-200 transition-colors ${isRead ? 'opacity-75' : ''}" data-notification-id="${id}">
                            <div class="flex items-start gap-3">
                                <div class="flex-shrink-0 mt-1">
                                    <i class="fa-sharp ${urgencyIcon} text-${urgencyColor}"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between">
                                        <h4 class="font-medium text-sm text-base-content truncate">${title}</h4>
                                        <div class="flex items-center gap-1 ml-2">
                                            ${!isRead ? '<div class="w-2 h-2 bg-primary rounded-full"></div>' : ''}
                                            ${isDismissible ? `<button class="btn btn-ghost btn-xs text-base-content/50 hover:text-base-content dismiss-btn" data-notification-id="${id}"><i class="fa-sharp fa-solid fa-times text-xs"></i></button>` : ''}
                                        </div>
                                    </div>
                                    <p class="text-xs text-base-content/70 mt-1">${message}</p>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-base-content/50">${createdAt}</span>
                                        ${linkUrl ? `<a href="${linkUrl}" class="text-xs text-primary hover:text-primary-focus">${linkText}</a>` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }).filter(html => html !== '').join('');

                notificationsContent.innerHTML = notificationsHtml;

                // Add click handlers for notifications
                notificationsContent.querySelectorAll('.notification-item').forEach(item => {
                    const notificationId = item.dataset.notificationId;
                    const notification = notifications.find(n => n.id == notificationId);

                    if (notification) {
                        item.addEventListener('click', function(e) {
                            if (e.target.closest('.dismiss-btn')) return;

                            // Mark as read if not already read
                            if (!notification.is_read) {
                                markNotificationAsRead(notificationId);
                            }

                            // Navigate to link if available
                            if (notification.link_url) {
                                window.location.href = notification.link_url;
                            }
                        });
                    }
                });

                // Add click handlers for dismiss buttons
                notificationsContent.querySelectorAll('.dismiss-btn').forEach(btn => {
                    btn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const notificationId = this.dataset.notificationId;
                        if (notificationId) {
                            dismissNotification(notificationId);
                        }
                    });
                });
            } catch (error) {
                console.error('Error rendering notifications:', error);
                notificationsContent.innerHTML = `
                    <div class="p-4 text-center text-error">
                        <i class="fa-sharp fa-exclamation-triangle text-2xl mb-2"></i>
                        <p>Error displaying notifications</p>
                        <button class="btn btn-xs btn-ghost mt-2" onclick="loadNotifications()">Reload</button>
                    </div>
                `;
            }
        }

        // Mark notification as read
        function markNotificationAsRead(notificationId) {
            fetch(`/api/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadNotificationCount();
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
            });
        }

        // Dismiss notification
        function dismissNotification(notificationId) {
            fetch(`/api/notifications/${notificationId}/dismiss`, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Add delay to avoid race condition and ensure backend has processed the dismissal
                    setTimeout(() => {
                        loadNotifications();
                    }, 100);
                }
            })
            .catch(error => {
                console.error('Error dismissing notification:', error);
                // Show user-friendly error message
                const toast = document.createElement('div');
                toast.className = 'toast toast-top toast-center';
                toast.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fa-sharp fa-exclamation-triangle"></i>
                        <span>Failed to dismiss notification. Please try again.</span>
                    </div>
                `;
                document.body.appendChild(toast);
                setTimeout(() => toast.remove(), 3000);
            });
        }

        // Dismiss all notifications
        function dismissAllNotifications() {
            fetch('/api/notifications/dismiss-all', {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Add delay to avoid race condition and ensure backend has processed all dismissals
                    setTimeout(() => {
                        loadNotifications();
                    }, 150);

                    // Show success message
                    const toast = document.createElement('div');
                    toast.className = 'toast toast-top toast-center';
                    toast.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fa-sharp fa-check-circle"></i>
                            <span>${data.message}</span>
                        </div>
                    `;
                    document.body.appendChild(toast);
                    setTimeout(() => toast.remove(), 3000);
                }
            })
            .catch(error => {
                console.error('Error dismissing all notifications:', error);
                // Show user-friendly error message
                const toast = document.createElement('div');
                toast.className = 'toast toast-top toast-center';
                toast.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fa-sharp fa-exclamation-triangle"></i>
                        <span>Failed to dismiss notifications. Please try again.</span>
                    </div>
                `;
                document.body.appendChild(toast);
                setTimeout(() => toast.remove(), 3000);
            });
        }

        // Function to adjust header position based on sidebar state
        function adjustHeaderPosition() {
            const sidebar = document.getElementById('sidebar');
            const header = document.getElementById('stickyHeader');

            if (window.innerWidth >= 1024 && sidebar && header) { // Desktop only
                const isCollapsed = sidebar.getAttribute('data-collapsed') === 'true';

                if (isCollapsed) {
                    header.style.marginLeft = '64px';
                    header.style.width = 'calc(100% - 64px)';
                } else {
                    header.style.marginLeft = '256px';
                    header.style.width = 'calc(100% - 256px)';
                }


            } else {
                // Reset on mobile
                if (header) {
                    header.style.marginLeft = '';
                    header.style.width = '';
                }

            }
        }

        // Listen for sidebar collapse events by observing sidebar attribute changes
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'data-collapsed') {
                        setTimeout(adjustHeaderPosition, 300); // Delay for animation
                    }
                });
            });
            observer.observe(sidebar, { attributes: true });
        }

        // Adjust on window resize
        window.addEventListener('resize', adjustHeaderPosition);

        // Initial adjustment
        setTimeout(adjustHeaderPosition, 100);

        // Sticky header that hides on scroll down, shows on scroll up
        const header = document.getElementById('stickyHeader');
        if (header) {
            let lastScrollTop = 0;
            let headerHeight = header.offsetHeight;
            // Make this variable accessible globally so the sidebar toggle can access it
            window.isHeaderVisible = true;

            // Only apply sticky behavior on mobile
            function updateHeaderPosition() {
                if (window.innerWidth < 1024) { // lg breakpoint
                    const bannerContainer = document.getElementById('bannerContainer');
                    const bannerHeight = bannerContainer ? bannerContainer.offsetHeight : 0;

                    header.style.position = 'fixed';
                    header.style.top = bannerHeight + 'px';
                    document.body.style.paddingTop = (headerHeight + bannerHeight) + 'px';

                    // Make sure banner stays at the top
                    if (bannerContainer) {
                        bannerContainer.style.position = 'fixed';
                        bannerContainer.style.top = '0';
                        bannerContainer.style.left = '0';
                        bannerContainer.style.width = '100%';
                        bannerContainer.style.zIndex = '50';
                    }
                } else {
                    header.style.position = '';
                    header.style.top = '';
                    header.style.transform = '';
                    document.body.style.paddingTop = '0';

                    // Reset banner positioning
                    const bannerContainer = document.getElementById('bannerContainer');
                    if (bannerContainer) {
                        bannerContainer.style.position = '';
                        bannerContainer.style.top = '';
                        bannerContainer.style.left = '';
                        bannerContainer.style.width = '';
                        bannerContainer.style.zIndex = '';
                    }
                }
            }

            // Initial setup
            updateHeaderPosition();

            // Handle scroll events
            window.addEventListener('scroll', () => {
                if (window.innerWidth < 1024) { // Only on mobile
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const bannerContainer = document.getElementById('bannerContainer');
                    const bannerHeight = bannerContainer ? bannerContainer.offsetHeight : 0;

                    // Check if the sidebar is open
                    const sidebar = document.getElementById('sidebar');
                    const isSidebarOpen = sidebar && !sidebar.classList.contains('-translate-x-full');

                    // Only apply scroll behavior if sidebar is closed
                    // When sidebar is open, the drawer menu component handles header visibility
                    if (!isSidebarOpen) {
                        // Determine scroll direction
                        if (scrollTop > lastScrollTop && scrollTop > headerHeight) {
                            // Scrolling down & past header height
                            if (window.isHeaderVisible) {
                                header.style.transform = 'translateY(-100%)';
                                window.isHeaderVisible = false;

                                // Also hide the banner if it exists
                                if (bannerContainer) {
                                    bannerContainer.style.transform = 'translateY(-100%)';
                                }

                                // Update sidebar and toggle button positions
                                if (typeof adjustMobileElements === 'function') {
                                    adjustMobileElements();
                                }
                            }
                        } else {
                            // Scrolling up
                            if (!window.isHeaderVisible) {
                                header.style.transform = 'translateY(0)';
                                window.isHeaderVisible = true;

                                // Show the banner again if it exists
                                if (bannerContainer) {
                                    bannerContainer.style.transform = 'translateY(0)';
                                }

                                // Update sidebar and toggle button positions
                                if (typeof adjustMobileElements === 'function') {
                                    adjustMobileElements();
                                }
                            }
                        }
                    }

                    lastScrollTop = scrollTop;
                }
            });

            // Update on resize
            window.addEventListener('resize', () => {
                headerHeight = header.offsetHeight;
                updateHeaderPosition();
            });

            // Update header height when breadcrumbs are present
            const breadcrumbsRow = document.getElementById('breadcrumbsRow');
            if (breadcrumbsRow) {
                // Recalculate header height to include breadcrumbs
                headerHeight = header.offsetHeight;
            }
        }
    });
</script>
@endpush
