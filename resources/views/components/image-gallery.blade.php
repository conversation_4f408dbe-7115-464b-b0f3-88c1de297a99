@props([
    'images' => [],
    'contextType' => null,
    'contextId' => null,
    'showReorder' => true,
    'showDelete' => true,
    'showDownload' => true,
    'columns' => 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5',
    'title' => 'Images',
    'emptyMessage' => 'No images uploaded yet',
    'allowReorder' => true,
])

@php
    $galleryId = 'gallery-' . uniqid();
    $lightboxId = 'lightbox-' . uniqid();
@endphp

<div class="image-gallery-component" id="{{ $galleryId }}">
    @if($title)
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-base-content flex items-center gap-2">
                <i class="fa-sharp fa-images text-primary"></i>
                {{ $title }}
                @if(count($images) > 0)
                    <span class="badge badge-primary">{{ count($images) }}</span>
                @endif
            </h3>
            
            @if(count($images) > 0 && $showDownload)
                <button type="button" class="btn btn-outline btn-sm download-all-btn">
                    <i class="fa-sharp fa-download"></i>
                    Download All
                </button>
            @endif
        </div>
    @endif

    @if(count($images) > 0)
        <div class="image-grid grid {{ $columns }} gap-4" 
             data-context-type="{{ $contextType }}" 
             data-context-id="{{ $contextId }}"
             data-allow-reorder="{{ $allowReorder ? 'true' : 'false' }}">
            @foreach($images as $image)
                <div class="image-item relative group cursor-pointer" 
                     data-image-id="{{ $image['id'] ?? $image->id ?? '' }}"
                     data-image-url="{{ $image['full_url'] ?? $image['fullSrc'] ?? $image['src'] ?? '' }}"
                     data-image-title="{{ $image['title'] ?? $image['name'] ?? $image['alt'] ?? 'Image' }}">
                     
                    <!-- Image Container -->
                    <div class="aspect-square bg-base-200 rounded-lg overflow-hidden">
                        <img src="{{ $image['thumbnail_url'] ?? $image['src'] ?? '' }}" 
                             alt="{{ $image['alt'] ?? $image['title'] ?? $image['name'] ?? 'Image' }}" 
                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                             loading="lazy">
                        
                        <!-- Hover overlay -->
                        <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200 rounded-lg flex items-center justify-center">
                            <i class="fa-sharp fa-search-plus text-white opacity-0 group-hover:opacity-100 transition-opacity text-xl"></i>
                        </div>
                    </div>
                    
                    <!-- Action buttons -->
                    <div class="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        @if($showDownload)
                            <button type="button" 
                                    class="btn btn-circle btn-xs btn-info download-single-btn"
                                    data-image-id="{{ $image['id'] ?? $image->id ?? '' }}"
                                    title="Download">
                                <i class="fa-sharp fa-download text-xs"></i>
                            </button>
                        @endif
                        
                        @if($showDelete)
                            <button type="button" 
                                    class="btn btn-circle btn-xs btn-error delete-image-btn"
                                    data-image-id="{{ $image['id'] ?? $image->id ?? '' }}"
                                    title="Delete">
                                <i class="fa-sharp fa-trash text-xs"></i>
                            </button>
                        @endif
                    </div>
                    
                    <!-- Reorder handle -->
                    @if($allowReorder && $showReorder)
                        <div class="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <div class="btn btn-circle btn-xs btn-ghost reorder-handle cursor-move" title="Drag to reorder">
                                <i class="fa-sharp fa-grip-vertical text-xs"></i>
                            </div>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
        
        @if($allowReorder && count($images) > 1)
            <p class="text-sm text-base-content/60 mt-3">
                <i class="fa-sharp fa-info-circle mr-1"></i>
                Drag images to reorder them
            </p>
        @endif
    @else
        <div class="text-center text-base-content/70 py-12">
            <div class="flex flex-col items-center gap-4">
                <div class="avatar avatar-placeholder">
                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                        <i class="fa-sharp fa-images text-2xl"></i>
                    </div>
                </div>
                <div class="text-center">
                    <h3 class="text-lg font-medium text-base-content/80">{{ $emptyMessage }}</h3>
                    <p class="text-sm text-base-content/60 mt-1">Upload some images to get started</p>
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Lightbox Modal -->
<div id="{{ $lightboxId }}" class="lightbox-modal fixed inset-0 bg-black/80 backdrop-blur-sm z-50 hidden items-center justify-center p-4">
    <div class="relative max-w-7xl max-h-full">
        <!-- Close button -->
        <button class="absolute -top-12 right-0 text-white text-2xl hover:text-gray-300 z-10 lightbox-close">
            <i class="fa-sharp fa-times"></i>
        </button>
        
        <!-- Navigation buttons -->
        <button class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white text-3xl hover:text-gray-300 z-10 lightbox-prev">
            <i class="fa-sharp fa-chevron-left"></i>
        </button>
        <button class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white text-3xl hover:text-gray-300 z-10 lightbox-next">
            <i class="fa-sharp fa-chevron-right"></i>
        </button>
        
        <!-- Image container -->
        <div class="lightbox-content relative">
            <img class="lightbox-image max-w-full max-h-[90vh] object-contain rounded-lg shadow-lg" src="" alt="">
            
            <!-- Image info -->
            <div class="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-center p-3 rounded-b-lg">
                <div class="lightbox-title font-medium"></div>
                <div class="lightbox-counter text-sm opacity-75 mt-1"></div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .image-gallery-component .image-item {
        transition: transform 0.2s ease;
    }
    
    .image-gallery-component .image-item:hover {
        transform: translateY(-2px);
    }
    
    .lightbox-modal {
        animation: fadeIn 0.3s ease;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    .lightbox-image {
        animation: zoomIn 0.3s ease;
    }
    
    @keyframes zoomIn {
        from { transform: scale(0.9); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
    }
    
    /* Sortable.js styles */
    .sortable-ghost {
        opacity: 0.4;
    }
    
    .sortable-chosen {
        transform: scale(1.05);
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const gallery = document.getElementById('{{ $galleryId }}');
    const lightbox = document.getElementById('{{ $lightboxId }}');
    
    if (!gallery) return;
    
    let currentImageIndex = 0;
    let images = [];
    
    // Initialize sortable if reordering is allowed
    @if($allowReorder && count($images) > 1)
    const imageGrid = gallery.querySelector('.image-grid');
    if (imageGrid && typeof Sortable !== 'undefined') {
        new Sortable(imageGrid, {
            animation: 150,
            handle: '.reorder-handle',
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            onEnd: function(evt) {
                // Get new order
                const imageItems = imageGrid.querySelectorAll('.image-item');
                const imageIds = Array.from(imageItems).map(item => item.dataset.imageId);
                
                // Send reorder request
                fetch('/images/reorder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        image_ids: imageIds
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('Images reordered successfully');
                    } else {
                        console.error('Failed to reorder images:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error reordering images:', error);
                    // Revert the UI change
                    window.location.reload();
                });
            }
        });
    }
    @endif
    
    // Initialize lightbox
    function initLightbox() {
        const imageItems = gallery.querySelectorAll('.image-item');
        images = Array.from(imageItems).map(item => ({
            url: item.dataset.imageUrl,
            title: item.dataset.imageTitle,
            id: item.dataset.imageId
        }));
        
        // Add click listeners to image items
        imageItems.forEach((item, index) => {
            item.addEventListener('click', function(e) {
                // Don't open lightbox if clicking on action buttons
                if (e.target.closest('.download-single-btn, .delete-image-btn, .reorder-handle')) {
                    return;
                }
                
                currentImageIndex = index;
                openLightbox();
            });
        });
    }
    
    function openLightbox() {
        if (images.length === 0) return;
        
        updateLightboxImage();
        lightbox.classList.remove('hidden');
        lightbox.classList.add('flex');
        
        // Prevent body scrolling
        document.body.style.overflow = 'hidden';
    }
    
    function closeLightbox() {
        lightbox.classList.add('hidden');
        lightbox.classList.remove('flex');
        
        // Restore body scrolling
        document.body.style.overflow = '';
    }
    
    function updateLightboxImage() {
        const currentImage = images[currentImageIndex];
        if (!currentImage) return;
        
        const lightboxImage = lightbox.querySelector('.lightbox-image');
        const lightboxTitle = lightbox.querySelector('.lightbox-title');
        const lightboxCounter = lightbox.querySelector('.lightbox-counter');
        
        lightboxImage.src = currentImage.url;
        lightboxImage.alt = currentImage.title;
        lightboxTitle.textContent = currentImage.title;
        lightboxCounter.textContent = `${currentImageIndex + 1} of ${images.length}`;
        
        // Show/hide navigation buttons
        const prevBtn = lightbox.querySelector('.lightbox-prev');
        const nextBtn = lightbox.querySelector('.lightbox-next');
        
        prevBtn.style.display = currentImageIndex > 0 ? 'block' : 'none';
        nextBtn.style.display = currentImageIndex < images.length - 1 ? 'block' : 'none';
    }
    
    function navigateLightbox(direction) {
        if (direction === 'prev' && currentImageIndex > 0) {
            currentImageIndex--;
        } else if (direction === 'next' && currentImageIndex < images.length - 1) {
            currentImageIndex++;
        }
        updateLightboxImage();
    }
    
    // Lightbox event listeners
    lightbox.querySelector('.lightbox-close').addEventListener('click', closeLightbox);
    lightbox.querySelector('.lightbox-prev').addEventListener('click', () => navigateLightbox('prev'));
    lightbox.querySelector('.lightbox-next').addEventListener('click', () => navigateLightbox('next'));
    
    // Close lightbox when clicking outside image
    lightbox.addEventListener('click', function(e) {
        if (e.target === lightbox) {
            closeLightbox();
        }
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (lightbox.classList.contains('hidden')) return;
        
        switch(e.key) {
            case 'Escape':
                closeLightbox();
                break;
            case 'ArrowLeft':
                navigateLightbox('prev');
                break;
            case 'ArrowRight':
                navigateLightbox('next');
                break;
        }
    });
    
    // Delete image functionality
    gallery.addEventListener('click', function(e) {
        if (e.target.closest('.delete-image-btn')) {
            const btn = e.target.closest('.delete-image-btn');
            const imageId = btn.dataset.imageId;
            
            if (confirm('Are you sure you want to delete this image?')) {
                deleteImage(imageId);
            }
        }
    });
    
    function deleteImage(imageId) {
        fetch(`/images/${imageId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the image item from DOM
                const imageItem = gallery.querySelector(`[data-image-id="${imageId}"]`);
                if (imageItem) {
                    imageItem.remove();
                    // Reinitialize lightbox
                    initLightbox();
                }
            } else {
                alert('Failed to delete image');
            }
        })
        .catch(error => {
            console.error('Error deleting image:', error);
            alert('Failed to delete image');
        });
    }
    
    // Download functionality
    gallery.addEventListener('click', function(e) {
        if (e.target.closest('.download-single-btn')) {
            const btn = e.target.closest('.download-single-btn');
            const imageId = btn.dataset.imageId;
            downloadImage(imageId);
        }
    });
    
    gallery.querySelector('.download-all-btn')?.addEventListener('click', function() {
        downloadAllImages();
    });
    
    function downloadImage(imageId) {
        // Images might be stored as files, so use the file download route
        // The Image model should handle the conversion from image ID to file ID
        window.location.href = `/images/${imageId}/download`;
    }
    
    function downloadAllImages() {
        const contextType = gallery.querySelector('.image-grid')?.dataset.contextType;
        const contextId = gallery.querySelector('.image-grid')?.dataset.contextId;
        
        if (contextType && contextId) {
            // Special handling for inventory context
            if (contextType === 'inventory') {
                window.open(`/inventory/${contextId}/photos/download`, '_blank');
            } else {
                window.open(`/images/download/${contextType}/${contextId}`, '_blank');
            }
        }
    }
    
    // Initialize
    initLightbox();
});
</script>
@endpush