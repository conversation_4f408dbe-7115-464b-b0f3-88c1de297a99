@props([
    'id' => 'address',
    'name' => 'address',
    'value' => '',
    'placeholder' => 'Enter address...',
    'required' => false,
    'class' => '',
    'rows' => 3,
    'type' => 'textarea', // 'input' or 'textarea'
    'label' => 'Address',
    'icon' => 'fa-map-marker-alt',
    'iconColor' => 'text-primary',
    'restrictions' => null, // Country restrictions, e.g., ['us', 'ca']
    'showDisplayMode' => false, // Disabled for guest forms
])

@php
    $inputClass = $type === 'textarea'
        ? 'textarea textarea-bordered w-full ' . $class
        : 'input input-bordered w-full ' . $class;

    $uniqueId = $id . '_' . uniqid();
    $currentValue = old($name, $value);
@endphp

<div id="{{ $uniqueId }}_container">
    <!-- Address Container -->
    <div id="{{ $uniqueId }}_address_container" class="relative">
        <!-- Address Input Mode (always shown for guest forms) -->
        <div id="{{ $uniqueId }}_input_container" class="relative">
            @if($type === 'textarea')
                <textarea
                    name="{{ $name }}"
                    id="{{ $uniqueId }}"
                    rows="{{ $rows }}"
                    class="{{ $inputClass }}"
                    placeholder="{{ $placeholder }}"
                    @if($required) required @endif
                >{{ $currentValue }}</textarea>
                <!-- Loading indicator for textarea -->
                <div id="{{ $uniqueId }}_loading" class="absolute right-2 top-2 hidden z-10 bg-base-100/90 backdrop-blur-sm rounded-lg p-2 shadow-lg border border-base-300/50">
                    <div class="flex items-center gap-2">
                        <span class="loading loading-bars loading-md text-primary"></span>
                        <span class="text-xs text-base-content/70">Searching addresses...</span>
                    </div>
                </div>
                <!-- Disable autocomplete button for textarea -->
                <div id="{{ $uniqueId }}_disable_container" class="absolute right-2 bottom-2 hidden z-10">
                    <button type="button" id="{{ $uniqueId }}_disable_btn" class="btn btn-xs btn-outline btn-primary gap-1" title="Disable address suggestions">
                        <i class="fa-sharp fa-times text-xs"></i>
                        Manual Entry
                    </button>
                </div>
            @else
                <label class="input input-bordered w-full {{ $class }} flex items-center gap-2">
                    <i class="fa-sharp fa-location-crosshairs text-primary"></i>
                    <input
                        type="text"
                        name="{{ $name }}"
                        id="{{ $uniqueId }}"
                        class="grow"
                        value="{{ $currentValue }}"
                        placeholder="{{ $placeholder }}"
                        @if($required) required @endif
                    />
                    <!-- Loading indicator -->
                    <span id="{{ $uniqueId }}_loading" class="loading loading-bars loading-sm hidden text-primary"></span>
                    <!-- Disable autocomplete button -->
                    <button type="button" id="{{ $uniqueId }}_disable_btn" class="btn btn-xs btn-outline btn-primary gap-1 hidden" title="Disable address suggestions">
                        <i class="fa-sharp fa-times text-xs"></i>
                        Manual
                    </button>
                </label>
            @endif
        </div>
    </div>

    @error($name)
    <div class="label">
        <span class="label-text-alt text-error flex items-center gap-1">
            <i class="fa-sharp fa-exclamation-triangle"></i>
            {{ $message }}
        </span>
    </div>
    @enderror
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const container = document.getElementById('{{ $uniqueId }}_container');
    const addressContainer = document.getElementById('{{ $uniqueId }}_address_container');
    let inputContainer = document.getElementById('{{ $uniqueId }}_input_container');
    let input = document.getElementById('{{ $uniqueId }}');
    let loadingIndicator = document.getElementById('{{ $uniqueId }}_loading');

    if (!input) return;

    // Store session token for billing optimization
    let sessionToken = null;

    async function initializeGuestAutocomplete() {
        // Get the current input element
        let currentInput = document.getElementById('{{ $uniqueId }}');

        if (!currentInput || currentInput.type === 'hidden') {
            return;
        }

        // Check if element is visible (not in a hidden parent)
        if (currentInput.offsetParent === null && currentInput.style.display !== 'none') {
            setTimeout(initializeGuestAutocomplete, 100);
            return;
        }

        // Update the global input reference to the current input
        input = currentInput;

        // Check if this input already has autocomplete initialized
        if (currentInput.dataset.autocompleteInitialized === 'true') {
            return;
        }

        try {
            // Generate initial session token
            if (!sessionToken) {
                await generateSessionToken();
            }

            // Create a dropdown container for suggestions
            const suggestionsContainer = document.createElement('div');
            suggestionsContainer.id = '{{ $uniqueId }}_suggestions';
            suggestionsContainer.className = 'google-maps-autocomplete-suggestions absolute top-full left-0 right-0 hidden';
            suggestionsContainer.style.zIndex = '99999';
            suggestionsContainer.setAttribute('tabindex', '-1'); // Remove from tab order

            // Make sure the input container has relative positioning
            const inputContainer = currentInput.parentNode;
            if (inputContainer) {
                inputContainer.classList.add('google-maps-autocomplete-container');
                if (!inputContainer.style.position) {
                    inputContainer.style.position = 'relative';
                }
            }

            // Add CSS class to input for styling
            currentInput.classList.add('google-maps-autocomplete-input');

            // Always insert suggestions container immediately after the input element
            currentInput.parentNode.insertBefore(suggestionsContainer, currentInput.nextSibling);

            // Variables for managing autocomplete state
            let currentSuggestions = [];
            let selectedIndex = -1;
            let requestId = 0;
            let isSettingValueProgrammatically = false;
            let autocompleteDisabled = false;
            
            // Get disable button and container
            const disableBtn = document.getElementById('{{ $uniqueId }}_disable_btn');
            const disableContainer = document.getElementById('{{ $uniqueId }}_disable_container');

            // Function to generate a new session token
            async function generateSessionToken() {
                try {
                    const response = await fetch('/api/google-maps/guest/session-token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            sessionToken = data.sessionToken;
                        }
                    }
                } catch (error) {
                    console.error('Failed to generate session token:', error);
                }
            }

            // Function to make autocomplete requests using server-side API
            async function makeAutocompleteRequest(inputValue) {
                if (!inputValue || inputValue.length < 2 || autocompleteDisabled) {
                    suggestionsContainer.classList.add('hidden');
                    currentSuggestions = [];
                    return;
                }
                
                // Show disable button when autocomplete is active
                if (disableBtn && inputValue.length >= 2) {
                    disableBtn.classList.remove('hidden');
                    if (disableContainer) {
                        disableContainer.classList.remove('hidden');
                    }
                }

                const currentRequestId = ++requestId;

                // Show loading indicator
                const loadingIndicator = document.getElementById('{{ $uniqueId }}_loading');
                if (loadingIndicator) {
                    loadingIndicator.classList.remove('hidden');
                }

                // Prepare the request for server-side API
                const requestData = {
                    input: inputValue.trim(),
                    sessionToken: sessionToken,
                    @if($restrictions)
                    restrictions: @json($restrictions),
                    @endif
                    language: 'en-US',
                    region: 'us'
                };

                // Add location bias if available
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        async (position) => {
                            requestData.origin = {
                                lat: position.coords.latitude,
                                lng: position.coords.longitude
                            };
                            await makeRequest();
                        },
                        async () => {
                            // Geolocation failed, make request without location bias
                            await makeRequest();
                        },
                        { timeout: 2000 }
                    );
                } else {
                    await makeRequest();
                }

                async function makeRequest() {
                    try {

                        // Make request to our guest server-side API
                        const response = await fetch('/api/google-maps/guest/autocomplete', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                            },
                            body: JSON.stringify(requestData)
                        });

                        const data = await response.json();

                        // Hide loading indicator
                        if (loadingIndicator) {
                            loadingIndicator.classList.add('hidden');
                        }

                        // Check if this request is still the latest
                        if (currentRequestId !== requestId) {
                            return;
                        }

                        if (data.success && data.suggestions && data.suggestions.length > 0) {
                            currentSuggestions = data.suggestions;
                            selectedIndex = -1;
                            displaySuggestions(data.suggestions);

                            // Update session token if provided
                            if (data.sessionToken) {
                                sessionToken = data.sessionToken;
                            }
                        } else {
                            currentSuggestions = [];
                            suggestionsContainer.innerHTML = `
                                <div class="px-4 py-3 text-sm text-base-content/60">
                                    <i class="fa-sharp fa-search mr-2"></i>
                                    No addresses found for "${inputValue}"
                                </div>
                            `;
                            suggestionsContainer.classList.remove('hidden');
                        }
                    } catch (error) {
                        console.error('Autocomplete request failed:', error);

                        // Hide loading indicator
                        if (loadingIndicator) {
                            loadingIndicator.classList.add('hidden');
                        }

                        suggestionsContainer.innerHTML = `
                            <div class="px-4 py-3 text-sm text-error">
                                <i class="fa-sharp fa-exclamation-triangle mr-2"></i>
                                Unable to load suggestions. Please try again.
                            </div>
                        `;
                        suggestionsContainer.classList.remove('hidden');

                        // Hide error after 3 seconds
                        setTimeout(() => {
                            suggestionsContainer.classList.add('hidden');
                        }, 3000);
                    }
                }
            }

            // Function to display suggestions
            function displaySuggestions(suggestions) {
                suggestionsContainer.innerHTML = '';

                if (!suggestions || suggestions.length === 0) {
                    suggestionsContainer.classList.add('hidden');
                    return;
                }

                suggestions.forEach((suggestion, index) => {
                    const placePrediction = suggestion.placePrediction;
                    if (!placePrediction) return;

                    const suggestionElement = document.createElement('div');
                    suggestionElement.className = 'px-4 py-3 cursor-pointer hover:bg-base-200 border-b border-base-300 last:border-b-0 flex items-center gap-3 transition-colors duration-150';
                    suggestionElement.dataset.index = index;
                    suggestionElement.setAttribute('tabindex', '-1'); // Remove from tab order

                    // Add location icon
                    const icon = document.createElement('i');
                    icon.className = 'fa-sharp fa-location-dot text-primary text-sm flex-shrink-0';

                    // Add text content
                    const textContainer = document.createElement('div');
                    textContainer.className = 'flex-1 min-w-0';

                    const mainText = document.createElement('div');
                    mainText.className = 'text-sm font-medium text-base-content truncate';
                    mainText.textContent = placePrediction.text.toString();

                    textContainer.appendChild(mainText);

                    // Add place type info if available
                    if (placePrediction.types && placePrediction.types.length > 0) {
                        const typeInfo = document.createElement('div');
                        typeInfo.className = 'text-xs text-base-content/60';
                        const primaryType = placePrediction.types[0].replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                        typeInfo.textContent = primaryType;
                        textContainer.appendChild(typeInfo);
                    }

                    suggestionElement.appendChild(icon);
                    suggestionElement.appendChild(textContainer);

                    suggestionElement.addEventListener('click', async (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        await selectSuggestion(suggestion, index);
                    });

                    suggestionsContainer.appendChild(suggestionElement);
                });

                suggestionsContainer.classList.remove('hidden');
                updateSelectedSuggestion();
            }

            // Function to update visual selection of suggestions
            function updateSelectedSuggestion() {
                const suggestionElements = suggestionsContainer.children;
                for (let i = 0; i < suggestionElements.length; i++) {
                    const element = suggestionElements[i];
                    if (i === selectedIndex) {
                        element.classList.add('bg-primary', 'text-primary-content');
                        element.classList.remove('hover:bg-base-200');
                        // Update icon color for selected item
                        const icon = element.querySelector('i');
                        if (icon) {
                            icon.classList.remove('text-primary');
                            icon.classList.add('text-primary-content');
                        }
                        // Scroll into view if needed
                        element.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
                    } else {
                        element.classList.remove('bg-primary', 'text-primary-content');
                        element.classList.add('hover:bg-base-200');
                        // Reset icon color for unselected items
                        const icon = element.querySelector('i');
                        if (icon) {
                            icon.classList.add('text-primary');
                            icon.classList.remove('text-primary-content');
                        }
                    }
                }
            }

            // Function to select a suggestion using server-side API
            async function selectSuggestion(suggestion, index) {
                try {

                    const placePrediction = suggestion.placePrediction;
                    if (!placePrediction || !placePrediction.placeId) return;

                    // Fetch place details from server-side API

                    let formattedAddress;
                    let place = null;

                    try {
                        const placeDetailsResponse = await fetch('/api/google-maps/guest/place-details', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                            },
                            body: JSON.stringify({
                                placeId: placePrediction.placeId,
                                sessionToken: sessionToken,
                                fields: ['id', 'displayName', 'formattedAddress', 'shortFormattedAddress', 'location', 'addressComponents', 'types', 'primaryType', 'primaryTypeDisplayName']
                            })
                        });


                        if (!placeDetailsResponse.ok) {
                            console.error('Place details API error:', placeDetailsResponse.status, placeDetailsResponse.statusText);
                            throw new Error(`HTTP ${placeDetailsResponse.status}: ${placeDetailsResponse.statusText}`);
                        }

                        const placeDetailsData = await placeDetailsResponse.json();

                        if (placeDetailsData.success && placeDetailsData.place) {
                            place = placeDetailsData.place;

                            // For textarea inputs, construct multi-line address
                            if (currentInput.tagName.toLowerCase() === 'textarea') {
                                formattedAddress = constructMultiLineAddress(place);
                            } else {
                                // For text inputs, use full formatted address (includes zip and state)
                                formattedAddress = place.formattedAddress || place.displayName;
                            }

                        } else {
                            throw new Error(placeDetailsData.error || 'Place details API returned success=false');
                        }
                    } catch (error) {
                        console.error('Error fetching place details:', error);
                        formattedAddress = placePrediction.text;
                    }

                    // Set flag to prevent autocomplete triggering
                    isSettingValueProgrammatically = true;

                    // Update input value
                    currentInput.value = formattedAddress;

                    // Hide suggestions
                    suggestionsContainer.classList.add('hidden');
                    currentSuggestions = [];
                    selectedIndex = -1;

                    // Trigger change events
                    currentInput.dispatchEvent(new Event('change', { bubbles: true }));
                    currentInput.dispatchEvent(new Event('input', { bubbles: true }));

                    // Reset flag after a short delay
                    setTimeout(() => {
                        isSettingValueProgrammatically = false;
                    }, 100);

                    // Generate new session token for next session
                    await generateSessionToken();

                } catch (error) {
                    console.error('Error selecting suggestion:', error);

                    // Fallback to using the prediction text
                    const placePrediction = suggestion.placePrediction;
                    const fallbackAddress = placePrediction ? placePrediction.text : '';

                    if (fallbackAddress) {
                        isSettingValueProgrammatically = true;
                        currentInput.value = fallbackAddress;
                        suggestionsContainer.classList.add('hidden');
                        currentSuggestions = [];
                        selectedIndex = -1;
                        currentInput.dispatchEvent(new Event('change', { bubbles: true }));
                        currentInput.dispatchEvent(new Event('input', { bubbles: true }));
                        setTimeout(() => {
                            isSettingValueProgrammatically = false;
                        }, 100);
                    }
                }
            }

            // Function to construct multi-line address for textarea
            function constructMultiLineAddress(place) {
                let addressLines = [];
                let isBusinessLocation = false;

                // Check if this is a business location
                if (place.displayName && place.displayName.text) {
                    const displayText = place.displayName.text;
                    // Check if display name looks like a business name (not just an address)
                    if (!displayText.match(/^\d+\s+\w+\s+(St|Street|Ave|Avenue|Rd|Road|Dr|Drive|Blvd|Boulevard|Ln|Lane|Ct|Court|Cir|Circle|Way|Pl|Place)/i)) {
                        addressLines.push(displayText);
                        isBusinessLocation = true;
                    }
                }

                // Use full formatted address (includes zip and state) for better detail
                const fullFormattedAddr = place.formattedAddress;
                if (fullFormattedAddr) {
                    addressLines.push(fullFormattedAddr);
                }

                // For business locations, return business name + full address
                // For regular addresses, return only the full address
                return isBusinessLocation ? addressLines.join('\n') : (fullFormattedAddr || place.displayName || '');
            }

            // Setup disable autocomplete functionality
            if (disableBtn) {
                disableBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Toggle autocomplete state
                    autocompleteDisabled = !autocompleteDisabled;
                    
                    if (autocompleteDisabled) {
                        // Disable autocomplete
                        suggestionsContainer.classList.add('hidden');
                        currentSuggestions = [];
                        selectedIndex = -1;
                        
                        // Update button appearance
                        disableBtn.innerHTML = `
                            <i class="fa-sharp fa-check text-xs"></i>
                            Auto Entry
                        `;
                        disableBtn.title = 'Enable address suggestions';
                        disableBtn.classList.remove('btn-outline');
                        disableBtn.classList.add('btn-primary');
                        
                        // Focus back on input
                        currentInput.focus();
                    } else {
                        // Enable autocomplete
                        disableBtn.innerHTML = `
                            <i class="fa-sharp fa-times text-xs"></i>
                            Manual Entry
                        `;
                        disableBtn.title = 'Disable address suggestions';
                        disableBtn.classList.remove('btn-primary');
                        disableBtn.classList.add('btn-outline');
                        
                        // Trigger autocomplete if there's text
                        const inputValue = currentInput.value.trim();
                        if (inputValue.length >= 2) {
                            makeAutocompleteRequest(inputValue);
                        }
                    }
                });
            }

            // Add input event listener for typing
            currentInput.addEventListener('input', function(e) {
                const inputValue = e.target.value.trim();

                // Skip autocomplete if we're setting the value programmatically or autocomplete is disabled
                if (isSettingValueProgrammatically || autocompleteDisabled) {
                    return;
                }

                // Clear previous timeout
                clearTimeout(currentInput.autocompleteTimeout);

                // Hide suggestions and disable button if input is too short
                if (inputValue.length < 2) {
                    suggestionsContainer.classList.add('hidden');
                    currentSuggestions = [];
                    selectedIndex = -1;
                    
                    // Hide disable button
                    if (disableBtn) {
                        disableBtn.classList.add('hidden');
                        if (disableContainer) {
                            disableContainer.classList.add('hidden');
                        }
                    }
                    return;
                }

                // Debounce the autocomplete request
                currentInput.autocompleteTimeout = setTimeout(() => {
                    makeAutocompleteRequest(inputValue);
                }, 300);
            });

            // Add keyboard navigation support
            currentInput.addEventListener('keydown', async function(e) {
                // Only handle navigation when suggestions are visible
                if (suggestionsContainer.classList.contains('hidden') || currentSuggestions.length === 0) {
                    return;
                }

                switch (e.key) {
                    case 'Tab':
                        // Tab selects first suggestion if none selected
                        if (selectedIndex === -1 && currentSuggestions.length > 0) {
                            e.preventDefault();
                            selectedIndex = 0;
                            updateSelectedSuggestion();
                        }
                        // If a suggestion is already selected, Tab should select it
                        else if (selectedIndex >= 0 && selectedIndex < currentSuggestions.length) {
                            e.preventDefault();
                            await selectSuggestion(currentSuggestions[selectedIndex], selectedIndex);
                        }
                        break;

                    case 'ArrowDown':
                        e.preventDefault();
                        if (selectedIndex < currentSuggestions.length - 1) {
                            selectedIndex++;
                        } else {
                            selectedIndex = 0; // Wrap to first
                        }
                        updateSelectedSuggestion();
                        break;

                    case 'ArrowUp':
                        e.preventDefault();
                        if (selectedIndex > 0) {
                            selectedIndex--;
                        } else {
                            selectedIndex = currentSuggestions.length - 1; // Wrap to last
                        }
                        updateSelectedSuggestion();
                        break;

                    case 'Enter':
                        if (selectedIndex >= 0 && selectedIndex < currentSuggestions.length) {
                            e.preventDefault();
                            await selectSuggestion(currentSuggestions[selectedIndex], selectedIndex);
                        }
                        break;

                    case 'Escape':
                        e.preventDefault();
                        suggestionsContainer.classList.add('hidden');
                        currentSuggestions = [];
                        selectedIndex = -1;
                        break;
                }
            });

            // Add click outside listener to hide suggestions
            document.addEventListener('click', function(e) {
                const isClickOnInput = currentInput.contains(e.target) || e.target === currentInput;
                const isClickOnSuggestions = suggestionsContainer.contains(e.target);
                const isClickOnDisableBtn = disableBtn && (disableBtn.contains(e.target) || e.target === disableBtn);
                
                if (!isClickOnInput && !isClickOnSuggestions && !isClickOnDisableBtn) {
                    suggestionsContainer.classList.add('hidden');
                }
            });

            // Mark this input as having autocomplete initialized
            currentInput.dataset.autocompleteInitialized = 'true';


        } catch (error) {
            console.error('{{ $uniqueId }}: Failed to initialize guest autocomplete:', error);
            // Fallback to regular input functionality
        }
    }

    // Initialize autocomplete immediately
    setTimeout(initializeGuestAutocomplete, 100);

    // Expose reinitialize function globally for multi-step forms
    window.reinitializeGoogleMapsAutocomplete_{{ $uniqueId }} = function() {
        setTimeout(initializeGuestAutocomplete, 100);
    };
    
});
</script>
@endpush

@once
@push('styles')
<style>
/* Custom autocomplete suggestions dropdown */
.google-maps-autocomplete-suggestions {
    background-color: var(--color-base-100);
    border: 1px solid hsl(var(--bc) / 0.2);
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    font-family: inherit;
    max-height: 300px;
    overflow-y: auto;
    font-size: 0.875rem;
    line-height: 1.25rem;
}

/* Suggestion items */
.google-maps-autocomplete-suggestions > div {
    transition: all 0.15s ease-in-out;
}

.google-maps-autocomplete-suggestions > div:hover {
    background-color: hsl(var(--b2)) !important;
}

/* Scrollbar styling for suggestions */
.google-maps-autocomplete-suggestions::-webkit-scrollbar {
    width: 6px;
}

.google-maps-autocomplete-suggestions::-webkit-scrollbar-track {
    background: hsl(var(--b2));
    border-radius: 3px;
}

.google-maps-autocomplete-suggestions::-webkit-scrollbar-thumb {
    background: hsl(var(--bc) / 0.3);
    border-radius: 3px;
}

.google-maps-autocomplete-suggestions::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--bc) / 0.5);
}

/* Loading state styling */
.google-maps-autocomplete-loading {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

/* Focus ring for accessibility */
.google-maps-autocomplete-input:focus {
    outline: 2px solid hsl(var(--p));
    outline-offset: 2px;
}

/* Ensure input container has proper positioning */
.google-maps-autocomplete-container {
    position: relative;
}
</style>
@endpush
@endonce
