@props(['title', 'icon' => '', 'color' => 'primary'])

@php
    $colorClasses = [
        'primary' => 'from-primary/10 to-primary/5 bg-primary text-primary-content',
        'secondary' => 'from-secondary/10 to-secondary/5 bg-secondary text-secondary-content',
        'accent' => 'from-accent/10 to-accent/5 bg-accent text-accent-content',
        'info' => 'from-info/10 to-info/5 bg-info text-info-content',
        'success' => 'from-success/10 to-success/5 bg-success text-success-content',
        'warning' => 'from-warning/10 to-warning/5 bg-warning text-warning-content',
        'error' => 'from-error/10 to-error/5 bg-error text-error-content',
    ];
    
    $colorClass = $colorClasses[$color] ?? $colorClasses['primary'];
    $parts = explode(' ', $colorClass);
    $headerGradient = $parts[0] . ' ' . $parts[1]; // Gets 'from-primary/10 to-primary/5'
    $avatarClasses = $parts[2] . ' ' . $parts[3]; // Gets 'bg-primary text-primary-content'
@endphp

<div {{ $attributes->merge(['class' => 'card bg-base-100 shadow-md']) }}>
    <!-- Header Section -->
    <div class="bg-gradient-to-r {{ $headerGradient }} px-2 py-2 border-b border-base-300/50">
        <div class="flex items-center gap-3">
            <div class="avatar avatar-placeholder">
                @if($icon)
                    <div class="{{ $avatarClasses }} w-8 rounded-lg">
                        <i class="fa-sharp {{ $icon }} text-sm"></i>
                    </div>
                @endif
            </div>
            <h4 class="text-lg font-semibold text-base-content">{{ $title }}</h4>
        </div>
    </div>

    <!-- Content Section -->
    <div class="p-6 space-y-6">
        {{ $slot }}
    </div>
</div>
