<!-- Event Show Modal -->
<div id="event-show-modal" class="modal">
    <div class="modal-box max-w-2xl">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-primary/10 to-primary/5 -m-6 mb-6 px-6 py-4 border-b border-base-300/50">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="avatar avatar-placeholder">
                        <div class="bg-primary text-primary-content w-10 rounded-lg">
                            <i class="fa-sharp fa-calendar-check text-lg"></i>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-base-content" id="event-show-title">Event Details</h3>
                        <p class="text-base-content/70 text-sm">View event information and details</p>
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-circle btn-ghost" onclick="closeEventShowModal()">
                    <i class="fa-sharp fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Event Details Content -->
        <div class="space-y-4">
            <!-- Event Title -->
            <div class="flex items-center gap-3">
                <div class="w-4 h-4 rounded-full flex-shrink-0" id="event-show-color" style="background-color: #3788d8;"></div>
                <h4 class="text-lg font-semibold text-base-content" id="event-show-event-title">Event Title</h4>
            </div>

            <!-- Event Description -->
            <div id="event-show-description-container" class="bg-base-50 rounded-lg p-4 border border-base-200" style="display: none;">
                <div class="flex items-start gap-2">
                    <i class="fa-sharp fa-align-left text-base-content/60 mt-1"></i>
                    <div>
                        <div class="text-sm font-medium text-base-content/80 mb-1">Description</div>
                        <div class="text-base-content" id="event-show-description"></div>
                    </div>
                </div>
            </div>

            <!-- Date and Time Information -->
            <div class="bg-base-50 rounded-lg p-4 border border-base-200">
                <div class="flex items-start gap-2">
                    <i class="fa-sharp fa-clock text-base-content/60 mt-1"></i>
                    <div class="flex-1">
                        <div class="text-sm font-medium text-base-content/80 mb-2">Date & Time</div>
                        <div class="space-y-1">
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-base-content/60">Start:</span>
                                <span class="font-medium" id="event-show-start-date"></span>
                            </div>
                            <div class="flex items-center gap-2" id="event-show-end-container">
                                <span class="text-sm text-base-content/60">End:</span>
                                <span class="font-medium" id="event-show-end-date"></span>
                            </div>
                            <div id="event-show-all-day-badge" class="badge badge-info badge-sm" style="display: none;">
                                <i class="fa-sharp fa-sun mr-1"></i>
                                All Day Event
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location -->
            <div id="event-show-location-container" class="bg-base-50 rounded-lg p-4 border border-base-200" style="display: none;">
                <div class="flex items-start gap-2">
                    <i class="fa-sharp fa-map-marker-alt text-base-content/60 mt-1"></i>
                    <div>
                        <div class="text-sm font-medium text-base-content/80 mb-1">Location</div>
                        <div class="text-base-content" id="event-show-location"></div>
                    </div>
                </div>
            </div>

            <!-- Recurrence Information -->
            <div id="event-show-recurrence-container" class="bg-base-50 rounded-lg p-4 border border-base-200" style="display: none;">
                <div class="flex items-start gap-2">
                    <i class="fa-sharp fa-repeat text-base-content/60 mt-1"></i>
                    <div>
                        <div class="text-sm font-medium text-base-content/80 mb-1">Recurrence</div>
                        <div class="text-base-content" id="event-show-recurrence"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Actions -->
        <div class="modal-action mt-6">
            <button type="button" class="btn btn-ghost" onclick="closeEventShowModal()">
                <i class="fa-sharp fa-times mr-2"></i>Close
            </button>
            <button type="button" class="btn btn-primary" id="event-show-edit-btn" onclick="editEventFromShow()" style="display: none;">
                <i class="fa-sharp fa-pen-to-square mr-2"></i>Edit Event
            </button>
        </div>
    </div>
</div>

<script>
// Event Show Modal Functions
let currentEventShowData = null;

function openEventShowModal(eventData) {
    const modal = document.getElementById('event-show-modal');
    const title = document.getElementById('event-show-event-title');
    const color = document.getElementById('event-show-color');
    const description = document.getElementById('event-show-description');
    const descriptionContainer = document.getElementById('event-show-description-container');
    const startDate = document.getElementById('event-show-start-date');
    const endDate = document.getElementById('event-show-end-date');
    const endContainer = document.getElementById('event-show-end-container');
    const allDayBadge = document.getElementById('event-show-all-day-badge');
    const location = document.getElementById('event-show-location');
    const locationContainer = document.getElementById('event-show-location-container');
    const recurrence = document.getElementById('event-show-recurrence');
    const recurrenceContainer = document.getElementById('event-show-recurrence-container');

    // Store event data for edit functionality
    currentEventShowData = eventData;

    // Set event title and color
    title.textContent = eventData.title;
    color.style.backgroundColor = eventData.color;

    // Set description
    if (eventData.description && eventData.description.trim()) {
        description.textContent = eventData.description;
        descriptionContainer.style.display = 'block';
    } else {
        descriptionContainer.style.display = 'none';
    }

    // Format and set dates
    const formatDate = (date, allDay = false) => {
        if (!date) return '';
        
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        if (!allDay) {
            options.hour = '2-digit';
            options.minute = '2-digit';
            options.hour12 = true;
        }
        
        return date.toLocaleDateString('en-US', options);
    };

    startDate.textContent = formatDate(eventData.start, eventData.allDay);
    
    if (eventData.end && !eventData.allDay) {
        endDate.textContent = formatDate(eventData.end, eventData.allDay);
        endContainer.style.display = 'flex';
    } else {
        endContainer.style.display = 'none';
    }

    // Show all day badge if applicable
    if (eventData.allDay) {
        allDayBadge.style.display = 'inline-flex';
    } else {
        allDayBadge.style.display = 'none';
    }

    // Set location
    if (eventData.location && eventData.location.trim()) {
        location.textContent = eventData.location;
        locationContainer.style.display = 'block';
    } else {
        locationContainer.style.display = 'none';
    }

    // Set recurrence information
    if (eventData.recurrence) {
        const recurrenceText = formatRecurrenceText(eventData.recurrence);
        recurrence.textContent = recurrenceText;
        recurrenceContainer.style.display = 'block';
    } else {
        recurrenceContainer.style.display = 'none';
    }

    // Show/hide edit button based on permissions
    const editBtn = document.getElementById('event-show-edit-btn');
    if (window.canEditEvents) {
        editBtn.style.display = 'inline-flex';
    } else {
        editBtn.style.display = 'none';
    }

    // Show modal
    modal.classList.add('modal-open');
}

function closeEventShowModal() {
    const modal = document.getElementById('event-show-modal');
    modal.classList.remove('modal-open');
    currentEventShowData = null;
}

function editEventFromShow() {
    if (currentEventShowData) {
        // Close show modal
        closeEventShowModal();
        
        // Open edit modal with the stored event data
        openEventModal('edit', currentEventShowData);
    }
}

function formatRecurrenceText(recurrence) {
    if (!recurrence) return '';
    
    let text = '';
    const frequency = recurrence.frequency;
    const interval = recurrence.interval || 1;
    
    if (interval === 1) {
        switch (frequency) {
            case 'daily':
                text = 'Daily';
                break;
            case 'weekly':
                text = 'Weekly';
                break;
            case 'monthly':
                text = 'Monthly';
                break;
            case 'yearly':
                text = 'Yearly';
                break;
        }
    } else {
        switch (frequency) {
            case 'daily':
                text = `Every ${interval} days`;
                break;
            case 'weekly':
                text = `Every ${interval} weeks`;
                break;
            case 'monthly':
                text = `Every ${interval} months`;
                break;
            case 'yearly':
                text = `Every ${interval} years`;
                break;
        }
    }
    
    // Add days of week for weekly recurrence
    if (frequency === 'weekly' && recurrence.days_of_week && recurrence.days_of_week.length > 0) {
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const selectedDays = recurrence.days_of_week.map(day => dayNames[day]).join(', ');
        text += ` on ${selectedDays}`;
    }
    
    // Add end conditions
    if (recurrence.count) {
        text += ` (${recurrence.count} times)`;
    } else if (recurrence.until_date) {
        const untilDate = new Date(recurrence.until_date);
        text += ` until ${untilDate.toLocaleDateString()}`;
    }
    
    return text;
}
</script>
