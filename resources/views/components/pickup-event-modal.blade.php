<!-- Pickup Event Modal -->
<div id="pickup-event-modal" class="modal">
    <div class="modal-box max-w-2xl">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-primary/10 to-primary/5 -m-6 mb-6 px-6 py-4 border-b border-base-300/50">
            <div class="flex items-center gap-3">
                <div class="avatar avatar-placeholder">
                    <div class="bg-primary text-primary-content w-10 rounded-lg">
                        <i class="fa-sharp fa-truck text-lg"></i>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-base-content" id="pickup-event-modal-title">Create Pickup Request</h3>
                    <p class="text-base-content/70 text-sm">Pickup events are now created through pickup requests</p>
                </div>
            </div>
        </div>

        <div class="space-y-6">
            <!-- Information Section -->
            <div class="bg-info/10 border border-info/20 rounded-lg p-4">
                <div class="flex items-start gap-3">
                    <i class="fa-sharp fa-info-circle text-info text-xl mt-1"></i>
                    <div>
                        <h4 class="font-semibold text-base-content mb-2">Pickup Scheduling Has Changed</h4>
                        <p class="text-base-content/80 text-sm mb-3">
                            Pickup events are now created through pickup requests to ensure all pickup details are properly captured and managed.
                            This provides better tracking and allows for more comprehensive pickup information.
                        </p>
                        <p class="text-base-content/80 text-sm">
                            To schedule a pickup, please create a pickup request which will then be converted to a pickup event.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-center gap-4">
                <button type="button" class="btn btn-outline gap-2" onclick="closePickupEventModal()">
                    <i class="fa-sharp fa-times"></i> Cancel
                </button>
                <a href="/pickup-requests/create" class="btn btn-primary gap-2">
                    <i class="fa-sharp fa-plus"></i> Create Pickup Request
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Pickup Event Modal Functions
function openPickupEventModal(action = 'add', eventData = null) {
    const modal = document.getElementById('pickup-event-modal');
    modal.classList.add('modal-open');
}

function closePickupEventModal() {
    const modal = document.getElementById('pickup-event-modal');
    modal.classList.remove('modal-open');
}

</script>
