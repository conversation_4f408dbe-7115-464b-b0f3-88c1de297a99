<div class="form-control w-full mt-4 relative">
    <input type="text" id="{{ $id }}" 
           class="input input-bordered w-full mb-4" 
           placeholder="{{ $placeholder }}" 
           value="{{ $selectedNumber ?? '' }}">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Floating Results Window -->
    <div id="{{ $id }}_resultsContainer" class="absolute w-full bg-white shadow-lg border rounded-lg z-10 hidden" style="top: 3.5rem;">
        <ul id="{{ $id }}_results" class="list-none p-4 text-sm text-gray-700">
            <!-- Dynamically filled by JS -->
        </ul>
        <div id="{{ $id }}_noResults" class="text-center p-4 text-gray-600 hidden">
            <p>
                No certificates found... <a href="{{ route('certificates.create') }}" class="link link-primary">Create new?</a>
            </p>
        </div>
    </div>

    <!-- Hidden Input for Selected Certificate ID -->
    <input type="hidden" name="{{ $name }}" value="{{ $selectedId ?? '' }}">
</div>

<script>
document.addEventListener("DOMContentLoaded", function () {
    const searchInput = document.getElementById("{{ $id }}");
    const resultsContainer = document.getElementById("{{ $id }}_resultsContainer");
    const resultsList = document.getElementById("{{ $id }}_results");
    const noResults = document.getElementById("{{ $id }}_noResults");
    const action = "{{ $action }}"; // Pass the action (e.g., "navigate" or "form")

    searchInput.addEventListener("input", function () {
        const query = searchInput.value.trim();

        if (query.length === 0) {
            resultsContainer.classList.add("hidden");
            resultsList.innerHTML = "";
            noResults.classList.add("hidden");
            return;
        }

        fetch(`/certificatesearch/${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                resultsList.innerHTML = "";

                if (data.length > 0) {
                    noResults.classList.add("hidden");
                    resultsContainer.classList.remove("hidden");

                    data.forEach(certificate => {
                        const listItem = document.createElement("li");
                        listItem.className = "py-2 px-4 hover:bg-gray-100 cursor-pointer rounded";
                        listItem.setAttribute("data-certificate-id", certificate.id);

                        const statusClass = certificate.status === 'completed' ? 'text-success' : 
                                           (certificate.status === 'verifying' ? 'text-warning' : 'text-primary');
                        
                        const formattedStatus = certificate.status.charAt(0).toUpperCase() + certificate.status.slice(1);

                        listItem.innerHTML = `
                            <strong>${certificate.certificate_number}</strong> - <span class="${statusClass}">${formattedStatus}</span><br>
                            <span class="text-gray-600">Customer: ${certificate.customer.name}</span><br>
                            ${certificate.scheduled_destruction_date ? `<span class="text-gray-500">Scheduled: ${new Date(certificate.scheduled_destruction_date).toLocaleDateString()}</span>` : ""}
                        `;

                        listItem.addEventListener("click", () => {
                            if (action === "navigate") {
                                // Navigate to certificate view route
                                window.location.href = `/certificates/${certificate.id}`;
                            } else if (action === "form") {
                                // Populate form inputs
                                searchInput.value = certificate.certificate_number;
                                const hiddenInput = document.querySelector(`input[name="{{ $name }}"]`);
                                if (hiddenInput) {
                                    hiddenInput.value = certificate.id;
                                }
                                resultsContainer.classList.add("hidden");
                            }
                        });

                        resultsList.appendChild(listItem);
                    });
                } else {
                    noResults.classList.remove("hidden");
                    resultsContainer.classList.remove("hidden");
                }
            })
            .catch(error => console.error("Error fetching certificates:", error));
    });

    document.addEventListener("click", (e) => {
        if (!resultsContainer.contains(e.target) && e.target !== searchInput) {
            resultsContainer.classList.add("hidden");
        }
    });
});
</script>
