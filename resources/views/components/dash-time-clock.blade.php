@perms('view_own_timecards')
    <div class="space-y-4" id="time-clock-widget">
        <!-- Current Status Display -->
        <div class="text-center">
            <div class="text-lg font-semibold text-base-content mb-2" id="status-display">
                <span class="loading loading-spinner loading-sm"></span>
                Loading...
            </div>
            <div class="text-sm text-base-content/70" id="current-time-display">
                --:--:-- --
            </div>
        </div>

        <!-- Hours Display -->
        <div class="grid grid-cols-2 gap-4 text-center">
            <div class="bg-primary/10 rounded-lg p-3 border-primary border">
                <div class="text-xs text-base-content/60 uppercase tracking-wide">Today's Hours</div>
                <div class="text-lg font-mono font-bold text-primary" id="widget-total-hours">
                    00:00:00
                </div>
            </div>
            <div class="bg-warning/10 rounded-lg p-3 border-warning border">
                <div class="text-xs text-base-content/60 uppercase tracking-wide">Break Time</div>
                <div class="text-lg font-mono font-bold text-warning-content" id="widget-break-hours">
                    00:00:00
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-2" id="action-buttons">
            <div class="flex gap-2">
                <button type="button" 
                        class="btn btn-success btn-sm flex-1 gap-1 hidden" 
                        id="widget-clock-in-btn"
                        onclick="performTimeClockAction('clock_in')">
                    <i class="fa-sharp fa-user-clock text-xs"></i>
                    <span class="hidden sm:inline">Clock In</span>
                    <span class="sm:hidden">In</span>
                </button>
                
                <button type="button" 
                        class="btn btn-error btn-sm flex-1 gap-1 hidden" 
                        id="widget-clock-out-btn"
                        onclick="performTimeClockAction('clock_out')">
                    <i class="fa-sharp fa-user-clock text-xs"></i>
                    <span class="hidden sm:inline">Clock Out</span>
                    <span class="sm:hidden">Out</span>
                </button>
            </div>
            
            <div class="flex gap-2">
                <button type="button" 
                        class="btn btn-warning btn-sm flex-1 gap-1 hidden" 
                        id="widget-break-out-btn"
                        onclick="performTimeClockAction('break_out')">
                    <i class="fa-sharp fa-coffee text-xs"></i>
                    <span class="hidden sm:inline">Start Break</span>
                    <span class="sm:hidden">Break</span>
                </button>
                
                <button type="button" 
                        class="btn btn-info btn-sm flex-1 gap-1 hidden" 
                        id="widget-break-in-btn"
                        onclick="performTimeClockAction('clock_in')">
                    <i class="fa-sharp fa-coffee text-xs"></i>
                    <span class="hidden sm:inline">End Break</span>
                    <span class="sm:hidden">Return</span>
                </button>
            </div>
        </div>

        <!-- Quick Link to Full Time Clock -->
        <div class="text-center pt-2 border-t border-base-300">
            <a href="{{ route('time-clock') }}" class="btn btn-ghost btn-xs gap-1">
                <i class="fa-sharp fa-external-link text-xs"></i>
                Full Time Clock
            </a>
        </div>
    </div>

    <script>
        // Time Clock Widget JavaScript
        (function() {
            let widgetCurrentStatus = 'clock_out';
            let widgetTotalSeconds = 0;
            let widgetBreakSeconds = 0;
            let widgetTotalHoursInterval = null;
            let widgetBreakHoursInterval = null;

            // Initialize the widget
            function initTimeClockWidget() {
                updateCurrentTime();
                setInterval(updateCurrentTime, 1000);
                refreshWidgetData();
                setInterval(refreshWidgetData, 30000); // Refresh every 30 seconds
            }

            // Update current time display
            function updateCurrentTime() {
                const now = new Date();
                const options = {
                    hour: 'numeric',
                    minute: 'numeric',
                    second: 'numeric',
                    hour12: true
                };
                const timeDisplay = document.getElementById('current-time-display');
                if (timeDisplay) {
                    timeDisplay.textContent = now.toLocaleTimeString([], options);
                }
            }

            // Refresh widget data from API
            function refreshWidgetData() {
                fetch('/api/time-clock/current-data', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'same-origin'
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`API request failed: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        updateWidgetDisplay(data);
                    }
                })
                .catch(error => {
                    console.error('Error refreshing time clock widget:', error);
                    updateStatusDisplay('Error', 'error');
                });
            }

            // Update widget display with fresh data
            function updateWidgetDisplay(data) {
                widgetCurrentStatus = data.current_status;
                widgetTotalSeconds = timeStringToSeconds(data.formatted_total_hours);
                widgetBreakSeconds = timeStringToSeconds(data.formatted_total_break_hours);

                // Update status display
                updateStatusDisplay(widgetCurrentStatus);

                // Update hours display
                const totalHoursEl = document.getElementById('widget-total-hours');
                const breakHoursEl = document.getElementById('widget-break-hours');
                
                if (totalHoursEl) totalHoursEl.textContent = data.formatted_total_hours;
                if (breakHoursEl) breakHoursEl.textContent = data.formatted_total_break_hours;

                // Update button visibility
                updateButtonVisibility(widgetCurrentStatus);

                // Start/stop counters
                startWidgetCounters();
            }

            // Update status display
            function updateStatusDisplay(status, type = null) {
                const statusDisplay = document.getElementById('status-display');
                if (!statusDisplay) return;

                let statusText = '';
                let statusClass = '';

                if (type === 'error') {
                    statusText = 'Connection Error';
                    statusClass = 'text-error';
                } else {
                    switch (status) {
                        case 'clock_out':
                            statusText = 'Clocked Out';
                            statusClass = 'text-base-content';
                            break;
                        case 'clock_in':
                            statusText = 'Clocked In';
                            statusClass = 'text-success';
                            break;
                        case 'break':
                            statusText = 'On Break';
                            statusClass = 'text-base-content';
                            break;
                        default:
                            statusText = 'Unknown Status';
                            statusClass = 'text-base-content';
                    }
                }

                statusDisplay.innerHTML = `<span class="${statusClass}">${statusText}</span>`;
            }

            // Update button visibility based on status
            function updateButtonVisibility(status) {
                const clockInBtn = document.getElementById('widget-clock-in-btn');
                const clockOutBtn = document.getElementById('widget-clock-out-btn');
                const breakOutBtn = document.getElementById('widget-break-out-btn');
                const breakInBtn = document.getElementById('widget-break-in-btn');

                // Hide all buttons first
                [clockInBtn, clockOutBtn, breakOutBtn, breakInBtn].forEach(btn => {
                    if (btn) btn.classList.add('hidden');
                });

                // Show appropriate buttons based on status
                switch (status) {
                    case 'clock_out':
                        if (clockInBtn) clockInBtn.classList.remove('hidden');
                        break;
                    case 'clock_in':
                        if (clockOutBtn) clockOutBtn.classList.remove('hidden');
                        if (breakOutBtn) breakOutBtn.classList.remove('hidden');
                        break;
                    case 'break':
                        if (clockOutBtn) clockOutBtn.classList.remove('hidden');
                        if (breakInBtn) breakInBtn.classList.remove('hidden');
                        break;
                }
            }

            // Start widget counters
            function startWidgetCounters() {
                // Clear existing intervals
                if (widgetTotalHoursInterval) clearInterval(widgetTotalHoursInterval);
                if (widgetBreakHoursInterval) clearInterval(widgetBreakHoursInterval);

                if (widgetCurrentStatus === 'clock_in') {
                    // Update total hours every second when clocked in
                    widgetTotalHoursInterval = setInterval(function() {
                        widgetTotalSeconds++;
                        const totalHoursEl = document.getElementById('widget-total-hours');
                        if (totalHoursEl) {
                            totalHoursEl.textContent = secondsToTimeString(widgetTotalSeconds);
                        }
                    }, 1000);
                } else if (widgetCurrentStatus === 'break') {
                    // Update break time every second when on break
                    widgetBreakHoursInterval = setInterval(function() {
                        widgetBreakSeconds++;
                        const breakHoursEl = document.getElementById('widget-break-hours');
                        if (breakHoursEl) {
                            breakHoursEl.textContent = secondsToTimeString(widgetBreakSeconds);
                        }
                    }, 1000);
                }
            }

            // Helper functions
            function timeStringToSeconds(timeString) {
                const [hours, minutes, seconds] = timeString.split(':').map(Number);
                return hours * 3600 + minutes * 60 + seconds;
            }

            function secondsToTimeString(seconds) {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                const secs = seconds % 60;
                return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }

            // Global function for button actions
            window.performTimeClockAction = function(action) {
                // Disable all buttons during action
                const buttons = document.querySelectorAll('#action-buttons button');
                buttons.forEach(btn => {
                    btn.disabled = true;
                    btn.classList.add('loading');
                });

                // Get CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (!csrfToken) {
                    console.error('CSRF token not found');
                    enableButtons();
                    showToast('Security token not found. Please refresh the page.', 'error');
                    return;
                }

                // Create FormData for consistency with main page
                const formData = new FormData();
                formData.append('action', action);
                formData.append('notes', '');
                formData.append('_token', csrfToken.getAttribute('content'));

                // Make AJAX request
                fetch('{{ route("time-clock.action") }}', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                    },
                    credentials: 'same-origin',
                    body: formData
                })
                .then(async (response) => {
                    // Always try to parse as JSON, even for non-200 responses
                    // (service worker returns JSON for queued/offline actions)
                    try {
                        const data = await response.json();
                        return { data, status: response.status, ok: response.ok };
                    } catch (e) {
                        // If JSON parsing fails, check if it's an error response
                        if (!response.ok) {
                            throw new Error(`Server error: ${response.status}`);
                        }
                        // Otherwise treat as network error
                        throw new Error('Failed to parse response');
                    }
                })
                .then(result => {
                    enableButtons();
                    const data = result.data;

                    if (data.success) {
                        showToast(data.message || 'Action completed successfully', 'success');
                        // Refresh widget data immediately
                        setTimeout(refreshWidgetData, 500);
                    } else if (data.queued || result.status === 202) {
                        // Handle offline/queued actions from service worker
                        showToast(data.message || 'Action queued for when connection is restored', 'warning');
                    } else if (data.offline) {
                        // Handle offline state
                        showToast('No internet connection. Please try again when online.', 'error');
                    } else {
                        // Show detailed error message with appropriate duration
                        const errorMessage = data.message || 'Action failed';
                        showToast(errorMessage, 'error');
                        
                        // Log for debugging
                        console.error('Time clock widget action failed:', {
                            action: action,
                            message: errorMessage,
                            data: data
                        });
                    }
                })
                .catch(error => {
                    console.error('Time clock action error:', error);
                    enableButtons();
                    showToast('Failed to perform action. Please try again.', 'error');
                });
            };

            // Helper function to re-enable buttons
            function enableButtons() {
                const buttons = document.querySelectorAll('#action-buttons button');
                buttons.forEach(btn => {
                    btn.disabled = false;
                    btn.classList.remove('loading');
                });
            }

            // Helper function to show toast messages
            function showToast(message, type = 'success') {
                // Try to use existing toast system
                const dynamicToast = document.getElementById('dynamicToast');
                const dynamicToastMessage = document.getElementById('dynamicToastMessage');

                if (dynamicToast && dynamicToastMessage) {
                    // Update toast content
                    dynamicToastMessage.textContent = message;

                    // Update toast styling based on type
                    const alertDiv = dynamicToast.querySelector('.alert');
                    if (alertDiv) {
                        let alertClass = 'alert-success';
                        if (type === 'error') alertClass = 'alert-error';
                        else if (type === 'warning') alertClass = 'alert-warning';

                        alertDiv.className = `alert ${alertClass} shadow-lg`;
                    }

                    // Show toast
                    dynamicToast.classList.remove('hidden');

                    // Hide after 5 seconds (longer for warnings)
                    const hideDelay = type === 'warning' ? 7000 : 5000;
                    setTimeout(() => {
                        dynamicToast.classList.add('hidden');
                    }, hideDelay);
                } else {
                    // Fallback to console log if toast system not available
                    console.log(`${type.toUpperCase()}: ${message}`);
                }
            }

            // Initialize when DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initTimeClockWidget);
            } else {
                initTimeClockWidget();
            }
        })();
    </script>
@else
    <div class="text-center text-base-content/60">
        <i class="fa-sharp fa-lock text-2xl mb-2"></i>
        <p>Time clock requires appropriate permissions</p>
    </div>
@endperms
