<!-- Mobile Dock - Only visible on mobile -->
<div class="lg:hidden fixed bottom-0 left-0 right-0 z-50  text-neutral-content border-t border-neutral-content/20 shadow-lg"
    id="mobileDock">
    <div class="dock dock-sm bg-neutral">
        <!-- Menu Toggle Button -->
        <button id="mobileDockMenuToggle" aria-label="Toggle Menu">
            <i class="fa-sharp fa-solid fa-bars text-lg"></i>
        </button>

        <!-- Home Button -->
        <a href="{{ route('dashboard') }}" class="dock-item" aria-label="Home">
            <i class="fa-sharp fa-solid fa-house text-lg"></i>
        </a>


        <!-- Notifications Button -->
        <button id="mobileDockNotifications" aria-label="Notifications" class="relative">
            <div class="relative">
            <i class="fa-sharp fa-solid fa-bell text-lg"></i>
            <!-- Notification badge -->
            <div class="absolute -top-1 -right-2 w-4 h-4 bg-error rounded-full hidden items-center justify-center" id="mobileNotificationBadge">
                <span class="text-xs text-error-content font-bold" id="mobileNotificationCount">0</span>
            </div>
            </div>
        </button>
    </div>
</div>

<style>
    /* Mobile dock styles */
    #mobileDock {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    /* Dock item styling */
    .dock-item {
        min-height: 3rem;
        min-width: 3rem;
        transition: all 0.2s ease-in-out;
    }

    .dock-item:hover {
        transform: translateY(-2px);
    }

    .dock-active {
        background-color: hsl(var(--p));
        color: hsl(var(--pc));
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const menuToggle = document.getElementById('mobileDockMenuToggle');
        const userToggle = document.getElementById('mobileDockUserToggle');
        const userMenu = document.getElementById('mobileDockUserMenu');
        const notificationsBtn = document.getElementById('mobileDockNotifications');
        const headerToggle = document.getElementById('headerSidebarToggle');

        // Connect mobile dock menu toggle to the existing sidebar toggle functionality
        if (menuToggle && headerToggle) {
            menuToggle.addEventListener('click', function() {
                headerToggle.click(); // Trigger the existing sidebar toggle
            });
        }

        // User menu toggle
        if (userToggle && userMenu) {
            userToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });

            // Close user menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!userToggle.contains(e.target) && !userMenu.contains(e.target)) {
                    userMenu.classList.add('hidden');
                }
            });
        }

        // Notifications button functionality
        const mobileNotificationBadge = document.getElementById('mobileNotificationBadge');
        const mobileNotificationCount = document.getElementById('mobileNotificationCount');

        if (notificationsBtn) {
            // Load notification count on page load
            loadMobileNotificationCount();

            // Load notification count periodically
            setInterval(loadMobileNotificationCount, 30000); // Every 30 seconds

            notificationsBtn.addEventListener('click', function() {
                openMobileNotifications();
            });
        }

        // Load mobile notification count
        function loadMobileNotificationCount() {
            fetch('/api/notifications/count', {
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                updateMobileNotificationBadge(data.unread_count);
            })
            .catch(error => {
                console.error('Error loading mobile notification count:', error);
            });
        }

        // Update mobile notification badge
        function updateMobileNotificationBadge(count) {
            if (count > 0) {
                mobileNotificationBadge.classList.remove('hidden');
                mobileNotificationBadge.classList.add('flex');
                mobileNotificationCount.textContent = count > 99 ? '99+' : count;
            } else {
                mobileNotificationBadge.classList.add('hidden');
                mobileNotificationBadge.classList.remove('flex');
            }
        }

        // Open mobile notifications interface
        function openMobileNotifications() {
            // Create mobile notifications modal
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 z-50 bg-base-100 lg:hidden';
            modal.innerHTML = `
                <div class="flex flex-col h-full">
                    <!-- Header -->
                    <div class="bg-primary text-primary-content p-4 flex items-center justify-between">
                        <h2 class="text-lg font-semibold">Notifications</h2>
                        <button id="closeMobileNotifications" class="btn btn-ghost btn-circle btn-sm text-primary-content">
                            <i class="fa-sharp fa-solid fa-times text-lg"></i>
                        </button>
                    </div>

                    <!-- Actions -->
                    <div class="p-4 border-b border-base-300 bg-base-200">
                        <button id="mobileDismissAllBtn" class="btn btn-ghost btn-sm w-full">
                            <i class="fa-sharp fa-solid fa-check-double"></i>
                            Dismiss All Notifications
                        </button>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 overflow-y-auto" id="mobileNotificationsContent">
                        <div class="p-4 text-center text-base-content/70">
                            <span class="loading loading-spinner loading-sm"></span>
                            <p class="mt-2">Loading notifications...</p>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Load notifications
            loadMobileNotifications();

            // Add event listeners
            document.getElementById('closeMobileNotifications').addEventListener('click', function() {
                document.body.removeChild(modal);
            });

            document.getElementById('mobileDismissAllBtn').addEventListener('click', function() {
                dismissAllMobileNotifications(modal);
            });
        }

        // Load mobile notifications
        function loadMobileNotifications() {
            const content = document.getElementById('mobileNotificationsContent');
            if (!content) return;

            fetch('/api/notifications', {
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data && typeof data === 'object') {
                    updateMobileNotificationBadge(data.unread_count || 0);
                    renderMobileNotifications(data.notifications || [], content);
                } else {
                    throw new Error('Invalid response format');
                }
            })
            .catch(error => {
                console.error('Error loading mobile notifications:', error);
                content.innerHTML = `
                    <div class="p-4 text-center text-error">
                        <i class="fa-sharp fa-exclamation-triangle text-2xl mb-2"></i>
                        <p>Failed to load notifications</p>
                        <button class="btn btn-xs btn-primary mt-2" onclick="loadMobileNotifications()">Try Again</button>
                    </div>
                `;
            });
        }

        // Render mobile notifications
        function renderMobileNotifications(notifications, container) {
            // Hide/show dismiss all button based on notification count
            const dismissAllBtn = document.getElementById('mobileDismissAllBtn');
            if (dismissAllBtn) {
                if (notifications.length === 0) {
                    dismissAllBtn.style.display = 'none';
                } else {
                    dismissAllBtn.style.display = 'block';
                }
            }

            if (notifications.length === 0) {
                container.innerHTML = `
                    <div class="p-8 text-center text-base-content/70">
                        <i class="fa-sharp fa-bell text-4xl mb-4"></i>
                        <p class="text-lg">No new notifications</p>
                        <p class="text-sm">You're all caught up!</p>
                    </div>
                `;
                return;
            }

            const notificationsHtml = notifications.map(notification => `
                <div class="mobile-notification-item border-b border-base-300 p-4 ${notification.is_read ? 'opacity-75' : ''}" data-notification-id="${notification.id}">
                    <div class="flex items-start gap-3">
                        <div class="flex-shrink-0 mt-1">
                            <i class="fa-sharp ${notification.urgency_icon} text-${notification.urgency === 'critical' ? 'error' : (notification.urgency === 'high' ? 'warning' : (notification.urgency === 'normal' ? 'success' : 'info'))} text-lg"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between mb-2">
                                <h4 class="font-medium text-base-content">${notification.title}</h4>
                                <div class="flex items-center gap-2 ml-2">
                                    ${!notification.is_read ? '<div class="w-2 h-2 bg-primary rounded-full"></div>' : ''}
                                    ${notification.is_dismissible ? `<button class="mobile-dismiss-btn btn btn-ghost btn-circle btn-xs text-base-content/50" data-notification-id="${notification.id}"><i class="fa-sharp fa-solid fa-times"></i></button>` : ''}
                                </div>
                            </div>
                            <p class="text-sm text-base-content/80 mb-3">${notification.message}</p>
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-base-content/50">${notification.created_at}</span>
                                ${notification.link_url ? `<a href="${notification.link_url}" class="btn btn-primary btn-xs">${notification.link_text || 'View'}</a>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = notificationsHtml;

            // Add swipe-to-dismiss functionality and click handlers
            container.querySelectorAll('.mobile-notification-item').forEach(item => {
                const notificationId = item.dataset.notificationId;
                const notification = notifications.find(n => n.id == notificationId);

                // Add swipe-to-dismiss
                let startX = 0;
                let currentX = 0;
                let isDragging = false;

                item.addEventListener('touchstart', function(e) {
                    startX = e.touches[0].clientX;
                    isDragging = true;
                    item.style.transition = 'none';
                });

                item.addEventListener('touchmove', function(e) {
                    if (!isDragging) return;
                    currentX = e.touches[0].clientX;
                    const deltaX = currentX - startX;

                    if (deltaX < 0) { // Swiping left
                        item.style.transform = `translateX(\${Math.max(deltaX, -100)}px)`;
                        item.style.opacity = Math.max(1 + deltaX / 100, 0.3);
                    }
                });

                item.addEventListener('touchend', function(e) {
                    if (!isDragging) return;
                    isDragging = false;
                    item.style.transition = 'transform 0.3s ease, opacity 0.3s ease';

                    const deltaX = currentX - startX;
                    if (deltaX < -50 && notification.is_dismissible) { // Swipe threshold
                        dismissMobileNotification(notificationId);
                    } else {
                        item.style.transform = 'translateX(0)';
                        item.style.opacity = '1';
                    }
                });

                // Click to mark as read and navigate
                item.addEventListener('click', function(e) {
                    if (e.target.closest('.mobile-dismiss-btn')) return;

                    // Mark as read if not already read
                    if (!notification.is_read) {
                        markMobileNotificationAsRead(notificationId);
                    }

                    // Navigate to link if available
                    if (notification.link_url) {
                        window.location.href = notification.link_url;
                    }
                });
            });

            // Add click handlers for dismiss buttons
            container.querySelectorAll('.mobile-dismiss-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const notificationId = this.dataset.notificationId;
                    dismissMobileNotification(notificationId);
                });
            });
        }

        // Mark mobile notification as read
        function markMobileNotificationAsRead(notificationId) {
            fetch(`/api/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadMobileNotificationCount();
                }
            })
            .catch(error => {
                console.error('Error marking mobile notification as read:', error);
            });
        }

        // Dismiss mobile notification
        function dismissMobileNotification(notificationId) {
            fetch(`/api/notifications/${notificationId}/dismiss`, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Add delay to avoid race condition
                    setTimeout(() => {
                        loadMobileNotifications();
                    }, 100);
                }
            })
            .catch(error => {
                console.error('Error dismissing mobile notification:', error);
                // Show user-friendly error message
                const toast = document.createElement('div');
                toast.className = 'toast toast-top toast-center';
                toast.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fa-sharp fa-exclamation-triangle"></i>
                        <span>Failed to dismiss notification. Please try again.</span>
                    </div>
                `;
                document.body.appendChild(toast);
                setTimeout(() => toast.remove(), 3000);
            });
        }

        // Dismiss all mobile notifications
        function dismissAllMobileNotifications(modal) {
            fetch('/api/notifications/dismiss-all', {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Add delay to avoid race condition
                    setTimeout(() => {
                        loadMobileNotifications();
                    }, 150);

                    // Show success message
                    const toast = document.createElement('div');
                    toast.className = 'toast toast-top toast-center';
                    toast.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fa-sharp fa-check-circle"></i>
                            <span>${data.message}</span>
                        </div>
                    `;
                    document.body.appendChild(toast);
                    setTimeout(() => toast.remove(), 3000);
                }
            })
            .catch(error => {
                console.error('Error dismissing all mobile notifications:', error);
                // Show user-friendly error message
                const toast = document.createElement('div');
                toast.className = 'toast toast-top toast-center';
                toast.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fa-sharp fa-exclamation-triangle"></i>
                        <span>Failed to dismiss notifications. Please try again.</span>
                    </div>
                `;
                document.body.appendChild(toast);
                setTimeout(() => toast.remove(), 3000);
            });
        }
    });
</script>
