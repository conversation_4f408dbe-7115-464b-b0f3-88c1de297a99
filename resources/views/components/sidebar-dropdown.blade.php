@props(['title', 'icon'])

@php
    $dropdownId = Str::uuid(); // Unique ID for the dropdown
    $flyoutId = 'flyout-' . $dropdownId; // Unique ID for the flyout
@endphp

<li class="relative dropdown-item">
  <!-- Dropdown Toggle with Tooltip -->
  <div class="tooltip tooltip-right" data-tip="{{ $title }}">
    <button
      class="dropdown-toggle w-full px-4 py-2 text-left flex items-center justify-between hover:bg-secondary text-neutral-content cursor-pointer rounded focus:outline-none focus:bg-gray-700"
      onclick="
        const sidebar = document.getElementById('sidebar');
        const isCollapsed = sidebar.getAttribute('data-collapsed') === 'true';

        if (!isCollapsed) {
          // Normal dropdown behavior
          document.getElementById('{{ $dropdownId }}').classList.toggle('hidden');
          const chevron = this.querySelector('.dropdown-chevron');
          chevron.classList.toggle('fa-chevron-down');
          chevron.classList.toggle('fa-chevron-up');
        }
      ">
      <div class="flex items-center space-x-2">
        <i class="{{ $icon }} flex-shrink-0"></i>
        <span class="menu-text">{{ $title }}</span>
      </div>
      <i class="fa-sharp fa-solid fa-chevron-down dropdown-chevron"></i>
    </button>
  </div>

  <!-- Normal Dropdown Content -->
  <ul id="{{ $dropdownId }}" class="hidden pl-4 space-y-2">
    {{ $slot }}
  </ul>

  <!-- Flyout Menu for Collapsed State -->
  <div class="flyout-menu" id="{{ $flyoutId }}">
    <div class="p-2">
      <h3 class="text-neutral-content font-semibold mb-2 px-2">{{ $title }}</h3>
      <ul class="space-y-1">
        {{ $slot }}
      </ul>
    </div>
  </div>
</li>
