@props([
    'eventType' => 'form_submitted',
    'eventLabel' => 'Form Submission',
    'config' => null,
    'userGroups' => null,
    'users' => null,
    'fieldPrefix' => 'notification_config',
    'showAdvanced' => false
])

@php
    $userGroups = $userGroups ?? \App\Models\UserGroup::all();
    $users = $users ?? \App\Models\User::where('enabled', true)->get();
    
    // Get current configuration values
    $isEnabled = $config ? $config->is_enabled : false;
    $targetType = $config ? $config->target_type : 'user_group';
    $targetIds = $config ? $config->target_ids : [];
    $urgency = $config ? $config->urgency : 'normal';
    $customTitle = $config ? $config->custom_title : '';
    $customMessage = $config ? $config->custom_message : '';
    $includeLink = $config ? $config->include_link : true;
    $linkText = $config ? $config->link_text : '';
    $expiresAfterDays = $config ? $config->expires_after_days : null;
    $isDismissible = $config ? $config->is_dismissible : true;
    $autoDismiss = $config ? $config->auto_dismiss : false;
    
    $fieldName = $fieldPrefix . '[' . $eventType . ']';
@endphp

<div class="notification-config-section border border-base-300 rounded-lg p-6 space-y-4" 
     data-event-type="{{ $eventType }}">
    
    <!-- Header with Enable Toggle -->
    <div class="flex items-center justify-between">
        <div>
            <h3 class="text-lg font-semibold text-base-content">
                <i class="fa-sharp fa-bell text-accent mr-2"></i>
                {{ $eventLabel }} Notifications
            </h3>
            <p class="text-sm text-base-content/70">
                Configure who gets notified when {{ strtolower($eventLabel) }} occurs
            </p>
        </div>
        
        <label class="label cursor-pointer">
            <input type="hidden" name="{{ $fieldName }}[is_enabled]" value="0">
            <input type="checkbox" 
                   name="{{ $fieldName }}[is_enabled]" 
                   value="1"
                   class="toggle toggle-accent notification-enabled-toggle"
                   {{ $isEnabled ? 'checked' : '' }}
                   onchange="toggleNotificationConfig(this, '{{ $eventType }}')">
            <span class="label-text ml-2">Enable</span>
        </label>
    </div>

    <!-- Configuration Options (hidden when disabled) -->
    <div class="notification-config-options space-y-4" 
         style="{{ $isEnabled ? '' : 'display: none;' }}">
        
        <!-- Hidden event type field -->
        <input type="hidden" name="{{ $fieldName }}[event_type]" value="{{ $eventType }}">
        
        <!-- Target Type Selection -->
        <div class="form-control">
            <label class="label">
                <span class="label-text font-medium">
                    <i class="fa-sharp fa-users text-accent mr-2"></i>
                    Who should be notified?
                </span>
            </label>
            
            <div class="space-y-3">
                <!-- All Users Option -->
                <label class="label cursor-pointer justify-start gap-3">
                    <input type="radio" 
                           name="{{ $fieldName }}[target_type]" 
                           value="all_users"
                           class="radio radio-accent target-type-radio"
                           {{ $targetType === 'all_users' ? 'checked' : '' }}
                           onchange="toggleTargetOptions('{{ $eventType }}', 'all_users')">
                    <span class="label-text">All Users</span>
                </label>
                
                <!-- User Groups Option -->
                <label class="label cursor-pointer justify-start gap-3">
                    <input type="radio" 
                           name="{{ $fieldName }}[target_type]" 
                           value="user_group"
                           class="radio radio-accent target-type-radio"
                           {{ $targetType === 'user_group' ? 'checked' : '' }}
                           onchange="toggleTargetOptions('{{ $eventType }}', 'user_group')">
                    <span class="label-text">Specific User Groups</span>
                </label>
                
                <!-- Specific Users Option -->
                <label class="label cursor-pointer justify-start gap-3">
                    <input type="radio" 
                           name="{{ $fieldName }}[target_type]" 
                           value="specific_users"
                           class="radio radio-accent target-type-radio"
                           {{ $targetType === 'specific_users' ? 'checked' : '' }}
                           onchange="toggleTargetOptions('{{ $eventType }}', 'specific_users')">
                    <span class="label-text">Specific Users</span>
                </label>
            </div>
        </div>

        <!-- User Groups Selection -->
        <div class="target-options target-user-groups" 
             style="{{ $targetType === 'user_group' ? '' : 'display: none;' }}">
            <label class="label">
                <span class="label-text">Select User Groups:</span>
            </label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-48 overflow-y-auto border border-base-300 rounded p-3">
                @foreach($userGroups as $group)
                    <label class="label cursor-pointer justify-start gap-2">
                        <input type="checkbox" 
                               name="{{ $fieldName }}[target_ids][]" 
                               value="{{ $group->id }}"
                               class="checkbox checkbox-accent checkbox-sm"
                               {{ in_array($group->id, $targetIds) ? 'checked' : '' }}>
                        <span class="label-text text-sm">{{ $group->name }}</span>
                    </label>
                @endforeach
            </div>
        </div>

        <!-- Specific Users Selection -->
        <div class="target-options target-specific-users" 
             style="{{ $targetType === 'specific_users' ? '' : 'display: none;' }}">
            <label class="label">
                <span class="label-text">Select Users:</span>
            </label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-48 overflow-y-auto border border-base-300 rounded p-3">
                @foreach($users as $user)
                    <label class="label cursor-pointer justify-start gap-2">
                        <input type="checkbox" 
                               name="{{ $fieldName }}[target_ids][]" 
                               value="{{ $user->id }}"
                               class="checkbox checkbox-accent checkbox-sm"
                               {{ in_array($user->id, $targetIds) ? 'checked' : '' }}>
                        <span class="label-text text-sm">{{ $user->name }}</span>
                    </label>
                @endforeach
            </div>
        </div>

        <!-- Urgency Level -->
        <div class="form-control">
            <label class="label">
                <span class="label-text font-medium">
                    <i class="fa-sharp fa-exclamation-triangle text-accent mr-2"></i>
                    Notification Priority
                </span>
            </label>
            <select name="{{ $fieldName }}[urgency]" class="select select-bordered">
                <option value="low" {{ $urgency === 'low' ? 'selected' : '' }}>Low</option>
                <option value="normal" {{ $urgency === 'normal' ? 'selected' : '' }}>Normal</option>
                <option value="high" {{ $urgency === 'high' ? 'selected' : '' }}>High</option>
                <option value="critical" {{ $urgency === 'critical' ? 'selected' : '' }}>Critical</option>
            </select>
        </div>

        @if($showAdvanced)
        <!-- Advanced Options Toggle -->
        <div class="divider">
            <button type="button" 
                    class="btn btn-ghost btn-sm"
                    onclick="toggleAdvancedOptions('{{ $eventType }}')">
                <i class="fa-sharp fa-cog mr-2"></i>
                Advanced Options
            </button>
        </div>

        <div class="advanced-options space-y-4" style="display: none;">
            <!-- Custom Title -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Custom Notification Title (optional)</span>
                </label>
                <input type="text" 
                       name="{{ $fieldName }}[custom_title]" 
                       value="{{ $customTitle }}"
                       class="input input-bordered"
                       placeholder="Leave empty for default title">
            </div>

            <!-- Custom Message -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Custom Notification Message (optional)</span>
                </label>
                <textarea name="{{ $fieldName }}[custom_message]" 
                          class="textarea textarea-bordered"
                          rows="3"
                          placeholder="Leave empty for default message">{{ $customMessage }}</textarea>
            </div>

            <!-- Link Options -->
            <div class="form-control">
                <label class="label cursor-pointer justify-start gap-3">
                    <input type="hidden" name="{{ $fieldName }}[include_link]" value="0">
                    <input type="checkbox" 
                           name="{{ $fieldName }}[include_link]" 
                           value="1"
                           class="checkbox checkbox-accent"
                           {{ $includeLink ? 'checked' : '' }}>
                    <span class="label-text">Include link to resource</span>
                </label>
            </div>

            <!-- Custom Link Text -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Custom Link Text (optional)</span>
                </label>
                <input type="text" 
                       name="{{ $fieldName }}[link_text]" 
                       value="{{ $linkText }}"
                       class="input input-bordered"
                       placeholder="e.g., 'View Submission'">
            </div>

            <!-- Expiration -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Auto-expire after (days)</span>
                </label>
                <input type="number" 
                       name="{{ $fieldName }}[expires_after_days]" 
                       value="{{ $expiresAfterDays }}"
                       class="input input-bordered"
                       min="1"
                       placeholder="Leave empty for no expiration">
            </div>

            <!-- Dismissible Options -->
            <div class="space-y-2">
                <div class="form-control">
                    <label class="label cursor-pointer justify-start gap-3">
                        <input type="hidden" name="{{ $fieldName }}[is_dismissible]" value="0">
                        <input type="checkbox" 
                               name="{{ $fieldName }}[is_dismissible]" 
                               value="1"
                               class="checkbox checkbox-accent"
                               {{ $isDismissible ? 'checked' : '' }}>
                        <span class="label-text">Allow users to dismiss notification</span>
                    </label>
                </div>

                <div class="form-control">
                    <label class="label cursor-pointer justify-start gap-3">
                        <input type="hidden" name="{{ $fieldName }}[auto_dismiss]" value="0">
                        <input type="checkbox" 
                               name="{{ $fieldName }}[auto_dismiss]" 
                               value="1"
                               class="checkbox checkbox-accent"
                               {{ $autoDismiss ? 'checked' : '' }}>
                        <span class="label-text">Auto-dismiss when read</span>
                    </label>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<script>
function toggleNotificationConfig(checkbox, eventType) {
    const section = checkbox.closest('.notification-config-section');
    const options = section.querySelector('.notification-config-options');
    
    if (checkbox.checked) {
        options.style.display = '';
    } else {
        options.style.display = 'none';
    }
}

function toggleTargetOptions(eventType, targetType) {
    const section = document.querySelector(`[data-event-type="${eventType}"]`);
    const allOptions = section.querySelectorAll('.target-options');
    
    // Hide all target options
    allOptions.forEach(option => {
        option.style.display = 'none';
    });
    
    // Show the selected target option
    if (targetType !== 'all_users') {
        const targetOption = section.querySelector(`.target-${targetType.replace('_', '-')}`);
        if (targetOption) {
            targetOption.style.display = '';
        }
    }
}

function toggleAdvancedOptions(eventType) {
    const section = document.querySelector(`[data-event-type="${eventType}"]`);
    const advanced = section.querySelector('.advanced-options');
    
    if (advanced.style.display === 'none') {
        advanced.style.display = '';
    } else {
        advanced.style.display = 'none';
    }
}
</script>
