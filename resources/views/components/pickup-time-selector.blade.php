@props([
    'name' => 'pickup_datetime',
    'value' => '',
    'required' => false,
    'label' => 'Pickup Date & Time',
    'icon' => 'fa-calendar-clock',
    'iconColor' => 'text-primary',
    'autoSave' => false,
    'autoSaveField' => null,
    'showWeekView' => true,
    'pickupRequestId' => null
])

@php
    $componentId = 'pickup-time-selector-' . uniqid();
    $dateInputId = $componentId . '-date';
    $timeInputId = $componentId . '-time';
    $weekViewId = $componentId . '-week-view';
    $hiddenInputId = $componentId . '-hidden';

    // Parse existing value if provided, otherwise default to tomorrow
    $existingDate = '';
    $existingTime = '';
    if ($value) {
        try {
            $datetime = new DateTime($value);
            $existingDate = $datetime->format('Y-m-d');
            $existingTime = $datetime->format('H:i');
        } catch (Exception $e) {
            // Invalid datetime, use defaults
        }
    }

    // If no existing date, default to tomorrow
    if (empty($existingDate)) {
        $existingDate = date('Y-m-d', strtotime('+1 day'));
    }

    // If no existing time, default to 9:00 AM
    if (empty($existingTime)) {
        $existingTime = '09:00';
    }
@endphp

<fieldset class="fieldset">
    <legend class="fieldset-legend">
        <i class="fa-sharp {{ $icon }} {{ $iconColor }} mr-2"></i>
        {{ $label }} @if($required)<span class="text-error font-bold">*</span>@endif
    </legend>

    <div class="space-y-4">
        <!-- Date and Time Inputs -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Pickup Date -->
            <div>
                <label class="label" for="{{ $dateInputId }}">
                    <span class="label-text">
                        <i class="fa-sharp fa-calendar {{ $iconColor }} mr-2"></i>
                        Pickup Date @if($required)<span class="text-error font-bold">*</span>@endif
                    </span>
                </label>
                <div class="relative">
                    <input type="date"
                           name="{{ $name }}_date"
                           id="{{ $dateInputId }}"
                           class="input input-bordered w-full {{ $autoSave ? 'auto-save-datetime' : '' }}"
                           @if($autoSave && $autoSaveField) data-field="{{ $autoSaveField }}" @endif
                           value="{{ $existingDate }}"
                           min="{{ date('Y-m-d') }}"
                           @if($required) required @endif>
                    @if($autoSave)
                        <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                            <i class="fa-sharp fa-spinner fa-spin {{ $iconColor }}"></i>
                        </span>
                    @endif
                </div>
                @error($name . '_date')
                    <div class="text-error text-sm mt-1">{{ $message }}</div>
                @enderror
            </div>

            <!-- Pickup Time -->
            <div>
                <label class="label" for="{{ $timeInputId }}">
                    <span class="label-text">
                        <i class="fa-sharp fa-clock {{ $iconColor }} mr-2"></i>
                        Pickup Time @if($required)<span class="text-error font-bold">*</span>@endif
                    </span>
                </label>
                <div class="relative">
                    <input type="time"
                           name="{{ $name }}_time"
                           id="{{ $timeInputId }}"
                           class="input input-bordered w-full {{ $autoSave ? 'auto-save-datetime' : '' }}"
                           @if($autoSave && $autoSaveField) data-field="{{ $autoSaveField }}" @endif
                           value="{{ $existingTime ?: '09:00' }}"
                           @if($required) required @endif>
                    @if($autoSave)
                        <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                            <i class="fa-sharp fa-spinner fa-spin {{ $iconColor }}"></i>
                        </span>
                    @endif
                </div>
                @error($name . '_time')
                    <div class="text-error text-sm mt-1">{{ $message }}</div>
                @enderror
            </div>
        </div>

        @if($showWeekView)
            <!-- Week View Calendar -->
            <div>
                <label class="label">
                    <span class="label-text">
                        <i class="fa-sharp fa-calendar-week {{ $iconColor }} mr-2"></i>
                        Schedule Preview (3 Months)
                    </span>
                </label>
                
                <div id="{{ $weekViewId }}-container" class="border border-base-300 rounded-lg bg-base-50 min-h-[500px]">
                    <div id="{{ $weekViewId }}" class="h-[420px]"></div>
                    <div id="{{ $weekViewId }}-placeholder" class="text-center text-base-content/50 py-8">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                <i class="fa-sharp fa-calendar text-2xl"></i>
                            </div>
                        </div>
                        <p class="mt-2">Select date and time to preview week schedule</p>
                    </div>
                </div>

                <div class="mt-3 text-xs text-base-content/70">
                    <div class="grid grid-cols-2 gap-2">
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 rounded" style="background-color: #10b981;"></div>
                            <span>Proposed Pickup</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 rounded" style="background-color: #3b82f6;"></div>
                            <span>Existing Pickups</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 rounded" style="background-color: #ef4444;"></div>
                            <span>Blocked Times</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 rounded" style="background-color: #8b5cf6;"></div>
                            <span>Available Slots (click to select)</span>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Hidden field for combined datetime -->
        <input type="hidden" name="{{ $name }}" id="{{ $hiddenInputId }}" value="{{ $value }}" @if($required) required @endif>

        @error($name)
            <div class="text-error text-sm mt-1">{{ $message }}</div>
        @enderror

        <div class="text-sm text-base-content/60">
            <i class="fa-sharp fa-info-circle mr-1"></i>
            @if($showWeekView)
                Select date and time to preview schedule. Use ‹ › buttons to navigate through the next 3 months, or click available slots to select
            @else
                Select your preferred pickup date and time
            @endif
        </div>
    </div>
</fieldset>

@if($showWeekView)
    @push('styles')
        <!-- Include FullCalendar library -->
        <link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css' rel='stylesheet' />
        <style>
            /* Custom styles for the week calendar */
            #{{ $weekViewId }} .fc-toolbar {
                margin-bottom: 0.75rem;
                padding: 0.75rem;
                background-color: hsl(var(--b2));
                border-radius: 0.5rem;
                border: 1px solid hsl(var(--b3));
            }

            #{{ $weekViewId }} .fc-toolbar-title {
                font-size: 1.125rem;
                font-weight: 600;
                color: hsl(var(--bc));
            }

            #{{ $weekViewId }} .fc-button {
                @apply btn btn-sm;
                min-width: 3rem;
                height: 2.5rem;
            }

            #{{ $weekViewId }} .fc-today-button {
                @apply btn btn-primary btn-sm;
            }

            #{{ $weekViewId }} .fc-customPrev-button,
            #{{ $weekViewId }} .fc-customNext-button {
                @apply btn btn-outline btn-sm;
                min-width: 4rem;
            }

            #{{ $weekViewId }} .fc-weekLabel-button {
                @apply btn btn-ghost btn-sm;
                cursor: default;
                pointer-events: none;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                color: hsl(var(--bc) / 0.6);
            }

            #{{ $weekViewId }} .fc-weekLabel-button {
                background-color: transparent;
                border: none;
                color: #6b7280;
                font-weight: 600;
                cursor: default;
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
                letter-spacing: 0.05em;
                text-transform: uppercase;
            }

            #{{ $weekViewId }} .fc-weekLabel-button:hover {
                background-color: transparent;
                border: none;
                transform: none;
                box-shadow: none;
                color: #6b7280;
            }

            #{{ $weekViewId }} .fc-timegrid-slot {
                height: 1.5rem;
            }

            #{{ $weekViewId }} .fc-event {
                font-size: 0.7rem;
                border-radius: 0.25rem;
                padding: 1px 2px;
                line-height: 1.2;
            }

            #{{ $weekViewId }} .fc-event-title {
                font-weight: 500;
            }

            #{{ $weekViewId }} .available-slot {
                cursor: pointer !important;
                transition: opacity 0.2s ease;
            }

            #{{ $weekViewId }} .available-slot:hover {
                opacity: 0.8 !important;
            }

            #{{ $weekViewId }} .proposed-event {
                font-weight: 600;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
                animation: proposedGlow 2s ease-in-out infinite alternate;
                border: 2px solid rgba(16, 185, 129, 0.6) !important;
            }

            @keyframes proposedGlow {
                from {
                    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
                }
                to {
                    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.7);
                }
            }
        </style>
    @endpush

    @push('scripts')
        <script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js'></script>
    @endpush
@endif

<script>
document.addEventListener('DOMContentLoaded', function() {
    const componentId = '{{ $componentId }}';
    const dateInput = document.getElementById('{{ $dateInputId }}');
    const timeInput = document.getElementById('{{ $timeInputId }}');
    const hiddenInput = document.getElementById('{{ $hiddenInputId }}');
    const showWeekView = {{ $showWeekView ? 'true' : 'false' }};
    const autoSave = {{ $autoSave ? 'true' : 'false' }};
    const autoSaveField = '{{ $autoSaveField }}';
    const pickupRequestId = {{ $pickupRequestId ? $pickupRequestId : 'null' }};
    
    let weekCalendar = null;
    let currentViewDate = null; // Track which week we're currently viewing
    let navigationData = null; // Store navigation boundaries

    // Update hidden input when date or time changes
    function updateHiddenInput() {
        const date = dateInput.value;
        const time = timeInput.value;
        
        if (date && time) {
            const datetime = date + 'T' + time + ':00';
            hiddenInput.value = datetime;
            
            // Auto-save if enabled
            if (autoSave && autoSaveField) {
                saveDateTime(datetime);
            }
            
            // Update week view if enabled
            if (showWeekView) {
                updateWeekView();
            }
        } else {
            hiddenInput.value = '';
        }
    }

    // Auto-save function
    function saveDateTime(datetime) {
        const indicator = dateInput.parentNode.querySelector('.saving-indicator') || 
                         timeInput.parentNode.querySelector('.saving-indicator');
        
        if (indicator) {
            indicator.classList.remove('hidden');
        }

        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        fetch('{{ route("pickup-requests.auto-save") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken,
                'X-Auto-Save': 'true'
            },
            body: JSON.stringify({
                field: autoSaveField,
                value: datetime
            })
        })
        .then(response => response.json())
        .then(data => {
            if (indicator) {
                indicator.classList.add('hidden');
            }
            if (data.success) {
                // Show auto-save status if available
                const statusIndicator = document.getElementById('autoSaveStatus');
                if (statusIndicator) {
                    statusIndicator.classList.remove('hidden');
                    setTimeout(() => {
                        statusIndicator.classList.add('hidden');
                    }, 3000);
                }
            }
        })
        .catch(error => {
            if (indicator) {
                indicator.classList.add('hidden');
            }
            console.error('Auto-save error:', error);
        });
    }

    // Add event listeners
    dateInput.addEventListener('change', updateHiddenInput);
    timeInput.addEventListener('change', updateHiddenInput);

    // Week view functionality
    async function updateWeekView(viewDate = null) {
        if (!showWeekView) return;

        const pickupDate = dateInput.value;
        const pickupTime = timeInput.value;
        const weekViewEl = document.getElementById('{{ $weekViewId }}');
        const weekViewPlaceholder = document.getElementById('{{ $weekViewId }}-placeholder');

        // For initial calendar display without pre-selected date/time, use default values
        const today = new Date();
        const defaultDate = pickupDate || today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');
        const defaultTime = pickupTime || '09:00';
        
        // Only hide calendar if showWeekView is explicitly false or if both are missing in non-week view
        if (!showWeekView && (!pickupDate || !pickupTime)) {
            // Show placeholder and hide calendar
            weekViewEl.style.display = 'none';
            weekViewPlaceholder.style.display = 'block';
            if (weekCalendar) {
                weekCalendar.destroy();
                weekCalendar = null;
            }
            currentViewDate = null;
            navigationData = null;
            return;
        }

        // Hide placeholder and show calendar
        weekViewPlaceholder.style.display = 'none';
        weekViewEl.style.display = 'block';

        try {
            // Build URL with optional view_date parameter, using default values if needed
            let url = pickupRequestId ?
                `/pickup-requests/${pickupRequestId}/week-view?pickup_date=${defaultDate}&pickup_time=${defaultTime}` :
                `/pickup-requests/week-view?pickup_date=${defaultDate}&pickup_time=${defaultTime}`;

            if (viewDate) {
                url += `&view_date=${viewDate}`;
                currentViewDate = viewDate;
            } else {
                currentViewDate = defaultDate;
            }

            const response = await fetch(url);
            const data = await response.json();

            if (data.error) {
                weekViewEl.style.display = 'none';
                weekViewPlaceholder.style.display = 'block';
                weekViewPlaceholder.innerHTML = `
                    <div class="text-center py-8 text-error">
                        <i class="fa-sharp fa-exclamation-triangle text-2xl mb-2"></i>
                        <p>${data.error}</p>
                    </div>
                `;
                return;
            }

            // Store navigation data
            navigationData = data.navigation;

            // Destroy existing calendar if it exists
            if (weekCalendar) {
                weekCalendar.destroy();
            }

            // Initialize FullCalendar with the week view
            const selectedDate = viewDate ? new Date(viewDate) : new Date(defaultDate + 'T' + defaultTime);
            
            weekCalendar = new FullCalendar.Calendar(weekViewEl, {
                initialView: 'timeGridWeek',
                initialDate: selectedDate,
                headerToolbar: {
                    left: 'customPrev,weekLabel,customNext',
                    center: 'title',
                    right: 'today'
                },
                customButtons: {
                    customPrev: {
                        text: '‹ Prev',
                        click: function() {
                            navigateWeek('prev');
                        }
                    },
                    weekLabel: {
                        text: 'Week',
                        click: function() {
                            // Do nothing - this is just a label
                        }
                    },
                    customNext: {
                        text: 'Next ›',
                        click: function() {
                            navigateWeek('next');
                        }
                    }
                },
                height: 'auto',
                timeZone: 'local',
                slotMinTime: '09:00:00',
                slotMaxTime: '17:00:00',
                slotDuration: '00:30:00',
                slotLabelInterval: '01:00:00',
                allDaySlot: false,
                displayEventTime: false,
                validRange: {
                    start: data.navigation ? data.navigation.range_start : null,
                    end: data.navigation ? data.navigation.range_end : null
                },
                events: data.events.map(event => {
                    let backgroundColor = '#3b82f6'; // Primary blue
                    let borderColor = '#3b82f6';
                    let textColor = '#ffffff';
                    let classNames = [];
                    let eventStart = event.start;
                    let eventEnd = event.end;
                    let eventTitle = event.title;

                    if (event.is_proposed) {
                        backgroundColor = '#10b981'; // Success green
                        borderColor = '#10b981';
                        classNames.push('proposed-event');
                        // Make proposed time take up a full hour
                        const startTime = new Date(event.start);
                        const endTime = new Date(startTime.getTime() + (60 * 60 * 1000)); // Add 1 hour
                        eventEnd = endTime.getFullYear() + '-' + String(endTime.getMonth() + 1).padStart(2, '0') + '-' + String(endTime.getDate()).padStart(2, '0') + 'T' + String(endTime.getHours()).padStart(2, '0') + ':' + String(endTime.getMinutes()).padStart(2, '0') + ':' + String(endTime.getSeconds()).padStart(2, '0');
                    } else if (event.is_blockout_event) {
                        backgroundColor = '#ef4444'; // Error red
                        borderColor = '#ef4444';
                        classNames.push('blockout-event');
                    } else if (event.is_available_slot) {
                        backgroundColor = '#8b5cf6'; // Accent purple
                        borderColor = '#8b5cf6';
                        classNames.push('available-slot', 'cursor-pointer');
                        // For available slots, show only start time
                        const startTime = new Date(event.start);
                        const timeString = startTime.toLocaleTimeString('en-US', {
                            hour: 'numeric',
                            minute: '2-digit',
                            hour12: true
                        });
                        eventTitle = timeString;
                    } else {
                        // For already scheduled events, show only start time in title
                        const startTime = new Date(event.start);
                        const timeString = startTime.toLocaleTimeString('en-US', {
                            hour: 'numeric',
                            minute: '2-digit',
                            hour12: true
                        });
                        eventTitle = `${timeString} - ${event.title}`;
                        // Keep the actual event duration (use actual_end if available)
                        if (event.actual_end) {
                            eventEnd = event.actual_end;
                        }
                    }

                    return {
                        id: event.id,
                        title: eventTitle,
                        start: eventStart,
                        end: eventEnd,
                        backgroundColor: backgroundColor,
                        borderColor: borderColor,
                        textColor: textColor,
                        classNames: classNames,
                        extendedProps: {
                            isAvailableSlot: event.is_available_slot,
                            timeSlot: event.time_slot,
                            date: event.date,
                            isProposed: event.is_proposed,
                            isBlockout: event.is_blockout_event,
                            actualEnd: event.actual_end
                        }
                    };
                }),
                eventClick: function(info) {
                    if (info.event.extendedProps.isAvailableSlot) {
                        selectTimeSlot(info.event.extendedProps.date, info.event.extendedProps.timeSlot);
                    }
                },
                eventMouseEnter: function(info) {
                    if (info.event.extendedProps.isAvailableSlot) {
                        info.el.style.cursor = 'pointer';
                        info.el.style.opacity = '0.8';
                    }
                },
                eventMouseLeave: function(info) {
                    if (info.event.extendedProps.isAvailableSlot) {
                        info.el.style.opacity = '1';
                    }
                }
            });

            weekCalendar.render();

            // Update navigation button states and apply DaisyUI classes
            if (navigationData) {
                const prevBtn = weekViewEl.querySelector('.fc-customPrev-button');
                const nextBtn = weekViewEl.querySelector('.fc-customNext-button');
                const todayBtn = weekViewEl.querySelector('.fc-today-button');
                const weekLabel = weekViewEl.querySelector('.fc-weekLabel-button');

                // Apply DaisyUI classes to buttons
                if (prevBtn) {
                    prevBtn.className = 'fc-customPrev-button btn btn-outline btn-sm';
                    prevBtn.disabled = !navigationData.can_go_prev;
                    if (!navigationData.can_go_prev) {
                        prevBtn.classList.add('btn-disabled');
                    }
                }

                if (nextBtn) {
                    nextBtn.className = 'fc-customNext-button btn btn-outline btn-sm';
                    nextBtn.disabled = !navigationData.can_go_next;
                    if (!navigationData.can_go_next) {
                        nextBtn.classList.add('btn-disabled');
                    }
                }

                if (todayBtn) {
                    todayBtn.className = 'fc-today-button btn btn-primary btn-sm';
                }

                if (weekLabel) {
                    weekLabel.className = 'fc-weekLabel-button btn btn-ghost btn-sm';
                    weekLabel.style.pointerEvents = 'none';
                }
            }

        } catch (error) {
            console.error('Error loading week view:', error);
            weekViewEl.style.display = 'none';
            weekViewPlaceholder.style.display = 'block';
            weekViewPlaceholder.innerHTML = `
                <div class="text-center py-8 text-error">
                    <i class="fa-sharp fa-exclamation-triangle text-2xl mb-2"></i>
                    <p>Error loading week view</p>
                </div>
            `;
        }
    }

    // Navigation function for week view
    function navigateWeek(direction) {
        if (!navigationData) return;

        let newViewDate = null;

        if (direction === 'prev' && navigationData.can_go_prev) {
            newViewDate = navigationData.prev_week_start.split('T')[0]; // Extract date part
        } else if (direction === 'next' && navigationData.can_go_next) {
            newViewDate = navigationData.next_week_start.split('T')[0]; // Extract date part
        }

        if (newViewDate) {
            updateWeekView(newViewDate);
        }
    }

    function selectTimeSlot(date, timeSlot) {
        // Update the form fields with the selected date and time
        dateInput.value = date;
        timeInput.value = timeSlot;

        // Update hidden input
        updateHiddenInput();

        // Refresh the week view to show the new proposed time
        updateWeekView();

        // Show a brief confirmation
        const toast = document.createElement('div');
        toast.className = 'toast toast-top toast-end z-50';
        toast.innerHTML = `
            <div class="alert alert-success">
                <i class="fa-sharp fa-check-circle"></i>
                <span>Time slot selected: ${new Date(date + ' ' + timeSlot).toLocaleString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric',
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                })}</span>
            </div>
        `;
        document.body.appendChild(toast);

        // Remove toast after 3 seconds
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    // Initialize if values are already set
    if (dateInput.value && timeInput.value) {
        updateHiddenInput();

        // Auto-load week view if enabled and we have default values
        if (showWeekView) {
            setTimeout(() => {
                updateWeekView();
            }, 100);
        }
    } else if (showWeekView) {
        // Even without initial values, show the week view for selection
        setTimeout(() => {
            updateWeekView();
        }, 100);
    }
});
</script>
