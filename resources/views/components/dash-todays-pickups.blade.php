@props(['todaysPickups'])

@php
    $timezone = \App\Models\GlobalConfig::getTimeZone();
@endphp

<div class="space-y-4">
    @if($todaysPickups->count() > 0)
        <!-- Summary Stats -->
        <div class="grid grid-cols-3 gap-4 mb-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-success">{{ $todaysPickups->where('status', 'confirmed')->count() }}</div>
                <div class="text-xs text-base-content/60">Confirmed</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-warning">{{ $todaysPickups->where('status', 'pending')->count() }}</div>
                <div class="text-xs text-base-content/60">Pending</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-info">{{ $todaysPickups->count() }}</div>
                <div class="text-xs text-base-content/60">Total</div>
            </div>
        </div>

        <!-- Pickup List -->
        <div class="space-y-3 max-h-96 overflow-y-auto">
            @foreach($todaysPickups as $pickup)
                @php
                    $pickupTime = null;
                    $leaveTime = null;
                    
                    // Determine pickup time
                    if ($pickup->event && $pickup->event->start_date) {
                        $pickupTime = $pickup->event->start_date->setTimezone($timezone);
                    } else {
                        $pickupTime = $pickup->preferred_pickup_date->setTimezone($timezone);
                    }
                    
                    // Status styling
                    $statusClasses = [
                        'confirmed' => 'border-l-success bg-success/5',
                        'pending' => 'border-l-warning bg-warning/5',
                        'incoming' => 'border-l-info bg-info/5',
                        'completed' => 'border-l-neutral bg-neutral/5',
                        'cancelled' => 'border-l-error bg-error/5'
                    ];
                    $statusClass = $statusClasses[$pickup->status] ?? 'border-l-base-300 bg-base-100';
                @endphp
                
                <a href="{{ route('pickup-requests.show', $pickup) }}" 
                   class="block border-l-4 {{ $statusClass }} p-4 rounded-r-lg hover:shadow-md transition-shadow duration-200">
                    
                    <!-- Header with time and status -->
                    <div class="flex justify-between items-start mb-2">
                        <div class="flex items-center gap-2">
                            @if($pickup->status === 'pending')
                                <i class="fa-sharp fa-exclamation-triangle text-warning text-sm"></i>
                            @endif
                            <span class="font-semibold text-base-content">
                                {{ $pickupTime->format('g:i A') }}
                            </span>
                        </div>
                        <span class="badge badge-sm 
                            @if($pickup->status === 'confirmed') badge-success
                            @elseif($pickup->status === 'pending') badge-warning
                            @elseif($pickup->status === 'incoming') badge-info
                            @elseif($pickup->status === 'completed') badge-neutral
                            @else badge-error
                            @endif">
                            {{ ucfirst($pickup->status) }}
                        </span>
                    </div>
                    
                    <!-- Customer/Business Name -->
                    <div class="font-medium text-base-content mb-1">
                        @if($pickup->business_name)
                            {{ $pickup->business_name }}
                            <div class="text-sm text-base-content/70">{{ $pickup->contact_name }}</div>
                        @else
                            {{ $pickup->contact_name }}
                        @endif
                    </div>
                    
                    <!-- Details Grid -->
                    <div class="grid grid-cols-2 gap-2 text-sm text-base-content/70">
                        @if($pickup->load_size)
                            <div>
                                <i class="fa-sharp fa-box text-xs mr-1"></i>
                                {{ ucfirst($pickup->load_size) }} load
                            </div>
                        @endif

                        @if($pickup->accessibility_level)
                            <div>
                                <i class="fa-sharp fa-wheelchair text-xs mr-1"></i>
                                {{ ucfirst($pickup->accessibility_level) }}
                            </div>
                        @endif

                        @if(isset($pickup->distance_text))
                            <div>
                                <i class="fa-sharp fa-route text-xs mr-1"></i>
                                {{ $pickup->distance_text }}
                            </div>
                        @endif

                        @if(isset($pickup->leave_time))
                            <div>
                                <i class="fa-sharp fa-clock text-xs mr-1"></i>
                                Leave: {{ $pickup->leave_time->format('g:i A') }}
                            </div>
                        @endif
                    </div>
                    
                    <!-- Address (truncated) -->
                    <div class="text-sm text-base-content/60 mt-2 truncate">
                        <i class="fa-sharp fa-map-marker-alt text-xs mr-1"></i>
                        {{ $pickup->pickup_address }}
                    </div>
                </a>
            @endforeach
        </div>
        
        <!-- View All Link -->
        <div class="pt-3 border-t border-base-300">
            <a href="{{ route('pickup-requests.index') }}" 
               class="btn btn-sm btn-outline w-full">
                <i class="fa-sharp fa-calendar-day mr-2"></i>
                View All Pickup Requests
            </a>
        </div>
    @else
        <!-- No pickups today -->
        <div class="text-center py-8">
            <i class="fa-sharp fa-calendar-check text-4xl text-base-content/30 mb-4"></i>
            <p class="text-base-content/60">No pickup requests scheduled for today</p>
            <a href="{{ route('pickup-requests.create') }}" 
               class="btn btn-sm btn-primary mt-4">
                <i class="fa-sharp fa-plus mr-2"></i>
                Create New Pickup Request
            </a>
        </div>
    @endif
</div>
