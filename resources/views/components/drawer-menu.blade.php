@php
  $sidebarCollapsed = session('sidebar_collapsed', false);
  $sidebarWidth = $sidebarCollapsed ? '64px' : '256px';
  $mainContentPadding = $sidebarCollapsed ? '64px' : '256px';
@endphp

<div class="flex h-full overflow-hidden flex-grow">
  <!-- Sidebar Background Fill (Desktop Only) -->
  <div
    class="hidden lg:block fixed left-0 top-0 bg-neutral transition-all duration-300 z-30"
    id="sidebarBackground"
    style="width: {{ $sidebarWidth }}; height: 100vh;"
  ></div>

  <!-- Sidebar -->
  <aside
    class="sidebar-container fixed left-0 top-0 bg-neutral text-base-content flex flex-col transform -translate-x-full lg:translate-x-0 lg:fixed transition-all duration-300 z-40 {{ $sidebarCollapsed ? 'sidebar-collapsed' : '' }}"
    id="sidebar"
    data-collapsed="{{ $sidebarCollapsed ? 'true' : 'false' }}"
    style="width: {{ $sidebarWidth }}; height: 100vh;"
  >

    <!-- Mobile Close Button - only visible on mobile when menu is open -->
    <div class="lg:hidden flex items-center justify-between p-4 border-b border-neutral-content/20">
      <div class="flex items-center">
        <img src="{{ asset('img/etrflow-full.webp') }}" alt="ETRFlow" class="h-8 w-auto">
      </div>
      <button
        id="mobileMenuClose"
        class="p-2 bg-neutral-focus hover:bg-neutral-content/20 text-neutral-content rounded-lg transition-colors duration-200"
        aria-label="Close Menu"
      >
        <i class="fa-sharp fa-solid fa-times text-xl"></i>
      </button>
    </div>

    <!-- Desktop Collapse Toggle Bar -->
    <div class="hidden lg:block fixed right-0 z-10" id="collapseToggleContainer">
      <button
        id="sidebarCollapseToggle"
        class="cursor-pointer bg-neutral hover:bg-neutral-focus text-neutral-content p-2 rounded-r-md shadow-lg transition-colors duration-200"
        title="Toggle Sidebar"
      >
        <i class="fa-sharp fa-solid {{ $sidebarCollapsed ? 'fa-chevron-right' : 'fa-chevron-left' }} text-sm" id="collapseIcon"></i>
      </button>
    </div>

    <!-- Logo Section - Desktop only -->
    <div class="p-4 border-b border-neutral-content/20 hidden lg:block">
      <a href="{{ route('dashboard') }}" class="flex items-center justify-center">
        <img src="{{ asset('img/etrflow-full.webp') }}" alt="ETRFlow" class="h-8 w-auto sidebar-logo">
        <img src="{{ asset('img/etrflow-icon.webp') }}" alt="ETRFlow" class="h-8 w-8 sidebar-logo-collapsed hidden">
      </a>
    </div>

    <nav class="flex-1 p-4 sidebar-nav overflow-y-auto overflow-x-hidden">
      <x-dynamic-sidebar-menu />
      @if(\App\Models\GlobalConfig::isTasksEnabled())
        <livewire:task-summary :user="auth()->user()" />
      @endif
    </nav>

  </aside>

  <!-- Mobile Toggle Button removed - now in header -->

  <!-- Main Content -->
  <main class="w-full min-h-screen overflow-x-hidden transition-all duration-300" id="mainContent" style="padding-left: {{ $mainContentPadding }};">
    {{ $slot }}
  </main>
</div>

<script>
  const sidebar = document.getElementById('sidebar');
  const sidebarToggle = document.getElementById('headerSidebarToggle');
  const mobileMenuClose = document.getElementById('mobileMenuClose');
  const collapseToggle = document.getElementById('sidebarCollapseToggle');
  const collapseIcon = document.getElementById('collapseIcon');

  // Sidebar collapse state management
  let isCollapsed = document.getElementById('sidebar').getAttribute('data-collapsed') === 'true';

  // Sync with localStorage for client-side consistency
  if (localStorage.getItem('sidebarCollapsed') !== null) {
    const localStorageState = localStorage.getItem('sidebarCollapsed') === 'true';
    if (localStorageState !== isCollapsed) {
      // If local storage differs from server state, update server
      updateServerSidebarState(localStorageState);
      isCollapsed = localStorageState;
    }
  } else {
    // Set localStorage to match server state
    localStorage.setItem('sidebarCollapsed', isCollapsed);
  }

  // Function to update server-side sidebar state
  function updateServerSidebarState(collapsed) {
    fetch('{{ route('sidebar.state') }}', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      },
      body: JSON.stringify({ collapsed: collapsed })
    }).catch(error => {
      console.log('Failed to update sidebar state:', error);
    });
  }

  // Position the collapse toggle in the middle of the viewport
  function positionCollapseToggle() {
    const toggleContainer = document.getElementById('collapseToggleContainer');
    if (toggleContainer && window.innerWidth >= 1024) {
      const viewportHeight = window.innerHeight;
      const toggleHeight = toggleContainer.offsetHeight;
      const topPosition = (viewportHeight / 2) - (toggleHeight / 2);

      if (isCollapsed) {
        toggleContainer.style.left = '64px'; // w-16 = 64px
      } else {
        toggleContainer.style.left = '256px'; // w-64 = 256px
      }
      toggleContainer.style.top = topPosition + 'px';
    }
  }

  // Apply initial collapse state
  function applySidebarState() {
    if (window.innerWidth >= 1024) { // Only on desktop
      const mainContent = document.getElementById('mainContent');
      const sidebarBackground = document.getElementById('sidebarBackground');

      if (isCollapsed) {
        sidebar.classList.add('sidebar-collapsed');
        sidebar.setAttribute('data-collapsed', 'true');
        sidebar.style.width = '64px'; // Collapsed width
        collapseIcon.classList.remove('fa-chevron-left');
        collapseIcon.classList.add('fa-chevron-right');

        // Adjust sidebar background width
        if (sidebarBackground) {
          sidebarBackground.style.width = '64px';
        }

        // Adjust main content padding
        if (mainContent) {
          mainContent.style.paddingLeft = '64px';
        }
      } else {
        sidebar.classList.remove('sidebar-collapsed');
        sidebar.setAttribute('data-collapsed', 'false');
        sidebar.style.width = '256px'; // Expanded width
        collapseIcon.classList.remove('fa-chevron-right');
        collapseIcon.classList.add('fa-chevron-left');

        // Adjust sidebar background width
        if (sidebarBackground) {
          sidebarBackground.style.width = '256px';
        }

        // Adjust main content padding
        if (mainContent) {
          mainContent.style.paddingLeft = '256px';
        }
      }
      positionCollapseToggle();

      // Re-attach flyout event listeners after state change
      attachFlyoutListeners();
    }
  }

  // Toggle sidebar collapse
  function toggleSidebarCollapse() {
    console.log('toggleSidebarCollapse called, current isCollapsed:', isCollapsed);

    if (window.innerWidth >= 1024) { // Only on desktop
      // Close dropdowns BEFORE changing state if we're about to collapse
      if (!isCollapsed) {
        console.log('About to collapse sidebar, closing dropdowns first');
        closeAllDropdowns();
      }

      // Always hide flyout menus when toggling sidebar state
      hideAllFlyoutMenus();

      isCollapsed = !isCollapsed;
      console.log('New isCollapsed state:', isCollapsed);
      localStorage.setItem('sidebarCollapsed', isCollapsed);

      // Update server state
      updateServerSidebarState(isCollapsed);

      applySidebarState();
    }
  }

  // Add event listener for collapse toggle
  if (collapseToggle) {
    collapseToggle.addEventListener('click', toggleSidebarCollapse);
  }

  // Ensure sidebar has proper positioning
  function adjustMobileElements() {
    const mainContent = document.getElementById('mainContent');

    if (window.innerWidth < 1024) { // lg breakpoint - mobile
      // For mobile, sidebar should be full-screen
      sidebar.style.top = '0';
      sidebar.style.height = '100vh';
      sidebar.style.width = '100vw'; // Full screen width on mobile

      // Reset collapse state on mobile
      sidebar.classList.remove('sidebar-collapsed');
      sidebar.setAttribute('data-collapsed', 'false');

      // Reset main content padding on mobile
      if (mainContent) {
        mainContent.style.paddingLeft = '0';
        mainContent.style.marginTop = '0';
      }
    } else { // Desktop
      // On desktop, sidebar starts from top of page with normal width
      sidebar.style.top = '0';
      sidebar.style.height = '100vh';
      sidebar.style.width = ''; // Reset to CSS-controlled width

      // Reset main content margin-top on desktop - header is positioned relative to sidebar
      if (mainContent) {
        mainContent.style.marginTop = '0';
      }

      // Apply collapse state on desktop
      applySidebarState();
    }
  }

  // Run on load and resize
  window.addEventListener('resize', () => {
    adjustMobileElements();
    positionCollapseToggle();
  });
  window.addEventListener('scroll', positionCollapseToggle);
  adjustMobileElements();



  // Function to close the mobile menu
  function closeMobileMenu() {
    sidebar.classList.add('-translate-x-full');

    // Show the header when menu is closed
    const header = document.getElementById('stickyHeader');
    const bannerContainer = document.getElementById('bannerContainer');

    if (header) {
      header.style.transform = 'translateY(0)';

      // Also show the banner if it exists
      if (bannerContainer) {
        bannerContainer.style.transform = 'translateY(0)';
      }

      // Update the header visibility state in the header's script
      if (window.isHeaderVisible !== undefined) {
        window.isHeaderVisible = true;
      }
    }
  }

  // Function to open the mobile menu
  function openMobileMenu() {
    sidebar.classList.remove('-translate-x-full');

    // Hide the header when menu is opened
    const header = document.getElementById('stickyHeader');
    const bannerContainer = document.getElementById('bannerContainer');

    if (header) {
      header.style.transform = 'translateY(-100%)';

      // Also hide the banner if it exists
      if (bannerContainer) {
        bannerContainer.style.transform = 'translateY(-100%)';
      }

      // Update the header visibility state in the header's script
      if (window.isHeaderVisible !== undefined) {
        window.isHeaderVisible = false;
      }
    }
  }

  // Header toggle button event listener
  sidebarToggle.addEventListener('click', () => {
    if (sidebar.classList.contains('-translate-x-full')) {
      openMobileMenu();
    } else {
      closeMobileMenu();
    }
  });

  // Mobile close button event listener
  if (mobileMenuClose) {
    mobileMenuClose.addEventListener('click', () => {
      closeMobileMenu();
    });
  }

  // Keyboard support - close menu with Escape key on mobile
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && window.innerWidth < 1024 && !sidebar.classList.contains('-translate-x-full')) {
      closeMobileMenu();
    }
  });



  // Handle page navigation - ensure header is visible on new pages
  window.addEventListener('beforeunload', () => {
    const header = document.getElementById('stickyHeader');
    const bannerContainer = document.getElementById('bannerContainer');

    if (header) {
      header.style.transform = 'translateY(0)';

      if (bannerContainer) {
        bannerContainer.style.transform = 'translateY(0)';
      }

      if (window.isHeaderVisible !== undefined) {
        window.isHeaderVisible = true;
      }
    }
  });

  // Also handle page visibility changes (for SPA-like navigation)
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      // Page became visible again, ensure header is shown
      const header = document.getElementById('stickyHeader');
      const bannerContainer = document.getElementById('bannerContainer');

      if (header) {
        header.style.transform = 'translateY(0)';

        if (bannerContainer) {
          bannerContainer.style.transform = 'translateY(0)';
        }

        if (window.isHeaderVisible !== undefined) {
          window.isHeaderVisible = true;
        }
      }
    }
  });

  // Function to close all open dropdowns
  function closeAllDropdowns() {
    console.log('closeAllDropdowns called');

    // Find all dropdown menus - they are UL elements inside dropdown-item that are not flyout-menu
    const dropdowns = document.querySelectorAll('.dropdown-item ul:not(.flyout-menu ul)');
    console.log('Found dropdowns:', dropdowns.length);

    dropdowns.forEach((dropdown, index) => {
      console.log(`Closing dropdown ${index}:`, dropdown);
      const wasVisible = !dropdown.classList.contains('hidden');
      dropdown.classList.add('hidden');
      if (wasVisible) {
        console.log(`Dropdown ${index} was visible, now hiding`);
      }
    });

    // Reset all chevron icons to down position
    const chevrons = document.querySelectorAll('.dropdown-chevron');
    console.log('Found chevrons:', chevrons.length);

    chevrons.forEach((chevron, index) => {
      console.log(`Resetting chevron ${index}:`, chevron);
      chevron.classList.remove('fa-chevron-up');
      chevron.classList.add('fa-chevron-down');
    });

    console.log('closeAllDropdowns completed');
  }

  // Flyout menu management
  let currentFlyoutMenu = null;
  let flyoutHideTimeout = null;

  // Position flyout menu next to dropdown item
  function positionFlyoutMenu(dropdownItem, flyoutMenu) {
    if (!dropdownItem || !flyoutMenu) return;

    const dropdownRect = dropdownItem.getBoundingClientRect();
    const flyoutRect = flyoutMenu.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // Calculate initial position - position flyout next to the dropdown item
    let left = dropdownRect.right + 4; // 4px margin to the right of the item
    let top = dropdownRect.top - 60; // Align with the top of the dropdown item, offset by 60px

    // Adjust if flyout would go off the right edge of viewport
    if (left + flyoutRect.width > viewportWidth) {
      left = dropdownRect.left - flyoutRect.width - 4; // Position to the left instead
    }

    // Adjust if flyout would go off the bottom of viewport
    if (top + flyoutRect.height > viewportHeight) {
      top = viewportHeight - flyoutRect.height - 16; // 16px margin from bottom
    }

    // Ensure flyout doesn't go above viewport
    if (top < 16) {
      top = 16; // 16px margin from top
    }

    flyoutMenu.style.left = left + 'px';
    flyoutMenu.style.top = top + 'px';
  }

  // Show flyout menu
  function showFlyoutMenu(dropdownItem, flyoutMenu) {
    // Clear any pending hide timeout
    if (flyoutHideTimeout) {
      clearTimeout(flyoutHideTimeout);
      flyoutHideTimeout = null;
    }

    // Hide current flyout if different from the one we want to show
    if (currentFlyoutMenu && currentFlyoutMenu !== flyoutMenu) {
      hideFlyoutMenu(currentFlyoutMenu, true); // Immediate hide
    }

    // Show the new flyout
    currentFlyoutMenu = flyoutMenu;
    positionFlyoutMenu(dropdownItem, flyoutMenu);
    flyoutMenu.classList.add('flyout-visible');
  }

  // Hide flyout menu
  function hideFlyoutMenu(flyoutMenu, immediate = false) {
    if (!flyoutMenu) return;

    if (immediate) {
      flyoutMenu.classList.remove('flyout-visible');
      if (currentFlyoutMenu === flyoutMenu) {
        currentFlyoutMenu = null;
      }
    } else {
      // Delay hiding by 300ms
      flyoutHideTimeout = setTimeout(() => {
        flyoutMenu.classList.remove('flyout-visible');
        if (currentFlyoutMenu === flyoutMenu) {
          currentFlyoutMenu = null;
        }
        flyoutHideTimeout = null;
      }, 300);
    }
  }

  // Hide all flyout menus
  function hideAllFlyoutMenus() {
    const flyoutMenus = document.querySelectorAll('.flyout-menu');
    flyoutMenus.forEach(menu => {
      menu.classList.remove('flyout-visible');
    });
    currentFlyoutMenu = null;
    if (flyoutHideTimeout) {
      clearTimeout(flyoutHideTimeout);
      flyoutHideTimeout = null;
    }
  }

  // Add event listeners for flyout menu management
  function attachFlyoutListeners() {
    const dropdownItems = document.querySelectorAll('.dropdown-item');

    dropdownItems.forEach(item => {
      const flyoutMenu = item.querySelector('.flyout-menu');
      if (flyoutMenu) {
        // Remove existing listeners
        item.removeEventListener('mouseenter', item._flyoutEnterHandler);
        item.removeEventListener('mouseleave', item._flyoutLeaveHandler);
        flyoutMenu.removeEventListener('mouseenter', flyoutMenu._flyoutEnterHandler);
        flyoutMenu.removeEventListener('mouseleave', flyoutMenu._flyoutLeaveHandler);

        // Mouse enter dropdown item
        item._flyoutEnterHandler = function() {
          if (sidebar.classList.contains('sidebar-collapsed')) {
            showFlyoutMenu(item, flyoutMenu);
          }
        };

        // Mouse leave dropdown item
        item._flyoutLeaveHandler = function() {
          if (sidebar.classList.contains('sidebar-collapsed')) {
            // Only hide if not moving to flyout menu
            setTimeout(() => {
              if (currentFlyoutMenu === flyoutMenu && !flyoutMenu.matches(':hover')) {
                hideFlyoutMenu(flyoutMenu);
              }
            }, 50); // Small delay to allow mouse to move to flyout
          }
        };

        // Mouse enter flyout menu
        flyoutMenu._flyoutEnterHandler = function() {
          if (sidebar.classList.contains('sidebar-collapsed')) {
            // Clear any pending hide timeout
            if (flyoutHideTimeout) {
              clearTimeout(flyoutHideTimeout);
              flyoutHideTimeout = null;
            }
          }
        };

        // Mouse leave flyout menu
        flyoutMenu._flyoutLeaveHandler = function() {
          if (sidebar.classList.contains('sidebar-collapsed')) {
            hideFlyoutMenu(flyoutMenu);
          }
        };

        // Attach listeners
        item.addEventListener('mouseenter', item._flyoutEnterHandler);
        item.addEventListener('mouseleave', item._flyoutLeaveHandler);
        flyoutMenu.addEventListener('mouseenter', flyoutMenu._flyoutEnterHandler);
        flyoutMenu.addEventListener('mouseleave', flyoutMenu._flyoutLeaveHandler);
      }
    });
  }

  // Initial setup
  document.addEventListener('DOMContentLoaded', function() {
    attachFlyoutListeners();
  });
</script>

<style>
  /* Sidebar width transitions */
  #sidebar {
    transition: width 0.3s ease-in-out;
  }

  /* Mobile full-screen menu styles */
  @media (max-width: 1023px) {
    #sidebar {
      width: 100vw !important;
      height: 100vh !important;
      top: 0 !important;
      z-index: 50;
    }

    /* Ensure mobile menu content is properly spaced */
    #sidebar .sidebar-nav {
      padding-top: 1rem;
    }
  }

  /* Sidebar background transitions */
  #sidebarBackground {
    transition: width 0.3s ease-in-out;
  }

  /* Sidebar navigation scrolling */
  .sidebar-nav {
    /* Enable smooth scrolling */
    scroll-behavior: smooth;
    /* Custom scrollbar styling for webkit browsers */
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--b3)) transparent;
  }

  .sidebar-nav::-webkit-scrollbar {
    width: 6px;
  }

  .sidebar-nav::-webkit-scrollbar-track {
    background: transparent;
  }

  .sidebar-nav::-webkit-scrollbar-thumb {
    background: hsl(var(--b3));
    border-radius: 3px;
  }

  .sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--bc) / 0.3);
  }

  /* Flyout menu scrolling */
  .flyout-menu {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--b3)) transparent;

  }

  .flyout-menu::-webkit-scrollbar {
    width: 6px;
  }

  .flyout-menu::-webkit-scrollbar-track {
    background: transparent;
  }

  .flyout-menu::-webkit-scrollbar-thumb {
    background: hsl(var(--b3));
    border-radius: 3px;
  }

  .flyout-menu::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--bc) / 0.3);
  }

  /* Sidebar collapse styles */
  .sidebar-collapsed .sidebar-nav {
    padding: 1rem 0.5rem;
  }

  .sidebar-collapsed .menu-text {
    display: none !important;
  }

  .sidebar-collapsed .dropdown-chevron {
    display: none !important;
  }

  /* Show flyout chevron only when sidebar is collapsed */
  .sidebar-collapsed .flyout-chevron {
    display: inline-block !important;
  }

  /* Hide flyout chevron when sidebar is expanded */
  .sidebar-container:not(.sidebar-collapsed) .flyout-chevron {
    display: none !important;
  }

  .sidebar-collapsed .menu-item {
    justify-content: center;
    padding: 0.5rem;
    gap: 0 !important;
  }

  .sidebar-collapsed .dropdown-toggle {
    justify-content: center;
    padding: 0.5rem;
    gap: 0 !important;
    position: relative;
  }

  /* Position flyout chevron in collapsed state */
  .sidebar-collapsed .dropdown-toggle .flyout-chevron {
    position: absolute;
    right: 0.25rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.75rem;
    opacity: 0.7;
    transition: opacity 0.2s ease;
  }

  /* Make flyout chevron more visible on hover */
  .sidebar-collapsed .dropdown-toggle:hover .flyout-chevron {
    opacity: 1;
  }

  .sidebar-collapsed .menu-item > div,
  .sidebar-collapsed .dropdown-toggle > div {
    gap: 0 !important;
  }

  /* Logo visibility in sidebar states */
  .sidebar-logo {
    transition: opacity 0.3s ease;
  }

  .sidebar-collapsed .sidebar-logo {
    display: none;
  }

  .sidebar-collapsed .sidebar-logo-collapsed {
    display: block !important;
  }

  .sidebar-logo-collapsed {
    transition: opacity 0.3s ease;
  }

  /* Flyout menu styles */
  .flyout-menu {
    position: fixed;
    left: 100%;
    top: 0;
    min-width: 200px;
    max-width: 300px;
    max-height: 80vh;
    overflow-y: auto;
    z-index: 1000;
    margin-left: 0.25rem;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-20px) scale(0.95);
    transition: all 0.15s ease-in-out;
    /* Always hidden by default - JavaScript will control visibility */
    display: none !important;
  }

  /* Flyout menu visible state - controlled by JavaScript */
  .flyout-menu.flyout-visible {
    display: block !important;
    opacity: 1;
    visibility: visible;
    transform: translateX(0) scale(1);
  }

  /* Ensure flyout menu background is always applied when visible */
  .sidebar-collapsed .flyout-menu,
  .flyout-menu.bg-neutral,
  .dropdown-item .flyout-menu,
  div[id^="flyout-"] {
    background-color: hsl(var(--color-base-100)) !important;
    border: 1px solid hsl(var(--b3)) !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  }

  /* Force background on all child elements too */
  .flyout-menu > div {
    background: transparent !important;
  }

  .flyout-menu .p-2 {
    padding: 0.5rem;
  }

  .flyout-menu h3 {

    font-weight: 600;
    margin-bottom: 0.5rem;
    padding: 0 0.5rem;
    border-bottom: 1px solid hsl(var(--b3));
    padding-bottom: 0.5rem;
  }

  .flyout-menu ul {
    padding: 0;
    margin: 0;
    list-style: none;
  }

  .flyout-menu .menu-item {
    justify-content: flex-start;
    padding: 0.5rem 1rem;
    margin-bottom: 0.25rem;
    border-radius: 0.375rem;
    color: hsl(var(--color-base-content));
    gap: 0.5rem !important; /* Ensure spacing between icon and text */
  }

  .flyout-menu .menu-item:hover {
    background: var(--color-secondary);
    color: var(--color-secondary-content);
  }

  .flyout-menu .menu-text {
    display: inline-block !important; /* Always show text in flyout menus */
  }



  /* Override collapsed state for flyout menu items */
  .sidebar-collapsed .flyout-menu .menu-text {
    display: inline-block !important;
  }

  .sidebar-collapsed .flyout-menu .menu-item {
    justify-content: flex-start !important;
    gap: 0.5rem !important;
  }



  /* Ensure flyout menus are hidden when sidebar is expanded */
  .sidebar-container:not(.sidebar-collapsed) .flyout-menu {
    display: none !important;
  }

  /* Hide normal dropdown content when sidebar is collapsed - but NOT flyout menus */
  .sidebar-collapsed .dropdown-item ul:not(.flyout-menu ul) {
    display: none !important;
  }

  /* Normal dropdown menu styling */
  .sidebar-container ul[id*="dropdown"] {
    background: hsl(var(--n)) !important;
    border: 1px solid hsl(var(--b3));
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  /* Task summary adjustments for collapsed state */
  .sidebar-collapsed .task-summary {
    display: none;
  }

  /* Main content area adjustments */
  @media (min-width: 1024px) {
    #mainContent {
      padding-left: 256px; /* Default expanded width */
      margin-top: 0 !important; /* No margin-top needed - header positioned relative to sidebar */
    }
  }

  @media (max-width: 1023px) {
    #mainContent {
      padding-left: 0 !important; /* No padding on mobile */
      margin-top: 0 !important; /* No margin-top needed - body has padding-top for header */
      padding-bottom: 5rem !important; /* Add bottom padding for mobile dock */
    }
  }
</style>
