@props(['model', 'limit' => 10, 'showHeader' => true])

@php
    $logs = app(\App\Services\ActivityLoggerService::class)->getLogsForModel($model, $limit);
@endphp

<div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
    @if($showHeader)
        <div class="px-4 py-3 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-base font-medium text-gray-900">
                    <i class="fa-sharp fa-history text-gray-500 mr-2"></i>
                    Recent Activity
                </h3>
                @if($logs->count() >= $limit)
                    <a href="{{ route('activity-logs.index', ['model_type' => get_class($model), 'model_id' => $model->getKey()]) }}"
                       class="text-xs text-blue-600 hover:text-blue-800">
                        View All
                    </a>
                @endif
            </div>
        </div>
    @endif

    <div class="p-4">
        @if($logs->count() > 0)
            <div class="space-y-2">
                @foreach($logs as $log)
                    <div class="flex items-start space-x-2">
                        <!-- Event Icon -->
                        <div class="flex-shrink-0">
                            <div class="w-6 h-6 rounded-full flex items-center justify-center
                                @if($log->event_type === 'created') bg-green-100 text-green-600
                                @elseif($log->event_type === 'updated') bg-yellow-100 text-yellow-600
                                @elseif($log->event_type === 'deleted') bg-red-100 text-red-600
                                @elseif($log->event_type === 'viewed') bg-blue-100 text-blue-600
                                @else bg-gray-100 text-gray-600
                                @endif">
                                @if($log->event_type === 'created')
                                    <i class="fa-sharp fa-plus text-xs"></i>
                                @elseif($log->event_type === 'updated')
                                    <i class="fa-sharp fa-edit text-xs"></i>
                                @elseif($log->event_type === 'deleted')
                                    <i class="fa-sharp fa-trash text-xs"></i>
                                @elseif($log->event_type === 'viewed')
                                    <i class="fa-sharp fa-eye text-xs"></i>
                                @else
                                    <i class="fa-sharp fa-cog text-xs"></i>
                                @endif
                            </div>
                        </div>

                        <!-- Event Details -->
                        <div class="flex-1 min-w-0">
                            <div class="text-xs text-gray-900">
                                {{ $log->description }}
                            </div>

                            @if($log->event_type === 'updated' && $log->old_values && $log->new_values)
                                <div class="mt-0.5 text-xs text-gray-500 truncate">
                                    {{ $log->changes_summary }}
                                </div>
                            @endif

                            <div class="mt-0.5 flex items-center space-x-1 text-xs text-gray-400">
                                @if($log->user)
                                    <span>{{ $log->user->name }}</span>
                                    <span>•</span>
                                @endif
                                <span>{{ $log->created_at->diffForHumans() }}</span>
                            </div>
                        </div>

                        <!-- View Details Link -->
                        <div class="flex-shrink-0">
                            <a href="{{ route('activity-logs.show', $log) }}"
                               class="text-gray-300 hover:text-gray-500"
                               title="View Details">
                                <i class="fa-sharp fa-external-link-alt text-xs"></i>
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-4">
                <i class="fa-sharp fa-history text-gray-300 text-lg mb-1"></i>
                <p class="text-gray-500 text-xs">No activity recorded yet.</p>
            </div>
        @endif
    </div>
</div>
