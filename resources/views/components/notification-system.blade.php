{{-- Global Notification System Component --}}
<div id="globalNotificationSystem">
    {{-- Toast Container --}}
    <div id="notificationToastContainer" class="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
        {{-- Toast notifications will be inserted here --}}
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Global notification system
    let lastNotificationCheck = Date.now();
    let isPolling = false;
    
    // Start polling for new notifications
    startNotificationPolling();
    
    function startNotificationPolling() {
        if (isPolling) return;
        isPolling = true;
        
        // Poll every 30 seconds
        setInterval(checkForNewNotifications, 30000);
        
        // Also check when page becomes visible again
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                checkForNewNotifications();
            }
        });
    }
    
    function checkForNewNotifications() {
        fetch('/api/notifications?timestamp=' + lastNotificationCheck, {
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.notifications && data.notifications.length > 0) {
                // Filter notifications created after last check
                const newNotifications = data.notifications.filter(notification => {
                    const createdAt = new Date(notification.created_at_timestamp || 0).getTime();
                    return createdAt > lastNotificationCheck;
                });
                
                // Show toast for new notifications
                newNotifications.forEach(notification => {
                    showNotificationToast(notification);
                });
                
                // Update notification counts in header and mobile dock
                updateGlobalNotificationCounts(data.unread_count);
            }
            
            lastNotificationCheck = Date.now();
        })
        .catch(error => {
            console.error('Error checking for new notifications:', error);
        });
    }
    
    function showNotificationToast(notification) {
        const toastContainer = document.getElementById('notificationToastContainer');
        if (!toastContainer) return;
        
        const toast = document.createElement('div');
        toast.className = `alert ${notification.urgency_class} shadow-lg transform transition-all duration-300 translate-x-full opacity-0`;
        toast.innerHTML = `
            <div class="flex items-start gap-3">
                <i class="fa-sharp ${notification.urgency_icon} text-lg flex-shrink-0 mt-1"></i>
                <div class="flex-1 min-w-0">
                    <h4 class="font-medium text-sm">${notification.title}</h4>
                    <p class="text-xs opacity-80 mt-1">${notification.message}</p>
                    ${notification.link_url ? `<a href="${notification.link_url}" class="btn btn-xs btn-ghost mt-2">${notification.link_text || 'View'}</a>` : ''}
                </div>
                <button class="btn btn-ghost btn-xs btn-circle" onclick="dismissToast(this)">
                    <i class="fa-sharp fa-solid fa-times text-xs"></i>
                </button>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full', 'opacity-0');
        }, 100);
        
        // Auto-remove after 8 seconds
        setTimeout(() => {
            dismissToast(toast.querySelector('button'));
        }, 8000);
        
        // Add click handler for the toast
        toast.addEventListener('click', function(e) {
            if (e.target.closest('button') || e.target.closest('a')) return;
            
            // Mark as read and navigate if there's a link
            if (notification.link_url) {
                markNotificationAsRead(notification.id);
                window.location.href = notification.link_url;
            }
        });
    }
    
    function dismissToast(button) {
        const toast = button.closest('.alert');
        if (toast) {
            toast.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }
    
    function updateGlobalNotificationCounts(count) {
        // Update desktop notification badge
        const desktopBadge = document.getElementById('notificationBadge');
        const desktopCount = document.getElementById('notificationCount');
        if (desktopBadge && desktopCount) {
            if (count > 0) {
                desktopBadge.classList.remove('hidden');
                desktopBadge.classList.add('flex');
                desktopCount.textContent = count > 99 ? '99+' : count;
            } else {
                desktopBadge.classList.add('hidden');
                desktopBadge.classList.remove('flex');
            }
        }
        
        // Update mobile notification badge
        const mobileBadge = document.getElementById('mobileNotificationBadge');
        const mobileCount = document.getElementById('mobileNotificationCount');
        if (mobileBadge && mobileCount) {
            if (count > 0) {
                mobileBadge.classList.remove('hidden');
                mobileBadge.classList.add('flex');
                mobileCount.textContent = count > 99 ? '99+' : count;
            } else {
                mobileBadge.classList.add('hidden');
                mobileBadge.classList.remove('flex');
            }
        }
    }
    
    function markNotificationAsRead(notificationId) {
        fetch(`/api/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);
        });
    }
    
    // Make dismissToast globally available
    window.dismissToast = dismissToast;
});
</script>
@endpush
