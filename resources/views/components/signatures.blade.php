<div class="card bg-base-100 shadow-lg">
    <div class="card-body">
        <div class="flex justify-between items-center mb-4">
            <h3 class="card-title">Signatures</h3>
            @php
                $cocFileId = $certificate->stats['coc_file_id'] ?? null;
                $cocFile = $cocFileId ? $certificate->files->firstWhere('id', $cocFileId) : null;
            @endphp

            @if($cocFile)
                <div class="badge badge-success gap-2">
                    <i class="fa-sharp fa-file-check"></i>
                    Chain of Custody Document Uploaded
                </div>
            @endif
        </div>

        @if($cocFile)
            <div class="alert alert-success mb-4">
                <i class="fa-sharp fa-circle-check mr-2"></i>
                <div>
                    <span class="font-bold">Chain of Custody document uploaded:</span>
                    <span class="ml-2">{{ $cocFile->original_filename }}</span>
                    <div class="mt-1">
                        <a href="{{ route('files.view', $cocFile) }}" class="btn btn-neutral" target="_blank">
                            <i class="fa-sharp fa-eye mr-1"></i> View document
                        </a>
                    </div>
                </div>
            </div>
        @endif

        <!-- Digital Signatures Section -->
        <div id="digital-signatures-section" class="join join-vertical bg-base-100">
            <!-- Client Section -->
            @php
                $clientSignature = $certificate->signatures->where('role', 'client')->first();
                $isClientSigned = $clientSignature !== null;
            @endphp
            <div class="collapse collapse-arrow join-item border border-base-300">
                <input type="radio" name="signature-accordion" id="client-section" checked="checked" />
                <div class="collapse-title text-lg font-medium flex items-center">
                    <span class="mr-2">
                        @if($isClientSigned)
                            <i class="fa-sharp fa-circle-check text-success"></i>
                        @else
                            <i class="fa-sharp fa-circle-xmark text-error"></i>
                        @endif
                    </span>
                    <div class="flex flex-col">
                        <span>Client Representative</span>
                        <span class="text-xs font-normal">
                            @if($isClientSigned)
                                Signed by {{ $clientSignature->signatory_name }} on {{ $clientSignature->signature_date->format('m/d/Y') }}
                            @else
                                Unsigned
                            @endif
                        </span>
                    </div>
                </div>
                <div class="collapse-content">
                    <p><strong>Stats:</strong> <span id="client-stats-indicator"></span></p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="col-span-2">
                            <label class="font-medium mt-4 mb-1">Service Selection:</label>
                            <div class="mb-2">
                                <span class="text-sm">Does the client want certified destruction with device and drive serialization, uncertified destruction, or decline destruction services?</span>
                            </div>
                            <select id="service_selection" class="select select-bordered w-full {{ $isClientSigned ? '!bg-gray-300 !text-black !border-1 !border-gray-700' : '' }}" {{ $isClientSigned ? 'disabled' : '' }} @if(!$isClientSigned) onchange="updateUi()" @endif>
                                <option value="">Select Service Selection</option>
                                <option value="certified_destruction" {{ $certificate->service_selection === 'certified_destruction' ? 'selected' : '' }}>Certified Destruction with Device & Drive Serialization</option>
                                <option value="uncertified_destruction" {{ $certificate->service_selection === 'uncertified_destruction' ? 'selected' : '' }}>Data Device Shredding WITHOUT CERTIFICATION</option>
                                <option value="decline_destruction" {{ $certificate->service_selection === 'decline_destruction' ? 'selected' : '' }}>Data Destruction Services DECLINED</option>
                            </select>
                        </div>

                        <div id="manifest-questions-container" class="col-span-2 space-y-4" style="display: none;">
                            <input type="hidden" id="is_client_signed_hidden" value="{{ $isClientSigned ? 'true' : 'false' }}">

                            <!-- Did client provide manifest question -->
                            <div>
                                <label class="font-medium mb-3 block">Did the client provide a manifest?</label>
                                <div class="space-y-3">
                                    <!-- Yes Option -->
                                    <div class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-success/30 transition-all duration-200 cursor-pointer">
                                        <label class="flex items-start cursor-pointer p-4">
                                            <input type="radio" name="client_provided_manifest" value="yes" class="radio radio-primary mt-1 mr-3 flex-shrink-0"
                                                {{ ($certificate->stats['client_provided_manifest'] ?? '') === 'yes' ? 'checked' : '' }}
                                                onchange="updateClientProvidedManifest('yes')" {{ $isClientSigned ? 'disabled' : '' }}>
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center gap-2 mb-2">
                                                    <i class="fa-sharp fa-check-circle text-success"></i>
                                                    <span class="text-lg font-medium text-base-content">Yes - Manifest Provided</span>
                                                </div>
                                                <p class="text-sm text-base-content/70">Client has provided a list of drives/devices to be destroyed</p>
                                            </div>
                                        </label>
                                    </div>

                                    <!-- No Option -->
                                    <div class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-warning/30 transition-all duration-200 cursor-pointer">
                                        <label class="flex items-start cursor-pointer p-4">
                                            <input type="radio" name="client_provided_manifest" value="no" class="radio radio-primary mt-1 mr-3 flex-shrink-0"
                                                {{ ($certificate->stats['client_provided_manifest'] ?? '') === 'no' ? 'checked' : '' }}
                                                onchange="updateClientProvidedManifest('no')" {{ $isClientSigned ? 'disabled' : '' }}>
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center gap-2 mb-2">
                                                    <i class="fa-sharp fa-times-circle text-warning"></i>
                                                    <span class="text-lg font-medium text-base-content">No - No Manifest Provided</span>
                                                </div>
                                                <p class="text-sm text-base-content/70">Client has not provided a list of drives/devices</p>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="count-fields-container" class="col-span-2 mb-6" style="display: none;">
                            <!-- Total Assets Section -->
                            <div class="card bg-base-200 p-4 mb-4">
                                <h5 class="text-lg font-semibold text-base-content mb-4 flex items-center">
                                    <i class="fa-sharp fa-list-ol text-primary mr-2"></i>
                                    Total Assets Count
                                </h5>
                                <div class="alert alert-info mb-4">
                                    <i class="fa-sharp fa-info-circle mr-2"></i>
                                    <div>
                                        <p class="font-medium">Asset Counting Rules:</p>
                                        <ul class="text-sm mt-1 list-disc list-inside space-y-1">
                                            <li>Each loose drive = 1 billable unit</li>
                                            <li>Each device (computer, server, etc.) with drives = 1 unit per drive inside</li>
                                            <li>Each device without drives = 1 billable unit</li>
                                            <li>All assets must be serialized and countable at time of transfer</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium text-base-content">Client Manifest Total Assets</span>
                                        </label>
                                        <p class="text-sm mb-2 text-base-content/70">Total billable units on client manifest (drives + devices).</p>
                                        <input type="number" id="client_manifest_total_count"
                                            class="input input-bordered w-full bg-base-100 {{ $isClientSigned ? '!bg-gray-300 !text-black !border-1 !border-gray-700' : '' }}"
                                            value="{{ $certificate->client_manifest_drive_count ?? '' }}"
                                            {{ $isClientSigned ? 'readonly' : '' }}
                                            @if(!$isClientSigned) oninput="debounceSaveStats('client')" @endif
                                            placeholder="Enter Total Assets on Manifest" min="0">
                                    </div>

                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium text-base-content">E-Tech Verified Total Assets</span>
                                        </label>
                                        <p class="text-sm mb-2 text-base-content/70">Total billable units verified by E-Tech on site.</p>
                                        <input type="number" id="etech_verified_total_count"
                                            class="input input-bordered w-full bg-base-100 {{ $isClientSigned ? '!bg-gray-300 !text-black !border-1 !border-gray-700' : '' }}"
                                            value="{{ $certificate->etech_verified_drive_count ?? '' }}"
                                            {{ $isClientSigned ? 'readonly' : '' }}
                                            @if(!$isClientSigned) oninput="debounceSaveStats('client')" @endif
                                            placeholder="Enter E-Tech Verified Total Assets" min="0">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-4">
                        <i class="fa-sharp fa-circle-info mr-2"></i>
                        <div>
                            <p class="font-medium">Important Information:</p>
                            <p class="text-sm mt-1">The client understands that any discrepancies between their manifest and our final count upon certification will be noted. The final certification count conducted by the certifying technician is the official and final count.</p>
                        </div>
                    </div>

                    @if ($isClientSigned)
                        <button class="btn btn-secondary mt-4" onclick="viewSignature('{{ $clientSignature->getSignatureImageUrl() }}', `{{ $clientSignature->contract_text }}`, '{{ $clientSignature->id }}')">View Signature</button>
                    @else
                        <button class="btn btn-primary mt-4" onclick="openSignatureModal('client')" id="client-sign-button">Sign</button>
                    @endif
                </div>
            </div>

            <!-- Driver Section -->
            @php
                $driverSignature = $certificate->signatures->where('role', 'driver')->first();
                $isDriverSigned = $driverSignature !== null;
                $isDropOff = ($certificate->stats['driver_pickup_dropoff'] ?? '') === 'No';
            @endphp
            <div class="collapse collapse-arrow join-item border border-base-300"
                 data-driver-signed="{{ $isDriverSigned ? 'true' : 'false' }}"
                 data-is-dropoff="{{ $isDropOff ? 'true' : 'false' }}">
                <input type="radio" name="signature-accordion" id="driver-section-checkbox" />
                <div class="collapse-title text-lg font-medium flex items-center">
                    <span class="mr-2" id="driver-status-icon">
                        @if($isDriverSigned)
                            <i class="fa-sharp fa-circle-check text-success"></i>
                        @elseif($isDropOff)
                            <i class="fa-sharp fa-circle-check text-success"></i>
                        @else
                            <i class="fa-sharp fa-circle-xmark text-error"></i>
                        @endif
                    </span>
                    <div class="flex flex-col">
                        <span>Driver</span>
                        <span class="text-xs font-normal" id="driver-status-text">
                            @if($isDriverSigned)
                                Signed by {{ $driverSignature->signatory_name }} on {{ $driverSignature->signature_date->format('m/d/Y') }}
                            @elseif($isDropOff)
                                Dropped Off - No Signature Required
                            @else
                                Unsigned
                            @endif
                        </span>
                    </div>
                </div>
                <div class="collapse-content">
                    <p><strong>Stats:</strong> <span id="driver-stats-indicator"></span></p>


                            <label>Were these picked up?
                                <br>
                                <span class="text-sm">- Were these picked up from the client? If not, they were dropped off at the warehouse.</span>
                               <br>

                            </label>
                            <select id="driver_pickup_dropoff" class="select select-bordered w-full {{ $isDriverSigned ? '!bg-gray-300 !text-black !border-1 !border-gray-700' : '' }}" {{ $isDriverSigned ? 'disabled' : '' }} @if(!$isDriverSigned) onchange="debounceSaveStats('driver'); updateDriverSignButtonVisibility();" @endif>
                                <option value="">Select Were These Picked Up</option>
                                <option value="Yes" {{ ($certificate->stats['driver_pickup_dropoff'] ?? '') === 'Yes' ? 'selected' : '' }}>Yes - Picked Up</option>
                                <option value="No" {{ ($certificate->stats['driver_pickup_dropoff'] ?? '') === 'No' ? 'selected' : '' }}>No - Dropped Off</option>
                            </select>


                    @if ($isDriverSigned)
                        <button class="btn btn-secondary mt-4" onclick="viewSignature('{{ $driverSignature->getSignatureImageUrl() }}', `{{ $driverSignature->contract_text }}`, '{{ $driverSignature->id }}')">View Signature</button>
                    @else
                        <button class="btn btn-primary mt-4" onclick="openSignatureModal('driver')" id="driver-sign-button">Sign</button>
                    @endif
                </div>
            </div>

            <!-- Receiving Tech Section -->
            @php
                $receivingTechSignature = $certificate->signatures->where('role', 'warehouse')->first();
                $isReceivingTechSigned = $receivingTechSignature !== null;
            @endphp
            <div class="collapse collapse-arrow join-item border border-base-300">
                <input type="radio" name="signature-accordion" id="receiving-tech-section" />
                <div class="collapse-title text-lg font-medium flex items-center">
                    <span class="mr-2">
                        @if($isReceivingTechSigned)
                            <i class="fa-sharp fa-circle-check text-success"></i>
                        @else
                            <i class="fa-sharp fa-circle-xmark text-error"></i>
                        @endif
                    </span>
                    <div class="flex flex-col">
                        <span>Receiving Technician</span>
                        <span class="text-xs font-normal">
                            @if($isReceivingTechSigned)
                                Signed by {{ $receivingTechSignature->signatory_name }} on {{ $receivingTechSignature->signature_date->format('m/d/Y') }}
                            @else
                                Unsigned
                            @endif
                        </span>
                    </div>
                </div>
                <div class="collapse-content">


                            <p class="text-sm mb-4">
                                By signing below, you confirm that you have received the devices either from the driver or directly from the client. If a manifest was provided, it has been safely stored with the drives.
                            </p>


                    @if ($isReceivingTechSigned)
                        <button class="btn btn-secondary mt-4" onclick="viewSignature('{{ $receivingTechSignature->getSignatureImageUrl() }}', `{{ $receivingTechSignature->contract_text }}`, '{{ $receivingTechSignature->id }}')">View Signature</button>
                    @else
                        <button class="btn btn-primary mt-4" onclick="openSignatureModal('warehouse')" id="warehouse-sign-button">Sign</button>
                    @endif
                </div>
            </div>

            <!-- Certifying Tech Section -->
            @php
                $certifyingTechSignature = $certificate->signatures->where('role', 'technician')->first();
                $isCertifyingTechSigned = $certifyingTechSignature !== null;
            @endphp
            <div class="collapse collapse-arrow join-item border border-base-300">
                <input type="radio" name="signature-accordion" id="certifying-tech-section" />
                <div class="collapse-title text-lg font-medium flex items-center">
                    <span class="mr-2">
                        @if($isCertifyingTechSigned)
                            <i class="fa-sharp fa-circle-check text-success"></i>
                        @else
                            <i class="fa-sharp fa-circle-xmark text-error"></i>
                        @endif
                    </span>
                    <div class="flex flex-col">
                        <span>Certifying Technician</span>
                        <span class="text-xs font-normal">
                            @if($isCertifyingTechSigned)
                                Signed by {{ $certifyingTechSignature->signatory_name }} on {{ $certifyingTechSignature->signature_date->format('m/d/Y') }}
                            @else
                                Unsigned
                            @endif
                        </span>
                    </div>
                </div>
                <div class="collapse-content">
                    <p><strong>Stats:</strong> <span id="technician-stats-indicator"></span></p>


                            <label>Drives Removed
                                <br>
                                <span class="text-sm">
                                - I certify that all drives were physically removed from the devices provided by the clients
                                </span>
                                <br>


                            </label>
                            <select id="technician_drives_removed" class="select select-bordered w-full {{ $isCertifyingTechSigned ? '!bg-gray-300 !text-black !border-1 !border-gray-700' : '' }}" {{ $isCertifyingTechSigned ? 'disabled' : '' }} @if(!$isCertifyingTechSigned) onchange="debounceSaveStats('technician')" @endif>
                                <option value="">Select Drives Removed</option>
                                <option value="Yes" {{ ($certificate->stats['technician_drives_removed'] ?? '') === 'Yes' ? 'selected' : '' }}>Yes</option>
                                <option value="No" {{ ($certificate->stats['technician_drives_removed'] ?? '') === 'No' ? 'selected' : '' }}>No</option>
                            </select>


                            <label>Total Drives Found
                                <br>
                                <span class="text-sm">
                                - Enter the total number of drives found to be shredded. This will serve as the official and final count.
                                </span>
                                <br>
                            </label>
                            <input type="number" id="technician_total_drives_found" class="input input-bordered w-full {{ $isCertifyingTechSigned ? '!bg-gray-300 !text-black !border-1 !border-gray-700' : '' }}" value="{{ $certificate->stats['technician_total_drives_found'] ?? '' }}" {{ $isCertifyingTechSigned ? 'readonly' : '' }} @if(!$isCertifyingTechSigned) oninput="debounceSaveStats('technician')" @endif placeholder="Enter Total Drives Found">


                            <label>Drives Serialized
                                <br>
                                <span class="text-sm">
                                - I certify that all drives presented by the client have been recorded and are accounted for.
                                </span>
                                <br>
                            </label>
                            <select id="technician_drives_serialized" class="select select-bordered w-full {{ $isCertifyingTechSigned ? '!bg-gray-300 !text-black !border-1 !border-gray-700' : '' }}" {{ $isCertifyingTechSigned ? 'disabled' : '' }} @if(!$isCertifyingTechSigned) onchange="debounceSaveStats('technician')" @endif>
                                <option value="">Select Drives Serialized</option>
                                <option value="Yes" {{ ($certificate->stats['technician_drives_serialized'] ?? '') === 'Yes' ? 'selected' : '' }}>Yes, Serialized and Destroyed</option>
                                <option value="NoDrivesFound" {{ ($certificate->stats['technician_drives_serialized'] ?? '') === 'NoDrivesFound' ? 'selected' : '' }}>No Drives Found</option>
                                <option value="AllDrivesDestroyed" {{ ($certificate->stats['technician_drives_serialized'] ?? '') === 'AllDrivesDestroyed' ? 'selected' : '' }}>All Drives Destroyed with No Serialization Required</option>
                            </select>


                    @if ($isCertifyingTechSigned)
                        <button class="btn btn-secondary mt-4" onclick="viewSignature('{{ $certifyingTechSignature->getSignatureImageUrl() }}', `{{ $certifyingTechSignature->contract_text }}`, '{{ $certifyingTechSignature->id }}')">View Signature</button>
                    @else
                        <button class="btn btn-primary mt-4" onclick="openSignatureModal('technician')" id="technician-sign-button">Sign</button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.1.7/dist/signature_pad.umd.min.js"></script>

<script>
    // Make signaturePad a global variable
    if (typeof signaturePad === 'undefined') {
        var signaturePad;
    }
    // Use window object to avoid duplicate declarations
    if (typeof window.currentRole === 'undefined') {
        window.currentRole = null; // Store the current role
    }
    // Global variable to store the current signature id for deletion
    if (typeof window.currentSignatureId === 'undefined') {
        window.currentSignatureId = null;
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Add event listener for signatory name input
        const nameInput = document.getElementById('modal_signatory_name');
        const roleSelect = document.getElementById('modal_signatory_role');
        if (nameInput && roleSelect) {
            nameInput.addEventListener('input', function() {
                const name = nameInput.value.trim();
                if (name === '') {
                    roleSelect.disabled = true;
                    document.getElementById('contract_content').innerHTML = "";
                } else {
                    roleSelect.disabled = false;
                    // Update agreement text if a role has already been selected.
                    if (roleSelect.value.trim() !== '') {
                        loadContract();
                    }
                }
            });
        }

        // Signature Pad
        const canvas = document.getElementById('signature-pad');
        signaturePad = new SignaturePad(canvas);

        document.getElementById('clear-signature').addEventListener('click', function() {
            signaturePad.clear();
        });

        // Initialize driver pickup/dropoff change event
        const pickupDropoffElement = document.getElementById('driver_pickup_dropoff');
        if (pickupDropoffElement) {
            pickupDropoffElement.addEventListener('change', function() {
                updateDriverSignButtonVisibility();
            });
        }

        // Disable sign buttons initially
        disableSignButtons();
        updateDriverSignButtonVisibility();

        // Re-validate stats and enable sign buttons on page load
        ['client', 'driver', 'warehouse', 'technician'].forEach(role => {
            saveStats(role); // Trigger stats validation for each role
        });

        // Check if there's a Chain of Custody document
        @php
            $cocFileId = $certificate->stats['coc_file_id'] ?? null;
            $hasCocDocument = $cocFileId && $certificate->files->firstWhere('id', $cocFileId);
        @endphp
        console.log('Has Chain of Custody document:', {{ $hasCocDocument ? 'true' : 'false' }});

        // Toggle count fields on page load
        updateUi();
    });

    function addSignature() {
        var signatoryNameElement = document.getElementById('modal_signatory_name');
        var signatoryRoleElement = document.getElementById('modal_signatory_role');

        if (!signatoryNameElement) {
            alert('Signatory name element not found.');
            return;
        }

        var signatoryName = signatoryNameElement.value.trim();
        var signatoryRole = window.currentRole;
        const canvas = document.getElementById('signature-pad');

        // Check if the name field is empty
        if (signatoryName === '') {
            alert('Please enter a signatory name.');
            signatoryNameElement.focus();
            return;
        }

        var signatureData = null;
        if (!signaturePad.isEmpty()) {
            signatureData = signaturePad.toDataURL();
        } else {
            alert('Please provide a signature.');
            return;
        }

        // Get the contract text
        var contractText = document.getElementById('contract_content').innerHTML;

        if (signatoryName !== '' && signatoryRole !== '' && signatureData) {
            fetch('{{ route('signatures.store') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        certificate_id: {{ $certificate->id }},
                        signatory_name: signatoryName,
                        role: signatoryRole,
                        signature_data: signatureData,
                        contract_text: contractText // Include the contract text
                        // Note: IP address is captured server-side in the SignatureController
                    })
                })
                .then(response => {
                    // Check if the response is JSON
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        return response.json().then(data => {
                            return { status: response.status, data };
                        });
                    } else {
                        // If not JSON, get the text and create an error object
                        return response.text().then(text => {
                            console.error('Non-JSON response:', text);
                            return {
                                status: response.status,
                                data: {
                                    success: false,
                                    message: 'Server returned non-JSON response. Check server logs.'
                                }
                            };
                        });
                    }
                })
                .then(({ status, data }) => {
                    if (data.success) {
                        // If this is a driver signature, update the data attribute before reloading
                        if (signatoryRole === 'driver') {
                            const driverSection = document.querySelector('.collapse-arrow[data-driver-signed]');
                            if (driverSection) {
                                driverSection.setAttribute('data-driver-signed', 'true');
                                // Update the icon immediately
                                updateDriverStatusIcon(true, document.getElementById('driver_pickup_dropoff').value === 'No');
                            }
                        }
                        location.reload(); // Reload the page after adding a signature
                    } else {
                        // Check if we should run diagnostics
                        if (status === 500) {
                            console.error('Server error occurred. Running diagnostics...');
                            runSignatureDiagnostics();
                        }
                        alert('Error: ' + (data.message || 'Unknown error occurred'));
                    }
                })
                .catch(error => {
                    console.error('Error submitting signature:', error);
                    alert('Error submitting signature: ' + error.message);
                    runSignatureDiagnostics();
                });
        } else {
            alert('Please fill in all the required fields and provide a signature.');
        }
    }

    // Function to toggle the Add Signature button based on name input
    function toggleAddSignatureButton() {
        const nameInput = document.getElementById('modal_signatory_name');
        const addSignatureBtn = document.querySelector('.modal-action .btn-primary');

        if (nameInput && addSignatureBtn) {
            const nameValue = nameInput.value.trim();
            addSignatureBtn.disabled = nameValue === '';
        }
    }

    function deleteSignature(signatureId) {
        if (!signatureId) {
            console.error('No signature ID provided for deletion.');
            return;
        }

        fetch(`/signatures/${signatureId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log(`Signature with ID ${signatureId} deleted successfully.`);
                    location.reload(); // Reload the page after deleting a signature
                } else {
                    console.error('Error deleting signature:', data.message || 'Unknown error');
                    alert('Error deleting signature.');
                }
            })
            .catch(error => {
                console.error('Error deleting signature:', error);
                alert('An error occurred while deleting the signature.');
            });
    }

    function confirmDeleteSignature() {
        if (!currentSignatureId) {
            console.warn('No signature selected for deletion.');
            alert('No signature selected for deletion.');
            return;
        }
        if (confirm('Are you sure you want to delete this signature?')) {
            console.log(`Deleting signature with ID: ${currentSignatureId}`);
            deleteSignature(currentSignatureId); // Call the delete function
        }
    }

    function openSignatureModal(role) {
        window.currentRole = role; // Set the current role
        const nameInput = document.getElementById('modal_signatory_name');
        const signatoryNameLabel = document.getElementById('signatory_name_label');
        const addSignatureBtn = document.querySelector('.modal-action .btn-primary');

        // Clear the name input field
        if (nameInput) {
            nameInput.value = '';
        }

        // Disable the Add Signature button initially
        if (addSignatureBtn) {
            addSignatureBtn.disabled = true;
        }

        // Update placeholder and label text based on role
        if (nameInput) {
            switch (role) {
                case 'client':
                    nameInput.placeholder = 'Client Name';
                    signatoryNameLabel.textContent = 'Client Name';
                    break;
                case 'driver':
                    nameInput.placeholder = 'Driver Name';
                    signatoryNameLabel.textContent = 'Driver Name';
                    break;
                case 'warehouse':
                    nameInput.placeholder = 'Receiver Name';
                    signatoryNameLabel.textContent = 'Receiver Name';
                    break;
                case 'technician':
                    nameInput.placeholder = 'Certifying Tech Name';
                    signatoryNameLabel.textContent = 'Certifying Tech Name';
                    break;
                default:
                    nameInput.placeholder = 'Signatory Name';
                    signatoryNameLabel.textContent = 'Signatory Name';
                    break;
            }

            // Add input event listener to toggle the Add Signature button
            nameInput.addEventListener('input', function() {
                toggleAddSignatureButton();
            });
        }

        const serviceSelectionEl = document.getElementById('service_selection');
        const serviceSelection = serviceSelectionEl.value;
        const serviceSelectionText = serviceSelectionEl.options[serviceSelectionEl.selectedIndex].text;

        const clientManifestTotalCount = document.getElementById('client_manifest_total_count')?.value || '0';
        const etechVerifiedTotalCount = document.getElementById('etech_verified_total_count')?.value || '0';

        const currentDate = new Date().toLocaleDateString();

        // Fetch contract content and replace placeholders dynamically
        fetch(`{{ route('certificates.contract') }}?role=${role}&service_selection=${serviceSelection}`)
            .then(response => response.json())
            .then(data => {
                const contractContent = document.getElementById('contract_content');
                const signatoryName = nameInput.value.trim() || '[NAME]'; // Use placeholder if name is empty

                // Build key points HTML if available
                if (data.key_points && data.key_points.length > 0) {
                    const keyPointsList = document.getElementById('modal_key_points_list');
                    const keyPointsDisplay = document.getElementById('modal_key_points_display');

                    if (keyPointsList && keyPointsDisplay) {
                        keyPointsList.innerHTML = '';
                        data.key_points.forEach(point => {
                            const li = document.createElement('li');
                            li.innerHTML = point
                                .replace(/\[SIGNATORY_NAME\]/g, signatoryName)
                                .replace(/\[NAME\]/g, signatoryName)
                                .replace(/\[SERVICE_SELECTION\]/g, serviceSelectionText)
                                .replace(/\[CLIENT_MANIFEST_TOTAL_COUNT\]/g, clientManifestTotalCount)
                                .replace(/\[ETECH_VERIFIED_TOTAL_COUNT\]/g, etechVerifiedTotalCount)
                                .replace(/\[CURRENT_DATE\]/g, currentDate)
                                .replace(/\[TECHNICIAN_TOTAL_DRIVES_FOUND\]/g, document.getElementById('technician_total_drives_found')?.value || 'N/A');
                            keyPointsList.appendChild(li);
                        });
                        keyPointsDisplay.style.display = 'block';
                    }
                } else {
                    const keyPointsDisplay = document.getElementById('modal_key_points_display');
                    if (keyPointsDisplay) {
                        keyPointsDisplay.style.display = 'none';
                    }
                }

                // Check if we have section information (new format)
                let contractHTML = '';

                if (data.section) {
                    contractHTML = `<h3>${data.section}</h3><h4>${data.title}</h4>`;
                } else {
                    contractHTML = `<h3>${data.title}</h3>`;
                }

                // Check if we have the text field (new format) or paragraphs (old format)
                if (data.text) {
                    // New format with single text field
                    contractHTML += `<div>${data.text
                        .replace(/\[SIGNATORY_NAME\]/g, signatoryName)
                        .replace(/\[NAME\]/g, signatoryName)
                        .replace(/\[SERVICE_SELECTION\]/g, serviceSelectionText)
                        .replace(/\[CLIENT_MANIFEST_TOTAL_COUNT\]/g, clientManifestTotalCount)
                        .replace(/\[ETECH_VERIFIED_TOTAL_COUNT\]/g, etechVerifiedTotalCount)
                        .replace(/\[CURRENT_DATE\]/g, currentDate)
                        .replace(/\[TECHNICIAN_TOTAL_DRIVES_FOUND\]/g, document.getElementById('technician_total_drives_found')?.value || 'N/A')}</div>`;
                } else {
                    // Old format with paragraphs array
                    data.paragraphs.forEach(paragraph => {
                        contractHTML += `<p>${paragraph
                            .replace(/\[NAME\]/g, signatoryName)
                            .replace(/\[SIGNATORY_NAME\]/g, signatoryName)
                            .replace(/\[SERVICE_SELECTION\]/g, serviceSelectionText)
                            .replace(/\[CLIENT_MANIFEST_TOTAL_COUNT\]/g, clientManifestTotalCount)
                            .replace(/\[ETECH_VERIFIED_TOTAL_COUNT\]/g, etechVerifiedTotalCount)
                            .replace(/\[CURRENT_DATE\]/g, currentDate)
                            .replace(/\[TECHNICIAN_TOTAL_DRIVES_FOUND\]/g, document.getElementById('technician_total_drives_found')?.value || 'N/A')}</p>`;
                    });
                }

                contractContent.innerHTML = contractHTML;

                // Update contract text dynamically as the name is typed
                nameInput.addEventListener('input', function () {
                    const updatedName = nameInput.value.trim() || '[NAME]';

                    // Update key points if available
                    if (data.key_points && data.key_points.length > 0) {
                        const keyPointsList = document.getElementById('modal_key_points_list');
                        if (keyPointsList) {
                            keyPointsList.innerHTML = '';
                            data.key_points.forEach(point => {
                                const li = document.createElement('li');
                                li.innerHTML = point
                                    .replace(/\[SIGNATORY_NAME\]/g, updatedName)
                                    .replace(/\[NAME\]/g, updatedName)
                                    .replace(/\[SERVICE_SELECTION\]/g, serviceSelectionText)
                                    .replace(/\[CLIENT_MANIFEST_TOTAL_COUNT\]/g, clientManifestTotalCount)
                                    .replace(/\[ETECH_VERIFIED_TOTAL_COUNT\]/g, etechVerifiedTotalCount)
                                    .replace(/\[CURRENT_DATE\]/g, currentDate)
                                    .replace(/\[TECHNICIAN_TOTAL_DRIVES_FOUND\]/g, document.getElementById('technician_total_drives_found')?.value || 'N/A');
                                keyPointsList.appendChild(li);
                            });
                        }
                    }

                    let updatedHTML = '';

                    if (data.section) {
                        updatedHTML = `<h3>${data.section}</h3><h4>${data.title}</h4>`;
                    } else {
                        updatedHTML = `<h3>${data.title}</h3>`;
                    }

                    if (data.text) {
                        // New format with single text field
                        updatedHTML += `<div>${data.text
                            .replace(/\[SIGNATORY_NAME\]/g, updatedName)
                            .replace(/\[NAME\]/g, updatedName)
                            .replace(/\[SERVICE_SELECTION\]/g, serviceSelectionText)
                            .replace(/\[CLIENT_MANIFEST_TOTAL_COUNT\]/g, clientManifestTotalCount)
                            .replace(/\[ETECH_VERIFIED_TOTAL_COUNT\]/g, etechVerifiedTotalCount)
                            .replace(/\[CURRENT_DATE\]/g, currentDate)
                            .replace(/\[TECHNICIAN_TOTAL_DRIVES_FOUND\]/g, document.getElementById('technician_total_drives_found')?.value || 'N/A')}</div>`;
                    } else {
                        // Old format with paragraphs array
                        data.paragraphs.forEach(paragraph => {
                            updatedHTML += `<p>${paragraph
                                .replace(/\[NAME\]/g, updatedName)
                                .replace(/\[SIGNATORY_NAME\]/g, updatedName)
                                .replace(/\[SERVICE_SELECTION\]/g, serviceSelectionText)
                                .replace(/\[CLIENT_MANIFEST_TOTAL_COUNT\]/g, clientManifestTotalCount)
                                .replace(/\[ETECH_VERIFIED_TOTAL_COUNT\]/g, etechVerifiedTotalCount)
                                .replace(/\[CURRENT_DATE\]/g, currentDate)
                                .replace(/\[TECHNICIAN_TOTAL_DRIVES_FOUND\]/g, document.getElementById('technician_total_drives_found')?.value || 'N/A')}</p>`;
                        });
                    }

                    contractContent.innerHTML = updatedHTML;
                });
            })
            .catch(error => {
                console.error("Error loading contract:", error);
                document.getElementById('contract_content').innerHTML = "<p>Error loading contract.</p>";
            });

        document.getElementById('signatureModal').classList.add('modal-open');

        // Focus on the signatory name input field after the modal is opened
        setTimeout(() => {
            if (nameInput) {
                nameInput.focus();
                // Initialize the button state
                toggleAddSignatureButton();
            }
        }, 100); // Small delay to ensure the modal is fully opened
    }

    function closeSignatureModal() {
        document.getElementById('signatureModal').classList.remove('modal-open');
        // Re-enable sign buttons
        enableSignButtons();
    }

    function loadContract() {
        var signatoryRole = document.getElementById('modal_signatory_role').value.trim();
        if (signatoryRole !== '') {
            // Updated fetch URL to call the certificate controller endpoint
            fetch(`{{ route('certificates.contract') }}?role=${signatoryRole}`)
                .then(response => response.json())
                .then(data => {
                    let signatoryName = document.getElementById('modal_signatory_name').value.trim();

                    // Handle key points if available
                    if (data.key_points && data.key_points.length > 0) {
                        const keyPointsList = document.getElementById('modal_key_points_list');
                        const keyPointsDisplay = document.getElementById('modal_key_points_display');

                        if (keyPointsList && keyPointsDisplay) {
                            keyPointsList.innerHTML = '';
                            data.key_points.forEach(point => {
                                const li = document.createElement('li');
                                li.innerHTML = point.replace(/\[NAME\]/g, signatoryName);
                                keyPointsList.appendChild(li);
                            });
                            keyPointsDisplay.style.display = 'block';
                        }
                    } else {
                        const keyPointsDisplay = document.getElementById('modal_key_points_display');
                        if (keyPointsDisplay) {
                            keyPointsDisplay.style.display = 'none';
                        }
                    }

                    let contractHTML = `<h3>${data.title}</h3>`;
                    if (data.paragraphs) {
                        data.paragraphs.forEach(paragraph => {
                            contractHTML += `<p>${paragraph.replace('[NAME]', signatoryName)}</p>`;
                        });
                    }
                    document.getElementById('contract_content').innerHTML = contractHTML;
                })
                .catch(error => {
                    console.error("Error loading contract:", error);
                    document.getElementById('contract_content').innerHTML = "<p>Error loading contract.</p>";
                });
        } else {
            document.getElementById('contract_content').innerHTML = "";
            const keyPointsDisplay = document.getElementById('modal_key_points_display');
            if (keyPointsDisplay) {
                keyPointsDisplay.style.display = 'none';
            }
        }
    }

    function viewSignature(signatureImage, contractText, signatureId) {
        console.log(`Opening signature with ID: ${signatureId}`); // Log the signature ID
        window.currentSignatureId = signatureId; // Set the current signature ID
        const signatureImageElement = document.getElementById('signatureImage');
        const contractTextElement = document.getElementById('contractText');

        if (signatureImage) {
            signatureImageElement.src = signatureImage;
            signatureImageElement.style.display = 'block';
        } else {
            signatureImageElement.style.display = 'none';
        }

        contractTextElement.innerHTML = contractText;

        document.getElementById('signatureViewModal').classList.add('modal-open');
    }

    function closeSignatureViewModal() {
        const signatureImageElement = document.getElementById('signatureImage');
        const contractTextElement = document.getElementById('contractText');

        signatureImageElement.src = "";
        signatureImageElement.style.display = 'none';
        contractTextElement.innerHTML = "";

        document.getElementById('signatureViewModal').classList.remove('modal-open');
    }

    // Debounce function
    function debounce(func, delay) {
        let timeout;
        return function(...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        }
    }

    // Save stats function
    async function saveStats(role) {
        const stats = {};
        const indicator = document.getElementById(`${role}-stats-indicator`);
        // If indicator doesn't exist, create a temporary object with a no-op textContent setter
        const safeIndicator = indicator || { textContent: () => {} };
        let isValid = true;

        if (role === 'client') {
            stats.service_selection = document.getElementById('service_selection').value;
            stats.client_manifest_drive_count = document.getElementById('client_manifest_total_count')?.value || 0;
            stats.etech_verified_drive_count = document.getElementById('etech_verified_total_count')?.value || 0;

            // Add manifest questions to stats, but only if the manifest questions container is visible
            // This prevents saving empty values if the fields are hidden
            if (document.getElementById('manifest-questions-container').style.display !== 'none') {
                const clientProvidedManifestRadio = document.querySelector('input[name="client_provided_manifest"]:checked');
                stats.client_provided_manifest = clientProvidedManifestRadio ? clientProvidedManifestRadio.value : '';

                // Set manifest includes based on service selection and manifest provided
                if (stats.client_provided_manifest === 'yes') {
                    stats.manifest_includes_drives = 'yes'; // Always yes if manifest provided for certified services
                    // Only include devices for origin serialization
                    stats.manifest_includes_devices = stats.service_selection === 'certified_drive_origin_serialization' ? 'yes' : 'no';
                } else {
                    stats.manifest_includes_drives = 'no';
                    stats.manifest_includes_devices = 'no';
                }
            } else {
                // If hidden, ensure these stats are cleared or set to a default if they were previously set
                stats.client_provided_manifest = '';
                stats.manifest_includes_drives = '';
                stats.manifest_includes_devices = '';
            }

            if (!stats.service_selection) {
                isValid = false;
            }
        } else if (role === 'driver') {
            stats.driver_pickup_dropoff = document.getElementById('driver_pickup_dropoff').value;
            if (!stats.driver_pickup_dropoff) {
                isValid = false;
            }
        } else if (role === 'warehouse') {
            // No stats to save for warehouse
            isValid = true;
        } else if (role === 'technician') {
            stats.technician_drives_removed = document.getElementById('technician_drives_removed').value;
            stats.technician_total_drives_found = document.getElementById('technician_total_drives_found').value;
            stats.technician_drives_serialized = document.getElementById('technician_drives_serialized').value;

            if (!stats.technician_drives_removed || !stats.technician_total_drives_found || !stats.technician_drives_serialized) {
                isValid = false;
            }
        }

        if (indicator) indicator.textContent = 'Saving...';

        try {
            const response = await fetch('{{ route('certificates.saveStats', $certificate) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    stats: stats
                })
            });

            const data = await response.json();
            if (response.ok) {
                if (indicator) {
                    indicator.textContent = 'Saved!';
                    setTimeout(() => indicator.textContent = '', 2000); // Clear after 2 seconds
                }
            } else {
                if (indicator) indicator.textContent = 'Save Failed!';
                console.error('Error saving stats:', data.message || 'Unknown error');
            }

            // Enable/disable sign button based on validation
            const signButtonId = `${role}-sign-button`;
            const signButton = document.getElementById(signButtonId);
            if (signButton) {
                // Always enable the warehouse sign button, validate others
                if (role === 'warehouse') {
                    signButton.disabled = false;
                } else {
                    signButton.disabled = !isValid;
                }
            }

        } catch (error) {
            if (indicator) indicator.textContent = 'Save Error!';
            console.error('Error saving stats:', error);
        }
    }

    const debounceSaveStats = (role) => debounce(() => saveStats(role), 500)();

    function updateDriverSignButtonVisibility() {
        const pickupDropoffElement = document.getElementById('driver_pickup_dropoff');
        if (!pickupDropoffElement) {
            console.warn('Element with ID "driver_pickup_dropoff" not found.');
            return;
        }

        const isDropOff = pickupDropoffElement.value === 'No';
        const driverSection = document.querySelector('.collapse-arrow[data-driver-signed]');
        let isDriverSigned = false;

        if (driverSection) {
            // Update the data attribute to reflect the current dropoff status
            driverSection.setAttribute('data-is-dropoff', isDropOff ? 'true' : 'false');
            // Get the signed status from the data attribute
            isDriverSigned = driverSection.getAttribute('data-driver-signed') === 'true';
        }

        const signButton = document.getElementById('driver-sign-button');

        if (signButton && !isDriverSigned) {
            signButton.style.display = isDropOff ? 'none' : 'inline-block';
        } else if (signButton && isDriverSigned) {
            signButton.style.display = 'none';
        } else if (!signButton && !isDriverSigned) {
            console.warn('Element with ID "driver-sign-button" not found.');
        }

        // Update the driver status icon
        updateDriverStatusIcon(isDriverSigned, isDropOff);
    }

    function updateDriverStatusIcon(isDriverSigned, isDropOff) {
        const driverStatusIcon = document.querySelector('#driver-status-icon i');
        const driverStatusText = document.getElementById('driver-status-text');

        if (!driverStatusIcon) {
            console.warn('Driver status icon not found.');
            return;
        }

        if (isDriverSigned) {
            // Show green check icon - signature exists
            driverStatusIcon.className = 'fa-sharp fa-circle-check text-success';
            // We don't update the text here as it contains the signatory name and date
            // which we don't have access to in JavaScript without reloading
        } else if (isDropOff) {
            // Show green check icon - drop off
            driverStatusIcon.className = 'fa-sharp fa-circle-check text-success';
            // Update the status text
            if (driverStatusText) {
                driverStatusText.textContent = 'Dropped Off - No Signature Required';
            }
        } else {
            // Show red X icon
            driverStatusIcon.className = 'fa-sharp fa-circle-xmark text-error';
            // Update the status text
            if (driverStatusText) {
                driverStatusText.textContent = 'Unsigned';
            }
        }

        console.log('Driver status updated:', { isDriverSigned, isDropOff, icon: driverStatusIcon.className });
    }

    function disableSignButtons() {
        const signButtons = document.querySelectorAll('button[id$="-sign-button"]');
        signButtons.forEach(button => {
            button.disabled = true;
        });
    }

    function enableSignButtons() {
        const signButtons = document.querySelectorAll('button[id$="-sign-button"]');
        signButtons.forEach(button => {
            button.disabled = false;
        });
    }

    function confirmDeleteSignature() {
        if (!window.currentSignatureId) {
            console.warn('No signature selected for deletion.'); // Log a warning
            alert('No signature selected for deletion.');
            return;
        }
        if (confirm('Are you sure you want to delete this signature?')) {
            console.log(`Deleting signature with ID: ${window.currentSignatureId}`); // Log the deletion action
            deleteSignature(window.currentSignatureId); // Use the global variable for the current signature ID
            closeSignatureViewModal(); // Close the modal after deletion
        }
    }

    function updateUi() {
        const serviceSelection = document.getElementById('service_selection').value;
        const manifestQuestionsContainer = document.getElementById('manifest-questions-container');
        const countFieldsContainer = document.getElementById('count-fields-container');

        // Get the selected manifest option
        const clientProvidedManifestRadio = document.querySelector('input[name="client_provided_manifest"]:checked');
        const clientProvidedManifest = clientProvidedManifestRadio ? clientProvidedManifestRadio.value : '';

        // Hide everything by default
        manifestQuestionsContainer.style.display = 'none';
        countFieldsContainer.style.display = 'none';

        const isClientSigned = document.getElementById('is_client_signed_hidden').value === 'true';

        if (serviceSelection === 'certified_destruction') {
            if (!isClientSigned) {
                manifestQuestionsContainer.style.display = 'block';
            }

            if (clientProvidedManifest === 'yes') {
                countFieldsContainer.style.display = 'block';
            } else if (clientProvidedManifest === 'no') {
                // Reset all counts if no manifest provided
                const totalCountField = document.getElementById('client_manifest_total_count');
                const verifiedCountField = document.getElementById('etech_verified_total_count');
                if (totalCountField) totalCountField.value = 0;
                if (verifiedCountField) verifiedCountField.value = 0;
            }
        } else {
            // Reset all counts for non-certified services
            const totalCountField = document.getElementById('client_manifest_total_count');
            const verifiedCountField = document.getElementById('etech_verified_total_count');
            if (totalCountField) totalCountField.value = 0;
            if (verifiedCountField) verifiedCountField.value = 0;
        }
        debounceSaveStats('client');
    }

    function updateClientProvidedManifest(value) {
        // Update the stats to match the new radio button structure
        updateUi();
    }
</script>
@endpush