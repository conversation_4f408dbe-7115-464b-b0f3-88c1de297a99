<!-- Universal Dialog Component for Confirmations, Alerts, and Warnings -->
<dialog id="appDialog" class="modal">
    <div class="modal-box">
        <!-- Dialog Icon -->
        <div class="flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full" id="dialogIcon">
            <i class="text-2xl" id="dialogIconClass"></i>
        </div>
        
        <!-- Dialog Title -->
        <h3 class="text-lg font-bold text-center mb-4" id="dialogTitle">Confirmation</h3>
        
        <!-- Dialog Content -->
        <div class="py-4 text-center" id="dialogContent">
            <p id="dialogMessage">Are you sure you want to proceed?</p>
        </div>
        
        <!-- Dialog Actions -->
        <div class="modal-action justify-center" id="dialogActions">
            <!-- Default confirmation buttons -->
            <button type="button" class="btn btn-ghost" id="dialogCancelBtn">Cancel</button>
            <button type="button" class="btn btn-primary" id="dialogConfirmBtn">Confirm</button>
        </div>
    </div>
    
    <!-- Backdrop to close modal -->
    <form method="dialog" class="modal-backdrop">
        <button type="submit">close</button>
    </form>
</dialog>

<script>
class AppDialog {
    constructor() {
        this.dialog = document.getElementById('appDialog');
        this.iconContainer = document.getElementById('dialogIcon');
        this.iconClass = document.getElementById('dialogIconClass');
        this.title = document.getElementById('dialogTitle');
        this.message = document.getElementById('dialogMessage');
        this.actions = document.getElementById('dialogActions');
        this.cancelBtn = document.getElementById('dialogCancelBtn');
        this.confirmBtn = document.getElementById('dialogConfirmBtn');
        
        this.currentCallback = null;
        this.currentRejectCallback = null;
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Cancel button
        this.cancelBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (this.currentRejectCallback) {
                this.currentRejectCallback();
            }
            this.close();
        });

        // Confirm button
        this.confirmBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (this.currentCallback) {
                this.currentCallback();
            }
            this.close();
        });

        // ESC key and backdrop click handling
        this.dialog.addEventListener('close', () => {
            if (this.currentRejectCallback) {
                this.currentRejectCallback();
            }
        });
    }
    
    show() {
        this.dialog.showModal();
    }
    
    close() {
        this.dialog.close();
        this.currentCallback = null;
        this.currentRejectCallback = null;
    }
    
    // Confirmation dialog
    confirm(options = {}) {
        const {
            title = 'Confirm Action',
            message = 'Are you sure you want to proceed?',
            confirmText = 'Confirm',
            cancelText = 'Cancel',
            confirmClass = 'btn-primary',
            icon = 'fa-question-circle',
            iconColor = 'bg-primary/10 text-primary'
        } = options;

        return new Promise((resolve, reject) => {
            this.currentCallback = () => resolve(true);
            this.currentRejectCallback = () => resolve(false);

            this.setupDialog({
                title,
                message,
                icon,
                iconColor,
                actions: [
                    { text: cancelText, class: 'btn btn-ghost', action: 'cancel' },
                    { text: confirmText, class: `btn ${confirmClass}`, action: 'confirm' }
                ]
            });

            this.show();
        });
    }
    
    // Alert dialog (info/success)
    alert(options = {}) {
        const {
            title = 'Information',
            message = 'This is an alert message.',
            buttonText = 'OK',
            type = 'info', // info, success, warning, error
            icon = this.getIconForType(type),
            iconColor = this.getIconColorForType(type)
        } = options;
        
        return new Promise((resolve) => {
            this.currentCallback = () => resolve(true);
            this.currentRejectCallback = () => resolve(true);
            
            this.setupDialog({
                title,
                message,
                icon,
                iconColor,
                actions: [
                    { text: buttonText, class: `btn ${this.getButtonClassForType(type)}`, action: 'confirm' }
                ]
            });
            
            this.show();
        });
    }
    
    // Warning dialog
    warning(options = {}) {
        return this.alert({
            ...options,
            type: 'warning',
            title: options.title || 'Warning',
            icon: options.icon || 'fa-exclamation-triangle'
        });
    }
    
    // Error dialog
    error(options = {}) {
        return this.alert({
            ...options,
            type: 'error',
            title: options.title || 'Error',
            icon: options.icon || 'fa-times-circle'
        });
    }
    
    // Success dialog
    success(options = {}) {
        return this.alert({
            ...options,
            type: 'success',
            title: options.title || 'Success',
            icon: options.icon || 'fa-check-circle'
        });
    }
    
    setupDialog({ title, message, icon, iconColor, actions }) {
        // Set title
        this.title.textContent = title;

        // Set message
        this.message.textContent = message;

        // Set icon
        this.iconClass.className = `fa-sharp ${icon} text-2xl`;
        this.iconContainer.className = `flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full ${iconColor}`;

        // Update existing buttons instead of creating new ones
        if (actions.length === 1) {
            // Single button (alert/success/error/warning)
            this.cancelBtn.style.display = 'none';
            this.confirmBtn.style.display = 'inline-flex';
            this.confirmBtn.textContent = actions[0].text;
            this.confirmBtn.className = actions[0].class;
        } else if (actions.length === 2) {
            // Two buttons (confirmation)
            this.cancelBtn.style.display = 'inline-flex';
            this.confirmBtn.style.display = 'inline-flex';

            // Find cancel and confirm actions
            const cancelAction = actions.find(a => a.action === 'cancel');
            const confirmAction = actions.find(a => a.action === 'confirm');

            if (cancelAction) {
                this.cancelBtn.textContent = cancelAction.text;
                this.cancelBtn.className = cancelAction.class;
            }

            if (confirmAction) {
                this.confirmBtn.textContent = confirmAction.text;
                this.confirmBtn.className = confirmAction.class;
            }
        }
    }
    
    getIconForType(type) {
        const icons = {
            info: 'fa-info-circle',
            success: 'fa-check-circle',
            warning: 'fa-exclamation-triangle',
            error: 'fa-times-circle'
        };
        return icons[type] || 'fa-info-circle';
    }
    
    getIconColorForType(type) {
        const colors = {
            info: 'bg-info/10 text-info',
            success: 'bg-success/10 text-success',
            warning: 'bg-warning/10 text-warning',
            error: 'bg-error/10 text-error'
        };
        return colors[type] || 'bg-info/10 text-info';
    }
    
    getButtonClassForType(type) {
        const classes = {
            info: 'btn-info',
            success: 'btn-success',
            warning: 'btn-warning',
            error: 'btn-error'
        };
        return classes[type] || 'btn-info';
    }
}

// Initialize the dialog when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.appDialog = new AppDialog();
});

// Global convenience functions
window.showConfirm = (options) => {
    if (!window.appDialog) {
        console.error('AppDialog not initialized yet');
        return Promise.resolve(false);
    }
    return window.appDialog.confirm(options);
};
window.showAlert = (options) => window.appDialog?.alert(options);
window.showWarning = (options) => window.appDialog?.warning(options);
window.showError = (options) => window.appDialog?.error(options);
window.showSuccess = (options) => window.appDialog?.success(options);
</script>
