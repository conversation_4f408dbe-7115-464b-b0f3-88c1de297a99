@props(['model', 'modelType'])

<div class="bg-base-100 shadow-md rounded-lg p-6 mt-6">
    <div class="flex items-center mb-2">
        <i class="fa-sharp fa-file-upload text-primary mr-2"></i>
        <h2 class="text-lg font-semibold">Document Management</h2>
    </div>

    <div class="divider my-2"></div>

    <!-- File Upload Component -->
    <div class="mb-4">
        <h4 class="font-medium mb-2">Upload Documents</h4>
        <p class="text-sm text-base-content mb-3">Upload up to 5 files at once. Supported types: PDF, JPG, PNG, GIF, DOC, DOCX, XLS, XLSX, TXT (Max: 10MB per file)</p>

        <!-- Dropzone Area -->
        <div id="dropzone" class="border-2 border-dashed border-base-300 rounded-lg p-6 text-center hover:bg-base-200 transition cursor-pointer mb-4">
            <i class="fa-sharp fa-cloud-arrow-up text-3xl text-gray-400 mb-2"></i>
            <p class="text-gray-600">Drag and drop files here or click to browse</p>
            <p class="text-xs text-base-content mt-1">Up to 5 files, 10MB each</p>
            <input type="file" id="fileInput" class="hidden" multiple accept=".pdf,.jpg,.jpeg,.png,.gif,.doc,.docx,.xls,.xlsx,.txt" />
        </div>

        <!-- Selected Files Queue -->
        <div id="fileQueue" class="hidden mb-4">
            <h5 class="font-medium mb-2">Selected Files</h5>
            <div class="overflow-x-auto">
                <table class="table table-sm w-full">
                    <thead>
                        <tr>
                            <th>File</th>
                            <th>Size</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="queuedFiles">
                        <!-- Files will be added here dynamically -->
                    </tbody>
                </table>
            </div>

            <div class="flex justify-end mt-4">
                <button id="uploadAllBtn" class="btn btn-primary">
                    <i class="fa-sharp fa-upload mr-1"></i> Upload All Files
                </button>
            </div>
        </div>

        <!-- Upload Progress -->
        <div id="uploadProgress" class="hidden">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium">Uploading files...</span>
                <span id="progressCounter" class="text-sm">0/0</span>
            </div>
            <progress id="progressBar" class="progress progress-primary w-full" value="0" max="100"></progress>
        </div>

        <!-- Hidden Form for CSRF Token -->
        <form id="fileUploadForm" class="hidden">
            @csrf
        </form>
    </div>

    <!-- File List -->
    <div>
        <h4 class="font-medium mb-2">Uploaded Files</h4>

        @if($model->files->isEmpty())
            <div class="text-sm text-base-content italic">
                <i class="fa-sharp fa-circle-info mr-1"></i>
                <span>No files have been uploaded yet.</span>
            </div>
        @else
            <div class="overflow-x-auto">
                <table class="table table-sm w-full">
                    <thead>
                        <tr>
                            <th>File</th>
                            <th>Notes</th>
                            <th>Size</th>
                            <th>Uploaded</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($model->files as $file)
                            <tr>
                                <td>
                                    <div class="flex items-center gap-2">
                                        @if($file->is_image)
                                            <div class="image-preview-trigger cursor-pointer"
                                                 data-image-url="{{ $file->getThumbnailUrl(400, null) }}"
                                                 data-image-name="{{ $file->original_filename }}">
                                                <i class="fa-sharp {{ $file->icon }}"></i>
                                                <span class="font-medium">{{ Str::limit($file->original_filename, 20) }}</span>
                                            </div>
                                        @else
                                            <i class="fa-sharp {{ $file->icon }}"></i>
                                            <span class="font-medium">{{ Str::limit($file->original_filename, 20) }}</span>
                                        @endif
                                        @if(isset($file->metadata['file_type']) && $file->metadata['file_type'] != 'other')
                                            <span class="badge badge-sm badge-primary">{{ ucfirst($file->metadata['file_type']) }}</span>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-xs btn-ghost {{ $file->description ? 'text-primary' : 'text-gray-400' }}"
                                            onclick="openFileNotesModal('{{ $file->id }}', '{{ addslashes($file->original_filename) }}', '{{ addslashes($file->description ?? '') }}')"
                                            title="{{ $file->description ? 'Edit notes' : 'Add notes' }}"
                                            data-file-id="{{ $file->id }}"
                                            data-description="{{ addslashes($file->description ?? '') }}">
                                        <i class="fa-sharp fa-note-sticky"></i>
                                    </button>
                                </td>
                                <td class="text-xs">{{ round($file->size / 1024, 2) }} KB</td>
                                <td class="text-xs">
                                    {{ $file->created_at->format('M j, Y') }}
                                    <span class="text-gray-500">by {{ $file->uploader->name ?? 'Unknown' }}</span>
                                </td>
                                <td>
                                    <div class="flex gap-1">
                                        <a href="{{ route('files.view', $file) }}" class="btn btn-xs btn-ghost text-info" target="_blank" title="View">
                                            <i class="fa-sharp fa-eye"></i>
                                        </a>
                                        <a href="{{ route('files.download', $file) }}" class="btn btn-xs btn-ghost text-primary" title="Download">
                                            <i class="fa-sharp fa-download"></i>
                                        </a>
                                        @if(Auth::id() === $file->uploaded_by || Auth::user()->isAdmin())
                                            <button type="button" class="btn btn-xs btn-ghost text-error" title="Delete"
                                                    onclick="deleteFile('{{ $file->id }}')">
                                                <i class="fa-sharp fa-trash"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif
    </div>
</div>

<!-- File Notes Modal -->
@once
<dialog id="fileNotesModal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">File Notes</h3>
        <p id="fileNameDisplay" class="text-sm mb-4"></p>

        <form id="fileNotesForm">
            <div class="form-control">
                <textarea id="fileNotesInput" class="textarea textarea-bordered h-24" placeholder="Enter notes about this file"></textarea>
            </div>

            <div class="modal-action">
                <input type="hidden" id="fileIdInput" value="">
                <button type="button" class="btn btn-primary" onclick="saveFileNotes()">Save</button>
                <button type="button" class="btn" onclick="closeFileNotesModal()">Cancel</button>
            </div>
        </form>
    </div>
    <form method="dialog" class="modal-backdrop">
        <button onclick="closeFileNotesModal()">close</button>
    </form>
</dialog>

<script>
    // File Notes Modal Functions
    function openFileNotesModal(fileId, fileName, notes) {
        document.getElementById('fileIdInput').value = fileId;
        document.getElementById('fileNameDisplay').textContent = fileName;
        document.getElementById('fileNotesInput').value = notes;
        document.getElementById('fileNotesModal').showModal();
    }

    function closeFileNotesModal() {
        document.getElementById('fileNotesModal').close();
    }

    function deleteFile(fileId) {
        if (!confirm('Are you sure you want to delete this file?')) {
            return;
        }

        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Add cache: 'no-cache' to bypass service worker issues
        fetch(`/files/${fileId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            cache: 'no-cache',
            credentials: 'same-origin'
        })
        .then(response => {
            // Check if response is ok before trying to parse JSON
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Remove the row from the table
                const row = document.querySelector(`button[data-file-id="${fileId}"]`).closest('tr');
                if (row) {
                    row.remove();
                }

                // If no more files, show the empty message
                const tbody = document.querySelector('tbody');
                if (tbody && tbody.children.length === 0) {
                    const table = tbody.closest('.overflow-x-auto');
                    if (table) {
                        table.innerHTML = `
                            <div class="text-sm text-base-content italic">
                                <i class="fa-sharp fa-circle-info mr-1"></i>
                                <span>No files have been uploaded yet.</span>
                            </div>
                        `;
                    }
                }
                
                // Show success message
                showSuccessToast(data.message || 'File deleted successfully');
            } else {
                alert('Error deleting file: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Check if it's a network error from service worker
            if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
                // Try to reload the page as a fallback
                console.log('Network error detected, reloading page...');
                window.location.reload();
            } else {
                alert('Error deleting file. Please try again.');
            }
        });
    }
    
    // Helper function to show success toast
    function showSuccessToast(message) {
        // Check if dynamic toast exists
        const dynamicToast = document.getElementById('dynamicToast');
        const dynamicToastMessage = document.getElementById('dynamicToastMessage');
        
        if (dynamicToast && dynamicToastMessage) {
            dynamicToastMessage.textContent = message;
            dynamicToast.classList.remove('hidden');
            
            // Hide after 3 seconds
            setTimeout(() => {
                dynamicToast.classList.add('hidden');
            }, 3000);
        }
    }

    function saveFileNotes() {
        const fileId = document.getElementById('fileIdInput').value;
        const notes = document.getElementById('fileNotesInput').value;
        const fileName = document.getElementById('fileNameDisplay').textContent;
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Show saving indicator
        const saveButton = document.querySelector('#fileNotesForm .btn-primary');
        const originalText = saveButton.innerHTML;
        saveButton.innerHTML = '<i class="fa-sharp fa-spinner fa-spin"></i> Saving...';
        saveButton.disabled = true;

        fetch(`/files/${fileId}/update-notes`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({
                description: notes
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the button color based on whether there are notes
                const noteButton = document.querySelector(`button[data-file-id="${fileId}"]`);
                if (noteButton) {
                    if (notes) {
                        noteButton.classList.remove('text-gray-400');
                        noteButton.classList.add('text-primary');
                        noteButton.title = 'Edit notes';
                    } else {
                        noteButton.classList.remove('text-primary');
                        noteButton.classList.add('text-gray-400');
                        noteButton.title = 'Add notes';
                    }
                }

                // Update the onclick attribute to include the new description
                const escapedNotes = notes.replace(/'/g, "\\'");
                if (noteButton) {
                    noteButton.setAttribute('onclick', `openFileNotesModal('${fileId}', '${fileName}', '${escapedNotes}')`);
                    noteButton.setAttribute('data-description', escapedNotes);
                }

                closeFileNotesModal();
            } else {
                alert('Error saving notes: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error saving notes. Please try again.');
        })
        .finally(() => {
            // Restore button state
            saveButton.innerHTML = originalText;
            saveButton.disabled = false;
        });
    }

    // Dropzone and Multiple File Upload Functionality
    document.addEventListener('DOMContentLoaded', function() {
        const dropzone = document.getElementById('dropzone');
        const fileInput = document.getElementById('fileInput');
        const fileQueue = document.getElementById('fileQueue');
        const queuedFiles = document.getElementById('queuedFiles');
        const uploadAllBtn = document.getElementById('uploadAllBtn');
        const uploadProgress = document.getElementById('uploadProgress');
        const progressBar = document.getElementById('progressBar');
        const progressCounter = document.getElementById('progressCounter');
        const modelType = '{{ $modelType }}';
        const modelId = '{{ $model->id }}';
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        let files = [];
        const MAX_FILES = 5;
        const FILE_TYPES = {
            'pdf': 'PDF Document',
            'jpg': 'JPEG Image',
            'jpeg': 'JPEG Image',
            'png': 'PNG Image',
            'gif': 'GIF Image',
            'doc': 'Word Document',
            'docx': 'Word Document',
            'xls': 'Excel Spreadsheet',
            'xlsx': 'Excel Spreadsheet',
            'txt': 'Text Document'
        };

        // Initialize dropzone
        dropzone.addEventListener('click', () => {
            fileInput.click();
        });

        // Handle file selection
        fileInput.addEventListener('change', handleFileSelection);

        // Handle drag and drop
        dropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropzone.classList.add('border-primary');
        });

        dropzone.addEventListener('dragleave', () => {
            dropzone.classList.remove('border-primary');
        });

        dropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropzone.classList.remove('border-primary');

            if (e.dataTransfer.files.length > 0) {
                handleFiles(e.dataTransfer.files);
            }
        });

        // Handle file selection from input
        function handleFileSelection() {
            if (fileInput.files.length > 0) {
                handleFiles(fileInput.files);
            }
        }

        // Process selected files
        function handleFiles(selectedFiles) {
            // Check if adding these files would exceed the limit
            if (files.length + selectedFiles.length > MAX_FILES) {
                alert(`You can only upload up to ${MAX_FILES} files at once. Please remove some files and try again.`);
                return;
            }

            // Add files to the queue
            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];

                // Check file size (10MB limit)
                if (file.size > 10 * 1024 * 1024) {
                    alert(`File "${file.name}" exceeds the 10MB size limit.`);
                    continue;
                }

                // Add file to the queue
                const fileId = 'file-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
                files.push({
                    id: fileId,
                    file: file,
                    description: '',
                    type: 'other'
                });

                // Add file to the UI
                addFileToQueue(fileId, file);
            }

            // Show the file queue if there are files
            if (files.length > 0) {
                fileQueue.classList.remove('hidden');
            }

            // Reset the file input
            fileInput.value = '';
        }

        // Add a file to the queue UI
        function addFileToQueue(fileId, file) {
            const extension = file.name.split('.').pop().toLowerCase();
            const fileType = FILE_TYPES[extension] || 'Unknown File Type';
            const fileSize = formatFileSize(file.size);

            const row = document.createElement('tr');
            row.id = fileId;
            row.innerHTML = `
                <td class="font-medium">${file.name}</td>
                <td>${fileSize}</td>
                <td>
                    <select class="select select-bordered select-sm w-full max-w-xs file-type">
                        <option value="other" selected>Other</option>
                        <option value="contract">Contract</option>
                        <option value="photo_id">Photo ID</option>
                        <option value="receipt">Receipt</option>
                    </select>
                </td>
                <td>
                    <input type="text" class="input input-bordered input-sm w-full file-description" placeholder="Description (optional)">
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-ghost text-error remove-file" data-file-id="${fileId}">
                        <i class="fa-sharp fa-trash"></i>
                    </button>
                </td>
            `;

            queuedFiles.appendChild(row);

            // Add event listeners for the new row
            row.querySelector('.file-type').addEventListener('change', (e) => {
                const fileIndex = files.findIndex(f => f.id === fileId);
                if (fileIndex !== -1) {
                    files[fileIndex].type = e.target.value;
                }
            });

            row.querySelector('.file-description').addEventListener('input', (e) => {
                const fileIndex = files.findIndex(f => f.id === fileId);
                if (fileIndex !== -1) {
                    files[fileIndex].description = e.target.value;
                }
            });

            row.querySelector('.remove-file').addEventListener('click', () => {
                // Remove file from the array
                files = files.filter(f => f.id !== fileId);

                // Remove the row from the UI
                row.remove();

                // Hide the queue if there are no more files
                if (files.length === 0) {
                    fileQueue.classList.add('hidden');
                }
            });
        }

        // Format file size for display
        function formatFileSize(bytes) {
            if (bytes < 1024) {
                return bytes + ' bytes';
            } else if (bytes < 1024 * 1024) {
                return (bytes / 1024).toFixed(2) + ' KB';
            } else {
                return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
            }
        }

        // Upload all files
        uploadAllBtn.addEventListener('click', async () => {
            if (files.length === 0) {
                alert('Please select at least one file to upload.');
                return;
            }

            // Show progress UI
            fileQueue.classList.add('hidden');
            uploadProgress.classList.remove('hidden');
            progressBar.value = 0;
            progressBar.max = files.length;
            progressCounter.textContent = `0/${files.length}`;

            let successCount = 0;
            let errorCount = 0;

            // Upload each file
            for (let i = 0; i < files.length; i++) {
                const fileObj = files[i];
                const formData = new FormData();

                formData.append('file', fileObj.file);
                formData.append('description', fileObj.description);
                formData.append('file_type', fileObj.type);
                formData.append('_token', csrfToken);

                try {
                    const response = await fetch(`/${modelType}s/${modelId}/files`, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });

                    const result = await response.json();

                    if (result.success) {
                        successCount++;
                    } else {
                        errorCount++;
                        console.error('Error uploading file:', result.message);
                    }
                } catch (error) {
                    errorCount++;
                    console.error('Error uploading file:', error);
                }

                // Update progress
                progressBar.value = i + 1;
                progressCounter.textContent = `${i + 1}/${files.length}`;
            }

            // Show completion message
            if (errorCount === 0) {
                alert(`All ${successCount} files uploaded successfully!`);
            } else {
                alert(`${successCount} files uploaded successfully. ${errorCount} files failed to upload.`);
            }

            // Reset and reload the page to show the new files
            window.location.reload();
        });
    });
</script>
@endonce

<style>
    /* Image preview trigger styles */
    .image-preview-trigger {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .image-preview-trigger:hover {
        text-decoration: underline;
        color: var(--color-primary);
    }
</style>
