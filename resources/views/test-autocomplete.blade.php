<x-app-layout page-title="Test Google Maps Autocomplete">
    <div class="py-6">
        <div class="max-w-2xl mx-auto px-4">
            <div class="card bg-base-100 shadow-md">
                <div class="card-body">
                    <h2 class="card-title">Test Google Maps Autocomplete - Server-Side API</h2>
                    <div class="alert alert-info mb-4">
                        <i class="fa-sharp fa-info-circle"></i>
                        <span>Now using server-side Google Maps API calls to protect the API key!</span>
                    </div>
                    
                    <form action="/test-autocomplete" method="POST" id="testForm">
                        @csrf
                        
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Test Address Field (No Display Mode)</span>
                            </label>
                            <x-google-maps-autocomplete
                                id="test_address"
                                name="test_address"
                                placeholder="Start typing an address..."
                                type="textarea"
                                rows="3"
                                label="Test Address"
                                :showDisplayMode="false"
                            />
                        </div>

                        <div class="form-control mt-4">
                            <label class="label">
                                <span class="label-text">Test Address Field with Display Mode (Existing Address) - New API</span>
                            </label>
                            <x-google-maps-autocomplete
                                id="test_address_display"
                                name="test_address_display"
                                value="123 Main St, Anytown, USA 12345"
                                placeholder="Start typing an address..."
                                type="input"
                                label="Address with Display Mode"
                                icon="fa-map-marker-alt"
                                iconColor="text-info"
                                :restrictions="['us', 'ca']"
                                :showDisplayMode="true"
                                displayLabel="Current Address"
                                changeButtonText="Change"
                            />
                        </div>

                        <div class="form-control mt-4">
                            <label class="label">
                                <span class="label-text">Test Address Field with Display Mode (No Existing Address)</span>
                            </label>
                            <x-google-maps-autocomplete
                                id="test_address_empty"
                                name="test_address_empty"
                                value=""
                                placeholder="Start typing an address..."
                                type="input"
                                label="Address with Display Mode (Empty)"
                                icon="fa-map-marker-alt"
                                iconColor="text-success"
                                :restrictions="['us', 'ca']"
                                :showDisplayMode="true"
                                displayLabel="Current Address"
                                changeButtonText="Change"
                            />
                        </div>
                        
                        <div class="form-control mt-4">
                            <label class="label">
                                <span class="label-text">Regular Text Field (for comparison)</span>
                            </label>
                            <input type="text" name="regular_field" class="input input-bordered" placeholder="Regular field" value="test value">
                        </div>
                        
                        <div class="form-control mt-6">
                            <button type="submit" class="btn btn-primary">Submit Test Form</button>
                        </div>
                    </form>
                    
                    <div class="mt-6">
                        <h3 class="text-lg font-semibold mb-2">Debug Info</h3>
                        <div id="debugInfo" class="bg-base-200 p-4 rounded text-sm font-mono">
                            <div>Form will be debugged here...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('testForm');
            const debugInfo = document.getElementById('debugInfo');
            
            // Debug form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault(); // Prevent actual submission for testing
                
                const formData = new FormData(form);
                let debugHtml = '<div><strong>Form Submission Debug:</strong></div>';
                
                for (let [key, value] of formData.entries()) {
                    debugHtml += `<div>${key}: "${value}"</div>`;
                }
                
                debugHtml += '<div><strong>Raw form elements:</strong></div>';
                const inputs = form.querySelectorAll('input, textarea');
                inputs.forEach(input => {
                    if (input.name) {
                        debugHtml += `<div>${input.name} (${input.type}): "${input.value}"</div>`;
                    }
                });
                
                debugInfo.innerHTML = debugHtml;
            });
            
            // Monitor address field changes
            const addressField = document.querySelector('input[name="test_address"], textarea[name="test_address"]');
            if (addressField) {
                addressField.addEventListener('change', function() {
                    console.log('Address field changed to:', this.value);
                });
            }
            
            // Monitor for address selection events
            document.addEventListener('addressSelected', function(e) {
                console.log('Address selected event:', e.detail);
                debugInfo.innerHTML += `<div><strong>Address Selected:</strong> ${e.detail.place.formatted_address || e.detail.place.formattedAddress}</div>`;
            });
        });
    </script>
</x-app-layout>
