<x-app-layout
    page-title="Edit Discount: {{ $discount->name }}"
    page-icon="fa-sharp fa-edit"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('discounts.index'),
            'text' => 'Back to Discounts'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'Delete Discount',
            'route' => route('discounts.destroy', $discount),
            'icon' => 'fa-sharp fa-trash',
            'class' => 'btn btn-error btn-sm gap-2',
            'confirm' => 'Are you sure you want to delete this discount? This action cannot be undone.'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Discounts', 'route' => 'discounts.index', 'icon' => 'fa-tags'],
        ['name' => 'Edit: ' . $discount->name, 'icon' => 'fa-edit']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow p-6">
                <form action="{{ route('discounts.update', $discount->id) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Discount Name -->
                        <fieldset class="fieldset">
                            <label for="name" class="label">Discount Name <span class="text-error">*</span></label>
                            <input type="text" name="name" id="name" class="input" value="{{ old('name', $discount->name) }}" required>
                            @error('name')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>

                        <!-- Discount Type -->
                        <fieldset class="fieldset">
                            <label for="type" class="label">Type <span class="text-error">*</span></label>
                            <select name="type" id="type" class="select select-bordered" required>
                                <option value="percent" {{ old('type', $discount->type) === 'percent' ? 'selected' : '' }}>Percentage (%)</option>
                                <option value="fixed" {{ old('type', $discount->type) === 'fixed' ? 'selected' : '' }}>Fixed Amount ($)</option>
                            </select>
                            @error('type')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>

                        <!-- Discount Amount -->
                        <fieldset class="fieldset">
                            <label for="amount" class="label">Amount <span class="text-error">*</span></label>
                            <input type="number" name="amount" id="amount" class="input input-bordered" value="{{ old('amount', $discount->amount) }}" step="0.01" required>
                            @error('amount')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>

                        <!-- Scope -->
                        <fieldset class="fieldset">
                            <label for="scope" class="label">Scope <span class="text-error">*</span></label>
                            <select name="scope" id="scope" class="select select-bordered" required>
                                <option value="invoice" {{ old('scope', $discount->scope) === 'invoice' ? 'selected' : '' }}>Entire Invoice</option>
                                <option value="line_item" {{ old('scope', $discount->scope) === 'line_item' ? 'selected' : '' }}>Single Line Item</option>
                            </select>
                            @error('scope')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>

                        <!-- Start Date -->
                        <fieldset class="fieldset">
                            <label for="start_date" class="label">Start Date</label>
                            <input type="datetime-local" name="start_date" id="start_date" class="input input-bordered" value="{{ old('start_date', $discount->start_date ? $discount->start_date->format('Y-m-d\\TH:i') : '') }}">
                            @error('start_date')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>

                        <!-- End Date -->
                        <fieldset class="fieldset">
                            <label for="end_date" class="label">End Date</label>
                            <input type="datetime-local" name="end_date" id="end_date" class="input input-bordered" value="{{ old('end_date', $discount->end_date ? $discount->end_date->format('Y-m-d\\TH:i') : '') }}">
                            @error('end_date')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>

                        <!-- Minimum Purchase -->
                        <fieldset class="fieldset">
                            <label for="minimum_purchase" class="label">Minimum Purchase Amount</label>
                            <input type="number" name="minimum_purchase" id="minimum_purchase" class="input input-bordered" value="{{ old('minimum_purchase', $discount->minimum_purchase) }}" step="0.01">
                            @error('minimum_purchase')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>

                        <!-- Maximum Discount -->
                        <fieldset class="fieldset">
                            <label for="maximum_discount" class="label">Maximum Discount Amount</label>
                            <input type="number" name="maximum_discount" id="maximum_discount" class="input input-bordered" value="{{ old('maximum_discount', $discount->maximum_discount) }}" step="0.01">
                            @error('maximum_discount')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>

                        <!-- Category -->
                        <fieldset class="fieldset">
                            <label for="category_id" class="label">Category (Optional)</label>
                            <select name="category_id" id="category_id" class="select select-bordered">
                                <option value="">All Categories</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ old('category_id', $discount->category_id) == $category->id ? 'selected' : '' }}>{{ $category->name }}</option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>

                        <!-- Reusable -->
                        <fieldset class="fieldset">
                            <label class="cursor-pointer label">
                                <span class="label-text">Reusable</span>
                                <input type="hidden" name="reusable" value="0">
                                <input type="checkbox" name="reusable" class="toggle toggle-primary" value="1" {{ old('reusable', $discount->reusable) ? 'checked' : '' }}>
                            </label>
                            <p>Can the same customer use this discount multiple times?</p>
                        </fieldset>

                        <!-- is customer specific -->
                        <fieldset class="fieldset">
                            <label class="cursor-pointer label">
                                <span class="label-text">Customer Specific</span>
                                <input type="hidden" name="is_customer_specific" value="0">
                                <input type="checkbox" name="is_customer_specific" class="toggle toggle-primary" value="1" {{ old('is_customer_specific', $discount->is_customer_specific) ? 'checked' : '' }}>
                            </label>
                            <p>Is this discount only for specific customers? You will manually add them later.</p>
                        </fieldset>

                    </div>

                    <!-- Submit Button -->
                    <div class="mt-8 flex justify-end">
                        <button type="submit" class="btn btn-primary bg-primary text-primary-content hover:bg-primary-focus">Update Discount</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
