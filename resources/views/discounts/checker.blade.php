<x-app-layout
    page-title="Discount Checker"
    page-icon="fa-sharp fa-search"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('discounts.index'),
            'text' => 'Back to Discounts'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Discounts', 'route' => 'discounts.index', 'icon' => 'fa-tags'],
        ['name' => 'Discount Checker', 'icon' => 'fa-search']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-md rounded p-6">
                <h3 class="text-lg font-semibold mb-4">Search for a Customer</h3>

                <!-- Use the existing Dynamic Customer Search Component -->
                <x-dynamic-customer-search id="customerSearch" name="customer_id"
                    placeholder="Search for customers by name, email, phone, or nickname..." action="form" />

                <!-- Discounts Section -->
                <div id="discounts_section" class="mt-6 hidden">
                    <!-- Usable Discounts -->
                    <div id="usable_discounts_section" class="mb-8 hidden">
                        <h3 class="text-lg font-semibold mb-4">Usable Discounts</h3>
                        <div id="usable_discounts_list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <!-- Usable discounts will be dynamically added here -->
                        </div>
                    </div>

                    <!-- Unusable Discounts -->
                    <div id="unusable_discounts_section" class="hidden">
                        <h3 class="text-lg font-semibold mb-4">Unusable Discounts</h3>
                        <div id="unusable_discounts_list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <!-- Unusable discounts will be dynamically added here -->
                        </div>
                    </div>

                    <p id="no_discounts" class="text-base-content text-opacity-60 hidden">No discounts available for this customer.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Confirmation Modal -->
    <div id="customConfirmModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 transition-opacity">
                <div class="absolute inset-0 bg-base-300 opacity-75"></div>
            </div>
            
            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-base-100 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-base-100 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 sm:mx-0 sm:h-10 sm:w-10">
                            <svg class="h-6 w-6 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Confirm Unmark Discount</h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500">Are you sure you want to unmark this discount as used?</p>
                                <p class="mt-2 text-sm text-gray-500">This will decrease the usage count by 1.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button id="customConfirmBtn" type="button" class="btn btn-sm btn-danger ml-3">Unmark Discount</button>
                    <button id="customCancelBtn" type="button" class="btn btn-sm btn-secondary">Cancel</button>
                </div>
            </div>
        </div>
    </div>

@push('scripts')
<script>
   document.addEventListener("DOMContentLoaded", function () {
        const customerResultsContainer = document.getElementById("customerSearch_results");
        const discountsSection = document.getElementById("discounts_section");
        const usableDiscountsSection = document.getElementById("usable_discounts_section");
        const unusableDiscountsSection = document.getElementById("unusable_discounts_section");
        const usableDiscountsList = document.getElementById("usable_discounts_list");
        const unusableDiscountsList = document.getElementById("unusable_discounts_list");
        const noDiscountsMessage = document.getElementById("no_discounts");
        
        // Store the selected customer ID at a higher scope
        let selectedCustomerId = null;
        
        // Check if customer_id exists in URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const customerIdFromUrl = urlParams.get('customer_id');
        
        if (customerIdFromUrl) {
            // If customer ID is provided in the URL, load customer's discounts directly
            selectedCustomerId = customerIdFromUrl;
            loadCustomerDiscounts(customerIdFromUrl);
            
            // Optional: Display customer name if available
            fetch(`/customers/${customerIdFromUrl}/name`, {
                headers: {
                    "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content,
                    "Accept": "application/json"
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.name) {
                    const customerInfoDiv = document.createElement('div');
                    customerInfoDiv.classList.add('mt-4', 'mb-6', 'p-3', 'bg-blue-50', 'border', 'border-blue-200', 'rounded');
                    customerInfoDiv.innerHTML = `<p class="text-lg">Viewing discounts for: <strong>${data.name}</strong></p>`;
                    
                    // Insert after the search component
                    const searchComponent = document.querySelector('#customerSearch');
                    searchComponent.parentNode.insertBefore(customerInfoDiv, searchComponent.nextSibling);
                }
            })
            .catch(error => console.error("Error fetching customer name:", error));
        }

        customerResultsContainer.addEventListener("click", function (event) {
            const clickedElement = event.target.closest("li");

            if (!clickedElement) return;

            const customerId = clickedElement.getAttribute("data-customer-id");
            const customerName = clickedElement.querySelector("strong").textContent;

            console.log(`Selected Customer: ${customerName} (ID: ${customerId})`);
            
            // Store the customer ID for later use
            selectedCustomerId = customerId;

            if (customerId) {
                loadCustomerDiscounts(customerId);
            }
        });
        
        function loadCustomerDiscounts(customerId) {
            fetch(`/customers/${customerId}/eligible-discounts`, {
                headers: {
                    "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content,
                },
            })
            .then((response) => response.json())
            .then((data) => {
                console.log("Discounts data:", data);

                // Clear current discounts
                usableDiscountsList.innerHTML = "";
                unusableDiscountsList.innerHTML = "";

                // Show sections based on data
                if (data.usable.length > 0 || data.unusable.length > 0) {
                    discountsSection.classList.remove("hidden");
                    noDiscountsMessage.classList.add("hidden");

                    // Usable discounts
                    if (data.usable.length > 0) {
                        usableDiscountsSection.classList.remove("hidden");
                        data.usable.forEach((discount) => {
                            addDiscountCard(usableDiscountsList, discount, true);
                        });
                    } else {
                        usableDiscountsSection.classList.add("hidden");
                    }

                    // Unusable discounts
                    if (data.unusable.length > 0) {
                        unusableDiscountsSection.classList.remove("hidden");
                        data.unusable.forEach((discount) => {
                            addDiscountCard(unusableDiscountsList, discount, false);
                        });
                    } else {
                        unusableDiscountsSection.classList.add("hidden");
                    }
                } else {
                    discountsSection.classList.remove("hidden");
                    noDiscountsMessage.classList.remove("hidden");
                }
            })
            .catch((error) => {
                console.error("Error fetching discounts:", error);
            });
        }

        function addDiscountCard(container, discount, isUsable) {
            const discountCard = document.createElement("div");
            discountCard.classList.add("card", "bg-gray-100", "shadow-md", "p-4");

            const usageInfo = discount.maximum_uses
                ? `${discount.usage_count || 0} / ${discount.maximum_uses}`
                : `${discount.usage_count || 0} / Unlimited`;

            discountCard.innerHTML = `
                <h4 class="text-lg font-semibold">${discount.name}</h4>
                <p class="text-sm text-gray-600">
                    ${discount.type === "percent" ? `${discount.amount}% off` : `$${discount.amount} off`}
                </p>
                ${
                    discount.category_name
                        ? `<p class="text-xs text-gray-500">Category: ${discount.category_name}</p>`
                        : ""
                }
                <p class="text-xs text-gray-500">Uses: ${usageInfo}</p>
                ${
                    isUsable
                        ? `<button class="btn btn-primary btn-sm mark-used" data-discount-id="${discount.id}">
                               Mark As Used
                           </button>`
                        : `<div class="flex flex-col mt-2 gap-2">
                               <p class="text-red-500 text-sm">Already Used</p>
                               ${discount.usage_count > 0 ? 
                                 `<button class="btn btn-warning btn-sm unmark-used" data-discount-id="${discount.id}">
                                     Unmark as Used
                                  </button>` : ''}
                            </div>`
                }
            `;

            container.appendChild(discountCard);

            // Attach event listener after the element is added to the DOM
            if (isUsable) {
                const markUsedBtn = discountCard.querySelector(".mark-used");
                markUsedBtn.addEventListener("click", function() {
                    const discountId = this.getAttribute("data-discount-id");
                    console.log(`Marking discount ${discountId} as used for customer ${selectedCustomerId}`);
                    markDiscountAsUsed(selectedCustomerId, discountId, this);
                });
            } else if (discount.usage_count > 0) {
                const unmarkUsedBtn = discountCard.querySelector(".unmark-used");
                unmarkUsedBtn.addEventListener("click", function() {
                    const discountId = this.getAttribute("data-discount-id");
                    console.log(`Opening confirmation to unmark discount ${discountId} for customer ${selectedCustomerId}`);
                    openCustomConfirmModal(selectedCustomerId, discountId);
                });
            }
        }
        
        // Custom modal handling
        const customConfirmModal = document.getElementById('customConfirmModal');
        const customConfirmBtn = document.getElementById('customConfirmBtn');
        const customCancelBtn = document.getElementById('customCancelBtn');
        
        // Data to store for the unmark action
        let unmarkData = { customerId: null, discountId: null };
        
        function openCustomConfirmModal(customerId, discountId) {
            // Store data for when the confirm button is clicked
            unmarkData.customerId = customerId;
            unmarkData.discountId = discountId;
            
            // Show the modal
            customConfirmModal.classList.remove('hidden');
        }
        
        // Close modal when cancel button is clicked
        customCancelBtn.addEventListener('click', function() {
            customConfirmModal.classList.add('hidden');
        });
        
        // Handle unmark when confirm button is clicked
        customConfirmBtn.addEventListener('click', function() {
            unmarkDiscountAsUsed(unmarkData.customerId, unmarkData.discountId);
            customConfirmModal.classList.add('hidden');
        });
        
        // Click outside to close
        customConfirmModal.addEventListener('click', function(e) {
            if (e.target === customConfirmModal) {
                customConfirmModal.classList.add('hidden');
            }
        });
        
        function unmarkDiscountAsUsed(customerId, discountId) {
            console.log(`Sending request to unmark discount ${discountId} for customer ${customerId}`);
            
            // Add CSRF token to headers
            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            fetch(`/customers/${customerId}/discounts/${discountId}/unuse`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": token,
                    "Accept": "application/json"
                },
                body: JSON.stringify({ _token: token })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("Response:", data);
                if (data.success) {
                    // Show success message
                    const toast = document.getElementById('dynamicToast');
                    const toastMessage = document.getElementById('dynamicToastMessage');
                    
                    toastMessage.textContent = 'Discount usage unmarked successfully!';
                    toast.classList.remove('hidden');
                    toast.classList.add('alert-success');
                    
                    setTimeout(() => {
                        toast.classList.add('hidden');
                    }, 3000);
                    
                    // Reload the page with the customer_id parameter
                    setTimeout(() => {
                        window.location.href = `/discounts/checker?customer_id=${customerId}`;
                    }, 1000);
                } else {
                    alert(data.message || "Failed to unmark discount usage.");
                }
            })
            .catch(error => {
                console.error("Error unmarking discount usage:", error);
                alert("An error occurred. Please try again.");
            });
        }

        function markDiscountAsUsed(customerId, discountId, buttonElement) {
            console.log(`Sending request to mark discount ${discountId} as used for customer ${customerId}`);
            
            // Add CSRF token to headers
            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            fetch(`/customers/${customerId}/discounts/${discountId}/use`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": token,
                    "Accept": "application/json"
                },
                body: JSON.stringify({ _token: token })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("Response:", data);
                if (data.success) {
                    // Show success message
                    const toast = document.getElementById('dynamicToast');
                    const toastMessage = document.getElementById('dynamicToastMessage');
                    
                    toastMessage.textContent = 'Discount marked as used successfully!';
                    toast.classList.remove('hidden');
                    toast.classList.add('alert-success');
                    
                    setTimeout(() => {
                        toast.classList.add('hidden');
                    }, 3000);
                    
                    // Reload the page with the customer_id parameter
                    setTimeout(() => {
                        window.location.href = `/discounts/checker?customer_id=${customerId}`;
                    }, 1000);
                } else {
                    alert(data.message || "Failed to mark discount as used.");
                }
            })
            .catch(error => {
                console.error("Error marking discount as used:", error);
                alert("An error occurred. Please try again.");
            });
        }
    });
</script>

<style>
    /* Animation for modal */
    .transform {
        transition-property: transform, opacity;
        transition-duration: 0.3s;
    }
    
    /* Fade in/out for modal background */
    .transition-opacity {
        transition-property: opacity;
        transition-duration: 0.3s;
    }
</style>
@endpush
    
</x-app-layout>
