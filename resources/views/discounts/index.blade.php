<x-app-layout
    page-title="Discounts"
    page-icon="fa-sharp fa-tags"
    :primary-buttons="[
        [
            'type' => 'create',
            'route' => route('discounts.create'),
            'text' => 'Create Discount'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'Discount Checker',
            'route' => route('discounts.checker'),
            'icon' => 'fa-sharp fa-search',
            'class' => 'btn btn-info btn-sm gap-2'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Discounts', 'icon' => 'fa-tags']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow p-6">
                <div class="mb-4">
                    <h3 class="text-lg font-semibold">All Discounts</h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="table table-zebra w-full">
                        <thead class="bg-primary text-primary-content">
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Scope</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($discounts as $discount)
                                <tr class="hover:bg-base-200">
                                    <td>{{ $discount->name }}</td>
                                    <td>{{ ucfirst($discount->type) }}</td>
                                    <td>{{ $discount->type === 'percent' ? $discount->amount . '%' : '$' . $discount->amount }}</td>
                                    <td>{{ ucfirst($discount->scope) }}</td>
                                    <td>{{ $discount->start_date }}</td>
                                    <td>{{ $discount->end_date ?: 'No Expiration' }}</td>
                                    <td class="flex space-x-2">
                                        <a href="{{ route('discounts.edit', $discount) }}" class="btn btn-info bg-info text-info-content hover:bg-info-focus btn-sm">Edit</a>
                                        <form action="{{ route('discounts.destroy', $discount) }}" method="POST" onsubmit="return confirm('Are you sure?');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-error bg-error text-error-content hover:bg-error-focus btn-sm">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    {{ $discounts->links() }}
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
