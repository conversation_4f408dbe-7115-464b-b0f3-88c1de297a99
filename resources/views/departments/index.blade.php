<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            Departments
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <div class="flex justify-between items-center">
                        <h3 class="card-title text-base-content">Manage Departments</h3>
                        <a href="{{ route('departments.create') }}" 
                            class="btn btn-primary btn-sm">
                            <i class="fas fa-plus mr-2"></i>Create New Department
                        </a>
                    </div>

                    <div class="overflow-x-auto mt-4">
                        <table class="table table-zebra w-full">
                            <thead>
                                <tr>
                                    <th class="bg-base-200">Name</th>
                                    <th class="bg-base-200">Leader</th>
                                    <th class="bg-base-200">Description</th>
                                    <th class="bg-base-200 text-right">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($departments as $department)
                                    <tr class="hover">
                                        <td>{{ $department->name }}</td>
                                        <td>{{ $department->leader->name }}</td>
                                        <td>{{ $department->description }}</td>
                                        <td class="text-right">
                                            <div class="flex justify-end gap-2">
                                                <a href="{{ route('departments.edit', $department) }}" 
                                                   class="btn btn-ghost btn-sm">
                                                    <i class="fa-sharp fa-edit"></i>
                                                </a>
                                                <form action="{{ route('departments.destroy', $department) }}" method="POST" class="inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-ghost btn-sm text-error"
                                                        onclick="return confirm('Are you sure you want to delete this department?');">
                                                        <i class="fa-sharp fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center text-base-content/60">
                                            No departments found.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
