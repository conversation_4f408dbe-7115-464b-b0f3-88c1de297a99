<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            Edit Department "{{ $department->name }}"
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h3 class="card-title text-base-content mb-6">Department Details</h3>

                    <form action="{{ route('departments.update', $department) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Name Field -->
                        <div class="form-control mb-6">
                            <label for="name" class="label">
                                <span class="label-text">Name</span>
                            </label>
                            <input type="text" name="name" id="name" 
                                class="input input-bordered w-full"
                                value="{{ old('name', $department->name) }}" required>
                            @error('name')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                            @enderror
                        </div>

                        <!-- Leader Field -->
                        <div class="form-control mb-6">
                            <label for="user_id" class="label">
                                <span class="label-text">Leader</span>
                            </label>
                            <select name="user_id" id="user_id" class="select select-bordered w-full" required>
                                <option value="">Select a leader</option>
                                @foreach ($users as $user)
                                    <option value="{{ $user->id }}" 
                                        {{ old('user_id', $department->user_id) == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('user_id')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                            @enderror
                        </div>

                        <!-- Description Field -->
                        <div class="form-control mb-6">
                            <label for="description" class="label">
                                <span class="label-text">Description</span>
                            </label>
                            <textarea name="description" id="description" rows="4" 
                                class="textarea textarea-bordered w-full">{{ old('description', $department->description) }}</textarea>
                            @error('description')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end gap-2">
                            <a href="{{ route('departments.index') }}" class="btn btn-ghost">
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fa-sharp fa-save mr-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
