<!-- Pickup Requests Settings Tab -->
<div id="calendar-tab" class="tab-content">
    <h3 class="text-lg font-semibold text-primary mb-6">Pickup Request Settings</h3>

    <!-- Horizontal Sub-tabs for Pickup Settings -->
    <div class="tabs tabs-boxed mb-6">
        <a class="tab tab-active pickup-subtab" data-subtab="basic">
            <i class="fa-sharp fa-calendar mr-2"></i>
            Basic Setup
        </a>
        <a class="tab pickup-subtab" data-subtab="scheduling">
            <i class="fa-sharp fa-clock mr-2"></i>
            Scheduling
        </a>
        <a class="tab pickup-subtab" data-subtab="notifications">
            <i class="fa-sharp fa-bell mr-2"></i>
            Notifications
        </a>
        <a class="tab pickup-subtab" data-subtab="messages">
            <i class="fa-sharp fa-message mr-2"></i>
            Messages
        </a>
        <a class="tab pickup-subtab" data-subtab="items">
            <i class="fa-sharp fa-boxes mr-2"></i>
            Item Types
        </a>
    </div>

    <!-- Basic Setup Tab -->
    <div id="pickup-basic-tab" class="pickup-subtab-content active">
        <div class="card bg-base-100 shadow-sm border border-base-300">
            <div class="card-body">
                <h4 class="card-title text-base mb-4">
                    <i class="fa-sharp fa-calendar text-primary mr-2"></i>
                    Calendar & Location Setup
                </h4>

                <!-- Pickup Calendar -->
                <div class="form-control mb-6">
                    <label for="pickup_calendar_id" class="label">
                        <span class="label-text font-medium">Pickup Calendar</span>
                    </label>
                    <select name="pickup_calendar_id" id="pickup_calendar_id" class="select select-bordered w-full">
                        <option value="">-- Select a pickup calendar --</option>
                        @if($public_calendars->isEmpty())
                            <option value="" disabled>No public calendars available</option>
                        @else
                            @foreach($public_calendars as $calendar)
                                <option value="{{ $calendar->id }}" {{ $pickup_calendar_id == $calendar->id ? 'selected' : '' }}>
                                    {{ $calendar->title }}
                                </option>
                            @endforeach
                        @endif
                    </select>
                    @if($public_calendars->isEmpty())
                        <div class="alert alert-warning mt-2">
                            <i class="fa-sharp fa-circle-info"></i>
                            <span>No public calendars found. Please <a href="{{ route('calendars.create') }}" class="link link-primary">create a public calendar</a> first.</span>
                        </div>
                    @endif
                    <p class="text-sm opacity-70 mt-1">This calendar will be used for customer pickup scheduling. Only public calendars are shown.</p>
                </div>

                <!-- Warehouse Location -->
                <div class="form-control mb-6">
                    <label for="warehouse_location" class="label">
                        <span class="label-text font-medium">Warehouse Location</span>
                    </label>
                    <input type="text" name="warehouse_location" id="warehouse_location"
                        class="input input-bordered w-full"
                        value="{{ $warehouse_location ?? '' }}"
                        placeholder="Enter warehouse address (e.g., 123 Main St, City, State 12345)">
                    <p class="text-sm opacity-70 mt-1">This address will be used to calculate distance and travel time to pickup locations. Enter the full address for best accuracy.</p>
                    <div class="alert alert-info mt-2">
                        <i class="fa-sharp fa-info-circle"></i>
                        <div>
                            <strong>How it works:</strong> When viewing pickup requests, the system will calculate the distance and estimated travel time from this warehouse location to the pickup address using Google Maps.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scheduling Tab -->
    <div id="pickup-scheduling-tab" class="pickup-subtab-content">
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <!-- Availability Windows -->
            <div class="card bg-base-100 shadow-sm border border-base-300">
                <div class="card-body">
                    <h4 class="card-title text-base mb-4">
                        <i class="fa-sharp fa-calendar-days text-primary mr-2"></i>
                        Availability Windows
                    </h4>
                    <p class="text-sm opacity-70 mb-4">Define when pickup scheduling is available. Staff can still create pickups outside these windows, but they will be visually indicated as outside normal hours.</p>

                    <input type="hidden" name="pickup_availability_windows" id="pickup_availability_windows_input"
                        value="{{ json_encode($pickup_availability_windows) }}">

                    <div class="space-y-4">
                        @php
                            $daysOfWeek = [
                                '0' => 'Sunday',
                                '1' => 'Monday',
                                '2' => 'Tuesday',
                                '3' => 'Wednesday',
                                '4' => 'Thursday',
                                '5' => 'Friday',
                                '6' => 'Saturday'
                            ];
                        @endphp

                        @foreach($daysOfWeek as $dayNum => $dayName)
                            <div class="day-availability" data-day="{{ $dayNum }}">
                                <div class="flex items-center justify-between mb-2">
                                    <h5 class="font-medium text-sm">{{ $dayName }}</h5>
                                    <button type="button" class="btn btn-primary btn-xs add-window-btn" data-day="{{ $dayNum }}">
                                        <i class="fa-sharp fa-plus"></i>
                                        Add
                                    </button>
                                </div>
                                <div class="windows-list space-y-2" data-day="{{ $dayNum }}">
                                    @if(isset($pickup_availability_windows[$dayNum]) && is_array($pickup_availability_windows[$dayNum]))
                                        @foreach($pickup_availability_windows[$dayNum] as $index => $window)
                                            <div class="window-item flex items-center gap-2 p-2 bg-base-200 rounded">
                                                <input type="time" class="input input-bordered input-xs window-start"
                                                    value="{{ $window['start'] ?? '' }}" placeholder="Start time">
                                                <span class="text-xs opacity-70">to</span>
                                                <input type="time" class="input input-bordered input-xs window-end"
                                                    value="{{ $window['end'] ?? '' }}" placeholder="End time">
                                                <button type="button" class="btn btn-error btn-xs btn-square remove-window-btn">
                                                    <i class="fa-sharp fa-trash"></i>
                                                </button>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Event & Timing Settings -->
            <div class="space-y-6">
                <!-- Event Settings -->
                <div class="card bg-base-100 shadow-sm border border-base-300">
                    <div class="card-body">
                        <h4 class="card-title text-base mb-4">
                            <i class="fa-sharp fa-cog text-primary mr-2"></i>
                            Event Settings
                        </h4>
                        <p class="text-sm opacity-70 mb-4">Configure default duration and scheduling intervals for pickup events.</p>

                        <div class="grid grid-cols-1 gap-4">
                            <!-- Event Duration -->
                            <div class="form-control">
                                <label for="pickup_event_duration" class="label">
                                    <span class="label-text font-medium">Default Event Duration</span>
                                </label>
                                <select name="pickup_event_duration" id="pickup_event_duration" class="select select-bordered select-sm w-full">
                                    <option value="15" {{ $pickup_event_duration == 15 ? 'selected' : '' }}>15 minutes</option>
                                    <option value="30" {{ $pickup_event_duration == 30 ? 'selected' : '' }}>30 minutes</option>
                                    <option value="45" {{ $pickup_event_duration == 45 ? 'selected' : '' }}>45 minutes</option>
                                    <option value="60" {{ $pickup_event_duration == 60 ? 'selected' : '' }}>1 hour</option>
                                    <option value="90" {{ $pickup_event_duration == 90 ? 'selected' : '' }}>1.5 hours</option>
                                    <option value="120" {{ $pickup_event_duration == 120 ? 'selected' : '' }}>2 hours</option>
                                    <option value="180" {{ $pickup_event_duration == 180 ? 'selected' : '' }}>3 hours</option>
                                    <option value="240" {{ $pickup_event_duration == 240 ? 'selected' : '' }}>4 hours</option>
                                </select>
                                <p class="text-xs opacity-70 mt-1">How long pickup events will run by default.</p>
                            </div>

                            <!-- Scheduling Interval -->
                            <div class="form-control">
                                <label for="pickup_scheduling_interval" class="label">
                                    <span class="label-text font-medium">Scheduling Interval</span>
                                </label>
                                <select name="pickup_scheduling_interval" id="pickup_scheduling_interval" class="select select-bordered select-sm w-full">
                                    <option value="5" {{ $pickup_scheduling_interval == 5 ? 'selected' : '' }}>Every 5 minutes</option>
                                    <option value="10" {{ $pickup_scheduling_interval == 10 ? 'selected' : '' }}>Every 10 minutes</option>
                                    <option value="15" {{ $pickup_scheduling_interval == 15 ? 'selected' : '' }}>Every 15 minutes</option>
                                    <option value="30" {{ $pickup_scheduling_interval == 30 ? 'selected' : '' }}>Every 30 minutes</option>
                                    <option value="60" {{ $pickup_scheduling_interval == 60 ? 'selected' : '' }}>Every hour</option>
                                    <option value="120" {{ $pickup_scheduling_interval == 120 ? 'selected' : '' }}>Every 2 hours</option>
                                </select>
                                <p class="text-xs opacity-70 mt-1">How often pickup slots are available for customer scheduling.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Lead Time Settings -->
                <div class="card bg-base-100 shadow-sm border border-base-300">
                    <div class="card-body">
                        <h4 class="card-title text-base mb-4">
                            <i class="fa-sharp fa-clock text-primary mr-2"></i>
                            Lead Time Requirements
                        </h4>
                        <p class="text-sm opacity-70 mb-4">Configure minimum advance notice required for guest pickup requests.</p>

                        <div class="grid grid-cols-1 gap-4 mb-4">
                            <!-- Lead Time Days -->
                            <div class="form-control">
                                <label for="pickup_lead_time_days" class="label">
                                    <span class="label-text font-medium">Lead Time (Days)</span>
                                </label>
                                <input type="number" name="pickup_lead_time_days" id="pickup_lead_time_days"
                                    class="input input-bordered input-sm w-full"
                                    value="{{ $pickup_lead_time_days ?? 1 }}"
                                    min="0" max="30" required>
                                <p class="text-xs opacity-70 mt-1">Minimum days in advance required.</p>
                            </div>

                            <!-- Lead Time Hours -->
                            <div class="form-control">
                                <label for="pickup_lead_time_hours" class="label">
                                    <span class="label-text font-medium">Lead Time (Hours)</span>
                                </label>
                                <input type="number" name="pickup_lead_time_hours" id="pickup_lead_time_hours"
                                    class="input input-bordered input-sm w-full"
                                    value="{{ $pickup_lead_time_hours ?? 0 }}"
                                    min="0" max="23" required>
                                <p class="text-xs opacity-70 mt-1">Additional hours beyond days.</p>
                            </div>

                            <!-- Business Days Only -->
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-4">
                                    <span class="label-text font-medium">Business Days Only</span>
                                    <input type="hidden" name="pickup_lead_time_business_days_only" value="0">
                                    <input type="checkbox" name="pickup_lead_time_business_days_only" value="1"
                                        class="toggle toggle-primary toggle-sm"
                                        {{ ($pickup_lead_time_business_days_only ?? 1) == '1' ? 'checked' : '' }}>
                                </label>
                                <p class="text-xs opacity-70 mt-1">Count only business days (Mon-Fri) for lead time.</p>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fa-sharp fa-info-circle"></i>
                            <div class="text-sm">
                                <strong>Example:</strong> With 1 day business-days-only lead time, if today is Friday,
                                guests cannot schedule pickups until Tuesday or later.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Buffer Time Settings -->
                <div class="card bg-base-100 shadow-sm border border-base-300">
                    <div class="card-body">
                        <h4 class="card-title text-base mb-4">
                            <i class="fa-sharp fa-stopwatch text-primary mr-2"></i>
                            Buffer Time
                        </h4>
                        <p class="text-sm opacity-70 mb-4">Configure buffer time between pickup events to allow for travel, setup, or cleanup.</p>

                        <div class="grid grid-cols-1 gap-4 mb-4">
                            <!-- Buffer Time Duration -->
                            <div class="form-control">
                                <label for="pickup_buffer_time" class="label">
                                    <span class="label-text font-medium">Buffer Time (Minutes)</span>
                                </label>
                                <select name="pickup_buffer_time" id="pickup_buffer_time" class="select select-bordered select-sm w-full">
                                    <option value="0" {{ ($pickup_buffer_time ?? 0) == 0 ? 'selected' : '' }}>No buffer time</option>
                                    <option value="15" {{ ($pickup_buffer_time ?? 0) == 15 ? 'selected' : '' }}>15 minutes</option>
                                    <option value="30" {{ ($pickup_buffer_time ?? 0) == 30 ? 'selected' : '' }}>30 minutes</option>
                                    <option value="45" {{ ($pickup_buffer_time ?? 0) == 45 ? 'selected' : '' }}>45 minutes</option>
                                    <option value="60" {{ ($pickup_buffer_time ?? 0) == 60 ? 'selected' : '' }}>1 hour</option>
                                    <option value="90" {{ ($pickup_buffer_time ?? 0) == 90 ? 'selected' : '' }}>1.5 hours</option>
                                    <option value="120" {{ ($pickup_buffer_time ?? 0) == 120 ? 'selected' : '' }}>2 hours</option>
                                </select>
                                <p class="text-xs opacity-70 mt-1">Time gap required after each pickup event ends.</p>
                            </div>

                            <!-- Buffer Direction -->
                            <div class="form-control">
                                <label for="pickup_buffer_direction" class="label">
                                    <span class="label-text font-medium">Buffer Direction</span>
                                </label>
                                <select name="pickup_buffer_direction" id="pickup_buffer_direction" class="select select-bordered select-sm w-full">
                                    <option value="after" {{ ($pickup_buffer_direction ?? 'after') == 'after' ? 'selected' : '' }}>After events (recommended)</option>
                                    <option value="before" {{ ($pickup_buffer_direction ?? 'after') == 'before' ? 'selected' : '' }}>Before events</option>
                                    <option value="both" {{ ($pickup_buffer_direction ?? 'after') == 'both' ? 'selected' : '' }}>Before and after events</option>
                                </select>
                                <p class="text-xs opacity-70 mt-1">When to apply the buffer time relative to events.</p>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fa-sharp fa-info-circle"></i>
                            <div class="text-sm">
                                <strong>Example:</strong> With 30-minute buffer time "after events", if a pickup runs 10:00-11:00 AM,
                                the next available slot would be 11:30 AM or later.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications Tab -->
    <div id="pickup-notifications-tab" class="pickup-subtab-content">
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <!-- User Group Notifications -->
            <div class="card bg-base-100 shadow-sm border border-base-300">
                <div class="card-body">
                    <h4 class="card-title text-base mb-4">
                        <i class="fa-sharp fa-users text-primary mr-2"></i>
                        User Group Notifications
                    </h4>
                    <p class="text-sm opacity-70 mb-4">Configure which user groups receive notifications when guests submit pickup requests.</p>

                    <input type="hidden" name="pickup_notification_user_groups" id="pickup_notification_user_groups_input"
                        value="{{ json_encode($pickup_notification_user_groups ?? []) }}">

                    <div class="space-y-3">
                        @if($user_groups->isEmpty())
                            <div class="alert alert-warning">
                                <i class="fa-sharp fa-circle-info"></i>
                                <span>No user groups found. Please <a href="{{ route('user_groups.create') }}" class="link link-primary">create user groups</a> first.</span>
                            </div>
                        @else
                            @foreach($user_groups as $group)
                                <label class="label cursor-pointer justify-start gap-3 p-3 bg-base-200 rounded-lg hover:bg-base-300 transition-colors">
                                    <input type="checkbox"
                                        class="checkbox checkbox-primary checkbox-sm pickup-notification-group"
                                        value="{{ $group->id }}"
                                        data-group-name="{{ $group->name }}"
                                        {{ in_array($group->id, $pickup_notification_user_groups ?? []) ? 'checked' : '' }}>
                                    <div class="flex-1">
                                        <span class="label-text font-medium text-sm">{{ $group->name }}</span>
                                        @if($group->description)
                                            <p class="text-xs opacity-70 mt-1">{{ $group->description }}</p>
                                        @endif
                                        <p class="text-xs opacity-60 mt-1">{{ $group->users_count ?? 0 }} {{ Str::plural('user', $group->users_count ?? 0) }}</p>
                                    </div>
                                </label>
                            @endforeach
                        @endif
                    </div>

                    @if(!$user_groups->isEmpty())
                        <div class="alert alert-info mt-4">
                            <i class="fa-sharp fa-info-circle"></i>
                            <div class="text-sm">
                                <strong>How it works:</strong> When a guest submits a pickup request, all users in the selected groups will receive a notification with details about the new request.
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Email Notifications -->
            <div class="card bg-base-100 shadow-sm border border-base-300">
                <div class="card-body">
                    <h4 class="card-title text-base mb-4">
                        <i class="fa-sharp fa-envelope text-primary mr-2"></i>
                        Email Notifications
                    </h4>
                    <p class="text-sm opacity-70 mb-4">Email addresses that will receive notifications whenever a new pickup is requested or scheduled.</p>

                    <div class="form-control">
                        <label for="pickup_scheduling_notification_emails" class="label">
                            <span class="label-text font-medium">Notification Email Addresses</span>
                        </label>
                        <textarea name="pickup_scheduling_notification_emails"
                            id="pickup_scheduling_notification_emails"
                            class="textarea textarea-bordered w-full h-32"
                            placeholder="Enter email addresses separated by commas or new lines&#10;Example: <EMAIL>, <EMAIL>">{{ $pickup_scheduling_notification_emails ?? '' }}</textarea>
                        <p class="text-xs opacity-70 mt-1">Enter multiple emails separated by commas or on new lines.</p>
                    </div>

                    <div class="alert alert-info mt-4">
                        <i class="fa-sharp fa-info-circle"></i>
                        <div class="text-sm">
                            <strong>When notifications are sent:</strong>
                            <ul class="list-disc list-inside mt-1 text-xs">
                                <li>When a guest submits a new pickup request</li>
                                <li>When staff create a new pickup request</li>
                                <li>When a pickup request is scheduled (converted to calendar event)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages Tab -->
    <div id="pickup-messages-tab" class="pickup-subtab-content">
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <!-- Welcome Message -->
            <div class="card bg-base-100 shadow-sm border border-base-300">
                <div class="card-body">
                    <h4 class="card-title text-base mb-4">
                        <i class="fa-sharp fa-hand-wave text-primary mr-2"></i>
                        Welcome Message
                    </h4>
                    <p class="text-sm opacity-70 mb-4">This message will be displayed between the steps and contact info section on the first page of the pickup request form.</p>

                    <div class="form-control">
                        <label for="pickup_welcome_message" class="label">
                            <span class="label-text font-medium">Welcome Message Content</span>
                        </label>
                        <textarea name="pickup_welcome_message" id="pickup_welcome_message"
                            class="textarea textarea-bordered w-full h-64 jodit-editor"
                            placeholder="Enter your welcome message here...">{{ $pickup_welcome_message ?? '' }}</textarea>
                        <p class="text-xs opacity-70 mt-1">Use the rich text editor to format your welcome message with HTML styling.</p>
                    </div>

                    <div class="alert alert-info mt-4">
                        <i class="fa-sharp fa-info-circle"></i>
                        <div class="text-sm">
                            <strong>Where it appears:</strong> This message will be shown to customers on the pickup request form, helping them understand the process and feel welcomed.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Item Details Message -->
            <div class="card bg-base-100 shadow-sm border border-base-300">
                <div class="card-body">
                    <h4 class="card-title text-base mb-4">
                        <i class="fa-sharp fa-list-check text-primary mr-2"></i>
                        Item Details Message
                    </h4>
                    <p class="text-sm opacity-70 mb-4">This message will be displayed on the item details page of the pickup request form.</p>

                    <div class="form-control">
                        <label for="pickup_item_details_message" class="label">
                            <span class="label-text font-medium">Item Details Message Content</span>
                        </label>
                        <textarea name="pickup_item_details_message" id="pickup_item_details_message"
                            class="textarea textarea-bordered w-full h-64 jodit-editor"
                            placeholder="Enter your item details message here...">{{ $pickup_item_details_message ?? '' }}</textarea>
                        <p class="text-xs opacity-70 mt-1">Use the rich text editor to provide instructions or information about item details.</p>
                    </div>

                    <div class="alert alert-info mt-4">
                        <i class="fa-sharp fa-info-circle"></i>
                        <div class="text-sm">
                            <strong>Where it appears:</strong> This message will be shown to customers when they are entering details about their items for pickup.
                        </div>
                    </div>
                </div>
            </div>

            <!-- First-Time Customer Message -->
            <div class="card bg-base-100 shadow-sm border border-base-300">
                <div class="card-body">
                    <h4 class="card-title text-base mb-4">
                        <i class="fa-sharp fa-user-plus text-primary mr-2"></i>
                        First-Time Customer Message
                    </h4>
                    <p class="text-sm opacity-70 mb-4">This message will be displayed in a modal when first-time customers click "No" to having done pickups before.</p>

                    <div class="form-control">
                        <label for="pickup_first_time_message" class="label">
                            <span class="label-text font-medium">First-Time Customer Message Content</span>
                        </label>
                        <textarea name="pickup_first_time_message" id="pickup_first_time_message"
                            class="textarea textarea-bordered w-full h-64 jodit-editor"
                            placeholder="Enter your first-time customer message here...">{{ $pickup_first_time_message ?? '' }}</textarea>
                        <p class="text-xs opacity-70 mt-1">Use the rich text editor to welcome new customers and explain your pickup process.</p>
                    </div>

                    <div class="alert alert-info mt-4">
                        <i class="fa-sharp fa-info-circle"></i>
                        <div class="text-sm">
                            <strong>Where it appears:</strong> This message will be shown in a modal when customers indicate they haven't done pickups with you before, helping to guide new customers through the process.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Agreement Message -->
            <div class="card bg-base-100 shadow-sm border border-base-300">
                <div class="card-body">
                    <h4 class="card-title text-base mb-4">
                        <i class="fa-sharp fa-handshake text-primary mr-2"></i>
                        Agreement Message
                    </h4>
                    <p class="text-sm opacity-70 mb-4">This message will be displayed on the final agreement step that customers must accept before submitting their pickup request.</p>

                    <div class="form-control">
                        <label for="pickup_agreement_message" class="label">
                            <span class="label-text font-medium">Agreement Message Content</span>
                        </label>
                        <textarea name="pickup_agreement_message" id="pickup_agreement_message"
                            class="textarea textarea-bordered w-full h-64 jodit-editor"
                            placeholder="Enter your agreement message here...">{{ $pickup_agreement_message ?? '' }}</textarea>
                        <p class="text-xs opacity-70 mt-1">Use the rich text editor to create the terms and agreement message that customers must accept.</p>
                    </div>

                    <div class="alert alert-info mt-4">
                        <i class="fa-sharp fa-info-circle"></i>
                        <div class="text-sm">
                            <strong>Where it appears:</strong> This message will be shown on the final step of the pickup request form. Customers must check a box to agree to these terms before they can submit their request.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Item Types Tab -->
    <div id="pickup-items-tab" class="pickup-subtab-content">
        <div class="card bg-base-100 shadow-sm border border-base-300">
            <div class="card-body">
                <div class="flex justify-between items-center mb-4">
                    <div>
                        <h4 class="card-title text-base mb-2">
                            <i class="fa-sharp fa-boxes text-primary mr-2"></i>
                            Pickup Item Types Configuration
                        </h4>
                        <p class="text-sm opacity-70">Configure the item types available for selection in pickup requests. Each item type includes a name, value, icon, and optional notes.</p>
                    </div>
                    <button type="button" id="add-item-type" class="btn btn-primary btn-sm">
                        <i class="fa-sharp fa-plus mr-1"></i>
                        Add Item Type
                    </button>
                </div>

                <input type="hidden" name="pickup_item_types" id="pickup_item_types_input" value="{{ json_encode($pickup_item_types ?? []) }}">

                <div id="item-types-container" class="space-y-4 mb-6">
                    <!-- Item types will be populated by JavaScript -->
                </div>

                <div class="alert alert-info">
                    <i class="fa-sharp fa-info-circle"></i>
                    <div class="text-sm">
                        <strong>How it works:</strong>
                        <ul class="list-disc list-inside mt-1 text-xs">
                            <li><strong>Name:</strong> Display name shown to users</li>
                            <li><strong>Value:</strong> Internal identifier (lowercase, underscores only)</li>
                            <li><strong>Icon:</strong> FontAwesome Sharp icon class (e.g., fa-sharp fa-laptop)</li>
                            <li><strong>Notes:</strong> Additional information shown when item is selected</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Jodit Editor CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jodit/4.2.47/es2015/jodit.fat.min.css" integrity="sha512-2cfnJ8ZMBqkaNXsi/6ucIcFRvKVFKW69HEP5S7L2fQtAaPrVg5XLkkUgl46kkaN5PPArXwLPCOqhbsAAiHQiXA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<style>
    .pickup-subtab-content {
        display: none;
    }
    .pickup-subtab-content.active {
        display: block;
    }

    /* Jodit editor styling */
    .jodit-container {
        border-radius: 0.5rem;
        border: 1px solid hsl(var(--bc) / 0.2);
    }

    .jodit-toolbar {
        border-radius: 0.5rem 0.5rem 0 0;
        background: hsl(var(--b2));
    }

    .jodit-workplace {
        border-radius: 0 0 0.5rem 0.5rem;
    }
</style>

<!-- Jodit Editor JavaScript -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jodit/4.2.47/es2018/jodit.fat.min.js" integrity="sha512-CMQqfQ74mIbWy6wovENywkFVRsYhKiY3FVBMojkvE2xzGcrZodnEr4RqiJgNmpZoA83E9l5peJONaIp2V2BV7w==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Pickup subtab functionality
    const pickupSubtabs = document.querySelectorAll('.pickup-subtab');
    const pickupSubtabContents = document.querySelectorAll('.pickup-subtab-content');

    function switchPickupSubtab(targetSubtab) {
        // Remove active class from all subtabs
        pickupSubtabs.forEach(tab => {
            tab.classList.remove('tab-active');
        });

        // Hide all subtab contents
        pickupSubtabContents.forEach(content => {
            content.classList.remove('active');
        });

        // Activate the clicked subtab
        const clickedTab = document.querySelector(`[data-subtab="${targetSubtab}"]`);
        if (clickedTab) {
            clickedTab.classList.add('tab-active');
        }

        // Show the corresponding content
        const targetContent = document.getElementById(`pickup-${targetSubtab}-tab`);
        if (targetContent) {
            targetContent.classList.add('active');
        }
    }

    // Add click event listeners to pickup subtabs
    pickupSubtabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            const targetSubtab = this.getAttribute('data-subtab');
            switchPickupSubtab(targetSubtab);
        });
    });

    // Initialize with the first subtab active
    switchPickupSubtab('basic');

    // Initialize Jodit editors
    const joditEditors = document.querySelectorAll('.jodit-editor');
    joditEditors.forEach(function(textarea) {
        if (typeof Jodit !== 'undefined') {
            const editor = Jodit.make(textarea, {
                height: 300,
                toolbarSticky: false,
                showCharsCounter: false,
                showWordsCounter: false,
                showXPathInStatusbar: false,
                buttons: [
                    'bold', 'italic', 'underline', '|',
                    'ul', 'ol', '|',
                    'font', 'fontsize', '|',
                    'paragraph', '|',
                    'link', '|',
                    'align', '|',
                    'undo', 'redo', '|',
                    'hr', '|',
                    'fullsize', 'source'
                ],
                events: {
                    afterInit: function (editorInstance) {
                        // Add the prose class to the editor content area for better styling
                        const editorArea = editorInstance.editor;
                        if (editorArea) {
                            editorArea.classList.add('prose', 'prose-sm', 'max-w-none');
                        }
                    },
                },
            });
        }
    });
});
</script>
