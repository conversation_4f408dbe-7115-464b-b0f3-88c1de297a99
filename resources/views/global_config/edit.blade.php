<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            Global Configuration
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title text-2xl text-primary mb-6">Global Configuration</h2>

                    <form action="{{ route('globalconfig.update') }}" method="POST" id="global-config-form">
                        @csrf
                        @method('PUT')
                        <input type="hidden" name="current_tab" id="current_tab" value="{{ session('current_tab', 'general') }}">

                        <!-- Vertical Menu Navigation -->
                        <div class="flex flex-col lg:flex-row gap-6">
                            <!-- Menu Navigation -->
                            <div class="lg:w-1/4">
                                <ul class="menu bg-base-200 rounded-box w-full">
                                    <li>
                                        <a class="menu-item config-tab active bg-primary text-primary-content" data-tab="general">
                                            <i class="fa-sharp fa-solid fa-cog h-5 w-5"></i>
                                            General Settings
                                        </a>
                                    </li>
                                    <li>
                                        <a class="menu-item config-tab" data-tab="timeclock">
                                            <i class="fa-sharp fa-solid fa-clock h-5 w-5"></i>
                                            Time Clock
                                        </a>
                                    </li>
                                    <li>
                                        <a class="menu-item config-tab" data-tab="calendar">
                                            <i class="fa-sharp fa-solid fa-truck-pickup h-5 w-5"></i>
                                            Pickup Requests
                                        </a>
                                    </li>
                                    <li>
                                        <a class="menu-item config-tab" data-tab="invoice">
                                            <i class="fa-sharp fa-solid fa-file-invoice h-5 w-5"></i>
                                            Invoice Settings
                                        </a>
                                    </li>
                                    <li>
                                        <a class="menu-item config-tab" data-tab="payout">
                                            <i class="fa-sharp fa-solid fa-money-bill h-5 w-5"></i>
                                            Payout Settings
                                        </a>
                                    </li>
                                    <li>
                                        <a class="menu-item config-tab" data-tab="contact">
                                            <i class="fa-sharp fa-solid fa-address-book h-5 w-5"></i>
                                            Contact Settings
                                        </a>
                                    </li>
                                    <li>
                                        <a class="menu-item config-tab" data-tab="features">
                                            <i class="fa-sharp fa-solid fa-toggle-on h-5 w-5"></i>
                                            Feature Toggles
                                        </a>
                                    </li>
                                </ul>
                            </div>

                            <!-- Content Area -->
                            <div class="lg:w-3/4">
                                <!-- General Settings Tab -->
                                <div id="general-tab" class="tab-content active">

                                    <h3 class="text-lg font-semibold text-primary mb-4">General Settings</h3>

                                    <!-- App Name -->
                                    <div class="form-control mb-6">
                                        <label for="app_name" class="label">
                                            <span class="label-text font-medium">App Name</span>
                                        </label>
                                        <input type="text" name="app_name" id="app_name" class="input input-bordered w-full"
                                            value="{{ $app_name }}" required>
                                    </div>

                                    <!-- Site Logos -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                        <!-- Site Logo Square -->
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Site Logo (Square)</span>
                                            </label>
                                            <div class="card bg-base-200 border border-base-300">
                                                <div class="card-body p-4">
                                                    @if(isset($site_logo_square) && $site_logo_square)
                                                        <div class="flex flex-col items-center mb-4">
                                                            <img src="{{ $site_logo_square }}" alt="Site Logo Square" class="w-24 h-24 object-contain rounded-lg border border-base-300 mb-2">
                                                            <span class="text-sm text-base-content/70">Current Logo</span>
                                                        </div>
                                                    @else
                                                        <div class="flex flex-col items-center mb-4">
                                                            <div class="w-24 h-24 bg-base-300 rounded-lg flex items-center justify-center mb-2">
                                                                <i class="fa-sharp fa-image text-2xl text-base-content/50"></i>
                                                            </div>
                                                            <span class="text-sm text-base-content/70">No logo uploaded</span>
                                                        </div>
                                                    @endif
                                                    <button type="button" class="btn btn-primary btn-sm w-full" onclick="openLogoModal('square')">
                                                        <i class="fa-sharp fa-upload mr-2"></i>
                                                        {{ isset($site_logo_square) && $site_logo_square ? 'Change Logo' : 'Upload Logo' }}
                                                    </button>
                                                </div>
                                            </div>
                                            <p class="text-sm opacity-70 mt-1">Square logo for app icons and compact displays. Recommended size: 512x512px.</p>
                                        </div>

                                        <!-- Site Logo Wide -->
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Site Logo (Wide)</span>
                                            </label>
                                            <div class="card bg-base-200 border border-base-300">
                                                <div class="card-body p-4">
                                                    @if(isset($site_logo_wide) && $site_logo_wide)
                                                        <div class="flex flex-col items-center mb-4">
                                                            <img src="{{ $site_logo_wide }}" alt="Site Logo Wide" class="max-w-full h-16 object-contain rounded-lg border border-base-300 mb-2">
                                                            <span class="text-sm text-base-content/70">Current Logo</span>
                                                        </div>
                                                    @else
                                                        <div class="flex flex-col items-center mb-4">
                                                            <div class="w-32 h-16 bg-base-300 rounded-lg flex items-center justify-center mb-2">
                                                                <i class="fa-sharp fa-image text-2xl text-base-content/50"></i>
                                                            </div>
                                                            <span class="text-sm text-base-content/70">No logo uploaded</span>
                                                        </div>
                                                    @endif
                                                    <button type="button" class="btn btn-primary btn-sm w-full" onclick="openLogoModal('wide')">
                                                        <i class="fa-sharp fa-upload mr-2"></i>
                                                        {{ isset($site_logo_wide) && $site_logo_wide ? 'Change Logo' : 'Upload Logo' }}
                                                    </button>
                                                </div>
                                            </div>
                                            <p class="text-sm opacity-70 mt-1">Wide logo with text for headers and branding. Recommended size: 1024x256px.</p>
                                        </div>
                                    </div>

                                    <!-- General Customer -->
                                    <div class="form-control mb-6">
                                        <label for="general_customer_id" class="label">
                                            <span class="label-text font-medium">General Customer</span>
                                        </label>
                                        <x-dynamic-customer-search
                                            id="general_customer_search"
                                            name="general_customer_id"
                                            :placeholder="'Search for a customer...'"
                                            :action="'form'"
                                            :selectedId="$general_customer_id"
                                            :selectedName="optional(\App\Models\Customer::find($general_customer_id))->name" />
                                        <p class="text-sm opacity-70 mt-1">Default customer for general transactions and operations.</p>
                                    </div>
                                </div>

                                <!-- Time Clock Settings Tab -->
                                <div id="timeclock-tab" class="tab-content">
                                    <h3 class="text-lg font-semibold text-primary mb-4">Time Clock Settings</h3>

                                    <!-- Minimum Break Duration -->
                                    <div class="form-control mb-6">
                                        <label for="min_break_duration" class="label">
                                            <span class="label-text font-medium">Minimum Break Duration (minutes)</span>
                                        </label>
                                        <input type="number" name="min_break_duration" id="min_break_duration" class="input input-bordered w-full"
                                            value="{{ $min_break_duration }}" required min="0">
                                        <p class="text-sm opacity-70 mt-1">Minimum duration for breaks in minutes. Used for time clock functionality.</p>
                                    </div>

                                    <!-- Daily Timeclock Report Settings -->
                                    <div class="form-control mb-6">
                                        <label class="label cursor-pointer justify-start gap-4">
                                            <span class="label-text font-medium">Enable Daily Timeclock Report</span>
                                            <input type="hidden" name="timeclock_daily_report_enabled" value="0">
                                            <input type="checkbox" name="timeclock_daily_report_enabled" value="1" class="toggle toggle-primary"
                                                {{ isset($timeclock_daily_report_enabled) && $timeclock_daily_report_enabled == '1' ? 'checked' : '' }}
                                                id="timeclock_daily_report_toggle">
                                        </label>
                                        <p class="text-sm opacity-70 mt-1">When enabled, a daily report of timeclock data will be sent to the specified email address.</p>
                                    </div>

                                    <div id="timeclock_report_settings" class="pl-6 border-l-2 border-base-300 mb-6 {{ isset($timeclock_daily_report_enabled) && $timeclock_daily_report_enabled == '1' ? '' : 'hidden' }}">
                                        <!-- Report Email -->
                                        <div class="form-control mb-4">
                                            <label for="timeclock_report_email" class="label">
                                                <span class="label-text font-medium">Report Recipient Email</span>
                                            </label>
                                            <input type="email" name="timeclock_report_email" id="timeclock_report_email"
                                                class="input input-bordered w-full"
                                                value="{{ $timeclock_report_email ?? '' }}"
                                                {{ isset($timeclock_daily_report_enabled) && $timeclock_daily_report_enabled == '1' ? 'required' : '' }}>
                                            <p class="text-sm opacity-70 mt-1">Email address where the daily timeclock report will be sent.</p>
                                        </div>

                                        <!-- Report Time -->
                                        <div class="form-control mb-4">
                                            <label for="timeclock_report_time" class="label">
                                                <span class="label-text font-medium">Report Send Time</span>
                                            </label>
                                            <input type="time" name="timeclock_report_time" id="timeclock_report_time"
                                                class="input input-bordered w-full"
                                                value="{{ $timeclock_report_time ?? '07:00' }}"
                                                {{ isset($timeclock_daily_report_enabled) && $timeclock_daily_report_enabled == '1' ? 'required' : '' }}>
                                            <p class="text-sm opacity-70 mt-1">Time of day when the report will be sent (24-hour format).</p>
                                        </div>
                                    </div>

                                    <!-- Auto Clock-Out Settings -->
                                    <div class="form-control mb-6">
                                        <label class="label cursor-pointer justify-start gap-4">
                                            <span class="label-text font-medium">Enable Automatic Clock-Out</span>
                                            <input type="hidden" name="timeclock_auto_clock_out_enabled" value="0">
                                            <input type="checkbox" name="timeclock_auto_clock_out_enabled" value="1" class="toggle toggle-primary"
                                                {{ isset($timeclock_auto_clock_out_enabled) && $timeclock_auto_clock_out_enabled == '1' ? 'checked' : '' }}
                                                id="timeclock_auto_clock_out_toggle">
                                        </label>
                                        <p class="text-sm opacity-70 mt-1">When enabled, users will be automatically clocked out at the specified time if they forget to clock out manually.</p>
                                    </div>

                                    <div id="timeclock_auto_clock_out_settings" class="pl-6 border-l-2 border-base-300 mb-6 {{ isset($timeclock_auto_clock_out_enabled) && $timeclock_auto_clock_out_enabled == '1' ? '' : 'hidden' }}">
                                        <!-- Auto Clock-Out Time -->
                                        <div class="form-control mb-4">
                                            <label for="timeclock_auto_clock_out_time" class="label">
                                                <span class="label-text font-medium">Auto Clock-Out Time</span>
                                            </label>
                                            <input type="time" name="timeclock_auto_clock_out_time" id="timeclock_auto_clock_out_time"
                                                class="input input-bordered w-full"
                                                value="{{ $timeclock_auto_clock_out_time ?? '22:00' }}"
                                                {{ isset($timeclock_auto_clock_out_enabled) && $timeclock_auto_clock_out_enabled == '1' ? 'required' : '' }}>
                                            <p class="text-sm opacity-70 mt-1">Time when users will be automatically clocked out (24-hour format). Default is 10:00 PM (22:00).</p>
                                        </div>

                                        <div class="alert alert-info">
                                            <i class="fa-sharp fa-info-circle"></i>
                                            <div>
                                                <strong>How it works:</strong> Users who are still clocked in or on break at this time will be automatically clocked out.
                                                A warning message will be displayed to inform them of the automatic clock-out and advise them to contact management if corrections are needed.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                @include('global_config.partials.pickup-settings')

                                <!-- Invoice Settings Tab -->
                                <div id="invoice-tab" class="tab-content">
                                    <h3 class="text-lg font-semibold text-primary mb-4">Invoice Settings</h3>

                                    <!-- Active Customer Contract Discount -->
                                    <div class="form-control mb-6">
                                        <label for="active_customer_contract_id" class="label">
                                            <span class="label-text font-medium">Active Customer Contract Discount</span>
                                            <span class="label-text-alt opacity-70">(Customer-specific discounts only)</span>
                                        </label>
                                        <select name="active_customer_contract_id" id="active_customer_contract_id" class="select select-bordered w-full">
                                            <option value="">-- Select a discount --</option>
                                            @if($active_discounts->isEmpty())
                                                <option value="" disabled>No discounts available</option>
                                            @else
                                                @foreach($active_discounts as $discount)
                                                    <option value="{{ $discount->id }}" {{ $active_customer_contract_id == $discount->id ? 'selected' : '' }}>
                                                        {{ $discount->name }} ({{ $discount->type == 'percent' ? $discount->amount . '%' : '$' . number_format($discount->amount, 2) }})
                                                    </option>
                                                @endforeach
                                            @endif
                                        </select>

                                        @if($active_discounts->isEmpty())
                                            <div class="alert alert-warning mt-2">
                                                <i class="fa-sharp fa-circle-info"></i>
                                                <span>No customer-specific discounts found. Please <a href="{{ route('discounts.create') }}" class="link link-primary">create a customer-specific discount</a> first.</span>
                                            </div>
                                        @endif
                                        <p class="text-sm opacity-70 mt-1">This discount will be available to apply to customer accounts as an "Active Contract" discount. Only customer-specific discounts are shown.</p>
                                    </div>

                                    <!-- Popular Inventory Items -->
                                    <div class="form-control mb-6">
                                        <label for="popular_items" class="label">
                                            <span class="label-text font-medium">Popular Inventory Items</span>
                                        </label>
                                        <p class="text-sm opacity-70 mb-2">These items will be available to add to invoices via the quick-add menu.</p>
                                        <input type="hidden" name="invoices_popular_inventory_items" id="invoices_popular_inventory_items"
                                            value="{{ json_encode($popular_items->pluck('id')->toArray()) }}">

                                        <div class="card bg-base-100 bordered">
                                            <div class="card-body p-4">
                                                <ul id="popular-items-list" class="space-y-2">
                                                    @foreach ($popular_items as $item)
                                                        <li data-id="{{ $item->id }}" class="flex justify-between items-center p-2 bg-base-200 rounded-md">
                                                            <span>{{ $item->name }}</span>
                                                            <button type="button" class="btn btn-error btn-sm btn-square" onclick="removePopularItem({{ $item->id }})">
                                                                <i class="fa-sharp fa-trash"></i>
                                                            </button>
                                                        </li>
                                                    @endforeach
                                                </ul>

                                                <div class="form-control mt-4">
                                                    <label for="item_search" class="label">
                                                        <span class="label-text">Add Popular Item</span>
                                                    </label>
                                                    <div class="relative">
                                                        <input type="text" id="item_search" class="input input-bordered w-full" placeholder="Search item by name or asset tag...">
                                                        <ul id="item_search_results" class="menu bg-base-200 w-full mt-1 rounded-box absolute z-10 hidden"></ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Payout Settings Tab -->
                                <div id="payout-tab" class="tab-content">
                                    <h3 class="text-lg font-semibold text-primary mb-4">Payout Settings</h3>

                                    <!-- Payout Products -->
                                    <div class="form-control mb-6">
                                        <label class="label">
                                            <span class="label-text font-medium">Payout Products</span>
                                        </label>
                                        <p class="text-sm opacity-70 mb-2">Define products that your business buys from customers. These will appear in the simplified payout form.</p>
                                        <input type="hidden" name="payout_products" id="payout_products_input"
                                            value="{{ isset($payout_products) ? json_encode($payout_products->pluck('id')->toArray()) : '[]' }}">

                                        <div class="card bg-base-100 bordered">
                                            <div class="card-body p-4">
                                                <ul id="payout-products-list" class="space-y-2">
                                                    @if(isset($payout_products) && count($payout_products) > 0)
                                                        @foreach($payout_products as $product)
                                                            <li data-id="{{ $product->id }}" class="flex justify-between items-center p-2 bg-base-200 rounded-md">
                                                                <span>{{ $product->name }}</span>
                                                                <button type="button" class="btn btn-error btn-sm btn-square" onclick="removePayoutProduct('{{ $product->id }}')">
                                                                    <i class="fa-sharp fa-trash"></i>
                                                                </button>
                                                            </li>
                                                        @endforeach
                                                    @endif
                                                </ul>

                                                <div class="form-control mt-4">
                                                    <label for="payout_item_search" class="label">
                                                        <span class="label-text">Add Payout Product</span>
                                                    </label>
                                                    <div class="relative">
                                                        <input type="text" id="payout_item_search" class="input input-bordered w-full" placeholder="Search inventory by name or asset tag...">
                                                        <ul id="payout_item_search_results" class="menu bg-base-200 w-full mt-1 rounded-box absolute z-10 hidden"></ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <!-- Contact Settings Tab -->
                                <div id="contact-tab" class="tab-content">
                                    <h3 class="text-lg font-semibold text-primary mb-4">Contact Settings</h3>
                                    <p class="text-sm opacity-70 mb-6">Configure business contact information displayed on invoices and customer communications.</p>

                                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                                        <!-- Site Email -->
                                        <div class="form-control">
                                            <label for="site_email" class="label">
                                                <span class="label-text font-medium">Business Email</span>
                                            </label>
                                            <input type="email" name="site_email" id="site_email"
                                                class="input input-bordered w-full"
                                                value="{{ $site_email ?? '' }}"
                                                placeholder="<EMAIL>">
                                            <p class="text-sm opacity-70 mt-1">Primary business email address for customer communications.</p>
                                        </div>

                                        <!-- Site Phone -->
                                        <div class="form-control">
                                            <label for="site_phone" class="label">
                                                <span class="label-text font-medium">Business Phone</span>
                                            </label>
                                            <input type="text" name="site_phone" id="site_phone"
                                                class="input input-bordered w-full"
                                                value="{{ $site_phone ?? '' }}"
                                                placeholder="(*************">
                                            <p class="text-sm opacity-70 mt-1">Primary business phone number for customer support.</p>
                                        </div>

                                        <!-- Site Website -->
                                        <div class="form-control">
                                            <label for="site_website" class="label">
                                                <span class="label-text font-medium">Business Website</span>
                                            </label>
                                            <input type="url" name="site_website" id="site_website"
                                                class="input input-bordered w-full"
                                                value="{{ $site_website ?? '' }}"
                                                placeholder="https://company.com">
                                            <p class="text-sm opacity-70 mt-1">Company website URL.</p>
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="fa-sharp fa-info-circle"></i>
                                        <div>
                                            <strong>Usage:</strong> This contact information will be displayed on generated invoices and other customer-facing documents. Only fields with values will be shown.
                                        </div>
                                    </div>
                                </div>

                                <!-- Feature Toggles Tab -->
                                <div id="features-tab" class="tab-content">
                                    <h3 class="text-lg font-semibold text-primary mb-4">Feature Toggles</h3>

                                    <div class="form-control mb-6">
                                        <label class="label cursor-pointer justify-start gap-4">
                                            <span class="label-text font-medium">Enable Tasks Feature</span>
                                            <input type="hidden" name="tasks_enabled" value="0">
                                            <input type="checkbox" name="tasks_enabled" value="1" class="toggle toggle-primary" {{ $tasks_enabled == '1' ? 'checked' : '' }}>
                                        </label>
                                        <p class="text-sm opacity-70 mt-1">When enabled, the tasks feature will be available in the application.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Save Button -->
                        <div class="mt-8">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fa-sharp fa-solid fa-save mr-2"></i>
                                Save Configuration
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Logo Upload Modals -->
    <!-- Square Logo Modal -->
    <div id="square-logo-modal" class="modal">
        <div class="modal-box max-w-2xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-bold text-lg text-primary">
                    <i class="fa-sharp fa-upload mr-2"></i>
                    Upload Square Logo
                </h3>
                <button type="button" class="btn btn-sm btn-circle btn-ghost modal-close">
                    <i class="fa-sharp fa-times"></i>
                </button>
            </div>
            
            <div class="mb-4">
                <p class="text-sm text-base-content/70">Upload a square logo for app icons and compact displays. Recommended size: 512x512px.</p>
            </div>
            
            <x-image-dropzone
                id="square-logo-dropzone"
                name="square_logo"
                :max-files="1"
                :max-filesize="5"
                :max-width="1024"
                :max-height="1024"
                :client-resize="true"
                accepted-formats="image/jpeg,image/jpg,image/png,image/webp"
                :session-based="false"
                :multiple="false"
                label=""
                help-text="Drop your square logo here or click to upload"
                sub-text="JPG, PNG, or WebP format. Will be resized to optimal dimensions."
            />
            
            <div class="modal-action">
                <button type="button" class="btn btn-ghost modal-close">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="uploadLogo('square')">
                    <i class="fa-sharp fa-save mr-2"></i>
                    Save Logo
                </button>
            </div>
        </div>
        <div class="modal-backdrop"></div>
    </div>

    <!-- Wide Logo Modal -->
    <div id="wide-logo-modal" class="modal">
        <div class="modal-box max-w-2xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-bold text-lg text-primary">
                    <i class="fa-sharp fa-upload mr-2"></i>
                    Upload Wide Logo
                </h3>
                <button type="button" class="btn btn-sm btn-circle btn-ghost modal-close">
                    <i class="fa-sharp fa-times"></i>
                </button>
            </div>
            
            <div class="mb-4">
                <p class="text-sm text-base-content/70">Upload a wide logo with text for headers and branding. Recommended size: 1024x256px.</p>
            </div>
            
            <x-image-dropzone
                id="wide-logo-dropzone"
                name="wide_logo"
                :max-files="1"
                :max-filesize="5"
                :max-width="2048"
                :max-height="512"
                :client-resize="true"
                accepted-formats="image/jpeg,image/jpg,image/png,image/webp"
                :session-based="false"
                :multiple="false"
                label=""
                help-text="Drop your wide logo here or click to upload"
                sub-text="JPG, PNG, or WebP format. Will be resized to optimal dimensions."
            />
            
            <div class="modal-action">
                <button type="button" class="btn btn-ghost modal-close">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="uploadLogo('wide')">
                    <i class="fa-sharp fa-save mr-2"></i>
                    Save Logo
                </button>
            </div>
        </div>
        <div class="modal-backdrop"></div>
    </div>

    @push('styles')
        <style>
            .tab-content {
                display: none;
            }
            .tab-content.active {
                display: block;
            }
            .menu-item config-tab.active {
                background-color: var(--fallback-p,oklch(var(--p)/1));
                color: var(--fallback-pc,oklch(var(--pc)/1));
            }
            .menu-item config-tab:hover {
                background-color: var(--fallback-b3,oklch(var(--b3)/1));
            }
            .menu-item config-tab.active:hover {
                background-color: var(--fallback-p,oklch(var(--p)/1));
                color: var(--fallback-pc,oklch(var(--pc)/1));
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                // Track form changes
                let formChanged = false;
                const form = document.getElementById('global-config-form');
                const currentTabInput = document.getElementById('current_tab');
                
                // Track initial form state
                const initialFormData = new FormData(form);
                
                // Menu functionality
                const configTabs = document.querySelectorAll('.config-tab');
                const tabContents = document.querySelectorAll('.tab-content');

                // Function to switch tab
                function switchTab(targetTab) {
                    configTabs.forEach(item => {
                        item.classList.remove('bg-primary', 'text-primary-content');
                        if (item.getAttribute('data-tab') === targetTab) {
                            item.classList.add('bg-primary', 'text-primary-content');
                        }
                    });
                    
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });
                    
                    const targetContent = document.getElementById(targetTab + '-tab');
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }
                    
                    // Update hidden input
                    currentTabInput.value = targetTab;
                }

                // Restore tab from session
                const savedTab = currentTabInput.value || 'general';
                switchTab(savedTab);

                configTabs.forEach(tabItem => {
                    tabItem.addEventListener('click', function(e) {
                        e.preventDefault();
                        const targetTab = this.getAttribute('data-tab');
                        switchTab(targetTab);
                    });
                });
                
                // Track form changes
                form.addEventListener('change', function() {
                    formChanged = true;
                });
                
                form.addEventListener('input', function() {
                    formChanged = true;
                });
                
                // Update current tab before form submission
                form.addEventListener('submit', function() {
                    formChanged = false; // Reset flag on submit
                });
                
                // Warn about unsaved changes
                window.addEventListener('beforeunload', function(e) {
                    if (formChanged) {
                        e.preventDefault();
                        e.returnValue = 'You have unsaved changes. Are you sure you want to leave this page?';
                        return e.returnValue;
                    }
                });
                
                // Handle navigation clicks
                document.querySelectorAll('a').forEach(link => {
                    link.addEventListener('click', function(e) {
                        if (formChanged && !this.hasAttribute('data-ignore-changes')) {
                            if (!confirm('You have unsaved changes. Are you sure you want to leave this page?')) {
                                e.preventDefault();
                            }
                        }
                    });
                });

                const popularItemsInput = document.getElementById('invoices_popular_inventory_items');
                const popularItemsList = document.getElementById('popular-items-list');
                const itemSearchInput = document.getElementById('item_search');
                const itemSearchResults = document.getElementById('item_search_results');
                const payoutProductsInput = document.getElementById('payout_products_input');
                const payoutProductsList = document.getElementById('payout-products-list');
                const payoutItemSearchInput = document.getElementById('payout_item_search');
                const payoutItemSearchResults = document.getElementById('payout_item_search_results');
                const availabilityWindowsInput = document.getElementById('pickup_availability_windows_input');

                // Timeclock report toggle functionality
                const timeclockReportToggle = document.getElementById('timeclock_daily_report_toggle');
                const timeclockReportSettings = document.getElementById('timeclock_report_settings');
                const timeclockReportEmail = document.getElementById('timeclock_report_email');
                const timeclockReportTime = document.getElementById('timeclock_report_time');

                if (timeclockReportToggle) {
                    timeclockReportToggle.addEventListener('change', function() {
                        if (this.checked) {
                            timeclockReportSettings.classList.remove('hidden');
                            timeclockReportEmail.setAttribute('required', 'required');
                            timeclockReportTime.setAttribute('required', 'required');
                        } else {
                            timeclockReportSettings.classList.add('hidden');
                            timeclockReportEmail.removeAttribute('required');
                            timeclockReportTime.removeAttribute('required');
                        }
                    });
                }

                // Auto clock-out toggle functionality
                const autoClockOutToggle = document.getElementById('timeclock_auto_clock_out_toggle');
                const autoClockOutSettings = document.getElementById('timeclock_auto_clock_out_settings');
                const autoClockOutTime = document.getElementById('timeclock_auto_clock_out_time');

                if (autoClockOutToggle) {
                    autoClockOutToggle.addEventListener('change', function() {
                        if (this.checked) {
                            autoClockOutSettings.classList.remove('hidden');
                            autoClockOutTime.setAttribute('required', 'required');
                        } else {
                            autoClockOutSettings.classList.add('hidden');
                            autoClockOutTime.removeAttribute('required');
                        }
                    });
                }

                let selectedItems = JSON.parse(popularItemsInput.value) || [];
                let payoutProducts = JSON.parse(payoutProductsInput.value) || [];

                // Payout Products Management is now handled through the search functionality
                // Similar to popular inventory items

                // Function to add a popular item
                function addPopularItem(item) {
                    if (selectedItems.includes(item.id)) {
                        alert('Item is already in the popular list.');
                        return;
                    }

                    // Add item to the list
                    selectedItems.push(item.id);
                    popularItemsInput.value = JSON.stringify(selectedItems);

                    // Update the UI
                    const listItem = document.createElement('li');
                    listItem.className = 'flex justify-between items-center p-2 bg-base-200 rounded-md';
                    listItem.dataset.id = item.id;
                    listItem.innerHTML = `
                        <span>${item.name}</span>
                        <button type="button" class="btn btn-error btn-sm btn-square" onclick="removePopularItem(${item.id})">
                            <i class="fa-sharp fa-trash"></i>
                        </button>
                    `;
                    popularItemsList.appendChild(listItem);
                }

                // Function to remove a popular item
                window.removePopularItem = function (id) {
                    selectedItems = selectedItems.filter(itemId => itemId !== id);
                    popularItemsInput.value = JSON.stringify(selectedItems);

                    const listItem = popularItemsList.querySelector(`li[data-id="${id}"]`);
                    if (listItem) listItem.remove();
                };

                // Search inventory items
                itemSearchInput.addEventListener('input', function () {
                    const query = itemSearchInput.value.trim();

                    if (!query) {
                        itemSearchResults.classList.add('hidden');
                        itemSearchResults.innerHTML = '';
                        return;
                    }

                    fetch(`/inventory/search?query=${encodeURIComponent(query)}&active=true`, {
                        method: 'GET',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'Accept': 'application/json',
                        },
                    })
                        .then(response => response.json())
                        .then(items => {
                            itemSearchResults.innerHTML = '';
                            if (items.length > 0) {
                                items.forEach(item => {
                                    const listItem = document.createElement('li');
                                    listItem.className = 'p-2 cursor-pointer hover:bg-base-300';
                                    listItem.textContent = `${item.name} (${item.asset_tag}) - ${item.location}`;
                                    listItem.addEventListener('click', function () {
                                        addPopularItem(item);
                                        itemSearchResults.classList.add('hidden');
                                        itemSearchInput.value = '';
                                    });
                                    itemSearchResults.appendChild(listItem);
                                });
                                itemSearchResults.classList.remove('hidden');
                            } else {
                                itemSearchResults.innerHTML = '<li class="p-2 opacity-70">No items found.</li>';
                                itemSearchResults.classList.remove('hidden');
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching search results:', error);
                            itemSearchResults.innerHTML = '<li class="p-2 text-error">Error fetching results.</li>';
                            itemSearchResults.classList.remove('hidden');
                        });
                });

                // Close search results when clicking outside
                document.addEventListener('click', function (e) {
                    if (!itemSearchResults.contains(e.target) && e.target !== itemSearchInput) {
                        itemSearchResults.classList.add('hidden');
                    }
                });

                // Function to add a payout product
                function addPayoutProduct(item) {
                    // Check if item is already in the list
                    if (payoutProducts.includes(item.id)) {
                        alert('Product is already in the payout list.');
                        return;
                    }

                    // Add item to the list (just the ID, like popular items)
                    payoutProducts.push(item.id);
                    payoutProductsInput.value = JSON.stringify(payoutProducts);

                    // Update the UI
                    const listItem = document.createElement('li');
                    listItem.className = 'flex justify-between items-center p-2 bg-base-200 rounded-md';
                    listItem.dataset.id = item.id;
                    listItem.innerHTML = `
                        <span>${item.name}</span>
                        <button type="button" class="btn btn-error btn-sm btn-square" onclick="removePayoutProduct('${item.id}')">
                            <i class="fa-sharp fa-trash"></i>
                        </button>
                    `;
                    payoutProductsList.appendChild(listItem);
                }

                // Function to remove a payout product
                window.removePayoutProduct = function (id) {
                    payoutProducts = payoutProducts.filter(productId => productId !== id);
                    payoutProductsInput.value = JSON.stringify(payoutProducts);

                    const listItem = payoutProductsList.querySelector(`li[data-id="${id}"]`);
                    if (listItem) listItem.remove();
                };

                // Search inventory items for payout products
                payoutItemSearchInput.addEventListener('input', function () {
                    const query = payoutItemSearchInput.value.trim();

                    if (!query) {
                        payoutItemSearchResults.classList.add('hidden');
                        payoutItemSearchResults.innerHTML = '';
                        return;
                    }

                    fetch(`/inventory/search?query=${encodeURIComponent(query)}&active=true`, {
                        method: 'GET',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'Accept': 'application/json',
                        },
                    })
                        .then(response => response.json())
                        .then(items => {
                            payoutItemSearchResults.innerHTML = '';
                            if (items.length > 0) {
                                items.forEach(item => {
                                    const listItem = document.createElement('li');
                                    listItem.className = 'p-2 cursor-pointer hover:bg-base-300';
                                    listItem.textContent = `${item.name} (${item.asset_tag}) - ${item.location}`;
                                    listItem.addEventListener('click', function () {
                                        addPayoutProduct(item);
                                        payoutItemSearchResults.classList.add('hidden');
                                        payoutItemSearchInput.value = '';
                                    });
                                    payoutItemSearchResults.appendChild(listItem);
                                });
                                payoutItemSearchResults.classList.remove('hidden');
                            } else {
                                payoutItemSearchResults.innerHTML = '<li class="p-2 opacity-70">No items found.</li>';
                                payoutItemSearchResults.classList.remove('hidden');
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching search results:', error);
                            payoutItemSearchResults.innerHTML = '<li class="p-2 text-error">Error fetching results.</li>';
                            payoutItemSearchResults.classList.remove('hidden');
                        });
                });

                // Close search results when clicking outside
                document.addEventListener('click', function (e) {
                    if (!payoutItemSearchResults.contains(e.target) && e.target !== payoutItemSearchInput) {
                        payoutItemSearchResults.classList.add('hidden');
                    }
                });

                // Availability Windows Management
                function updateAvailabilityWindowsInput() {
                    const windows = {};
                    document.querySelectorAll('.day-availability').forEach(dayContainer => {
                        const day = dayContainer.dataset.day;
                        const windowsList = dayContainer.querySelector('.windows-list');
                        const windowItems = windowsList.querySelectorAll('.window-item');

                        windows[day] = [];
                        windowItems.forEach(item => {
                            const start = item.querySelector('.window-start').value;
                            const end = item.querySelector('.window-end').value;
                            if (start && end) {
                                windows[day].push({ start, end });
                            }
                        });
                    });
                    availabilityWindowsInput.value = JSON.stringify(windows);
                }

                // Add window button click handlers
                document.querySelectorAll('.add-window-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const day = this.dataset.day;
                        const windowsList = document.querySelector(`.windows-list[data-day="${day}"]`);

                        const windowItem = document.createElement('div');
                        windowItem.className = 'window-item flex items-center gap-2 p-2 bg-base-200 rounded';
                        windowItem.innerHTML = `
                            <input type="time" class="input input-bordered input-sm window-start" placeholder="Start time">
                            <span class="text-sm opacity-70">to</span>
                            <input type="time" class="input input-bordered input-sm window-end" placeholder="End time">
                            <button type="button" class="btn btn-error btn-sm btn-square remove-window-btn">
                                <i class="fa-sharp fa-trash"></i>
                            </button>
                        `;

                        windowsList.appendChild(windowItem);

                        // Add event listeners to new inputs
                        windowItem.querySelector('.window-start').addEventListener('change', updateAvailabilityWindowsInput);
                        windowItem.querySelector('.window-end').addEventListener('change', updateAvailabilityWindowsInput);
                        windowItem.querySelector('.remove-window-btn').addEventListener('click', function() {
                            windowItem.remove();
                            updateAvailabilityWindowsInput();
                        });
                    });
                });

                // Remove window button click handlers for existing windows
                document.querySelectorAll('.remove-window-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        this.closest('.window-item').remove();
                        updateAvailabilityWindowsInput();
                    });
                });

                // Time input change handlers for existing windows
                document.querySelectorAll('.window-start, .window-end').forEach(input => {
                    input.addEventListener('change', updateAvailabilityWindowsInput);
                });

                // Initialize the input with current values
                updateAvailabilityWindowsInput();

                // Pickup Notification User Groups Management
                function updatePickupNotificationUserGroupsInput() {
                    const selectedGroups = [];
                    document.querySelectorAll('.pickup-notification-group:checked').forEach(checkbox => {
                        selectedGroups.push(parseInt(checkbox.value));
                    });
                    document.getElementById('pickup_notification_user_groups_input').value = JSON.stringify(selectedGroups);
                }

                // Add event listeners to pickup notification checkboxes
                document.querySelectorAll('.pickup-notification-group').forEach(checkbox => {
                    checkbox.addEventListener('change', updatePickupNotificationUserGroupsInput);
                });

                // Initialize the pickup notification input with current values
                updatePickupNotificationUserGroupsInput();

                // Pickup Item Types Management
                let itemTypesData = @json($pickup_item_types ?? []);

                function updateItemTypesInput() {
                    document.getElementById('pickup_item_types_input').value = JSON.stringify(itemTypesData);
                }

                function renderItemTypes() {
                    const container = document.getElementById('item-types-container');
                    container.innerHTML = '';

                    itemTypesData.forEach((itemType, index) => {
                        const itemDiv = document.createElement('div');
                        itemDiv.className = 'card bg-base-200 p-4';
                        itemDiv.innerHTML = `
                            <div class="flex justify-between items-start mb-3">
                                <h6 class="font-semibold text-base-content">Item Type ${index + 1}</h6>
                                <button type="button" class="btn btn-error btn-sm btn-square" onclick="removeItemType(${index})">
                                    <i class="fa-sharp fa-trash"></i>
                                </button>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">Name</span>
                                    </label>
                                    <input type="text" class="input input-bordered input-sm"
                                           value="${itemType.name || ''}"
                                           onchange="updateItemType(${index}, 'name', this.value)"
                                           placeholder="e.g., Laptops">
                                </div>
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">Value</span>
                                    </label>
                                    <input type="text" class="input input-bordered input-sm"
                                           value="${itemType.value || ''}"
                                           onchange="updateItemType(${index}, 'value', this.value)"
                                           placeholder="e.g., laptops">
                                </div>
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">Icon</span>
                                    </label>
                                    <input type="text" class="input input-bordered input-sm"
                                           value="${itemType.icon || ''}"
                                           onchange="updateItemType(${index}, 'icon', this.value)"
                                           placeholder="e.g., fa-sharp fa-laptop">
                                </div>
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">Icon Preview</span>
                                    </label>
                                    <div class="flex items-center h-10">
                                        <i class="${itemType.icon || 'fa-sharp fa-question'} text-2xl text-primary"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-control mt-4">
                                <label class="label">
                                    <span class="label-text font-medium">Notes</span>
                                </label>
                                <textarea class="textarea textarea-bordered textarea-sm"
                                          onchange="updateItemType(${index}, 'notes', this.value)"
                                          placeholder="Additional information shown when this item type is selected">${itemType.notes || ''}</textarea>
                            </div>
                        `;
                        container.appendChild(itemDiv);
                    });
                }

                window.updateItemType = function(index, field, value) {
                    if (itemTypesData[index]) {
                        itemTypesData[index][field] = value;
                        updateItemTypesInput();

                        // Re-render to update icon preview
                        if (field === 'icon') {
                            renderItemTypes();
                        }
                    }
                };

                window.removeItemType = function(index) {
                    itemTypesData.splice(index, 1);
                    updateItemTypesInput();
                    renderItemTypes();
                };

                window.addItemType = function() {
                    itemTypesData.push({
                        name: '',
                        value: '',
                        icon: 'fa-sharp fa-question',
                        notes: ''
                    });
                    updateItemTypesInput();
                    renderItemTypes();
                };

                // Add event listener for add button
                document.getElementById('add-item-type').addEventListener('click', addItemType);

                // Initialize item types display
                renderItemTypes();
                updateItemTypesInput();

                // Logo upload functions
                window.openLogoModal = function(type) {
                    const modal = document.getElementById(`${type}-logo-modal`);
                    if (modal) {
                        modal.classList.add('modal-open');
                        
                        // Initialize dropzone if not already initialized
                        setTimeout(() => {
                            const dropzoneComponent = modal.querySelector('.image-dropzone-component');
                            if (dropzoneComponent && !dropzoneComponent._dropzoneInstance) {
                                console.log('Initializing dropzone for modal');
                                new ImageDropzone(dropzoneComponent);
                            }
                        }, 100);
                    }
                };

                window.uploadLogo = function(type) {
                    // Get the modal and find the dropzone component inside it
                    const modal = document.getElementById(`${type}-logo-modal`);
                    if (!modal) {
                        alert('Error: Could not find modal');
                        return;
                    }

                    // Look for the image-dropzone-component within the modal
                    const dropzoneComponent = modal.querySelector('.image-dropzone-component');
                    if (!dropzoneComponent) {
                        alert('Error: Could not find logo upload component');
                        return;
                    }

                    // Get the dropzone instance from the component
                    let dropzoneInstance = dropzoneComponent._dropzoneInstance;
                    if (!dropzoneInstance) {
                        // Try to get the file from the file input as fallback
                        const fileInput = dropzoneComponent.querySelector('input[type="file"]');
                        if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
                            alert('Please select a logo file first');
                            return;
                        }
                        var file = fileInput.files[0];
                    } else {
                        // Get file from dropzone instance
                        if (!dropzoneInstance.files || dropzoneInstance.files.length === 0) {
                            alert('Please select a logo file first');
                            return;
                        }
                        var file = dropzoneInstance.files[0].file;
                    }
                    const formData = new FormData();
                    formData.append('logo_file', file);
                    formData.append('logo_type', type);
                    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

                    // Show loading state
                    const saveBtn = document.querySelector(`#${type}-logo-modal .btn-primary`);
                    const originalText = saveBtn.innerHTML;
                    saveBtn.disabled = true;
                    saveBtn.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Uploading...';

                    fetch('{{ route("globalconfig.upload-logo") }}', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Reload the page to show the new logo
                            window.location.reload();
                        } else {
                            alert(data.message || 'Error uploading logo');
                        }
                    })
                    .catch(error => {
                        console.error('Upload error:', error);
                        alert('Error uploading logo. Please try again.');
                    })
                    .finally(() => {
                        // Reset button state
                        saveBtn.disabled = false;
                        saveBtn.innerHTML = originalText;
                    });
                };

                // Modal close handlers
                document.querySelectorAll('.modal-close').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const modal = this.closest('.modal');
                        if (modal) {
                            modal.classList.remove('modal-open');
                        }
                    });
                });

                // Modal backdrop click handlers
                document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                    backdrop.addEventListener('click', function() {
                        const modal = this.closest('.modal');
                        if (modal) {
                            modal.classList.remove('modal-open');
                        }
                    });
                });
            });
        </script>
    @endpush
</x-app-layout>
