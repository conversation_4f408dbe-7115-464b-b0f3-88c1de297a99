<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            {{ __('Profile Photo') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100 overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    @if (session('status'))
                        <div class="mb-4 font-medium text-sm text-success">
                            {{ session('status') }}
                        </div>
                    @endif

                    <form method="POST" action="{{ route('profile.photo.update') }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-base-content">{{ __('Current Profile Photo') }}</h3>
                            <div class="mt-2">
                                <img src="{{ $user->profilePhotoUrl }}" alt="{{ $user->name }}" class="rounded-full h-20 w-20 object-cover">
                            </div>
                        </div>

                        <div class="mb-6">
                            <x-label for="photo" value="{{ __('New Profile Photo') }}" />
                            <input id="photo" type="file" name="photo" class="file-input file-input-bordered w-full mt-1" accept="image/jpeg,image/png,image/webp">
                            <x-input-error for="photo" class="mt-2" />
                            <p class="mt-1 text-sm text-base-content/70">
                                {{ __('Photo must be less than 1MB. Supported formats: JPG, PNG, WebP.') }}
                            </p>
                        </div>

                        <div class="flex items-center justify-end">
                            @if ($user->profile_photo_id)
                                <form method="POST" action="{{ route('profile.photo.destroy') }}" class="mr-3">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-error">
                                        {{ __('Remove Photo') }}
                                    </button>
                                </form>
                            @endif

                            <a href="{{ route('profile.show') }}" class="btn btn-ghost mr-3">
                                {{ __('Cancel') }}
                            </a>

                            <x-button>
                                {{ __('Save') }}
                            </x-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
