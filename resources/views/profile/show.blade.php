@php
use Illuminate\Support\Facades\Auth;
@endphp

<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            {{ __('Profile') }}
        </h2>
    </x-slot>

    <div>
        <div class="max-w-7xl mx-auto py-10 sm:px-6 lg:px-8">

            @if (<PERSON><PERSON>\Fortify\Features::canUpdateProfileInformation())
                @livewire('profile.update-profile-information-form')

                <x-section-border />
            @endif

            @if (<PERSON>vel\Fortify\Features::enabled(<PERSON>vel\Fortify\Features::updatePasswords()))
                <div class="mt-10 sm:mt-0">
                    @livewire('profile.update-password-form')
                </div>

                <x-section-border />
            @endif

            @if (Laravel\Fortify\Features::canManageTwoFactorAuthentication())
                <div class="mt-10 sm:mt-0">
                    @livewire('profile.two-factor-authentication-form')
                </div>

                <x-section-border />
            @endif

            <div class="mt-10 sm:mt-0">
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h2 class="card-title">{{ __('Emergency Contact Information') }}</h2>
                        <p class="text-sm text-base-content/70 mb-4">
                            {{ __('Manage your emergency contact information and optional health details.') }}
                        </p>

                        <div class="flex items-center mt-4">
                            @if(Auth::user()->emergency_contact_name)
                                <div class="flex-1">
                                    <p class="font-medium">{{ Auth::user()->emergency_contact_name }}</p>
                                    <p class="text-sm text-base-content/70">{{ Auth::user()->emergency_contact_relationship }}</p>
                                    <p class="text-sm text-base-content/70">{{ Auth::user()->emergency_contact_phone }}</p>
                                </div>
                            @else
                                <div class="flex-1">
                                    <div class="alert alert-warning">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        <span>{{ __('No emergency contact information provided') }}</span>
                                    </div>
                                </div>
                            @endif

                            <div>
                                <a href="{{ route('profile.emergency-contact.edit') }}" class="btn btn-primary">
                                    {{ __('Manage Emergency Contacts') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <x-section-border />

            <div class="mt-10 sm:mt-0">
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h2 class="card-title">{{ __('Browser Notifications') }}</h2>
                        <p class="text-sm text-base-content/70 mb-4">
                            {{ __('Enable browser notifications to receive real-time updates on your desktop and mobile devices.') }}
                        </p>

                        <div class="space-y-4">
                            <!-- Notification Status -->
                            <div class="flex items-center justify-between p-4 bg-base-200 rounded-lg">
                                <div>
                                    <h3 class="font-medium">{{ __('Push Notifications') }}</h3>
                                    <p class="text-sm text-base-content/70">
                                        Status: <span id="notification-status" class="font-medium">Checking...</span>
                                    </p>
                                </div>
                                <div class="space-x-2">
                                    <button id="enable-notifications-btn" class="btn btn-primary btn-sm" style="display: none;">
                                        <i class="fa-sharp fa-bell mr-2"></i>
                                        {{ __('Enable') }}
                                    </button>
                                    <button id="disable-notifications-btn" class="btn btn-error btn-sm" style="display: none;">
                                        <i class="fa-sharp fa-bell-slash mr-2"></i>
                                        {{ __('Disable') }}
                                    </button>
                                    <button id="test-notification-btn" class="btn btn-info btn-sm">
                                        <i class="fa-sharp fa-paper-plane mr-2"></i>
                                        {{ __('Test') }}
                                    </button>
                                </div>
                            </div>

                            <!-- Browser Support Info -->
                            <div class="bg-info/10 border border-info/20 rounded-lg p-4">
                                <h4 class="font-medium text-info mb-2">
                                    <i class="fa-sharp fa-info-circle mr-2"></i>
                                    {{ __('About Browser Notifications') }}
                                </h4>
                                <ul class="text-sm text-base-content/70 space-y-1 ml-4">
                                    <li>• {{ __('Get instant notifications for new pickup requests, form submissions, and system alerts') }}</li>
                                    <li>• {{ __('Works on desktop and mobile browsers when the site is not open') }}</li>
                                    <li>• {{ __('You can manage notification permissions in your browser settings') }}</li>
                                    <li>• {{ __('Notifications respect your user group permissions and targeting') }}</li>
                                </ul>
                            </div>

                            <!-- Active Subscriptions -->
                            <div class="bg-base-200 rounded-lg p-4">
                                <h4 class="font-medium mb-3">{{ __('Active Devices') }}</h4>
                                <div id="subscription-list">
                                    <p class="text-sm text-base-content/70">{{ __('Loading device information...') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <x-section-border />

            <div class="mt-10 sm:mt-0">
                @livewire('profile.logout-other-browser-sessions-form')
            </div>

            @if (Laravel\Jetstream\Jetstream::hasAccountDeletionFeatures())
                <x-section-border />

                <div class="mt-10 sm:mt-0">
                    @livewire('profile.delete-user-form')
                </div>
            @endif
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            // Load subscription information when page loads
            await loadSubscriptionInfo();
            
            
            // Override the default updateUI function to also load subscriptions
            if (window.browserNotifications) {
                const originalUpdateUI = window.browserNotifications.updateUI;
                window.browserNotifications.updateUI = function() {
                    originalUpdateUI.call(this);
                    loadSubscriptionInfo();
                };
            }
        });

        async function loadSubscriptionInfo() {
            try {
                const response = await fetch('/api/notifications/subscriptions', {
                    method: 'GET',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Content-Type': 'application/json',
                    },
                    credentials: 'same-origin'
                });

                if (response.ok) {
                    const data = await response.json();
                    displaySubscriptions(data.subscriptions);
                } else {
                    console.error('Failed to load subscriptions:', response.status, response.statusText);
                    document.getElementById('subscription-list').innerHTML = 
                        '<p class="text-sm text-error">Failed to load device information.</p>';
                }
            } catch (error) {
                console.error('Error loading subscriptions:', error);
                document.getElementById('subscription-list').innerHTML = 
                    '<p class="text-sm text-error">Error loading device information.</p>';
            }
        }

        function displaySubscriptions(subscriptions) {
            const container = document.getElementById('subscription-list');
            
            if (subscriptions.length === 0) {
                container.innerHTML = '<p class="text-sm text-base-content/70">No active notification subscriptions.</p>';
                return;
            }

            const html = subscriptions.map(sub => {
                const deviceInfo = sub.device_info || {};
                const platform = deviceInfo.platform || 'Unknown';
                const userAgent = deviceInfo.userAgent || '';
                
                // Extract browser info from user agent
                let browserName = 'Unknown Browser';
                if (userAgent.includes('Chrome')) browserName = 'Chrome';
                else if (userAgent.includes('Firefox')) browserName = 'Firefox';
                else if (userAgent.includes('Safari')) browserName = 'Safari';
                else if (userAgent.includes('Edge')) browserName = 'Edge';

                const icon = getDeviceIcon(platform, browserName);

                return `
                    <div class="flex items-center justify-between p-3 bg-base-100 rounded border mb-2">
                        <div class="flex items-center space-x-3">
                            <i class="${icon} text-primary"></i>
                            <div>
                                <p class="font-medium text-sm">${browserName} on ${platform}</p>
                                <p class="text-xs text-base-content/50">Subscribed: ${sub.created_at}</p>
                            </div>
                        </div>
                        <div class="badge badge-success badge-sm">Active</div>
                    </div>
                `;
            }).join('');

            container.innerHTML = html;
        }

        function getDeviceIcon(platform, browser) {
            // Determine device type
            if (platform.includes('iPhone') || platform.includes('iPad')) {
                return 'fa-sharp fa-mobile-screen-button';
            } else if (platform.includes('Android')) {
                return 'fa-sharp fa-mobile-screen';
            } else if (platform.includes('Win')) {
                return 'fa-sharp fa-desktop';
            } else if (platform.includes('Mac')) {
                return 'fa-sharp fa-desktop';
            } else if (platform.includes('Linux')) {
                return 'fa-sharp fa-desktop';
            } else {
                return 'fa-sharp fa-globe';
            }
        }










    </script>
    @endpush
</x-app-layout>
