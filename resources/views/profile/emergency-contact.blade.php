<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-base-content leading-tight">
                {{ __('Emergency Contact Information') }}
            </h2>
            <a href="{{ route('profile.show') }}" class="btn btn-sm btn-outline">
                <i class="fa-sharp fa-arrow-left mr-1"></i> {{ __('Back to Profile') }}
            </a>
        </div>
    </x-slot>

    <div>
        <div class="max-w-7xl mx-auto py-10 sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title mb-4">{{ __('Emergency Contact Information') }}</h2>
                    <p class="text-sm text-base-content mb-6">
                        {{ __('Update your emergency contact information. This information will be available to staff in case of emergency.') }}
                    </p>

                    <div id="status-message" class="alert mb-4 hidden">
                        <span id="status-message-text"></span>
                    </div>

                    <form id="emergency-contact-form">
                        @csrf
                        <!-- Primary Emergency Contact Section -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-primary mb-2">{{ __('Primary Emergency Contact') }}</h3>
                            <p class="text-sm text-base-content mb-4">
                                {{ __('You are required to provide at least one emergency contact.') }}
                            </p>
                        </div>

                        <!-- Emergency Contact Name -->
                        <div class="form-control w-full mb-4">
                            <label for="emergency_contact_name" class="label">
                                <span class="label-text">{{ __('Emergency Contact Name') }}</span>
                            </label>
                            <input id="emergency_contact_name" name="emergency_contact_name" type="text"
                                   class="input input-bordered w-full"
                                   value="{{ $user->emergency_contact_name }}" />
                            <div class="error-message text-error text-sm mt-1" id="emergency_contact_name_error"></div>
                        </div>

                        <!-- Emergency Contact Relationship -->
                        <div class="form-control w-full mb-4">
                            <label for="emergency_contact_relationship" class="label">
                                <span class="label-text">{{ __('Relationship') }}</span>
                            </label>
                            <input id="emergency_contact_relationship" name="emergency_contact_relationship" type="text"
                                   class="input input-bordered w-full"
                                   value="{{ $user->emergency_contact_relationship }}" />
                            <div class="error-message text-error text-sm mt-1" id="emergency_contact_relationship_error"></div>
                        </div>

                        <!-- Emergency Contact Phone -->
                        <div class="form-control w-full mb-4">
                            <label for="emergency_contact_phone" class="label">
                                <span class="label-text">{{ __('Phone Number') }}</span>
                            </label>
                            <input id="emergency_contact_phone" name="emergency_contact_phone" type="text"
                                   class="input input-bordered w-full"
                                   value="{{ $user->emergency_contact_phone }}" />
                            <div class="error-message text-error text-sm mt-1" id="emergency_contact_phone_error"></div>
                        </div>

                        <!-- Additional Emergency Contacts -->
                        <div class="form-control w-full mb-6">
                            <label for="additional_emergency_contacts" class="label">
                                <span class="label-text">{{ __('Additional Emergency Contacts') }}</span>
                            </label>
                            <textarea id="additional_emergency_contacts" name="additional_emergency_contacts"
                                      class="textarea textarea-bordered w-full h-24"
                                      placeholder="List additional emergency contacts with their names, relationships, and phone numbers">{{ $user->additional_emergency_contacts }}</textarea>
                            <label class="label">
                                <span class="label-text-alt text-gray-500">Optional: List any additional emergency contacts</span>
                            </label>
                            <div class="error-message text-error text-sm mt-1" id="additional_emergency_contacts_error"></div>
                        </div>

                        <!-- Health Information Section -->
                        <div class="divider">
                            <h3 class="text-lg font-medium text-primary">{{ __('Optional Health Information') }}</h3>
                        </div>

                        <div class="mb-6">
                            
                                <span>{{ __('You may provide additional medical information if you wish. This information will be shared with emergency services in the event of an emergency. Information provided is completely optional and will not be used for any other purpose.') }}</span>
                            
                        </div>

                        <!-- Allergies -->
                        <div class="form-control w-full mb-4">
                            <label for="allergies" class="label">
                                <span class="label-text">{{ __('Allergies') }}</span>
                            </label>
                            <input id="allergies" name="allergies" type="text"
                                   class="input input-bordered w-full"
                                   value="{{ $user->allergies }}" />
                            <div class="error-message text-error text-sm mt-1" id="allergies_error"></div>
                        </div>

                        <!-- Medications -->
                        <div class="form-control w-full mb-4">
                            <label for="medications" class="label">
                                <span class="label-text">{{ __('Medications') }}</span>
                            </label>
                            <textarea id="medications" name="medications"
                                      class="textarea textarea-bordered w-full">{{ $user->medications }}</textarea>
                            <div class="error-message text-error text-sm mt-1" id="medications_error"></div>
                        </div>

                        <!-- Medical Conditions -->
                        <div class="form-control w-full mb-4">
                            <label for="medical_conditions" class="label">
                                <span class="label-text">{{ __('Medical Conditions') }}</span>
                            </label>
                            <textarea id="medical_conditions" name="medical_conditions"
                                      class="textarea textarea-bordered w-full">{{ $user->medical_conditions }}</textarea>
                            <div class="error-message text-error text-sm mt-1" id="medical_conditions_error"></div>
                        </div>

                        <!-- Blood Type -->
                        <div class="form-control w-full mb-4">
                            <label for="blood_type" class="label">
                                <span class="label-text">{{ __('Blood Type') }}</span>
                            </label>
                            <select id="blood_type" name="blood_type" class="select select-bordered w-full">
                                <option value="">-- Select Blood Type --</option>
                                <option value="A+" {{ $user->blood_type == 'A+' ? 'selected' : '' }}>A+</option>
                                <option value="A-" {{ $user->blood_type == 'A-' ? 'selected' : '' }}>A-</option>
                                <option value="B+" {{ $user->blood_type == 'B+' ? 'selected' : '' }}>B+</option>
                                <option value="B-" {{ $user->blood_type == 'B-' ? 'selected' : '' }}>B-</option>
                                <option value="AB+" {{ $user->blood_type == 'AB+' ? 'selected' : '' }}>AB+</option>
                                <option value="AB-" {{ $user->blood_type == 'AB-' ? 'selected' : '' }}>AB-</option>
                                <option value="O+" {{ $user->blood_type == 'O+' ? 'selected' : '' }}>O+</option>
                                <option value="O-" {{ $user->blood_type == 'O-' ? 'selected' : '' }}>O-</option>
                                <option value="Unknown" {{ $user->blood_type == 'Unknown' ? 'selected' : '' }}>Unknown</option>
                            </select>
                            <div class="error-message text-error text-sm mt-1" id="blood_type_error"></div>
                        </div>

                        <!-- Additional Health Information -->
                        <div class="form-control w-full mb-6">
                            <label for="additional_health_info" class="label">
                                <span class="label-text">{{ __('Additional Health Information') }}</span>
                            </label>
                            <textarea id="additional_health_info" name="additional_health_info"
                                      class="textarea textarea-bordered w-full">{{ $user->additional_health_info }}</textarea>
                            <div class="error-message text-error text-sm mt-1" id="additional_health_info_error"></div>
                        </div>

                        <!-- Save Status -->
                        <div class="flex items-center justify-end mt-4">
                            <div id="saving-indicator" class="hidden me-3">
                                <span class="loading loading-spinner loading-sm"></span>
                                <span class="text-sm text-base-content">{{ __('Saving changes...') }}</span>
                            </div>
                            <div id="saved-indicator" class="hidden me-3">
                                <div class="flex items-center text-success">
                                    <i class="fa-sharp fa-circle-check mr-1"></i>
                                    <span>{{ __('All changes saved.') }}</span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get all form inputs
            const formInputs = document.querySelectorAll('#emergency-contact-form input, #emergency-contact-form textarea, #emergency-contact-form select');

            // Create a debounce function
            function debounce(func, wait) {
                let timeout;
                return function() {
                    const context = this;
                    const args = arguments;
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        func.apply(context, args);
                    }, wait);
                };
            }

            // Function to save form data
            const saveFormData = debounce(function() {
                // Show saving indicator
                document.getElementById('saving-indicator').classList.remove('hidden');
                document.getElementById('saved-indicator').classList.add('hidden');

                // Clear previous error messages
                document.querySelectorAll('.error-message').forEach(el => {
                    el.textContent = '';
                });

                // Get form data
                const form = document.getElementById('emergency-contact-form');
                const formData = new FormData(form);

                // Send AJAX request
                fetch('{{ route('profile.emergency-contact.update') }}', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // Hide saving indicator
                    document.getElementById('saving-indicator').classList.add('hidden');

                    if (data.success) {
                        // Show success message
                        document.getElementById('saved-indicator').classList.remove('hidden');

                        // Hide success message after 3 seconds
                        setTimeout(() => {
                            document.getElementById('saved-indicator').classList.add('hidden');
                        }, 3000);
                    } else {
                        // Show error messages
                        const errors = data.errors;
                        for (const field in errors) {
                            const errorElement = document.getElementById(`${field}_error`);
                            if (errorElement) {
                                errorElement.textContent = errors[field][0];
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('saving-indicator').classList.add('hidden');

                    // Show error message
                    const statusMessage = document.getElementById('status-message');
                    const statusMessageText = document.getElementById('status-message-text');
                    statusMessage.classList.remove('hidden', 'alert-success');
                    statusMessage.classList.add('alert-error');
                    statusMessageText.textContent = 'An error occurred while saving your changes. Please try again.';
                });
            }, 500); // 500ms debounce

            // Add event listeners to all form inputs
            formInputs.forEach(input => {
                input.addEventListener('change', saveFormData);
                input.addEventListener('input', saveFormData);
            });
        });
    </script>
    @endpush
</x-app-layout>
