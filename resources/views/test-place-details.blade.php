<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Test Place Details API</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Test Place Details API</h1>
        
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-lg font-semibold mb-4">Test Place ID</h2>
            <p class="mb-4">Testing with E-Tech Recyclers place ID: <code class="bg-gray-100 px-2 py-1 rounded">ChIJVyeWGFFPE4cRvkXz5Lk35BE</code></p>
            
            <button id="testBtn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Test Place Details API
            </button>
            
            <div id="loading" class="hidden mt-4">
                <div class="text-blue-600">Loading...</div>
            </div>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4">Results</h2>
            <pre id="results" class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96"></pre>
        </div>
    </div>

    <script>
        document.getElementById('testBtn').addEventListener('click', async function() {
            const loadingDiv = document.getElementById('loading');
            const resultsDiv = document.getElementById('results');
            const btn = this;
            
            btn.disabled = true;
            loadingDiv.classList.remove('hidden');
            resultsDiv.textContent = '';
            
            try {
                console.log('Testing place details API...');
                
                const response = await fetch('/api/google-maps/place-details', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        placeId: 'ChIJVyeWGFFPE4cRvkXz5Lk35BE',
                        sessionToken: 'test_session_' + Date.now(),
                        fields: ['id', 'displayName', 'formattedAddress', 'shortFormattedAddress', 'location', 'addressComponents', 'types', 'primaryType', 'primaryTypeDisplayName']
                    })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${response.statusText}\n${errorText}`);
                }
                
                const data = await response.json();
                console.log('Response data:', data);
                
                resultsDiv.textContent = JSON.stringify(data, null, 2);
                
                if (data.success && data.place && data.place.addressComponents) {
                    console.log('Address components:', data.place.addressComponents);
                    
                    // Test address construction
                    const place = data.place;
                    let streetNumber = '';
                    let route = '';
                    
                    place.addressComponents.forEach(component => {
                        const types = component.types || [];
                        if (types.includes('street_number')) {
                            streetNumber = component.longName;
                        } else if (types.includes('route')) {
                            route = component.longName;
                        }
                    });
                    
                    console.log('Extracted street info:', { streetNumber, route });
                    
                    if (streetNumber && route) {
                        const constructedAddress = `${streetNumber} ${route}`;
                        console.log('Constructed street address:', constructedAddress);
                        
                        // Add to results
                        resultsDiv.textContent += '\n\n--- CONSTRUCTED ADDRESS ---\n';
                        resultsDiv.textContent += `Street Number: ${streetNumber}\n`;
                        resultsDiv.textContent += `Route: ${route}\n`;
                        resultsDiv.textContent += `Full Street Address: ${constructedAddress}`;
                    }
                }
                
            } catch (error) {
                console.error('Error:', error);
                resultsDiv.textContent = 'ERROR: ' + error.message;
            } finally {
                btn.disabled = false;
                loadingDiv.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
