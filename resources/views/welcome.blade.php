<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>ETRFlow</title>
    @vite('resources/css/app.css') <!-- Ensure Tailwind is included -->
    @vite('resources/css/stars.css')
    @vite('resources/js/app.jsx')
</head>
<body class="min-h-screen">
    <!-- Stars background divs with fixed positioning -->
    <div id='stars' class="fixed inset-0 z-0"></div>
    <div id='stars2' class="fixed inset-0 z-0"></div>
    <div id='stars3' class="fixed inset-0 z-0"></div>

    <!-- Content container with relative positioning -->
    <div class="flex justify-center items-center min-h-screen relative z-10">
        <div class="bg-slate-950 shadow-md rounded-lg p-6 w-96 border border-1 border-blue-500">
            <!-- Logo Area -->
            <div class="mb-4 flex justify-center">
                <img src="{{ asset('img/logo.webp') }}" alt="ETRFlow Logo" class="w-32 h-32 mx-auto">
            </div>

            <!-- Title -->
            <h1 class="text-2xl font-bold text-white mb-2 text-center">ETRFlow</h1>

            <!-- Description -->
            <p class="text-white mb-6 text-center">
                This is an internal tool used by <strong>E-Tech Recyclers</strong> for <strong>E-Tech Employees</strong> only.
            </p>

            <!-- Login Form -->
            <div id="login-section">
                @if ($errors->any())
                    <div class="mb-4 p-3 bg-red-100 border border-red-300 rounded-md">
                        <div class="font-medium text-red-600">{{ __('Login Failed') }}</div>
                        <ul class="mt-2 list-disc list-inside text-sm text-red-600">
                            @if ($errors->has('email') || $errors->has('password'))
                                <li>{{ __('The email or password you entered is incorrect. Please try again.') }}</li>
                            @else
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            @endif
                        </ul>
                    </div>
                @endif

                @if (session('status'))
                    <div class="mb-4 font-medium text-sm text-green-600">
                        {{ session('status') }}
                    </div>
                @endif

                <form method="POST" action="{{ route('login') }}" id="login-form">
                    @csrf

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-200">{{ __('Email') }}</label>
                        <input id="email" class="input input-bordered w-full mt-1" type="email" name="email" value="{{ old('email') }}" required autofocus autocomplete="username" />
                    </div>

                    <div class="mt-4">
                        <label for="password" class="block text-sm font-medium text-gray-200">{{ __('Password') }}</label>
                        <input id="password" class="input input-bordered mt-1 w-full" type="password" name="password" required autocomplete="current-password" />
                    </div>

                    <div class="block mt-4">
                        <label for="remember_me" class="inline-flex items-center">
                            <input id="remember_me" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="remember">
                            <span class="ml-2 text-sm text-gray-400">{{ __('Remember me') }}</span>
                        </label>
                    </div>

                    <div class="flex items-center justify-between mt-4">
                        @if (Route::has('password.request'))
                            <a class="text-sm text-blue-400 hover:text-blue-500" href="{{ route('password.request') }}" target="_blank">
                                {{ __('Forgot your password?') }}
                            </a>
                        @endif

                        <button type="submit" class="btn btn-primary">
                            {{ __('Log in') }}
                        </button>
                    </div>
                </form>

                <div class="mt-6 pt-4 border-t border-gray-700 text-center">
                    <p class="text-sm text-gray-400">Need an account?</p>
                    <a href="{{ route('register') }}" class="mt-2 block w-full bg-gray-800 text-white py-2 px-4 rounded-lg hover:bg-gray-700 text-center">
                        Register
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Focus on email field when page loads
            const emailField = document.getElementById('email');
            if (emailField) {
                emailField.focus();
            }
        });
    </script>
</body>
</html>
