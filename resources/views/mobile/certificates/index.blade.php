<x-app-layout
    page-title="EZ Custody"
    page-icon="fa-sharp fa-hard-drive"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'EZ Custody', 'icon' => 'fa-hard-drive']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Introduction Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-hard-drive text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">EZ Data Custody Documents</h4>
                    </div>
                </div>

                <div class="p-6">
                    <p class="text-base-content/70 mb-4">
                        This simplified interface will guide you through creating a data chain of custody document and collecting key signatures.
                    </p>

                    <div class="bg-info/10 border border-info/20 rounded-lg p-4">
                        <div class="flex items-start gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-info text-info-content w-6 rounded">
                                    <i class="fa-sharp fa-info-circle text-xs"></i>
                                </div>
                            </div>
                            <div class="text-sm text-base-content/80">
                                <p class="font-medium mb-1">Quick Start Guide:</p>
                                <ul class="space-y-1 text-xs">
                                    <li>• Select or create a customer</li>
                                    <li>• Set the pickup/dropoff date and time</li>
                                    <li>• Follow the 5-step guided process</li>
                                    <li>• Collect digital signatures</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
<form action="{{ route('mobile.certificates.start') }}" method="POST" class="space-y-6">
                        @csrf
            <!-- Certificate Creation Form -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-plus text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Create New Certificate</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    

                        <!-- Customer Selection -->
                        <div>
                            <label class="label" for="customerSearch">
                                <span class="label-text">
                                    <i class="fa-sharp fa-user text-primary mr-2"></i>
                                    Customer <span class="text-error font-bold">*</span>
                                </span>
                            </label>
                            <p class="text-base-content/70 text-sm mb-3">
                                Start typing to search for a customer. If they are not in the system, click the "Create New Customer" button.
                                For business customers, enter their BUSINESS NAME first.
                            </p>

                            <div class="relative">
                                <x-dynamic-customer-search
                                    id="customerSearch"
                                    name="customer_id"
                                    placeholder="Search for customers by name, business name, email, phone, or nickname..."
                                    action="form"
                                    quickCreateButtonId="quickCreateCustomerBtn"
                                />
                                <x-quick-create-customer
                                    id="customerSearch"
                                    name="customer_id"
                                />
                            </div>

                            @error('customer_id')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>

                        <!-- Pickup/Dropoff Date -->
                        <div>
                            <label class="label" for="pickup_dropoff_date">
                                <span class="label-text">
                                    <i class="fa-sharp fa-calendar-clock text-accent mr-2"></i>
                                    Pickup/Dropoff Date & Time <span class="text-error font-bold">*</span>
                                </span>
                            </label>
                            <p class="text-base-content/70 text-sm mb-3">
                                Select the date and time when the drives were picked up or dropped off. Default is current date and time.
                            </p>
                            <input type="datetime-local"
                                   name="pickup_dropoff_date"
                                   id="pickup_dropoff_date"
                                   class="input input-bordered w-full"
                                   value="{{ old('pickup_dropoff_date', now()->format('Y-m-d\TH:i')) }}"
                                   required>
                            @error('pickup_dropoff_date')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>

                        
                    
                </div>
            </div>
            <!-- Submit Section -->
                        <div class="card bg-base-100 shadow-md">
                            <div class="p-6">
                                <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                                    <div class="flex items-center gap-3">
                                        <div class="avatar avatar-placeholder">
                                            <div class="bg-base-300 text-base-content w-10 rounded-lg">
                                                <i class="fa-sharp fa-rocket text-sm"></i>
                                            </div>
                                        </div>
                                        <div class="text-center sm:text-left">
                                            <p class="font-medium text-base-content">Begin Certificate Process</p>
                                            <p class="text-sm text-base-content/60">Start the guided 5-step workflow</p>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg w-full sm:w-auto">
                                        <i class="fa-sharp fa-file-certificate"></i>
                                        Start Certificate Process
                                    </button>
                                </div>
                            </div>
                        </div>
            </form>
        </div>
    </div>
</x-app-layout>
