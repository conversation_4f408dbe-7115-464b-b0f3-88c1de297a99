<x-app-layout
    page-title="Step 2: Destruction Method"
    page-icon="fa-sharp fa-hammer"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'EZ Custody', 'route' => 'mobile.certificates.index', 'icon' => 'fa-hard-drive'],
        ['name' => 'Step 2', 'icon' => 'fa-hammer']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Progress Steps -->
            <div class="card bg-base-100 shadow-md">
                <div class="p-6">
                    <ul class="steps steps-horizontal w-full">
                        <li class="step step-primary">Transfer</li>
                        <li class="step step-primary">Method</li>
                        <li class="step">Manifest</li>
                        <li class="step">Client</li>
                        <li class="step">Employee</li>
                    </ul>
                </div>
            </div>

            <!-- Certificate Information -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-info text-info-content w-8 rounded-lg">
                                <i class="fa-sharp fa-info-circle text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Certificate Information</h4>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-file-certificate text-primary"></i>
                            <span class="font-medium">Certificate:</span>
                            <span class="font-mono">{{ $certificate->certificate_number }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user text-secondary"></i>
                            <span class="font-medium">Customer:</span>
                            <span>{{ $certificate->customer->name }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-calendar-clock text-accent"></i>
                            <span class="font-medium">Accepted:</span>
                            <span>{{ $certificate->pickup_dropoff_date ? $certificate->pickup_dropoff_date->format('m/d/Y g:i A') : 'N/A' }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-truck text-warning"></i>
                            <span class="font-medium">Method:</span>
                            <span>{{ ($certificate->stats['driver_pickup_dropoff'] ?? '') === 'Yes' ? 'Picked Up from Client' : (($certificate->stats['driver_pickup_dropoff'] ?? '') === 'No' ? 'Dropped Off at Warehouse' : 'Not Set') }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2 Form -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-hammer text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Step 2: Destruction Method</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    <p class="text-base-content/70">
                        Select the destruction method required by the client:
                    </p>

                    <form action="{{ route('mobile.certificates.saveStep2', $certificate) }}" method="POST" class="space-y-6">
                        @csrf

                        <div class="space-y-4">
                            <!-- With Certification Option -->
                            <div class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-success/30 transition-all duration-200 cursor-pointer">
                                <label class="flex items-start cursor-pointer p-4">
                                    <input type="radio"
                                           name="certification_requirement"
                                           value="certified_destruction"
                                           class="radio radio-primary mt-1 mr-3 flex-shrink-0"
                                           required
                                           {{ in_array($certificate->service_selection, ['certified_destruction', 'certified_drive_serialization', 'certified_drive_origin_serialization']) ? 'checked' : '' }}>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center gap-2 mb-2">
                                            <i class="fa-sharp fa-certificate text-success"></i>
                                            <span class="text-lg font-medium text-base-content">Certified Destruction with Device & Drive Serialization</span>
                                        </div>
                                        <p class="text-sm text-base-content/70">All devices and drives will be individually recorded by serial number or asset tag and destroyed. The certificate will include a detailed list of all destroyed assets with their serial numbers.</p>
                                    </div>
                                </label>
                            </div>

                            <!-- Without Certification Option -->
                            <div class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-warning/30 transition-all duration-200 cursor-pointer">
                                <label class="flex items-start cursor-pointer p-4">
                                    <input type="radio"
                                           name="certification_requirement"
                                           value="uncertified_destruction"
                                           class="radio radio-primary mt-1 mr-3 flex-shrink-0"
                                           required
                                           {{ $certificate->service_selection === 'uncertified_destruction' ? 'checked' : '' }}>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center gap-2 mb-2">
                                            <i class="fa-sharp fa-hammer text-warning"></i>
                                            <span class="text-lg font-medium text-base-content">Destroy WITHOUT Certification</span>
                                        </div>
                                        <p class="text-sm text-base-content/70">All drives will be physically destroyed, but serial numbers will not be recorded. No digital Certificate of Destruction will be issued. The service receipt will serve as proof of destruction.</p>
                                    </div>
                                </label>
                            </div>

                            <!-- Declined Option -->
                            <div class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-error/30 transition-all duration-200 cursor-pointer">
                                <label class="flex items-start cursor-pointer p-4">
                                    <input type="radio"
                                           name="certification_requirement"
                                           value="decline_destruction"
                                           class="radio radio-primary mt-1 mr-3 flex-shrink-0"
                                           required
                                           {{ $certificate->service_selection === 'decline_destruction' ? 'checked' : '' }}>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center gap-2 mb-2">
                                            <i class="fa-sharp fa-ban text-error"></i>
                                            <span class="text-lg font-medium text-base-content">Drive Destruction DECLINED</span>
                                        </div>
                                        <p class="text-sm text-base-content/70">Client has declined data destruction services. Drives will be returned to the client or disposed of according to their instructions.</p>
                                    </div>
                                </label>
                            </div>

                            @error('certification_requirement')
                            <div class="alert alert-error">
                                <i class="fa-sharp fa-circle-exclamation"></i>
                                <span>{{ $message }}</span>
                            </div>
                            @enderror
                        </div>

                        <!-- Navigation -->
                        <div class="divider my-8">
                            <span class="text-base-content/50 font-medium">Continue to next step</span>
                        </div>

                        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">

                            <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg w-full sm:w-auto">
                                Continue to Step 3
                                <i class="fa-sharp fa-arrow-right"></i>
                            </button>
                        </div>
                    </form>
                </div>

            </div>
                            <div class="p-6 space-y-6">
                            <a href="{{ route('mobile.certificates.step1', $certificate) }}" class="btn btn-outline btn-lg gap-2 w-full sm:w-auto">
                                <i class="fa-sharp fa-arrow-left"></i>
                                Back to Step 1
                            </a>
                </div>
        </div>
    </div>
</x-app-layout>
