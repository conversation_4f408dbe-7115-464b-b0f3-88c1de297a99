<x-app-layout page-title="Step 1: Pickup or Dropoff" page-icon="fa-sharp fa-truck" :breadcrumbs="[
    ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
    ['name' => 'EZ Custody', 'route' => 'mobile.certificates.index', 'icon' => 'fa-hard-drive'],
    ['name' => 'Step 1', 'icon' => 'fa-truck'],
]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Progress Steps -->
            <div class="card bg-base-100 shadow-md">
                <div class="p-6">
                    <ul class="steps steps-horizontal w-full">
                        <li class="step step-primary">Transfer</li>
                        <li class="step">Method</li>
                        <li class="step">Manifest</li>
                        <li class="step">Client</li>
                        <li class="step">Employee</li>
                    </ul>
                </div>
            </div>

            <!-- Certificate Information -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-info text-info-content w-8 rounded-lg">
                                <i class="fa-sharp fa-info-circle text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Certificate Information</h4>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-file-certificate text-primary"></i>
                            <span class="font-medium">Certificate:</span>
                            <span class="font-mono">{{ $certificate->certificate_number }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user text-secondary"></i>
                            <span class="font-medium">Customer:</span>
                            <span>{{ $certificate->customer->name }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-calendar-clock text-accent"></i>
                            <span class="font-medium">Accepted:</span>
                            <span>{{ $certificate->pickup_dropoff_date ? $certificate->pickup_dropoff_date->format('m/d/Y g:i A') : 'N/A' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 1 Form -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-truck text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Step 1: Pickup or Dropoff</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    <p class="text-base-content/70">
                        Were these items picked up from the client, or did the client drop them off at the warehouse?
                    </p>

                    <form action="{{ route('mobile.certificates.saveStep1', $certificate) }}" method="POST"
                        class="space-y-6">
                        @csrf

                        <div class="space-y-4">
                            <!-- Pickup Option -->
                            <div
                                class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                                <label class="flex items-start cursor-pointer p-4">
                                    <input type="radio" name="pickup_dropoff" value="Yes"
                                        class="radio radio-primary mt-1 mr-3 flex-shrink-0" required
                                        {{ old('pickup_dropoff', $certificate->stats['driver_pickup_dropoff'] ?? '') === 'Yes' ? 'checked' : '' }}>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center gap-2 mb-2">
                                            <i class="fa-sharp fa-truck text-primary"></i>
                                            <span class="text-lg font-medium text-base-content">Picked Up from
                                                Client</span>
                                        </div>
                                        <p class="text-sm text-base-content/70">Driver collected items from client
                                            location</p>
                                    </div>
                                </label>
                            </div>

                            <!-- Dropoff Option -->
                            <div
                                class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                                <label class="flex items-start cursor-pointer p-4">
                                    <input type="radio" name="pickup_dropoff" value="No"
                                        class="radio radio-primary mt-1 mr-3 flex-shrink-0" required
                                        {{ old('pickup_dropoff', $certificate->stats['driver_pickup_dropoff'] ?? '') === 'No' ? 'checked' : '' }}>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center gap-2 mb-2">
                                            <i class="fa-sharp fa-building text-secondary"></i>
                                            <span class="text-lg font-medium text-base-content">Dropped Off at
                                                Warehouse</span>
                                        </div>
                                        <p class="text-sm text-base-content/70">Client brought items to our facility</p>
                                    </div>
                                </label>
                            </div>

                            @error('pickup_dropoff')
                                <div class="alert alert-error">
                                    <i class="fa-sharp fa-circle-exclamation"></i>
                                    <span>{{ $message }}</span>
                                </div>
                            @enderror
                        </div>

                        <!-- Navigation -->
                        <div class="divider my-8">
                            <span class="text-base-content/50 font-medium">Continue to next step</span>
                        </div>


                        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                            <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg w-full sm:w-auto">
                                Continue to Step 2
                                <i class="fa-sharp fa-arrow-right"></i>
                            </button>
                        </div>

                    </form>

                </div>


            </div>
                            <div class="p-6 space-y-6">
                    <a href="{{ route('mobile.certificates.index') }}"
                        class="btn btn-outline btn-lg gap-2 w-full sm:w-auto">
                        <i class="fa-sharp fa-arrow-left"></i>
                        Back to Start
                    </a>
                </div>
        </div>
    </div>
</x-app-layout>
