<x-app-layout
    page-title="Step 5: Employee Signature"
    page-icon="fa-sharp fa-user-pen"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'EZ Custody', 'route' => 'mobile.certificates.index', 'icon' => 'fa-hard-drive'],
        ['name' => 'Step 5', 'icon' => 'fa-user-pen']
    ]">

    <div class="py-6 lg:py-8">
        <div class="mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <ul class="steps steps-horizontal w-full mb-8">
                        <li class="step step-primary">Transfer</li>
                        <li class="step step-primary">Method</li>
                        <li class="step step-primary">Manifest</li>
                        <li class="step step-primary">Client</li>
                        <li class="step step-primary">Employee</li>
                    </ul>

                    <h2 class="card-title text-xl text-primary mb-4">Step 5: Employee Signature</h2>

                    <!-- Certificate Info -->
                    <div class="bg-base-200 p-4 rounded-lg mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium">Certificate:</span> {{ $certificate->certificate_number }}
                            </div>
                            <div>
                                <span class="font-medium">Customer:</span> {{ $certificate->customer->name }}
                            </div>
                            <div>
                                <span class="font-medium">Accepted Date:</span>
                                {{ $certificate->pickup_dropoff_date ? $certificate->pickup_dropoff_date->format('m/d/Y g:i A') : 'N/A' }}
                            </div>
                            <div>
                                <span class="font-medium">Status:</span>
                                <span class="badge badge-sm badge-primary">{{ $certificate->getFormattedStatus() }}</span>
                            </div>
                        </div>
                    </div>

        <form action="{{ route('mobile.certificates.saveStep5', $certificate) }}" method="POST" id="signatureForm">
            @csrf
            <input type="hidden" name="role" value="{{ $role }}">

            <div class="form-control mb-4">
                <label class="label">
                    <span class="label-text font-medium text-base-content">{{ $isDropOff ? 'Receiving Employee Name' : 'Driver Name' }}</span>
                </label>
                <input type="text" name="signatory_name" id="signatory_name" class="input input-bordered w-full bg-base-100" required value="{{ old('signatory_name') }}" placeholder="Enter your full name">
                @error('signatory_name')
                    <div class="alert alert-error mt-2">
                        <i class="fa-sharp fa-circle-exclamation"></i>
                        <span>{{ $message }}</span>
                    </div>
                @enderror
            </div>

            <div class="form-control mb-6">
                <label class="label">
                    <span class="label-text font-medium text-base-content">Agreement</span>
                </label>

                <!-- Key Points Section -->
                <div class="bg-info/10 border border-info/20 p-4 rounded-lg mb-4" id="key_points_display" style="display: none;">
                    <h4 class="font-semibold text-info mb-3 flex items-center gap-2">
                        <i class="fa-sharp fa-list-check"></i>
                        Key Points
                    </h4>
                    <ul class="list-disc list-inside space-y-1 text-sm text-base-content" id="key_points_list">
                        <!-- Key points will be populated here -->
                    </ul>
                </div>

                <div class="bg-base-200 p-4 rounded-lg max-h-60 overflow-y-auto mb-4 text-base-content" id="contract_display">
                    Loading contract...
                </div>
                <input type="hidden" name="contract_text" id="contract_text" value="">
            </div>

            <div class="form-control mb-6">
                <label class="label">
                    <span class="label-text font-medium text-base-content">Employee Signature</span>
                </label>
                <p class="text-sm mb-2 text-base-content/70">Please sign below using your finger or a stylus.</p>
                <div class="">
                    <canvas id="signature-pad" class="signature-pad bg-base-100 p-2 rounded-lg border border-base-300" width="600" height="200"></canvas>
                </div>
                <input type="hidden" name="signature_data" id="signature_data">
                @error('signature_data')
                    <div class="alert alert-error mt-2">
                        <i class="fa-sharp fa-circle-exclamation"></i>
                        <span>{{ $message }}</span>
                    </div>
                @enderror

                <div class="mt-2">
                    <button type="button" id="clear-signature" class="btn btn-sm btn-neutral gap-2">
                        <i class="fa-sharp fa-eraser"></i>
                        Clear Signature
                    </button>
                </div>
            </div>

                    <!-- Navigation -->
                    <div class="divider my-8">
                        <span class="text-base-content/50 font-medium">Complete the process</span>
                    </div>

                    <div class="flex flex-col sm:flex-row justify-between items-center gap-4">

                        <button type="submit" class="btn btn-success btn-lg gap-2 shadow-lg w-full sm:w-auto" id="submit-button">
                            Complete Certificate
                            <i class="fa-sharp fa-check"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <div class="p-6 space-y-6">
            <a href="{{ route('mobile.certificates.step4', $certificate) }}" class="btn btn-outline btn-lg gap-2 w-full sm:w-auto">
                <i class="fa-sharp fa-arrow-left"></i>
                Back to Step 4
            </a>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.1.7/dist/signature_pad.umd.min.js"></script>
<script>
    let signaturePad;

    document.addEventListener('DOMContentLoaded', function() {
        const canvas = document.getElementById('signature-pad');
        signaturePad = new SignaturePad(canvas);

        // Clear signature button
        document.getElementById('clear-signature').addEventListener('click', function() {
            signaturePad.clear();
        });

        // Load contract dynamically for employee
        loadContractForEmployee();

        // Update contract text with signatory name
        const signatoryNameInput = document.getElementById('signatory_name');

        signatoryNameInput.addEventListener('input', function() {
            updateContractWithName();
        });

        // Form submission
        document.getElementById('signatureForm').addEventListener('submit', function(e) {
            if (signaturePad.isEmpty()) {
                e.preventDefault();
                alert('Please provide a signature before proceeding.');
                return false;
            }

            // Get the signature data as a data URL
            const signatureData = signaturePad.toDataURL();
            document.getElementById('signature_data').value = signatureData;

            // Update contract text with actual name before submission
            updateContractWithName();
        });
    });

    function loadContractForEmployee() {
        const serviceSelection = "{{ $certificate->service_selection }}";
        const employeeRole = "{{ $role }}"; // This will be either 'driver' or 'warehouse'
        const contractUrl = `{{ route('certificates.contract') }}?role=${employeeRole}&service_selection=${serviceSelection}`;

        console.log('Loading employee contract from:', contractUrl);
        console.log('Employee role:', employeeRole);

        // Fetch contract content for employee role
        fetch(contractUrl)
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Contract data received:', data);
                window.contractData = data; // Store for later use
                updateContractWithName();
            })
            .catch(error => {
                console.error("Error loading contract:", error);
                document.getElementById('contract_display').innerHTML = `<p>Error loading contract: ${error.message}</p>`;
            });
    }

    function updateContractWithName() {
        if (!window.contractData) return;

        const signatoryNameInput = document.getElementById('signatory_name');
        const contractDisplay = document.getElementById('contract_display');
        const contractText = document.getElementById('contract_text');

        const signatoryName = signatoryNameInput.value.trim() || '[NAME]';
        const currentDate = new Date().toLocaleDateString();

        // Get certificate data for placeholders
        const clientManifestTotalCount = "{{ $certificate->client_manifest_drive_count ?? 0 }}";
        const etechVerifiedTotalCount = "{{ $certificate->etech_verified_drive_count ?? 0 }}";

        // Get service selection text for display
        const serviceSelectionText = "{{ match($certificate->service_selection) {
            'certified_destruction' => 'Certified Destruction with Device & Drive Serialization',
            'uncertified_destruction' => 'Data Device Shredding WITHOUT CERTIFICATION',
            'decline_destruction' => 'Data Destruction Services DECLINED',
            default => $certificate->service_selection ?? ''
        } }}";

        // Build key points HTML if available
        if (window.contractData.key_points && window.contractData.key_points.length > 0) {
            const keyPointsList = document.getElementById('key_points_list');
            const keyPointsDisplay = document.getElementById('key_points_display');

            keyPointsList.innerHTML = '';
            window.contractData.key_points.forEach(point => {
                const li = document.createElement('li');
                li.innerHTML = point
                    .replace(/\[SIGNATORY_NAME\]/g, signatoryName)
                    .replace(/\[NAME\]/g, signatoryName)
                    .replace(/\[SERVICE_SELECTION\]/g, serviceSelectionText)
                    .replace(/\[CLIENT_MANIFEST_TOTAL_COUNT\]/g, clientManifestTotalCount)
                    .replace(/\[ETECH_VERIFIED_TOTAL_COUNT\]/g, etechVerifiedTotalCount)
                    .replace(/\[CURRENT_DATE\]/g, currentDate);
                keyPointsList.appendChild(li);
            });
            keyPointsDisplay.style.display = 'block';
        } else {
            document.getElementById('key_points_display').style.display = 'none';
        }

        // Build contract HTML
        let contractHTML = '';

        if (window.contractData.section) {
            contractHTML = `<h3>${window.contractData.section}</h3><h4>${window.contractData.title}</h4>`;
        } else {
            contractHTML = `<h3>${window.contractData.title}</h3>`;
        }

        // Check if we have the text field (new format) or paragraphs (old format)
        if (window.contractData.text) {
            // New format with single text field
            contractHTML += `<div>${window.contractData.text
                .replace(/\[SIGNATORY_NAME\]/g, signatoryName)
                .replace(/\[NAME\]/g, signatoryName)
                .replace(/\[SERVICE_SELECTION\]/g, serviceSelectionText)
                .replace(/\[CLIENT_MANIFEST_TOTAL_COUNT\]/g, clientManifestTotalCount)
                .replace(/\[ETECH_VERIFIED_TOTAL_COUNT\]/g, etechVerifiedTotalCount)
                .replace(/\[CURRENT_DATE\]/g, currentDate)}</div>`;
        } else {
            // Old format with paragraphs array
            window.contractData.paragraphs.forEach(paragraph => {
                contractHTML += `<p>${paragraph
                    .replace(/\[NAME\]/g, signatoryName)
                    .replace(/\[SIGNATORY_NAME\]/g, signatoryName)
                    .replace(/\[SERVICE_SELECTION\]/g, serviceSelectionText)
                    .replace(/\[CLIENT_MANIFEST_TOTAL_COUNT\]/g, clientManifestTotalCount)
                    .replace(/\[ETECH_VERIFIED_TOTAL_COUNT\]/g, etechVerifiedTotalCount)
                    .replace(/\[CURRENT_DATE\]/g, currentDate)}</p>`;
            });
        }

        contractDisplay.innerHTML = contractHTML;
        contractText.value = contractHTML;
    }
</script>
@endpush
</x-app-layout>
