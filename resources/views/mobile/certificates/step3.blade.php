<x-app-layout page-title="Step 3: Manifest Information" page-icon="fa-sharp fa-list-check" :breadcrumbs="[
    ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
    ['name' => 'EZ Custody', 'route' => 'mobile.certificates.index', 'icon' => 'fa-hard-drive'],
    ['name' => 'Step 3', 'icon' => 'fa-list-check'],
]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Progress Steps -->
            <div class="card bg-base-100 shadow-md">
                <div class="p-6">
                    <ul class="steps steps-horizontal w-full">
                        <li class="step step-primary">Transfer</li>
                        <li class="step step-primary">Method</li>
                        <li class="step step-primary">Manifest</li>
                        <li class="step">Client</li>
                        <li class="step">Employee</li>
                    </ul>
                </div>
            </div>

            <!-- Certificate Information -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-info text-info-content w-8 rounded-lg">
                                <i class="fa-sharp fa-info-circle text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Certificate Information</h4>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-file-certificate text-primary"></i>
                            <span class="font-medium">Certificate:</span>
                            <span class="font-mono">{{ $certificate->certificate_number }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user text-secondary"></i>
                            <span class="font-medium">Customer:</span>
                            <span>{{ $certificate->customer->name }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-calendar-clock text-accent"></i>
                            <span class="font-medium">Accepted:</span>
                            <span>{{ $certificate->pickup_dropoff_date ? $certificate->pickup_dropoff_date->format('m/d/Y g:i A') : 'N/A' }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-hammer text-warning"></i>
                            <span class="font-medium">Destruction:</span>
                            <span>{{ $certificate->stats['client_certification_requirement'] ?? 'Not Set' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3 Form -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-accent text-accent-content w-8 rounded-lg">
                                <i class="fa-sharp fa-list-check text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Step 3: Manifest Information</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    <p class="text-base-content/70">
                        Did the client provide a manifest listing the drives to be destroyed? This section is only applicable if the client selected a "Certified" destruction method.
                    </p>

                    <form action="{{ route('mobile.certificates.saveStep3', $certificate) }}" method="POST"
                        id="manifestForm" class="space-y-6">
                        @csrf

                        <div id="manifest-questions-container" class="space-y-4">
                            <!-- Yes Option -->
                            <div class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-success/30 transition-all duration-200 cursor-pointer">
                                <label class="flex items-start cursor-pointer p-4">
                                    <input type="radio" name="manifest_provided" value="Yes" class="radio radio-primary mt-1 mr-3 flex-shrink-0" required
                                        {{ old('manifest_provided', ($certificate->stats['client_provided_count'] ?? '') === 'Verified Manifest Provided' ? 'Yes' : '') === 'Yes' ? 'checked' : '' }}
                                        onchange="toggleManifestFields()">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center gap-2 mb-2">
                                            <i class="fa-sharp fa-check-circle text-success"></i>
                                            <span class="text-lg font-medium text-base-content">Yes - Manifest Provided</span>
                                        </div>
                                        <p class="text-sm text-base-content/70">Client has provided a list of drives to be destroyed</p>
                                    </div>
                                </label>
                            </div>

                            <!-- No Option -->
                            <div class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-warning/30 transition-all duration-200 cursor-pointer">
                                <label class="flex items-start cursor-pointer p-4">
                                    <input type="radio" name="manifest_provided" value="No" class="radio radio-primary mt-1 mr-3 flex-shrink-0" required
                                        {{ old('manifest_provided', ($certificate->stats['client_provided_count'] ?? '') === 'No Manifest Provided' ? 'No' : '') === 'No' ? 'checked' : '' }}
                                        onchange="toggleManifestFields()">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center gap-2 mb-2">
                                            <i class="fa-sharp fa-times-circle text-warning"></i>
                                            <span class="text-lg font-medium text-base-content">No - No Manifest Provided</span>
                                        </div>
                                        <p class="text-sm text-base-content/70">Client has not provided a list of drives</p>
                                    </div>
                                </label>
                            </div>

                            @error('manifest_provided')
                                <div class="alert alert-error mt-2">
                                    <i class="fa-sharp fa-circle-exclamation"></i>
                                    <span>{{ $message }}</span>
                                </div>
                            @enderror
                        </div>

                        <div id="count-fields-container" class="mb-6 hidden">
                            <!-- Total Assets Section -->
                            <div class="card bg-base-200 p-4 mb-4">
                                <h5 class="text-lg font-semibold text-base-content mb-4 flex items-center">
                                    <i class="fa-sharp fa-list-ol text-primary mr-2"></i>
                                    Total Assets Count
                                </h5>
                                <div class="alert alert-info mb-4">
                                    <i class="fa-sharp fa-info-circle mr-2"></i>
                                    <div>
                                        <p class="font-medium">Asset Counting Rules:</p>
                                        <ul class="text-sm mt-1 list-disc list-inside space-y-1">
                                            <li>Each loose drive = 1 billable unit</li>
                                            <li>Each device (computer, server, etc.) with drives = 1 unit per drive inside</li>
                                            <li>Each device without drives = 1 billable unit</li>
                                            <li>All assets must be serialized and countable at time of transfer</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium text-base-content">Client Manifest Total Assets</span>
                                        </label>
                                        <p class="text-sm mb-2 text-base-content/70">Total billable units on client manifest (drives + devices).</p>
                                        <input type="number" name="client_total_assets"
                                            class="input input-bordered w-full bg-base-100"
                                            value="{{ old('client_total_assets', $certificate->client_manifest_drive_count ?? 0) }}"
                                            min="0">
                                        @error('client_total_assets')
                                            <div class="alert alert-error mt-2">
                                                <i class="fa-sharp fa-circle-exclamation"></i>
                                                <span>{{ $message }}</span>
                                            </div>
                                        @enderror
                                    </div>

                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium text-base-content">E-Tech Verified Total Assets</span>
                                        </label>
                                        <p class="text-sm mb-2 text-base-content/70">Total billable units verified by E-Tech on site.</p>
                                        <input type="number" name="etech_verified_assets"
                                            class="input input-bordered w-full bg-base-100"
                                            value="{{ old('etech_verified_assets', $certificate->etech_verified_drive_count ?? 0) }}"
                                            min="0">
                                        @error('etech_verified_assets')
                                            <div class="alert alert-error mt-2">
                                                <i class="fa-sharp fa-circle-exclamation"></i>
                                                <span>{{ $message }}</span>
                                            </div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation -->
                        <div class="divider my-8">
                            <span class="text-base-content/50 font-medium">Continue to next step</span>
                        </div>

                        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">

                            <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg w-full sm:w-auto">
                                Continue to Step 4
                                <i class="fa-sharp fa-arrow-right"></i>
                            </button>
                        </div>
                    </form>
                </div>

            </div>
            <div class="p-6 space-y-6">
                <a href="{{ route('mobile.certificates.step2', $certificate) }}"
                    class="btn btn-outline btn-lg gap-2 w-full sm:w-auto">
                    <i class="fa-sharp fa-arrow-left"></i>
                    Back to Step 2
                </a>
            </div>

        </div>
    </div>

    @push('scripts')
        <script>
            function toggleManifestFields() {
                const serviceSelection = "{{ $certificate->service_selection }}";
                const manifestQuestionsContainer = document.getElementById('manifest-questions-container');
                const countFieldsContainer = document.getElementById('count-fields-container');
                const manifestProvidedRadioYes = document.querySelector('input[name="manifest_provided"][value="Yes"]');
                const manifestProvidedRadioNo = document.querySelector('input[name="manifest_provided"][value="No"]');

                // Hide all manifest related sections by default
                manifestQuestionsContainer.classList.add('hidden');
                countFieldsContainer.classList.add('hidden');

                // Only show manifest questions if service selection is certified
                if (serviceSelection === 'certified_destruction' || serviceSelection === 'certified_drive_serialization' || serviceSelection === 'certified_drive_origin_serialization') {
                    manifestQuestionsContainer.classList.remove('hidden');

                    // If 'Yes' is selected for manifest provided, show count fields
                    if (manifestProvidedRadioYes && manifestProvidedRadioYes.checked) {
                        countFieldsContainer.classList.remove('hidden');
                    }
                } else {
                    // If not a certified service, ensure no radio is checked and counts are reset
                    if (manifestProvidedRadioYes) manifestProvidedRadioYes.checked = false;
                    if (manifestProvidedRadioNo) manifestProvidedRadioNo.checked = false;
                    const clientAssetsField = document.querySelector('input[name="client_total_assets"]');
                    const etechAssetsField = document.querySelector('input[name="etech_verified_assets"]');
                    if (clientAssetsField) clientAssetsField.value = 0;
                    if (etechAssetsField) etechAssetsField.value = 0;
                }
            }

            // Initialize on page load
            document.addEventListener('DOMContentLoaded', function() {
                toggleManifestFields();
            });
        </script>
    @endpush
</x-app-layout>
