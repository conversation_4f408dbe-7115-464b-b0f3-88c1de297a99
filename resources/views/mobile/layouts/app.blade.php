<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-theme="light">
<x-head :title="$title ?? config('app.name', 'ETRFlow')" />

<body class="font-sans antialiased bg-base-200 text-base-content">
    <!-- Header -->
    <header class="header-scroll bg-base-100 shadow-md">
        <div class="navbar px-4 py-2">
            <div class="flex-1">
                <h1 class="text-xl font-bold">
                    @yield('title', 'Certificate of Destruction')
                </h1>
            </div>
            <div class="flex-none">
                @auth
                    <div class="dropdown dropdown-end">
                        <label tabindex="0" class="btn btn-ghost btn-circle avatar">
                            <div class="w-10 rounded-full bg-primary text-primary-content flex items-center justify-center">
                                <span class="text-lg font-bold">{{ substr(Auth::user()->name, 0, 1) }}</span>
                            </div>
                        </label>
                        <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                            <li class="font-semibold">
                                <span>{{ Auth::user()->name }}</span>
                            </li>
                            <li>
                                <a href="{{ route('dashboard') }}">
                                    <i class="fa-sharp fa-tachometer-alt mr-2"></i> Dashboard
                                </a>
                            </li>
                            <li>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="w-full text-left">
                                        <i class="fa-sharp fa-sign-out-alt mr-2"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                @endauth
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-4">
        @yield('content')
    </main>

    <!-- Toast Messages -->
    @if (session('success'))
        <div id="toast-success" class="toast toast-top toast-end">
            <div class="alert alert-success">
                <i class="fa-sharp fa-circle-check"></i>
                <span>{{ session('success') }}</span>
            </div>
        </div>
    @endif

    @if (session('error'))
        <div id="toast-error" class="toast toast-top toast-end">
            <div class="alert alert-error">
                <i class="fa-sharp fa-circle-xmark"></i>
                <span>{{ session('error') }}</span>
            </div>
        </div>
    @endif

    <!-- Modals -->
    @stack('modals')

    <!-- Scripts -->
    <script>
        // Hide header on scroll down, show on scroll up
        let lastScrollTop = 0;
        const header = document.querySelector('.header-scroll');

        window.addEventListener('scroll', function() {
            let scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > lastScrollTop && scrollTop > 60) {
                // Scrolling down
                header.classList.add('hidden');
            } else {
                // Scrolling up
                header.classList.remove('hidden');
            }

            lastScrollTop = scrollTop;
        });

        // Auto-hide toast messages
        document.addEventListener('DOMContentLoaded', function() {
            const toastSuccess = document.getElementById('toast-success');
            const toastError = document.getElementById('toast-error');

            if (toastSuccess) {
                setTimeout(() => {
                    toastSuccess.style.opacity = '0';
                    setTimeout(() => {
                        toastSuccess.style.display = 'none';
                    }, 300);
                }, 3000);
            }

            if (toastError) {
                setTimeout(() => {
                    toastError.style.opacity = '0';
                    setTimeout(() => {
                        toastError.style.display = 'none';
                    }, 300);
                }, 5000);
            }
        });
    </script>

    @stack('scripts')
    @stack('styles')
</body>
</html>
