<x-app-layout
    page-title="Step 3: Item Photos"
    page-icon="fa-sharp fa-camera"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'EZ Payout', 'route' => 'mobile.payouts.index', 'icon' => 'fa-money-bill-transfer'],
        ['name' => 'Step 3', 'icon' => 'fa-camera']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Progress Steps -->
            <div class="card bg-base-100 shadow-md">
                <div class="p-6">
                    <ul class="steps steps-horizontal w-full">
                        <li class="step step-primary">Photo ID</li>
                        <li class="step step-primary">Payout Details</li>
                        <li class="step step-primary">Item Photos</li>
                    </ul>
                </div>
            </div>

            <!-- Current Customer Information -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-success text-success-content w-8 rounded-lg">
                                <i class="fa-sharp fa-user text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Current Customer & Progress</h4>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-hashtag text-primary"></i>
                            <span class="font-medium">Payout #:</span>
                            <span class="font-mono">{{ $mobilePayout->payout_number }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user text-secondary"></i>
                            <span class="font-medium">Customer:</span>
                            <span>{{ $mobilePayout->customer->name }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-check-circle text-success"></i>
                            <span class="font-medium">Step 1:</span>
                            <span class="badge badge-success badge-sm">ID Verified</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-check-circle text-success"></i>
                            <span class="font-medium">Step 2:</span>
                            <span class="badge badge-success badge-sm">Details Set</span>
                        </div>
                    </div>

                    <!-- License Upload Status -->
                    @php
                        $step1Data = $mobilePayout->getStepData('step1');
                    @endphp
                    @if(!empty($step1Data) && $step1Data['id_verified'])
                    <div class="divider my-4"></div>
                    <div class="bg-success/10 border border-success/20 rounded-lg p-4">
                        <div class="flex items-start gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-6 rounded">
                                    <i class="fa-sharp fa-check text-xs"></i>
                                </div>
                            </div>
                            <div class="text-sm text-base-content/80">
                                <p class="font-medium mb-1">Photo ID Verified</p>
                                <p>Customer ID verification completed on {{ \Carbon\Carbon::parse($step1Data['verification_date'])->format('M j, Y g:i A') }}</p>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Payout Summary -->
                    @php
                        $step2Data = $mobilePayout->getStepData('step2');
                    @endphp
                    @if(!empty($step2Data))
                    <div class="divider my-4"></div>
                    <div class="bg-base-200/50 rounded-lg p-4">
                        <h5 class="font-semibold text-base-content mb-2">Payout Summary:</h5>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                            <div>
                                <strong>Item:</strong>
                                @if($step2Data['product_type'] === 'custom')
                                    {{ $step2Data['custom_product_name'] }}
                                @else
                                    @php
                                        $product = \App\Models\Inventory::find($step2Data['product_type']);
                                    @endphp
                                    {{ $product ? $product->name : 'Unknown Product' }}
                                @endif
                            </div>
                            <div><strong>Amount:</strong> ${{ number_format($step2Data['payout_amount'], 2) }}</div>
                            <div><strong>Quantity:</strong> {{ $step2Data['quantity'] }}</div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Step 4 Form -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-warning/10 to-warning/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-warning text-warning-content w-8 rounded-lg">
                                <i class="fa-sharp fa-camera text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Step 3: Item Photos</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    <div class="bg-info/10 border border-info/20 rounded-lg p-4">
                        <div class="flex items-start gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-info text-info-content w-6 rounded">
                                    <i class="fa-sharp fa-camera text-xs"></i>
                                </div>
                            </div>
                            <div class="text-sm text-base-content/80">
                                <p class="font-medium mb-1">Photo Documentation (Optional)</p>
                                <p>Take clear photos of the items you're paying for. This provides documentation for the transaction. You can upload multiple photos or skip this step if photos aren't needed.</p>
                            </div>
                        </div>
                    </div>

                    <form action="{{ route('mobile.payouts.saveStep3', $mobilePayout) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                        @csrf

                        <!-- Item Photos Upload -->
                        <x-image-dropzone
                            id="mobile-payout-photos"
                            name="item_photos[]"
                            :max-files="10"
                            :max-filesize="10"
                            :max-width="1250"
                            :max-height="1250"
                            :client-resize="true"
                            accepted-formats="image/jpeg,image/jpg,image/png,image/webp"
                            :multiple="true"
                            capture="environment"
                            :trigger-camera-upload="true"
                            label="Item Photos (Optional)"
                            help-text="Take photos of the items being paid for. On mobile devices, tap below to open your camera directly."
                            sub-text="Images will be automatically compressed to optimize upload speed."
                        />

                        <!-- Navigation -->
                        <div class="divider my-8">
                            <span class="text-base-content/50 font-medium">Complete the payout transaction</span>
                        </div>

                        <div class="flex justify-center">
                            <button type="submit" class="btn btn-success btn-lg gap-2 shadow-lg w-full sm:w-auto">
                                <i class="fa-sharp fa-check-circle"></i>
                                Complete Payout
                            </button>
                        </div>
                    </form>

                    <!-- Skip Photos Option -->
                    <div class="text-center">
                        <p class="text-sm text-base-content/60 mb-3">Don't need to upload photos?</p>
                        <form action="{{ route('mobile.payouts.saveStep3', $mobilePayout) }}" method="POST">
                            @csrf
                            <input type="hidden" name="skip_photos" value="1">
                            <button type="submit" class="btn btn-ghost btn-sm gap-2">
                                <i class="fa-sharp fa-check-circle"></i>
                                Skip Photos & Complete Payout
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Back Button -->
            <div class="flex justify-center mt-4">
                <a href="{{ route('mobile.payouts.step2', $mobilePayout) }}" class="btn btn-ghost btn-sm gap-2">
                    <i class="fa-sharp fa-arrow-left"></i>
                    Back to Step 2
                </a>
            </div>
        </div>
    </div>

</x-app-layout>
