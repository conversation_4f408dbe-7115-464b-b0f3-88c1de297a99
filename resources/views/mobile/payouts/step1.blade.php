<x-app-layout
    page-title="Step 1: Photo ID Verification"
    page-icon="fa-sharp fa-id-card"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'EZ Payout', 'route' => 'mobile.payouts.index', 'icon' => 'fa-money-bill-transfer'],
        ['name' => 'Step 1', 'icon' => 'fa-id-card']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Progress Steps -->
            <div class="card bg-base-100 shadow-md">
                <div class="p-6">
                    <ul class="steps steps-horizontal w-full">
                        <li class="step step-primary">Photo ID</li>
                        <li class="step">Payout Details</li>
                        <li class="step">Item Photos</li>
                    </ul>
                </div>
            </div>

            <!-- Current Customer Information -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-success text-success-content w-8 rounded-lg">
                                <i class="fa-sharp fa-user text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Current Customer</h4>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-hashtag text-primary"></i>
                            <span class="font-medium">Payout #:</span>
                            <span class="font-mono">{{ $mobilePayout->payout_number }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user text-secondary"></i>
                            <span class="font-medium">Customer:</span>
                            <span>{{ $mobilePayout->customer->name }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-check-circle text-success"></i>
                            <span class="font-medium">Step 1:</span>
                            <span class="badge badge-success badge-sm">Completed</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2 Form -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-id-card text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Step 1: Photo ID Verification</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    @if($hasExistingId)
                        <div class="bg-info/10 border border-info/20 rounded-lg p-4">
                            <div class="flex items-start gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-info text-info-content w-6 rounded">
                                        <i class="fa-sharp fa-info-circle text-xs"></i>
                                    </div>
                                </div>
                                <div class="text-sm text-base-content/80">
                                    <p class="font-medium mb-1">Photo ID Found on File</p>
                                    <p>We have {{ count($existingPhotoIds) }} photo ID(s) on file for this customer. You can proceed with the existing ID or upload a new one.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Display existing photo IDs with thumbnails -->
                        <div class="space-y-4">
                            <h5 class="font-semibold text-base-content">Existing Photo IDs:</h5>

                            <!-- Thumbnail Grid -->
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach($existingPhotoIds as $photoId)
                                <div class="bg-base-100 border border-base-300 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    @if($photoId->is_image)
                                        <!-- Image Thumbnail -->
                                        <div class="mb-3">
                                            <img src="{{ $photoId->getThumbnailUrl(200, 120) }}"
                                                 alt="Customer ID: {{ $photoId->original_filename }}"
                                                 class="w-full h-30 object-cover rounded-lg border border-base-200">
                                        </div>
                                    @else
                                        <!-- Non-image file icon -->
                                        <div class="w-full h-30 bg-base-200 rounded-lg border border-base-300 flex items-center justify-center mb-3">
                                            <div class="text-center">
                                                <i class="fa-sharp fa-file-pdf text-4xl text-error mb-2"></i>
                                                <p class="text-xs text-base-content/60">PDF Document</p>
                                            </div>
                                        </div>
                                    @endif

                                    <!-- File Info -->
                                    <div class="space-y-1">
                                        <div class="font-medium text-sm truncate" title="{{ $photoId->original_filename }}">
                                            {{ $photoId->original_filename }}
                                        </div>
                                        @if($photoId->description)
                                        <div class="text-xs text-base-content/70 truncate" title="{{ $photoId->description }}">
                                            {{ $photoId->description }}
                                        </div>
                                        @endif
                                        <div class="text-xs text-base-content/50">
                                            {{ $photoId->created_at->format('M j, Y g:i A') }}
                                        </div>
                                    </div>


                                </div>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <div class="bg-warning/10 border border-warning/20 rounded-lg p-4">
                            <div class="flex items-start gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-warning text-warning-content w-6 rounded">
                                        <i class="fa-sharp fa-exclamation-triangle text-xs"></i>
                                    </div>
                                </div>
                                <div class="text-sm text-base-content/80">
                                    <p class="font-medium mb-1">No Photo ID on File</p>
                                    <p>This customer does not have a photo ID on file. Please upload a photo of their driver's license or government-issued ID.</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <form action="{{ route('mobile.payouts.saveStep1', $mobilePayout) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                        @csrf

                        <!-- Photo ID Upload -->
                        <div>
                            <label class="label" for="photo_id">
                                <span class="label-text">
                                    <i class="fa-sharp fa-camera text-primary mr-2"></i>
                                    @if($hasExistingId)
                                        Upload New Photo ID (Optional)
                                    @else
                                        Upload Photo ID <span class="text-error font-bold">*</span>
                                    @endif
                                </span>
                            </label>
                            <p class="text-base-content/70 text-sm mb-3">
                                Take a clear photo of the customer's driver's license or government-issued photo ID. On mobile devices, tap the button below to open your camera directly. Ensure all text is readable.
                                <br><span class="text-info font-medium">Images will be automatically compressed to optimize upload speed.</span>
                            </p>
                            <input type="file"
                                   name="photo_id"
                                   id="photo_id"
                                   class="file-input file-input-bordered w-full"
                                   accept="image/*,application/pdf"
                                   capture="environment"
                                   @if(!$hasExistingId) required @endif>
                            <div class="label">
                                <span class="label-text-alt text-base-content/60" id="photoIdInputLabel">
                                    <i class="fa-sharp fa-camera mr-1"></i>
                                    Tap to open camera
                                </span>
                            </div>
                            @error('photo_id')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror

                            <!-- Photo Preview -->
                            <div id="photoPreview" class="mt-3 hidden">
                                <p class="text-xs text-base-content/60 mb-2">Selected photo:</p>
                                <div id="photoPreviewContainer" class="inline-block bg-base-100 border border-base-300 rounded-lg p-2">
                                    <!-- Preview will be inserted here -->
                                </div>
                            </div>
                        </div>

                        <!-- Navigation -->
                        <div class="divider my-8">
                            <span class="text-base-content/50 font-medium">Continue to payout details</span>
                        </div>

                        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">

                            <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg w-full sm:w-auto">
                                Continue to Step 2
                                <i class="fa-sharp fa-arrow-right"></i>
                            </button>
                        </div>
                    </form>
                </div>

            </div>
            <!-- Back Button -->
            <div class="flex justify-center mt-4">
                <a href="{{ route('mobile.payouts.index', $mobilePayout) }}" class="btn btn-ghost btn-sm gap-2">
                    <i class="fa-sharp fa-arrow-left"></i>
                    Back to Beginning
                </a>
            </div>
        </div>
    </div>

    @vite('resources/js/mobile-payout-compression.js')

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const photoIdInput = document.getElementById('photo_id');
            const photoIdInputLabel = document.getElementById('photoIdInputLabel');
            const photoPreview = document.getElementById('photoPreview');
            const photoPreviewContainer = document.getElementById('photoPreviewContainer');

            // Check if device is mobile and optimize for camera
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            if (isMobile && photoIdInput) {
                // For mobile devices, optimize for camera use - single file only
                // Remove PDF from accept attribute since camera capture doesn't support PDF
                photoIdInput.setAttribute('accept', 'image/*');
                
                // Update UI text for mobile
                if (photoIdInputLabel) {
                    photoIdInputLabel.innerHTML = '<i class="fa-sharp fa-camera mr-1"></i>Tap to open camera';
                }
            }

            // Handle photo preview
            if (photoIdInput) {
                photoIdInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    
                    if (file && file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        
                        reader.onload = function(e) {
                            photoPreviewContainer.innerHTML = `
                                <img src="${e.target.result}" 
                                     alt="Selected photo ID" 
                                     class="w-30 h-20 object-cover rounded border border-base-200">
                                <div class="text-xs text-base-content/60 mt-1 text-center truncate" title="${file.name}">
                                    ${file.name}
                                </div>
                            `;
                            photoPreview.classList.remove('hidden');
                        };
                        
                        reader.readAsDataURL(file);
                    } else {
                        // Hide preview if no valid image is selected
                        photoPreview.classList.add('hidden');
                        photoPreviewContainer.innerHTML = '';
                    }
                });
            }
        });
    </script>
    @endpush
</x-app-layout>
