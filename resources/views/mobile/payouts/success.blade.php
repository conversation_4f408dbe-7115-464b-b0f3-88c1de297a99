<x-app-layout
    page-title="Payout Completed Successfully"
    page-icon="fa-sharp fa-check-circle"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'EZ Payout', 'route' => 'mobile.payouts.index', 'icon' => 'fa-money-bill-transfer'],
        ['name' => 'Success', 'icon' => 'fa-check-circle']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Success Message -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-success text-success-content w-8 rounded-lg">
                                <i class="fa-sharp fa-check-circle text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Payout Completed Successfully!</h4>
                    </div>
                </div>

                <div class="p-6 text-center">
                    <div class="avatar avatar-placeholder mb-4">
                        <div class="bg-success text-success-content w-16 rounded-full">
                            <i class="fa-sharp fa-check text-2xl"></i>
                        </div>
                    </div>
                    <h2 class="text-2xl font-bold text-base-content mb-2">Transaction Complete!</h2>
                    <p class="text-base-content/70 mb-6">
                        The payout has been successfully processed and recorded in the system.
                    </p>

                    <div class="bg-success/10 border border-success/20 rounded-lg p-4 mb-6">
                        <div class="flex items-center justify-center gap-3 mb-2">
                            <i class="fa-sharp fa-file-invoice-dollar text-success text-lg"></i>
                            <span class="font-semibold text-base-content">Invoice Generated</span>
                        </div>
                        <p class="text-sm text-base-content/80">
                            Invoice #{{ $mobilePayout->invoice->id }} has been created and marked as paid.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Transaction Summary -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-info text-info-content w-8 rounded-lg">
                                <i class="fa-sharp fa-file-invoice text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Transaction Summary</h4>
                    </div>
                </div>

                <div class="p-6">
                    @php
                        $step2Data = $mobilePayout->getStepData('step2');
                    @endphp

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Payout Information -->
                        <div class="space-y-4">
                            <h5 class="font-semibold text-base-content border-b border-base-300 pb-2">Payout Information</h5>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="font-medium">Payout Number:</span>
                                    <span class="font-mono">{{ $mobilePayout->payout_number }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium">Invoice Number:</span>
                                    <span class="font-mono">{{ $mobilePayout->invoice->id }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium">Date Completed:</span>
                                    <span>{{ $mobilePayout->completed_at->format('M j, Y g:i A') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium">Status:</span>
                                    <span class="badge badge-success">{{ $mobilePayout->getFormattedStatus() }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Customer & Item Details -->
                        <div class="space-y-4">
                            <h5 class="font-semibold text-base-content border-b border-base-300 pb-2">Customer & Item Details</h5>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="font-medium">Customer:</span>
                                    <span>{{ $mobilePayout->customer->name }}</span>
                                </div>
                                @if(!empty($step2Data))
                                <div class="flex justify-between">
                                    <span class="font-medium">Item:</span>
                                    <span>
                                        @if($step2Data['product_type'] === 'custom')
                                            {{ $step2Data['custom_product_name'] }}
                                        @else
                                            @php
                                                $product = \App\Models\Inventory::find($step2Data['product_type']);
                                            @endphp
                                            {{ $product ? $product->name : 'Unknown Product' }}
                                        @endif
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium">Quantity:</span>
                                    <span>{{ $step2Data['quantity'] }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium">Rate per Unit:</span>
                                    <span>${{ number_format($step2Data['payout_amount'], 2) }}</span>
                                </div>
                                @php
                                    $totalAmount = $step2Data['quantity'] * $step2Data['payout_amount'];
                                @endphp
                                <div class="flex justify-between">
                                    <span class="font-medium">Total Payout:</span>
                                    <span class="text-lg font-bold text-success">${{ number_format($totalAmount, 2) }}</span>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card bg-base-100 shadow-md">
                <div class="p-6">
                    <div class="flex flex-col gap-4 md:flex-row justify-around">
                        @if($mobilePayout->invoice)
                        <a href="{{ route('invoices.show', $mobilePayout->invoice) }}" class="btn btn-primary btn-lg gap-2 w-full sm:w-auto">
                            <i class="fa-sharp fa-file-invoice"></i>
                            View Invoice
                        </a>
                        @endif
                        <a href="{{ route('mobile.payouts.index') }}" class="btn btn-outline btn-lg gap-2 w-full sm:w-auto">
                            <i class="fa-sharp fa-plus"></i>
                            Create Another Payout
                        </a>
                        <a href="{{ route('dashboard') }}" class="btn btn-success btn-lg gap-2 w-full sm:w-auto">
                            <i class="fa-sharp fa-home"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

        </div>
    </div>
</x-app-layout>
