<x-app-layout
    page-title="EZ Payout"
    page-icon="fa-sharp fa-money-bill-transfer"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'EZ Payout', 'icon' => 'fa-money-bill-transfer']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Introduction Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-money-bill-transfer text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">EZ Customer Payout System</h4>
                    </div>
                </div>

                <div class="p-6">
                    <p class="text-base-content/70 mb-4">
                        This simplified interface will guide you through creating a customer payout with step-by-step verification and documentation.
                    </p>

                    <div class="bg-info/10 border border-info/20 rounded-lg p-4">
                        <div class="flex items-start gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-info text-info-content w-6 rounded">
                                    <i class="fa-sharp fa-info-circle text-xs"></i>
                                </div>
                            </div>
                            <div class="text-sm text-base-content/80">
                                <p class="font-medium mb-1">Quick Start Guide:</p>
                                <ul class="space-y-1 text-xs">
                                    <li>• Select or create a customer</li>
                                    <li>• Verify customer photo ID</li>
                                    <li>• Enter payout details and weight</li>
                                    <li>• Take photos of items</li>
                                    <li>• Review and complete the payout</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Incomplete Payouts Section -->
            @if($incompletePayouts->count() > 0)
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-warning/10 to-warning/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-warning text-warning-content w-8 rounded-lg">
                                <i class="fa-sharp fa-clock text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Resume Incomplete Payouts</h4>
                    </div>
                </div>

                <div class="p-6">
                    <p class="text-base-content/70 mb-4">
                        You have {{ $incompletePayouts->count() }} incomplete payout{{ $incompletePayouts->count() > 1 ? 's' : '' }} in progress. You can resume where you left off or start a new one.
                    </p>

                    <div class="space-y-3">
                        @foreach($incompletePayouts as $payout)
                        <div class="bg-warning/5 border border-warning/20 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-warning text-warning-content w-10 rounded-lg">
                                            <i class="fa-sharp fa-user text-sm"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <p class="font-medium text-base-content">
                                            {{ $payout->customer->business_name ?: $payout->customer->name }}
                                        </p>
                                        <p class="text-sm text-base-content/60">
                                            Started {{ $payout->getTimeSinceCreation() }} • Step {{ $payout->getCurrentStep() }} of 5
                                        </p>
                                        <p class="text-xs text-base-content/50">
                                            Payout #{{ $payout->payout_number }}
                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <div class="text-right">
                                        <div class="text-xs text-base-content/60 mb-1">Progress</div>
                                        <div class="flex items-center gap-2">
                                            <progress class="progress progress-warning w-16 h-2" value="{{ $payout->getProgressPercentage() }}" max="100"></progress>
                                            <span class="text-xs text-base-content/60">{{ number_format($payout->getProgressPercentage()) }}%</span>
                                        </div>
                                    </div>
                                    <a href="{{ $payout->getResumeUrl() }}" class="btn btn-warning btn-sm gap-1">
                                        <i class="fa-sharp fa-play text-xs"></i>
                                        Resume
                                    </a>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            @if($deletedCount > 0)
            <div class="alert alert-info">
                <i class="fa-sharp fa-info-circle"></i>
                <span>{{ $deletedCount }} old incomplete payout{{ $deletedCount > 1 ? 's were' : ' was' }} automatically cleaned up.</span>
            </div>
            @endif

            <form action="{{ route('mobile.payouts.start') }}" method="POST" class="space-y-6">
                @csrf
                
                <!-- Customer Selection Card -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-user text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Select Customer</h4>
                        </div>
                    </div>

                    <div class="p-6 space-y-6">
                        <!-- Customer Selection -->
                        <div>
                            <label class="label" for="customerSearch">
                                <span class="label-text">
                                    <i class="fa-sharp fa-user text-primary mr-2"></i>
                                    Customer <span class="text-error font-bold">*</span>
                                </span>
                            </label>
                            <p class="text-base-content/70 text-sm mb-3">
                                Start typing to search for a customer. If they are not in the system, click the "Create New Customer" button.
                                For business customers, enter their BUSINESS NAME first.
                            </p>

                            <div class="relative">
                                <x-dynamic-customer-search
                                    id="customerSearch"
                                    name="customer_id"
                                    placeholder="Search for customers by name, business name, email, phone, or nickname..."
                                    action="form"
                                    quickCreateButtonId="quickCreateCustomerBtn"
                                />
                                <x-quick-create-customer
                                    id="customerSearch"
                                    name="customer_id"
                                />
                            </div>

                            @error('customer_id')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Submit Section -->
                <div class="card bg-base-100 shadow-md">
                    <div class="p-6">
                        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-success text-success-content w-10 rounded-lg">
                                        <i class="fa-sharp fa-plus text-sm"></i>
                                    </div>
                                </div>
                                <div class="text-center sm:text-left">
                                    <p class="font-medium text-base-content">Start New EZ Payout</p>
                                    <p class="text-sm text-base-content/60">Begin a fresh guided 5-step workflow</p>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg w-full sm:w-auto">
                                <i class="fa-sharp fa-money-bill-transfer"></i>
                                Start New Payout
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</x-app-layout>
