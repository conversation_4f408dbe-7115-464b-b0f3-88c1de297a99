<x-app-layout
    page-title="Step 2: Payout Details"
    page-icon="fa-sharp fa-calculator"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'EZ Payout', 'route' => 'mobile.payouts.index', 'icon' => 'fa-money-bill-transfer'],
        ['name' => 'Step 2', 'icon' => 'fa-calculator']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Progress Steps -->
            <div class="card bg-base-100 shadow-md">
                <div class="p-6">
                    <ul class="steps steps-horizontal w-full">
                        <li class="step step-primary">Photo ID</li>
                        <li class="step step-primary">Payout Details</li>
                        <li class="step">Item Photos</li>
                    </ul>
                </div>
            </div>

            <!-- Current Customer Information -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-success text-success-content w-8 rounded-lg">
                                <i class="fa-sharp fa-user text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Current Customer & Progress</h4>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-hashtag text-primary"></i>
                            <span class="font-medium">Payout #:</span>
                            <span class="font-mono">{{ $mobilePayout->payout_number }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user text-secondary"></i>
                            <span class="font-medium">Customer:</span>
                            <span>{{ $mobilePayout->customer->name }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-check-circle text-success"></i>
                            <span class="font-medium">Step 1:</span>
                            <span class="badge badge-success badge-sm">ID Verified</span>
                        </div>
                    </div>

                    <!-- License Upload Status -->
                    @php
                        $step1Data = $mobilePayout->getStepData('step1');
                        $customer = $mobilePayout->customer;
                        $latestPhotoId = null;
                        if (!empty($step1Data) && $step1Data['id_verified']) {
                            // Get the most recent photo ID for this customer
                            $latestPhotoId = $customer->files()
                                ->where(function($query) {
                                    $query->where('description', 'LIKE', '%photo_id%')
                                          ->orWhere('description', 'LIKE', '%Customer ID%')
                                          ->orWhere('description', 'LIKE', '%ID%')
                                          ->orWhere('metadata->file_type', 'photo_id');
                                })
                                ->orderBy('created_at', 'desc')
                                ->first();
                        }
                    @endphp
                    @if(!empty($step1Data) && $step1Data['id_verified'])
                    <div class="divider my-4"></div>
                    <div class="bg-success/10 border border-success/20 rounded-lg p-4">
                        <div class="flex items-start gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-6 rounded">
                                    <i class="fa-sharp fa-check text-xs"></i>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm text-base-content/80">
                                    <p class="font-medium mb-1">Photo ID Verified</p>
                                    <p>Customer ID verification completed on {{ \Carbon\Carbon::parse($step1Data['verification_date'])->format('M j, Y g:i A') }}</p>
                                </div>

                                @if($latestPhotoId && $latestPhotoId->is_image)
                                <div class="mt-3">
                                    <p class="text-xs text-base-content/60 mb-2">Uploaded ID:</p>
                                    <div class="inline-block bg-base-100 border border-base-300 rounded-lg p-2">
                                        <img src="{{ $latestPhotoId->getThumbnailUrl(120, 80) }}"
                                             alt="Customer ID"
                                             class="w-30 h-20 object-cover rounded border border-base-200">
                                        <div class="text-xs text-base-content/60 mt-1 text-center truncate" title="{{ $latestPhotoId->original_filename }}">
                                            {{ $latestPhotoId->original_filename }}
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Step 3 Form -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-accent text-accent-content w-8 rounded-lg">
                                <i class="fa-sharp fa-calculator text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Step 2: Payout Details</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    <p class="text-base-content/70">
                        Select what you're paying for and enter the quantity and amount per unit.
                    </p>

                    <form action="{{ route('mobile.payouts.saveStep2', $mobilePayout) }}" method="POST" class="space-y-6">
                        @csrf

                        <!-- Product Selection -->
                        <div>
                            <label class="label">
                                <span class="label-text">
                                    <i class="fa-sharp fa-box text-primary mr-2"></i>
                                    What are you paying for? <span class="text-error font-bold">*</span>
                                </span>
                            </label>
                            <p class="text-base-content/70 text-sm mb-3">
                                Select the product or service you're paying the customer for.
                            </p>

                            <input type="hidden" name="product_type" id="productTypeInput" value="{{ old('product_type') }}">
                            <!-- Hidden fields for department and tax policy -->
                            <input type="hidden" name="department_id" id="department_id" value="{{ old('department_id') }}">
                            <input type="hidden" name="tax_policy_id" id="tax_policy_id" value="{{ old('tax_policy_id') }}">

                            <!-- Predefined Products -->
                            @if($payout_products->count() > 0)
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                                @foreach($payout_products as $product)
                                <button type="button"
                                        class="btn btn-outline product-btn h-auto p-4 text-left justify-start"
                                        data-product-id="{{ $product->id }}"
                                        data-product-name="{{ $product->name }}"
                                        data-product-price="{{ $product->suggested_price ?? 0 }}"
                                        data-unit="{{ $product->unit ?? 'unit' }}"
                                        data-tax-policy-id="{{ $product->category->tax_policy_id ?? '' }}"
                                        data-department-id="{{ $product->category->department_id ?? '' }}">
                                    <div class="flex flex-col items-start w-full">
                                        <div class="font-semibold">{{ $product->name }}</div>
                                        @if(!empty($product->asset_tag))
                                            <span class="text-xs opacity-70 mt-1">({{ $product->asset_tag }})</span>
                                        @endif
                                        @if($product->suggested_price)
                                        <div class="text-sm opacity-70">${{ number_format($product->suggested_price, 2) }} per {{ $product->unit ?? 'unit' }}</div>
                                        @endif
                                    </div>
                                </button>
                                @endforeach
                            </div>
                            @else
                            <div class="bg-warning/10 border border-warning/20 rounded-lg p-4">
                                <div class="flex items-start gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-warning text-warning-content w-6 rounded">
                                            <i class="fa-sharp fa-exclamation-triangle text-xs"></i>
                                        </div>
                                    </div>
                                    <div class="text-sm text-base-content/80">
                                        <p class="font-medium mb-1">No Products Available</p>
                                        <p>No payout products are configured. Please contact an administrator to set up payout products.</p>
                                    </div>
                                </div>
                            </div>
                            @endif

                            @error('product_type')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>

                        <!-- Quantity/Weight -->
                        <div>
                            <label class="label" for="quantity">
                                <span class="label-text" id="quantityLabel">
                                    <i class="fa-sharp fa-weight-scale text-warning mr-2"></i>
                                    Quantity <span class="text-error font-bold">*</span>
                                </span>
                            </label>
                            <p class="text-base-content/70 text-sm mb-3" id="quantityDescription">
                                Enter the quantity of items being paid for.
                            </p>
                            <input type="number"
                                   name="quantity"
                                   id="quantity"
                                   class="input input-bordered w-full"
                                   placeholder="1"
                                   step="0.01"
                                   min="0.01"
                                   value="{{ old('quantity', 1) }}"
                                   required>
                            @error('quantity')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>

                        <!-- Amount Per Unit -->
                        <div>
                            <label class="label" for="payout_amount">
                                <span class="label-text" id="amountLabel">
                                    <i class="fa-sharp fa-dollar-sign text-success mr-2"></i>
                                    Amount Per Unit <span class="text-error font-bold">*</span>
                                </span>
                            </label>
                            <p class="text-base-content/70 text-sm mb-3" id="amountDescription">
                                Enter the amount you're paying per unit.
                            </p>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number"
                                       name="payout_amount"
                                       id="payout_amount"
                                       class="input input-bordered flex-1"
                                       placeholder="0.00"
                                       step="0.01"
                                       min="0.01"
                                       value="{{ old('payout_amount') }}"
                                       required>
                            </div>
                            @error('payout_amount')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>

                        <!-- Total Calculation -->
                        <div class="bg-success/10 border border-success/20 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="font-semibold text-base-content">Total Payout</h5>
                                    <p class="text-sm text-base-content/70" id="calculationBreakdown">Enter quantity and amount to see total</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-success" id="totalAmount">$0.00</div>
                                </div>
                            </div>
                        </div>



                        <!-- Description -->
                        <div>
                            <label class="label" for="description">
                                <span class="label-text">
                                    <i class="fa-sharp fa-comment text-accent mr-2"></i>
                                    Description (Optional)
                                </span>
                            </label>
                            <textarea name="description"
                                      id="description"
                                      class="textarea textarea-bordered w-full"
                                      rows="3"
                                      placeholder="Additional notes about this payout...">{{ old('description') }}</textarea>
                            @error('description')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>

                        <!-- Navigation -->
                        <div class="divider my-8">
                            <span class="text-base-content/50 font-medium">Continue to item photos</span>
                        </div>

                        <div class="flex justify-center">
                            <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg w-full sm:w-auto">
                                Continue to Step 3
                                <i class="fa-sharp fa-arrow-right"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Back Button -->
            <div class="flex justify-center mt-4">
                <a href="{{ route('mobile.payouts.step1', $mobilePayout) }}" class="btn btn-ghost btn-sm gap-2">
                    <i class="fa-sharp fa-arrow-left"></i>
                    Back to Step 1
                </a>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const productButtons = document.querySelectorAll('.product-btn');
            const productTypeInput = document.getElementById('productTypeInput');
            const payoutAmountInput = document.getElementById('payout_amount');
            const quantityInput = document.getElementById('quantity');
            const quantityLabel = document.getElementById('quantityLabel');
            const quantityDescription = document.getElementById('quantityDescription');
            const amountLabel = document.getElementById('amountLabel');
            const amountDescription = document.getElementById('amountDescription');
            const totalAmount = document.getElementById('totalAmount');
            const calculationBreakdown = document.getElementById('calculationBreakdown');
            const departmentInput = document.getElementById('department_id');
            const taxPolicyInput = document.getElementById('tax_policy_id');

            // Calculate total function
            function calculateTotal() {
                const quantity = parseFloat(quantityInput.value) || 0;
                const amount = parseFloat(payoutAmountInput.value) || 0;
                const total = quantity * amount;

                totalAmount.textContent = '$' + total.toFixed(2);

                if (quantity > 0 && amount > 0) {
                    calculationBreakdown.textContent = `${quantity} × $${amount.toFixed(2)} = $${total.toFixed(2)}`;
                } else {
                    calculationBreakdown.textContent = 'Enter quantity and amount to see total';
                }
            }

            // Handle predefined product selection
            productButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Reset all buttons
                    productButtons.forEach(btn => {
                        btn.classList.remove('btn-primary');
                        btn.classList.add('btn-outline');
                    });

                    // Activate selected button
                    this.classList.remove('btn-outline');
                    this.classList.add('btn-primary');

                    // Set hidden input
                    productTypeInput.value = this.dataset.productId;

                    // Set default price if available
                    const price = this.dataset.productPrice;
                    if (price && price > 0) {
                        payoutAmountInput.value = parseFloat(price).toFixed(2);
                    }

                    // Update labels based on unit
                    const unit = this.dataset.unit || 'unit';
                    quantityLabel.innerHTML = `<i class="fa-sharp fa-weight-scale text-warning mr-2"></i>Quantity (${unit}) <span class="text-error font-bold">*</span>`;
                    quantityDescription.textContent = `Enter the quantity in ${unit}s.`;
                    amountLabel.innerHTML = `<i class="fa-sharp fa-dollar-sign text-success mr-2"></i>Amount Per ${unit.charAt(0).toUpperCase() + unit.slice(1)} <span class="text-error font-bold">*</span>`;
                    amountDescription.textContent = `Enter the amount you're paying per ${unit}.`;

                    // Auto-select department and tax policy if available
                    const departmentId = this.dataset.departmentId;
                    const taxPolicyId = this.dataset.taxPolicyId;

                    if (departmentId && departmentInput) {
                        departmentInput.value = departmentId;
                    }

                    if (taxPolicyId && taxPolicyInput) {
                        taxPolicyInput.value = taxPolicyId;
                    }

                    // Recalculate total
                    calculateTotal();

                    // Auto-focus quantity input after product selection
                    setTimeout(() => {
                        quantityInput.focus();
                        quantityInput.select(); // Select all text in the input for easy replacement
                    }, 100);
                });
            });

            // Add event listeners for calculation
            quantityInput.addEventListener('input', calculateTotal);
            payoutAmountInput.addEventListener('input', calculateTotal);

            // Restore selection on page load
            const currentProductType = productTypeInput.value;
            if (currentProductType) {
                const selectedBtn = document.querySelector(`[data-product-id="${currentProductType}"]`);
                if (selectedBtn) {
                    selectedBtn.click();
                }
            }

            // Initial calculation
            calculateTotal();
        });
    </script>
    @endpush
</x-app-layout>
