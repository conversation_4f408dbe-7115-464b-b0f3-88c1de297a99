<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create User Group') }}
            </h2>
            <a href="{{ route('user_groups.index') }}" class="btn btn-sm">
                <i class="fa-sharp fa-solid fa-arrow-left mr-2"></i>
                Back to Groups
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <form action="{{ route('user_groups.store') }}" method="POST">
                        @csrf
                        
                        <div class="form-control w-full max-w-md">
                            <label class="label">
                                <span class="label-text">Group Name</span>
                            </label>
                            <input type="text" name="name" class="input input-bordered w-full" 
                                value="{{ old('name') }}" required>
                            @error('name')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>
                        
                        <div class="form-control w-full max-w-md mt-4">
                            <label class="label">
                                <span class="label-text">Description</span>
                            </label>
                            <textarea name="description" class="textarea textarea-bordered h-24">{{ old('description') }}</textarea>
                            @error('description')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>
                        
                        <div class="mt-6">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa-sharp fa-save mr-2"></i>
                                Create Group
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="mt-6 text-center text-gray-500">
                <p>After creating the group, you'll be able to add members and assign permissions.</p>
            </div>
        </div>
    </div>
</x-app-layout>
