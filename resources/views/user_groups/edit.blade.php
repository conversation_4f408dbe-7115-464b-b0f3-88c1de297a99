<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit User Group') }}
            </h2>
            <div class="flex gap-2">
                <a href="{{ route('user_groups.index') }}" class="btn btn-sm">
                    <i class="fa-sharp fa-solid fa-arrow-left mr-2"></i>
                    Back to Groups
                </a>
                <a href="{{ route('user_groups.show', $userGroup) }}" class="btn btn-info btn-sm">
                    <i class="fa-sharp fa-solid fa-eye mr-2"></i>
                    View Group
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <form action="{{ route('user_groups.update', $userGroup) }}" method="POST">
                @csrf
                @method('PUT')
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Group Info Card -->
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title">
                                <i class="fa-sharp fa-users-gear text-primary mr-2"></i>
                                Group Information
                            </h2>
                            
                            <div class="form-control w-full">
                                <label class="label">
                                    <span class="label-text">Group Name</span>
                                </label>
                                <input type="text" name="name" class="input input-bordered w-full" 
                                    value="{{ old('name', $userGroup->name) }}" 
                                    {{ $userGroup->name === 'Admin' ? 'readonly' : '' }}
                                    required>
                                @error('name')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>
                            
                            <div class="form-control w-full">
                                <label class="label">
                                    <span class="label-text">Description</span>
                                </label>
                                <textarea name="description" class="textarea textarea-bordered h-24">{{ old('description', $userGroup->description) }}</textarea>
                                @error('description')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Group Members Card -->
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title">
                                <i class="fa-sharp fa-users text-primary mr-2"></i>
                                Group Members
                            </h2>
                            
                            <div class="form-control">
                                <div class="input-group mb-4">
                                    <input type="text" id="user-search" placeholder="Search users..." class="input input-bordered w-full" />
                                    <button type="button" class="btn btn-square">
                                        <i class="fa-sharp fa-search"></i>
                                    </button>
                                </div>
                                
                                <div class="overflow-y-auto max-h-96 border rounded-lg p-2">
                                    @foreach($users as $user)
                                        <div class="form-control user-item">
                                            <label class="label cursor-pointer justify-start gap-2">
                                                <input type="checkbox" name="users[]" value="{{ $user->id }}" 
                                                    class="checkbox" 
                                                    {{ in_array($user->id, $groupMembers->pluck('id')->toArray()) ? 'checked' : '' }}>
                                                <div class="avatar placeholder mr-2">
                                                    <div class="bg-neutral-focus text-neutral-content rounded-full w-8">
                                                        <span>{{ substr($user->name, 0, 1) }}</span>
                                                    </div>
                                                </div>
                                                <div>
                                                    <span class="user-name">{{ $user->name }}</span>
                                                    <span class="text-xs text-gray-500 block">{{ $user->email }}</span>
                                                </div>
                                            </label>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permissions Card -->
                    <div class="card bg-base-100 shadow-xl md:col-span-2">
                        <div class="card-body">
                            <h2 class="card-title">
                                <i class="fa-sharp fa-shield-check text-primary mr-2"></i>
                                Permissions
                                @if($userGroup->name === 'Admin')
                                    <span class="badge badge-warning">Admin group always has all permissions</span>
                                @endif
                            </h2>
                            
                            <div class="join join-vertical w-full">
                                @foreach($allPermissions as $scope => $permissions)
                                    <div class="collapse collapse-arrow join-item border border-base-300">
                                        <input type="radio" name="accordion-permissions" {{ $loop->first ? 'checked' : '' }} />
                                        <div class="collapse-title text-xl font-medium bg-base-200">
                                            {{ ucfirst($scope) }} <span class="text-sm text-gray-500">({{ count($permissions) }} permissions)</span>
                                        </div>
                                        <div class="collapse-content bg-base-100">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                                                @foreach($permissions as $permission)
                                                    <div class="form-control">
                                                        <label class="label cursor-pointer justify-start gap-2">
                                                            <input type="checkbox" name="permissions[]" value="{{ $permission->id }}" 
                                                                class="checkbox" 
                                                                {{ in_array($permission->id, $assignedPermissionIds) ? 'checked' : '' }}
                                                                {{ $userGroup->name === 'Admin' ? 'checked disabled' : '' }}>
                                                            <span class="label-text">{{ $permission->description }}</span>
                                                        </label>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fa-sharp fa-save mr-2"></i>
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const userSearch = document.getElementById('user-search');
            const userItems = document.querySelectorAll('.user-item');
            
            userSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                
                userItems.forEach(item => {
                    const userName = item.querySelector('.user-name').textContent.toLowerCase();
                    if (userName.includes(searchTerm)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    </script>
    @endpush
</x-app-layout>
