<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('User Groups') }}
            </h2>
            <a href="{{ route('user_groups.create') }}" class="btn btn-primary btn-sm">
                <i class="fa-sharp fa-solid fa-plus mr-2"></i>
                Create New Group
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="alert alert-success mb-6">
                    <i class="fa-sharp fa-circle-check mr-2"></i>
                    {{ session('success') }}
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-error mb-6">
                    <i class="fa-sharp fa-circle-exclamation mr-2"></i>
                    {{ session('error') }}
                </div>
            @endif

            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <div class="overflow-x-auto">
                        <table class="table table-zebra w-full">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Members</th>
                                    <th>Permissions</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($userGroups as $group)
                                    <tr>
                                        <td class="font-medium">{{ $group->name }}</td>
                                        <td>{{ $group->description ?? 'No description' }}</td>
                                        <td>
                                            <div class="badge badge-primary">{{ $group->users_count }} {{ Str::plural('member', $group->users_count) }}</div>
                                        </td>
                                        <td>
                                            <div class="badge badge-secondary">{{ $group->permissions_count }} {{ Str::plural('permission', $group->permissions_count) }}</div>
                                        </td>
                                        <td class="flex gap-2">
                                            <a href="{{ route('user_groups.show', $group) }}" class="btn btn-sm btn-info">
                                                <i class="fa-sharp fa-eye"></i>
                                            </a>
                                            <a href="{{ route('user_groups.edit', $group) }}" class="btn btn-sm btn-primary">
                                                <i class="fa-sharp fa-edit"></i>
                                            </a>
                                            @if($group->name !== 'Admin')
                                                <form action="{{ route('user_groups.destroy', $group) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this group?');">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-error">
                                                        <i class="fa-sharp fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center py-4">No user groups found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
