<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('User Group Details') }}
            </h2>
            <div class="flex gap-2">
                <a href="{{ route('user_groups.index') }}" class="btn btn-sm">
                    <i class="fa-sharp fa-solid fa-arrow-left mr-2"></i>
                    Back to Groups
                </a>
                <a href="{{ route('user_groups.edit', $userGroup) }}" class="btn btn-primary btn-sm">
                    <i class="fa-sharp fa-solid fa-edit mr-2"></i>
                    Edit Group
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="alert alert-success mb-6">
                    <i class="fa-sharp fa-circle-check mr-2"></i>
                    {{ session('success') }}
                </div>
            @endif

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Group Info Card -->
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h2 class="card-title">
                            <i class="fa-sharp fa-users-gear text-primary mr-2"></i>
                            {{ $userGroup->name }}
                        </h2>
                        <p class="text-gray-600">{{ $userGroup->description ?? 'No description provided.' }}</p>
                        
                        <div class="divider"></div>
                        
                        <div class="stats stats-vertical shadow-sm">
                            <div class="stat">
                                <div class="stat-title">Members</div>
                                <div class="stat-value text-primary">{{ $userGroup->users->count() }}</div>
                                <div class="stat-desc">Users in this group</div>
                            </div>
                            
                            <div class="stat">
                                <div class="stat-title">Permissions</div>
                                <div class="stat-value text-secondary">{{ $userGroup->permissions->count() }}</div>
                                <div class="stat-desc">Assigned permissions</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Group Members Card -->
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h2 class="card-title">
                            <i class="fa-sharp fa-users text-primary mr-2"></i>
                            Group Members
                        </h2>
                        
                        @if($userGroup->users->isEmpty())
                            <div class="alert alert-info">
                                <i class="fa-sharp fa-info-circle mr-2"></i>
                                No members in this group.
                            </div>
                        @else
                            <div class="overflow-y-auto max-h-96">
                                <ul class="menu bg-base-200 rounded-box">
                                    @foreach($userGroup->users as $user)
                                        <li>
                                            <a href="{{ route('admin.users.edit', $user) }}" class="flex items-center">
                                                <div class="avatar placeholder mr-2">
                                                    <div class="bg-neutral-focus text-neutral-content rounded-full w-8">
                                                        <span>{{ substr($user->name, 0, 1) }}</span>
                                                    </div>
                                                </div>
                                                <span>{{ $user->name }}</span>
                                                <span class="text-xs text-gray-500">{{ $user->email }}</span>
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Permissions Card -->
                <div class="card bg-base-100 shadow-xl md:col-span-3">
                    <div class="card-body">
                        <h2 class="card-title">
                            <i class="fa-sharp fa-shield-check text-primary mr-2"></i>
                            Permissions
                        </h2>
                        
                        @if($groupedPermissions->isEmpty())
                            <div class="alert alert-info">
                                <i class="fa-sharp fa-info-circle mr-2"></i>
                                No permissions assigned to this group.
                            </div>
                        @else
                            <div class="join join-vertical w-full">
                                @foreach($groupedPermissions as $scope => $permissions)
                                    <div class="collapse collapse-arrow join-item border border-base-300">
                                        <input type="radio" name="accordion-permissions" {{ $loop->first ? 'checked' : '' }} />
                                        <div class="collapse-title text-xl font-medium bg-base-200">
                                            {{ ucfirst($scope) }} <span class="text-sm text-gray-500">({{ count($permissions) }} permissions)</span>
                                        </div>
                                        <div class="collapse-content bg-base-100">
                                            <div class="overflow-x-auto">
                                                <table class="table table-zebra w-full">
                                                    <thead>
                                                        <tr>
                                                            <th>Permission</th>
                                                            <th>Name</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($permissions as $permission)
                                                            <tr>
                                                                <td class="font-medium">{{ $permission->description }}</td>
                                                                <td><code>{{ $permission->name }}</code></td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
