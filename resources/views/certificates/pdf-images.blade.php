<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Images - {{ $certificate->certificate_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 15px;
            font-size: 22px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #000;
        }
        .header h1 {
            margin: 0;
            color: #000;
            font-size: 36px;
            margin-bottom: 10px;
        }
        .header p {
            margin: 5px 0;
            font-size: 20px;
            color: #444;
        }
        .images-section {
            margin-bottom: 30px;
        }
        .images-section h2 {
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
            font-size: 24px;
            margin-bottom: 20px;
            color: #000;
        }
        .images-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        .image-container {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: center;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .image-container img {
            max-width: 100%;
            max-height: 300px;
            width: auto;
            height: auto;
            object-fit: contain;
            border: 1px solid #ccc;
        }
        .image-info {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        .image-filename {
            font-weight: bold;
            margin-bottom: 5px;
            word-break: break-all;
        }
        .image-description {
            font-style: italic;
            margin-bottom: 5px;
        }
        .image-metadata {
            font-size: 12px;
            color: #888;
        }
        .page-break {
            page-break-before: always;
        }
        .no-images {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
            background-color: #f9f9f9;
            border: 1px dashed #ccc;
            border-radius: 5px;
        }
        .footer-info {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #ccc;
            font-size: 14px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Certificate Images</h1>
        <p><strong>Certificate Number:</strong> {{ $certificate->certificate_number }}</p>
        <p><strong>Customer:</strong> {{ $certificate->customer->business_name ?? $certificate->customer->contact_name }}</p>
        <p><strong>Generated:</strong> {{ now()->format('F j, Y \a\t g:i A') }}</p>
    </div>

    @if($imageFiles->count() > 0)
        <div class="images-section">
            <h2>Attached Images ({{ $imageFiles->count() }} {{ Str::plural('image', $imageFiles->count()) }})</h2>
            
            @php
                $chunkedImages = $imageFiles->chunk(4); // 4 images per page
            @endphp
            
            @foreach($chunkedImages as $pageIndex => $imageChunk)
                @if($pageIndex > 0)
                    <div class="page-break"></div>
                @endif
                
                <div class="images-grid">
                    @foreach($imageChunk as $file)
                        <div class="image-container">
                            @php
                                // Get the full file path for PDF generation
                                $disk = $file->is_public ? 'public' : 'private';
                                $fullPath = Storage::disk($disk)->path($file->filepath);
                                
                                // Convert to base64 for PDF embedding
                                $imageData = null;
                                if (file_exists($fullPath)) {
                                    $imageData = base64_encode(file_get_contents($fullPath));
                                    $mimeType = $file->mime_type;
                                }
                            @endphp
                            
                            @if($imageData)
                                <img src="data:{{ $mimeType }};base64,{{ $imageData }}" alt="{{ $file->original_filename }}">
                            @else
                                <div style="height: 200px; display: flex; align-items: center; justify-content: center; background-color: #f0f0f0; border: 1px dashed #ccc;">
                                    <span style="color: #999;">Image not available</span>
                                </div>
                            @endif
                            
                            <div class="image-info">
                                <div class="image-filename">{{ $file->original_filename }}</div>
                                @if($file->description)
                                    <div class="image-description">{{ $file->description }}</div>
                                @endif
                                <div class="image-metadata">
                                    Size: {{ $file->human_readable_size }} | 
                                    Uploaded: {{ $file->created_at->format('M j, Y') }}
                                    @if($file->uploader)
                                        by {{ $file->uploader->name }}
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                    
                    @if($imageChunk->count() < 4)
                        @for($i = $imageChunk->count(); $i < 4; $i++)
                            <div class="image-container" style="visibility: hidden;"></div>
                        @endfor
                    @endif
                </div>
            @endforeach
        </div>
    @else
        <div class="no-images">
            <p>No images have been attached to this certificate.</p>
        </div>
    @endif

    <div class="footer-info">
        <p>This document contains all images attached to Certificate {{ $certificate->certificate_number }}.</p>
        <p>Images are displayed in the order they were uploaded to the system.</p>
    </div>
</body>
</html>
