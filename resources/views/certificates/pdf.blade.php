<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate of Destruction - {{ $certificate->certificate_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.4; /* Reduced from 1.6 */
            color: #333;
            margin: 0;
            padding: 15px; /* Reduced from 20px */
            font-size: 22px;
        }
        p {
            margin-top: 0;
            margin-bottom: 6px; /* Reduced paragraph spacing */
        }
        .logo-container {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px 0;
        }
        .logo-image {
            max-width: 400px; /* Increased from 350px */
            max-height: 400px; /* Increased from 250px */
            margin: 0 auto;
            display: block;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
        }
        .header h1 {
            margin: 0;
            color: #000;
            font-size: 40px;
            margin-bottom: 10px;
        }
        .header p {
            margin: 5px 0;
            font-size: 24px;
            color: #444;
        }
        .certificate-info-container {
            margin-bottom: 20px;
        }
        .company-info {
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .company-info h3 {
            margin: 0 0 5px 0;
            font-size: 28px;
            color: #333;
        }
        .company-info p {
            margin: 2px 0;
            font-size: 20px;
        }
        .info-columns {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 70px;
        }
        .certificate-info, .customer-info {
            width: 48%;
            vertical-align: top;
            padding: 0 10px;
        }
        .certificate-info h3, .customer-info h3 {
            margin: 0 0 8px 0;
            font-size: 24px;
            color: #333;

            padding-bottom: 3px;
        }
        .certificate-info table, .customer-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .certificate-info table td, .customer-info table td {
            padding: 3px 5px;
            font-size: 20px;
            vertical-align: top;
        }
        .certificate-info table td:first-child, .customer-info table td:first-child {
            font-weight: bold;
            width: 120px;
        }
        .mt-4 {
            margin-top: 15px;
        }
        .certificate-notes h3 {
            margin: 0 0 8px 0;
            font-size: 20px;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 3px;
        }
        .notes-content {
            font-size: 20px;
            line-height: 1.4;
        }
        .notes-content p {
            margin-bottom: 5px;
        }
        .notes-content ul, .notes-content ol {
            margin-left: 15px;
            margin-bottom: 5px;
        }
        .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        .section h2 {
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
            font-size: 22px;
            margin-top: 30px;
        }
        .contract-text {
            font-size: 20px;
            margin-bottom: 15px;
            text-align: justify;
        }
        .contract-text p {
            margin-bottom: 8px; /* Reduced spacing between paragraphs */
        }
        .signature-container {
            margin-top: 20px;
            border-top: 1px dashed #ccc;
            padding-top: 10px;
        }
        .signature-image {
            max-width: 500px; /* Increased by 50% from 400px */
            margin-bottom: 15px;
            width: 100%;
            height: auto;
        }
        .signature-info {
            font-size: 12px;
        }
        .signature-info p {
            margin: 3px 0;
        }
        .device-cards {
            width: 100%;
            margin-top: 20px;
            border-collapse: separate;
            border-spacing: 10px;
        }
        .device-cards-row {
            vertical-align: top;
        }
        .device-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background-color: #f9f9f9;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            page-break-inside: avoid;
            position: relative;
            width: 100%;
            display: block;
        }
        .device-card.destroyed {
            border-left: 4px solid #2e7d32;
        }
        .device-card-header {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #eee;
            padding-bottom: 4px; /* Reduced from 5px */
            margin-bottom: 6px; /* Reduced from 8px */
        }
        .device-card-number {
            font-weight: bold;
            font-size: 18px;
            color: #333;
        }
        .device-card-serial {
            font-family: monospace;
            font-weight: bold;
            font-size: 16px;
        }
        .device-card-details {
            display: flex;
            flex-wrap: wrap;
            gap: 8px; /* Reduced from 10px */
            margin-bottom: 6px; /* Reduced from 8px */
        }
        .device-card-detail {
            flex: 1;
            min-width: 120px;
        }
        .device-card-label {
            font-weight: bold;
            font-size: 14px;
            color: #666;
            margin-bottom: 2px;
        }
        .device-card-value {
            font-size: 16px;
        }
        .device-card-notes {
            border-top: 1px dashed #eee;
            padding-top: 5px;
            margin-top: 5px;
            font-size: 15px;
            font-style: italic;
            color: #555;
        }
        .device-card-destruction {
            margin-top: 5px;
            padding: 5px;
            background-color: #e8f5e9;
            border-radius: 3px;
            font-size: 15px;
            color: #2e7d32;
        }
        .page-break {
            page-break-after: always;
        }
        .footer {
            font-size: 14px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            width: 100%;
            background-color: white;
            padding-bottom: 10px;
            display: flex;
            align-items: flex-start;
        }
        .footer-logo {
            width: 240px;
            margin-right: 15px;
            position: absolute;
            bottom: 10px;
            left: 10px;
        }
        .footer-text {
            flex: 1;
            text-align: center;
        }
        .footer-space {
            height: 100px; /* Space to prevent content from overlapping with footer */
        }
        .page-content {
            margin-bottom: 100px; /* Ensure content doesn't overlap with footer */
        }
        /* First page styling */
        .first-page {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="page-content">
        <div class="first-page">
            <div class="logo-container">
                <img src="{{ public_path('img/etech-cert-destruction-logo.png') }}" alt="E-Tech Certificate of Destruction" class="logo-image">
            </div>

            <div class="header">
                <h1>Certificate of Destruction</h1>
                <p>Certificate Number: {{ $certificate->certificate_number }}</p>
                <p>Version: {{ $version }} | Generated: {{ now()->format('F j, Y g:i A') }}</p>
            </div>



            <table class="info-columns">
                <tr>
                    <!-- Certificate Information -->
                    <td class="certificate-info">
                        <h3>Certificate Details</h3>
                        <table>
                        <tr>
                            <td>Certificate Number:</td>
                            <td>{{ $certificate->certificate_number }}</td>
                        </tr>
                        <tr>
                            <td>Scheduled Destruction:</td>
                            <td>{{ $certificate->scheduled_destruction_date ? $certificate->scheduled_destruction_date->format('F j, Y') : 'Not scheduled' }}</td>
                        </tr>
                        <tr>
                            <td>Status:</td>
                            <td>
                                {{ $certificate->getFormattedStatus() }}
                            </td>
                        </tr>
                        <tr>
                            <td>Actual Destruction:</td>
                            <td>{{ $certificate->actual_destruction_date ? $certificate->actual_destruction_date->format('F j, Y g:i A') : 'Not completed' }}</td>
                        </tr>
                        <tr>
                            <td>Destruction Method:</td>
                            <td>{{ $certificate->destruction_method ?? 'Not specified' }}</td>
                        </tr>
                        <tr>
                            <td>Destruction Location:</td>
                            <td>{{ $certificate->destruction_location ?? 'Not specified' }}</td>
                        </tr>
                        <tr>
                            <td>Total Devices:</td>
                            <td>{{ $deviceCount }}</td>
                        </tr>
                        <tr>
                            <td>Devices Destroyed:</td>
                            <td>{{ $certificate->devices->where('is_destroyed', true)->count() }} of {{ $deviceCount }}</td>
                        </tr>
                        </table>

                        @if($certificate->notes)
                        <div class="certificate-notes mt-4">
                            <h3>Certificate Notes</h3>
                            <div class="notes-content p-3 bg-gray-50 rounded border border-gray-200 mt-2">
                                {!! $certificate->notes !!}
                            </div>
                        </div>
                        @endif
                    </td>

                    <!-- Customer Information -->
                    <td class="customer-info">
                    <h3>Customer Information{{ $certificate->customer->type === 'Business' ? ' - ' . $certificate->customer->name : '' }}</h3>
                    <table>
                        <tr>
                            <td>{{ $certificate->customer->type === 'Business' ? 'Business Name' : 'Name' }}:</td>
                            <td>{{ $certificate->customer->name }}</td>
                        </tr>
                        @if($certificate->customer->type === 'Business' && $certificate->customer->contact)
                        <tr>
                            <td>Contact Person:</td>
                            <td>{{ $certificate->customer->contact }}</td>
                        </tr>
                        @endif
                        <tr>
                            <td>Email:</td>
                            <td>{{ $certificate->customer->email ?: 'Not provided' }}</td>
                        </tr>
                        <tr>
                            <td>Phone:</td>
                            <td>{{ $certificate->customer->phone ?: 'Not provided' }}</td>
                        </tr>
                        <tr>
                            <td>Address:</td>
                            <td>{{ $certificate->customer->address ?: 'Not provided' }}</td>
                        </tr>
                        <tr>
                            <td>Customer Type:</td>
                            <td>{{ $certificate->customer->type }}</td>
                        </tr>
                    </table>

                    <!-- Certifying Technician Information -->
                    @if($certifyingTechnician)
                    <h3 class="mt-4">Certifying Technician</h3>
                    <table>
                        <tr>
                            <td>Name:</td>
                            <td>{{ $certifyingTechnician->name }}</td>
                        </tr>
                        <tr>
                            <td>Email:</td>
                            <td>{{ $certifyingTechnician->email }}</td>
                        </tr>
                    </table>
                    @endif
                    </td>
                </tr>
            </table>

            <!-- Company Information -->
            <div class="company-info">
                <img src="{{ public_path('img/company-logo.png') }}" alt="Company Logo" style="width: 360px; height: auto; margin: 0 auto 20px auto; display: block;">
                <h3>{{ $companyData['company_name'] }}</h3>
                <p>{{ $companyData['company_address'] }}</p>
                <p>Phone: {{ $companyData['company_phone'] }} | Website: {{ $companyData['company_website'] }}</p>
                <p>Email: {{ $companyData['company_email'] }}</p>
            </div>

        </div><!-- End of first-page -->

        @if(isset($contractData['client']))
        <div class="section">
            <h2>Client Certification</h2>
            <div class="contract-text">
                {!! nl2br($contractData['client']['contractText'] ?? '') !!}
            </div>
            <div class="signature-container">
                @if($contractData['client']['signatureImageData'])
                    <img class="signature-image" src="{{ $contractData['client']['signatureImageData'] }}" alt="Client Signature">
                @endif
                <div class="signature-info">
                    <p><strong>Signatory:</strong> {{ $contractData['client']['signature']->signatory_name }}</p>
                    <p><strong>Date:</strong> {{ $contractData['client']['signature']->signature_date->format('F j, Y g:i A') }}</p>
                    <p><strong>IP Address:</strong> {{ $contractData['client']['signature']->ip_address }}</p>
                </div>
            </div>
        </div>
        @endif

        @if(isset($contractData['driver']))
        <div class="section">
            <h2>Driver Certification</h2>
            <div class="contract-text">
                {!! nl2br($contractData['driver']['contractText'] ?? '') !!}
            </div>
            <div class="signature-container">
                @if($contractData['driver']['signatureImageData'])
                    <img class="signature-image" src="{{ $contractData['driver']['signatureImageData'] }}" alt="Driver Signature">
                @endif
                <div class="signature-info">
                    <p><strong>Signatory:</strong> {{ $contractData['driver']['signature']->signatory_name }}</p>
                    <p><strong>Date:</strong> {{ $contractData['driver']['signature']->signature_date->format('F j, Y g:i A') }}</p>
                    <p><strong>IP Address:</strong> {{ $contractData['driver']['signature']->ip_address }}</p>
                </div>
            </div>
        </div>
        @endif

        @if(isset($contractData['warehouse']))
        <div class="section">
            <h2>Warehouse Receiving Certification</h2>
            <div class="contract-text">
                {!! nl2br($contractData['warehouse']['contractText'] ?? '') !!}
            </div>
            <div class="signature-container">
                @if($contractData['warehouse']['signatureImageData'])
                    <img class="signature-image" src="{{ $contractData['warehouse']['signatureImageData'] }}" alt="Warehouse Signature">
                @endif
                <div class="signature-info">
                    <p><strong>Signatory:</strong> {{ $contractData['warehouse']['signature']->signatory_name }}</p>
                    <p><strong>Date:</strong> {{ $contractData['warehouse']['signature']->signature_date->format('F j, Y g:i A') }}</p>
                    <p><strong>IP Address:</strong> {{ $contractData['warehouse']['signature']->ip_address }}</p>
                </div>
            </div>
        </div>
        @endif

        @if(isset($contractData['technician']))
        <div class="section">
            <h2>Technician Certification</h2>
            <div class="contract-text">
                {!! nl2br($contractData['technician']['contractText'] ?? '') !!}
            </div>
            <div class="signature-container">
                @if($contractData['technician']['signatureImageData'])
                    <img class="signature-image" src="{{ $contractData['technician']['signatureImageData'] }}" alt="Technician Signature">
                @endif
                <div class="signature-info">
                    <p><strong>Signatory:</strong> {{ $contractData['technician']['signature']->signatory_name }}</p>
                    <p><strong>Date:</strong> {{ $contractData['technician']['signature']->signature_date->format('F j, Y g:i A') }}</p>
                    <p><strong>IP Address:</strong> {{ $contractData['technician']['signature']->ip_address }}</p>
                </div>
            </div>
        </div>
        @endif

        <div class="page-break"></div>

        <div class="section">
            <h2>Device Inventory</h2>
            <p>The following devices were processed under this certificate:</p>

            @if($certificate->devices->count() > 0)
                @php
                    // Sort devices by ID to ensure consistent ordering
                    $sortedDevices = $certificate->devices->sortBy('id');
                    $totalDevices = count($sortedDevices);
                @endphp

                <table class="device-cards">
                    @php $counter = 0; @endphp
                    @foreach($sortedDevices as $index => $device)
                        @if($counter % 2 == 0)
                            <tr class="device-cards-row">
                        @endif

                        <td width="50%">
                            <div class="device-card {{ $device->is_destroyed ? 'destroyed' : '' }}">
                                <div class="device-card-header">
                                    <div class="device-card-number">Device #{{ $index + 1 }}</div>
                                    <div class="device-card-serial">SN: {{ $device->serial_number }}</div>
                                </div>

                                <div class="device-card-details">
                                    @php
                                        $deviceType = \App\Models\Device::DEVICE_TYPES[$device->device_type] ?? $device->device_type;
                                    @endphp
                                    @if($deviceType && $deviceType != 'unknown')
                                    <div class="device-card-detail">
                                        <div class="device-card-label">Device Type</div>
                                        <div class="device-card-value">{{ $deviceType }}</div>
                                    </div>
                                    @endif

                                    @if($device->manufacturer)
                                    <div class="device-card-detail">
                                        <div class="device-card-label">Manufacturer</div>
                                        <div class="device-card-value">{{ $device->manufacturer }}</div>
                                    </div>
                                    @endif

                                    @if($device->model)
                                    <div class="device-card-detail">
                                        <div class="device-card-label">Model</div>
                                        <div class="device-card-value">{{ $device->model }}</div>
                                    </div>
                                    @endif

                                    <div class="device-card-detail">
                                        <div class="device-card-label">Scanned Date</div>
                                        <div class="device-card-value">{{ $device->created_at->format('M j, Y g:i A') }}</div>
                                    </div>

                                    @if($device->is_destroyed && $device->destroyed_at)
                                    <div class="device-card-detail" style="flex-basis: 100%; margin-top: 5px; border-top: 1px solid #eee; padding-top: 5px;">
                                        <div class="device-card-label">Destroyed Date</div>
                                        <div class="device-card-value">{{ $device->destroyed_at->format('M j, Y g:i A') }}
                                        @if($device->destroyedByUser)
                                            <span style="font-size: 14px; color: #666;">by {{ $device->destroyedByUser->name }}</span>
                                        @endif
                                        </div>
                                    </div>
                                    @endif
                                </div>

                                @if($device->is_destroyed)
                                    <div class="device-card-destruction">
                                        <strong>Status:</strong> Destroyed
                                    </div>
                                @endif

                                @if(!empty($device->notes))
                                    <div class="device-card-notes">
                                        <strong>Notes:</strong> {{ $device->notes }}
                                    </div>
                                @endif
                            </div>
                        </td>

                        @php $counter++; @endphp
                        @if($counter % 2 == 0 || $loop->last)
                            @if($loop->last && $counter % 2 != 0)
                                <td width="50%"></td>
                            @endif
                            </tr>
                        @endif
                    @endforeach
                </table>
            @else
                <p>No devices were recorded for this certificate.</p>
            @endif
        </div>

        <!-- Add space at the bottom to prevent content from overlapping with footer -->
        <div class="footer-space"></div>

    </div><!-- End of page-content -->

    <div class="footer">
        <div class="footer-logo">
            <img src="{{ public_path('img/company-logo.png') }}" alt="Company Logo" style="width: 100%;">
        </div>
        <div class="footer-text">
            @php
                // Replace placeholders in the footer text
                $certificateInfoLine = str_replace(
                    ['[GENERATION_DATE]', '[CERTIFICATE_NUMBER]', '[VERSION]'],
                    [now()->format('F j, Y g:i A'), $certificate->certificate_number, $version],
                    $footerData['certificate_info_line'] ?? 'Certificate generated on ' . now()->format('F j, Y g:i A')
                );
            @endphp

            <p>{{ $certificateInfoLine }}</p>

            @if(isset($footerData['record_line']))
                <p>{{ $footerData['record_line'] }}</p>
            @endif

            @if(isset($footerData['guarantee_line']))
                <p>{{ $footerData['guarantee_line'] }}</p>
            @endif

            @if(isset($footerData['data_line']))
                <p>{{ $footerData['data_line'] }}</p>
            @endif

            @if(isset($footerData['contact_info']))
                <p>{{ $footerData['contact_info'] }}</p>
            @endif
        </div>
    </div>
</body>
</html>
