@php
    // Build action buttons array dynamically based on PDF existence
    $actionButtons = [
        [
            'name' => 'Edit Certificate',
            'route' => route('certificates.edit', $certificate),
            'icon' => 'fa-sharp fa-pen-to-square',
            'class' => 'btn btn-primary btn-sm gap-2',
            'permission' => 'edit_certificates'
        ]
    ];

    // Add PDF-related buttons based on whether PDF exists
    if (isset($currentPdf)) {
        $actionButtons[] = [
            'name' => 'View PDF',
            'route' => route('certificates.viewPdf', $currentPdf),
            'icon' => 'fa-sharp fa-eye',
            'class' => 'btn btn-success btn-sm gap-2',
            'target' => '_blank'
        ];
        $actionButtons[] = [
            'name' => 'Regenerate PDF',
            'icon' => 'fa-sharp fa-file-pdf',
            'class' => 'btn btn-secondary btn-sm gap-2',
            'id' => 'regenerate-pdf-btn'
        ];
    } else {
        $actionButtons[] = [
            'name' => 'Generate PDF',
            'route' => route('certificates.generatePdf', $certificate),
            'icon' => 'fa-sharp fa-file-pdf',
            'class' => 'btn btn-secondary btn-sm gap-2'
        ];
    }
@endphp

<x-app-layout
    page-title="Certificate: {{ $certificate->certificate_number }}"
    page-icon="fa-sharp fa-certificate"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('certificates.index'),
            'text' => 'Back to Certificates'
        ]
    ]"
    :action-buttons="$actionButtons"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Certificates', 'route' => 'certificates.index', 'icon' => 'fa-certificate'],
        ['name' => $certificate->certificate_number, 'icon' => 'fa-certificate']
    ]">

    <div class="py-6 lg:py-8">

        <!-- Page Content -->
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100 overflow-hidden shadow-sm sm:rounded-lg p-6">
                <!-- Certificate Header -->
                <div class="text-center mb-8">
                    <img src="{{ asset('img/etech-cert-destruction-logo.png') }}" alt="E-Tech Certificate of Destruction" class="mx-auto mb-4" style="max-height: 120px;">
                    <h1 class="text-3xl font-bold text-base-content mb-2">Certificate of Destruction</h1>
                    <p class="text-lg text-base-content">Certificate Number: {{ $certificate->certificate_number }}</p>
                    <p class="text-sm text-base-content/70">Generated: {{ now()->format('F j, Y g:i A') }}</p>
                </div>

                <!-- Certificate Status Banner -->
                <div class="mb-6">
                    @if($certificate->status === 'pending')
                        <div class="alert alert-info">
                            <i class="fa-sharp fa-circle-info mr-2"></i>
                            <span class="text-info-content">This certificate is <strong>Pending</strong></span>
                        </div>
                    @elseif($certificate->status === 'verifying')
                        <div class="alert alert-warning">
                            <i class="fa-sharp fa-triangle-exclamation mr-2"></i>
                            <span class="text-warning-content">This certificate is <strong>Verifying</strong></span>
                        </div>
                    @elseif($certificate->status === 'completed')
                        <div class="alert alert-success">
                            <i class="fa-sharp fa-circle-check mr-2"></i>
                            <span class="text-success-content">This certificate is <strong>Complete</strong></span>
                        </div>
                    @endif
                </div>

                <!-- Certificate and Customer Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <!-- Certificate Information -->
                    <div class="">
                        <div class="">
                            <h2 class="card-title border-b pb-2 mb-4 text-base-content">Certificate Details</h2>
                            <table class="table">
                                <tbody>
                                    <tr>
                                        <td class="font-medium text-base-content">Certificate Number:</td>
                                        <td class="text-base-content">{{ $certificate->certificate_number }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-base-content">Scheduled Destruction:</td>
                                        <td class="text-base-content">{{ $certificate->scheduled_destruction_date ? $certificate->scheduled_destruction_date->format('F j, Y') : 'Not scheduled' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-base-content">Status:</td>
                                        <td>
                                            @if($certificate->status === 'pending')
                                                <span class="badge badge-primary">Pending</span>
                                            @elseif($certificate->status === 'verifying')
                                                <span class="badge badge-warning">Verifying</span>
                                            @elseif($certificate->status === 'completed')
                                                <span class="badge badge-success">Complete</span>
                                            @else
                                                <span class="badge badge-neutral">{{ $certificate->status }}</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-base-content">Actual Destruction:</td>
                                        <td class="text-base-content">{{ $certificate->actual_destruction_date ? $certificate->actual_destruction_date->format('F j, Y g:i A') : 'Not completed' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-base-content">Destruction Method:</td>
                                        <td class="text-base-content">{{ $certificate->destruction_method ?? 'Not specified' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-base-content">Destruction Location:</td>
                                        <td class="text-base-content">{{ $certificate->destruction_location ?? 'Not specified' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-base-content">Total Devices:</td>
                                        <td class="text-base-content">{{ $deviceCount }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-base-content">Devices Destroyed:</td>
                                        <td class="text-base-content">{{ $certificate->devices->where('is_destroyed', true)->count() }} of {{ $deviceCount }}</td>
                                    </tr>
                                </tbody>
                            </table>

                            @if($certificate->notes)
                            <div class="mt-4">
                                <h3 class="font-medium border-b pb-2 mb-4 text-base-content">Certificate Notes</h3>
                                <div class="p-3 bg-base-200 rounded border border-base-300 text-base-content">
                                    {!! $certificate->notes !!}
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Customer Information -->
                    <div class="">
                        <div class="">
                            <div class="flex justify-between items-start mb-4">
                                <h2 class="card-title text-base-content">Customer Information</h2>
                                <a href="{{ route('customers.show', $certificate->customer) }}" class="btn btn-xs btn-ghost">
                                    <i class="fa-sharp fa-external-link"></i>
                                    View Profile
                                </a>
                            </div>

                            <table class="table">
                                <tbody>
                                    <tr>
                                        <td class="font-medium text-base-content">{{ $certificate->customer->type === 'Business' ? 'Business Name' : 'Name' }}:</td>
                                        <td class="text-base-content">{{ $certificate->customer->name }}</td>
                                    </tr>
                                    @if($certificate->customer->type === 'Business' && $certificate->customer->contact)
                                    <tr>
                                        <td class="font-medium text-base-content">Contact Person:</td>
                                        <td class="text-base-content">{{ $certificate->customer->contact }}</td>
                                    </tr>
                                    @endif
                                    <tr>
                                        <td class="font-medium text-base-content">Email:</td>
                                        <td class="text-base-content">{{ $certificate->customer->email ?: 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-base-content">Phone:</td>
                                        <td class="text-base-content">{{ $certificate->customer->phone ?: 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-base-content">Address:</td>
                                        <td class="text-base-content">{{ $certificate->customer->address ?: 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium text-base-content">Customer Type:</td>
                                        <td class="text-base-content">{{ $certificate->customer->type }}</td>
                                    </tr>
                                </tbody>
                            </table>

                            @if($certifyingTechnician)
                            <div class="mt-4">
                                <h3 class="font-medium border-b pb-2 mb-2 text-base-content">Certifying Technician</h3>
                                <table class="table">
                                    <tbody>
                                        <tr>
                                            <td class="font-medium text-base-content">Name:</td>
                                            <td class="text-base-content">{{ $certifyingTechnician->name }}</td>
                                        </tr>
                                        <tr>
                                            <td class="font-medium text-base-content">Email:</td>
                                            <td class="text-base-content">{{ $certifyingTechnician->email }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Company Information -->

                <div class="text-center">
                    <img src="{{ asset('img/company-logo.png') }}" alt="Company Logo" class="mx-auto mb-4" style="max-height: 80px;">
                    <h3 class="text-xl font-bold text-base-content">{{ $companyData['name'] }}</h3>
                    <p class="text-base-content">{{ $companyData['address'] }}</p>
                    <p class="text-base-content">Phone: {{ $companyData['phone'] }} | Website: {{ $companyData['website'] }}</p>
                    <p class="text-base-content">Email: {{ $companyData['email'] }}</p>
                </div>


                <!-- Signatures Section -->
                @if(count($contractData) > 0)
                <div class="mb-8">
                    <h2 class="text-2xl font-bold mb-4">Signatures</h2>
                    <div class="grid grid-cols-1 gap-6">
                        @foreach(['client', 'driver', 'warehouse', 'technician'] as $type)
                            @if(isset($contractData[$type]))
                                <div class="">
                                    <div class="card-body">
                                        <h3 class="card-title border-b border-base-content pb-2 mb-4 text-base-content">
                                            {{ ucfirst($type) }} Certification
                                        </h3>
                                        <div class="mb-4 text-sm">
                                            {!! nl2br($contractData[$type]['contractText'] ?? '') !!}
                                        </div>
                                        <div class="flex flex-col items-center">
                                            @if(isset($contractData[$type]['signature']->signature_image_path))
                                                <img src="{{ asset('storage/' . $contractData[$type]['signature']->signature_image_path) }}"
                                                     alt="{{ ucfirst($type) }} Signature"
                                                     class="border-b border-gray-400 pb-2 mb-4"
                                                     style="max-width: 400px; max-height: 150px;">
                                            @endif
                                            <div class="w-full mt-4 text-center">
                                                <p class="mb-2"><strong>Signatory:</strong> {{ $contractData[$type]['signature']->signatory_name }}</p>
                                                <p class="mb-2"><strong>Date:</strong> {{ $contractData[$type]['signature']->signature_date->format('F j, Y g:i A') }}</p>
                                                <p><strong>IP Address:</strong> {{ $contractData[$type]['signature']->ip_address }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Device Inventory Section -->
                <div>
                    <h2 class="text-2xl font-bold text-base-content mb-4">Device Inventory</h2>
                    <p class="text-base-content/70 mb-4">The following devices were processed under this certificate:</p>

                    @if($certificate->devices->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($certificate->devices->sortBy('id') as $index => $device)
                                <div class="card shadow-md {{ $device->is_destroyed ? 'bg-neutral text-neutral-content' : 'bg-base-100 text-base-content' }}">
                                    <div class="card-body p-4">
                                        <div class="flex justify-between items-center mb-2">
                                            <div class="font-bold">Device #{{ $index + 1 }}</div>
                                            <div class="text-sm font-medium">SN: {{ $device->serial_number }}</div>
                                        </div>

                                        <div class="grid grid-cols-2 gap-2 text-sm mb-2">
                                            @php
                                                $deviceType = \App\Models\Device::DEVICE_TYPES[$device->device_type] ?? $device->device_type;
                                            @endphp
                                            @if($deviceType && $deviceType != 'unknown')
                                            <div>
                                                <span class="font-medium">Device Type:</span>
                                                <span>{{ $deviceType }}</span>
                                            </div>
                                            @endif

                                            @if($device->manufacturer)
                                            <div>
                                                <span class="font-medium">Manufacturer:</span>
                                                <span>{{ $device->manufacturer }}</span>
                                            </div>
                                            @endif

                                            @if($device->model)
                                            <div>
                                                <span class="font-medium">Model:</span>
                                                <span>{{ $device->model }}</span>
                                            </div>
                                            @endif

                                            <div>
                                                <span class="font-medium">Scanned Date:</span>
                                                <span>{{ $device->created_at->format('M j, Y g:i A') }}</span>
                                            </div>

                                            @if($device->is_destroyed && $device->destroyed_at)
                                            <div class="col-span-2 mt-1 pt-1 border-t border-base-300">
                                                <span class="font-medium">Destroyed Date:</span>
                                                <span>{{ $device->destroyed_at->format('M j, Y g:i A') }}</span>
                                                @if($device->destroyedByUser)
                                                    <span class="text-success">by {{ $device->destroyedByUser->name }}</span>
                                                @endif
                                            </div>
                                            @endif
                                        </div>

                                        @if($device->is_destroyed)
                                            <div class="text-sm text-success font-medium mt-2">
                                                <i class="fa-sharp fa-circle-check mr-1"></i>
                                                Destroyed: {{ $device->destroyed_at->format('M j, Y g:i A') }}
                                                @if($device->destroyedByUser)
                                                    by {{ $device->destroyedByUser->name }}
                                                @endif
                                            </div>
                                        @else
                                            <div class="text-sm text-warning font-medium mt-2">
                                                <i class="fa-sharp fa-triangle-exclamation mr-1"></i>
                                                Not yet destroyed
                                            </div>
                                        @endif

                                        @if(!empty($device->notes))
                                            <div class="text-sm mt-2 bg-base-200 p-2 rounded">
                                                <span class="font-medium">Notes:</span> {{ $device->notes }}
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="alert alert-info">
                            <i class="fa-sharp fa-circle-info mr-2"></i>
                            <span>No devices were recorded for this certificate.</span>
                        </div>
                    @endif
                </div>

                <!-- Uploaded Documents Section -->
                @if($certificate->files->count() > 0)
                <div class="mt-8">
                    <h2 class="text-2xl font-bold mb-4">Uploaded Documents</h2>

                    <div class="overflow-x-auto">
                        <table class="table table-zebra w-full">
                            <thead>
                                <tr>
                                    <th>File</th>
                                    <th>Description</th>
                                    <th>Size</th>
                                    <th>Uploaded</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($certificate->files as $file)
                                    <tr>
                                        <td>
                                            <div class="flex items-center gap-2">
                                                <i class="fa-sharp {{ $file->icon }} text-primary text-xl"></i>
                                                <div>
                                                    <div class="font-bold">{{ Str::limit($file->original_filename, 30) }}</div>
                                                    <div class="text-xs text-base-content/50">{{ $file->extension }}</div>
                                                </div>
                                                @if(isset($certificate->stats['coc_file_id']) && $certificate->stats['coc_file_id'] == $file->id)
                                                    <span class="badge badge-primary">Chain of Custody</span>
                                                @elseif(isset($file->metadata['is_coc']) && $file->metadata['is_coc'])
                                                    <span class="badge badge-primary">Chain of Custody</span>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-ghost {{ $file->description ? 'text-primary' : 'text-base-content/50' }}"
                                                    onclick="openFileNotesModal('{{ $file->id }}', '{{ addslashes($file->original_filename) }}', '{{ addslashes($file->description ?? '') }}')"
                                                    title="{{ $file->description ? 'View notes' : 'No notes' }}"
                                                    data-file-id="{{ $file->id }}"
                                                    data-description="{{ addslashes($file->description ?? '') }}">
                                                <i class="fa-sharp fa-note-sticky"></i>
                                                <span class="ml-1">{{ $file->description ? 'View notes' : 'No notes' }}</span>
                                            </button>
                                        </td>
                                        <td>{{ $file->human_readable_size }}</td>
                                        <td>
                                            <div>{{ $file->created_at->format('M j, Y') }}</div>
                                            <div class="text-xs text-base-content/50">
                                                @if($file->uploader)
                                                    by {{ $file->uploader->name }}
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <div class="flex gap-2">
                                                <a href="{{ route('files.view', $file) }}" class="btn btn-sm btn-info" target="_blank">
                                                    <i class="fa-sharp fa-eye mr-1"></i>
                                                </a>
                                                <a href="{{ route('files.download', $file) }}" class="btn btn-sm btn-primary">
                                                    <i class="fa-sharp fa-download mr-1"></i>
                                                </a>
                                                @if(Auth::id() === $file->uploaded_by || Auth::user()->isAdmin())
                                                    <button type="button" class="btn btn-sm btn-error"
                                                            onclick="deleteFile('{{ $file->id }}', '{{ isset($certificate->stats['coc_file_id']) && $certificate->stats['coc_file_id'] == $file->id }}')">
                                                        <i class="fa-sharp fa-trash mr-1"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
    <!-- File Notes Modal -->
    <dialog id="fileNotesModal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg mb-4">File Notes</h3>
            <p id="fileNameDisplay" class="text-sm mb-4"></p>

            <div class="form-control">
                <textarea id="fileNotesInput" class="textarea textarea-bordered h-24" readonly></textarea>
            </div>

            <div class="modal-action">
                <button type="button" class="btn" onclick="closeFileNotesModal()">Close</button>
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button onclick="closeFileNotesModal()">close</button>
        </form>
    </dialog>

    <script>
        function openFileNotesModal(fileId, fileName, notes) {
            document.getElementById('fileNameDisplay').textContent = fileName;
            document.getElementById('fileNotesInput').value = notes || 'No notes available for this file.';
            document.getElementById('fileNotesModal').showModal();
        }

        function closeFileNotesModal() {
            document.getElementById('fileNotesModal').close();
        }

        function deleteFile(fileId, isCocDocument) {
            if (!confirm('Are you sure you want to delete this file?')) {
                return;
            }

            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            fetch(`/files/${fileId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to reflect the changes
                    window.location.reload();
                } else {
                    alert('Error deleting file: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error deleting file. Please try again.');
            });
        }
    </script>

    <!-- PDF Regeneration Confirmation Modal -->
    <div id="pdf-confirm-modal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg">Confirm PDF Regeneration</h3>
            <p class="py-4">A PDF for this certificate already exists. Are you sure you want to generate a new version?</p>
            <div class="modal-action">
                <a href="{{ route('certificates.generatePdf', $certificate) }}" class="btn btn-primary">Yes, Generate New PDF</a>
                <button class="btn" onclick="document.getElementById('pdf-confirm-modal').classList.remove('modal-open')">Cancel</button>
            </div>
        </div>
    </div>

    <script>
        function confirmGeneratePdf() {
            document.getElementById('pdf-confirm-modal').classList.add('modal-open');
        }

        // Handle regenerate PDF button click
        document.addEventListener('DOMContentLoaded', function() {
            const regenerateBtn = document.getElementById('regenerate-pdf-btn');
            if (regenerateBtn) {
                regenerateBtn.addEventListener('click', function() {
                    confirmGeneratePdf();
                });
            }
        });
    </script>
</x-app-layout>
