<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate of Destruction - {{ $certificate->certificate_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.3; /* Reduced from 1.6 */
            color: #333;
            margin: 0;
            padding: 10px;
            font-size: 22px;
        }
        p {
            margin-top: 0;
            margin-bottom: 6px; /* Reduced paragraph spacing */
        }
        .logo-container {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px 0;
        }
        .logo-image {
            max-width: 400px; /* Increased from 350px */
            max-height: 400px; /* Increased from 250px */
            margin: 0 auto;
            display: block;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
        }
        .header h1 {
            margin: 0;
            color: #000;
            font-size: 40px;
            margin-bottom: 10px;
        }
        .header p {
            margin: 5px 0;
            font-size: 24px;
            color: #444;
        }
        .certificate-info-container {
            margin-bottom: 20px;
        }
        .company-info {
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .company-info h3 {
            margin: 0 0 5px 0;
            font-size: 28px;
            color: #333;
        }
        .company-info p {
            margin: 2px 0;
            font-size: 20px;
        }
        .info-columns {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 70px;
        }
        .certificate-info, .customer-info {
            width: 48%;
            vertical-align: top;
            padding: 0 10px;
        }
        .certificate-info h3, .customer-info h3 {
            margin: 0 0 8px 0;
            font-size: 24px;
            color: #333;
            padding-bottom: 3px;
        }
        .certificate-info table, .customer-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .certificate-info table td, .customer-info table td {
            padding: 3px 5px;
            font-size: 20px;
            vertical-align: top;
        }
        .certificate-info table td:first-child, .customer-info table td:first-child {
            font-weight: bold;
            width: 120px;
        }
        .mt-4 {
            margin-top: 15px;
        }
        .certificate-notes h3 {
            margin: 0 0 8px 0;
            font-size: 20px;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 3px;
        }
        .notes-content {
            font-size: 20px;
            line-height: 1.4;
        }
        .notes-content p {
            margin-bottom: 5px;
        }
        .notes-content ul, .notes-content ol {
            margin-left: 15px;
            margin-bottom: 5px;
        }
        .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        .section h2 {
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
            font-size: 22px;
            margin-top: 30px;
        }
        .contract-text {
            font-size: 18px;
            margin-bottom: 15px;
            text-align: justify;
        }
        .contract-text p {
            margin-bottom: 8px; /* Reduced spacing between paragraphs */
        }
        .signature-container {
            margin-top: 20px;
            border-top: 1px dashed #ccc;
            padding-top: 10px;
        }
        .signature-image {
            max-width: 400px; /* Increased by 50% from 400px */
            margin-bottom: 15px;
            width: 100%;
            height: auto;
        }
        .signature-info {
            font-size: 12px;
        }
        .signature-info p {
            margin: 3px 0;
        }
        .page-break {
            page-break-after: always;
        }
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            font-size: 14px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
            background-color: white;
            padding-bottom: 10px;
            display: flex;
            align-items: flex-start;
        }
        .footer-logo {
            width: 240px;
            margin-right: 15px;
            position: absolute;
            bottom: 10px;
            left: 10px;
        }
        .footer-text {
            flex: 1;
            text-align: center;
        }
        .footer-space {
            height: 100px; /* Space to prevent content from overlapping with footer */
        }
        .page-content {
            margin-bottom: 100px; /* Ensure content doesn't overlap with footer */
        }
        /* First page styling */
        .first-page {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="page-content">
        <div class="first-page">
            <div class="logo-container">
                <img src="{{ public_path('img/etech-cert-destruction-logo.png') }}" alt="E-Tech Certificate of Destruction" class="logo-image">
            </div>

            <div class="header">
                <h1>Certificate of Destruction</h1>
                <p>Certificate Number: {{ $certificate->certificate_number }}</p>
                <p>Version: {{ $version ?? '1.0' }} | Generated: {{ now()->format('F j, Y g:i A') }}</p>

                @php
                    // Check if a Chain of Custody document has been uploaded
                    $cocFileId = $certificate->stats['coc_file_id'] ?? null;
                    $hasUploadedCOC = !empty($cocFileId);

                    // Only check for missing signatures if no COC document was uploaded
                    $missingSignatures = [];

                    if (!$hasUploadedCOC) {
                        $requiredRoles = ['client', 'technician', 'warehouse'];

                        // Only include driver as required if it's not a drop-off
                        if (($certificate->stats['driver_pickup_dropoff'] ?? '') !== 'No') {
                            $requiredRoles[] = 'driver';
                        }

                        foreach ($requiredRoles as $role) {
                            if (!isset($contractData[$role])) {
                                $missingSignatures[] = ucfirst($role);
                            }
                        }
                    }
                @endphp

                @if(!empty($missingSignatures) && !$hasUploadedCOC)
                <div style="margin-top: 10px; padding: 10px; border: 2px solid #f44336; background-color: #ffebee; color: #d32f2f;">
                    <p style="font-weight: bold; margin: 0;">WARNING: Incomplete Certificate</p>
                    <p style="margin: 5px 0 0 0;">The following required signatures are missing: {{ implode(', ', $missingSignatures) }}</p>
                </div>
                @elseif($hasUploadedCOC)
                <div style="margin-top: 10px; padding: 10px; border: 2px solid #4caf50; background-color: #e8f5e9; color: #2e7d32;">
                    <p style="font-weight: bold; margin: 0;">Chain of Custody Document Uploaded</p>
                    <p style="margin: 5px 0 0 0;">A Chain of Custody document has been uploaded for this certificate.</p>
                </div>
                @endif
            </div>

            <table class="info-columns">
                <tr>
                    <!-- Certificate Information -->
                    <td class="certificate-info">
                        <h3>Certificate Details</h3>
                        <table>
                        <tr>
                            <td>Certificate Number:</td>
                            <td>{{ $certificate->certificate_number }}</td>
                        </tr>
                        <tr>
                            <td>Scheduled Destruction:</td>
                            <td>{{ $certificate->scheduled_destruction_date ? $certificate->scheduled_destruction_date->format('F j, Y') : 'Not scheduled' }}</td>
                        </tr>
                        <tr>
                            <td>Status:</td>
                            <td>
                                {{ ucfirst(strtolower($certificate->status)) }}
                            </td>
                        </tr>
                        <tr>
                            <td>Actual Destruction:</td>
                            <td>{{ $certificate->actual_destruction_date ? $certificate->actual_destruction_date->format('F j, Y g:i A') : 'Not completed' }}</td>
                        </tr>
                        <tr>
                            <td>Destruction Method:</td>
                            <td>{{ $certificate->destruction_method ?? 'Not specified' }}</td>
                        </tr>
                        <tr>
                            <td>Destruction Location:</td>
                            <td>{{ $certificate->destruction_location ?? 'Not specified' }}</td>
                        </tr>
                        <tr>
                            <td>Devices Destroyed:</td>
                            <td>{{ $certificate->devices->where('is_destroyed', true)->count() }} of {{ $deviceCount }}</td>
                        </tr>
                        </table>


                    </td>

                    <!-- Customer Information -->
                    <td class="customer-info">
                    <h3>Customer Information{{ $certificate->customer && $certificate->customer->type === 'Business' ? ' - ' . $certificate->customer->name : '' }}</h3>
                    <table>
                        @if($certificate->customer)
                        <tr>
                            <td>{{ $certificate->customer->type === 'Business' ? 'Business Name' : 'Name' }}:</td>
                            <td>{{ $certificate->customer->name }}</td>
                        </tr>
                        @if($certificate->customer->type === 'Business' && $certificate->customer->contact)
                        <tr>
                            <td>Contact Person:</td>
                            <td>{{ $certificate->customer->contact }}</td>
                        </tr>
                        @endif
                        <tr>
                            <td>Email:</td>
                            <td>{{ $certificate->customer->email ?: 'Not provided' }}</td>
                        </tr>
                        <tr>
                            <td>Phone:</td>
                            <td>{{ $certificate->customer->phone ?: 'Not provided' }}</td>
                        </tr>
                        <tr>
                            <td>Address:</td>
                            <td>{{ $certificate->customer->address ?: 'Not provided' }}</td>
                        </tr>

                        @else
                        <tr>
                            <td colspan="2">No customer information available</td>
                        </tr>
                        @endif
                    </table>

                    <!-- Certifying Technician Information -->
                    @if(isset($certifyingTechnician))
                    <h3 class="mt-4">Certifying Technician</h3>
                    <table>
                        <tr>
                            <td>Name:</td>
                            <td>{{ $certifyingTechnician->name }}</td>
                        </tr>
                        <tr>
                            <td>Email:</td>
                            <td>{{ $certifyingTechnician->email }}</td>
                        </tr>
                    </table>
                    @endif
                    </td>
                </tr>
            </table>

            <!-- Company Information -->
            <div class="company-info">
                <img src="{{ public_path('img/company-logo.png') }}" alt="Company Logo" style="width: 360px; height: auto; margin: 0 auto 20px auto; display: block;">
                <h3>{{ $companyData['name'] ?? 'E-Tech Recyclers & Asset Solutions LLC' }}</h3>
                <p>{{ $companyData['address'] ?? '123 Main Street, Anytown, TX 12345' }}</p>
                <p>Phone: {{ $companyData['phone'] ?? '(*************' }} | Website: {{ $companyData['website'] ?? 'www.example.com' }}</p>
                <p>Email: {{ $companyData['email'] ?? '<EMAIL>' }}</p>
            </div>

        </div><!-- End of first-page -->

        <!-- Certificate Notes Page if available -->
        @if($certificate->notes)
        <div class="section">
            <h2>Certificate Notes</h2>
            <div class="notes-content p-3 bg-gray-50 rounded border border-gray-200 mt-2">
                {!! $certificate->notes !!}
            </div>
        </div>
        <div class="page-break"></div>
        @endif

        <div class="section">
            <h2>Signatures</h2>

            @if(isset($contractData['client']))
            <div class="section">
                <h2>Client Certification</h2>
                <div class="contract-text">
                    {!! $contractData['client']['contractText'] !!}
                </div>
                <div class="signature-container">
                    @if($contractData['client']['signatureImageData'])
                        <img class="signature-image" src="{{ $contractData['client']['signatureImageData'] }}" alt="Client Signature">
                    @endif
                    <div class="signature-info">
                        <p><strong>Signatory:</strong> {{ $contractData['client']['signature']->signatory_name ?? 'Unknown' }}</p>
                        <p><strong>Date:</strong> {{ $contractData['client']['signature']->signature_date ? $contractData['client']['signature']->signature_date->format('F j, Y g:i A') : 'Date not recorded' }}</p>
                        @if(isset($contractData['client']['signature']->ip_address))
                        <p><strong>IP Address:</strong> {{ $contractData['client']['signature']->ip_address }}</p>
                        @endif
                    </div>
                </div>
            </div>
            <div class="page-break"></div>
            @endif

            <!-- Driver Section - Always included -->
            <div class="section">
                <h2>Driver Certification</h2>
                @if(isset($contractData['driver']))
                <!-- Driver signature exists -->
                <div class="contract-text">
                    {!! $contractData['driver']['contractText'] !!}
                </div>
                <div class="signature-container">
                    @if($contractData['driver']['signatureImageData'])
                        <img class="signature-image" src="{{ $contractData['driver']['signatureImageData'] }}" alt="Driver Signature">
                    @endif
                    <div class="signature-info">
                        <p><strong>Signatory:</strong> {{ $contractData['driver']['signature']->signatory_name ?? 'Unknown' }}</p>
                        <p><strong>Date:</strong> {{ $contractData['driver']['signature']->signature_date ? $contractData['driver']['signature']->signature_date->format('F j, Y g:i A') : 'Date not recorded' }}</p>
                        @if(isset($contractData['driver']['signature']->ip_address))
                        <p><strong>IP Address:</strong> {{ $contractData['driver']['signature']->ip_address }}</p>
                        @endif
                    </div>
                </div>
                @else
                <!-- No driver signature - check if drop-off or if COC was uploaded -->
                @php
                    $cocFileId = $certificate->stats['coc_file_id'] ?? null;
                    $hasUploadedCOC = !empty($cocFileId);
                @endphp

                @if(($certificate->stats['driver_pickup_dropoff'] ?? '') === 'No')
                <div style="padding: 15px; background-color: #f5f5f5; border: 1px solid #ddd; margin-bottom: 20px;">
                    <p style="font-size: 24px; margin: 0;"><strong>Note:</strong> The devices were dropped off directly by the client. No driver signature is required.</p>
                </div>
                @elseif($hasUploadedCOC)
                <div style="padding: 15px; background-color: #f5f5f5; border: 1px solid #ddd; margin-bottom: 20px;">
                    <p style="font-size: 24px; margin: 0;"><strong>Note:</strong> A Chain of Custody document has been uploaded. Digital driver signature was not required.</p>
                </div>
                @else
                <div style="padding: 15px; background-color: #ffebee; border: 1px solid #f44336; margin-bottom: 20px;">
                    <p style="font-size: 24px; margin: 0; color: #d32f2f;"><strong>Missing:</strong> Driver signature was not collected.</p>
                </div>
                @endif
                @endif
            </div>
            <div class="page-break"></div>

            @if(isset($contractData['warehouse']))
            <div class="section">
                <h2>Warehouse Receiving Certification</h2>
                <div class="contract-text">
                    {!! $contractData['warehouse']['contractText'] !!}
                </div>
                <div class="signature-container">
                    @if($contractData['warehouse']['signatureImageData'])
                        <img class="signature-image" src="{{ $contractData['warehouse']['signatureImageData'] }}" alt="Warehouse Signature">
                    @endif
                    <div class="signature-info">
                        <p><strong>Signatory:</strong> {{ $contractData['warehouse']['signature']->signatory_name ?? 'Unknown' }}</p>
                        <p><strong>Date:</strong> {{ $contractData['warehouse']['signature']->signature_date ? $contractData['warehouse']['signature']->signature_date->format('F j, Y g:i A') : 'Date not recorded' }}</p>
                        @if(isset($contractData['warehouse']['signature']->ip_address))
                        <p><strong>IP Address:</strong> {{ $contractData['warehouse']['signature']->ip_address }}</p>
                        @endif
                    </div>
                </div>
            </div>
            <div class="page-break"></div>
            @endif

            @if(isset($contractData['technician']))
            <div class="section">
                <h2>Technician Certification</h2>
                <div class="contract-text">
                    {!! $contractData['technician']['contractText'] !!}
                </div>
                <div class="signature-container">
                    @if($contractData['technician']['signatureImageData'])
                        <img class="signature-image" src="{{ $contractData['technician']['signatureImageData'] }}" alt="Technician Signature">
                    @endif
                    <div class="signature-info">
                        <p><strong>Signatory:</strong> {{ $contractData['technician']['signature']->signatory_name ?? 'Unknown' }}</p>
                        <p><strong>Date:</strong> {{ $contractData['technician']['signature']->signature_date ? $contractData['technician']['signature']->signature_date->format('F j, Y g:i A') : 'Date not recorded' }}</p>
                        @if(isset($contractData['technician']['signature']->ip_address))
                        <p><strong>IP Address:</strong> {{ $contractData['technician']['signature']->ip_address }}</p>
                        @endif
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Add space at the bottom to prevent content from overlapping with footer -->
        <div class="footer-space"></div>
    </div><!-- End of page-content -->

    <div class="footer">
        <div class="footer-logo">
            <img src="{{ public_path('img/company-logo.png') }}" alt="Company Logo" style="width: 100%;">
        </div>
        <div class="footer-text">
            @php
                // Replace placeholders in the footer text
                $certificateInfoLine = str_replace(
                    ['[GENERATION_DATE]', '[CERTIFICATE_NUMBER]', '[VERSION]'],
                    [now()->format('F j, Y g:i A'), $certificate->certificate_number, $version ?? '1.0'],
                    $footerData['certificate_info_line'] ?? 'Certificate generated on ' . now()->format('F j, Y g:i A')
                );
            @endphp

            <p>{{ $certificateInfoLine }}</p>

            @if(isset($footerData['record_line']))
                <p>{{ $footerData['record_line'] }}</p>
            @endif

            @if(isset($footerData['guarantee_line']))
                <p>{{ $footerData['guarantee_line'] }}</p>
            @endif

            @if(isset($footerData['data_line']))
                <p>{{ $footerData['data_line'] }}</p>
            @endif

            @if(isset($footerData['contact_info']))
                <p>{{ $footerData['contact_info'] }}</p>
            @endif
        </div>
    </div>
</body>
</html>
