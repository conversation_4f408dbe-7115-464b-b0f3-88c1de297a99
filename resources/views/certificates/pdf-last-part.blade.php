<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate of Destruction - Device Inventory</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.4; /* Reduced from 1.6 */
            color: #333;
            margin: 0;
            padding: 15px; /* Reduced from 20px */
            font-size: 22px;
        }
        p {
            margin-top: 0;
            margin-bottom: 6px; /* Reduced paragraph spacing */
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
        }
        .header h1 {
            margin: 0;
            color: #000;
            font-size: 40px;
            margin-bottom: 10px;
        }
        .header p {
            margin: 5px 0;
            font-size: 24px;
            color: #444;
        }
        .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        .section h2 {
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
            font-size: 22px;
            margin-top: 30px;
        }
        .device-cards {
            width: 100%;
            margin-top: 20px;
            border-collapse: separate;
            border-spacing: 10px;
        }
        .device-cards-row {
            vertical-align: top;
        }
        .device-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background-color: #f9f9f9;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            page-break-inside: avoid;
            position: relative;
            width: 100%;
            display: block;
        }
        .device-card.destroyed {
            border-left: 4px solid #2e7d32;
        }
        .device-card-header {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #eee;
            padding-bottom: 4px; /* Reduced from 5px */
            margin-bottom: 6px; /* Reduced from 8px */
        }
        .device-card-number {
            font-weight: bold;
            font-size: 18px;
            color: #333;
        }
        .device-card-serial {
            font-family: monospace;
            font-weight: bold;
            font-size: 16px;
        }
        .device-card-details {
            display: flex;
            flex-wrap: wrap;
            gap: 8px; /* Reduced from 10px */
            margin-bottom: 6px; /* Reduced from 8px */
        }
        .device-card-detail {
            flex: 1;
            min-width: 120px;
        }
        .device-card-label {
            font-weight: bold;
            font-size: 14px;
            color: #666;
            margin-bottom: 2px;
        }
        .device-card-value {
            font-size: 16px;
        }
        .device-card-notes {
            border-top: 1px dashed #eee;
            padding-top: 5px;
            margin-top: 5px;
            font-size: 15px;
            font-style: italic;
            color: #555;
        }
        .device-card-destruction {
            margin-top: 5px;
            padding: 5px;
            background-color: #e8f5e9;
            border-radius: 3px;
            font-size: 15px;
            color: #2e7d32;
        }
        .page-break {
            page-break-after: always;
        }
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            font-size: 14px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
            background-color: white;
            padding-bottom: 10px;
            display: flex;
            align-items: flex-start;
        }
        .footer-logo {
            width: 240px;
            margin-right: 15px;
            position: absolute;
            bottom: 10px;
            left: 10px;
        }
        .footer-text {
            flex: 1;
            text-align: center;
        }
        .footer-space {
            height: 100px; /* Space to prevent content from overlapping with footer */
        }
        .page-content {
            margin-bottom: 100px; /* Ensure content doesn't overlap with footer */
        }
    </style>
</head>
<body>
    <div class="page-content">
        <div class="section">
            <h2>Device Inventory</h2>
            <p>The following devices were processed under this certificate:</p>

            @if($certificate->devices->count() > 0)
                @php
                    // Sort devices by ID to ensure consistent ordering
                    $sortedDevices = $certificate->devices->sortBy('id');
                    $totalDevices = count($sortedDevices);
                @endphp

                <table class="device-cards">
                    @php $counter = 0; @endphp
                    @foreach($sortedDevices as $index => $device)
                        @if($counter % 2 == 0)
                            <tr class="device-cards-row">
                        @endif

                        <td width="50%">
                            <div class="device-card {{ $device->is_destroyed ? 'destroyed' : '' }}">
                                <div class="device-card-header">
                                    <div class="device-card-number">Device #{{ $index + 1 }}</div>
                                    <div class="device-card-serial">SN: {!! nl2br(wordwrap($device->serial_number, 25, "
", true)) !!}</div>
                                </div>

                                <div class="device-card-details">
                                    @if($device->type)
                                    <div class="device-card-detail">
                                        <div class="device-card-label">Device Type</div>
                                        <div class="device-card-value word-break">{{ $device->type }}</div>
                                    </div>
                                    @endif

                                    @if($device->manufacturer)
                                    <div class="device-card-detail">
                                        <div class="device-card-label">Manufacturer</div>
                                        <div class="device-card-value">{{ $device->manufacturer }}</div>
                                    </div>
                                    @endif

                                    @if($device->model)
                                    <div class="device-card-detail">
                                        <div class="device-card-label">Model</div>
                                        <div class="device-card-value truncate" title="{{ $device->model }}">{!! nl2br(wordwrap($device->model, 25, "
", true)) !!}</div>
                                    </div>
                                    @endif

                                    <div class="device-card-detail">
                                        <div class="device-card-label">Scanned Date</div>
                                        <div class="device-card-value">{{ $device->created_at->format('M j, Y g:i A') }}</div>
                                    </div>

                                    @if($device->is_destroyed && $device->destroyed_at)
                                    <div class="device-card-detail detail-separator" style="flex-basis: 100%;">
                                        <div class="device-card-label">Destroyed Date</div>
                                        <div class="device-card-value">{{ $device->destroyed_at->format('M j, Y g:i A') }}
                                        @if($device->destroyedByUser)
                                            <span style="font-size: 14px; color: #666;">by {{ $device->destroyedByUser->name }}</span>
                                        @endif
                                        </div>
                                    </div>
                                    @endif

                                    @if($device->pc_manufacturer || $device->pc_serial_number || $device->pc_form_factor)
                                    <div class="device-card-detail detail-separator" style="flex-basis: 100%;">
                                        <div class="device-card-label">Origin PC Details</div>
                                        <div class="device-card-value">
                                            @if($device->pc_manufacturer)<strong>Mfr:</strong> {{ $device->pc_manufacturer }}@endif
                                            @if($device->pc_serial_number)<strong>Serial:</strong> {{ $device->pc_serial_number }}@endif
                                            @if($device->pc_form_factor)<strong>Form Factor:</strong> {{ $device->pc_form_factor }}@endif
                                        </div>
                                    </div>
                                    @endif
                                </div>

                                @if($device->is_destroyed)
                                    <div class="device-card-destruction">
                                        <strong>Status:</strong> Destroyed
                                    </div>
                                @endif

                                @if(!empty($device->notes))
                                    <div class="device-card-notes">
                                        <strong>Notes:</strong> {{ $device->notes }}
                                    </div>
                                @endif
                            </div>
                        </td>

                        @php $counter++; @endphp
                        @if($counter % 2 == 0 || $loop->last)
                            @if($loop->last && $counter % 2 != 0)
                                <td width="50%"></td>
                            @endif
                            </tr>
                        @endif
                    @endforeach
                </table>
            @else
                <p>No devices were recorded for this certificate.</p>
            @endif
        </div>

        <!-- Add space at the bottom to prevent content from overlapping with footer -->
        <div class="footer-space"></div>
    </div><!-- End of page-content -->

    <div class="footer">
        <div class="footer-logo">
            <img src="{{ public_path('img/company-logo.png') }}" alt="Company Logo" style="width: 100%;">
        </div>
        <div class="footer-text">
            @php
                // Replace placeholders in the footer text
                $certificateInfoLine = str_replace(
                    ['[GENERATION_DATE]', '[CERTIFICATE_NUMBER]', '[VERSION]'],
                    [now()->format('F j, Y g:i A'), $certificate->certificate_number, $version ?? '1.0'],
                    $footerData['certificate_info_line'] ?? 'Certificate generated on ' . now()->format('F j, Y g:i A')
                );
            @endphp

            <p>{{ $certificateInfoLine }}</p>

            @if(isset($footerData['record_line']))
                <p>{{ $footerData['record_line'] }}</p>
            @endif

            @if(isset($footerData['guarantee_line']))
                <p>{{ $footerData['guarantee_line'] }}</p>
            @endif

            @if(isset($footerData['data_line']))
                <p>{{ $footerData['data_line'] }}</p>
            @endif

            @if(isset($footerData['contact_info']))
                <p>{{ $footerData['contact_info'] }}</p>
            @endif
        </div>
    </div>
</body>
</html>
