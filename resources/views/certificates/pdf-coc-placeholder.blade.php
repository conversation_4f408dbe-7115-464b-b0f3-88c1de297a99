<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chain of Custody Document</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 15px;
            font-size: 22px;
        }
        .container {
            text-align: center;
            margin: 100px auto;
            max-width: 600px;
        }
        h1 {
            font-size: 28px;
            margin-bottom: 20px;
            color: #333;
        }
        p {
            font-size: 20px;
            margin-bottom: 10px;
        }
        .alert {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: left;
        }
        .file-info {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chain of Custody Document</h1>
        
        <p>A Chain of Custody document was uploaded for this certificate but could not be included in this PDF.</p>
        
        <div class="file-info">
            <p><strong>File Name:</strong> {{ $cocFile->original_filename }}</p>
            <p><strong>File Type:</strong> {{ $cocFile->mime_type }}</p>
            <p><strong>Uploaded:</strong> {{ $cocFile->created_at->format('F j, Y g:i A') }}</p>
            @if($cocFile->uploader)
                <p><strong>Uploaded By:</strong> {{ $cocFile->uploader->name }}</p>
            @endif
        </div>
        
        <p>Please access the original Chain of Custody document separately.</p>
        
        @if(isset($error))
            <div class="alert">
                <p><strong>Error:</strong> {{ $error }}</p>
                <p>This error is only visible in this PDF and does not affect the validity of the certificate.</p>
            </div>
        @endif
    </div>
</body>
</html>
