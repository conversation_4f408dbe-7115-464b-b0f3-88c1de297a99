<x-app-layout
    page-title="Create Certificate of Destruction"
    page-icon="fa-sharp fa-file-certificate"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('certificates.index'),
            'text' => 'Back to Certificates'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Certificates', 'route' => 'certificates.index', 'icon' => 'fa-certificate'],
        ['name' => 'Create Certificate', 'icon' => 'fa-file-certificate']
    ]">

    <div class="py-6 lg:py-8">


        <!-- Page Content -->
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-base-100 border-b border-base-300">

                    <form action="{{ route('certificates.store') }}" method="POST">
                        @csrf

                        <div class="mb-4">
                            <label for="certificate_number" class="block text-base-content text-sm font-bold mb-2">Certificate Number:</label>
                            <input type="text" name="certificate_number" id="certificate_number" class="shadow bg-base-200 border border-base-300 rounded w-full py-2 px-3 text-base-content leading-tight focus:outline-none focus:shadow-outline" value="{{ $certificate_number }}" readonly>
                        </div>

                        <div class="mb-4">
                            <label for="customerSearch" class="block text-base-content text-sm font-bold mb-2">Customer:</label>
                            <p class="text-base-content/70 text-sm mb-2">Start typing to see if the customer is in our system. If they are not in the system, click the "Create New Customer" button.</p>
                            <p class="text-base-content/70 text-sm mb-2">If they are a business customer, enter their BUSINESS NAME first.</p>
                            <div class="relative">
                                <!-- Dynamic Search Component -->
                                <x-dynamic-customer-search
                                    id="customerSearch"
                                    name="customer_id"
                                    placeholder="Search for customers by name, business name, email, phone, or nickname..."
                                    action="form"
                                    quickCreateButtonId="quickCreateCustomerBtn"
                                />
                                <!-- Quick Create Button -->
                                <x-quick-create-customer
                                    id="customerSearch"
                                    name="customer_id"
                                />
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="pickup_dropoff_date" class="block text-base-content text-sm font-bold mb-2">Acceptance Date & Time:</label>
                            <p class="text-base-content/70 text-sm mb-2">Select the date and time when the drives were picked up or dropped off. When they were transferred from the client to the contractor (E-Tech).</p>
                            <input type="datetime-local" name="pickup_dropoff_date" id="pickup_dropoff_date" class="shadow border border-base-300 rounded w-full py-2 px-3 text-base-content leading-tight focus:outline-none focus:shadow-outline">
                        </div>

                        <div class="mb-4">
                            <label for="scheduled_destruction_date" class="block text-base-content text-sm font-bold mb-2">Scheduled Destruction Date:</label>
                            <p class="text-base-content/70 text-sm mb-2">Select the date when the destruction is scheduled to occur. Default is two business days from today.</p>
                            <input type="date" name="scheduled_destruction_date" id="scheduled_destruction_date" class="shadow border border-base-300 rounded w-full py-2 px-3 text-base-content leading-tight focus:outline-none focus:shadow-outline" value="{{ date('Y-m-d') }}">
                        </div>



                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                // Function to calculate date that is N business days from today
                                function getBusinessDaysFromToday(businessDays) {
                                    let date = new Date();
                                    let count = 0;

                                    while (count < businessDays) {
                                        // Add one day
                                        date.setDate(date.getDate() + 1);

                                        // Check if it's a weekday (0 = Sunday, 6 = Saturday)
                                        if (date.getDay() !== 0 && date.getDay() !== 6) {
                                            count++;
                                        }
                                    }

                                    return date;
                                }

                                // Format date as YYYY-MM-DD for input field
                                function formatDateForInput(date) {
                                    const year = date.getFullYear();
                                    // Month is 0-indexed, so add 1 and pad with leading zero if needed
                                    const month = String(date.getMonth() + 1).padStart(2, '0');
                                    const day = String(date.getDate()).padStart(2, '0');

                                    return `${year}-${month}-${day}`;
                                }

                                // Set the date field to two business days from today
                                const twoBusinessDays = getBusinessDaysFromToday(2);
                                document.getElementById('scheduled_destruction_date').value = formatDateForInput(twoBusinessDays);

                                // Set pickup/dropoff date to current date and time
                                const now = new Date();
                                const year = now.getFullYear();
                                const month = String(now.getMonth() + 1).padStart(2, '0');
                                const day = String(now.getDate()).padStart(2, '0');
                                const hours = String(now.getHours()).padStart(2, '0');
                                const minutes = String(now.getMinutes()).padStart(2, '0');
                                const currentDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;
                                document.getElementById('pickup_dropoff_date').value = currentDateTime;
                            });
                        </script>

                        <button type="submit" class="btn btn-primary">
                            <i class="fa-sharp fa-file-certificate"></i>
                            Create Chain of Custody Document
                        </button>
                    </form>

                </div>
            </div>
        </div>
    </div>



</x-app-layout>
