<x-app-layout
    page-title="Certificates of Destruction"
    page-icon="fa-sharp fa-certificate"
    :primary-buttons="[
        [
            'type' => 'create',
            'route' => route('certificates.create'),
            'text' => 'New Certificate'
        ]
    ]"
    :action-buttons="[
        [
            'name' => request('status') === 'trashed' ? 'View Active' : 'View Trashed',
            'route' => request('status') === 'trashed'
                ? route('certificates.index', request()->except(['status', 'page']))
                : route('certificates.index', array_merge(request()->except(['status', 'page']), ['status' => 'trashed'])),
            'icon' => request('status') === 'trashed' ? 'fa-sharp fa-eye' : 'fa-sharp fa-trash',
            'class' => 'btn ' . (request('status') === 'trashed' ? 'btn-primary' : 'btn-secondary') . ' btn-sm gap-2'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Certificates', 'icon' => 'fa-certificate']
    ]">

    <div class="py-6 lg:py-8 space-y-8">
        <!-- Page Content -->
        <div class="mx-auto sm:px-6 lg:px-8 space-y-8">
            <!-- Header Card -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-base-content">Certificate Management</h1>
                            <p class="text-base-content/70 mt-1 text-lg">Manage certificates of destruction and chain of custody documents.</p>
                        </div>
                        <div class="hidden lg:flex items-center gap-2 text-base-content/60">
                            <div class="stat">
                                <div class="stat-title text-xs">Total Certificates</div>
                                <div class="stat-value text-2xl">{{ $certificates->total() }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search & Filters Card -->
            <div class="card  bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-magnifying-glass text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Search & Filter</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    <!-- Search Section -->
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">Quick Search</span>
                            <span class="label-text-alt">Search by certificate number or customer name</span>
                        </label>
                        <x-dynamic-certificate-search
                            id="certificateSearch"
                            name="certificate_id"
                            placeholder="Search for certificates by number or customer name..."
                            action="navigate"
                        />
                    </div>

                    <!-- Filters Section -->
                    <form method="GET" action="{{ route('certificates.index') }}" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- Preserve existing query parameters except the ones we're setting -->
                        @foreach(request()->except(['status', 'page']) as $key => $value)
                            <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                        @endforeach

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">Status</span>
                                <span class="label-text-alt">
                                    @if(request('status'))
                                        <span class="badge badge-primary badge-xs">Filtered</span>
                                    @endif
                                </span>
                            </label>
                            <select name="status" class="select select-bordered w-full" onchange="this.form.submit()">
                                <option value="">All Statuses</option>
                                <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="verifying" {{ request('status') === 'verifying' ? 'selected' : '' }}>Verifying</option>
                                <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="trashed" {{ request('status') === 'trashed' ? 'selected' : '' }}>Trashed</option>
                            </select>
                        </div>

                        <div class="form-control lg:flex lg:justify-end lg:items-end">
                            <div class="flex gap-2">
                                @if(request('status'))
                                    <a href="{{ route('certificates.index', request()->except(['status', 'page'])) }}"
                                       class="btn btn-ghost btn-sm gap-2">
                                        <i class="fa-sharp fa-xmark"></i>
                                        Clear Filters
                                    </a>
                                @endif
                                <button type="submit" class="btn btn-primary btn-sm gap-2">
                                    <i class="fa-sharp fa-magnifying-glass"></i>
                                    Apply Filters
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Certificate Directory Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-list text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Certificate Directory</h4>
                        </div>
                        <div class="flex items-center gap-2 text-sm text-base-content/60">
                            <span>Showing {{ $certificates->firstItem() ?? 0 }}-{{ $certificates->lastItem() ?? 0 }} of {{ $certificates->total() }}</span>
                        </div>
                    </div>
                </div>

                <!-- Sorting Controls -->
                <div class="px-6 py-4 border-b border-base-300/50 bg-base-50">
                    <form method="GET" action="{{ route('certificates.index') }}" class="flex flex-wrap items-center gap-4">
                        <!-- Preserve existing query parameters except sort and order -->
                        @foreach(request()->except(['sort', 'order', 'page']) as $key => $value)
                            <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                        @endforeach

                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium text-base-content/70">Sort by:</label>
                            <select name="sort" class="select select-bordered select-sm w-auto min-w-32" onchange="this.form.submit()">
                                <option value="certificate_number" {{ $sort === 'certificate_number' ? 'selected' : '' }}>Certificate Number</option>
                                <option value="customer_name" {{ $sort === 'customer_name' ? 'selected' : '' }}>Customer</option>
                                <option value="status" {{ $sort === 'status' ? 'selected' : '' }}>Status</option>
                                <option value="actual_destruction_date" {{ $sort === 'actual_destruction_date' ? 'selected' : '' }}>Destruction Date</option>
                                <option value="pickup_dropoff_date" {{ $sort === 'pickup_dropoff_date' ? 'selected' : '' }}>Pickup/Dropoff Date</option>
                                <option value="created_at" {{ $sort === 'created_at' ? 'selected' : '' }}>Created</option>
                            </select>
                        </div>

                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium text-base-content/70">Order:</label>
                            <select name="order" class="select select-bordered select-sm w-auto" onchange="this.form.submit()">
                                <option value="asc" {{ $order === 'asc' ? 'selected' : '' }}>Ascending</option>
                                <option value="desc" {{ $order === 'desc' ? 'selected' : '' }}>Descending</option>
                            </select>
                        </div>
                    </form>
                </div>

                <!-- Desktop Header Row -->
                <div class="hidden lg:block px-6 py-3 bg-base-200/30 border-b border-base-300/50">
                    <div class="grid grid-cols-12 gap-3 text-xs font-medium text-base-content/70 uppercase tracking-wide">
                        <div class="col-span-2">Certificate #</div>
                        <div class="col-span-2">Customer</div>
                        <div class="col-span-2">Status & Devices</div>
                        <div class="col-span-4">Important Dates</div>
                        <div class="col-span-2 text-center">Actions</div>
                    </div>
                </div>

                <!-- Certificate Cards Section -->
                <div class="divide-y divide-base-200/50">
                    @forelse ($certificates as $certificate)
                        <!-- Certificate Card Row -->
                        <div class="p-3 lg:p-4 hover:bg-base-200/50 transition-all duration-200 {{ $loop->even ? 'bg-base-100' : 'bg-base-200/30' }}">
                            <!-- Mobile Layout -->
                            <div class="lg:hidden">
                                <!-- Certificate Number & Status -->
                                <div class="flex items-center gap-2 mb-2">
                                    <div class="flex-1 min-w-0">
                                        <a href="{{ route('certificates.show', $certificate) }}" class="link-primary font-medium hover:link-hover text-lg">
                                            {{ $certificate->certificate_number }}
                                        </a>
                                        @if(request('status') === 'trashed')
                                            <span class="badge badge-error badge-xs ml-2">Deleted</span>
                                        @endif
                                    </div>
                                    <div class="badge badge-sm {{ match($certificate->status) {
                                        'pending' => 'badge-primary',
                                        'verifying' => 'badge-warning',
                                        'completed' => 'badge-success',
                                        default => 'badge-outline'
                                    } }}">
                                        {{ $certificate->getFormattedStatus() }}
                                    </div>
                                </div>

                                <!-- Two Column Details -->
                                <div class="grid grid-cols-2 gap-3 mb-3 text-xs">
                                    <!-- Left Column -->
                                    <div class="space-y-1">
                                        <!-- Customer -->
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-user text-primary text-xs"></i>
                                            <a href="{{ route('customers.show', $certificate->customer) }}" class="link link-hover truncate hover:text-primary transition-colors text-xs">
                                                {{ Str::limit($certificate->customer->name, 20) }}
                                            </a>
                                        </div>

                                        <!-- Device Count -->
                                        <div class="flex items-center gap-1 text-base-content/60">
                                            <i class="fa-sharp fa-hard-drive text-secondary text-xs"></i>
                                            <span>{{ $certificate->devices->count() }} {{ Str::plural('device', $certificate->devices->count()) }}</span>
                                        </div>

                                        <!-- Date Created -->
                                        <div class="flex items-center gap-1 text-base-content/60">
                                            <i class="fa-sharp fa-plus text-success text-xs"></i>
                                            <span>Created: {{ $certificate->created_at->format('m/d/y') }}</span>
                                        </div>
                                    </div>

                                    <!-- Right Column -->
                                    <div class="space-y-1">
                                        <!-- Pickup/Dropoff Date -->
                                        <div class="flex items-center gap-1 text-base-content/60">
                                            <i class="fa-sharp fa-truck text-base-content text-xs"></i>
                                            <span>Accepted: {{ $certificate->pickup_dropoff_date ? $certificate->pickup_dropoff_date->format('m/d/y g:i A') : 'N/A' }}</span>
                                        </div>

                                        <!-- Completion Date -->
                                        <div class="flex items-center gap-1 text-base-content/60">
                                            <i class="fa-sharp fa-check-circle text-info text-xs"></i>
                                            <span>Completed: {{ $certificate->actual_destruction_date ? $certificate->actual_destruction_date->format('m/d/y') : 'N/A' }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Mobile Action Bar -->
                                <div class="flex gap-2 pt-2 border-t border-base-200/50">
                                    @if(request('status') === 'trashed')
                                        <form action="{{ route('certificates.restore', $certificate->id) }}" method="POST" class="flex-1">
                                            @csrf
                                            <button type="submit" class="btn btn-success btn-sm w-full gap-2"
                                                    onclick="return confirm('Are you sure you want to restore this certificate?')">
                                                <i class="fa-sharp fa-undo"></i>
                                                Restore
                                            </button>
                                        </form>
                                    @else
                                        <a href="{{ route('certificates.show', $certificate) }}" class="btn btn-primary btn-sm flex-1 gap-1">
                                            <i class="fa-sharp fa-eye"></i>
                                            View
                                        </a>
                                        <a href="{{ route('certificates.edit', $certificate) }}" class="btn btn-warning btn-sm flex-1 gap-1">
                                            <i class="fa-sharp fa-pen-to-square"></i>
                                            Edit
                                        </a>
                                        @perms('delete_certificates')
                                        <form action="{{ route('certificates.destroy', $certificate) }}" method="POST" class="flex-1">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-error btn-sm w-full gap-1"
                                                    onclick="return confirm('Are you sure you want to delete this certificate? This action cannot be undone.')">
                                                <i class="fa-sharp fa-trash"></i>
                                                Delete
                                            </button>
                                        </form>
                                        @endperms
                                    @endif
                                </div>
                            </div>

                            <!-- Desktop Layout -->
                            <div class="hidden lg:grid lg:grid-cols-12 gap-3 items-center text-sm">
                                <!-- Certificate Number Column (2 cols) -->
                                <div class="lg:col-span-2">
                                    <div class="flex flex-col">
                                        <a href="{{ route('certificates.show', $certificate) }}" class="link-primary font-medium hover:link-hover text-sm">
                                            {{ $certificate->certificate_number }}
                                        </a>
                                        @if(request('status') === 'trashed')
                                            <span class="badge badge-error badge-xs mt-1 w-fit">Deleted</span>
                                        @endif
                                    </div>
                                </div>

                                <!-- Customer Column (2 cols) -->
                                <div class="lg:col-span-2">
                                    <a href="{{ route('customers.show', $certificate->customer) }}" class="link link-hover hover:text-primary transition-colors text-xs">
                                        {{ Str::limit($certificate->customer->name, 25) }}
                                    </a>
                                </div>

                                <!-- Status & Device Count Column (2 cols) -->
                                <div class="lg:col-span-2">
                                    <div class="flex flex-col gap-1">
                                        <div class="badge badge-sm {{ match($certificate->status) {
                                            'pending' => 'badge-primary',
                                            'verifying' => 'badge-warning',
                                            'completed' => 'badge-success',
                                            default => 'badge-outline'
                                        } }} w-fit">
                                            {{ $certificate->getFormattedStatus() }}
                                        </div>
                                        <div class="text-xs text-base-content/60">
                                            <i class="fa-sharp fa-hard-drive text-secondary text-xs mr-1"></i>
                                            {{ $certificate->devices->count() }} {{ Str::plural('device', $certificate->devices->count()) }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Dates Column (4 cols) -->
                                <div class="lg:col-span-4">
                                    <div class="grid grid-cols-1 gap-1 text-xs text-base-content/60">
                                        <!-- Created Date -->
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-plus text-success text-xs"></i>
                                            <span>Created: {{ $certificate->created_at->format('m/d/y') }}</span>
                                        </div>
                                        <!-- Pickup/Dropoff Date -->
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-truck text-base-content text-xs"></i>
                                            <span>Accepted: {{ $certificate->pickup_dropoff_date ? $certificate->pickup_dropoff_date->format('m/d/y g:i A') : 'N/A' }}</span>
                                        </div>
                                        <!-- Completion Date -->
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-check-circle text-info text-xs"></i>
                                            <span>Completed: {{ $certificate->actual_destruction_date ? $certificate->actual_destruction_date->format('m/d/y') : 'N/A' }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions Column (2 cols) -->
                                <div class="lg:col-span-2">
                                    <div class="flex gap-1 justify-center">
                                        @if(request('status') === 'trashed')
                                            <div class="tooltip" data-tip="Restore Certificate">
                                                <form action="{{ route('certificates.restore', $certificate->id) }}" method="POST" class="inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-circle btn-ghost text-success hover:bg-success hover:text-success-content transition-all"
                                                            onclick="return confirm('Are you sure you want to restore this certificate?')">
                                                        <i class="fa-sharp fa-undo"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        @else
                                            <div class="tooltip" data-tip="View Certificate">
                                                <a href="{{ route('certificates.show', $certificate) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-primary hover:text-primary-content transition-all">
                                                    <i class="fa-sharp fa-eye"></i>
                                                </a>
                                            </div>
                                            <div class="tooltip" data-tip="Edit Certificate">
                                                <a href="{{ route('certificates.edit', $certificate) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-warning hover:text-warning-content transition-all">
                                                    <i class="fa-sharp fa-pen-to-square"></i>
                                                </a>
                                            </div>
                                            @perms('delete_certificates')
                                            <div class="tooltip" data-tip="Delete Certificate">
                                                <form action="{{ route('certificates.destroy', $certificate) }}" method="POST" class="inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-circle btn-ghost text-error hover:bg-error hover:text-error-content transition-all"
                                                            onclick="return confirm('Are you sure you want to delete this certificate? This action cannot be undone.')">
                                                        <i class="fa-sharp fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                            @endperms
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <!-- Empty State -->
                        <div class="text-center text-base-content/70 py-12">
                            <div class="flex flex-col items-center gap-4">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                        <i class="fa-sharp fa-certificate text-2xl"></i>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <h3 class="text-lg font-medium text-base-content/80">No certificates found</h3>
                                    <p class="text-sm text-base-content/60 mt-1">
                                        @if(request()->hasAny(['status', 'search']))
                                            Try adjusting your search or filter criteria
                                        @else
                                            Get started by creating your first certificate
                                        @endif
                                    </p>
                                </div>
                                @if(!request()->hasAny(['status', 'search']))
                                    <a href="{{ route('certificates.create') }}" class="btn btn-primary btn-sm gap-2">
                                        <i class="fa-sharp fa-plus"></i>
                                        Add First Certificate
                                    </a>
                                @endif
                            </div>
                        </div>
                    @endforelse
                </div>

                <!-- Pagination Section -->
                @if($certificates->hasPages())
                    <div class="px-6 py-4 border-t border-base-300/50 bg-base-50">
                        <x-pagination :paginator="$certificates" :pagination="$pagination" />
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
