<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Certificate PDFs') }} - {{ $certificate->certificate_number }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body p-6">
                    <div class="flex items-center mb-4">
                        <i class="fa-sharp fa-file-pdf text-primary mr-2 text-xl"></i>
                        <h2 class="card-title text-xl font-bold">Certificate PDFs</h2>
                    </div>

                    <div class="mb-4">
                        <a href="{{ route('certificates.edit', $certificate) }}" class="btn btn-ghost gap-2">
                            <i class="fa-sharp fa-arrow-left"></i>
                            Back to Certificate
                        </a>
                        <a href="{{ route('certificates.generatePdf', $certificate) }}" class="btn btn-primary gap-2">
                            <i class="fa-sharp fa-file-pdf"></i>
                            Generate New PDF
                        </a>
                    </div>

                    @if(count($pdfs) > 0)
                        <div class="overflow-x-auto">
                            <table class="table table-zebra w-full">
                                <thead>
                                    <tr class="bg-base-200">
                                        <th class="font-bold">Version</th>
                                        <th class="font-bold">File Name</th>
                                        <th class="font-bold">Device Count</th>
                                        <th class="font-bold">Generated By</th>
                                        <th class="font-bold">Generated At</th>
                                        <th class="font-bold">Status</th>
                                        <th class="font-bold text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($pdfs as $pdf)
                                        <tr class="hover">
                                            <td>{{ $pdf->version }}</td>
                                            <td>{{ $pdf->file_name }}</td>
                                            <td>{{ $pdf->device_count }}</td>
                                            <td>{{ $pdf->generatedBy ? $pdf->generatedBy->name : 'Unknown' }}</td>
                                            <td>{{ $pdf->created_at->format('Y-m-d H:i:s') }}</td>
                                            <td>
                                                @if($pdf->is_current)
                                                    <span class="badge badge-success">Current</span>
                                                @else
                                                    <span class="badge badge-ghost">Archived</span>
                                                @endif
                                            </td>
                                            <td class="text-center">
                                                <a href="{{ route('certificates.viewPdf', $pdf) }}" class="btn btn-sm btn-info gap-1 mr-1" target="_blank">
                                                    <i class="fa-sharp fa-eye"></i>
                                                    View
                                                </a>
                                                <a href="{{ route('certificates.downloadPdf', $pdf) }}" class="btn btn-sm btn-primary gap-1">
                                                    <i class="fa-sharp fa-download"></i>
                                                    Download
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="bg-base-200 p-8 rounded-lg text-center">
                            <i class="fa-sharp fa-file-pdf text-gray-400 text-4xl mb-3"></i>
                            <p class="text-gray-600">No PDFs have been generated for this certificate yet.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
