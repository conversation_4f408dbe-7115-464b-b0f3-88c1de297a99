<x-app-layout
    page-title="Edit Certificate: {{ $certificate->certificate_number }}"
    page-icon="fa-sharp fa-file-edit"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('certificates.index'),
            'text' => 'Back to Certificates'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'View Certificate',
            'route' => route('certificates.show', $certificate),
            'icon' => 'fa-sharp fa-eye',
            'class' => 'btn btn-info btn-sm gap-2'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Certificates', 'route' => 'certificates.index', 'icon' => 'fa-certificate'],
        ['name' => 'Edit: ' . $certificate->certificate_number, 'icon' => 'fa-file-edit']
    ]">

    <div class="py-6 lg:py-8">

        <!-- Page Content -->
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            <!-- Tab Navigation -->
            <div class="tabs tabs-bordered tabs-lg mb-6 overflow-x-auto">
                <button type="button" class="tab tab-active whitespace-nowrap" onclick="switchTab('details', this)">
                    <i class="fa-sharp fa-certificate mr-2 hidden sm:inline"></i>
                    <span class="hidden sm:inline">Certificate</span>
                    <span class="sm:hidden">Details</span>
                </button>
                <button type="button" class="tab whitespace-nowrap" onclick="switchTab('devices', this)">
                    <i class="fa-sharp fa-hard-drive mr-2 hidden sm:inline"></i>
                    Devices
                </button>
                <button type="button" class="tab whitespace-nowrap" onclick="switchTab('signatures', this)">
                    <i class="fa-sharp fa-signature mr-2 hidden sm:inline"></i>
                    Signatures
                </button>
                <button type="button" class="tab whitespace-nowrap" onclick="switchTab('documents', this)">
                    <i class="fa-sharp fa-file-upload mr-2 hidden sm:inline"></i>
                    Documents
                </button>
                @perms('view_activity_logs')
                <button type="button" class="tab whitespace-nowrap" onclick="switchTab('activity', this)">
                    <i class="fa-sharp fa-history mr-2 hidden sm:inline"></i>
                    <span class="hidden sm:inline">Activity Log</span>
                    <span class="sm:hidden">Activity</span>
                </button>
                @endperms
            </div>

            <!-- Tab Content -->
            <div class="space-y-6">
                <!-- Certificate Details Tab -->
                <div id="tab-details" class="tab-content">
                    <form id="certificateForm" action="{{ route('certificates.update', $certificate) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="space-y-6">
                            <!-- Basic Information Section -->
                            <div class="card bg-base-100 shadow-lg">
                                <div class="card-body p-6">
                                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                                        <i class="fa-sharp fa-info-circle text-primary mr-2"></i>
                                        Basic Information
                                    </h3>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div class="form-control w-full">
                                            <label class="label">
                                                <span class="label-text font-medium">Certificate Number</span>
                                            </label>
                                            <input type="text" name="certificate_number" id="certificate_number"
                                                class="input input-bordered w-full bg-base-200 border-base-300" value="{{ $certificate->certificate_number }}"
                                                readonly />
                                        </div>

                                        <div class="form-control w-full">
                                            <label class="label">
                                                <span class="label-text font-medium">Status</span>
                                                <span id="status-save-indicator" class="label-text-alt hidden">Saving...</span>
                                            </label>
                                            <div class="flex items-center space-x-4">
                                                @foreach(\App\Models\Certificate::STATUSES as $value => $label)
                                                    <label class="label cursor-pointer justify-start gap-2">
                                                        <input type="radio" name="status" value="{{ $value }}"
                                                            class="radio {{ $value === 'pending' ? 'radio-primary' : ($value === 'verifying' ? 'radio-warning' : 'radio-success') }}"
                                                            {{ $certificate->status === $value ? 'checked' : '' }}
                                                            data-auto-save="true" />
                                                        <span class="label-text">{{ $label }}</span>
                                                    </label>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-4"></div>

                                    <div class="flex items-center justify-between mb-4">
                                        <h3 class="text-lg font-semibold flex items-center">
                                            <i class="fa-sharp fa-user text-primary mr-2"></i>
                                            Customer Information
                                        </h3>
                                        <button type="button" class="btn btn-sm btn-primary gap-2" onclick="openModal()">
                                            <i class="fa-sharp fa-edit"></i>
                                            Change Customer
                                        </button>
                                    </div>
                                    
                                    <div class="bg-base-200 p-4 rounded-lg">
                                        <div class="flex justify-between items-start mb-3">
                                            <h4 class="font-semibold text-lg">{{ $certificate->customer->name }}</h4>
                                            <a href="{{ route('customers.show', $certificate->customer) }}" class="btn btn-xs btn-ghost gap-1">
                                                <i class="fa-sharp fa-external-link"></i>
                                                View Profile
                                            </a>
                                        </div>

                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            @if($certificate->customer->type === 'Business' && $certificate->customer->contact)
                                            <div>
                                                <p class="text-sm font-medium text-base-content/70">Contact Person</p>
                                                <p>{{ $certificate->customer->contact }}</p>
                                            </div>
                                            @endif
                                            <div>
                                                <p class="text-sm font-medium text-base-content/70">Email</p>
                                                <p>{{ $certificate->customer->email ?: 'Not provided' }}</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-base-content/70">Phone</p>
                                                <p>{{ $certificate->customer->phone ?: 'Not provided' }}</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-base-content/70">Customer Type</p>
                                                <span class="badge {{ $certificate->customer->type === 'Business' ? 'badge-primary' : 'badge-secondary' }}">
                                                    {{ $certificate->customer->type }}
                                                </span>
                                            </div>
                                            <div class="md:col-span-2">
                                                <p class="text-sm font-medium text-base-content/70">Address</p>
                                                <p>{{ $certificate->customer->address ?: 'Not provided' }}</p>
                                            </div>
                                            @if($certificate->customer->website)
                                            <div class="md:col-span-2">
                                                <p class="text-sm font-medium text-base-content/70">Website</p>
                                                <a href="https://{{ $certificate->customer->website }}" target="_blank" class="link link-primary">
                                                    {{ $certificate->customer->website }}
                                                </a>
                                            </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Timeline & Scheduling Section -->
                            <div class="card bg-base-100 shadow-lg">
                                <div class="card-body p-6">
                                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                                        <i class="fa-sharp fa-calendar text-primary mr-2"></i>
                                        Timeline & Scheduling
                                    </h3>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div class="form-control w-full">
                                            <label class="label">
                                                <span class="label-text font-medium">Acceptance Date & Time</span>
                                                <span id="pickup-dropoff-date-status" class="label-text-alt hidden">Saving...</span>
                                            </label>
                                            <input type="datetime-local" name="pickup_dropoff_date" id="pickup_dropoff_date"
                                                class="input input-bordered w-full"
                                                value="{{ $certificate->pickup_dropoff_date ? $certificate->pickup_dropoff_date->format('Y-m-d\TH:i') : '' }}"
                                                data-auto-save="true" />
                                            <span class="text-xs text-base-content/70 mt-1">When drives were transferred from client to contractor</span>
                                        </div>

                                        <div class="form-control w-full">
                                            <label class="label">
                                                <span class="label-text font-medium">Scheduled Destruction Date</span>
                                                <span id="scheduled-date-status" class="label-text-alt hidden">Saving...</span>
                                            </label>
                                            <input type="date" name="scheduled_destruction_date" id="scheduled_destruction_date"
                                                class="input input-bordered w-full"
                                                value="{{ $certificate->scheduled_destruction_date ? $certificate->scheduled_destruction_date->format('Y-m-d') : '' }}"
                                                data-auto-save="true" />
                                            <span class="text-xs text-base-content/70 mt-1">Target date for destruction completion</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Destruction Details Section -->
                            <div class="card bg-base-100 shadow-lg">
                                <div class="card-body p-6">
                                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                                        <i class="fa-sharp fa-hammer text-primary mr-2"></i>
                                        Destruction Details
                                    </h3>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div class="form-control w-full">
                                            <label class="label">
                                                <span class="label-text font-medium">Destruction Method</span>
                                                <span id="destruction-method-status" class="label-text-alt hidden">Saving...</span>
                                            </label>
                                            <select name="destruction_method" id="destruction_method"
                                                class="select select-bordered w-full"
                                                data-auto-save="true">
                                                <option value="Shredding" {{ $certificate->destruction_method === 'Shredding' || !$certificate->destruction_method ? 'selected' : '' }}>Shredding</option>
                                                <option value="Degaussing" {{ $certificate->destruction_method === 'Degaussing' ? 'selected' : '' }}>Degaussing</option>
                                                <option value="Drilling" {{ $certificate->destruction_method === 'Drilling' ? 'selected' : '' }}>Drilling</option>
                                                <option value="Digital Erasure" {{ $certificate->destruction_method === 'Digital Erasure' ? 'selected' : '' }}>Digital Erasure</option>
                                            </select>
                                        </div>

                                        <div class="form-control w-full">
                                            <label class="label">
                                                <span class="label-text font-medium">Destruction Location</span>
                                                <span id="destruction-location-status" class="label-text-alt hidden">Saving...</span>
                                            </label>
                                            <select name="destruction_location" id="destruction_location"
                                                class="select select-bordered w-full"
                                                data-auto-save="true">
                                                <option value="Main Warehouse" {{ $certificate->destruction_location === 'Main Warehouse' || !$certificate->destruction_location ? 'selected' : '' }}>Main Warehouse</option>
                                                <option value="On-site Client Location" {{ $certificate->destruction_location === 'On-site Client Location' ? 'selected' : '' }}>On-site Client Location</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Notes Section -->
                            <div class="card bg-base-100 shadow-lg">
                                <div class="card-body p-6">
                                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                                        <i class="fa-sharp fa-note-sticky text-primary mr-2"></i>
                                        Certificate Notes
                                        <span id="notes-status" class="label-text-alt hidden ml-auto">Saving...</span>
                                    </h3>
                                    
                                    <!-- TipTap Editor Container -->
                                    <div class="tiptap-editor-container border border-base-300 rounded-lg overflow-hidden bg-primary">
                                        <div class="tiptap-toolbar bg-primary p-2 border-b border-base-300 flex flex-wrap gap-1"></div>
                                        <div id="tiptap-editor" class="p-4 min-h-[12rem] overflow-y-auto bg-base-100"></div>
                                    </div>
                                    <input type="hidden" name="notes" id="notes_input" value="{{ $certificate->notes }}">
                                </div>
                            </div>



                            <!-- PDF Generation Section -->
                            <div class="card bg-base-100 shadow-lg">
                                <div class="card-body p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        <h3 class="text-lg font-semibold flex items-center">
                                            <i class="fa-sharp fa-file-pdf text-primary mr-2"></i>
                                            Certificate PDF Generation
                                        </h3>
                                    </div>

                                    @php
                                        $currentPdf = \App\Models\CertificatePdf::getCurrentForCertificate($certificate->id);
                                    @endphp

                                    <div class="space-y-4">
                                        @if($currentPdf)
                                            <div class="alert alert-info">
                                                <i class="fa-sharp fa-circle-info"></i>
                                                <div>
                                                    <h4 class="font-semibold">Current PDF Available</h4>
                                                    <p class="text-sm">{{ $currentPdf->file_name }} - Generated {{ $currentPdf->created_at->format('F j, Y g:i A') }}</p>
                                                    <p class="text-sm">Contains {{ $currentPdf->device_count }} devices</p>
                                                </div>
                                            </div>
                                        @endif

                                        <div class="flex flex-wrap gap-2">
                                            @if($currentPdf)
                                                <a href="{{ route('certificates.viewPdf', $currentPdf) }}" class="btn btn-info gap-2" target="_blank">
                                                    <i class="fa-sharp fa-eye"></i>
                                                    View Current
                                                </a>
                                                <a href="{{ route('certificates.downloadPdf', $currentPdf) }}" class="btn btn-success gap-2">
                                                    <i class="fa-sharp fa-download"></i>
                                                    Download Current
                                                </a>
                                            @endif
                                            <button type="button" onclick="checkCertificateStatus()" class="btn btn-primary gap-2">
                                                <i class="fa-sharp fa-file-pdf"></i>
                                                Generate New PDF
                                            </button>
                                            <a href="{{ route('certificates.viewPdfs', $certificate) }}" class="btn btn-outline gap-2">
                                                <i class="fa-sharp fa-folder-open"></i>
                                                View All PDFs
                                            </a>
                                        </div>

                                        @if($currentPdf)
                                            <p class="text-sm text-base-content/60">
                                                <i class="fa-sharp fa-info-circle"></i>
                                                If devices are updated, you must manually regenerate the PDF. New PDFs will not overwrite existing ones.
                                            </p>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            {{-- Hidden warehouse fields for template placeholders --}}
                            <input type="hidden" id="warehouse_manifest_provided" 
                                   value="{{ $certificate->stats['warehouse_manifest_provided'] ?? 'No manifest provided' }}">
                            <input type="hidden" id="warehouse_device_count" 
                                   value="{{ $certificate->stats['warehouse_device_count'] ?? 'Devices not counted on receipt' }}">
                            <input type="hidden" id="warehouse_drive_count" 
                                   value="{{ $certificate->stats['warehouse_drive_count'] ?? 'Drives not counted on receipt' }}">
                        </div>
                    </form>
                </div> <!-- End Certificate Details Tab -->

                <!-- Devices Tab -->
                <div id="tab-devices" class="tab-content">
                    {{-- Load device management from the component --}}
                    <x-device-management :certificate="$certificate"
                                         :devices="$certificate->devices"
                                         :deviceTypes="\App\Models\Device::DEVICE_TYPES"
                                         :manufacturers="$manufacturers"
                                         :models="$models" />
                </div>

                <!-- Signatures Tab -->
                <div id="tab-signatures" class="tab-content">
                    <!-- Accordion for Signatures -->
                    <x-signatures :certificate="$certificate" :isDropOff="($certificate->stats['driver_pickup_dropoff'] ?? '') === 'No'" />
                </div>

                <!-- Documents Tab -->
                <div id="tab-documents" class="tab-content">
                    <div class="card bg-base-100 shadow-lg">
                        <div class="card-body p-6">
                            <div class="flex items-center mb-2">
                                <i class="fa-sharp fa-file-upload text-primary mr-2"></i>
                                <h2 class="card-title text-lg">Document Management</h2>
                            </div>

                            <div class="divider my-2"></div>

                            <!-- File Upload Component -->
                            <x-file-upload
                                title="Upload Document"
                                description="Upload PDF documents or images of physical agreements. Chain of custody documents will be automatically included in the generated PDF certificate."
                                :action="route('certificates.files.upload', $certificate)"
                                :showCocOption="true"
                                :certificate="$certificate"
                            />

                            <!-- File List Component -->
                            <x-file-list
                                title="Uploaded Documents"
                                :files="$certificate->files"
                                :certificate="$certificate"
                            />
                        </div>
                    </div>
                </div>

                <!-- Activity Log Tab -->
                @perms('view_activity_logs')
                <div id="tab-activity" class="tab-content">
                    <div class="card bg-base-100 shadow-lg">
                        <div class="card-body p-6">
                            <div class="flex items-center mb-4">
                                <i class="fa-sharp fa-history text-primary mr-2 text-xl"></i>
                                <h2 class="card-title text-xl font-bold">Activity Log</h2>
                            </div>

                            <x-activity-log-widget :model="$certificate" :limit="15" />
                        </div>
                    </div>
                </div>
                @endperms

            </div> <!-- End Tab Content -->

        </div>
    </div>

    <!-- Customer Edit Modal -->
    <div id="customerModal" class="modal">
        <div class="modal-box">
            <div class="flex items-center mb-4">
                <i class="fa-sharp fa-user-edit text-primary mr-2 text-xl"></i>
                <h3 class="font-bold text-lg">Select New Customer</h3>
            </div>
            <x-dynamic-customer-search id="customerSearchModal" name="new_customer_id"
                placeholder="Search for customers by name, email, phone, or nickname..." action="form" />
            <div class="modal-action">
                <button type="button" class="btn btn-ghost" onclick="closeModal()">Cancel</button>
                <button type="button" class="btn btn-primary gap-2" onclick="updateCustomer()">
                    <i class="fa-sharp fa-check"></i>
                    Update
                </button>
            </div>
        </div>
    </div>



    <!-- Signature Modal -->
    <div id="signatureModal" class="modal">
        <div class="modal-box w-11/12 max-w-5xl">
            <div class="flex items-center mb-4">
                <i class="fa-sharp fa-signature text-primary mr-2 text-xl"></i>
                <h3 class="font-bold text-lg">Add Signature</h3>
            </div>

            <div class="form-control w-full">
                <label class="label">
                    <span class="label-text font-medium" id="signatory_name_label">Signatory Name</span>
                </label>
                <input type="text" id="modal_signatory_name" class="input input-bordered w-full"
                    placeholder="Enter signatory name">
            </div>

            <!-- Key Points Section -->
            <div class="bg-info/10 border border-info/20 p-4 rounded-lg mb-4" id="modal_key_points_display" style="display: none;">
                <h4 class="font-semibold text-info mb-3 flex items-center gap-2">
                    <i class="fa-sharp fa-list-check"></i>
                    Key Points
                </h4>
                <ul class="list-disc list-inside space-y-1 text-sm text-base-content" id="modal_key_points_list">
                    <!-- Key points will be populated here -->
                </ul>
            </div>

            <div id="contract_content" class="p-4 bg-base-200 rounded-lg border border-base-300">
                <!-- Contract content will be loaded here -->
            </div>

            <!-- Signature Type Tabs -->
            <div class="tabs tabs-bordered mt-6">
                <a class="tab tab-bordered tab-active" id="tab-draw" onclick="switchSignatureTab('draw')">Draw Signature</a>
                <a class="tab tab-bordered" id="tab-type" onclick="switchSignatureTab('type')">Type Signature</a>
            </div>

            <!-- Draw Signature Pad -->
            <div id="draw-signature-section" class="mt-4">
                <label class="label">
                    <span class="label-text font-medium">Draw Your Signature</span>
                </label>
                <br>
                <div class="bg-white p-2 rounded-lg border border-base-300 inline-block">
                    <canvas id="signature-pad" class="signature-pad" width="400" height="200"></canvas>
                </div>
                <div class="mt-2">
                    <button type="button" id="clear-signature" class="btn btn-sm btn-outline gap-2">
                        <i class="fa-sharp fa-eraser"></i>
                        Clear Signature
                    </button>
                </div>
            </div>

            <!-- Type Signature Section -->
            <div id="type-signature-section" class="mt-4 hidden">
                <label class="label">
                    <span class="label-text font-medium">Type Your Signature</span>
                </label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="label">
                            <span class="label-text">Select Font</span>
                        </label>
                        <select id="signature-font" class="select select-bordered w-full" onchange="updateTypedSignature()">
                            <option value="'Dancing Script', cursive">Handwritten</option>
                            <option value="'Satisfy', cursive">Signature</option>
                            <option value="'Pacifico', cursive">Flowing</option>
                            <option value="'Caveat', cursive">Casual</option>
                            <option value="'Great Vibes', cursive">Elegant</option>
                        </select>
                    </div>
                    <div>
                        <label class="label">
                            <span class="label-text">Font Size</span>
                        </label>
                        <input type="range" min="24" max="72" value="48" class="range" id="signature-size" onchange="updateTypedSignature()" oninput="updateTypedSignature()">
                        <div class="w-full flex justify-between text-xs px-2 mt-1">
                            <span>Small</span>
                            <span>Medium</span>
                            <span>Large</span>
                        </div>
                    </div>
                </div>
                <div class="bg-base-100 p-4 rounded-lg border border-base-300 mt-4 flex justify-center items-center" style="min-height: 200px;">
                    <div id="typed-signature-preview" class="text-center"></div>
                </div>
                <div class="mt-2">
                    <canvas id="typed-signature-canvas" width="400" height="200" class="hidden"></canvas>
                </div>
            </div>

            <div class="modal-action">
                <button type="button" class="btn btn-ghost" onclick="closeSignatureModal()">Cancel</button>
                <button type="button" class="btn btn-primary gap-2" onclick="addSignature()">
                    <i class="fa-sharp fa-signature"></i>
                    Add Signature
                </button>
            </div>
        </div>
    </div>

    <!-- Signature View Modal -->
    <div id="signatureViewModal" class="modal">
        <div class="modal-box w-11/12 max-w-5xl">
            <div class="flex items-center mb-4">
                <i class="fa-sharp fa-file-signature text-primary mr-2 text-xl"></i>
                <h3 class="font-bold text-lg">Signature Details</h3>
            </div>
            <div id="signatureDetailsContent" class="space-y-4">
                <div class="bg-gray-100 p-4 rounded-lg">
                    <img id="signatureImage" src="" alt="Signature" class="max-h-40 mb-2" style="display: none;" />
                </div>
                <div id="contractText" class="p-4 bg-base-200 rounded-lg border border-base-300"></div>
            </div>
            <div class="modal-action">
                <button type="button" class="btn btn-ghost" onclick="closeSignatureViewModal()">Close</button>
                <button type="button" class="btn btn-error gap-2" onclick="confirmDeleteSignature()">
                    <i class="fa-sharp fa-trash"></i>
                    Delete Signature
                </button>
            </div>
        </div>
    </div>

    @push('styles')
        <style>
            /* Hide tab content by default */
            .tab-content {
                display: none;
            }
            /* Show active tab content */
            .tab-content.active {
                display: block;
            }
        </style>
    @endpush

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/tom-select@2.2.0/dist/js/tom-select.complete.min.js"></script>
        <link href="https://cdn.jsdelivr.net/npm/tom-select@2.2.0/dist/css/tom-select.css" rel="stylesheet">

        <!-- Google Fonts for Signature Styles -->
        <link href="https://fonts.googleapis.com/css2?family=Dancing+Script&family=Satisfy&family=Pacifico&family=Caveat&family=Great+Vibes&display=swap" rel="stylesheet">

        <script>
            // Tab switching functionality
            function switchTab(tabName, element) {
                // Remove active class from all tab contents
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // Remove active class from all tabs
                document.querySelectorAll('.tabs .tab').forEach(tab => {
                    tab.classList.remove('tab-active');
                });
                
                // Show selected tab content
                const selectedContent = document.getElementById('tab-' + tabName);
                if (selectedContent) {
                    selectedContent.classList.add('active');
                }
                
                // Add active class to clicked tab
                if (element) {
                    element.classList.add('tab-active');
                } else {
                    // If no element passed, find it by tabName
                    const tabButton = document.querySelector(`.tab[onclick*="switchTab('${tabName}'"]`);
                    if (tabButton) {
                        tabButton.classList.add('tab-active');
                    }
                }
                
                // Save current tab to localStorage
                localStorage.setItem('certificate-edit-active-tab', tabName);
            }
            
            // Initialize on page load
            document.addEventListener('DOMContentLoaded', function() {
                // Show the first tab by default
                const firstTabContent = document.getElementById('tab-details');
                if (firstTabContent) {
                    firstTabContent.classList.add('active');
                }
                
                // Restore last active tab if saved
                const savedTab = localStorage.getItem('certificate-edit-active-tab');
                if (savedTab) {
                    switchTab(savedTab);
                }
            });
            // Use window.lockedFields to avoid duplicate declarations
            if (typeof window.lockedFields === 'undefined') {
                window.lockedFields = {
                    device_type: false,
                    manufacturer: false,
                    model: false
                };
            }

            function toggleLock(field) {
                window.lockedFields[field] = !window.lockedFields[field];
                const lockIcon = document.getElementById(field + '_lock');
                lockIcon.classList.toggle('fa-lock');
                lockIcon.classList.toggle('fa-unlock');
                lockIcon.classList.toggle('text-red-500');
                lockIcon.classList.toggle('text-green-500');
            }

            // Function to fill a field with a value and trigger auto-save
            function fillField(fieldId, value) {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.value = value;

                    // Trigger the auto-save functionality
                    const event = new Event('input', { bubbles: true });
                    field.dispatchEvent(event);

                    // Add a visual feedback
                    field.classList.add('border-primary');
                    setTimeout(() => {
                        field.classList.remove('border-primary');
                    }, 1000);
                }
            }

            // Variable to track current signature type
            let currentSignatureType = 'draw';

            // Function to switch between signature tabs
            function switchSignatureTab(tabType) {
                currentSignatureType = tabType;

                // Update tab styling
                document.getElementById('tab-draw').classList.remove('tab-active');
                document.getElementById('tab-type').classList.remove('tab-active');
                document.getElementById(`tab-${tabType}`).classList.add('tab-active');

                // Show/hide appropriate sections
                if (tabType === 'draw') {
                    document.getElementById('draw-signature-section').classList.remove('hidden');
                    document.getElementById('type-signature-section').classList.add('hidden');
                } else {
                    document.getElementById('draw-signature-section').classList.add('hidden');
                    document.getElementById('type-signature-section').classList.remove('hidden');
                    updateTypedSignature(); // Update the typed signature preview
                }
            }

            // Function to update the typed signature preview
            function updateTypedSignature() {
                const signatoryName = document.getElementById('modal_signatory_name').value.trim() || 'Your Name';
                const fontFamily = document.getElementById('signature-font').value;
                const fontSize = document.getElementById('signature-size').value;

                const previewElement = document.getElementById('typed-signature-preview');
                previewElement.style.fontFamily = fontFamily;
                previewElement.style.fontSize = `${fontSize}px`;
                previewElement.textContent = signatoryName;

                // Also render to canvas for submission
                renderTypedSignatureToCanvas(signatoryName, fontFamily, fontSize);
            }

            // Function to render the typed signature to a canvas
            function renderTypedSignatureToCanvas(text, fontFamily, fontSize) {
                const canvas = document.getElementById('typed-signature-canvas');
                const ctx = canvas.getContext('2d');

                // Clear the canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Set the font and color
                ctx.font = `${fontSize}px ${fontFamily}`;
                ctx.fillStyle = 'black';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';

                // Draw the text centered on the canvas
                ctx.fillText(text, canvas.width / 2, canvas.height / 2);
            }

            // Modified addSignature function to handle both drawn and typed signatures
            const originalAddSignature = window.addSignature;
            window.addSignature = function() {
                const signatoryName = document.getElementById('modal_signatory_name').value.trim();

                // Check if the name field is empty
                if (signatoryName === '') {
                    alert('Please enter a signatory name.');
                    document.getElementById('modal_signatory_name').focus();
                    return;
                }

                let signatureData = null;

                if (currentSignatureType === 'draw') {
                    // Handle drawn signature
                    if (!signaturePad.isEmpty()) {
                        signatureData = signaturePad.toDataURL();
                    } else {
                        alert('Please provide a signature.');
                        return;
                    }
                } else {
                    // Handle typed signature
                    const canvas = document.getElementById('typed-signature-canvas');
                    signatureData = canvas.toDataURL();
                }

                // Get the contract text
                const contractText = document.getElementById('contract_content').innerHTML;

                if (signatoryName !== '' && window.currentRole !== '' && signatureData) {
                    fetch('{{ route('signatures.store') }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            body: JSON.stringify({
                                certificate_id: {{ $certificate->id }},
                                signatory_name: signatoryName,
                                role: window.currentRole,
                                signature_data: signatureData,
                                contract_text: contractText,
                                signature_type: currentSignatureType // Add signature type
                            })
                        })
                        .then(response => {
                            // Check if the response is JSON
                            const contentType = response.headers.get('content-type');
                            if (contentType && contentType.includes('application/json')) {
                                return response.json().then(data => {
                                    return { status: response.status, data };
                                });
                            } else {
                                // If not JSON, get the text and create an error object
                                return response.text().then(text => {
                                    console.error('Non-JSON response:', text);
                                    return {
                                        status: response.status,
                                        data: {
                                            success: false,
                                            message: 'Server returned non-JSON response. Check server logs.'
                                        }
                                    };
                                });
                            }
                        })
                        .then(({ status, data }) => {
                            if (data.success) {
                                // If this is a driver signature, update the data attribute before reloading
                                if (window.currentRole === 'driver') {
                                    const driverSection = document.querySelector('.collapse-arrow[data-driver-signed]');
                                    if (driverSection) {
                                        driverSection.setAttribute('data-driver-signed', 'true');
                                        // Update the icon immediately
                                        updateDriverStatusIcon(true, document.getElementById('driver_pickup_dropoff').value === 'No');
                                    }
                                }
                                
                                location.reload(); // Reload the page after adding a signature
                            } else {
                                // Check if we should run diagnostics
                                if (status === 500) {
                                    console.error('Server error occurred. Running diagnostics...');
                                    runSignatureDiagnostics();
                                }
                                alert('Error: ' + (data.message || 'Unknown error occurred'));
                            }
                        })
                        .catch(error => {
                            console.error('Error submitting signature:', error);
                            alert('Error submitting signature: ' + error.message);
                            runSignatureDiagnostics();
                        });
                } else {
                    alert('Please fill in all the required fields and provide a signature.');
                }
            };

            // Update typed signature when name changes
            document.addEventListener('DOMContentLoaded', function() {
                const nameInput = document.getElementById('modal_signatory_name');
                if (nameInput) {
                    nameInput.addEventListener('input', function() {
                        if (currentSignatureType === 'type') {
                            updateTypedSignature();
                        }
                    });
                }

                new TomSelect('#device_type', {
                    create: false,
                    sortField: {
                        field: 'text',
                        direction: 'asc'
                    }
                });

                new TomSelect('#manufacturer', {
                    create: true,
                    sortField: {
                        field: 'text',
                        direction: 'asc'
                    }
                });

                new TomSelect('#model', {
                    create: true,
                    sortField: {
                        field: 'text',
                        direction: 'asc'
                    }
                });
            });

            function openModal() {
                document.getElementById('customerModal').classList.add('modal-open');
            }

            function closeModal() {
                document.getElementById('customerModal').classList.remove('modal-open');
            }

            function updateCustomer() {
                const newCustomerId = document.querySelector('input[name="new_customer_id"]').value;
                if (newCustomerId) {
                    // Update the customer ID in the form
                    const form = document.getElementById('certificateForm');
                    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                    const methodField = document.createElement('input');
                    methodField.setAttribute('type', 'hidden');
                    methodField.setAttribute('name', '_method');
                    methodField.setAttribute('value', 'PUT');

                    const csrfField = document.createElement('input');
                    csrfField.setAttribute('type', 'hidden');
                    csrfField.setAttribute('name', '_token');
                    csrfField.setAttribute('value', csrfToken);

                    const input = document.createElement('input');
                    input.setAttribute('type', 'hidden');
                    input.setAttribute('name', 'customer_id');
                    input.setAttribute('value', newCustomerId);

                    form.appendChild(methodField);
                    form.appendChild(csrfField);
                    form.appendChild(input);

                    // Submit the form
                    form.submit();
                } else {
                    alert('Please select a customer.');
                }
            }

            window.confirmDeleteSignature = function() {
                if (!currentSignatureId) {
                    alert('No signature selected for deletion.');
                    return;
                }
                if (confirm('Are you sure you want to delete this signature?')) {
                    deleteSignature(currentSignatureId);
                    closeSignatureViewModal();
                }
            };

            // Auto-save functionality
            document.addEventListener('DOMContentLoaded', function() {
                // Get all elements with data-auto-save attribute
                const autoSaveElements = document.querySelectorAll('[data-auto-save="true"]');
                const certificateId = '{{ $certificate->id }}';
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                // Debounce function to prevent too many requests
                function debounce(func, wait) {
                    let timeout;
                    return function() {
                        const context = this, args = arguments;
                        clearTimeout(timeout);
                        timeout = setTimeout(function() {
                            func.apply(context, args);
                        }, wait);
                    };
                }

                // Function to show saving indicator
                function showSavingIndicator(field) {
                    let indicator;
                    if (field === 'status') {
                        indicator = document.getElementById('status-save-indicator');
                    } else if (field === 'scheduled_destruction_date') {
                        indicator = document.getElementById('scheduled-date-status');
                    } else if (field === 'pickup_dropoff_date') {
                        indicator = document.getElementById('pickup-dropoff-date-status');
                    } else if (field === 'destruction_method') {
                        indicator = document.getElementById('destruction-method-status');
                    } else if (field === 'destruction_location') {
                        indicator = document.getElementById('destruction-location-status');
                    }

                    if (indicator) {
                        indicator.textContent = 'Saving...';
                        indicator.classList.remove('hidden');
                        indicator.classList.remove('text-success');
                        indicator.classList.remove('text-error');
                    }
                }

                // Function to show saved indicator
                function showSavedIndicator(field, success) {
                    let indicator;
                    if (field === 'status') {
                        indicator = document.getElementById('status-save-indicator');
                    } else if (field === 'scheduled_destruction_date') {
                        indicator = document.getElementById('scheduled-date-status');
                    } else if (field === 'pickup_dropoff_date') {
                        indicator = document.getElementById('pickup-dropoff-date-status');
                    } else if (field === 'destruction_method') {
                        indicator = document.getElementById('destruction-method-status');
                    } else if (field === 'destruction_location') {
                        indicator = document.getElementById('destruction-location-status');
                    }

                    if (indicator) {
                        indicator.textContent = success ? 'Saved!' : 'Error saving';
                        indicator.classList.remove('hidden');
                        if (success) {
                            indicator.classList.add('text-success');
                            indicator.classList.remove('text-error');
                        } else {
                            indicator.classList.add('text-error');
                            indicator.classList.remove('text-success');
                        }

                        // Hide the indicator after 2 seconds
                        setTimeout(function() {
                            indicator.classList.add('hidden');
                        }, 2000);
                    }
                }

                // Function to save a field
                function saveField(field, value) {
                    showSavingIndicator(field);

                    fetch(`/certificates/${certificateId}/auto-save`, {
                        method: 'PATCH',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                            field: field,
                            value: value
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showSavedIndicator(field, true);
                        } else {
                            showSavedIndicator(field, false);
                            console.error('Error saving:', data.error);
                        }
                    })
                    .catch(error => {
                        showSavedIndicator(field, false);
                        console.error('Error saving:', error);
                    });
                }


                // Function to check if all drives are destroyed
                function checkDrivesDestroyed() {
                    // Count devices dynamically from the DOM
                    const deviceRows = document.querySelectorAll('tr[id^="device-"]');
                    const totalDevices = deviceRows.length;

                    // Count destroyed devices by checking which rows have the checkbox checked
                    let destroyedDevices = 0;
                    deviceRows.forEach(row => {
                        const checkbox = row.querySelector('input[type="checkbox"]');
                        if (checkbox && checkbox.checked) {
                            destroyedDevices++;
                        }
                    });

                    const techDrivesCount = {{ $certificate->stats['technician_total_drives_found'] ?? 0 }};

                    return {
                        totalDevices,
                        destroyedDevices,
                        techDrivesCount,
                        allDestroyed: destroyedDevices === totalDevices && (techDrivesCount === 0 || destroyedDevices === parseInt(techDrivesCount))
                    };
                }

                // Function to handle completion confirmation
                function confirmCompletion(radioElement) {
                    const drivesInfo = checkDrivesDestroyed();
                    const warningElement = document.getElementById('completion-warning');
                    const modal = document.getElementById('completionConfirmModal');
                    const confirmBtn = document.getElementById('confirmCompletionBtn');

                    // If no drives need destruction, just mark as complete
                    if (drivesInfo.totalDevices === 0 && drivesInfo.techDrivesCount === 0) {
                        saveField('status', 'completed');
                        return;
                    }

                    // Set up the confirmation button
                    confirmBtn.onclick = function() {
                        saveField('status', 'completed');
                        modal.close();
                    };

                    // Prepare warning message
                    if (!drivesInfo.allDestroyed) {
                        warningElement.innerHTML = `
                            <div class="alert alert-warning">
                                <i class="fa-sharp fa-triangle-exclamation mr-2"></i>
                                <span>You are about to mark this certificate as completed, but only <strong>${drivesInfo.destroyedDevices} of ${Math.max(drivesInfo.totalDevices, drivesInfo.techDrivesCount)}</strong> drives have been marked as destroyed. Are you sure this certificate is complete?</span>
                            </div>
                        `;
                    } else {
                        warningElement.innerHTML = `
                            <div class="alert alert-info">
                                <i class="fa-sharp fa-circle-info mr-2"></i>
                                <span>By marking this certificate complete, you are certifying that you have shredded all drives in accordance with the destruction requirements and company policies.</span>
                            </div>
                        `;
                    }

                    // Show the modal
                    modal.showModal();
                }

                // Function to cancel completion
                window.cancelCompletion = function() {
                    // Reset the radio button to its previous value
                    const statusRadios = document.querySelectorAll('input[name="status"]');
                    statusRadios.forEach(radio => {
                        if (radio.value === '{{ $certificate->status }}') {
                            radio.checked = true;
                        }
                    });

                    // Close the modal
                    document.getElementById('completionConfirmModal').close();
                };

                // Function to check certificate status before generating PDF
                window.checkCertificateStatus = function() {
                    // Get the current certificate status from the selected radio button
                    let currentStatus = '';
                    const statusRadios = document.querySelectorAll('input[name="status"]');
                    statusRadios.forEach(radio => {
                        if (radio.checked) {
                            currentStatus = radio.value;
                        }
                    });

                    // If the certificate is not completed (pending or verifying), show warning modal
                    if (currentStatus === 'pending' || currentStatus === 'verifying') {
                        // Update the status text in the modal
                        const statusText = currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1);
                        document.getElementById('current-status-text').textContent = statusText;

                        // Show the modal
                        document.getElementById('pdfWarningModal').showModal();
                    } else {
                        // If it's already completed, proceed directly to PDF generation
                        window.location.href = '{{ route('certificates.generatePdf', $certificate) }}';
                    }
                };

                // Add event listeners to all auto-save elements
                autoSaveElements.forEach(element => {
                    if (element.type === 'radio') {
                        // For radio buttons
                        element.addEventListener('change', debounce(function() {
                            if (this.checked) {
                                if (this.value === 'completed') {
                                    confirmCompletion(this);
                                } else {
                                    saveField('status', this.value);
                                }
                            }
                        }, 500));
                    } else if (element.type === 'date') {
                        // For date inputs
                        element.addEventListener('change', debounce(function() {
                            saveField('scheduled_destruction_date', this.value);
                        }, 500));
                    } else if (element.type === 'datetime-local') {
                        // For datetime-local inputs
                        element.addEventListener('change', debounce(function() {
                            saveField(this.name, this.value);
                        }, 500));
                    } else if (element.type === 'text') {
                        // For text inputs
                        element.addEventListener('input', debounce(function() {
                            saveField(this.name, this.value);
                        }, 500));
                    } else if (element.tagName === 'SELECT') {
                        // For select elements (destruction method and location)
                        element.addEventListener('change', debounce(function() {
                            saveField(this.name, this.value);
                        }, 500));
                    }
                });

            });
        </script>
    @endpush
    @push('scripts')



        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const notesInput = document.getElementById('notes_input');
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                const certificateId = '{{ $certificate->id }}';

                // We're using the npm-installed TipTap, so we'll initialize it directly
                // No need to check if it's loaded since it's bundled with our app

                // Debounce function to prevent too many requests
                function debounce(func, wait) {
                    let timeout;
                    return function() {
                        const context = this, args = arguments;
                        clearTimeout(timeout);
                        timeout = setTimeout(function() {
                            func.apply(context, args);
                        }, wait);
                    };
                }

                // Function to show saving indicator
                function showSavingIndicator() {
                    const indicator = document.getElementById('notes-status');
                    if (indicator) {
                        indicator.textContent = 'Saving...';
                        indicator.classList.remove('hidden');
                        indicator.classList.remove('text-success');
                        indicator.classList.remove('text-error');
                    }
                }

                // Function to show saved indicator
                function showSavedIndicator(success) {
                    const indicator = document.getElementById('notes-status');
                    if (indicator) {
                        indicator.textContent = success ? 'Saved!' : 'Error saving';
                        indicator.classList.remove('hidden');
                        if (success) {
                            indicator.classList.add('text-success');
                            indicator.classList.remove('text-error');
                        } else {
                            indicator.classList.add('text-error');
                            indicator.classList.remove('text-success');
                        }

                        // Hide the indicator after 2 seconds
                        setTimeout(function() {
                            indicator.classList.add('hidden');
                        }, 2000);
                    }
                }

                // Function to save notes
                function saveNotes(html) {
                    showSavingIndicator();

                    fetch(`/certificates/${certificateId}/auto-save`, {
                        method: 'PATCH',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                            field: 'notes',
                            value: html
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showSavedIndicator(true);
                        } else {
                            showSavedIndicator(false);
                            console.error('Error saving:', data.error);
                        }
                    })
                    .catch(error => {
                        showSavedIndicator(false);
                        console.error('Error saving:', error);
                    });
                }

                // Initialize TipTap Editor
                try {
                    // Create toolbar buttons
                    const toolbar = document.querySelector('.tiptap-toolbar');

                    // Define toolbar buttons with text labels instead of icons
                    const buttons = [
                        { name: 'Bold', icon: 'B', action: 'toggleBold' },
                        { name: 'Italic', icon: 'I', action: 'toggleItalic' },
                        { name: 'Underline', icon: 'U', action: 'toggleUnderline' },
                        { name: 'Bullet List', icon: '•', action: 'toggleBulletList' },
                        { name: 'Ordered List', icon: '1.', action: 'toggleOrderedList' },
                        { name: 'Clear', icon: 'Clear', action: 'clearNodes' }
                    ];

                    // This code assumes you've imported TipTap in your main JS bundle:
                    // import { Editor } from '@tiptap/core'
                    // import StarterKit from '@tiptap/starter-kit'

                    // Check if an editor instance already exists
                    if (window.tiptapEditor) {
                        console.log('TipTap editor already initialized, using existing instance');
                        return;
                    }

                    // Create editor instance using the npm-installed version
                    // The Editor and StarterKit should be available through your JS bundle
                    const editor = new window.TipTap.Editor({
                        element: document.querySelector('#tiptap-editor'),
                        extensions: [
                            window.TipTap.StarterKit.configure({
                                bulletList: {
                                    keepMarks: true,
                                    keepAttributes: false,
                                },
                                orderedList: {
                                    keepMarks: true,
                                    keepAttributes: false,
                                },
                            }),
                        ],
                        content: notesInput.value || '',
                        onUpdate: debounce(({ editor }) => {
                            const html = editor.getHTML();
                            notesInput.value = html;
                            saveNotes(html);
                        }, 1000),
                    });

                    // Create toolbar buttons
                    buttons.forEach(button => {
                        const btn = document.createElement('button');
                        btn.type = 'button';
                        btn.title = button.name;

                        // Add special styling for text labels
                        if (button.name === 'Bold') {
                            btn.innerHTML = `<strong>${button.icon}</strong>`;
                        } else if (button.name === 'Italic') {
                            btn.innerHTML = `<em>${button.icon}</em>`;
                        } else if (button.name === 'Underline') {
                            btn.innerHTML = `<u>${button.icon}</u>`;
                        } else {
                            btn.innerHTML = button.icon;
                        }

                        btn.addEventListener('click', () => {
                            editor.chain().focus()[button.action]().run();
                        });

                        // Update active state when editor state changes
                        editor.on('update', () => {
                            if (button.action.startsWith('toggle')) {
                                const actionName = button.action.replace('toggle', '').toLowerCase();
                                btn.classList.toggle('is-active', editor.isActive(actionName));
                            }
                        });

                        toolbar.appendChild(btn);
                    });

                    // Store editor instance globally
                    window.tiptapEditor = editor;

                } catch (error) {
                    console.error('Error initializing TipTap editor:', error);
                    // Create a fallback textarea
                    const editorContainer = document.querySelector('.tiptap-editor-container');
                    const fallbackTextarea = document.createElement('textarea');
                    fallbackTextarea.className = 'textarea textarea-bordered h-48 w-full';
                    fallbackTextarea.placeholder = 'Enter certificate notes here...';
                    fallbackTextarea.value = notesInput.value;
                    editorContainer.parentNode.replaceChild(fallbackTextarea, editorContainer);

                    // Add event listener for textarea
                    fallbackTextarea.addEventListener('input', debounce(function() {
                        notesInput.value = this.value;
                        saveNotes(this.value);
                    }, 1000));
                }
            });
        </script>
    @endpush

    <!-- Completion Confirmation Modal -->
    <dialog id="completionConfirmModal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg">Confirm Certificate Completion</h3>
            <div id="completion-warning" class="py-4">
                <!-- Content will be dynamically populated -->
            </div>
            <div class="modal-action">
                <button id="confirmCompletionBtn" class="btn btn-primary">Yes, Mark as Complete</button>
                <button class="btn" onclick="cancelCompletion()">Cancel</button>
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button onclick="cancelCompletion()">close</button>
        </form>
    </dialog>

    <!-- PDF Generation Warning Modal -->
    <dialog id="pdfWarningModal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg">Certificate Not Complete</h3>
            <div class="py-4">
                <div class="alert alert-warning">
                    <i class="fa-sharp fa-triangle-exclamation mr-2"></i>
                    <span>This certificate is currently marked as <strong id="current-status-text"></strong>. The generated PDF will indicate that the destruction process is not yet complete. Do you want to proceed?</span>
                </div>
            </div>
            <div class="modal-action">
                <a href="{{ route('certificates.generatePdf', $certificate) }}" class="btn btn-primary">Yes, Generate PDF</a>
                <button class="btn" onclick="document.getElementById('pdfWarningModal').close()">Cancel</button>
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button>close</button>
        </form>
    </dialog>
</x-app-layout>
