<x-app-layout
    page-title="Inventory Details: {{ $inventory->name }}"
    page-icon="fa-sharp fa-box"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('inventory.index'),
            'text' => 'Back to Inventory'
        ],
        [
            'type' => 'edit',
            'route' => route('inventory.edit', $inventory),
            'text' => 'Edit Item',
            'permission' => 'edit_inventory_items'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'View PDF',
            'route' => route('inventory.pdf', $inventory),
            'icon' => 'fa-sharp fa-file-pdf',
            'class' => 'btn btn-accent btn-sm gap-2',
            'target' => '_blank',
            'permission' => 'view_inventory_items'
        ],
        [
            'name' => $inventory->generated_description ? 'Regenerate Description' : 'Generate Description',
            'icon' => 'fa-sharp fa-sync',
            'class' => 'btn btn-info btn-sm gap-2',
            'id' => 'regenerate-description',
            'condition' => $inventory->category->ai_promptable,
            'permission' => 'generate_inventory_descriptions'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Inventory', 'route' => 'inventory.index', 'icon' => 'fa-boxes'],
        ['name' => $inventory->name, 'icon' => 'fa-eye']
    ]">
    

    <meta name="inventory-id" content="{{ $inventory->id }}">

    <div class="py-6 lg:py-8">


        <!-- Page Content -->
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">

            <!-- Title Section -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <div class="flex justify-between items-start gap-4">
                        <div class="flex-1">
                            <h1 id="inventory-title" class="text-3xl font-bold text-base-content">{{ $inventory->name }}</h1>
                            <div class="badge badge-primary badge-lg mt-2">{{ $inventory->category->name }}</div>
                        </div>
                        <button class="btn btn-primary btn-sm"
                            onclick="copyToClipboard(document.getElementById('inventory-title').innerHTML)">
                            <i class="fa-sharp fa-copy"></i> Copy Title
                        </button>
                    </div>
                </div>
            </div>

            <!-- Stats Section -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                        <div class="stat bg-base-200 rounded-lg">
                            <div class="stat-title">Asset Tag</div>
                            <div class="stat-value text-lg">{{ $inventory->asset_tag }}</div>
                        </div>
                        <div class="stat bg-base-200 rounded-lg">
                            <div class="stat-title">Location</div>
                            <div class="stat-value text-lg">{{ $inventory->location }}</div>
                        </div>
                        <div class="stat bg-base-200 rounded-lg">
                            <div class="stat-title">Suggested Price</div>
                            <div class="stat-value text-lg">${{ number_format($inventory->suggested_price, 2) }}</div>
                        </div>
                        <div class="stat bg-base-200 rounded-lg">
                            <div class="stat-title">Created</div>
                            <div class="stat-value text-lg">{{ $inventory->created_at->format('M j, Y') }}</div>
                        </div>
                        <div class="stat bg-base-200 rounded-lg">
                            <div class="stat-title">Last Updated</div>
                            <div class="stat-value text-lg">{{ $inventory->updated_at->format('M j, Y') }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid: Description/Condition (2/3) + Images (1/3) -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Left Column: Description and Condition -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Description Section -->
                    <div class="card bg-base-100 shadow-lg">
                        <div class="card-body">
                            <div class="flex justify-between items-start gap-4 mb-4">
                                <h2 class="card-title text-xl">Description</h2>
                                <button class="btn btn-primary btn-sm"
                                    onclick="copyToClipboard(document.getElementById('description-container').innerHTML, true)">
                                    <i class="fa-sharp fa-copy"></i> Copy Description
                                </button>
                            </div>

                            <div id="description-container" class="prose max-w-none">
                                @if ($inventory->generated_description)
                                    {!! $inventory->generated_description !!}
                                @else
                                    @if ($inventory->category->ai_promptable)
                                        <div class="alert alert-info">
                                            <i class="fa-sharp fa-info-circle"></i>
                                            <div>
                                                <h3 class="font-bold">No Generated Description Available</h3>
                                                <div class="text-sm">Click the button to generate a description for this inventory item. Note that this uses an AI model and costs money. Only generate descriptions after all the information entered into the product listing is correct!</div>
                                            </div>
                                        </div>
                                    @else
                                        <p class="text-base-content">{!! $inventory->description !!}</p>
                                    @endif
                                @endif
                            </div>

                            @if ($inventory->category->ai_promptable && $inventory->description)
                                <div class="mt-4 p-4 bg-base-200 rounded-lg">
                                    <h3 class="font-semibold text-base-content mb-2">Original Notes</h3>
                                    <div class="prose max-w-none text-sm">
                                        {!! $inventory->description !!}
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Condition Section -->
                    <div class="card bg-base-100 shadow-lg">
                        <div class="card-body">
                            <div class="flex justify-between items-start gap-4 mb-4">
                                <h2 class="card-title text-xl">Condition</h2>
                                <button class="btn btn-primary btn-sm"
                                    onclick="copyToClipboard(document.getElementById('inventoryCondition').innerHTML)">
                                    <i class="fa-sharp fa-copy"></i> Copy Condition
                                </button>
                            </div>

                            <div class="alert alert-warning">
                                <i class="fa-sharp fa-exclamation-triangle"></i>
                                <div>
                                    <p id="inventoryCondition" class="font-medium">{{ $inventory->condition }}</p>
                                    <div class="text-sm opacity-70">Remember to include condition info in Facebook listings!</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Image Gallery -->
                <div class="lg:col-span-1">
                    <div class="card bg-base-100 shadow-lg h-fit">
                        <div class="card-body">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="card-title text-xl">Images</h2>
                                @php
                                    $imageCount = \App\Models\Image::forContext('inventory', $inventory->id)->count();
                                @endphp
                                @if ($imageCount > 0)
                                    <a href="{{ route('inventory.photos.downloadAll', $inventory) }}"
                                        class="btn btn-secondary btn-sm">
                                        <i class="fa-sharp fa-download"></i> Download All
                                    </a>
                                @endif
                            </div>

                            <div id="imageGalleryContainer" class="grid grid-cols-2 gap-4">
                                <!-- Images will be dynamically loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- All Inventory Data Section -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h2 class="card-title text-xl mb-6">
                        <i class="fa-sharp fa-list mr-2"></i> All Inventory Data
                    </h2>

                    <div class="overflow-x-auto">
                        <table class="table table-zebra w-full">
                            <thead>
                                <tr class="bg-base-200">
                                    <th class="text-base-content font-semibold">Property</th>
                                    <th class="text-base-content font-semibold">Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($formattedChecklist as $property => $value)
                                    <tr class="hover:bg-base-200/50 transition-colors">
                                        <td class="font-medium text-base-content/80 w-1/3">
                                            {{ $property }}
                                        </td>
                                        <td class="text-base-content break-words">
                                            @if (empty($value) || $value === 'N/A' || $value === '')
                                                <span class="text-base-content/40 italic">Not specified</span>
                                            @else
                                                {{ $value }}
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @vite('resources/js/inventory-gallery.js')

    @push('styles')
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/photoswipe/5.4.4/photoswipe.min.css"
            integrity="sha512-LFWtdAXHQuwUGH9cImO9blA3a3GfQNkpF2uRlhaOpSbDevNyK1rmAjs13mtpjvWyi+flP7zYWboqY+8Mkd42xA=="
            crossorigin="anonymous" referrerpolicy="no-referrer" />
        <style>
            .photo-thumbnail {
                aspect-ratio: auto;
                object-fit: cover;
            }

            #photoGallery {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 10px;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            function copyToClipboard(html, withoutHeadings = false) {
                // Create a temporary DOM element to manipulate the HTML
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;

                // If withHeading is false, remove <h1> and <h2> elements
                if (withoutHeadings) {
                    const headings = tempDiv.querySelectorAll('h1, h2');
                    headings.forEach(heading => heading.remove());
                }

                // Get the cleaned HTML content
                const cleanedHtml = tempDiv.innerHTML;

                // Use the modern clipboard API with HTML support
                if (navigator.clipboard && window.ClipboardItem) {
                    const htmlBlob = new Blob([cleanedHtml], { type: "text/html" });
                    const textBlob = new Blob([tempDiv.textContent || tempDiv.innerText || ''], { type: "text/plain" });

                    const clipboardItem = new ClipboardItem({
                        "text/html": htmlBlob,
                        "text/plain": textBlob
                    });

                    navigator.clipboard.write([clipboardItem]).then(() => {
                        showToast('success', 'Copied to clipboard!', 2000);
                    }).catch((err) => {
                        console.error('Failed to copy to clipboard:', err);
                        showToast('error', 'Failed to copy to clipboard.', 3000);
                    });
                } else {
                    // Fallback for older browsers - copy as plain text
                    const tempElement = document.createElement("textarea");
                    tempElement.style.position = "fixed";
                    tempElement.style.opacity = "0";
                    tempElement.value = tempDiv.textContent || tempDiv.innerText || '';
                    document.body.appendChild(tempElement);
                    tempElement.select();
                    try {
                        document.execCommand("copy");
                        showToast('success', 'Copied to clipboard!', 2000);
                    } catch (err) {
                        console.error('Failed to copy to clipboard:', err);
                        showToast('error', 'Failed to copy to clipboard.', 3000);
                    }
                    document.body.removeChild(tempElement);
                }
            }





            document.addEventListener('DOMContentLoaded', function() {

                // Regenerate description logic
                const regenerateButton = document.getElementById('regenerate-description');

                // Check if the button exists before adding the event listener
                if (regenerateButton) {
                    regenerateButton.addEventListener('click', function() {
                        regenerateButton.disabled = true;
                        regenerateButton.innerHTML =
                            '<span class="loading loading-spinner"></span> Regenerating...';

                        fetch("{{ route('inventory.regenerateDescription', $inventory->id) }}", {
                                method: 'POST',
                                headers: {
                                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                                    'Content-Type': 'application/json',
                                }
                            })
                            .then(response => response.json())
                            .then(data => {
                                // Update the description container with the generated description
                                document.getElementById('description-container').innerHTML = data
                                    .generated_description;

                                // Re-enable the button and reset its content
                                regenerateButton.disabled = false;
                                regenerateButton.innerHTML =
                                    '<i class="fa-sharp fa-sync"></i> Regenerate Description';
                            })
                            .catch(() => {
                                // Handle errors gracefully
                                regenerateButton.disabled = false;
                                regenerateButton.innerHTML =
                                    '<i class="fa-sharp fa-sync"></i> Regenerate Description';
                                alert('Failed to regenerate description.');
                            });
                    });
                } else {
                    console.log('Regenerate button not found on this page.');
                }
            });
        </script>
    @endpush
</x-app-layout>
