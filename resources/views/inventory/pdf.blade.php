<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory PDF</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }
        h1 {
            font-size: 30px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
            background-color: #f4f4f4;
            padding: 10px;
        }
        .details, .checklist {
            margin-bottom: 20px;
        }
        .details td, .checklist td, .checklist th {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .checklist table {
            width: 100%;
            border-collapse: collapse;
        }
        .checklist th {
            background-color: #f4f4f4;
            font-weight: bold;
        }

        .details table{
            width: 100%;
            border-collapse: collapse;
        }

        /* make first column of details table width 30% */
        .details td:first-child {
            width: 30%;
        }

        /* make first column of checklist table width 30% */
        .checklist td:first-child {
            width: 30%;
        }
    </style>
</head>
<body>
    <h1>
        {{ $inventory->location }} - {{ $inventory->asset_tag }} - {{ $inventory->name }}</h1>

    <div class="details">
        <h2>Details</h2>
        <table>
            <tr>
                <td><strong>Status:</strong></td>
                <td>{{ $inventory->status }}</td>
            </tr>
            <tr>
                <td><strong>Location:</strong></td>
                <td>{{ $inventory->location }}</td>
            </tr>
            <tr>
                <td><strong>Technician:</strong></td>
                <td>{{ $inventory->technician->name ?? 'N/A' }}</td>
            </tr>
            <tr>
                <td><strong>Category:</strong></td>
                <td>{{ $inventory->category->name ?? 'N/A' }}</td>
            </tr>
            <tr>
                <td><strong>Description/Notes:</strong></td>
                <td>{!! $inventory->description !!}</td>
            </tr>
            <tr>
                <td><strong>Condition:</strong></td>
                <td>{{ $inventory->condition }}</td>
            </tr>
        </table>
    </div>

    <div class="checklist">
        <h2>Checklist</h2>
        <table>
            <thead>
                <tr>
                    <th>Field</th>
                    <th>Value</th>
                </tr>
            </thead>
            <tbody>
                @foreach($inventoryChecklist as $fieldName => $fieldValue)
                    <tr>
                        <td>{{ $fieldName }}</td>
                        <td>{{ $fieldValue }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

</body>
</html>
