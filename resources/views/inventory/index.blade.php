<x-app-layout
    page-title="{{ request()->has('trashed') && request('trashed') === 'true' ? 'Trashed Inventory Items' : 'Inventory Overview' }}"
    page-icon="fa-sharp fa-boxes"
    :primary-buttons="[
        [
            'type' => 'create',
            'route' => route('inventory.create'),
            'text' => 'Add New Item',
            'permission' => 'create_inventory_items'
        ]
    ]"
    :action-buttons="[
        [
            'name' => request()->has('trashed') && request('trashed') === 'true' ? 'View Active' : 'View Trashed',
            'route' => request()->has('trashed') && request('trashed') === 'true' ? route('inventory.index') : route('inventory.index', ['trashed' => 'true']),
            'icon' => request()->has('trashed') && request('trashed') === 'true' ? 'fa-sharp fa-eye' : 'fa-sharp fa-trash',
            'class' => 'btn ' . (request()->has('trashed') && request('trashed') === 'true' ? 'btn-primary' : 'btn-warning') . ' btn-sm gap-2'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Inventory', 'icon' => 'fa-boxes']
    ]">

    <div class="py-6 lg:py-8">
        <div class="mx-auto sm:px-6 lg:px-8 space-y-8" style="max-width: 1600px;">

            <!-- Search & Filter Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-magnifying-glass text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Search & Filter</h4>
                    </div>
                </div>
                <div class="p-6 space-y-6">
                    <form action="{{ route('inventory.index') }}" method="GET" id="filter-form" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- Preserve trashed parameter -->
                        @if(request('trashed'))
                            <input type="hidden" name="trashed" value="{{ request('trashed') }}">
                        @endif

                        <!-- Basic Search -->
                        <div>
                            <label for="search" class="label">
                                <span class="label-text font-medium">Search</span>
                                <span class="label-text-alt">Asset tag, name, location</span>
                            </label>
                            <input type="text" name="search" id="search" class="input input-bordered w-full"
                                value="{{ request('search') }}"
                                placeholder="Search inventory items...">
                        </div>

                        <!-- Category Filter -->
                        <div>
                            <label for="category" class="label">
                                <span class="label-text font-medium">Category</span>
                            </label>
                            <select name="category" id="category" class="select select-bordered w-full">
                                <option value="">All Categories</option>
                                @foreach ($categories as $category)
                                    <option value="{{ $category->id }}"
                                        {{ request('category') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Status Filter -->
                        <div>
                            <label for="status" class="label">
                                <span class="label-text font-medium">Status</span>
                            </label>
                            <select name="status" id="status" class="select select-bordered w-full">
                                <option value="">All Statuses</option>
                                @foreach ($statuses as $key => $label)
                                    <option value="{{ $key }}" {{ request('status') == $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Sort By -->
                        <div>
                            <label for="sort" class="label">
                                <span class="label-text font-medium">Sort By</span>
                            </label>
                            <select name="sort" id="sort" class="select select-bordered w-full" onchange="this.form.submit()">
                                <option value="created_at_desc"
                                    {{ request('sort') == 'created_at_desc' ? 'selected' : '' }}>Newest First</option>
                                <option value="created_at_asc"
                                    {{ request('sort') == 'created_at_asc' ? 'selected' : '' }}>Oldest First</option>
                                <option value="name_asc"
                                    {{ request('sort') == 'name_asc' ? 'selected' : '' }}>Name A-Z</option>
                                <option value="name_desc"
                                    {{ request('sort') == 'name_desc' ? 'selected' : '' }}>Name Z-A</option>
                                <option value="asset_tag_asc"
                                    {{ request('sort') == 'asset_tag_asc' ? 'selected' : '' }}>Asset Tag</option>
                            </select>
                        </div>

                        <!-- Submit Button -->
                        <div class="md:col-span-2 lg:col-span-4 flex gap-3">
                            <button type="submit" class="btn btn-primary gap-2">
                                <i class="fa-sharp fa-search"></i>
                                Apply Filters
                            </button>
                            <a href="{{ route('inventory.index', request()->only('trashed')) }}" class="btn btn-outline gap-2">
                                <i class="fa-sharp fa-refresh"></i>
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Inventory Directory Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-2 md:px-4 py-4 border-b border-base-300/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-list text-xs md:text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Inventory Directory</h4>
                        </div>
                        <div class="flex items-center gap-2 text-sm text-base-content/60">
                            <span>Showing {{ $inventories->firstItem() ?? 0 }}-{{ $inventories->lastItem() ?? 0 }} of {{ $inventories->total() }}</span>
                        </div>
                    </div>
                </div>

                <!-- Desktop Column Headers -->
                <div class="hidden lg:block px-6 py-4 border-b border-base-300/50 bg-base-50">
                    <div class="grid grid-cols-12 gap-3 items-center text-sm font-medium text-base-content/70">
                        <div class="col-span-2">Photo</div>
                        <div class="col-span-2">Item Details</div>
                        <div class="col-span-3">Description</div>
                        <div class="col-span-2">Location & Category</div>
                        <div class="col-span-1">Price</div>
                        <div class="col-span-1">Dates & Info</div>
                        <div class="col-span-1 text-center">Actions</div>
                    </div>
                </div>

                <!-- Inventory Items Section -->
                <div class="divide-y divide-base-200/50">
                    @forelse($inventories as $inventory)
                        <!-- Inventory Card Row -->
                        <div class="p-2 lg:p-4 hover:bg-base-200/50 transition-all duration-200 {{ $loop->even ? 'bg-base-100' : 'bg-base-200/30' }} border-b-4 border-base-300 lg:border-b-0">
                            <!-- Mobile Layout -->
                            <div class="lg:hidden">
                                <!-- Item Title Header -->
                                <div class="mb-2">
                                    <div class="flex items-start justify-between gap-2">
                                        <div class="flex-1 min-w-0">
                                            <div class="group relative">
                                                <h3 class="text-base font-bold text-base-content leading-tight">
                                                    <a href="{{ route('inventory.edit', $inventory) }}" class="hover:text-secondary transition-colors">
                                                        {{ $inventory->name }}
                                                    </a>
                                                    <div class="tooltip" data-tip="Click to edit inline">
                                                        <button class="inline-edit-btn opacity-0 group-hover:opacity-100 transition-opacity duration-200 ml-1 text-xs text-base-content/50 hover:text-secondary"
                                                                data-field="name"
                                                                data-inventory-id="{{ $inventory->id }}"
                                                                data-current-value="{{ $inventory->name }}">
                                                            <i class="fa-sharp fa-pencil"></i>
                                                        </button>
                                                    </div>
                                                </h3>
                                            </div>
                                            @if(request('trashed') === 'true')
                                                <span class="badge badge-error badge-xs mt-1">Deleted</span>
                                            @endif
                                        </div>
                                        <div class="dropdown dropdown-end flex-shrink-0">
                                            <div class="tooltip" data-tip="Click to change status">
                                                <div tabindex="0" role="button"
                                                     class="badge badge-sm cursor-pointer hover:brightness-110 transition-all editable-status {{ match ($inventory->status) {
                                                        'intake' => 'bg-neutral text-neutral-content',
                                                        'commodity' => 'bg-warning text-warning-content',
                                                        'refurbishing' => 'bg-orange-500 text-white',
                                                        'cleaning' => 'bg-info text-info-content',
                                                        'ready-to-list' => 'bg-secondary text-secondary-content',
                                                        'forsale' => 'bg-success text-success-content',
                                                        'sold' => 'bg-green-600 text-white',
                                                        'scrap' => 'bg-error text-error-content',
                                                        default => 'bg-neutral text-neutral-content',
                                                    } }}"
                                                     data-inventory-id="{{ $inventory->id }}"
                                                     data-current-status="{{ $inventory->status }}">
                                                    <i class="fa-sharp {{ match ($inventory->status) {
                                                        'intake' => 'fa-box-open',
                                                        'commodity' => 'fa-cube',
                                                        'refurbishing' => 'fa-tools',
                                                        'cleaning' => 'fa-broom',
                                                        'ready-to-list' => 'fa-check-circle',
                                                        'forsale' => 'fa-store',
                                                        'sold' => 'fa-dollar-sign',
                                                        'scrap' => 'fa-trash',
                                                        default => 'fa-question-circle',
                                                    } }} mr-1"></i>
                                                    {{ match ($inventory->status) {
                                                        'forsale' => 'For Sale',
                                                        default => ucfirst($inventory->status),
                                                    } }}
                                                </div>
                                                <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-64 p-2 shadow-lg border border-base-300">
                                                    @foreach(\App\Models\Inventory::STATUS as $statusKey => $statusLabel)
                                                        <li>
                                                            <a class="status-option flex items-center gap-3 p-3 hover:bg-base-200 rounded-lg transition-colors"
                                                               data-status="{{ $statusKey }}"
                                                               data-inventory-id="{{ $inventory->id }}">
                                                                <div class="badge badge-sm {{ match ($statusKey) {
                                                                    'intake' => 'bg-neutral text-neutral-content',
                                                                    'commodity' => 'bg-warning text-warning-content',
                                                                    'refurbishing' => 'bg-orange-500 text-white',
                                                                    'cleaning' => 'bg-info text-info-content',
                                                                    'ready-to-list' => 'bg-secondary text-secondary-content',
                                                                    'forsale' => 'bg-success text-success-content',
                                                                    'sold' => 'bg-green-600 text-white',
                                                                    'scrap' => 'bg-error text-error-content',
                                                                    default => 'bg-neutral text-neutral-content',
                                                                } }}">
                                                                    <i class="fa-sharp {{ match ($statusKey) {
                                                                        'intake' => 'fa-box-open',
                                                                        'commodity' => 'fa-cube',
                                                                        'refurbishing' => 'fa-tools',
                                                                        'cleaning' => 'fa-broom',
                                                                        'ready-to-list' => 'fa-check-circle',
                                                                        'forsale' => 'fa-store',
                                                                        'sold' => 'fa-dollar-sign',
                                                                        'scrap' => 'fa-trash',
                                                                        default => 'fa-question-circle',
                                                                    } }}"></i>
                                                                </div>
                                                                <span class="font-medium">{{ $statusKey === 'forsale' ? 'For Sale' : ucfirst($statusKey) }}</span>
                                                            </a>
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Photo and Details Section -->
                                <div class="flex gap-2 mb-2">
                                    <!-- Photo Column -->
                                    <div class="w-20 h-20 flex-shrink-0">
                                        <div class="relative bg-base-200 rounded-md overflow-hidden w-full h-full group">
                                            <a href="{{ route('inventory.show', $inventory) }}" class="block w-full h-full">
                                                @php
                                                    $firstImage = \App\Models\Image::forContext('inventory', $inventory->id)->first();
                                                    $imageSrc = $firstImage ? $firstImage->getImageSrc('sm') : asset('img/placeholder.jpg');
                                                @endphp
                                                <img
                                                    src="{{ $imageSrc }}"
                                                    alt="{{ $inventory->name }}"
                                                    class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300 ease-in-out"
                                                />
                                            </a>
                                            <!-- Asset Tag Badge -->
                                            <div class="absolute top-0.5 left-0.5 badge badge-neutral badge-xs font-mono text-xs leading-none px-1 py-0.5">
                                                #{{ $inventory->asset_tag }}
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Details Column -->
                                    <div class="flex-1 min-w-0 space-y-1 text-xs">
                                        <!-- Location -->
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-map text-neutral text-xs w-3"></i>
                                            <div class="tooltip" data-tip="Click to edit">
                                                <span class="editable-field cursor-pointer hover:text-info transition-colors font-medium"
                                                      data-field="location"
                                                      data-inventory-id="{{ $inventory->id }}">
                                                    {{ $inventory->location ?: 'No location' }}
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Category -->
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-boxes text-primary text-xs w-3"></i>
                                            <span class="font-medium text-base-content">{{ $inventory->category->name ?? 'N/A' }}</span>
                                        </div>

                                        <!-- Price -->
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-dollar-sign text-success text-xs w-3"></i>
                                            <div class="tooltip" data-tip="Click to edit">
                                                <span class="editable-field cursor-pointer hover:text-warning transition-colors font-bold text-success text-sm"
                                                      data-field="suggested_price"
                                                      data-inventory-id="{{ $inventory->id }}"
                                                      data-raw-value="{{ $inventory->suggested_price }}">
                                                    ${{ number_format($inventory->suggested_price, 2) }}
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Date Added -->
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-calendar text-info text-xs w-3"></i>
                                            <span class="text-base-content/70">{{ $inventory->created_at->format('M d, Y') }}</span>
                                        </div>

                                        <!-- Last Updated (if different) -->
                                        @if($inventory->updated_at->diffInDays($inventory->created_at) > 0)
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-clock text-warning text-xs w-3"></i>
                                                <span class="text-base-content/70">Updated {{ $inventory->updated_at->format('M d, Y') }}</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                @if (!empty($inventory->description))
                                    <!-- Description (Mobile) -->
                                    <div class="bg-base-200/30 rounded-md p-2 mb-2">
                                        <div class="flex items-center gap-1 mb-1">
                                            <i class="fa-sharp fa-sticky-note text-accent text-xs w-3"></i>
                                            <span class="font-medium text-base-content/70 text-xs">Description</span>
                                        </div>
                                        <div class="text-xs text-base-content/80 max-h-20 overflow-y-auto">
                                            {!! $inventory->description !!}
                                        </div>
                                    </div>
                                @endif

                                <!-- Mobile Action Bar -->
                                <div class="flex gap-1 pt-1 border-t border-base-200/30">
                                    <a href="{{ route('inventory.show', $inventory) }}" class="btn btn-primary btn-xs flex-1 gap-1">
                                        <i class="fa-sharp fa-eye text-xs"></i>
                                        <span class="text-xs">View</span>
                                    </a>
                                    <a href="{{ route('inventory.edit', $inventory) }}" class="btn btn-secondary btn-xs flex-1 gap-1">
                                        <i class="fa-sharp fa-edit text-xs"></i>
                                        <span class="text-xs">Edit</span>
                                    </a>
                                    <a href="{{ route('inventory.clone', $inventory) }}" class="btn btn-accent btn-xs flex-1 gap-1">
                                        <i class="fa-sharp fa-clone text-xs"></i>
                                        <span class="text-xs">Clone</span>
                                    </a>
                                </div>
                            </div>

                            <!-- Desktop Layout -->
                            <div class="hidden lg:grid lg:grid-cols-12 gap-3 items-stretch py-2">
                                <!-- Photo Column (2 cols) -->
                                <div class="col-span-2 p-0">
                                    <div class="relative bg-base-200 rounded-lg overflow-hidden aspect-square h-full min-h-24 group max-w-48">
                                        @php
                                            $firstImage = \App\Models\Image::forContext('inventory', $inventory->id)->first();
                                            $imageSrc = $firstImage ? $firstImage->getImageSrc('sm') : asset('img/placeholder.jpg');
                                        @endphp
                                        <img
                                            src="{{ $imageSrc }}"
                                            alt="{{ $inventory->name }}"
                                            class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300 ease-in-out"
                                        />

                                        <!-- Asset Tag Badge (Hovering) -->
                                        <div class="absolute top-2 left-2 badge badge-neutral badge-md font-mono shadow-lg text-base font-bold">
                                            #{{ $inventory->asset_tag }}
                                        </div>

                                        <!-- Hover Overlay with Action Buttons -->
                                        <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-3">
                                            <div class="tooltip" data-tip="View Item">
                                                <a href="{{ route('inventory.show', $inventory) }}" class="btn btn-circle btn-primary btn-lg shadow-lg hover:scale-110 transition-transform">
                                                    <i class="fa-sharp fa-eye text-lg"></i>
                                                </a>
                                            </div>
                                            <div class="tooltip" data-tip="Edit Item">
                                                <a href="{{ route('inventory.edit', $inventory) }}" class="btn btn-circle btn-secondary btn-lg shadow-lg hover:scale-110 transition-transform">
                                                    <i class="fa-sharp fa-edit text-lg"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Item Details Column (2 cols) -->
                                <div class="col-span-2">
                                    <div class="flex flex-col gap-1 h-full">
                                        <div class="group relative">
                                            <h3 class="font-bold text-lg">
                                                <a href="{{ route('inventory.edit', $inventory) }}" class="hover:text-secondary transition-colors">
                                                    {{ $inventory->name }}
                                                </a>
                                                <div class="tooltip" data-tip="Click to edit inline">
                                                    <button class="inline-edit-btn opacity-0 group-hover:opacity-100 transition-opacity duration-200 ml-2 text-sm text-base-content/50 hover:text-secondary"
                                                            data-field="name"
                                                            data-inventory-id="{{ $inventory->id }}"
                                                            data-current-value="{{ $inventory->name }}">
                                                        <i class="fa-sharp fa-pencil"></i>
                                                    </button>
                                                </div>
                                            </h3>
                                        </div>

                                        @if(request('trashed') === 'true')
                                            <span class="badge badge-error badge-xs w-fit">Deleted</span>
                                        @endif
                                        <div class="text-xs text-base-content/60 font-mono">Asset #{{ $inventory->asset_tag }}</div>

                                        <!-- Status Selector -->
                                        <div class="mt-1">
                                            <div class="dropdown dropdown-end">
                                                <div class="tooltip" data-tip="Click to change status">
                                                    <div tabindex="0" role="button"
                                                         class="badge badge-md cursor-pointer hover:brightness-110 transition-all editable-status {{ match ($inventory->status) {
                                                            'intake' => 'bg-neutral text-neutral-content',
                                                            'commodity' => 'bg-warning text-warning-content',
                                                            'refurbishing' => 'bg-orange-500 text-white',
                                                            'cleaning' => 'bg-info text-info-content',
                                                            'ready-to-list' => 'bg-secondary text-secondary-content',
                                                            'forsale' => 'bg-success text-success-content',
                                                            'sold' => 'bg-green-600 text-white',
                                                            'scrap' => 'bg-error text-error-content',
                                                            default => 'bg-neutral text-neutral-content',
                                                        } }}"
                                                         data-inventory-id="{{ $inventory->id }}"
                                                         data-current-status="{{ $inventory->status }}">
                                                        <i class="fa-sharp {{ match ($inventory->status) {
                                                            'intake' => 'fa-box-open',
                                                            'commodity' => 'fa-cube',
                                                            'refurbishing' => 'fa-tools',
                                                            'cleaning' => 'fa-broom',
                                                            'ready-to-list' => 'fa-check-circle',
                                                            'forsale' => 'fa-store',
                                                            'sold' => 'fa-dollar-sign',
                                                            'scrap' => 'fa-trash',
                                                            default => 'fa-question-circle',
                                                        } }} mr-1"></i>
                                                        {{ match ($inventory->status) {
                                                            'forsale' => 'For Sale',
                                                            default => ucfirst($inventory->status),
                                                        } }}
                                                    </div>
                                                    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-64 p-2 shadow-lg border border-base-300">
                                                        @foreach(\App\Models\Inventory::STATUS as $statusKey => $statusLabel)
                                                            <li>
                                                                <a class="status-option flex items-center gap-3 p-3 hover:bg-base-200 rounded-lg transition-colors"
                                                                   data-status="{{ $statusKey }}"
                                                                   data-inventory-id="{{ $inventory->id }}">
                                                                    <div class="badge badge-sm {{ match ($statusKey) {
                                                                        'intake' => 'bg-neutral text-neutral-content',
                                                                        'commodity' => 'bg-warning text-warning-content',
                                                                        'refurbishing' => 'bg-orange-500 text-white',
                                                                        'cleaning' => 'bg-info text-info-content',
                                                                        'ready-to-list' => 'bg-secondary text-secondary-content',
                                                                        'forsale' => 'bg-success text-success-content',
                                                                        'sold' => 'bg-green-600 text-white',
                                                                        'scrap' => 'bg-error text-error-content',
                                                                        default => 'bg-neutral text-neutral-content',
                                                                    } }}">
                                                                        <i class="fa-sharp {{ match ($statusKey) {
                                                                            'intake' => 'fa-box-open',
                                                                            'commodity' => 'fa-cube',
                                                                            'refurbishing' => 'fa-tools',
                                                                            'cleaning' => 'fa-broom',
                                                                            'ready-to-list' => 'fa-check-circle',
                                                                            'forsale' => 'fa-store',
                                                                            'sold' => 'fa-dollar-sign',
                                                                            'scrap' => 'fa-trash',
                                                                            default => 'fa-question-circle',
                                                                        } }}"></i>
                                                                    </div>
                                                                    <span class="font-medium">{{ $statusKey === 'forsale' ? 'For Sale' : ucfirst($statusKey) }}</span>
                                                                </a>
                                                            </li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Description Column (3 cols) -->
                                <div class="col-span-3">
                                    <div class="h-full flex flex-col">
                                        @if (!empty($inventory->description))
                                            <div class="flex items-center gap-1 mb-1">
                                                <i class="fa-sharp fa-sticky-note text-accent text-xs"></i>
                                                <span class="text-xs font-medium text-base-content/70">Description:</span>
                                            </div>
                                            <div class="bg-base-200/30 rounded-lg p-2 text-xs text-base-content/80 flex-1 overflow-y-auto max-h-24 border border-base-300/30">
                                                {!! $inventory->description !!}
                                            </div>
                                        @else
                                            <div class="flex items-center gap-1 mb-1">
                                                <i class="fa-sharp fa-sticky-note text-base-content/30 text-xs"></i>
                                                <span class="text-xs font-medium text-base-content/30">No description</span>
                                            </div>
                                            <div class="bg-base-200/20 rounded-lg p-2 text-xs text-base-content/40 flex-1 flex items-center justify-center border border-base-300/20">
                                                <span class="italic">No description available</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                <!-- Location & Category Column (2 cols) -->
                                <div class="col-span-2">
                                    <div class="flex flex-col gap-1">
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-map text-neutral text-xs"></i>
                                            <span class="text-xs font-medium text-base-content/70">Location:</span>
                                        </div>
                                        <div class="text-sm">
                                            <div class="tooltip" data-tip="Click to edit">
                                                <span class="editable-field cursor-pointer hover:text-info transition-colors"
                                                      data-field="location"
                                                      data-inventory-id="{{ $inventory->id }}">
                                                    {{ $inventory->location ?: 'Click to add location' }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-1 mt-1">
                                            <i class="fa-sharp fa-boxes text-primary text-xs"></i>
                                            <span class="text-xs font-medium text-base-content/70">Category:</span>
                                        </div>
                                        <div class="text-sm">{{ $inventory->category->name ?? 'N/A' }}</div>
                                    </div>
                                </div>

                                <!-- Price Column (1 col) -->
                                <div class="col-span-1">
                                    <div class="text-lg font-bold text-success">
                                        <div class="tooltip" data-tip="Click to edit">
                                            <span class="editable-field cursor-pointer hover:text-warning transition-colors"
                                                  data-field="suggested_price"
                                                  data-inventory-id="{{ $inventory->id }}"
                                                  data-raw-value="{{ $inventory->suggested_price }}">
                                                ${{ number_format($inventory->suggested_price, 2) }}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Dates & Info Column (1 col) -->
                                <div class="col-span-1">
                                    <div class="flex flex-col gap-1 text-xs">
                                        <div class="flex flex-col gap-1">
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-calendar text-info text-xs"></i>
                                                <span class="font-medium text-base-content/70">Added:</span>
                                            </div>
                                            <span class="text-xs">{{ $inventory->created_at->format('M d, Y') }}</span>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-images text-secondary text-xs"></i>
                                                <span class="font-medium text-base-content/70">Photos:</span>
                                            </div>
                                            @php
                                                $imageCount = \App\Models\Image::forContext('inventory', $inventory->id)->count();
                                            @endphp
                                            <span class="text-xs">{{ $imageCount }}</span>
                                        </div>
                                        @if($inventory->updated_at->diffInDays($inventory->created_at) > 0)
                                            <div class="flex flex-col gap-1">
                                                <div class="flex items-center gap-1">
                                                    <i class="fa-sharp fa-clock text-warning text-xs"></i>
                                                    <span class="font-medium text-base-content/70">Updated:</span>
                                                </div>
                                                <span class="text-xs">{{ $inventory->updated_at->format('M d, Y') }}</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                <!-- Actions Column (1 col) -->
                                <div class="col-span-1">
                                    <div class="flex flex-col gap-1 w-full">
                                        <a href="{{ route('inventory.show', $inventory) }}" class="btn btn-xs btn-primary w-full gap-1">
                                            <i class="fa-sharp fa-eye text-xs"></i>
                                            <span class="text-xs">View</span>
                                        </a>
                                        <a href="{{ route('inventory.edit', $inventory) }}" class="btn btn-xs btn-secondary w-full gap-1">
                                            <i class="fa-sharp fa-edit text-xs"></i>
                                            <span class="text-xs">Edit</span>
                                        </a>
                                        <a href="{{ route('inventory.clone', $inventory) }}" class="btn btn-xs btn-accent w-full gap-1">
                                            <i class="fa-sharp fa-clone text-xs"></i>
                                            <span class="text-xs">Clone</span>
                                        </a>
                                        <a href="{{ route('inventory.pdf', $inventory) }}" target="_blank" class="btn btn-xs btn-info w-full gap-1">
                                            <i class="fa-sharp fa-file-pdf text-xs"></i>
                                            <span class="text-xs">PDF</span>
                                        </a>
                                        @if (!request()->has('trashed') || request('trashed') !== 'true')
                                            <form action="{{ route('inventory.destroy', $inventory) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this item?');" class="w-full">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-xs btn-error w-full gap-1">
                                                    <i class="fa-sharp fa-trash text-xs"></i>
                                                    <span class="text-xs">Delete</span>
                                                </button>
                                            </form>
                                        @else
                                            <form action="{{ route('inventory.restore', $inventory) }}" method="POST" class="w-full">
                                                @csrf
                                                <button type="submit" class="btn btn-xs btn-success w-full gap-1">
                                                    <i class="fa-sharp fa-undo text-xs"></i>
                                                    <span class="text-xs">Restore</span>
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <!-- Empty State -->
                        <div class="text-center text-base-content/70 py-12">
                            <div class="flex flex-col items-center gap-4">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                        <i class="fa-sharp fa-boxes text-2xl"></i>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <h3 class="text-lg font-medium text-base-content/80">No inventory items found</h3>
                                    <p class="text-sm text-base-content/60 mt-1">
                                        @if(request()->hasAny(['search', 'category', 'status']))
                                            Try adjusting your search or filter criteria
                                        @else
                                            Get started by adding your first inventory item
                                        @endif
                                    </p>
                                </div>
                                @if(!request()->hasAny(['search', 'category', 'status']))
                                    <a href="{{ route('inventory.create') }}" class="btn btn-primary btn-sm gap-2">
                                        <i class="fa-sharp fa-plus"></i>
                                        Add First Item
                                    </a>
                                @endif
                            </div>
                        </div>
                    @endforelse
                <!-- Pagination Section -->
                @if($inventories->hasPages())
                    <div class="px-6 py-4 border-t border-base-300/50 bg-base-50">
                        <x-pagination :paginator="$inventories" :pagination="$pagination" />
                    </div>
                @endif
            </div>
        </div>
    </div>


<!-- Inline Editing JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle status dropdown selections
    document.querySelectorAll('.status-option').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();

            const newStatus = this.dataset.status;
            const inventoryId = this.dataset.inventoryId;
            const statusBadge = document.querySelector(`.editable-status[data-inventory-id="${inventoryId}"]`);
            const currentStatus = statusBadge.dataset.currentStatus;

            // Don't update if it's the same status
            if (newStatus === currentStatus) {
                return;
            }

            // Close the dropdown
            statusBadge.blur();

            // Show loading state
            const originalContent = statusBadge.innerHTML;
            statusBadge.innerHTML = '<span class="loading loading-spinner loading-xs"></span> Updating...';

            // Send AJAX request
            fetch(`/inventory/${inventoryId}/field`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    field: 'status',
                    value: newStatus
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.status_info) {
                    // Update all status badges for this inventory item (both mobile and desktop)
                    const statusInfo = data.status_info;
                    const allStatusBadges = document.querySelectorAll(`.editable-status[data-inventory-id="${inventoryId}"]`);

                    allStatusBadges.forEach(badge => {
                        badge.className = `badge badge-md cursor-pointer hover:brightness-110 transition-all editable-status ${statusInfo.badge_class}`;
                        badge.innerHTML = `<i class="fa-sharp ${statusInfo.icon} mr-1"></i>${statusInfo.label}`;
                        badge.dataset.currentStatus = newStatus;

                        // Close the dropdown by removing focus
                        badge.blur();

                        // Also close any open dropdowns by clicking elsewhere
                        document.activeElement.blur();

                        // Show success feedback
                        badge.classList.add('animate-pulse');
                        setTimeout(() => {
                            badge.classList.remove('animate-pulse');
                        }, 1000);
                    });
                } else {
                    // Show error and restore original content
                    alert(data.error || 'Failed to update status');
                    statusBadge.innerHTML = originalContent;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating status');
                statusBadge.innerHTML = originalContent;
            });
        });
    });

    // Handle inline editing for inventory fields (keeping existing editable-field for non-name fields)
    document.querySelectorAll('.editable-field').forEach(function(element) {
        element.addEventListener('click', function() {
            const field = this.dataset.field;
            const inventoryId = this.dataset.inventoryId;
            const currentValue = field === 'suggested_price' ? this.dataset.rawValue : this.textContent.trim();

            // Don't edit if already editing
            if (this.querySelector('input')) {
                return;
            }

            // Create input element
            const input = document.createElement('input');
            input.type = field === 'suggested_price' ? 'number' : 'text';
            input.value = currentValue === 'Click to add location' ? '' : currentValue;
            input.className = 'input input-sm input-bordered w-full max-w-xs';

            if (field === 'suggested_price') {
                input.step = '0.01';
                input.min = '0';
                input.placeholder = '0.00';
            }

            // Store original content
            const originalContent = this.innerHTML;

            // Replace content with input
            this.innerHTML = '';
            this.appendChild(input);
            input.focus();
            input.select();

            // Handle save on Enter or blur
            const saveEdit = () => {
                const newValue = input.value.trim();

                // Validate required fields
                if (field === 'name' && !newValue) {
                    alert('Name is required');
                    input.focus();
                    return;
                }

                // Show loading state
                this.innerHTML = '<span class="loading loading-spinner loading-xs"></span> Saving...';

                // Send AJAX request
                fetch(`/inventory/${inventoryId}/field`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        field: field,
                        value: newValue
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update the display value
                        this.innerHTML = data.value;

                        // Update raw value for price fields
                        if (field === 'suggested_price') {
                            this.dataset.rawValue = data.raw_value;
                        }

                        // Show success feedback
                        this.classList.add('text-success');
                        setTimeout(() => {
                            this.classList.remove('text-success');
                        }, 1000);
                    } else {
                        // Show error and restore original content
                        alert(data.error || 'Failed to update field');
                        this.innerHTML = originalContent;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while saving');
                    this.innerHTML = originalContent;
                });
            };

            // Handle cancel on Escape
            const cancelEdit = () => {
                this.innerHTML = originalContent;
            };

            // Event listeners
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    saveEdit();
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    cancelEdit();
                }
            });

            input.addEventListener('blur', saveEdit);
        });
    });

    // Handle inline editing for name field via edit buttons
    document.querySelectorAll('.inline-edit-btn').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const field = this.dataset.field;
            const inventoryId = this.dataset.inventoryId;
            const currentValue = this.dataset.currentValue;

            // Find the title element (the link containing the name)
            const titleLink = this.parentElement.parentElement.querySelector('a');

            // Don't edit if already editing
            if (titleLink.querySelector('input')) {
                return;
            }

            // Store original href and remove it to prevent navigation during editing
            const originalHref = titleLink.href;
            titleLink.removeAttribute('href');
            titleLink.style.cursor = 'default';

            // Create input element
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentValue;
            input.className = 'input input-sm input-bordered w-full max-w-xs';

            // Store original content
            const originalContent = titleLink.innerHTML;

            // Replace content with input
            titleLink.innerHTML = '';
            titleLink.appendChild(input);
            input.focus();
            input.select();

            // Handle save on Enter or blur
            const saveEdit = () => {
                const newValue = input.value.trim();

                // Validate required fields
                if (!newValue) {
                    alert('Name is required');
                    input.focus();
                    return;
                }

                // Show loading state
                titleLink.innerHTML = '<span class="loading loading-spinner loading-xs"></span> Saving...';

                // Send AJAX request
                fetch(`/inventory/${inventoryId}/field`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        field: field,
                        value: newValue
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update the display value and restore link functionality
                        titleLink.innerHTML = newValue;
                        titleLink.href = originalHref;
                        titleLink.style.cursor = '';

                        // Update the button's current value
                        button.dataset.currentValue = newValue;

                        // Show success feedback
                        titleLink.classList.add('text-success');
                        setTimeout(() => {
                            titleLink.classList.remove('text-success');
                        }, 1000);
                    } else {
                        // Show error and restore original content and link
                        alert(data.error || 'Failed to update field');
                        titleLink.innerHTML = originalContent;
                        titleLink.href = originalHref;
                        titleLink.style.cursor = '';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while saving');
                    titleLink.innerHTML = originalContent;
                    titleLink.href = originalHref;
                    titleLink.style.cursor = '';
                });
            };

            // Handle cancel on Escape
            const cancelEdit = () => {
                titleLink.innerHTML = originalContent;
                titleLink.href = originalHref;
                titleLink.style.cursor = '';
            };

            // Event listeners
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    saveEdit();
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    cancelEdit();
                }
            });

            input.addEventListener('blur', saveEdit);
        });
    });
});
</script>

<!-- Custom styles for status dropdown -->
<style>
.dropdown-content {
    z-index: 1000 !important;
}
</style>
</x-app-layout>


