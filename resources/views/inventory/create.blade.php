<x-app-layout
    page-title="Create New Inventory Item"
    page-icon="fa-sharp fa-plus"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('inventory.index'),
            'text' => 'Back to Inventory'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Inventory', 'route' => 'inventory.index', 'icon' => 'fa-boxes'],
        ['name' => 'Create New Item', 'icon' => 'fa-plus']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">

            <!-- Form Introduction Card -->
            <div class="bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
                <!-- Decorative Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 left-0 w-32 h-32 bg-primary rounded-full -translate-x-16 -translate-y-16"></div>
                    <div class="absolute bottom-0 right-0 w-24 h-24 bg-secondary rounded-full translate-x-12 translate-y-12"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-start gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-gradient-to-br from-primary to-primary-focus text-primary-content w-12 rounded-xl shadow-lg">
                                <i class="fa-sharp fa-plus-circle text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-2xl font-bold text-base-content mb-2">
                                Add New Inventory Item
                            </h3>
                            <p class="text-base-content/70 text-base leading-relaxed">
                                Fill in the details below to create a new inventory item. All required fields are marked with an asterisk (*).
                            </p>
                        </div>
                    </div>
                </div>
            </div>
                <form action="{{ route('inventory.store') }}" method="POST" class="space-y-10">
                    @csrf

                    <!-- Basic Information Section -->
                    <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg overflow-hidden">
                        <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-primary text-primary-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-info-circle text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Basic Information</h4>
                            </div>
                        </div>

                        <div class="p-6 space-y-6">
                            <!-- Name Field -->
                            <div>
                                <label class="label" for="name">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-tag text-primary mr-2"></i>
                                        Item Name <span class="text-error font-bold">*</span>
                                    </span>
                                </label>
                                <input type="text"
                                       name="name"
                                       id="name"
                                       class="input input-bordered w-full"
                                       value="{{ old('name') }}"
                                       placeholder="Enter a descriptive name for your item..."
                                       required>
                                @error('name')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>

                            <!-- Description Field -->
                            <div>
                                <label class="label" for="description">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-align-left text-secondary mr-2"></i>
                                        Description
                                    </span>
                                </label>
                                <textarea name="description"
                                          id="description"
                                          rows="4"
                                          class="textarea textarea-bordered w-full"
                                          placeholder="Provide detailed information about the item, its condition, specifications, or any other relevant details...">{{ old('description') }}</textarea>
                                @error('description')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Category and Classification Section -->
                    <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg overflow-hidden">
                        <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-tags text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Category & Classification</h4>
                            </div>
                        </div>

                        <div class="p-6 space-y-6">
                            <!-- Category Field -->
                            <div>
                                <label class="label" for="inv_category_id">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-folder text-secondary mr-2"></i>
                                        Category <span class="text-error font-bold">*</span>
                                    </span>
                                </label>
                                <select name="inv_category_id" id="inv_category_id" class="select select-bordered w-full">
                                    <option value="">Choose a category...</option>
                                    @foreach($categories as $category)
                                    <option value="{{ $category->id }}" data-quantity-type="{{ $category->quantity_type }}" {{ old('inv_category_id') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                    @endforeach
                                </select>
                                @error('inv_category_id')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror

                                <!-- Enhanced Category Notes -->
                                <div class="mt-4 p-4 bg-gradient-to-r from-info/5 to-info/10 rounded-xl border border-info/20 transition-all duration-300">
                                    <div id="categoryNotes" class="text-sm text-info flex items-start gap-3">
                                        <i class="fa-sharp fa-info-circle mt-0.5 flex-shrink-0 text-base"></i>
                                        <span class="leading-relaxed">Select a category to see specific requirements and notes.</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Status Field -->
                            <div>
                                <label class="label" for="status">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-flag text-accent mr-2"></i>
                                        Status <span class="text-error font-bold">*</span>
                                    </span>
                                </label>
                                <select name="status" id="status" class="select select-bordered w-full">
                                    <option value="intake">📥 Intake</option>
                                    <option value="refurbishing">🔧 Refurbishing</option>
                                    <option value="cleaning">🧽 Cleaning</option>
                                    <option value="ready-to-list">📋 Ready to List</option>
                                    <option value="forsale">💰 For Sale</option>
                                    <option value="sold">✅ Sold</option>
                                    <option value="scrap">🗑️ Scrap</option>
                                    <option value="commodity" class="hidden">📦 Commodity</option>
                                </select>
                                @error('status')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Assignment and Tracking Section -->
                    <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg overflow-hidden">
                        <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-accent text-accent-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-clipboard-list text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Assignment & Tracking</h4>
                            </div>
                        </div>

                        <div class="p-6 space-y-6">
                            <!-- Technician Field -->
                            <div id="technicianIdField" class="hidden transition-all duration-300">
                                <label class="label" for="technician_id">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-user-gear text-accent mr-2"></i>
                                        Assigned Technician
                                    </span>
                                </label>
                                <select name="technician_id" id="technician_id" class="select select-bordered w-full">
                                    <option value="">Choose a technician...</option>
                                    @foreach($technicians as $technician)
                                    <option value="{{ $technician->id }}" {{ old('technician_id') == $technician->id ? 'selected' : '' }}>
                                        👤 {{ $technician->name }}
                                    </option>
                                    @endforeach
                                </select>
                                @error('technician_id')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>

                            <!-- Quantity Field -->
                            <div id="quantityField" class="hidden transition-all duration-300">
                                <label class="label" for="quantity">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-hashtag text-accent mr-2"></i>
                                        Quantity
                                    </span>
                                </label>
                                <input type="number"
                                       name="quantity"
                                       id="quantity"
                                       class="input input-bordered max-w-xs"
                                       value="1"
                                       min="1"
                                       step="1"
                                       placeholder="1">
                                @error('quantity')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>

                            <!-- Location Field -->
                            <div>
                                <label class="label" for="location">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-map-marker-alt text-accent mr-2"></i>
                                        Location
                                    </span>
                                </label>
                                <input type="text"
                                       name="location"
                                       id="location"
                                       class="input input-bordered w-full"
                                       value="Bench"
                                       placeholder="Enter current location...">
                                @error('location')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Submit Section -->
                    <div class="divider my-8">
                        <span class="text-base-content/50 font-medium">Ready to create?</span>
                    </div>

                    <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg p-6">
                        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-300 text-base-content w-10 rounded-lg">
                                        <i class="fa-sharp fa-info-circle text-sm"></i>
                                    </div>
                                </div>
                                <div class="text-center sm:text-left">
                                    <p class="font-medium text-base-content">Review your information</p>
                                    <p class="text-sm text-base-content/60">Make sure all details are correct before creating</p>
                                </div>
                            </div>

                            <div class="flex gap-3 w-full sm:w-auto">
                                <a href="{{ route('inventory.index') }}" class="btn btn-outline btn-lg gap-2 flex-1 sm:flex-none hover:btn-error transition-all duration-200">
                                    <i class="fa-sharp fa-arrow-left"></i>
                                    Cancel
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none hover:btn-primary-focus transition-all duration-200 group">
                                    <i class="fa-sharp fa-plus group-hover:scale-110 transition-transform duration-200"></i>
                                    Create Item
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
        </div>
    </div>
</x-app-layout>

<script>
document.addEventListener("DOMContentLoaded", function () {
    const categorySelect = document.getElementById("inv_category_id");
    const technicianField = document.getElementById("technicianIdField");
    const quantityField = document.getElementById("quantityField");
    const statusSelect = document.getElementById("status");
    const categoryNotes = document.getElementById("categoryNotes");

    const updateFields = () => {
        const selectedOption = categorySelect.options[categorySelect.selectedIndex];
        const quantityType = selectedOption.dataset.quantityType;

        // Show/hide quantity field with animation
        if (quantityType === "unit") {
            quantityField.classList.remove("hidden");
            quantityField.classList.add("animate-fade-in");
        } else {
            quantityField.classList.add("hidden");
            quantityField.classList.remove("animate-fade-in");
        }

        // Show/hide technician field with animation
        if (quantityType === "individual") {
            technicianField.classList.remove("hidden");
            technicianField.classList.add("animate-fade-in");
        } else {
            technicianField.classList.add("hidden");
            technicianField.classList.remove("animate-fade-in");
        }

        // Adjust status options
        let statusUpdated = false;
        Array.from(statusSelect.options).forEach(option => {
            if (quantityType === "weight") {
                if (option.value !== "commodity" && option.value !== "sold") {
                    option.classList.add("hidden");
                } else {
                    option.classList.remove("hidden");
                }
                if (!statusUpdated) {
                    statusSelect.value = "commodity"; // Auto-select "commodity"
                    statusUpdated = true;
                    // set technician to null
                    document.getElementById("technician_id").value = "";
                }

                categoryNotes.innerHTML = `
                    <i class="fa-sharp fa-weight-scale text-warning mr-3 text-base"></i>
                    <span class="leading-relaxed"><strong class="text-warning">Weight-based Category:</strong> This category is for items sold by weight. These are individually tracked bins with a weight value.</span>
                `;
            } else {
                if (option.value === "commodity") {
                    option.classList.add("hidden");
                } else {
                    option.classList.remove("hidden");
                }

                // auto select "intake" if not already set
                if (!statusUpdated) {
                    statusSelect.value = "intake";
                    statusUpdated = true;
                }

                if (quantityType === "individual") {
                    categoryNotes.innerHTML = `
                        <i class="fa-sharp fa-microchip text-primary mr-3 text-base"></i>
                        <span class="leading-relaxed"><strong class="text-primary">Individual Tracking:</strong> This category is for single-unit items that must be tracked individually, like devices being refurbished.</span>
                    `;
                }
                if (quantityType === "unit") {
                    categoryNotes.innerHTML = `
                        <i class="fa-sharp fa-calculator text-secondary mr-3 text-base"></i>
                        <span class="leading-relaxed"><strong class="text-secondary">Unit-based Tracking:</strong> Items in this category are tracked by quantity, like sticks of RAM or per-piece cables.</span>
                    `;
                }
            }
        });

        // If no category selected, show default message
        if (!quantityType) {
            categoryNotes.innerHTML = `
                <i class="fa-sharp fa-info-circle text-info mr-3 text-base"></i>
                <span class="leading-relaxed">Select a category to see specific requirements and notes.</span>
            `;
        }
    };

    categorySelect.addEventListener("change", updateFields);
    updateFields(); // Run on page load to set initial state

    // Enhanced visual feedback for form interactions
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {


        // Blur effects
        input.addEventListener('blur', function() {
            const formField = this.closest('div');
            const section = this.closest('.bg-base-100');

            if (formField) {
                formField.classList.remove('ring-2', 'ring-primary/30', 'ring-offset-2');
            }
            if (section) {
                section.classList.remove('shadow-xl', 'border-primary/30');
            }
        });

        // Input validation feedback
        input.addEventListener('input', function() {
            if (this.checkValidity()) {
                this.classList.remove('input-error', 'select-error', 'textarea-error');
                this.classList.add('input-success', 'select-success', 'textarea-success');
            } else {
                this.classList.remove('input-success', 'select-success', 'textarea-success');
            }
        });
    });

    // Add loading state to submit button
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('button[type="submit"]');

    form.addEventListener('submit', function() {
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;
    });

    // Add smooth scroll to first error if validation fails
    const firstError = document.querySelector('.text-error');
    if (firstError) {
        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
});
</script>

<style>
/* Enhanced animations and transitions */
@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slide-in {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in {
    animation: fade-in 0.4s ease-out;
}

.animate-slide-in {
    animation: slide-in 0.3s ease-out;
}

/* Enhanced form styling */
.label {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bg-base-100 {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom input focus states */
.input:focus, .select:focus, .textarea:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Success states */
.input-success {
    border-color: hsl(var(--su));
    box-shadow: 0 0 0 2px hsl(var(--su) / 0.2);
}

.select-success {
    border-color: hsl(var(--su));
    box-shadow: 0 0 0 2px hsl(var(--su) / 0.2);
}

.textarea-success {
    border-color: hsl(var(--su));
    box-shadow: 0 0 0 2px hsl(var(--su) / 0.2);
}

/* Hover effects for sections */
.bg-base-100:hover {
    transform: translateY(-1px);
}

/* Enhanced button hover effects */
.btn:hover {
    transform: translateY(-1px);
}

/* Loading spinner for submit button */
.btn.loading::after {
    border-color: currentColor transparent currentColor transparent;
}
</style>
