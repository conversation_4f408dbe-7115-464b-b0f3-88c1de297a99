<div class="card bg-base-100 shadow-md">
    <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
        <div class="flex items-center gap-3">
            <div class="avatar avatar-placeholder">
                <div class="bg-info text-info-content w-8 rounded-lg">
                    <i class="fa-sharp fa-camera text-sm"></i>
                </div>
            </div>
            <h4 class="text-lg font-semibold text-base-content">Photos</h4>
        </div>
    </div>
    <div class="p-6 space-y-6">

        @php
            // Calculate remaining photos and get current images using the new system
            $currentImages = \App\Models\Image::forContext('inventory', $inventory->id)->get();
            $currentPhotoCount = $currentImages->count();
            $maxAllowedPhotos = 20;
            $remainingPhotos = max(0, $maxAllowedPhotos - $currentPhotoCount);
            
            // Format images for the gallery component
            $galleryImages = $currentImages->map(function($image) {
                return [
                    'id' => $image->id,
                    'thumbnail_url' => $image->getImageSrc('sm'),
                    'full_url' => $image->getImageSrc('full'),
                    'title' => $image->title ?: $image->og_filename,
                    'alt' => $image->alt_text ?: $image->title ?: $image->og_filename,
                    'name' => $image->og_filename
                ];
            })->toArray();
        @endphp
        
        <!-- Upload Photos with Immediate Upload -->
        @if($remainingPhotos > 0)
            <x-image-dropzone
                id="inventory-photos"
                name="photos[]"
                :max-files="$remainingPhotos"
                :max-filesize="10"
                :max-width="2400"
                :max-height="1920"
                :client-resize="true"
                accepted-formats="image/jpeg,image/jpg,image/png,image/webp"
                :multiple="true"
                :immediate-upload="true"
                :trigger-camera-upload="false"
                capture=""
                context-type="inventory"
                :context-id="$inventory->id"
                label=""
                help-text="Drag and drop photos here or click to browse - uploads immediately"
                sub-text="You can upload {{ $remainingPhotos }} more photo{{ $remainingPhotos !== 1 ? 's' : '' }}. Images will be automatically compressed and uploaded."
            />
        @else
            <div class="alert alert-warning">
                <i class="fa-sharp fa-exclamation-triangle"></i>
                <span>Maximum number of photos reached ({{ $maxAllowedPhotos }}). Delete some photos to upload more.</span>
            </div>
        @endif

        <!-- Image Gallery -->
        <div class="mt-6">
            <x-image-gallery
                :images="$galleryImages"
                context-type="inventory"
                :context-id="$inventory->id"
                :show-reorder="true"
                :show-delete="true"
                :show-download="true"
                title="Gallery"
                empty-message="No photos uploaded yet"
                :allow-reorder="true"
            />
        </div>
    </div>
</div>

@push('styles')
<style>
    .photo-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
    }

    .photo-item {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        border: 1px solid var(--color-base-300);
        border-radius: 8px;
        background-color: var(--color-base-100);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .photo-thumbnail {
        width: 100%;
        height: auto;
        aspect-ratio: 4 / 3;
        object-fit: cover;
        display: block;
    }

    /* Action Icons Row */
    .photo-actions {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        padding: 0.5rem;
    }

    .action-icon {
        color: var(--color-base-content);
        font-size: 1.25rem;
        cursor: pointer;
        transition: color 0.2s ease;
    }

    .action-icon:hover {
        color: var(--primary);
    }

    .action-icon i {
        display: inline-block;
    }

</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.6/Sortable.min.js"
    integrity="sha256-bQqDH8GbS66FF5etM5MVfoYa+3hiRZwRImNZsn4sQzc=" crossorigin="anonymous"></script>
<script>
document.addEventListener("DOMContentLoaded", () => {
    // Listen for successful image uploads
    const dropzoneElement = document.querySelector('[data-dropzone-id="dropzone-inventory-photos"]');
    if (dropzoneElement) {
        let uploadedCount = 0;
        let totalUploads = 0;
        
        // Track when files start uploading
        dropzoneElement.addEventListener('files-selected', function(e) {
            totalUploads = e.detail.count || 0;
            uploadedCount = 0;
        });
        
        dropzoneElement.addEventListener('image-uploaded', function(e) {
            uploadedCount++;
            
            // Only reload after all images have been uploaded
            if (uploadedCount >= totalUploads && totalUploads > 0) {
                // Wait a bit to ensure all uploads are complete
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            }
        });
    }
});
</script>
@endpush
