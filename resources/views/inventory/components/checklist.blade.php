<div class="card bg-base-100 shadow-md" id="checklist-card">
    <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
                <div class="avatar avatar-placeholder">
                    <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                        <i class="fa-sharp fa-list-check text-sm"></i>
                    </div>
                </div>
                <h4 class="text-lg font-semibold text-base-content">Checklist</h4>
            </div>
            <div class="card-saving-indicator hidden">
                <i class="fa-sharp fa-spinner fa-spin text-secondary"></i>
            </div>
        </div>
    </div>
    <div class="p-6 space-y-6">
        <form id="checklist-form" action="{{ route('inventory.checklist.update', $inventory->id) }}"
            method="POST">
            @csrf
            @method('PUT')

            <div class="flex justify-between items-center pb-3">
                <div id="checklist-save-status" class="text-sm text-base-content/70"><i
                        class="fa-sharp  fa-check"></i> All changes saved</div>
                <button type="button" id="manual-checklist-save" class="btn btn-xs btn-primary">
                    <i class="fa-sharp fa-save"></i> Save Now
                </button>
            </div>

            @if($inventory->category && $inventory->category->checklistFields->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6">
                @foreach ($inventory->category->checklistFields->sortBy('order') as $field)
                <div class="mb-6">
                    <label for="checklist_{{ $field->id }}"
                        class="block text-sm font-medium text-base-content mb-2">
                        {{ $field->name }}
                    </label>

                    <!-- Description -->
                    @if ($field->description)
                        <p class="text-sm text-base-content/60 mb-2">{{ $field->description }}</p>
                    @endif

                    @if ($field->type === 'text')
                        <!-- Text Field -->
                        <input type="text" name="checklist[{{ $field->id }}]"
                            id="checklist_{{ $field->id }}"
                            class="input w-full input-bordered auto-save-input"
                            value="{{ old('checklist.' . $field->id, $inventory->checklistItems->firstWhere('checklist_field_id', $field->id)->value ?? '') }}">
                    @elseif($field->type === 'textarea')
                        <!-- Textarea Field -->
                        <textarea name="checklist[{{ $field->id }}]" class="textarea w-full input-bordered auto-save-input">{{ old('checklist.' . $field->id, $inventory->checklistItems->firstWhere('checklist_field_id', $field->id)->long_value ?? '') }}</textarea>
                    @elseif($field->type === 'yes/no')
                        <!-- Yes/No Slider -->
                        <div class="relative inline-flex items-center cursor-pointer"
                            id="toggle-wrapper-{{ $field->id }}">
                            <input type="hidden" name="checklist[{{ $field->id }}]"
                                id="hidden-input-{{ $field->id }}"
                                value="{{ old('checklist.' . $field->id, $inventory->checklistItems->firstWhere('checklist_field_id', $field->id)->value ?? 0) }}"
                                class="hidden-auto-save-input">
                            <div id="toggle-switch-{{ $field->id }}" class="w-14 h-8"
                                style="background-color: {{ old('checklist.' . $field->id, $inventory->checklistItems->firstWhere('checklist_field_id', $field->id)->value ?? 0) ? 'var(--color-success)' : 'var(--color-error)' }}; border-radius: 9999px; position: relative; transition: background-color 0.3s;"
                                tabindex="0">
                                <span id="toggle-knob-{{ $field->id }}"
                                    style="width: 20px; height: 20px; background-color: var(--color-base-content); border-radius: 50%; position: absolute; top: 50%; left: {{ old('checklist.' . $field->id, $inventory->checklistItems->firstWhere('checklist_field_id', $field->id)->value ?? 0) ? 'calc(100% - 24px)' : '4px' }}; transform: translateY(-50%); transition: left 0.3s;">
                                </span>
                            </div>
                            <span id="toggle-text-{{ $field->id }}" class="ml-3 text-sm font-medium"
                                style="color: {{ old('checklist.' . $field->id, $inventory->checklistItems->firstWhere('checklist_field_id', $field->id)->value ?? 0) ? 'var(--color-success)' : 'var(--color-error)' }};">
                                {{ old('checklist.' . $field->id, $inventory->checklistItems->firstWhere('checklist_field_id', $field->id)->value ?? 0) ? 'Yes' : 'No' }}
                            </span>
                        </div>
                    @elseif($field->type === 'numeric')
                        <!-- Numeric with Units -->
                        <div class="flex items-center">
                            <input type="number" name="checklist[{{ $field->id }}]"
                                id="checklist_{{ $field->id }}"
                                class="w-full input input-bordered auto-save-input"
                                value="{{ old('checklist.' . $field->id, $inventory->checklistItems->firstWhere('checklist_field_id', $field->id)->value ?? '') }}">
                            <span
                                class="ml-2 text-base-content/70">{{ json_decode($field->options, true)['unit'] ?? '' }}</span>
                        </div>
                    @elseif($field->type === 'select')
                        <!-- Select Dropdown -->
                        <select name="checklist[{{ $field->id }}]" id="checklist_{{ $field->id }}"
                            class="w-full select select-bordered auto-save-input">

                            @php
                                // Get the current value from the database or old input
                                $currentValue = trim(
                                    old(
                                        "checklist.{$field->id}",
                                        $inventory->checklistItems->firstWhere('checklist_field_id', $field->id)
                                            ->value ?? '',
                                    ),
                                );
                            @endphp

                            @foreach (json_decode($field->options, true)['select_list'] ?? [] as $option)
                                <option value="{{ trim($option) }}"
                                    {{ $currentValue === trim($option) ? 'selected' : '' }}>
                                    {{ trim($option) }}
                                </option>
                            @endforeach
                        </select>
                    @elseif($field->type === 'date')
                        <!-- Date Field -->
                        @php
                            $dateValue = $inventory->checklistItems->firstWhere('checklist_field_id', $field->id)?->value;
                            $formattedDate = '';
                            if ($dateValue) {
                                try {
                                    $formattedDate = \Carbon\Carbon::parse($dateValue)->format('Y-m-d');
                                } catch (\Exception $e) {
                                    $formattedDate = $dateValue; // Use as-is if parsing fails
                                }
                            }
                        @endphp
                        <input type="date" name="checklist[{{ $field->id }}]"
                            id="checklist_{{ $field->id }}"
                            class="input w-full input-bordered auto-save-input"
                            value="{{ old('checklist.' . $field->id, $formattedDate) }}">
                    @endif

                    @if ($field->type === 'checkboxes')
                        <div class="grid grid-cols-2 gap-2" data-checkbox-group>
                            @php
                                // Ensure $checkedValues is always an array
                                $checklistItem = $inventory->checklistItems->firstWhere('checklist_field_id', $field->id);
                                $checkedValues = [];
                                if ($checklistItem && $checklistItem->long_value) {
                                    $checkedValues = (array) json_decode($checklistItem->long_value, true);
                                }
                            @endphp

                            @foreach (json_decode($field->options, true)['checkboxes'] ?? [] as $checkbox)
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" name="checklist[{{ $field->id }}][]"
                                        value="{{ trim($checkbox) }}"
                                        class="checkbox checkbox-primary auto-save-input" tabindex="0"
                                        {{ in_array(trim($checkbox), $checkedValues) ? 'checked' : '' }}>
                                    <span>{{ $checkbox }}</span>
                                </label>
                            @endforeach
                        </div>
                    @endif


                </div>
                @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <div class="avatar avatar-placeholder">
                        <div class="bg-base-300 text-base-content w-12 rounded-lg">
                            <i class="fa-sharp fa-list-check text-lg"></i>
                        </div>
                    </div>
                    <p class="text-base-content/60 mt-4">No checklist fields defined for this category.</p>
                    <p class="text-sm text-base-content/40">Contact an administrator to add checklist fields to the "{{ $inventory->category->name }}" category.</p>
                </div>
            @endif
        </form>
    </div>
</div>

@push('scripts')
<script>
    console.log('Checklist script loaded');
    let checklistSaveTimeout;

    // Update save status
    function updateSaveStatus(status, success = true) {
        const saveStatus = document.getElementById('checklist-save-status');
        saveStatus.textContent = status;

        // If we're saving, set background color to light amber
        const card = document.getElementById('checklist-card');
        if (success) {
            card.classList.remove('bg-error/10');
            card.classList.add('bg-base-100');
        } else {
            card.classList.remove('bg-base-200');
            card.classList.add('bg-error/10');
        }
    }

    // Auto-save checklist data
    function autoSaveChecklistData() {
        console.log('autoSaveChecklistData function called');

        const form = document.getElementById('checklist-form');
        if (!form) {
            console.error('Checklist form not found!');
            return;
        }

        const formData = new FormData(form);
        const formDataObj = Object.fromEntries(formData);

        // Add debugging to see what data is being sent
        console.log('Form action:', form.action);
        console.log('Sending checklist data:', formDataObj);

        fetch(form.action, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => {
                console.log('Response received - status:', response.status);
                console.log('Response headers:', response.headers);

                if (response.ok) {
                    return response.json().then(data => {
                        console.log('Success response data:', data);
                        updateSaveStatus("All changes saved");
                    }).catch(jsonError => {
                        console.error('JSON parsing error:', jsonError);
                        return response.text().then(text => {
                            console.log('Response as text:', text);
                            updateSaveStatus("Response parsing error", false);
                        });
                    });
                } else {
                    return response.text().then(text => {
                        console.error('Error response status:', response.status);
                        console.error('Error response text:', text);
                        updateSaveStatus("Failed to save changes", false);
                    });
                }
            })
            .catch(error => {
                console.error('Fetch error details:', error);
                updateSaveStatus("Error saving changes", false);
            });
    }

    // Trigger auto-save with debounce
    function triggerChecklistAutoSave() {
        console.log('triggerChecklistAutoSave called');
        clearTimeout(checklistSaveTimeout);
        updateSaveStatus("Saving...", false);
        checklistSaveTimeout = setTimeout(autoSaveChecklistData, 450);
    }

    document.addEventListener("DOMContentLoaded", function() {
        console.log('DOM Content Loaded - Setting up checklist event listeners');

        // Check if checklist form exists
        const checklistForm = document.getElementById('checklist-form');
        if (!checklistForm) {
            console.log('No checklist form found - skipping checklist event listeners');
            return;
        }

        // Add auto-save functionality to inputs
        const autoSaveInputs = document.querySelectorAll('.auto-save-input');
        console.log('Found auto-save inputs:', autoSaveInputs.length);
        autoSaveInputs.forEach((input, index) => {
            console.log(`Setting up input listener ${index}:`, input.name, input.type);
            input.addEventListener('input', triggerChecklistAutoSave);
        });

        // Add auto-save functionality to hidden inputs (for yes/no toggles)
        const hiddenInputs = document.querySelectorAll('.hidden-auto-save-input');
        console.log('Found hidden auto-save inputs:', hiddenInputs.length);
        hiddenInputs.forEach((input, index) => {
            console.log(`Setting up hidden input listener ${index}:`, input.name);
            input.addEventListener('change', triggerChecklistAutoSave);
        });

        // Handle checkboxes (for type "checkboxes")
        const checkboxGroups = document.querySelectorAll('[data-checkbox-group]');
        console.log('Found checkbox groups:', checkboxGroups.length);
        checkboxGroups.forEach(group => {
            const checkboxes = group.querySelectorAll('input[type="checkbox"]');
            console.log('Checkboxes in group:', checkboxes.length);
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', triggerChecklistAutoSave);
            });
        });

        // Initialize Yes/No toggles
        document.querySelectorAll('[id^="toggle-wrapper-"]').forEach(wrapper => {
            wrapper.addEventListener('click', () => {
                const id = wrapper.id.split('-')[2];
                const hiddenInput = document.getElementById(`hidden-input-${id}`);
                const isActive = hiddenInput.value === "1";

                hiddenInput.value = isActive ? "0" : "1";
                document.getElementById(`toggle-switch-${id}`).style.backgroundColor =
                    isActive ? "var(--color-error)" : "var(--color-success)"; // Gray for No, Green for Yes
                document.getElementById(`toggle-knob-${id}`).style.left = isActive ?
                    "4px" : "calc(100% - 24px)"; // Move knob accordingly
                document.getElementById(`toggle-text-${id}`).textContent = isActive ? "No" :
                    "Yes";
                document.getElementById(`toggle-text-${id}`).style.color = isActive ?
                    "var(--color-error)" : "var(--color-success)"; // Change text color

                // Trigger change event on hidden input to trigger auto-save
                hiddenInput.dispatchEvent(new Event('change'));
            });
        });

        //Handle hitting enter on yes/no toggles
        document.querySelectorAll('[id^="toggle-wrapper-"]').forEach(wrapper => {
            wrapper.addEventListener('keydown', (event) => {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    wrapper.click();
                }
            });
        });

        // Handle manual save button
        const manualSaveButton = document.getElementById('manual-checklist-save');
        if (manualSaveButton) {
            manualSaveButton.addEventListener('click', function() {
                console.log('Manual save button clicked');
                autoSaveChecklistData();
            });
        }
    });
</script>
@endpush
