{{-- AI Description Generator Component --}}
<div class="card bg-base-100 shadow-md">
    <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
        <div class="flex items-center gap-3">
            <div class="avatar avatar-placeholder">
                <div class="bg-accent text-accent-content w-8 rounded-lg">
                    <i class="fa-sharp fa-robot text-sm"></i>
                </div>
            </div>
            <h4 class="text-lg font-semibold text-base-content">AI Generated Description</h4>
        </div>
    </div>
    <div class="p-6 space-y-6">

        @if ($inventory->category->ai_promptable)
            <!-- Generate/Regenerate Button -->
            <div class="mb-4">
                <button id="generate-description-btn" class="btn btn-info" title="Generate Description">
                    <i class="fa-sharp fa-sync"></i>
                    @if ($inventory->generated_description)
                        Regenerate Description
                    @else
                        Generate Description
                    @endif
                </button>
                <p class="text-sm text-base-content/60 mt-2">
                    <i class="fa-sharp fa-info-circle mr-1"></i>
                    Generate an AI-powered product description based on the current inventory information and checklist data.
                    Note: This uses an AI model and costs money. Only generate descriptions after all information is correct!
                </p>
            </div>

            <!-- Description Container -->
            <div id="ai-description-container" class="prose max-w-none">
                @if ($inventory->generated_description)
                    <div class="bg-base-200 p-4 rounded-lg">
                        <h4 class="text-sm font-semibold text-base-content/80 mb-2">Generated Description:</h4>
                        <div class="text-base-content">
                            {!! $inventory->generated_description !!}
                        </div>
                    </div>
                @else
                    <div class="alert alert-info">
                        <i class="fa-sharp fa-info-circle"></i>
                        <div>
                            <h4 class="font-bold">No Generated Description Available</h4>
                            <div class="text-sm">Click the button above to generate a description for this inventory item.</div>
                        </div>
                    </div>
                @endif
            </div>
        @else
            <!-- Category doesn't support AI descriptions -->
            <div class="alert alert-warning">
                <i class="fa-sharp fa-exclamation-triangle"></i>
                <div>
                    <h4 class="font-bold">AI Descriptions Not Available</h4>
                    <div class="text-sm">AI-generated descriptions are not supported for this category.</div>
                </div>
            </div>
        @endif
    </div>
</div>

@if ($inventory->category->ai_promptable)
    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const generateButton = document.getElementById('generate-description-btn');
                
                if (generateButton) {
                    generateButton.addEventListener('click', function() {
                        // Disable button and show loading state
                        generateButton.disabled = true;
                        generateButton.innerHTML = '<i class="fa-sharp fa-spinner fa-spin"></i> Generating...';
                        
                        // Make AJAX request to generate description
                        fetch("{{ route('inventory.regenerateDescription', $inventory->id) }}", {
                                method: 'POST',
                                headers: {
                                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                                    'Content-Type': 'application/json',
                                }
                            })
                            .then(response => response.json())
                            .then(data => {
                                // Update the description container with the generated description
                                const container = document.getElementById('ai-description-container');
                                container.innerHTML = `
                                    <div class="bg-base-200 p-4 rounded-lg">
                                        <h4 class="text-sm font-semibold text-base-content/80 mb-2">Generated Description:</h4>
                                        <div class="text-base-content">
                                            ${data.generated_description}
                                        </div>
                                    </div>
                                `;

                                // Re-enable the button and reset its content
                                generateButton.disabled = false;
                                generateButton.innerHTML = '<i class="fa-sharp fa-sync"></i> Regenerate Description';
                            })
                            .catch(error => {
                                console.error('Error generating description:', error);
                                
                                // Handle errors gracefully
                                generateButton.disabled = false;
                                generateButton.innerHTML = '<i class="fa-sharp fa-sync"></i> Regenerate Description';
                                
                                // Show error message
                                const container = document.getElementById('ai-description-container');
                                container.innerHTML = `
                                    <div class="alert alert-error">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        <div>
                                            <h4 class="font-bold">Error Generating Description</h4>
                                            <div class="text-sm">Failed to generate description. Please try again.</div>
                                        </div>
                                    </div>
                                `;
                            });
                    });
                } else {
                    console.log('Generate description button not found on this page.');
                }
            });
        </script>
    @endpush
@endif
