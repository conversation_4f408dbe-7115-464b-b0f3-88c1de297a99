<x-app-layout
    page-title="Edit Inventory Item: {{ $inventory->asset_tag }}"
    page-icon="fa-sharp fa-edit"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('inventory.index'),
            'text' => 'Back to Inventory'
        ],
        [
            'type' => 'view',
            'route' => route('inventory.show', $inventory),
            'text' => 'View Item'
        ],
        [
            'type' => 'duplicate',
            'route' => route('inventory.create', ['duplicate' => $inventory->id])
        ],
        [
            'type' => 'pdf',
            'route' => route('inventory.pdf', $inventory),
            'target' => '_blank'
        ],

    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Inventory', 'route' => 'inventory.index', 'icon' => 'fa-boxes'],
        ['name' => 'Edit Item: ' . $inventory->asset_tag, 'icon' => 'fa-edit']
    ]">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/awesomplete/1.1.5/awesomplete.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/awesomplete/1.1.5/awesomplete.min.js"></script>

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-2 lg:px-8 space-y-6">

            <!-- 2x2 Grid Layout for Edit Forms -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

                <!-- Column 1: Primary Identity Card (spans 2 rows) -->
                <div class="lg:row-span-2">
                    <!-- General Information Card -->
                    <div class="card bg-base-100 shadow-md" id="inventory-details-card">
                        <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-primary text-primary-content w-8 rounded-lg">
                                            <i class="fa-sharp fa-box text-sm"></i>
                                        </div>
                                    </div>
                                    <h4 class="text-lg font-semibold text-base-content">General Information</h4>
                                </div>
                                <div class="card-saving-indicator hidden">
                                    <i class="fa-sharp fa-spinner fa-spin text-primary"></i>
                                </div>
                            </div>
                        </div>
                        <div class="p-6 space-y-6">
                            <form action="{{ route('inventory.update', $inventory->id) }}" method="POST" id="inventory-form" class="space-y-6">
                                @csrf
                                @method('PUT')

                                <!-- Name Field -->
                                <div>
                                    <label class="label" for="name">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-tag text-primary mr-2"></i>
                                            Item Name <span class="text-error font-bold">*</span>
                                        </span>
                                    </label>
                                    <input type="text" name="name" id="name" class="input input-bordered w-full"
                                           value="{{ old('name', $inventory->name) }}" placeholder="Enter item name..." required>
                                    @error('name')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>

                                <!-- Description Field -->
                                <div>
                                    <label class="label" for="description">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-sticky-note text-primary mr-2"></i>
                                            Notes / Description
                                        </span>
                                    </label>

                                    <!-- Quill Editor Container -->
                                    <div id="editor" class="w-full h-40 rounded bg-base-300"></div>

                                    <!-- Hidden Input Field to Store Quill Content -->
                                    <input type="hidden" name="description" id="description"
                                        value="{{ old('description', $inventory->description) }}">

                                    @error('description')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>

                                <!-- Condition Field -->
                                <div>
                                    <label class="label" for="condition">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-tools text-primary mr-2"></i>
                                            Condition
                                        </span>
                                    </label>
                                    <textarea name="condition" id="condition" class="textarea textarea-bordered w-full"
                                              placeholder="Describe the condition of the item in simple terms">{{ old('condition', $inventory->condition) }}</textarea>
                                    @error('condition')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>

                                <!-- Location Field -->
                                <div>
                                    <label class="label" for="location">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-map-marker text-primary mr-2"></i>
                                            Location
                                        </span>
                                    </label>
                                    <input type="text" name="location" id="location" class="input input-bordered w-full"
                                           value="{{ old('location', $inventory->location) }}" placeholder="Enter storage location...">
                                    @error('location')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Column 2: Secondary Info Cards -->
                <div class="space-y-6">

                    <!-- Status & Category Card -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-info-circle text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Status & Category</h4>
                            </div>
                        </div>
                        <div class="p-6 space-y-6">
                            <form action="{{ route('inventory.update', $inventory->id) }}" method="POST" id="status-form" class="space-y-6">
                                @csrf
                                @method('PUT')

                                <!-- Status Field -->
                                <div>
                                    <label class="label" for="status">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-info-circle text-secondary mr-2"></i>
                                            Status
                                        </span>
                                    </label>

                                    @php
                                        $hasInvoice = $inventory->getIndividualSoldItemInvoice() !== null;
                                        $isLockedSold = $inventory->status === 'sold' && $hasInvoice;
                                    @endphp

                                    @if ($isLockedSold)
                                        <!-- Display locked status with message if sold and has invoice -->
                                        <div class="space-y-2">
                                            <div class="flex items-center gap-2">
                                                <span class="badge bg-green-600 text-white">
                                                    <i class="fa-sharp fa-dollar-sign mr-1"></i>
                                                    Sold
                                                </span>
                                                <span class="badge bg-warning text-warning-content badge-sm">
                                                    <i class="fa-sharp fa-lock mr-1"></i>
                                                    Locked
                                                </span>
                                            </div>
                                            <p class="text-warning text-xs">
                                                <i class="fa-sharp fa-info-circle mr-1"></i>
                                                Status is locked because this item is linked to an invoice. Remove the item from the invoice to change status.
                                            </p>
                                        </div>
                                        <input type="hidden" name="status" value="sold">
                                    @else
                                        <select name="status" id="status" class="select select-bordered w-full">
                                            @if ($inventory->category->quantity_type === 'weight')
                                                <option value="commodity"
                                                    {{ old('status', $inventory->status) === 'commodity' ? 'selected' : '' }}>
                                                    Commodity
                                                </option>
                                                <option value="sold"
                                                    {{ old('status', $inventory->status) === 'sold' ? 'selected' : '' }}>Sold
                                                </option>
                                            @else
                                                <option value="intake"
                                                    {{ old('status', $inventory->status) === 'intake' ? 'selected' : '' }}>
                                                    Intake
                                                </option>
                                                <option value="refurbishing"
                                                    {{ old('status', $inventory->status) === 'refurbishing' ? 'selected' : '' }}>
                                                    Refurbishing
                                                </option>
                                                <option value="cleaning"
                                                    {{ old('status', $inventory->status) === 'cleaning' ? 'selected' : '' }}>
                                                    Cleaning
                                                </option>
                                                <option value="ready-to-list"
                                                    {{ old('status', $inventory->status) === 'ready-to-list' ? 'selected' : '' }}>
                                                    Ready to List
                                                </option>
                                                <option value="forsale"
                                                    {{ old('status', $inventory->status) === 'forsale' ? 'selected' : '' }}>For
                                                    Sale
                                                </option>
                                                <option value="sold"
                                                    {{ old('status', $inventory->status) === 'sold' ? 'selected' : '' }}>Sold
                                                </option>
                                                <option value="scrap"
                                                    {{ old('status', $inventory->status) === 'scrap' ? 'selected' : '' }}>Scrap
                                                </option>
                                            @endif
                                        </select>
                                    @endif

                                    @error('status')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>

                                <!-- Category Field -->
                                <div>
                                    <label class="label" for="inv_category_id">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-box text-secondary mr-2"></i>
                                            Category
                                        </span>
                                    </label>
                                    <select name="inv_category_id" id="inv_category_id" class="select select-bordered w-full">
                                        @foreach ($categories as $category)
                                            <option value="{{ $category->id }}"
                                                {{ old('inv_category_id', $inventory->inv_category_id) == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('inv_category_id')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>

                                <!-- Technician Field (only show if individual) -->
                                @if ($inventory->category->quantity_type === 'individual')
                                    <div>
                                        <label class="label" for="technician_id">
                                            <span class="label-text">
                                                <i class="fa-sharp fa-user text-secondary mr-2"></i>
                                                Technician
                                            </span>
                                        </label>
                                        <select name="technician_id" id="technician_id" class="select select-bordered w-full">
                                            @foreach ($technicians as $technician)
                                                <option value="{{ $technician->id }}"
                                                    {{ old('technician_id', $inventory->technician_id) == $technician->id ? 'selected' : '' }}>
                                                    {{ $technician->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('technician_id')
                                        <div class="label">
                                            <span class="label-text-alt text-error flex items-center gap-1">
                                                <i class="fa-sharp fa-exclamation-triangle"></i>
                                                {{ $message }}
                                            </span>
                                        </div>
                                        @enderror
                                    </div>
                                @endif
                            </form>
                        </div>
                    </div>

                    <!-- Pricing & Quantities Card -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-accent text-accent-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-dollar-sign text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Pricing & Quantities</h4>
                            </div>
                        </div>
                        <div class="p-6 space-y-6">
                            <form action="{{ route('inventory.update', $inventory->id) }}" method="POST" id="pricing-form" class="space-y-6">
                                @csrf
                                @method('PUT')

                                <!-- Suggested Price -->
                                <div>
                                    <label class="label" for="suggested_price">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-dollar-sign text-accent mr-2"></i>
                                            Suggested Price Per Unit
                                        </span>
                                    </label>
                                    <input type="number" name="suggested_price" id="suggested_price" step="0.01"
                                           class="input input-bordered w-full"
                                           value="{{ old('suggested_price', $inventory->suggested_price) }}" placeholder="0.00">
                                    @error('suggested_price')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>
                                <!-- Quantity Field (for unit type) -->
                                @if ($inventory->category->quantity_type === 'unit')
                                    <div>
                                        <label class="label" for="quantity">
                                            <span class="label-text">
                                                <i class="fa-sharp fa-boxes text-accent mr-2"></i>
                                                Quantity Available
                                            </span>
                                        </label>
                                        <input type="number" name="quantity" id="quantity" step="0.01"
                                               class="input input-bordered w-full"
                                               value="{{ old('quantity', $inventory->quantity) }}" placeholder="0">
                                        @error('quantity')
                                        <div class="label">
                                            <span class="label-text-alt text-error flex items-center gap-1">
                                                <i class="fa-sharp fa-exclamation-triangle"></i>
                                                {{ $message }}
                                            </span>
                                        </div>
                                        @enderror
                                    </div>
                                @endif

                                <!-- Unit Field (for unit or unlimited type) -->
                                @if ($inventory->category->quantity_type === 'unit' || $inventory->category->quantity_type === 'unlimited')
                                    <div>
                                        <label class="label" for="unit">
                                            <span class="label-text">
                                                <i class="fa-sharp fa-box text-accent mr-2"></i>
                                                Unit
                                            </span>
                                        </label>
                                        <select name="unit" id="unit" class="select select-bordered w-full">
                                            @foreach ($units as $unit)
                                                <option value="{{ $unit }}"
                                                    {{ old('unit', $inventory->unit) === $unit ? 'selected' : '' }}>
                                                    {{ $unit }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('unit')
                                        <div class="label">
                                            <span class="label-text-alt text-error flex items-center gap-1">
                                                <i class="fa-sharp fa-exclamation-triangle"></i>
                                                {{ $message }}
                                            </span>
                                        </div>
                                        @enderror
                                    </div>
                                @endif

                                <!-- Weight Field (for weight type) -->
                                @if ($inventory->category->quantity_type === 'weight')
                                    <div>
                                        <label class="label" for="weight">
                                            <span class="label-text">
                                                <i class="fa-sharp fa-weight text-accent mr-2"></i>
                                                Weight
                                            </span>
                                        </label>
                                        <div class="flex items-center gap-2">
                                            <input type="number" name="weight" id="weight" step="0.01"
                                                   class="input input-bordered w-full"
                                                   value="{{ old('weight', $inventory->weight) }}" placeholder="0.00">
                                            <span class="text-base-content/70 text-sm">{{ $inventory->category->unit }}</span>
                                        </div>
                                        @error('weight')
                                        <div class="label">
                                            <span class="label-text-alt text-error flex items-center gap-1">
                                                <i class="fa-sharp fa-exclamation-triangle"></i>
                                                {{ $message }}
                                            </span>
                                        </div>
                                        @enderror
                                    </div>
                                @endif
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sold Item Information - Full Width Section -->
            @if ($inventory->category->quantity_type === 'individual' && $inventory->status === 'sold' && $inventory->getIndividualSoldItemInvoice())
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-dollar-sign text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Sale Information</h4>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Sold Price x Quantity -->
                            <div>
                                <label class="label" for="sold_price_quantity">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-calculator text-success mr-2"></i>
                                        Sold Price x Quantity
                                    </span>
                                </label>
                                <input type="text" id="sold_price_quantity"
                                       class="input input-bordered w-full input-disabled" readonly
                                       value="${{ number_format($inventory->sold_price, 2) }} x {{ $inventory->getSoldQuantityAttribute() }} = ${{ number_format($inventory->sold_price * $inventory->getSoldQuantityAttribute(), 2) }}">
                            </div>

                            <!-- Sold Date -->
                            <div>
                                <label class="label" for="sold_time">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-calendar text-success mr-2"></i>
                                        Sold Date
                                    </span>
                                </label>
                                <input type="date" name="sold_time" id="sold_time"
                                       class="input input-bordered w-full input-disabled"
                                       value="{{ old('sold_time', optional($inventory->getSoldDateAttribute())->format('Y-m-d')) }}"
                                       readonly>
                            </div>

                            <!-- Customer -->
                            <div>
                                <label class="label">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-user text-success mr-2"></i>
                                        Customer
                                    </span>
                                </label>
                                @if ($inventory->customer)
                                    <a href="{{ route('customers.show', $inventory->customer->id) }}" target="_blank"
                                       class="link link-primary flex items-center gap-2">
                                        {{ $inventory->customer->name }}
                                        <i class="fa-sharp fa-external-link-alt text-xs"></i>
                                    </a>
                                @endif
                            </div>

                            <!-- Related Invoice -->
                            <div>
                                <label class="label">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-file-invoice text-success mr-2"></i>
                                        Related Invoice
                                    </span>
                                </label>
                                @if ($inventory->getIndividualSoldItemInvoice())
                                    <a href="{{ route('invoices.show', $inventory->getIndividualSoldItemInvoice()->id) }}"
                                       target="_blank" class="link link-primary flex items-center gap-2">
                                        {{ $inventory->getIndividualSoldItemInvoice()->invoice_number }} -
                                        {{ \Carbon\Carbon::parse($inventory->getIndividualSoldItemInvoice()->invoice_date)->format('M d Y') }}
                                        <i class="fa-sharp fa-external-link-alt text-xs"></i>
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @elseif ($inventory->category->quantity_type === 'individual' && $inventory->status === 'sold' && !$inventory->getIndividualSoldItemInvoice())
                <!-- Show message when sold but no invoice -->
                <div class="alert alert-info">
                    <i class="fa-sharp fa-info-circle"></i>
                    <span>This item is marked as sold but is not linked to any invoice. Sale details are not available.</span>
                </div>
            @endif

            <!-- Auto-Save Status & Manual Save Section -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-success text-success-content w-8 rounded-lg">
                                <i class="fa-sharp fa-save text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Save Changes</h4>
                    </div>
                </div>
                <div class="p-6">
                    <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-base-300 text-base-content w-10 rounded-lg">
                                    <i class="fa-sharp fa-info-circle text-sm"></i>
                                </div>
                            </div>
                            <div class="text-center sm:text-left">
                                <p id="saveStatusIndicator" class="font-medium text-base-content">Changes will save automatically</p>
                                <p class="text-sm text-base-content/60">Your changes are saved as you type for accessibility</p>
                            </div>
                        </div>
                        <div class="flex gap-3 w-full sm:w-auto">
                            <button type="button" id="save-all-button" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                                <i class="fa-sharp fa-save"></i>
                                Save All Changes
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Component Sections -->
            <!-- Photos Section -->
            @include('inventory.components.photos', ['inventory' => $inventory])

            <!-- Checklist Section -->
            @include('inventory.components.checklist', ['inventory' => $inventory])

            <!-- AI Description Generator Section -->
            @include('inventory.components.ai-description-generator', ['inventory' => $inventory])
        </div>
    </div>

    @importQuill

    @push('scripts')
        <script>
            // Initialize Quill Editor
            document.addEventListener('DOMContentLoaded', function() {
                // Custom toolbar options
                const toolbarOptions = [
                    ['bold', 'italic', 'underline', 'strike'],        // toggled buttons
                    ['blockquote', 'code-block'],
                    [{ 'header': 1 }, { 'header': 2 }],               // custom button values
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    [{ 'indent': '-1'}, { 'indent': '+1' }],          // outdent/indent
                    [{ 'color': [] }, { 'background': [] }],          // dropdown with defaults
                    [{ 'align': [] }],
                    ['clean']                                         // remove formatting button
                ];

                const quill = new Quill('#editor', {
                    theme: 'snow',
                    modules: {
                        toolbar: toolbarOptions
                    },
                    placeholder: 'Enter notes or description here...'
                });

                // Set the initial content if the "notes" field already has data
                const initialContent = document.getElementById('description').value;
                quill.root.innerHTML = initialContent;

                // Synchronize Quill content to the hidden input field
                const notesInput = document.getElementById('description');
                quill.on('text-change', function() {
                    notesInput.value = quill.root.innerHTML;
                    // Trigger auto-save if it exists
                    if (typeof triggerAutoSave === 'function') {
                        triggerAutoSave();
                    }
                });
            });
        </script>
    @endpush


    <script>
        // Auto-save functionality following style guide patterns
        document.addEventListener('DOMContentLoaded', function() {
            let saveTimeout;
            const saveStatusIndicator = document.getElementById('saveStatusIndicator');

            // Get all forms that need auto-save
            const inventoryForm = document.getElementById('inventory-form');
            const statusForm = document.getElementById('status-form');
            const pricingForm = document.getElementById('pricing-form');

            // Auto-save status messages
            const statusMessages = {
                default: "Changes will save automatically",
                saving: "Saving Changes...",
                success: "All changes saved",
                error: "Error saving changes"
            };

            // Update save status with proper styling
            function updateSaveStatus(status = 'default', customMessage = null) {
                if (!saveStatusIndicator) return;

                const message = customMessage || statusMessages[status];
                saveStatusIndicator.textContent = message;

                // Update text color based on status
                saveStatusIndicator.className = 'font-medium ' +
                    (status === 'success' ? 'text-success' :
                     status === 'error' ? 'text-error' :
                     status === 'saving' ? 'text-info' : 'text-base-content');

                // Show card-level saving indicators
                const cardIndicators = document.querySelectorAll('.card-saving-indicator');
                cardIndicators.forEach(indicator => {
                    if (status === 'saving') {
                        indicator.classList.remove('hidden');
                    } else {
                        indicator.classList.add('hidden');
                    }
                });

                // Reset status after delay for success/error
                if (status === 'success') {
                    setTimeout(() => updateSaveStatus('default'), 3000);
                } else if (status === 'error') {
                    setTimeout(() => updateSaveStatus('default'), 5000);
                }
            }

            // Auto-save function
            function autoSave() {
                updateSaveStatus('saving');

                // Collect data from all forms
                const allFormData = new FormData();

                // Add data from inventory form
                if (inventoryForm) {
                    const inventoryData = new FormData(inventoryForm);
                    for (let [key, value] of inventoryData.entries()) {
                        allFormData.append(key, value);
                    }
                }

                // Add data from status form
                if (statusForm) {
                    const statusData = new FormData(statusForm);
                    for (let [key, value] of statusData.entries()) {
                        allFormData.append(key, value);
                    }
                }

                // Add data from pricing form
                if (pricingForm) {
                    const pricingData = new FormData(pricingForm);
                    for (let [key, value] of pricingData.entries()) {
                        allFormData.append(key, value);
                    }
                }

                // Send the save request
                fetch("{{ route('inventory.update', $inventory->id) }}", {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: allFormData
                })
                .then(response => {
                    if (response.ok) {
                        updateSaveStatus('success');
                        return response.json();
                    } else {
                        throw new Error('Save failed');
                    }
                })
                .catch(error => {
                    console.error("Auto-save error:", error);
                    updateSaveStatus('error');
                });
            }

            // Trigger auto-save with debounce
            function triggerAutoSave() {
                clearTimeout(saveTimeout);
                updateSaveStatus('saving');
                saveTimeout = setTimeout(autoSave, 500);
            }

            // Add event listeners to all form inputs
            const forms = [inventoryForm, statusForm, pricingForm].filter(Boolean);
            forms.forEach(form => {
                const inputs = form.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    if (input.type === 'radio' || input.type === 'checkbox') {
                        input.addEventListener('change', triggerAutoSave);
                    } else {
                        input.addEventListener('input', triggerAutoSave);
                    }
                });
            });

            // Make triggerAutoSave globally available for Quill editor
            window.triggerAutoSave = triggerAutoSave;

            // Handle manual save button
            const saveAllButton = document.getElementById('save-all-button');
            if (saveAllButton) {
                saveAllButton.addEventListener('click', function() {
                    // Disable button and show loading state
                    saveAllButton.disabled = true;
                    saveAllButton.innerHTML = '<i class="fa-sharp fa-spinner fa-spin"></i> Saving...';

                    // Trigger the auto-save function
                    autoSave();

                    // Re-enable button after a delay
                    setTimeout(() => {
                        saveAllButton.disabled = false;
                        saveAllButton.innerHTML = '<i class="fa-sharp fa-save"></i> Save All Changes';
                    }, 2000);
                });
            }
        });
    </script>



</x-app-layout>
