@php
    // Build action buttons array dynamically based on permissions
    $actionButtons = [
        [
            'name' => 'Time Clock',
            'route' => route('time-clock'),
            'icon' => 'fa-sharp fa-clock',
            'class' => 'btn btn-secondary btn-sm gap-2'
        ],
        [
            'name' => 'Print My Timesheet',
            'route' => route('my-timesheet.print', ['start_date' => $startDate, 'end_date' => $endDate]),
            'icon' => 'fa-sharp fa-print',
            'class' => 'btn btn-info btn-sm gap-2',
            'target' => '_blank'
        ]
    ];

    // Add timesheets button if user has permission
    if (Auth::user()->hasPermission('view_timesheets')) {
        $actionButtons[] = [
            'name' => 'Timesheets',
            'route' => route('timesheets.index'),
            'icon' => 'fa-sharp fa-file-invoice',
            'class' => 'btn btn-accent btn-sm gap-2'
        ];
    }

    // Add create time card button if user has permission
    if (Auth::user()->hasPermission('edit_timecards')) {
        $actionButtons[] = [
            'name' => 'Create Time Card',
            'route' => route('timecards.create-for-date'),
            'icon' => 'fa-sharp fa-plus',
            'class' => 'btn btn-primary btn-sm gap-2'
        ];
    }
@endphp

<x-app-layout
    page-title="Time Cards"
    page-icon="fa-sharp fa-calendar-days"
    :action-buttons="$actionButtons"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Time Cards', 'icon' => 'fa-calendar-days']
    ]">


    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Filter Card -->
            <div class="card bg-base-100 shadow-xl mb-4">
                <div class="card-body p-4">
                    <div class="flex flex-wrap justify-between items-center mb-2">
                        <h2 class="card-title text-lg mb-0">
                            <i class="fa-sharp fa-solid fa-filter mr-2 text-primary"></i>
                            Filter Time Cards
                        </h2>

                        <!-- Quick Date Filters -->
                        <div class="flex flex-wrap gap-1">
                            <a href="{{ route('timecards.index', ['start_date' => now()->startOfWeek()->format('Y-m-d'), 'end_date' => now()->endOfWeek()->format('Y-m-d'), 'user_id' => $userId]) }}" class="btn btn-xs btn-outline">
                                This Week
                            </a>
                            @php
                                $isMTD = $startDate == now()->startOfMonth()->format('Y-m-d') && $endDate == now()->format('Y-m-d');
                            @endphp
                            <a href="{{ route('timecards.index', ['start_date' => now()->startOfMonth()->format('Y-m-d'), 'end_date' => now()->format('Y-m-d'), 'user_id' => $userId]) }}" class="btn btn-xs {{ $isMTD ? 'btn-primary' : 'btn-outline' }}">
                                MTD
                            </a>
                            <a href="{{ route('timecards.index', ['start_date' => now()->startOfMonth()->format('Y-m-d'), 'end_date' => now()->endOfMonth()->format('Y-m-d'), 'user_id' => $userId]) }}" class="btn btn-xs btn-outline">
                                This Month
                            </a>
                            <a href="{{ route('timecards.index', ['start_date' => now()->startOfMonth()->format('Y-m-d'), 'end_date' => now()->startOfMonth()->addDays(14)->format('Y-m-d'), 'user_id' => $userId]) }}" class="btn btn-xs btn-outline">
                                1st-15th
                            </a>
                            <a href="{{ route('timecards.index', ['start_date' => now()->startOfMonth()->addDays(15)->format('Y-m-d'), 'end_date' => now()->endOfMonth()->format('Y-m-d'), 'user_id' => $userId]) }}" class="btn btn-xs btn-outline">
                                16th-End
                            </a>
                            <a href="{{ route('timecards.index', ['start_date' => now()->subDays(30)->format('Y-m-d'), 'end_date' => now()->format('Y-m-d'), 'user_id' => $userId]) }}" class="btn btn-xs btn-outline">
                                Last 30 Days
                            </a>
                        </div>
                    </div>

                    <form method="GET" action="{{ route('timecards.index') }}" class="mt-2">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-2">
                            <div class="form-control">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text bg-base-200">
                                        <i class="fa-sharp fa-solid fa-calendar-day text-primary"></i>
                                    </span>
                                    <input type="date" name="start_date" value="{{ $startDate }}" class="input input-bordered input-sm w-full" placeholder="Start Date" />
                                </div>
                            </div>

                            <div class="form-control">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text bg-base-200">
                                        <i class="fa-sharp fa-solid fa-calendar-day text-primary"></i>
                                    </span>
                                    <input type="date" name="end_date" value="{{ $endDate }}" class="input input-bordered input-sm w-full" placeholder="End Date" />
                                </div>
                            </div>

                            @if(Auth::user()->hasPermission('view_timecards'))
                            <div class="form-control">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text bg-base-200">
                                        <i class="fa-sharp fa-solid fa-user text-primary"></i>
                                    </span>
                                    <select name="user_id" class="select select-bordered select-sm w-full">
                                        <option value="">All Users</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ $userId == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            @endif

                            <div class="form-control">
                                <button type="submit" class="btn btn-sm btn-primary w-full">
                                    <i class="fa-sharp fa-solid fa-filter mr-1"></i>
                                    Apply Filters
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Summary Stats for Entire Date Range -->
            @if($timeCards->count() > 0)
            <div class="stats shadow w-full mb-4 bg-base-100">
                <div class="stat">
                    <div class="stat-figure text-primary">
                        <i class="fa-sharp fa-solid fa-calendar-days text-2xl"></i>
                    </div>
                    <div class="stat-title">Days</div>
                    <div class="stat-value text-primary text-2xl">{{ $totalDays }}</div>
                    <div class="stat-desc text-xs">{{ $startDate }} to {{ $endDate }}</div>
                </div>

                <div class="stat">
                    <div class="stat-figure text-accent">
                        <i class="fa-sharp fa-solid fa-mug-hot text-2xl"></i>
                    </div>
                    <div class="stat-title">Break Hours</div>
                    <div class="stat-value text-accent text-2xl">
                        {{ number_format($totalBreakHours, 2) }}
                    </div>
                    <div class="stat-desc text-xs">Break time in period</div>
                </div>

                <div class="stat">
                    <div class="stat-figure text-secondary">
                        <i class="fa-sharp fa-solid fa-business-time text-2xl"></i>
                    </div>
                    <div class="stat-title">Work Hours</div>
                    <div class="stat-value text-secondary text-2xl">
                        {{ number_format($totalWorkHours, 2) }}
                    </div>
                    <div class="stat-desc text-xs">Regular work hours</div>
                </div>

                <div class="stat">
                    <div class="stat-figure text-info">
                        <i class="fa-sharp fa-solid fa-umbrella-beach text-2xl"></i>
                    </div>
                    <div class="stat-title">PTO Hours</div>
                    <div class="stat-value text-info text-2xl">
                        {{ number_format($totalPtoHours, 2) }}
                    </div>
                    <div class="stat-desc text-xs">
                        Sick: {{ number_format($totalSickHours, 2) }} |
                        Vacation: {{ number_format($totalVacationHours, 2) }}
                    </div>
                </div>

                <div class="stat">
                    <div class="stat-figure text-success">
                        <i class="fa-sharp fa-solid fa-calculator text-2xl"></i>
                    </div>
                    <div class="stat-title">Billable Hours</div>
                    <div class="stat-value text-success text-2xl">
                        {{ number_format($totalBillableHours, 2) }}
                    </div>
                    <div class="stat-desc text-xs">Work + PTO hours</div>
                </div>
            </div>
            @endif

            <!-- Time Cards Table (Paginated) -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body p-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="card-title text-lg">
                            <i class="fa-sharp fa-solid fa-list-check mr-2 text-primary"></i>
                            Time Card Records
                        </h3>
                        <div class="text-sm text-gray-500">
                            Showing {{ $timeCards->firstItem() ?? 0 }}-{{ $timeCards->lastItem() ?? 0 }} of {{ $timeCards->total() }} time cards
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="table table-zebra table-sm">
                            <thead class="bg-base-200 text-xs">
                                <tr>
                                    <th class="text-primary">Date</th>
                                    <th class="text-primary">User</th>
                                    <th class="text-primary text-center">Work</th>
                                    <th class="text-primary text-center">Break</th>
                                    <th class="text-primary text-center">PTO</th>
                                    <th class="text-primary text-center">Billable</th>
                                    <th class="text-primary text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($timeCards as $timeCard)
                                <tr class="hover">
                                    <td class="font-medium">
                                        {{ $timeCard->date->format('D, M d') }}
                                        @if($timeCard->date->isToday())
                                            <span class="badge badge-xs badge-primary ml-1">Today</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <div class="avatar avatar-placeholder">
                                                <div class="bg-neutral text-neutral-content rounded-full w-6">
                                                    <span class="text-xs">{{ substr($timeCard->user->name, 0, 1) }}</span>
                                                </div>
                                            </div>
                                            <span class="text-sm">{{ $timeCard->user->name }}</span>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="text-secondary text-sm">
                                            {{ number_format($timeCard->getTotalHoursDecimalAttribute(), 2) }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span class="text-accent text-sm">
                                            {{ number_format($timeCard->getTotalBreakHoursDecimalAttribute(), 2) }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        @php
                                            $totalPto = $timeCard->getTotalPtoDecimalAttribute();
                                            $hasSickTime = $timeCard->getTotalSickTimeDecimalAttribute() > 0;
                                            $hasVacationTime = $timeCard->getTotalVacationTimeDecimalAttribute() > 0;
                                        @endphp
                                        <span class="text-info text-sm">
                                            {{ number_format($totalPto, 2) }}
                                        </span>
                                        @if($hasSickTime || $hasVacationTime)
                                            <div class="flex justify-center gap-1 mt-1">
                                                @if($hasSickTime)
                                                    <span class="badge badge-xs badge-accent" title="Includes sick time">S</span>
                                                @endif
                                                @if($hasVacationTime)
                                                    <span class="badge badge-xs badge-primary" title="Includes vacation time">V</span>
                                                @endif
                                            </div>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        <span class="text-success text-sm font-medium">
                                            {{ number_format($timeCard->getTotalBillableHoursDecimalAttribute(), 2) }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <div class="flex justify-center space-x-1">
                                            <a href="{{ route('timecards.show', $timeCard->id) }}" class="btn btn-xs btn-circle btn-info" title="View Details">
                                                <i class="fa-sharp fa-solid fa-eye"></i>
                                            </a>

                                            @if(Auth::user()->hasPermission('edit_timecards') ||
                                                (Auth::user()->hasPermission('edit_own_timecards') && $timeCard->user_id === Auth::id()))
                                            <a href="{{ route('timecards.edit', $timeCard->id) }}" class="btn btn-xs btn-circle btn-warning" title="Edit Time Card">
                                                <i class="fa-sharp fa-solid fa-edit"></i>
                                            </a>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-6">
                                        <div class="flex flex-col items-center justify-center text-gray-500">
                                            <i class="fa-sharp fa-solid fa-calendar-xmark text-3xl mb-2"></i>
                                            <p class="text-base font-medium">No time cards found for the selected criteria.</p>
                                            <p class="text-xs">Try adjusting your filters or create a new time card.</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($timeCards->hasPages())
                        <x-pagination :paginator="$timeCards" :pagination="$pagination" />
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
