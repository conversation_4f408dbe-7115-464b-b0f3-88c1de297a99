<x-app-layout
    page-title="Time Clock"
    page-icon="fa-sharp fa-clock"
    :primary-buttons="[
        [
            'type' => 'view',
            'route' => route('timecards.index'),
            'text' => 'View Time Cards',
            'icon' => 'fa-sharp fa-calendar-days'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Time Clock', 'icon' => 'fa-clock']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
            <!-- Current Time and Status Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Current Time and Date -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-clock text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Current Time</h4>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="text-center space-y-3">
                            <div class="text-4xl font-mono font-bold text-primary" id="current-time">
                                {{ now()->setTimezone($timezone)->format('g:i:s A') }}
                            </div>
                            <div class="text-xl text-base-content">
                                {{ now()->setTimezone($timezone)->format('l, F j, Y') }}
                            </div>
                            <div class="flex items-center justify-center gap-2 text-sm text-base-content/70">
                                <i class="fa-sharp fa-globe-americas"></i>
                                <span>{{ $timezone }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Status -->
                <div class="card bg-base-100 shadow-md" id="current-status-card">
                    <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-info text-info-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-user-check text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Current Status</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-6">
                        <!-- Status Display -->
                        <div class="text-center">
                            @if($currentStatus == 'clock_in')
                                <div class="flex items-center justify-center gap-3 mb-4">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-success text-success-content w-12 rounded-xl">
                                            <i class="fa-sharp fa-user-clock text-lg"></i>
                                        </div>
                                    </div>
                                    <div class="text-left">
                                        <div class="text-xl font-semibold text-success">Clocked In</div>
                                        <div class="text-sm text-base-content/70">Currently working</div>
                                    </div>
                                </div>
                            @elseif($currentStatus == 'break')
                                <div class="flex items-center justify-center gap-3 mb-4">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-warning text-warning-content w-12 rounded-xl">
                                            <i class="fa-sharp fa-mug-hot text-lg"></i>
                                        </div>
                                    </div>
                                    <div class="text-left">
                                        <div class="text-xl font-semibold text-warning">On Break</div>
                                        <div class="text-sm text-base-content/70">Taking a break</div>
                                    </div>
                                </div>
                            @else
                                <div class="flex items-center justify-center gap-3 mb-4">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-error text-error-content w-12 rounded-xl">
                                            <i class="fa-sharp fa-user-xmark text-lg"></i>
                                        </div>
                                    </div>
                                    <div class="text-left">
                                        <div class="text-xl font-semibold text-error">Clocked Out</div>
                                        <div class="text-sm text-base-content/70">Not currently working</div>
                                    </div>
                                </div>
                            @endif

                            <!-- User identification -->
                            <div class="flex items-center justify-center gap-2 text-sm text-base-content/70">
                                <i class="fa-sharp fa-user"></i>
                                <span>Logged in as <span class="font-medium text-base-content">{{ Auth::user()->name }}</span></span>
                            </div>
                        </div>

                        <!-- Today's Stats -->
                        <div class="stats stats-vertical sm:stats-horizontal shadow-sm w-full">
                            <div class="stat bg-primary ">
                                <div class="stat-title text-primary-content">Hours Today</div>
                                <div class="stat-value text-primary-content text-2xl" id="total-hours">{{ $formattedTotalHours }}</div>
                                <div class="stat-desc text-primary-content">Working Time</div>
                            </div>
                            <div class="stat bg-warning">
                                <div class="stat-title text-warning-content">Break Time</div>
                                <div class="stat-value  text-warning-content text-2xl" id="break-hours">{{ $formattedTotalBreakHours }}</div>
                                <div class="stat-desc text-warning-content">Time on Break</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-accent text-accent-content w-8 rounded-lg">
                                <i class="fa-sharp fa-bolt text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Time Clock Actions</h4>
                    </div>
                </div>
                <div class="p-6 space-y-6">
                    <form action="{{ route('time-clock.action') }}" method="POST" class="space-y-6">
                        @csrf

                        <!-- Notes Field -->
                        <div>
                            <label class="label" for="notes">
                                <span class="label-text">
                                    <i class="fa-sharp fa-note-sticky text-primary mr-2"></i>
                                    Notes (optional)
                                </span>
                            </label>
                            <textarea name="notes" id="notes" class="textarea textarea-bordered w-full"
                                      placeholder="Add notes about this punch..."></textarea>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row gap-3 justify-center">
                            @if($currentStatus == 'clock_out')
                                <button type="button" name="action" value="clock_in" id="clock-in-btn"
                                        class="btn btn-success btn-lg gap-2 shadow-lg hover:shadow-xl transition-all duration-200 flex-1 sm:flex-none time-clock-action"
                                        data-action="clock_in">
                                    <span class="btn-content">
                                        <i class="fa-sharp fa-user-clock"></i>
                                        Clock In
                                    </span>
                                    <span class="loading loading-spinner loading-sm hidden"></span>
                                </button>
                            @elseif($currentStatus == 'clock_in')
                                <button type="button" name="action" value="break_out" id="break-out-btn"
                                        class="btn btn-warning btn-lg gap-2 shadow-lg hover:shadow-xl transition-all duration-200 flex-1 sm:flex-none time-clock-action"
                                        data-action="break_out">
                                    <span class="btn-content">
                                        <i class="fa-sharp fa-mug-hot"></i>
                                        Start Break
                                    </span>
                                    <span class="loading loading-spinner loading-sm hidden"></span>
                                </button>
                                <button type="button" name="action" value="clock_out" id="clock-out-btn"
                                        class="btn btn-error btn-lg gap-2 shadow-lg hover:shadow-xl transition-all duration-200 flex-1 sm:flex-none time-clock-action"
                                        data-action="clock_out">
                                    <span class="btn-content">
                                        <i class="fa-sharp fa-user-xmark"></i>
                                        Clock Out
                                    </span>
                                    <span class="loading loading-spinner loading-sm hidden"></span>
                                </button>
                            @elseif($currentStatus == 'break')
                                <button type="button" name="action" value="clock_in" id="end-break-btn"
                                        class="btn btn-success btn-lg gap-2 shadow-lg hover:shadow-xl transition-all duration-200 flex-1 sm:flex-none time-clock-action"
                                        data-action="clock_in">
                                    <span class="btn-content">
                                        <i class="fa-sharp fa-user-clock"></i>
                                        End Break
                                    </span>
                                    <span class="loading loading-spinner loading-sm hidden"></span>
                                </button>
                                <button type="button" name="action" value="clock_out" id="clock-out-from-break-btn"
                                        class="btn btn-error btn-lg gap-2 shadow-lg hover:shadow-xl transition-all duration-200 flex-1 sm:flex-none time-clock-action"
                                        data-action="clock_out">
                                    <span class="btn-content">
                                        <i class="fa-sharp fa-user-xmark"></i>
                                        Clock Out
                                    </span>
                                    <span class="loading loading-spinner loading-sm hidden"></span>
                                </button>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
            <!-- Regulations & Policies -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-scale-balanced text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Colorado State Regulations & Company Policies</h4>
                    </div>
                </div>
                <div class="p-6">
                    <div class="prose prose-sm max-w-none">
                        <ul class="space-y-3">
                            <li class="flex items-start gap-3">
                                <i class="fa-sharp fa-clock text-primary mt-1 flex-shrink-0"></i>
                                <span>Clock in when you're arrived and settled in, just before you start to work. Clock out at the end of the day just before you leave the building.</span>
                            </li>
                            <li class="flex items-start gap-3">
                                <i class="fa-sharp fa-money-bill text-success mt-1 flex-shrink-0"></i>
                                <span>You are entitled to 1.5x pay for all hours worked over 40 in a work week Sunday through Saturday.</span>
                            </li>
                            <li class="flex items-start gap-3">
                                <i class="fa-sharp fa-mug-hot text-warning mt-1 flex-shrink-0"></i>
                                <div>
                                    <span>You are required to take one uninterrupted 30 minute break per day for all shifts over 5 hours.</span>
                                </div>
                            </li>
                            <li class="flex items-start gap-3">
                                <i class="fa-sharp fa-coffee text-info mt-1 flex-shrink-0"></i>
                                <div>
                                    <span class="block mb-2">You are entitled to one paid ten minute break for every 4 hours worked:</span>
                                    <ul class="space-y-2 ml-4 text-sm text-base-content/80">
                                        <li>• Usually this means 20 minutes of paid-break time total across your 7 hour shift. No need to clock out.</li>
                                        <li>• Use this time for smoke breaks, bathrooms, drinking water, looking at your phone, etc.</li>
                                        <li>• E-Tech Recyclers permits you to take breaks at your discretion. You may take additional breaks beyond the state mandated amount if needed to stay hydrated, healthy, and safe.</li>
                                        <li>• Avoid individual breaks over 10 minutes long at a time, please.</li>
                                    </ul>
                                </div>
                            </li>
                            @if($autoClockOutEnabled)
                            <li class="flex items-start gap-3">
                                <i class="fa-sharp fa-clock-rotate-left text-secondary mt-1 flex-shrink-0"></i>
                                <div>
                                    <span><strong>Automatic Clock-Out:</strong> If you forget to clock out, the system will automatically clock you out at {{ \Carbon\Carbon::parse($autoClockOutTime)->format('g:i A') }}.</span>
                                    <span class="block text-sm text-base-content/70 mt-1">Always remember to clock out manually when you finish work to ensure accurate time tracking.</span>
                                </div>
                            </li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>
            <!-- Weekly Summary -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-calendar-week text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Weekly Summary</h4>
                    </div>
                </div>
                <div class="p-6 space-y-6">
                    <!-- Primary Stats -->
                    <div class="stats stats-vertical sm:stats-horizontal shadow-sm w-full">
                        <div class="stat">
                            <div class="stat-title">Billable Hours</div>
                            <div class="stat-value text-success text-2xl" id="weekly-billable-hours">
                                @if(isset($weeklyBillableHours))
                                    {{ number_format($weeklyBillableHours, 2) }}
                                @else
                                    {{ number_format((($weeklyHours ?? 0) - ($weeklyBreakHours ?? 0) + ($weeklyPtoTime ?? 0)), 2) }}
                                @endif
                            </div>
                            <div class="stat-desc">Work + PTO</div>
                        </div>

                        <div class="stat">
                            <div class="stat-title">Overtime</div>
                            <div class="stat-value text-accent text-2xl" id="weekly-overtime-hours">
                                @if(isset($weeklyOvertimeHours))
                                    {{ number_format($weeklyOvertimeHours, 2) }}
                                @else
                                    {{ number_format(max(0, (($weeklyHours ?? 0) - ($weeklyBreakHours ?? 0) - 40)), 2) }}
                                @endif
                            </div>
                            <div class="stat-desc">Hours > 40/week</div>
                        </div>

                        <div class="stat">
                            <div class="stat-title">PTO Hours</div>
                            <div class="stat-value text-info text-2xl" id="weekly-pto-hours">
                                @if(isset($weeklyPtoTime))
                                    {{ number_format($weeklyPtoTime, 2) }}
                                @else
                                    {{ number_format(($weeklySickTime ?? 0) + ($weeklyVacationTime ?? 0), 2) }}
                                @endif
                            </div>
                            <div class="stat-desc">Sick + Vacation</div>
                        </div>
                    </div>

                    <!-- Detailed Stats -->
                    <div class="stats stats-vertical sm:stats-horizontal shadow-sm w-full">
                        <div class="stat">
                            <div class="stat-title">Work Hours</div>
                            <div class="stat-value text-primary text-xl" id="weekly-total-hours">
                                {{ $formattedWeeklyHours ?? '00:00:00' }}
                            </div>
                            <div class="stat-desc">Total Working Time</div>
                        </div>

                        <div class="stat">
                            <div class="stat-title">Break Time</div>
                            <div class="stat-value text-secondary text-xl" id="weekly-break-hours">
                                {{ $formattedWeeklyBreakHours ?? '00:00:00' }}
                            </div>
                            <div class="stat-desc">Total Break Time</div>
                        </div>

                        <div class="stat">
                            <div class="stat-title">Sick Time</div>
                            <div class="stat-value text-accent text-xl" id="weekly-sick-hours">
                                {{ $formattedWeeklySickTime ?? '00:00:00' }}
                            </div>
                            <div class="stat-desc">{{ number_format($weeklySickTime ?? 0, 2) }} hrs</div>
                        </div>

                        <div class="stat">
                            <div class="stat-title">Vacation</div>
                            <div class="stat-value text-info text-xl" id="weekly-vacation-hours">
                                {{ $formattedWeeklyVacationTime ?? '00:00:00' }}
                            </div>
                            <div class="stat-desc">{{ number_format($weeklyVacationTime ?? 0, 2) }} hrs</div>
                        </div>
                    </div>

                    <!-- Progress Bar for Weekly Hours -->
                    <div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-base-content">Weekly Progress</span>
                            <span class="text-sm font-medium text-base-content/70">
                                @if(isset($weeklyRegularHours))
                                    {{ number_format($weeklyRegularHours, 2) }}
                                @else
                                    {{ number_format(min(40, ($weeklyHours ?? 0) - ($weeklyBreakHours ?? 0)), 2) }}
                                @endif
                                /40 hours
                            </span>
                        </div>
                        <div class="w-full bg-base-200 rounded-full h-3">
                            @php
                                $regularHours = isset($weeklyRegularHours) ? $weeklyRegularHours : min(40, ($weeklyHours ?? 0) - ($weeklyBreakHours ?? 0));
                                $progressPercent = min(100, ($regularHours / 40) * 100);
                                $progressColor = $progressPercent < 50 ? 'bg-info' : ($progressPercent < 85 ? 'bg-success' : 'bg-warning');
                            @endphp
                            <div class="{{ $progressColor }} h-3 rounded-full transition-all duration-300" style="width: {{ $progressPercent }}%"></div>
                        </div>
                    </div>

                    <!-- Action Button -->
                    <div class="flex justify-center">
                        <a href="{{ route('timecards.index') }}" class="btn btn-primary btn-lg gap-2 shadow-lg">
                            <i class="fa-sharp fa-calendar-days"></i>
                            View All My Time Cards
                        </a>
                    </div>
                </div>
            </div>

        <!-- Today's Punches -->
        <div class="card bg-base-100 shadow-lg border border-base-200 rounded-2xl overflow-hidden">
            <div class="card-body p-5">
                <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mr-3">
                            <i class="fa-sharp fa-list-check text-content-base"></i>
                        </div>
                        <h2 class="card-title font-bold m-0">Today's Punches</h2>
                    </div>
                    <div class="badge badge-primary px-3 py-3 font-medium">
                        {{ $punches->count() }} {{ Str::plural('punch', $punches->count()) }}
                    </div>
                </div>

                <div id="punches-container" class="overflow-y-auto max-h-[500px] pr-1.5 -mx-1 px-1">
                    @include('timecards.partials.punches-list', ['punches' => $punches])
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>


        document.addEventListener('DOMContentLoaded', function() {

            

            // Mobile and PWA detection
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isPWA = window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone;

            // Form submission state management
            let isSubmitting = false;
            let lastSubmissionTime = 0;
            const SUBMISSION_COOLDOWN = 2000; // 2 seconds

            // Network status tracking
            let isOnline = navigator.onLine;
            let pendingRequests = [];

            // Update current time every second
            function updateCurrentTime() {
                const now = new Date();
                const options = {
                    hour: 'numeric',
                    minute: 'numeric',
                    second: 'numeric',
                    hour12: true,
                    timeZone: '{{ $timezone }}'
                };
                document.getElementById('current-time').textContent = now.toLocaleTimeString([], options);
            }

            setInterval(updateCurrentTime, 1000);
            updateCurrentTime();

            // Initialize values for all counters
            let totalSeconds = timeStringToSeconds('{{ $formattedTotalHours }}');
            let breakSeconds = timeStringToSeconds('{{ $formattedTotalBreakHours }}');

            // Weekly totals now include today's real-time hours from the server
            let weeklyTotalSeconds = timeStringToSeconds('{{ $formattedWeeklyHours }}');
            let weeklyBreakSeconds = timeStringToSeconds('{{ $formattedWeeklyBreakHours }}');

            // Track the current status
            let currentStatus = '{{ $currentStatus }}';

            // Variables to track intervals
            let totalHoursInterval = null;
            let breakHoursInterval = null;

            // Function to start the appropriate counter based on status
            function startCounters() {
                // Clear any existing intervals first
                stopCounters();

                if (currentStatus === 'clock_in') {
                    // Update total hours every second when clocked in
                    totalHoursInterval = setInterval(function() {
                        totalSeconds++;
                        weeklyTotalSeconds++;
                        document.getElementById('total-hours').textContent = secondsToTimeString(totalSeconds);
                        document.getElementById('weekly-total-hours').textContent = secondsToTimeString(weeklyTotalSeconds);
                    }, 1000);
                } else if (currentStatus === 'break') {
                    // Update break time every second when on break
                    breakHoursInterval = setInterval(function() {
                        breakSeconds++;
                        weeklyBreakSeconds++;
                        document.getElementById('break-hours').textContent = secondsToTimeString(breakSeconds);
                        document.getElementById('weekly-break-hours').textContent = secondsToTimeString(weeklyBreakSeconds);
                    }, 1000);
                }
            }

            // Function to stop all counters
            function stopCounters() {
                if (totalHoursInterval) {
                    clearInterval(totalHoursInterval);
                    totalHoursInterval = null;
                }
                if (breakHoursInterval) {
                    clearInterval(breakHoursInterval);
                    breakHoursInterval = null;
                }
            }

            // Setup direct button click handlers instead of form submission
            document.querySelectorAll('.time-clock-action').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.getAttribute('data-action');
                    const notes = document.getElementById('notes').value;
                    performAction(action, notes, this);
                });
            });

            // Start the counters initially
            startCounters();

            // Set up a periodic refresh every 5 minutes even when the page is visible
            // This ensures the counters don't drift too far from the server time
            const REFRESH_INTERVAL = 5 * 60 * 1000; // 5 minutes in milliseconds
            setInterval(refreshTimeClockData, REFRESH_INTERVAL);

            // Network status monitoring
            window.addEventListener('online', handleOnline);
            window.addEventListener('offline', handleOffline);

            // Helper function to convert time string (HH:MM:SS) to seconds
            function timeStringToSeconds(timeString) {
                const [hours, minutes, seconds] = timeString.split(':').map(Number);
                return hours * 3600 + minutes * 60 + seconds;
            }

            // Helper function to convert seconds to time string (HH:MM:SS)
            function secondsToTimeString(totalSeconds) {
                const hours = Math.floor(totalSeconds / 3600);
                const minutes = Math.floor((totalSeconds % 3600) / 60);
                const seconds = totalSeconds % 60;

                return [
                    hours.toString().padStart(2, '0'),
                    minutes.toString().padStart(2, '0'),
                    seconds.toString().padStart(2, '0')
                ].join(':');
            }

            // Function to update the status display section
            function updateStatusDisplay(status) {
                // Find the status display section more precisely
                const statusCard = document.getElementById('current-status-card');
                if (!statusCard) return;
                
                const statusContainer = statusCard.querySelector('.p-6.space-y-6 > .text-center');
                if (!statusContainer) return;

                let statusHtml = '';

                if (status === 'clock_in') {
                    statusHtml = `
                        <div class="flex items-center justify-center gap-3 mb-4">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-12 rounded-xl">
                                    <i class="fa-sharp fa-user-clock text-lg"></i>
                                </div>
                            </div>
                            <div class="text-left">
                                <div class="text-xl font-semibold text-success">Clocked In</div>
                                <div class="text-sm text-base-content/70">Currently working</div>
                            </div>
                        </div>
                    `;
                } else if (status === 'break') {
                    statusHtml = `
                        <div class="flex items-center justify-center gap-3 mb-4">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-warning text-warning-content w-12 rounded-xl">
                                    <i class="fa-sharp fa-mug-hot text-lg"></i>
                                </div>
                            </div>
                            <div class="text-left">
                                <div class="text-xl font-semibold text-warning">On Break</div>
                                <div class="text-sm text-base-content/70">Taking a break</div>
                            </div>
                        </div>
                    `;
                } else {
                    statusHtml = `
                        <div class="flex items-center justify-center gap-3 mb-4">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-error text-error-content w-12 rounded-xl">
                                    <i class="fa-sharp fa-user-xmark text-lg"></i>
                                </div>
                            </div>
                            <div class="text-left">
                                <div class="text-xl font-semibold text-error">Clocked Out</div>
                                <div class="text-sm text-base-content/70">Not currently working</div>
                            </div>
                        </div>
                    `;
                }
                
                // Add the user identification HTML
                statusHtml += `
                    <div class="flex items-center justify-center gap-2 text-sm text-base-content/70">
                        <i class="fa-sharp fa-user"></i>
                        <span>Logged in as <span class="font-medium text-base-content">{{ Auth::user()->name }}</span></span>
                    </div>
                `;

                // Replace the entire content of the text-center div
                statusContainer.innerHTML = statusHtml;
            }

            // Function to update the action buttons based on current status
            function updateActionButtons(status) {
                const buttonContainer = document.querySelector('.flex.flex-col.sm\\:flex-row.gap-3.justify-center');
                if (!buttonContainer) return;

                let buttonsHtml = '';

                if (status === 'clock_out') {
                    buttonsHtml = `
                        <button type="button" name="action" value="clock_in" id="clock-in-btn"
                                class="btn btn-success btn-lg gap-2 shadow-lg hover:shadow-xl transition-all duration-200 flex-1 sm:flex-none time-clock-action"
                                data-action="clock_in">
                            <span class="btn-content">
                                <i class="fa-sharp fa-user-clock"></i>
                                Clock In
                            </span>
                            <span class="loading loading-spinner loading-sm hidden"></span>
                        </button>
                    `;
                } else if (status === 'clock_in') {
                    buttonsHtml = `
                        <button type="button" name="action" value="break_out" id="break-out-btn"
                                class="btn btn-warning btn-lg gap-2 shadow-lg hover:shadow-xl transition-all duration-200 flex-1 sm:flex-none time-clock-action"
                                data-action="break_out">
                            <span class="btn-content">
                                <i class="fa-sharp fa-mug-hot"></i>
                                Start Break
                            </span>
                            <span class="loading loading-spinner loading-sm hidden"></span>
                        </button>
                        <button type="button" name="action" value="clock_out" id="clock-out-btn"
                                class="btn btn-error btn-lg gap-2 shadow-lg hover:shadow-xl transition-all duration-200 flex-1 sm:flex-none time-clock-action"
                                data-action="clock_out">
                            <span class="btn-content">
                                <i class="fa-sharp fa-user-xmark"></i>
                                Clock Out
                            </span>
                            <span class="loading loading-spinner loading-sm hidden"></span>
                        </button>
                    `;
                } else if (status === 'break') {
                    buttonsHtml = `
                        <button type="button" name="action" value="clock_in" id="end-break-btn"
                                class="btn btn-success btn-lg gap-2 shadow-lg hover:shadow-xl transition-all duration-200 flex-1 sm:flex-none time-clock-action"
                                data-action="clock_in">
                            <span class="btn-content">
                                <i class="fa-sharp fa-user-clock"></i>
                                End Break
                            </span>
                            <span class="loading loading-spinner loading-sm hidden"></span>
                        </button>
                        <button type="button" name="action" value="clock_out" id="clock-out-from-break-btn"
                                class="btn btn-error btn-lg gap-2 shadow-lg hover:shadow-xl transition-all duration-200 flex-1 sm:flex-none time-clock-action"
                                data-action="clock_out">
                            <span class="btn-content">
                                <i class="fa-sharp fa-user-xmark"></i>
                                Clock Out
                            </span>
                            <span class="loading loading-spinner loading-sm hidden"></span>
                        </button>
                    `;
                }

                buttonContainer.innerHTML = buttonsHtml;

                // Re-attach event listeners to the new buttons
                buttonContainer.querySelectorAll('.time-clock-action').forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const action = this.getAttribute('data-action');
                        const notes = document.getElementById('notes').value;
                        performAction(action, notes, this);
                    });
                });
            }

            // Function to update the punches list
            function updatePunchesList(punchesHtml) {
                const punchesContainer = document.getElementById('punches-container');
                if (punchesContainer && punchesHtml) {
                    punchesContainer.innerHTML = punchesHtml;
                    
                    // Update the punch count badge
                    const punchCountBadge = document.querySelector('.badge.badge-primary');
                    if (punchCountBadge) {
                        // Count the punches from the updated HTML
                        const punchDivs = punchesContainer.querySelectorAll('.p-4.hover\\:bg-base-200\\/50');
                        const count = punchDivs.length;
                        punchCountBadge.textContent = `${count} ${count === 1 ? 'punch' : 'punches'}`;
                    }
                }
            }

            // Direct action handler
            function performAction(action, notes, button) {
                const now = Date.now();
                const timeSinceLastSubmission = now - lastSubmissionTime;

                // Prevent rapid double-taps/clicks
                if (isSubmitting || timeSinceLastSubmission < SUBMISSION_COOLDOWN) {
                    console.log('Submission blocked - too soon after last submission');
                    showNotification('please wait before trying again', 'warning');
                    return;
                }
                
                console.log('Performing time clock action:', {
                    action: action,
                    currentStatus: currentStatus,
                    notes: notes
                });

                // Create form data
                const formData = new FormData();
                formData.append('action', action);
                formData.append('notes', notes || '');
                formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

                // Set submission state
                isSubmitting = true;
                lastSubmissionTime = now;

                // Update button state
                setButtonLoading(button, true);

                // Check network status
                if (!isOnline) {
                    handleOfflineSubmission(formData, button);
                    return;
                }

                // Submit the action
                submitTimeClockAction(formData, button);
            }

            // Button state management
            function setButtonLoading(button, loading) {
                if (!button) return;

                const content = button.querySelector('.btn-content');
                const spinner = button.querySelector('.loading');

                if (loading) {
                    button.disabled = true;
                    if (content) content.classList.add('hidden');
                    if (spinner) spinner.classList.remove('hidden');
                } else {
                    button.disabled = false;
                    if (content) content.classList.remove('hidden');
                    if (spinner) spinner.classList.add('hidden');
                }
            }

            // Network status handlers
            function handleOnline() {
                isOnline = true;
                console.log('Network connection restored');
                showNotification('connection restored', 'success');

                // Process any pending requests
                processPendingRequests();
            }

            function handleOffline() {
                isOnline = false;
                console.log('Network connection lost');
                showNotification('no internet connection. Actions will be queued', 'warning');
            }

            // Offline submission handling
            function handleOfflineSubmission(formData, button) {
                const action = formData.get('action');
                const notes = formData.get('notes');

                // Queue the request for when connection is restored
                pendingRequests.push({
                    formData: formData,
                    button: button,
                    timestamp: Date.now(),
                    action: action,
                    notes: notes
                });

                showNotification(`${action.replace('_', ' ')} queued for when connection is restored`, 'info');

                // Reset button state
                setButtonLoading(button, false);
                isSubmitting = false;
            }

            // Process pending requests when connection is restored
            function processPendingRequests() {
                if (pendingRequests.length === 0) return;

                console.log(`Processing ${pendingRequests.length} pending requests`);

                const requests = [...pendingRequests];
                pendingRequests = [];

                requests.forEach((request, index) => {
                    setTimeout(() => {
                        submitTimeClockAction(request.formData, request.button);
                    }, index * 500); // Stagger requests by 500ms
                });
            }

            // Enhanced notification system
            function showNotification(message, type = 'info') {
                // Capitalize first letter and ensure proper sentence format
                const formattedMessage = message.charAt(0).toUpperCase() + message.slice(1);

                // Create toast container if it doesn't exist
                let toastContainer = document.getElementById('toast-container');
                if (!toastContainer) {
                    toastContainer = document.createElement('div');
                    toastContainer.id = 'toast-container';
                    toastContainer.className = 'toast toast-top toast-end z-50';
                    document.body.appendChild(toastContainer);
                }

                // Create toast element
                const toast = document.createElement('div');
                const alertClass = type === 'success' ? 'alert-success' :
                                 type === 'error' ? 'alert-error' :
                                 type === 'warning' ? 'alert-warning' : 'alert-info';

                toast.className = `alert ${alertClass} shadow-lg mb-2`;
                toast.innerHTML = `
                    <div class="flex items-center">
                        <i class="fa-sharp fa-${type === 'success' ? 'check-circle' :
                                              type === 'error' ? 'exclamation-circle' :
                                              type === 'warning' ? 'exclamation-triangle' : 'info-circle'} mr-2"></i>
                        <span>${formattedMessage}</span>
                    </div>
                `;

                toastContainer.appendChild(toast);

                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 5000);
            }

            // Submit time clock action with enhanced error handling
            function submitTimeClockAction(formData, button) {
                const action = formData.get('action');

                fetch('{{ route('time-clock.action') }}', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                    },
                    credentials: 'same-origin'
                })
                .then(response => {
                    // Handle 202 status (queued) from service worker
                    if (response.status === 202) {
                        return response.json();
                    }
                    
                    // For error responses, try to parse JSON for error message
                    if (!response.ok) {
                        return response.text().then(text => {
                            try {
                                const errorData = JSON.parse(text);
                                // Throw with the parsed error data
                                throw { 
                                    status: response.status, 
                                    data: errorData,
                                    isJsonError: true
                                };
                            } catch (e) {
                                // Not JSON, throw generic error
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }
                        });
                    }
                    
                    return response.text();
                })
                .then(data => {
                    // Check if response is JSON or HTML
                    try {
                        const jsonData = typeof data === 'string' ? JSON.parse(data) : data;
                        handleJsonResponse(jsonData, action);
                    } catch (e) {
                        // Response is HTML (redirect), handle as success
                        handleHtmlResponse(data, action);
                    }
                })
                .catch(error => {
                    console.error('Time clock action failed:', error);
                    handleSubmissionError(error, action, formData, button);
                })
                .finally(() => {
                    setButtonLoading(button, false);
                    isSubmitting = false;
                });
            }

            // Handle JSON response (if API returns JSON)
            function handleJsonResponse(data, action) {
                if (data.success) {
                    showNotification(data.message || `${action.replace('_', ' ')} successful!`, 'success');

                    // Update the current status immediately
                    if (data.current_status) {
                        currentStatus = data.current_status;
                        updateStatusDisplay(data.current_status);
                        updateActionButtons(data.current_status);
                        // Restart counters with new status
                        startCounters();
                    }

                    // Update the punches list if provided
                    if (data.punches_html) {
                        updatePunchesList(data.punches_html);
                    }

                    // Refresh the page data
                    refreshTimeClockData();
                } else if (data.queued) {
                    // Handle queued actions from service worker
                    showNotification(data.message || 'Action queued for when connection is restored', 'warning');
                } else {
                    // Show detailed error message
                    const errorMessage = data.message || `${action.replace('_', ' ')} failed`;
                    showNotification(errorMessage, 'error');

                    // Log detailed error for debugging
                    console.error('Time clock action failed:', {
                        action: action,
                        message: errorMessage,
                        data: data
                    });
                }
            }

            // Handle HTML response (redirect)
            function handleHtmlResponse(data, action) {
                showNotification(`${action.replace('_', ' ')} successful!`, 'success');
                // Reload the page to get updated state
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }

            // Handle submission errors with retry logic
            function handleSubmissionError(error, action, formData, button) {
                console.error('Submission error:', error);

                // Check if it's a validation error from the server
                if (error.isJsonError && error.data) {
                    // Show the specific error message from the server
                    const errorMessage = error.data.message || `${action.replace('_', ' ')} failed`;
                    showNotification(errorMessage, 'error');
                    return;
                }

                // Check if it's a network error
                if (error.name === 'TypeError' || error.message.includes('Failed to fetch')) {
                    // Network error - queue for retry
                    if (isOnline) {
                        // Still showing as online, but request failed - might be temporary
                        showNotification('connection issue. Retrying...', 'warning');

                        // Retry once after a short delay
                        setTimeout(() => {
                            if (navigator.onLine) {
                                submitTimeClockAction(formData, button);
                            } else {
                                handleOfflineSubmission(formData, button);
                            }
                        }, 2000);
                    } else {
                        handleOfflineSubmission(formData, button);
                    }
                } else {
                    // Server error
                    showNotification(`error: ${error.message}`, 'error');
                }
            }

            // Function to refresh data from the server
            function refreshTimeClockData() {
                // Add a subtle loading indicator
                const totalHoursEl = document.getElementById('total-hours');
                const breakHoursEl = document.getElementById('break-hours');
                const weeklyTotalHoursEl = document.getElementById('weekly-total-hours');
                const weeklyBreakHoursEl = document.getElementById('weekly-break-hours');
                const weeklySickHoursEl = document.getElementById('weekly-sick-hours');
                const weeklyVacationHoursEl = document.getElementById('weekly-vacation-hours');
                const weeklyPtoHoursEl = document.getElementById('weekly-pto-hours');
                const weeklyBillableHoursEl = document.getElementById('weekly-billable-hours');
                const weeklyOvertimeHoursEl = document.getElementById('weekly-overtime-hours');

                // Store original values
                const originalClasses = totalHoursEl.className;

                // Add opacity to indicate loading
                [
                    totalHoursEl,
                    breakHoursEl,
                    weeklyTotalHoursEl,
                    weeklyBreakHoursEl,
                    weeklySickHoursEl,
                    weeklyVacationHoursEl,
                    weeklyPtoHoursEl,
                    weeklyBillableHoursEl,
                    weeklyOvertimeHoursEl
                ].forEach(el => {
                    if (el) el.classList.add('opacity-50');
                });

                fetch('/api/time-clock/current-data', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'same-origin'
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Update the current status
                        currentStatus = data.current_status;

                        // Update the status display and action buttons
                        updateStatusDisplay(data.current_status);
                        updateActionButtons(data.current_status);

                        // Update the counter values
                        totalSeconds = timeStringToSeconds(data.formatted_total_hours);
                        breakSeconds = timeStringToSeconds(data.formatted_total_break_hours);
                        weeklyTotalSeconds = timeStringToSeconds(data.formatted_weekly_hours);
                        weeklyBreakSeconds = timeStringToSeconds(data.formatted_weekly_break_hours);

                        // Update the displayed values
                        totalHoursEl.textContent = data.formatted_total_hours;
                        breakHoursEl.textContent = data.formatted_total_break_hours;
                        weeklyTotalHoursEl.textContent = data.formatted_weekly_hours;
                        weeklyBreakHoursEl.textContent = data.formatted_weekly_break_hours;

                        // Update the new weekly summary elements
                        if (weeklySickHoursEl) weeklySickHoursEl.textContent = data.formatted_weekly_sick_time;
                        if (weeklyVacationHoursEl) weeklyVacationHoursEl.textContent = data.formatted_weekly_vacation_time;
                        if (weeklyPtoHoursEl) weeklyPtoHoursEl.textContent = Number(data.weekly_pto).toFixed(2);
                        if (weeklyBillableHoursEl) weeklyBillableHoursEl.textContent = Number(data.weekly_billable_hours).toFixed(2);
                        if (weeklyOvertimeHoursEl) weeklyOvertimeHoursEl.textContent = Number(data.weekly_overtime_hours).toFixed(2);

                        // Update the progress bar
                        const progressBar = document.querySelector('.bg-info, .bg-success, .bg-warning');
                        if (progressBar) {
                            const progressPercent = Math.min(100, (data.weekly_regular_hours / 40) * 100);
                            progressBar.style.width = progressPercent + '%';

                            // Update the progress color
                            progressBar.classList.remove('bg-info', 'bg-success', 'bg-warning');
                            if (progressPercent < 50) {
                                progressBar.classList.add('bg-info');
                            } else if (progressPercent < 85) {
                                progressBar.classList.add('bg-success');
                            } else {
                                progressBar.classList.add('bg-warning');
                            }

                            // Update the progress text
                            const progressText = document.querySelector('.flex.justify-between.items-center.mb-2 span:last-child');
                            if (progressText) {
                                progressText.textContent = Number(data.weekly_regular_hours).toFixed(2) + '/40 hours';
                            }
                        }

                        // Restart the counters with the updated status
                        startCounters();
                    }
                })
                .catch(error => {
                    console.error('Error refreshing time clock data:', error);
                    // Only show user notification for non-network errors
                    if (!error.message.includes('Failed to fetch')) {
                        showNotification('failed to refresh data', 'warning');
                    }
                })
                .finally(() => {
                    // Remove loading indicator
                    [
                        totalHoursEl,
                        breakHoursEl,
                        weeklyTotalHoursEl,
                        weeklyBreakHoursEl,
                        weeklySickHoursEl,
                        weeklyVacationHoursEl,
                        weeklyPtoHoursEl,
                        weeklyBillableHoursEl,
                        weeklyOvertimeHoursEl
                    ].forEach(el => {
                        if (el) el.classList.remove('opacity-50');
                    });
                });
            }

            // Handle page visibility changes
            document.addEventListener('visibilitychange', function() {
                if (document.visibilityState === 'visible') {
                    // Page is now visible, refresh the data
                    refreshTimeClockData();
                } else {
                    // Page is hidden, stop the counters
                    stopCounters();
                }
            });

            // Also refresh when the page regains focus
            window.addEventListener('focus', refreshTimeClockData);

            // For mobile devices, handle page show events
            window.addEventListener('pageshow', function(event) {
                // If the page is being shown from the bfcache (back-forward cache)
                if (event.persisted) {
                    refreshTimeClockData();
                }
            });

            // Service Worker registration for PWA support
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('Service Worker registered:', registration);

                        // Listen for messages from service worker
                        navigator.serviceWorker.addEventListener('message', event => {
                            if (event.data && event.data.type === 'RETRY_COMPLETED') {
                                showNotification(`${event.data.count} queued actions completed`, 'success');
                                refreshTimeClockData();
                            }
                        });
                    })
                    .catch(error => {
                        console.log('Service Worker registration failed:', error);
                    });

                // Retry failed requests when coming back online
                window.addEventListener('online', () => {
                    if (navigator.serviceWorker.controller) {
                        navigator.serviceWorker.controller.postMessage({
                            type: 'RETRY_FAILED_REQUESTS'
                        });
                    }
                });
            }
        });
    </script>
    @endpush
</x-app-layout>