<x-app-layout
    page-title="Edit Punch"
    page-icon="fa-sharp fa-edit"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('timecards.index'),
            'text' => 'Back to Time Cards'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Time Cards', 'route' => 'timecards.index', 'icon' => 'fa-calendar-days'],
        ['name' => 'Edit Punch', 'icon' => 'fa-sharp fa-edit']
    ]">


    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">


                    <div class="alert alert-info mb-6">
                        <i class="fa-sharp fa-solid fa-info-circle"></i>
                        <span>Editing a punch will create a new punch record that replaces this one. The original punch will be marked as replaced.</span>
                    </div>

                    <form method="POST" action="{{ route('timepunches.update', $punch->id) }}">
                        @csrf
                        @method('PUT')

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Punch Type</span>
                            </label>
                            <select name="type" id="punch-type" class="select select-bordered" required>
                                <option value="clock_in" {{ $punch->type === 'clock_in' ? 'selected' : '' }}>Clock In</option>
                                <option value="clock_out" {{ $punch->type === 'clock_out' ? 'selected' : '' }}>Clock Out</option>
                                <option value="break_in" {{ $punch->type === 'break_in' ? 'selected' : '' }}>Break End</option>
                                <option value="break_out" {{ $punch->type === 'break_out' ? 'selected' : '' }}>Break Start</option>
                                @if(Auth::user()->hasPermission('manage_paid_time_off'))
                                <option value="sick_time" {{ $punch->type === 'sick_time' ? 'selected' : '' }}>Sick Time</option>
                                <option value="vacation_time" {{ $punch->type === 'vacation_time' ? 'selected' : '' }}>Vacation Time</option>
                                @endif
                            </select>
                            @error('type')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <div class="form-control mt-4">
                            <label class="label">
                                <span class="label-text">Punch Time - {{ $timecard->date->format('l, F j, Y') }}</span>
                            </label>
                            <input type="hidden" name="punch_date" value="{{ $timecard->date->format('Y-m-d') }}" />
                            
                            <div class="relative">
                                <input type="time" 
                                       name="punch_time" 
                                       id="time_picker" 
                                       class="input input-bordered w-full"
                                       value="{{ $punch->punch_time->setTimezone($timezone)->format('H:i') }}"
                                       step="1"
                                       required />
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <i class="fa-sharp fa-solid fa-clock text-gray-400"></i>
                                </div>
                            </div>
                            <div class="text-xs text-gray-500 mt-1">
                                <i class="fa-sharp fa-solid fa-info-circle"></i>
                                Time is in 24-hour format. The date is fixed to the time card date.
                            </div>
                            @error('punch_time')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <div class="form-control mt-4">
                            <label class="label">
                                <span class="label-text">Notes</span>
                                <span class="label-text-alt" id="notes-help-text"></span>
                            </label>
                            <textarea name="notes" id="punch-notes" class="textarea textarea-bordered" placeholder="Add notes about this punch">{{ $punch->notes }}</textarea>
                            <div class="text-xs text-gray-500 mt-1" id="notes-hint" style="{{ in_array($punch->type, ['sick_time', 'vacation_time']) ? '' : 'display: none;' }}">
                                <i class="fa-sharp fa-solid fa-info-circle"></i>
                                For PTO entries, enter the hours in HH:MM:SS format (e.g., 8:00:00 for 8 hours).
                            </div>
                            @error('notes')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <div class="mt-6 flex space-x-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa-sharp fa-solid fa-save mr-2"></i>
                                Save Changes
                            </button>

                            <a href="{{ route('timecards.edit', $timecard->id) }}" class="btn btn-ghost">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @push('scripts')
    @vite('resources/js/edit-punch.js')
    @endpush
</x-app-layout>
