@if($punches->count() > 0)
    <div class="space-y-3">
        @foreach($punches as $index => $punch)
            <div class="p-4 hover:bg-base-200/50 transition-all duration-200 border border-base-300/50 rounded-xl {{ $loop->even ? 'bg-base-100' : 'bg-base-200/30' }}">
                <div class="flex items-center gap-4">
                    <div class="flex-none">
                        <div class="avatar avatar-placeholder">
                            <div class="{{ $punch->type_color }} text-white w-12 rounded-xl shadow-sm">
                                <i class="{{ $punch->type_icon }} text-lg"></i>
                            </div>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex justify-between items-start gap-3">
                            <div>
                                <div class="font-semibold text-base text-base-content">{{ $punch->type_name }}</div>
                                @if($punch->notes)
                                    <p class="text-sm text-base-content/70 mt-1 line-clamp-2">{{ $punch->notes }}</p>
                                @endif
                                @if($punch->original_punch_id)
                                    <div class="mt-2">
                                        <span class="badge badge-sm badge-error gap-1">
                                            <i class="fa-sharp fa-pen-to-square text-xs"></i>
                                            Edited
                                        </span>
                                    </div>
                                @endif
                            </div>
                            <div class="text-right flex-shrink-0">
                                <time class="font-mono text-lg font-bold text-primary">
                                    {{ $punch->punch_time->setTimezone($timezone)->format('g:i:s A') }}
                                </time>
                                <div class="text-xs text-base-content/60 mt-1">
                                    {{ $punch->punch_time->setTimezone($timezone)->format('M j') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
@else
    <div class="text-center text-base-content/70 py-12">
        <div class="flex flex-col items-center gap-4">
            <div class="avatar avatar-placeholder">
                <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                    <i class="fa-sharp fa-clock text-2xl"></i>
                </div>
            </div>
            <div class="text-center">
                <h3 class="text-lg font-medium text-base-content/80">No punches recorded today</h3>
                <p class="text-sm text-base-content/60 mt-1">Use the buttons above to clock in/out or take breaks</p>
            </div>
        </div>
    </div>
@endif
