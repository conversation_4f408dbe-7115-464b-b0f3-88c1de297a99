<x-app-layout
    page-title="Edit Time Card: {{ $timecard->user->name }} - {{ $timecard->date->format('M j, Y') }}"
    page-icon="fa-sharp fa-edit"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('timecards.index'),
            'text' => 'Back to Time Cards'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'View Details',
            'route' => route('timecards.show', $timecard->id),
            'icon' => 'fa-sharp fa-eye',
            'class' => 'btn btn-info btn-sm gap-2'
        ],
        [
            'name' => 'Time Clock',
            'route' => route('time-clock'),
            'icon' => 'fa-sharp fa-clock',
            'class' => 'btn btn-secondary btn-sm gap-2'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Time Cards', 'route' => 'timecards.index', 'icon' => 'fa-calendar-days'],
        ['name' => 'Edit: ' . $timecard->user->name . ' - ' . $timecard->date->format('M j'), 'icon' => 'fa-edit']
    ]">

    <div class="py-6 lg:py-8">


    <div class="py-4">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Time Card Header -->
            <div class="card bg-base-100 shadow-sm mb-4">
                <div class="card-body p-4">
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <div>
                            <h2 class="card-title text-xl mb-1">
                                <i class="fa-sharp fa-solid fa-calendar-day text-primary mr-2"></i>
                                {{ $timecard->date->format('l, F j, Y') }}
                            </h2>
                            <p class="text-base flex items-center">
                                <i class="fa-sharp fa-solid fa-user text-secondary mr-2"></i>
                                {{ $timecard->user->name }}
                            </p>
                        </div>

                        <div class="flex space-x-2 mt-3 md:mt-0">
                            <a href="{{ route('timecards.show', $timecard->id) }}" class="btn btn-sm btn-info">
                                <i class="fa-sharp fa-solid fa-eye mr-1"></i>
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Time Card Stats -->
            <div class="stats shadow-sm w-full mb-4 text-sm bg-base-100">
                <div class="stat">
                    <div class="stat-figure text-primary">
                        <i class="fa-sharp fa-solid fa-business-time text-2xl"></i>
                    </div>
                    <div class="stat-title text-xs">Work Hours</div>
                    <div class="stat-value text-primary text-xl" id="total-hours">{{ $timecard->total_hours }}</div>
                </div>

                <div class="stat">
                    <div class="stat-figure text-secondary">
                        <i class="fa-sharp fa-solid fa-mug-hot text-2xl"></i>
                    </div>
                    <div class="stat-title text-xs">Break Hours</div>
                    <div class="stat-value text-secondary text-xl" id="break-hours">{{ $timecard->total_break_hours }}</div>
                </div>

                <div class="stat">
                    <div class="stat-figure text-accent">
                        <i class="fa-sharp fa-solid fa-head-side-mask text-2xl"></i>
                    </div>
                    <div class="stat-title text-xs">Sick Time</div>
                    <div class="stat-value text-accent text-xl" id="sick-hours">{{ $timecard->total_sick_time }}</div>
                </div>

                <div class="stat">
                    <div class="stat-figure text-primary">
                        <i class="fa-sharp fa-solid fa-umbrella-beach text-2xl"></i>
                    </div>
                    <div class="stat-title text-xs">Vacation Time</div>
                    <div class="stat-value text-primary text-xl" id="vacation-hours">{{ $timecard->total_vacation_time }}</div>
                </div>

                <div class="stat">
                    <div class="stat-figure text-success">
                        <i class="fa-sharp fa-solid fa-calculator text-2xl"></i>
                    </div>
                    <div class="stat-title text-xs">Billable Hours</div>
                    <div class="stat-value text-success text-xl" id="billable-hours">{{ number_format($timecard->total_billable_hours_decimal, 2) }}</div>
                    <div class="stat-desc text-xs">Work + PTO</div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <!-- Left Column: Time Card Notes -->
                <div class="card bg-base-100 shadow-sm">
                    <div class="card-body p-4">
                        <h3 class="card-title text-base">
                            <i class="fa-sharp fa-solid fa-note-sticky text-primary mr-2"></i>
                            Time Card Notes
                        </h3>

                        <form method="POST" action="{{ route('timecards.update', $timecard->id) }}" class="mt-3">
                            @csrf
                            @method('PUT')

                            <div class="form-control">
                                <textarea name="notes" class="textarea textarea-bordered h-28 text-sm" placeholder="Add notes about this time card">{{ $timecard->notes }}</textarea>
                            </div>

                            <div class="mt-3">
                                <button type="submit" class="btn btn-sm btn-primary w-full">
                                    <i class="fa-sharp fa-solid fa-save mr-1"></i>
                                    Save Notes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Middle Column: Add New Punch -->
                <div class="card bg-base-100 shadow-sm">
                    <div class="card-body p-4">
                        <h3 class="card-title text-base">
                            <i class="fa-sharp fa-solid fa-plus-circle text-primary mr-2"></i>
                            Add New Punch
                        </h3>

                        <form id="add-punch-form" method="POST" action="{{ route('timecards.add-punch', $timecard->id) }}" class="mt-3">
                            @csrf

                            <div class="form-control">
                                <label class="label py-1">
                                    <span class="label-text text-sm">Punch Type</span>
                                </label>
                                <select name="type" class="select select-bordered select-sm w-full" required>
                                    <option value="clock_in">Clock In</option>
                                    <option value="clock_out">Clock Out</option>
                                    <option value="break_in">Break End</option>
                                    <option value="break_out">Break Start</option>
                                    @if(Auth::user()->hasPermission('manage_paid_time_off'))
                                    <option value="sick_time">Sick Time</option>
                                    <option value="vacation_time">Vacation Time</option>
                                    @endif
                                </select>
                            </div>

                            <div class="form-control mt-2">
                                <label class="label py-1">
                                    <span class="label-text text-sm">Punch Time ({{ $timezone }})</span>
                                </label>
                                <input type="hidden" name="punch_date" value="{{ $timecard->date->format('Y-m-d') }}" />

                                <div class="relative">
                                    <input type="time" 
                                           name="punch_time" 
                                           id="time_picker" 
                                           class="input input-bordered input-sm w-full"
                                           value="{{ now()->setTimezone($timezone)->format('H:i') }}"
                                           step="1"
                                           required />
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                        <i class="fa-sharp fa-solid fa-clock text-gray-400 text-sm"></i>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-500 mt-1">
                                    <i class="fa-sharp fa-solid fa-info-circle"></i>
                                    Time is in 24-hour format.
                                </div>
                            </div>

                            <div class="form-control mt-2">
                                <label class="label py-1">
                                    <span class="label-text text-sm">Notes</span>
                                    <span class="label-text-alt text-xs" id="notes-help-text"></span>
                                </label>
                                <textarea name="notes" id="punch-notes" class="textarea textarea-bordered textarea-sm h-14 text-sm" placeholder="Add notes about this punch"></textarea>
                                <div class="text-xs text-gray-500 mt-1" id="notes-hint" style="display: none;">
                                    <i class="fa-sharp fa-solid fa-info-circle"></i>
                                    For PTO entries, enter the hours in HH:MM:SS format (e.g., 8:00:00 for 8 hours).
                                </div>
                            </div>

                            <div class="mt-3">
                                <button type="submit" class="btn btn-sm btn-primary w-full">
                                    <i class="fa-sharp fa-solid fa-plus mr-1"></i>
                                    Add Punch
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Right Column: Time Card Legend -->
                <div class="card bg-base-100 shadow-sm">
                    <div class="card-body p-4">
                        <h3 class="card-title text-base">
                            <i class="fa-sharp fa-solid fa-circle-info text-primary mr-2"></i>
                            Punch Types
                        </h3>

                        <div class="overflow-x-auto">
                            <table class="table table-sm text-sm">
                                <tbody>
                                    <tr>
                                        <td class="flex items-center">
                                            <div class="w-6 h-6 rounded-full bg-success bg-opacity-20 flex items-center justify-center mr-2">
                                                <i class="fa-sharp fa-solid fa-sign-in-alt text-success-content text-xs"></i>
                                            </div>
                                            <span>Clock In</span>
                                        </td>
                                        <td>Start of work day</td>
                                    </tr>
                                    <tr>
                                        <td class="flex items-center">
                                            <div class="w-6 h-6 rounded-full bg-error bg-opacity-20 flex items-center justify-center mr-2">
                                                <i class="fa-sharp fa-solid fa-sign-out-alt text-error-content text-xs"></i>
                                            </div>
                                            <span>Clock Out</span>
                                        </td>
                                        <td>End of work day</td>
                                    </tr>
                                    <tr>
                                        <td class="flex items-center">
                                            <div class="w-6 h-6 rounded-full bg-warning bg-opacity-20 flex items-center justify-center mr-2">
                                                <i class="fa-sharp fa-solid fa-mug-hot text-warning-content text-xs"></i>
                                            </div>
                                            <span>Break Start</span>
                                        </td>
                                        <td>Start of break</td>
                                    </tr>
                                    <tr>
                                        <td class="flex items-center">
                                            <div class="w-6 h-6 rounded-full bg-info bg-opacity-20 flex items-center justify-center mr-2">
                                                <i class="fa-sharp fa-solid fa-person-walking text-info-content text-xs"></i>
                                            </div>
                                            <span>Break End</span>
                                        </td>
                                        <td>End of break</td>
                                    </tr>
                                    <tr>
                                        <td class="flex items-center">
                                            <div class="w-6 h-6 rounded-full bg-accent bg-opacity-20 flex items-center justify-center mr-2">
                                                <i class="fa-sharp fa-solid fa-head-side-mask text-accent-content text-xs"></i>
                                            </div>
                                            <span>Sick Time</span>
                                        </td>
                                        <td>Paid sick leave</td>
                                    </tr>
                                    <tr>
                                        <td class="flex items-center">
                                            <div class="w-6 h-6 rounded-full bg-primary bg-opacity-20 flex items-center justify-center mr-2">
                                                <i class="fa-sharp fa-solid fa-umbrella-beach text-primary-content text-xs"></i>
                                            </div>
                                            <span>Vacation Time</span>
                                        </td>
                                        <td>Paid vacation time</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="alert alert-info alert-sm mt-3 py-2 text-xs">
                            <i class="fa-sharp fa-solid fa-circle-info"></i>
                            <span>Click on any time or note in the punch history to edit it directly.</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Punch History -->
            <div class="card bg-base-100 shadow-sm mt-4">
                <div class="card-body p-4">
                    <h3 class="card-title text-base">
                        <i class="fa-sharp fa-solid fa-history text-primary mr-2"></i>
                        Punch History
                    </h3>

                    @if($punches->count() > 0)
                    <div class="overflow-x-auto mt-2">
                        <table class="table table-zebra table-sm text-sm">
                            <thead class="text-xs">
                                <tr>
                                    <th>Time</th>
                                    <th>Type</th>
                                    <th>Notes</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($punches as $punch)
                                <tr>
                                    <td>
                                        @if(!$punch->replaced_by)
                                            <span
                                                class="editable-punch-time cursor-pointer hover:bg-base-200 px-2 py-1 rounded inline-block min-w-[80px]"
                                                data-punch-id="{{ $punch->id }}"
                                                data-punch-time="{{ $punch->punch_time->toIso8601String() }}"
                                            >
                                                {{ $punch->punch_time->setTimezone($timezone)->format('g:i:s A') }}
                                            </span>
                                        @else
                                            {{ $punch->punch_time->setTimezone($timezone)->format('g:i:s A') }}
                                        @endif
                                    </td>
                                    <td>
                                        <div class="flex items-center">
                                            <div class="w-6 h-6 rounded-full {{ $punch->type_color }} bg-opacity-20 flex items-center justify-center mr-2">
                                                <i class="{{ $punch->type_icon }} text-xs"></i>
                                            </div>
                                            <span>{{ $punch->type_name }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        @if(!$punch->replaced_by)
                                            <span
                                                class="editable-punch-note cursor-pointer hover:bg-base-200 px-2 py-1 rounded block min-w-[80px] min-h-[20px]"
                                                data-punch-id="{{ $punch->id }}"
                                            >
                                                {{ $punch->notes ?: 'Add a note...' }}
                                            </span>
                                        @else
                                            {{ $punch->notes }}
                                        @endif
                                    </td>
                                    <td>
                                        @if($punch->original_punch_id)
                                            <span class="badge badge-xs badge-error">Edited</span>
                                        @elseif($punch->replaced_by)
                                            <span class="badge badge-xs badge-warning">Replaced</span>
                                        @else
                                            <span class="badge badge-xs badge-success">Active</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if(!$punch->replaced_by)
                                        <div class="flex space-x-1">
                                            <a href="{{ route('timepunches.edit', $punch->id) }}" class="btn btn-xs btn-circle btn-warning btn-outline">
                                                <i class="fa-sharp fa-solid fa-edit text-xs"></i>
                                            </a>

                                            <form method="POST" action="{{ route('timepunches.destroy', $punch->id) }}"
                                                  onsubmit="return confirm('Are you sure you want to delete this punch?');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-xs btn-circle btn-error btn-outline">
                                                    <i class="fa-sharp fa-solid fa-trash text-xs"></i>
                                                </button>
                                            </form>
                                        </div>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="alert alert-info alert-sm py-2 text-xs mt-2">
                        <i class="fa-sharp fa-solid fa-info-circle"></i>
                        <span>No punches recorded for this time card.</span>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    @vite('resources/js/timecard-edit.js')
    <script>
        // Inline editing functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing inline editing...');
            
            // Function to handle time editing
            function handleTimeClick(element) {
                // Don't do anything if we're already editing
                if (element.querySelector('input')) {
                    return;
                }
                
                const punchId = element.getAttribute('data-punch-id');
                const currentTime = element.getAttribute('data-punch-time');
                const currentDisplay = element.textContent.trim();
                
                console.log('Editing punch:', punchId, currentTime);
                
                // Extract time from ISO string
                let timeValue = '';
                try {
                    const date = new Date(currentTime);
                    if (!isNaN(date.getTime())) {
                        const hours = String(date.getHours()).padStart(2, '0');
                        const minutes = String(date.getMinutes()).padStart(2, '0');
                        const seconds = String(date.getSeconds()).padStart(2, '0');
                        timeValue = `${hours}:${minutes}:${seconds}`;
                    }
                } catch (error) {
                    console.error('Error parsing date:', error);
                    timeValue = currentTime.substring(11, 19);
                }
                
                // Create time input
                const input = document.createElement('input');
                input.type = 'time';
                input.value = timeValue;
                input.className = 'input input-bordered input-sm w-32';
                input.step = '1';
                
                // Replace content with input
                element.innerHTML = '';
                element.appendChild(input);
                input.focus();
                
                // Handle save on blur
                input.addEventListener('blur', function() {
                    const newTime = input.value;
                    if (newTime !== timeValue) {
                        // Save the change
                        savePunchTime(punchId, newTime, element, currentDisplay, currentTime);
                    } else {
                        element.textContent = currentDisplay;
                    }
                });
                
                // Handle keyboard
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        input.blur();
                    } else if (e.key === 'Escape') {
                        element.textContent = currentDisplay;
                    }
                });
            }
            
            // Function to save punch time
            function savePunchTime(punchId, newTime, element, originalDisplay, originalDateTime) {
                // Get the date from the original datetime
                let punchDate;
                try {
                    const date = new Date(originalDateTime);
                    if (!isNaN(date.getTime())) {
                        const year = date.getFullYear();
                        const month = String(date.getMonth() + 1).padStart(2, '0');
                        const day = String(date.getDate()).padStart(2, '0');
                        punchDate = `${year}-${month}-${day}`;
                    }
                } catch (error) {
                    punchDate = originalDateTime.substring(0, 10);
                }
                
                const newDateTime = `${punchDate} ${newTime}`;
                
                // Show loading
                element.innerHTML = '<span class="loading loading-spinner loading-xs"></span>';
                
                // Send AJAX request
                fetch(`/api/time-punches/${punchId}/ajax-update`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        punch_time: newDateTime
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        element.textContent = data.formatted_time;
                        element.setAttribute('data-punch-time', data.punch_time);
                        // CRITICAL: Update the punch ID to the new punch ID
                        element.setAttribute('data-punch-id', data.punch.id);
                        
                        // Update totals if provided
                        if (data.timecard) {
                            updateTimecardTotals(data.timecard);
                        }
                        
                        // Show success message
                        if (window.showToast) {
                            window.showToast('success', 'Time updated successfully');
                        }
                    } else {
                        element.textContent = originalDisplay;
                        if (window.showToast) {
                            window.showToast('error', data.message || 'Failed to update time');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    element.textContent = originalDisplay;
                    if (window.showToast) {
                        window.showToast('error', 'An error occurred while updating the time');
                    }
                });
            }
            
            // Function to update timecard totals
            function updateTimecardTotals(timecard) {
                const totalHoursEl = document.getElementById('total-hours');
                const breakHoursEl = document.getElementById('break-hours');
                const sickHoursEl = document.getElementById('sick-hours');
                const vacationHoursEl = document.getElementById('vacation-hours');
                const billableHoursEl = document.getElementById('billable-hours');
                
                if (totalHoursEl) totalHoursEl.textContent = timecard.total_hours;
                if (breakHoursEl) breakHoursEl.textContent = timecard.total_break_hours;
                if (sickHoursEl) sickHoursEl.textContent = timecard.total_sick_time || '0:00:00';
                if (vacationHoursEl) vacationHoursEl.textContent = timecard.total_vacation_time || '0:00:00';
                
                if (billableHoursEl && timecard.total_billable_hours_decimal) {
                    billableHoursEl.textContent = timecard.total_billable_hours_decimal;
                }
            }
            
            // Attach click handlers to all editable time elements
            const timeElements = document.querySelectorAll('.editable-punch-time');
            console.log('Found', timeElements.length, 'editable time elements');
            
            timeElements.forEach(element => {
                element.addEventListener('click', function(e) {
                    e.preventDefault();
                    handleTimeClick(this);
                });
            });
            
            // Also handle note editing
            const noteElements = document.querySelectorAll('.editable-punch-note');
            noteElements.forEach(element => {
                element.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    if (element.querySelector('input')) {
                        return;
                    }
                    
                    const punchId = element.getAttribute('data-punch-id');
                    const currentNote = element.textContent.trim();
                    
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.value = currentNote === 'Add a note...' ? '' : currentNote;
                    input.className = 'input input-bordered input-sm w-full';
                    input.placeholder = 'Add a note...';
                    
                    element.innerHTML = '';
                    element.appendChild(input);
                    input.focus();
                    
                    input.addEventListener('blur', function() {
                        const newNote = input.value;
                        
                        // Show loading
                        element.innerHTML = '<span class="loading loading-spinner loading-xs"></span>';
                        
                        // Save the note
                        fetch(`/api/time-punches/${punchId}/ajax-update`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                'Accept': 'application/json'
                            },
                            body: JSON.stringify({
                                notes: newNote
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                element.textContent = newNote || 'Add a note...';
                                // CRITICAL: Update the punch ID to the new punch ID
                                element.setAttribute('data-punch-id', data.punch.id);
                                if (window.showToast) {
                                    window.showToast('success', 'Note updated successfully');
                                }
                            } else {
                                element.textContent = currentNote || 'Add a note...';
                                if (window.showToast) {
                                    window.showToast('error', 'Failed to update note');
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            element.textContent = currentNote || 'Add a note...';
                        });
                    });
                    
                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter') {
                            input.blur();
                        } else if (e.key === 'Escape') {
                            element.textContent = currentNote || 'Add a note...';
                        }
                    });
                });
            });
        });
    </script>
    @endpush
    
    @push('styles')
    <style>
        .editable-punch-time,
        .editable-punch-note {
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
            border-radius: 0.25rem;
        }
        
        .editable-punch-time:hover,
        .editable-punch-note:hover {
            background-color: oklch(var(--b2));
            border-color: oklch(var(--bc) / 0.2);
        }
        
        .editable-punch-note:empty::before {
            content: 'Add a note...';
            color: oklch(var(--bc) / 0.5);
        }
    </style>
    @endpush
</x-app-layout>
