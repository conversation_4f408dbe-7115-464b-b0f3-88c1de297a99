@php
    // Build action buttons array dynamically based on permissions
    $actionButtons = [];

    // Add edit button if user has permission
    if (Auth::user()->hasPermission('edit_timecards') ||
        (Auth::user()->hasPermission('edit_own_timecards') && $timecard->user_id === Auth::id())) {
        $actionButtons[] = [
            'name' => 'Edit Time Card',
            'route' => route('timecards.edit', $timecard->id),
            'icon' => 'fa-sharp fa-edit',
            'class' => 'btn btn-primary btn-sm gap-2',
            'permission' => 'edit_timecards|edit_own_timecards'
        ];
    }
@endphp

<x-app-layout
    page-title="Time Card: {{ $timecard->user->name }} - {{ $timecard->date->format('M j, Y') }}"
    page-icon="fa-sharp fa-calendar-day"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('timecards.index'),
            'text' => 'Back to Time Cards'
        ]
    ]"
    :action-buttons="$actionButtons"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Time Cards', 'route' => 'timecards.index', 'icon' => 'fa-calendar-days'],
        ['name' => $timecard->user->name . ' - ' . $timecard->date->format('M j'), 'icon' => 'fa-calendar-day']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Time Card Header -->
            <div class="card bg-base-100 shadow-xl mb-6">
                <div class="card-body">
                    <div class="flex flex-col md:flex-row items-center gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-neutral text-neutral-content rounded-full w-16 h-16 flex items-center justify-center">
                                <span class="text-xl leading-none">{{ substr($timecard->user->name, 0, 1) }}</span>
                            </div>
                        </div>

                        <div class="flex-1">
                            <h2 class="text-2xl font-bold">{{ $timecard->user->name }}</h2>
                            <div class="flex flex-wrap gap-2 mt-1">
                                <div class="badge badge-primary">
                                    <i class="fa-sharp fa-solid fa-calendar-day mr-1"></i>
                                    {{ $timecard->date->format('D, M j, Y') }}
                                </div>

                                @if($timecard->date->isToday())
                                    <div class="badge badge-accent">Today</div>
                                @elseif($timecard->date->isYesterday())
                                    <div class="badge badge-secondary">Yesterday</div>
                                @elseif($timecard->date->isTomorrow())
                                    <div class="badge badge-secondary">Tomorrow</div>
                                @endif

                                <div class="badge badge-outline">
                                    <i class="fa-sharp fa-solid fa-clock mr-1"></i>
                                    {{ $timezone }}
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>

            <!-- Time Summary -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <div class="stat">
                            <div class="stat-figure text-primary">
                                <i class="fa-sharp fa-solid fa-business-time text-3xl"></i>
                            </div>
                            <div class="stat-title">Work Hours</div>
                            <div class="stat-value text-primary">{{ $timecard->total_hours }}</div>
                            <div class="stat-desc">Total time worked</div>
                        </div>
                    </div>
                </div>

                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <div class="stat">
                            <div class="stat-figure text-secondary">
                                <i class="fa-sharp fa-solid fa-mug-hot text-3xl"></i>
                            </div>
                            <div class="stat-title">Break Hours</div>
                            <div class="stat-value text-secondary">{{ $timecard->total_break_hours }}</div>
                            <div class="stat-desc">Total break time</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Time Card Details -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <div class="card bg-base-100 shadow-xl lg:col-span-2">
                    <div class="card-body">
                        <h2 class="card-title">
                            <i class="fa-sharp fa-solid fa-list-check mr-2 text-primary"></i>
                            Punch History
                        </h2>

                        @if($punches->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="table table-zebra">
                                <thead class="bg-base-200">
                                    <tr>
                                        <th class="text-primary">Time</th>
                                        <th class="text-primary">Type</th>
                                        <th class="text-primary">Notes</th>
                                        <th class="text-primary">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($punches as $index => $punch)
                                    <tr class="hover">
                                        <td class="font-medium">
                                            {{ $punch->punch_time->setTimezone($timezone)->format('g:i:s A') }}
                                        </td>
                                        <td>
                                            <span class="flex items-center {{ $punch->type_color }}">
                                                <i class="{{ $punch->type_icon }} mr-2"></i>
                                                {{ $punch->type_name }}
                                            </span>
                                        </td>

                                        <td>
                                            @if($punch->notes)
                                                <span class="tooltip" data-tip="{{ $punch->notes }}">
                                                    <i class="fa-sharp fa-solid fa-comment-dots text-info"></i>
                                                    <span class="ml-1">{{ Str::limit($punch->notes, 20) }}</span>
                                                </span>
                                            @else
                                                <span class="text-gray-400">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($punch->original_punch_id)
                                                <div class="badge badge-error gap-1">
                                                    <i class="fa-sharp fa-solid fa-pen-to-square"></i>
                                                    Edited
                                                </div>
                                            @elseif($punch->replaced_by)
                                                <div class="badge badge-warning gap-1">
                                                    <i class="fa-sharp fa-solid fa-rotate"></i>
                                                    Replaced
                                                </div>
                                            @else
                                                <div class="badge badge-success gap-1">
                                                    <i class="fa-sharp fa-solid fa-check"></i>
                                                    Active
                                                </div>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @else
                        <div class="alert alert-info">
                            <i class="fa-sharp fa-solid fa-info-circle"></i>
                            <span>No punches recorded for this time card.</span>
                        </div>
                        @endif
                    </div>
                </div>

                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h2 class="card-title">
                            <i class="fa-sharp fa-solid fa-circle-info mr-2 text-primary"></i>
                            Time Card Details
                        </h2>

                        <div class="overflow-x-auto">
                            <table class="table">
                                <tbody>
                                    <tr>
                                        <th class="bg-base-200">Date</th>
                                        <td>{{ $timecard->date->format('F j, Y') }}</td>
                                    </tr>
                                    <tr>
                                        <th class="bg-base-200">Day of Week</th>
                                        <td>{{ $timecard->date->format('l') }}</td>
                                    </tr>
                                    <tr>
                                        <th class="bg-base-200">User</th>
                                        <td>{{ $timecard->user->name }}</td>
                                    </tr>
                                    <tr>
                                        <th class="bg-base-200">First Punch</th>
                                        <td>
                                            @if($punches->count() > 0)
                                                {{ $punches->first()->punch_time->setTimezone($timezone)->format('g:i:s A') }}
                                            @else
                                                <span class="text-gray-400">-</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <th class="bg-base-200">Last Punch</th>
                                        <td>
                                            @if($punches->count() > 0)
                                                {{ $punches->last()->punch_time->setTimezone($timezone)->format('g:i:s A') }}
                                            @else
                                                <span class="text-gray-400">-</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <th class="bg-base-200">Total Punches</th>
                                        <td>{{ $punches->count() }}</td>
                                    </tr>
                                    <tr>
                                        <th class="bg-base-200">Status</th>
                                        <td>
                                            @php
                                                $status = $timecard->getCurrentStatus();
                                            @endphp

                                            @if($status == 'clock_in')
                                                <div class="badge badge-success gap-1">
                                                    <i class="fa-sharp fa-solid fa-user-clock"></i>
                                                    Clocked In
                                                </div>
                                            @elseif($status == 'break')
                                                <div class="badge badge-warning gap-1">
                                                    <i class="fa-sharp fa-solid fa-mug-hot"></i>
                                                    On Break
                                                </div>
                                            @else
                                                <div class="badge badge-error gap-1">
                                                    <i class="fa-sharp fa-solid fa-user-xmark"></i>
                                                    Clocked Out
                                                </div>
                                            @endif
                                        </td>
                                    </tr>
                                    @if($timecard->notes)
                                    <tr>
                                        <th class="bg-base-200">Notes</th>
                                        <td>{{ $timecard->notes }}</td>
                                    </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timeline Visualization -->
            @if($punches->count() > 1)
            <div class="card bg-base-100 shadow-xl mb-6">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fa-sharp fa-solid fa-timeline mr-2 text-primary"></i>
                        Time Card Timeline
                    </h2>

                    <div class="overflow-x-auto">
                        <div class="py-4">
                            <ul class="timeline timeline-snap-icon max-md:timeline-compact timeline-vertical">
                                @foreach($punches as $index => $punch)
                                <li>
                                    <div class="timeline-middle">
                                        <i class="{{ $punch->type_icon }} {{ $punch->type_color }}"></i>
                                    </div>
                                    <div class="{{ $index % 2 == 0 ? 'timeline-start md:text-end' : 'timeline-end' }} mb-10">
                                        <time class="font-mono">{{ $punch->punch_time->setTimezone($timezone)->format('g:i:s A') }}</time>
                                        <div class="text-lg font-black">{{ $punch->type_name }}</div>
                                        @if($punch->notes)
                                        <p class="text-sm">{{ $punch->notes }}</p>
                                        @endif
                                    </div>
                                    <hr/>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Action Buttons -->
            <div class="flex justify-between mt-6">
                <a href="{{ route('timecards.index') }}" class="btn btn-outline">
                    <i class="fa-sharp fa-solid fa-arrow-left mr-2"></i>
                    Back to Time Cards
                </a>

                @if(Auth::user()->hasPermission('edit_timecards') ||
                    (Auth::user()->hasPermission('edit_own_timecards') && $timecard->user_id === Auth::id()))
                <a href="{{ route('timecards.edit', $timecard->id) }}" class="btn btn-warning">
                    <i class="fa-sharp fa-solid fa-edit mr-2"></i>
                    Edit Time Card
                </a>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
