<x-app-layout
    page-title="Create Time Card"
    page-icon="fa-sharp fa-plus"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('timecards.index'),
            'text' => 'Back to Time Cards'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'Time Clock',
            'route' => route('time-clock'),
            'icon' => 'fa-sharp fa-clock',
            'class' => 'btn btn-primary btn-sm gap-2'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Time Cards', 'route' => 'timecards.index', 'icon' => 'fa-calendar-days'],
        ['name' => 'Create', 'icon' => 'fa-plus']
    ]">

    <div class="py-6 lg:py-8">

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
                        <h2 class="card-title">Create Time Card for Specific Date</h2>
                        
                        <a href="{{ route('timecards.index') }}" class="btn btn-ghost">
                            <i class="fa-sharp fa-solid fa-arrow-left mr-2"></i>
                            Back to List
                        </a>
                    </div>
                    
                    <form method="POST" action="{{ route('timecards.store-for-date') }}">
                        @csrf
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">User</span>
                                </label>
                                <select name="user_id" class="select select-bordered" required>
                                    <option value="">Select User</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }}</option>
                                    @endforeach
                                </select>
                                @error('user_id')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>
                            
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Date</span>
                                </label>
                                <input type="date" name="date" class="input input-bordered" 
                                       value="{{ now()->format('Y-m-d') }}" required />
                                @error('date')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="form-control mt-4">
                            <label class="label">
                                <span class="label-text">Notes</span>
                            </label>
                            <textarea name="notes" class="textarea textarea-bordered" placeholder="Add notes about this time card"></textarea>
                            @error('notes')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>
                        
                        <div class="mt-6">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa-sharp fa-solid fa-save mr-2"></i>
                                Create Time Card
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
