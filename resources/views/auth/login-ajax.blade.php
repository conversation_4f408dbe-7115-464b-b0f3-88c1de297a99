<div class="bg-slate-950">
    <div class="mb-4 flex justify-center">
        <img src="{{ asset('img/logo.webp') }}" alt="Logo" class="w-20 h-20" />
    </div>

    <h2 class="text-xl font-semibold text-white mb-6 text-center">Login</h2>

    @if ($errors->any())
        <div class="mb-4 p-3 bg-red-100 border border-red-300 rounded-md">
            <div class="font-medium text-red-600">{{ __('Login Failed') }}</div>
            <ul class="mt-2 list-disc list-inside text-sm text-red-600">
                @if ($errors->has('email') || $errors->has('password'))
                    <li>{{ __('The email or password you entered is incorrect. Please try again.') }}</li>
                @else
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                @endif
            </ul>
        </div>
    @endif

    @if (session('status'))
        <div class="mb-4 font-medium text-sm text-green-600">
            {{ session('status') }}
        </div>
    @endif

    <form method="POST" action="{{ route('login') }}" id="login-form">
        @csrf

        <div>
            <label for="email" class="block text-sm font-medium text-gray-200">{{ __('Email') }}</label>
            <input id="email" class="input input-bordered w-full mt-1" type="email" name="email" value="{{ old('email') }}" required autofocus autocomplete="username" />
        </div>

        <div class="mt-4">
            <label for="password" class="block text-sm font-medium text-gray-200">{{ __('Password') }}</label>
            <input id="password" class="input input-bordered mt-1 w-full" type="password" name="password" required autocomplete="current-password" />
        </div>

        <div class="block mt-4">
            <label for="remember_me" class="inline-flex items-center">
                <input id="remember_me" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="remember">
                <span class="ml-2 text-sm text-gray-400">{{ __('Remember me') }}</span>
            </label>
        </div>

        <div class="flex items-center justify-between mt-4">
            @if (Route::has('password.request'))
                <a class="text-sm text-blue-400 hover:text-blue-500" href="{{ route('password.request') }}" target="_blank">
                    {{ __('Forgot your password?') }}
                </a>
            @endif

            <button type="submit" class="btn btn-primary">
                {{ __('Log in') }}
            </button>
        </div>
    </form>
</div>