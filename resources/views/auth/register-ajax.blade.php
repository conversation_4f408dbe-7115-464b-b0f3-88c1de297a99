<div class="bg-slate-950">
    <div class="mb-4 flex justify-center">
        <img src="{{ asset('img/logo.webp') }}" alt="Logo" class="w-20 h-20" />
    </div>

    <h2 class="text-xl font-semibold text-white mb-6 text-center">Register</h2>

    @if ($errors->any())
        <div class="mb-4 p-3 bg-red-100 border border-red-300 rounded-md">
            <div class="font-medium text-red-600">{{ __('Registration Failed') }}</div>
            <ul class="mt-2 list-disc list-inside text-sm text-red-600">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form method="POST" action="{{ route('register') }}" id="register-form">
        @csrf

        <div>
            <label for="name" class="block text-sm font-medium text-gray-200">{{ __('Name') }}</label>
            <input id="name" class="input input-bordered w-full mt-1" type="text" name="name" value="{{ old('name') }}" required autofocus autocomplete="name" />
        </div>

        <div class="mt-4">
            <label for="email" class="block text-sm font-medium text-gray-200">{{ __('Email') }}</label>
            <input id="email" class="input input-bordered w-full mt-1" type="email" name="email" value="{{ old('email') }}" required autocomplete="username" />
        </div>

        <div class="mt-4">
            <label for="password" class="block text-sm font-medium text-gray-200">{{ __('Password') }}</label>
            <input id="password" class="input input-bordered mt-1 w-full" type="password" name="password" required autocomplete="new-password" />
        </div>

        <div class="mt-4">
            <label for="password_confirmation" class="block text-sm font-medium text-gray-200">{{ __('Confirm Password') }}</label>
            <input id="password_confirmation" class="input input-bordered mt-1 w-full" type="password" name="password_confirmation" required autocomplete="new-password" />
        </div>

        <div class="mt-4">
            <label for="passcode" class="block text-sm font-medium text-gray-200">{{ __('Passcode') }}</label>
            <input id="passcode" class="input input-bordered mt-1 w-full" type="text" name="passcode" required />
        </div>

        @if (Laravel\Jetstream\Jetstream::hasTermsAndPrivacyPolicyFeature())
            <div class="mt-4">
                <label for="terms" class="inline-flex items-center">
                    <input id="terms" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="terms" required>
                    <span class="ml-2 text-sm text-gray-400">{!! __('I agree to the :terms_of_service and :privacy_policy', [
                            'terms_of_service' => '<a target="_blank" href="'.route('terms.show').'" class="text-blue-400 hover:text-blue-500">'.__('Terms of Service').'</a>',
                            'privacy_policy' => '<a target="_blank" href="'.route('policy.show').'" class="text-blue-400 hover:text-blue-500">'.__('Privacy Policy').'</a>',
                    ]) !!}</span>
                </label>
            </div>
        @endif

        <div class="flex items-center justify-between mt-6">
            <a class="text-sm text-blue-400 hover:text-blue-500" href="#" id="back-to-login">
                {{ __('Already registered?') }}
            </a>

            <button type="submit" class="btn btn-primary">
                {{ __('Register') }}
            </button>
        </div>
    </form>
</div>

<script>
    document.getElementById('back-to-login').addEventListener('click', function(e) {
        e.preventDefault();
        document.getElementById('show-login-btn').click();
    });
</script>