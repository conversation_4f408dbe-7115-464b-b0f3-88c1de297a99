{{-- Demo page to show @importQuill directive working --}}
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quill Import Demo</title>
    @vite('resources/css/app.css')
    @stack('styles')
</head>
<body class="bg-base-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Quill Editor Import Demo</h1>
        
        <div class="alert alert-info mb-6">
            <i class="fa fa-info-circle"></i>
            <span>This page demonstrates the @importQuill directive. Even though it appears multiple times below, the Quill assets are only loaded once.</span>
        </div>

        <!-- First component using Quill -->
        <div class="card bg-base-200 shadow-xl mb-6">
            <div class="card-body">
                <h2 class="card-title">First Editor</h2>
                @importQuill
                <div id="editor1" class="w-full h-40 border border-base-300 rounded"></div>
                <input type="hidden" name="content1" id="content1">
            </div>
        </div>

        <!-- Second component using Quill -->
        <div class="card bg-base-200 shadow-xl mb-6">
            <div class="card-body">
                <h2 class="card-title">Second Editor</h2>
                @importQuill
                <div id="editor2" class="w-full h-40 border border-base-300 rounded"></div>
                <input type="hidden" name="content2" id="content2">
            </div>
        </div>

        <!-- Third component using Quill -->
        <div class="card bg-base-200 shadow-xl mb-6">
            <div class="card-body">
                <h2 class="card-title">Third Editor</h2>
                @importQuill
                <div id="editor3" class="w-full h-40 border border-base-300 rounded"></div>
                <input type="hidden" name="content3" id="content3">
            </div>
        </div>

        <div class="alert alert-success">
            <i class="fa fa-check-circle"></i>
            <span>Check the page source - you'll see the Quill CSS and JS are only included once, despite @importQuill appearing 3 times!</span>
        </div>
    </div>

    @stack('scripts')
    
    <script>
        // Initialize all Quill editors
        document.addEventListener('DOMContentLoaded', function() {
            // Editor 1
            const quill1 = new Quill('#editor1', { theme: 'snow' });
            quill1.on('text-change', function() {
                document.getElementById('content1').value = quill1.root.innerHTML;
            });

            // Editor 2
            const quill2 = new Quill('#editor2', { theme: 'snow' });
            quill2.on('text-change', function() {
                document.getElementById('content2').value = quill2.root.innerHTML;
            });

            // Editor 3
            const quill3 = new Quill('#editor3', { theme: 'snow' });
            quill3.on('text-change', function() {
                document.getElementById('content3').value = quill3.root.innerHTML;
            });
        });
    </script>
</body>
</html>
