<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            Create New Category
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100 shadow-md rounded-lg p-6">
                <form action="{{ route('inventory_categories.store') }}" method="POST">
                    @csrf

                    <!-- Name Field -->
                    <div class="mb-6">
                        <label for="name" class="block text-sm font-medium text-base-content mb-2">Name*</label>
                        <input type="text" name="name" id="name" class="w-full input input-bordered"
                            value="{{ old('name') }}" required>
                        @error('name')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Description Field -->
                    <div class="mb-6">
                        <label for="description"
                            class="block text-sm font-medium text-base-content mb-2">Description</label>
                        <textarea name="description" id="description" rows="4" class="textarea textarea-bordered w-full">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Code Field -->
                    <div class="mb-6">
                        <label for="code" class="block text-sm font-medium text-base-content mb-2">Code*</label>
                        <span class="text-sm text-base-content/70">Note: The code is used to identify the category in the
                            system. It should be unique, short, and cannot be changed once saved.</span>
                        <input type="text" name="code" id="code" class="w-full input input-bordered"
                            value="{{ old('code') }}" required>
                        @error('code')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status Field -->
                    <div class="mb-6">
                        <label for="status" class="block text-sm font-medium text-base-content mb-2">Status*</label>
                        <select name="status" id="status" class="select select-bordered w-full">
                            <option value="1" {{ old('status') == '1' ? 'selected' : '' }}>Active</option>
                            <option value="0" {{ old('status') == '0' ? 'selected' : '' }}>Inactive</option>
                        </select>
                        @error('status')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Department Field -->
                    <div class="mb-6">
                        <label for="department_id"
                            class="block text-sm font-medium text-base-content mb-2">Department*</label>
                        <select name="department_id" id="department_id" class="select select-bordered w-full" required>
                            @foreach ($departments as $department)
                                <option value="{{ $department->id }}"
                                    {{ old('department_id') == $department->id ? 'selected' : '' }}>
                                    {{ $department->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('department_id')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Tax Policy Field -->
                    <div class="mb-6">
                        <label for="tax_policy_id" class="block text-sm font-medium text-base-content mb-2">Default Tax
                            Policy*</label>
                        <select name="tax_policy_id" id="tax_policy_id" class="select select-bordered w-full" required>
                            @foreach ($tax_policies as $tax_policy)
                                <option value="{{ $tax_policy->id }}"
                                    {{ old('tax_policy_id') == $tax_policy->id ? 'selected' : '' }}>
                                    {{ $tax_policy->name }} ({{ $tax_policy->rate }}%)
                                </option>
                            @endforeach
                        </select>
                        @error('tax_policy_id')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- AI Promptable Field -->
                    <div class="mb-6">
                        <label for="ai_promptable" class="block text-sm font-medium text-base-content mb-2">AI
                            Promptable*</label>
                        <span class="text-sm text-base-content/70">AI Promptable categories are allowed to use AI generated
                            descriptions.</span>
                        <select name="ai_promptable" id="ai_promptable" class="select select-bordered w-full">
                            <option value="0" {{ old('ai_promptable') == '0' ? 'selected' : '' }}>No</option>
                            <option value="1" {{ old('ai_promptable') == '1' ? 'selected' : '' }}>Yes</option>
                        </select>
                        @error('ai_promptable')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Quantity Type Field -->
                    <div class="mb-6">
                        <label for="quantity_type" class="block text-sm font-medium text-base-content mb-2">Quantity
                            Type*</label>
                        <select name="quantity_type" id="quantity_type" class="select select-bordered w-full">
                            <option value="individual" {{ old('quantity_type') == 'individual' ? 'selected' : '' }}>
                                Individual</option>
                            <option value="unit" {{ old('quantity_type') == 'unit' ? 'selected' : '' }}>Unit</option>
                            <option value="unlimited" {{ old('quantity_type') == 'unlimited' ? 'selected' : '' }}>
                                Unlimited</option>
                        </select>
                        @error('quantity_type')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>
                    <div class="prose">
                        <p class="text-sm text-base-content/70">Note: Quantity Type is used to define how the quantity of the item
                            is measured.</p>
                        <ul class="text-base-content">
                            <li><strong>Individual:</strong> Used for items that are sold and tracked individually, like
                                refurbished PCs, or one gaylord full of batteries. Only one item may appear on one customer
                                invoice.</li>
                            <li><strong>Unit:</strong> Used for items that are sold in multiples of individual units. (Eg.
                                10x untested laptops, 20x DDR4 RAM Sticks) - Can use same item on multiple invoices.</li>
                            <li><strong>Unlimited:</strong> Used for items that are not tracked by quantity. (Eg. Services)
                                - Can use same item on multiple invoices.</li>
                            <li>Type cannot be changed once saved.</li>
                        </ul>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-right">
                        <button type="submit" class="btn btn-primary">
                            Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
