<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            Edit Category
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <a href="{{ route('inventory_categories.index') }}" class="btn btn-primary mb-2">
                <i class="fa-sharp fa-arrow-left mr-2"></i>
                Back to Categories</a>
            <!-- Main Form for Editing Category -->
            <div class="card card-bordered p-6 bg-base-100">
                <form id="category-form" action="{{ route('inventory_categories.update', $inventoryCategory->id) }}"
                    method="POST">
                    @csrf
                    @method('PUT')

                    <!-- Category Name -->
                    <div class="mb-6">
                        <label for="name" class="block text-sm font-medium text-base-content mb-2">Category Name</label>
                        <input type="text" name="name" id="name" class="w-full input input-bordered"
                            value="{{ old('name', $inventoryCategory->name) }}" required>
                        @error('name')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Category Description -->
                    <div class="mb-6">
                        <label for="description"
                            class="block text-sm font-medium text-base-content mb-2">Description</label>
                        <textarea name="description" id="description" rows="4" class="w-full textarea textarea-bordered auto-save-input">{{ old('description', $inventoryCategory->description) }}</textarea>
                        @error('description')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Category Code -->
                    <div class="mb-6">
                        <label for="code" class="block text-sm font-medium text-base-content mb-2">Code</label>
                        <input type="text" name="code" id="code"
                            class="w-full input input-bordered input-disabled"
                            value="{{ old('code', $inventoryCategory->code) }}"
                            {{ $inventoryCategory->exists ? 'readonly' : '' }} required>
                        @error('code')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Department Field -->
                    <div class="mb-6">
                        <label for="department_id"
                            class="block text-sm font-medium text-base-content mb-2">Department</label>
                        <select name="department_id" id="department_id"
                            class="select select-bordered w-full auto-save-input" required>
                            @foreach ($departments as $department)
                                <option value="{{ $department->id }}"
                                    {{ old('department_id', $inventoryCategory->department_id) == $department->id ? 'selected' : '' }}>
                                    {{ $department->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('department_id')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Tax Policy Field -->
                    <div class="mb-6">
                        <label for="tax_policy_id" class="block text-sm font-medium text-base-content mb-2">Default Tax
                            Policy</label>
                        <select name="tax_policy_id" id="tax_policy_id"
                            class="select select-bordered w-full auto-save-input" required>
                            @foreach ($tax_policies as $tax_policy)
                                <option value="{{ $tax_policy->id }}"
                                    {{ old('tax_policy_id', $inventoryCategory->tax_policy_id) == $tax_policy->id ? 'selected' : '' }}>
                                    {{ $tax_policy->name }} ({{ $tax_policy->rate }}%)
                                </option>
                            @endforeach
                        </select>
                        @error('tax_policy_id')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Quantity Type Field -->
                    <div class="mb-6">
                        <label for="quantity_type" class="block text-sm font-medium text-base-content mb-2">Quantity
                            Type</label>
                        <input type="text" name="quantity_type" id="quantity_type"
                            class="w-full input input-bordered"
                            value="{{ old('quantity_type', $inventoryCategory->quantity_type) }}" disabled>
                        @error('quantity_type')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- AI Promptable Field -->
                    <div class="mb-6">
                        <label for="ai_promptable" class="block text-sm font-medium text-base-content mb-2">AI
                            Promptable</label>
                        <select name="ai_promptable" id="ai_promptable"
                            class="select select-bordered w-full auto-save-input" required>

                            <option value="0"
                                {{ old('ai_promptable', $inventoryCategory->ai_promptable) == 0 ? 'selected' : '' }}>No
                            </option>
                            <option value="1"
                                {{ old('ai_promptable', $inventoryCategory->ai_promptable) == 1 ? 'selected' : '' }}>
                                Yes</option>
                        </select>
                        @error('ai_promptable')
                            <p class="text-error text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>
                </form>

                <!-- Save Status Indicator -->
                <div id="category-save-status" class="mt-4 text-sm text-base-content/70">All changes saved</div>
            </div>

            <!-- Checklist Management -->
            <div class="bg-base-100 shadow-md rounded-lg p-6 mt-8">
                <h3 class="text-lg font-semibold mb-4">Manage Checklist Fields</h3>

                <!-- Save Status Indicator -->
                <div id="save-status" class="text-sm text-base-content/70 mb-4"></div>

                <form id="checklist-fields-form" action="{{ route('checklist_fields.updateAll') }}" method="POST">
                    @csrf

                    @if ($inventoryCategory->checklistFields->isNotEmpty())
                        <table class="table table-zebra w-full">
                            <thead class="bg-base-200">
                                <tr>
                                    <th class="border border-base-300 px-4 py-2 text-left">Name</th>
                                    <th class="border border-base-300 px-4 py-2 text-left">Description</th>
                                    <th class="border border-base-300 px-4 py-2 text-left">Type</th>
                                    <th class="border border-base-300 px-4 py-2 text-left">Order</th>
                                    <th class="border border-base-300 px-4 py-2 text-left">Status</th>
                                    <th class="border border-base-300 px-4 py-2 text-left">Units/Options</th>
                                    <th class="border border-base-300 px-4 py-2 text-left">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($inventoryCategory->checklistFields->sortBy('order') as $field)
                                    <tr>
                                        <td class="border border-base-300 px-4 py-2">
                                            <input type="text" name="fields[{{ $field->id }}][name]"
                                                value="{{ old('fields.' . $field->id . '.name', $field->name) }}"
                                                class="w-full input input-bordered input-sm">
                                        </td>
                                        <td class="border border-base-300 px-4 py-2">
                                            <textarea name="fields[{{ $field->id }}][description]" rows="2"
                                                class="w-full textarea textarea-bordered textarea-sm auto-save-input">{{ old('fields.' . $field->id . '.description', $field->description) }}</textarea>
                                        </td>
                                        <td class="border border-base-300 px-4 py-2">{{ $field->type }}</td>
                                        <td class="border border-base-300 px-4 py-2">
                                            <input type="number" name="fields[{{ $field->id }}][order]"
                                                value="{{ old('fields.' . $field->id . '.order', $field->order) }}"
                                                class="w-full input input-bordered input-sm auto-save-input">
                                        </td>
                                        <td class="border border-base-300 px-4 py-2">
                                            <select name="fields[{{ $field->id }}][status]"
                                                class="select select-bordered select-sm w-full auto-save-input">
                                                <option value="1"
                                                    {{ old('fields.' . $field->id . '.status', $field->status) == 1 ? 'selected' : '' }}>
                                                    Active</option>
                                                <option value="0"
                                                    {{ old('fields.' . $field->id . '.status', $field->status) == 0 ? 'selected' : '' }}>
                                                    Inactive</option>
                                            </select>
                                        </td>
                                        <td class="border border-base-300 px-4 py-2">
                                            <!-- Dynamic Unit/Options Fields -->
                                            @if ($field->type === 'numeric')
                                                <input type="text"
                                                    name="fields[{{ $field->id }}][options][unit]"
                                                    value="{{ old('fields.' . $field->id . '.options.unit', json_decode($field->options, true)['unit'] ?? '') }}"
                                                    class="w-full input input-bordered input-sm auto-save-input"
                                                    placeholder="Unit (e.g., cm, kg)">
                                            @endif
                                            @if ($field->type === 'select')
                                                <textarea name="fields[{{ $field->id }}][options][select_list]" rows="2"
                                                    class="w-full textarea textarea-bordered textarea-sm auto-save-input"
                                                    placeholder="Comma-separated options">{{ old('fields.' . $field->id . '.options.select_list', implode(',', json_decode($field->options, true)['select_list'] ?? [])) }}</textarea>
                                            @endif
                                            @if ($field->type === 'checkboxes')
                                                <textarea name="fields[{{ $field->id }}][options][checkboxes]" rows="2"
                                                    class="w-full textarea textarea-bordered textarea-sm auto-save-input"
                                                    placeholder="Comma-separated options">{{ old('fields.' . $field->id . '.options.checkboxes', implode(',', json_decode($field->options, true)['checkboxes'] ?? [])) }}</textarea>
                                            @endif
                                        </td>
                                        <td class="border border-base-300 px-4 py-2 text-center">
                                            <button type="button"
                                                class="btn btn-error btn-sm"
                                                data-id="{{ $field->id }}">
                                                ✕
                                            </button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <p class="text-base-content/70">No checklist fields found for this category.</p>
                    @endif
                </form>
            </div>

            <!-- Add New Checklist Field -->
            <div class="bg-base-100 shadow-md rounded-lg p-6 mt-8">
                <h3 class="text-lg font-semibold mb-4">Add New Checklist Field</h3>
                <form id="add-field-form" action="{{ route('checklist_fields.store') }}" method="POST">
                    @csrf
                    <input type="hidden" name="category_id" value="{{ $inventoryCategory->id }}">

                    <!-- Field Name -->
                    <div class="mb-4">
                        <label for="new-field-name" class="block text-sm font-medium text-base-content mb-2">Field
                            Name</label>
                        <input type="text" name="name" id="new-field-name" class="w-full input input-bordered"
                            required>
                    </div>

                    <!-- Field Description -->
                    <div class="mb-4">
                        <label for="new-field-description" class="block text-sm font-medium text-base-content mb-2">Field
                            Description</label>
                        <textarea name="description" id="new-field-description" rows="2" class="w-full textarea textarea-bordered"></textarea>
                    </div>

                    <!-- Field Type -->
                    <div class="mb-4">
                        <label for="new-field-type" class="block text-sm font-medium text-base-content mb-2">Field
                            Type</label>
                        <select name="type" id="new-field-type" class="w-full select select-bordered">
                            <option value="text">Text</option>
                            <option value="textarea">Textarea</option>
                            <option value="yes/no">Yes/No</option>
                            <option value="numeric">Numeric with Units</option>
                            <option value="select">Select List</option>
                            <option value="checkboxes">Checkboxes</option>
                            <option value="date">Date</option>
                        </select>
                    </div>

                    <!-- Unit Input -->
                    <div id="unit-container" class="mb-4 hidden">
                        <label for="unit" class="block text-sm font-medium text-base-content mb-2">Unit</label>
                        <input type="text" name="options[unit]" id="unit"
                            class="w-full input input-bordered">
                    </div>

                    <!-- Options Input -->
                    <div id="options-container" class="mb-4 hidden">
                        <label for="options" class="block text-sm font-medium text-base-content mb-2">Options (Comma
                            Separated)</label>
                        <input type="text" name="options[select_list]" id="options"
                            class="w-full input input-bordered">
                    </div>

                    <!-- Field Status -->
                    <div class="mb-4">
                        <label for="new-field-status"
                            class="block text-sm font-medium text-base-content mb-2">Status</label>
                        <select name="status" id="new-field-status" class="w-full select select-bordered">
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-right">
                        <button type="submit" class="btn btn-primary">
                            Add Field
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Save category fields automatically
        const categoryForm = document.getElementById('category-form');
        const categorySaveStatus = document.getElementById('category-save-status');
        let categorySaveTimeout;

        categoryForm.querySelectorAll('.auto-save-input').forEach(input => {
            input.addEventListener('input', () => {
                clearTimeout(categorySaveTimeout);
                categorySaveStatus.textContent = "Saving...";
                categorySaveTimeout = setTimeout(() => saveCategoryChanges(), 2000);
            });
        });

        function saveCategoryChanges() {
            const formData = new FormData(categoryForm);

            // Ensure the _method=PUT is included for Laravel resource routing
            formData.append('_method', 'PUT');

            fetch(categoryForm.action, {
                    method: 'POST', // Laravel expects POST with _method=PUT for updates
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        categorySaveStatus.textContent = "All changes saved";
                    } else {
                        categorySaveStatus.textContent = "Failed to save changes";
                        console.error('Server Error:', data);
                    }
                })
                .catch(error => {
                    categorySaveStatus.textContent = "Error saving changes";
                    console.error('Error:', error);
                });
        }


        // Auto-save checklist fields
        let saveTimeout;
        const saveStatus = document.getElementById('save-status');

        document.querySelectorAll('.auto-save-input').forEach(input => {
            input.addEventListener('input', () => {
                clearTimeout(saveTimeout);
                saveStatus.textContent = "Saving...";
                saveTimeout = setTimeout(() => autoSaveFields(), 2000);
            });
        });

        function autoSaveFields() {
            const form = document.getElementById('checklist-fields-form');
            const formData = new FormData(form);

            fetch(form.action, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: formData
                })
                .then(response => {
                    if (response.ok) {
                        saveStatus.textContent = "All changes saved";
                    } else {
                        response.text().then(text => console.error('Response Error:', text));
                        saveStatus.textContent = "Failed to save changes";
                    }
                })
                .catch(error => {
                    saveStatus.textContent = "Error saving changes";
                    console.error('Error:', error);
                });
        }



        const newFieldType = document.getElementById('new-field-type');
        const unitContainer = document.getElementById('unit-container');
        const optionsContainer = document.getElementById('options-container');

        newFieldType.addEventListener('change', () => {
            const type = newFieldType.value;

            // Toggle visibility for units and options based on type
            unitContainer.classList.toggle('hidden', type !== 'numeric');
            optionsContainer.classList.toggle('hidden', type !== 'select');

            optionsContainer.classList.toggle('hidden', type !== 'checkboxes');
        });
    </script>
</x-app-layout>
