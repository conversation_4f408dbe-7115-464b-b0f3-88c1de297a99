<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            Inventory Categories
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-base-100 border-b border-base-300">
                    @if(session('success'))
                    <div class="alert alert-success mb-4 p-4">
                        {{ session('success') }}
                    </div>
                    @endif

                    <!-- Create New Category Button -->
                    <div class="flex justify-between mb-4">
                        <a href="{{ route('inventory_categories.create') }}" class="btn btn-primary">
                            Create New Category
                        </a>
                        <a href="{{ request()->has('trashed') && request()->input('trashed') == 'true' 
                            ? route('inventory_categories.index') 
                            : route('inventory_categories.index', ['trashed' => 'true']) }}"
                            class="btn btn-neutral">
                            {{ request()->has('trashed') && request()->input('trashed') == 'true' ? 'View Active' : 'View Trash' }}
                        </a>
                    </div>

                    <!-- Full-width Striped Table -->
                    <table class="table table-zebra w-full">
                        <thead class="bg-base-200">
                            <tr>
                                <th class="border border-base-300">ID</th>
                                <th class="border border-base-300">Name</th>
                                <th class="border border-base-300 w-1/3">Description</th>
                                <th class="border border-base-300">Code</th>
                                <th class="border border-base-300">Status</th>
                                <th class="border border-base-300">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($categories as $category)
                            <tr>
                                <td class="border border-base-300">{{ $category->id }}</td>
                                <td class="border border-base-300">{{ $category->name }}</td>
                                <td class="border border-base-300">{{ $category->description }}</td>
                                <td class="border border-base-300">{{ $category->code }}</td>
                                <td class="border border-base-300">{{ $category->status ? 'Active' : 'Inactive' }}</td>
                                <td class="border border-base-300">
                                    @if(request()->has('trashed') && request()->input('trashed') == 'true')
                                    <form action="{{ route('inventory_categories.restore', $category->id) }}" method="POST" style="display:inline;">
                                        @csrf
                                        <button type="submit" class="btn btn-success btn-sm">
                                            Restore
                                        </button>
                                    </form>

                                    @else
                                    <a href="{{ route('inventory_categories.edit', $category) }}" class="btn btn-primary btn-sm">Edit</a>
                                    <form action="{{ route('inventory_categories.destroy', $category) }}" method="POST" style="display:inline;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-error btn-sm" onclick="return confirm('Are you sure?')">
                                            <i class="fa-sharp fa-trash"></i>
                                        </button>
                                    </form>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>