<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-white leading-tight">
            Certificate Creation Diagnostics
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h3 class="text-lg font-semibold mb-4">Certificate Creation Diagnostic Tool</h3>
                    
                    <div class="mb-6">
                        <p class="mb-2">This tool helps diagnose issues with certificate creation by:</p>
                        <ul class="list-disc pl-6 mb-4">
                            <li>Testing database connectivity</li>
                            <li>Verifying customer records are accessible</li>
                            <li>Creating a test certificate</li>
                            <li>Checking redirect functionality</li>
                        </ul>
                        
                        <div class="alert alert-warning mb-4">
                            <div class="flex items-center">
                                <i class="fa-sharp fa-triangle-exclamation mr-2"></i>
                                <span>This will create a real certificate in the database with a diagnostic prefix.</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <h4 class="font-semibold mb-2">Test Certificate Creation</h4>
                        <button id="runDiagnostic" class="btn btn-primary">Run Diagnostic Test</button>
                    </div>
                    
                    <div id="results" class="hidden">
                        <h4 class="font-semibold mb-2">Results</h4>
                        <div id="resultContent" class="p-4 border rounded-lg bg-gray-50">
                            <!-- Results will be displayed here -->
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <h4 class="font-semibold mb-2">Manual Certificate Creation</h4>
                        <p class="mb-4">You can also try creating a certificate manually:</p>
                        <a href="{{ route('certificates.create') }}" class="btn btn-secondary">Create New Certificate</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const runButton = document.getElementById('runDiagnostic');
            const results = document.getElementById('results');
            const resultContent = document.getElementById('resultContent');
            
            runButton.addEventListener('click', async function() {
                // Show loading state
                runButton.disabled = true;
                runButton.innerHTML = '<i class="fa-sharp fa-spinner fa-spin"></i> Running...';
                results.classList.add('hidden');
                
                try {
                    // Call the diagnostic endpoint
                    const response = await fetch('{{ route("diagnostics.certificate-creation") }}');
                    const data = await response.json();
                    
                    // Display results
                    results.classList.remove('hidden');
                    
                    if (data.success) {
                        resultContent.innerHTML = `
                            <div class="text-green-600 mb-4">
                                <i class="fa-sharp fa-circle-check mr-2"></i>
                                <strong>Success!</strong> Certificate created successfully.
                            </div>
                            <div class="mb-2">
                                <strong>Certificate ID:</strong> ${data.certificate.id}
                            </div>
                            <div class="mb-2">
                                <strong>Certificate Number:</strong> ${data.certificate.certificate_number}
                            </div>
                            <div class="mb-4">
                                <strong>Created At:</strong> ${new Date(data.certificate.created_at).toLocaleString()}
                            </div>
                            <div>
                                <a href="${data.edit_url}" class="btn btn-sm btn-primary">
                                    <i class="fa-sharp fa-edit mr-1"></i>
                                    View Certificate
                                </a>
                            </div>
                        `;
                    } else {
                        resultContent.innerHTML = `
                            <div class="text-red-600 mb-4">
                                <i class="fa-sharp fa-circle-xmark mr-2"></i>
                                <strong>Error:</strong> ${data.message}
                            </div>
                            <div class="mb-2">
                                <strong>Error Details:</strong> ${data.error}
                            </div>
                            <pre class="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">${data.trace}</pre>
                        `;
                    }
                } catch (error) {
                    // Handle fetch errors
                    results.classList.remove('hidden');
                    resultContent.innerHTML = `
                        <div class="text-red-600 mb-4">
                            <i class="fa-sharp fa-circle-xmark mr-2"></i>
                            <strong>Error:</strong> Failed to connect to diagnostic endpoint
                        </div>
                        <div class="mb-2">
                            <strong>Error Details:</strong> ${error.message}
                        </div>
                    `;
                } finally {
                    // Reset button state
                    runButton.disabled = false;
                    runButton.innerHTML = 'Run Diagnostic Test';
                }
            });
        });
    </script>
    @endpush
</x-app-layout>
