<x-app-layout
    page-title="Notifications Management"
    page-icon="fa-sharp fa-solid fa-bell"
    :primary-buttons="[
        [
            'type' => 'create',
            'route' => route('notifications.create'),
            'text' => 'Create Notification',
            'permission' => 'create_notifications'
        ]
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto sm:px-2 lg:px-4 space-y-8">
            <!-- Header Card -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-base-content">Notifications Management</h1>
                            <p class="text-base-content/70 mt-1 text-lg">Manage and monitor system notifications</p>
                        </div>
                        <div class="hidden lg:flex items-center gap-2 text-base-content/60">
                            <div class="stat">
                                <div class="stat-title text-xs">Total Notifications</div>
                                <div class="stat-value text-2xl">{{ $notifications->total() }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications List -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-solid fa-bell text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">All Notifications</h4>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="table table-zebra w-full">
                <thead>
                    <tr class="bg-base-200">
                        <th class="text-left">Title</th>
                        <th class="text-left">Urgency</th>
                        <th class="text-left">Target</th>
                        <th class="text-left">Created By</th>
                        <th class="text-left">Created</th>
                        <th class="text-left">Expires</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($notifications as $notification)
                        <tr>
                            <td>
                                <div class="flex items-center gap-2">
                                    <i class="fa-sharp {{ $notification->getUrgencyIcon() }} text-{{ $notification->urgency === 'critical' ? 'error' : ($notification->urgency === 'high' ? 'warning' : ($notification->urgency === 'normal' ? 'success' : 'info')) }}"></i>
                                    <div>
                                        <div class="font-medium">{{ $notification->title }}</div>
                                        <div class="text-sm text-base-content/70 truncate max-w-xs">
                                            {{ Str::limit($notification->message, 60) }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-{{ $notification->urgency === 'critical' ? 'error' : ($notification->urgency === 'high' ? 'warning' : ($notification->urgency === 'normal' ? 'success' : 'info')) }} badge-sm">
                                    {{ ucfirst($notification->urgency) }}
                                </span>
                            </td>
                            <td>
                                <div class="text-sm">
                                    @if($notification->target_type === 'all_users')
                                        <span class="badge badge-primary badge-sm">All Users</span>
                                    @elseif($notification->target_type === 'user_group')
                                        <span class="badge badge-secondary badge-sm">User Groups</span>
                                        <div class="text-xs text-base-content/70 mt-1">
                                            {{ count($notification->target_ids ?? []) }} group(s)
                                        </div>
                                    @else
                                        <span class="badge badge-accent badge-sm">Specific Users</span>
                                        <div class="text-xs text-base-content/70 mt-1">
                                            {{ count($notification->target_ids ?? []) }} user(s)
                                        </div>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="text-sm">{{ $notification->creator->name }}</div>
                            </td>
                            <td>
                                <div class="text-sm">{{ $notification->created_at->format('M j, Y') }}</div>
                                <div class="text-xs text-base-content/70">{{ $notification->created_at->format('g:i A') }}</div>
                            </td>
                            <td>
                                @if($notification->expires_at)
                                    <div class="text-sm {{ $notification->isExpired() ? 'text-error' : '' }}">
                                        {{ $notification->expires_at->format('M j, Y') }}
                                    </div>
                                    <div class="text-xs text-base-content/70">
                                        {{ $notification->expires_at->format('g:i A') }}
                                    </div>
                                    @if($notification->isExpired())
                                        <span class="badge badge-error badge-xs">Expired</span>
                                    @endif
                                @else
                                    <span class="text-base-content/50">Never</span>
                                @endif
                            </td>
                            <td class="text-center">
                                <div class="flex justify-center gap-1">
                                    @perms('manage_notifications')
                                        <button 
                                            class="btn btn-ghost btn-xs text-error hover:bg-error hover:text-error-content"
                                            onclick="deleteNotification({{ $notification->id }})"
                                            title="Delete Notification"
                                        >
                                            <i class="fa-sharp fa-solid fa-trash text-xs"></i>
                                        </button>
                                    @endperms
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="text-center py-8 text-base-content/70">
                                <i class="fa-sharp fa-bell text-4xl mb-4 block"></i>
                                <p class="text-lg">No notifications found</p>
                                <p class="text-sm">Create your first notification to get started.</p>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
                </div>

                <!-- Pagination -->
                @if($notifications->hasPages())
                    <div class="px-6 py-4 border-t border-base-300/50 bg-base-50">
                        <x-pagination :paginator="$notifications" :pagination="session('pagination', 10)" />
                    </div>
                @endif
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function deleteNotification(notificationId) {
            if (confirm('Are you sure you want to delete this notification? This action cannot be undone.')) {
                fetch(`/notifications/${notificationId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        const toast = document.createElement('div');
                        toast.className = 'toast toast-top toast-center';
                        toast.innerHTML = `
                            <div class="alert alert-success">
                                <i class="fa-sharp fa-check-circle"></i>
                                <span>${data.message}</span>
                            </div>
                        `;
                        document.body.appendChild(toast);

                        // Remove toast after 3 seconds
                        setTimeout(() => toast.remove(), 3000);

                        // Reload page to update list
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the notification.');
                });
            }
        }
    </script>
    @endpush
</x-app-layout>
