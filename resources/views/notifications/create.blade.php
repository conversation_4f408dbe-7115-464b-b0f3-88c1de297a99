<x-app-layout page-title="Create Notification" page-icon="fa-sharp fa-solid fa-bell-plus" :breadcrumbs="[
    ['name' => 'Notifications', 'route' => 'notifications.index', 'icon' => 'fa-sharp fa-solid fa-bell'],
    ['name' => 'Create', 'icon' => 'fa-sharp fa-solid fa-plus'],
]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Introduction Card -->
            <div
                class="bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
                <!-- Decorative Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 left-0 w-32 h-32 bg-primary rounded-full -translate-x-16 -translate-y-16">
                    </div>
                    <div
                        class="absolute bottom-0 right-0 w-24 h-24 bg-secondary rounded-full translate-x-12 translate-y-12">
                    </div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-start gap-4">
                        <div class="avatar avatar-placeholder">
                            <div
                                class="bg-gradient-to-br from-primary to-primary-focus text-primary-content w-12 rounded-xl shadow-lg">
                                <i class="fa-sharp fa-solid fa-bell-plus text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-2xl font-bold text-base-content mb-2">Create New Notification</h3>
                            <p class="text-base-content/70 text-base leading-relaxed">Send notifications to users, user
                                groups, or all users with different urgency levels and optional links.</p>
                        </div>
                    </div>
                </div>
            </div>
            <form id="notificationForm" class="space-y-6">
                @csrf

                <!-- Basic Information -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-solid fa-info-circle text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Basic Information</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="md:col-span-2">
                                <label class="label">
                                    <span class="label-text font-medium">Title <span class="text-error">*</span></span>
                                </label>
                                <input type="text" name="title" class="input input-bordered w-full"
                                    placeholder="Enter notification title" required />
                            </div>

                            <div class="md:col-span-2">
                                <label class="label">
                                    <span class="label-text font-medium">Message <span
                                            class="text-error">*</span></span>
                                </label>
                                <textarea name="message" class="textarea textarea-bordered w-full h-24" placeholder="Enter notification message"
                                    required></textarea>
                            </div>

                            <div>
                                <label class="label">
                                    <span class="label-text font-medium">Urgency Level</span>
                                </label>
                                <select name="urgency" class="select select-bordered w-full">
                                    @foreach ($urgencyLevels as $value => $label)
                                        <option value="{{ $value }}" {{ $value === 'normal' ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Target Audience -->
                <div class="card bg-base-100 shadow-md">
                    <div
                        class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-solid fa-users text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Target Audience</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-6">
                        <div>
                            <label class="label">
                                <span class="label-text font-medium">Send To</span>
                            </label>
                            <select name="target_type" id="targetType" class="select select-bordered w-full">
                                @foreach ($targetTypes as $value => $label)
                                    <option value="{{ $value }}">{{ $label }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>


                    <!-- User Groups Selection -->
                    <div id="userGroupsSection" class="hidden">
                        <label class="label">
                            <span class="label-text font-medium">Select User Groups</span>
                        </label>
                        <div
                            class="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-48 overflow-y-auto border border-base-300 rounded-lg p-3">
                            @foreach ($userGroups as $group)
                                <label class="label cursor-pointer justify-start gap-2">
                                    <input type="checkbox" name="target_ids[]" value="{{ $group->id }}"
                                        class="checkbox checkbox-sm" />
                                    <span class="label-text">{{ $group->name }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>

                    <!-- Specific Users Selection -->
                    <div id="specificUsersSection" class="hidden">
                        <label class="label">
                            <span class="label-text font-medium">Select Users</span>
                        </label>
                        <div
                            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 max-h-64 overflow-y-auto border border-base-300 rounded-lg p-3">
                            @foreach ($users as $user)
                                <label class="label cursor-pointer justify-start gap-2">
                                    <input type="checkbox" name="target_ids[]" value="{{ $user->id }}"
                                        class="checkbox checkbox-sm" />
                                    <span class="label-text">{{ $user->name }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>
                </div>
        

        <!-- Optional Settings -->
        <div class="card bg-base-100 shadow-md">
            <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                <div class="flex items-center gap-3">
                    <div class="avatar avatar-placeholder">
                        <div class="bg-accent text-accent-content w-8 rounded-lg">
                            <i class="fa-sharp fa-solid fa-cog text-sm"></i>
                        </div>
                    </div>
                    <h4 class="text-lg font-semibold text-base-content">Optional Settings</h4>
                </div>
            </div>
            <div class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">Link URL</span>
                        </label>
                        <input type="url" name="link_url" class="input input-bordered w-full"
                            placeholder="https://example.com" />
                    </div>

                    <div>
                        <label class="label">
                            <span class="label-text font-medium">Link Text</span>
                        </label>
                        <input type="text" name="link_text" class="input input-bordered w-full"
                            placeholder="Click here" />
                    </div>

                    <div>
                        <label class="label">
                            <span class="label-text font-medium">Expires At</span>
                        </label>
                        <input type="datetime-local" name="expires_at" class="input input-bordered w-full" />
                    </div>

                    <div class="flex flex-col gap-2">
                        <label class="label cursor-pointer justify-start gap-2">
                            <input type="checkbox" name="is_dismissible" class="checkbox" checked />
                            <span class="label-text">Allow users to dismiss</span>
                        </label>

                        <label class="label cursor-pointer justify-start gap-2">
                            <input type="checkbox" name="auto_dismiss" class="checkbox" />
                            <span class="label-text">Auto-dismiss when read</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Section -->
        <div class="divider my-8">
            <span class="text-base-content/50 font-medium">Ready to send?</span>
        </div>

        <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg p-6">
            <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                <div class="flex items-center gap-3">
                    <div class="avatar avatar-placeholder">
                        <div class="bg-base-300 text-base-content w-10 rounded-lg">
                            <i class="fa-sharp fa-solid fa-info-circle text-sm"></i>
                        </div>
                    </div>
                    <div class="text-center sm:text-left">
                        <p class="font-medium text-base-content">Review your notification</p>
                        <p class="text-sm text-base-content/60">Make sure all details are correct before sending</p>
                    </div>
                </div>
                <div class="flex gap-3 w-full sm:w-auto">
                    <a href="{{ route('notifications.index') }}"
                        class="btn btn-outline btn-lg gap-2 flex-1 sm:flex-none">
                        <i class="fa-sharp fa-solid fa-arrow-left"></i>
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none"
                        id="submitBtn">
                        <i class="fa-sharp fa-solid fa-paper-plane"></i>
                        Send Notification
                    </button>
                </div>
            </div>
        </div>
        </form>
    </div>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const targetTypeSelect = document.getElementById('targetType');
                const userGroupsSection = document.getElementById('userGroupsSection');
                const specificUsersSection = document.getElementById('specificUsersSection');
                const form = document.getElementById('notificationForm');
                const submitBtn = document.getElementById('submitBtn');

                // Handle target type changes
                targetTypeSelect.addEventListener('change', function() {
                    const targetType = this.value;

                    // Hide all sections first
                    userGroupsSection.classList.add('hidden');
                    specificUsersSection.classList.add('hidden');

                    // Clear all checkboxes
                    document.querySelectorAll('input[name="target_ids[]"]').forEach(checkbox => {
                        checkbox.checked = false;
                    });

                    // Show relevant section
                    if (targetType === 'user_group') {
                        userGroupsSection.classList.remove('hidden');
                    } else if (targetType === 'specific_users') {
                        specificUsersSection.classList.remove('hidden');
                    }
                });

                // Handle form submission
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Disable submit button
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Sending...';

                    const formData = new FormData(form);

                    // Handle boolean checkboxes properly
                    const isDismissible = document.querySelector('input[name="is_dismissible"]').checked;
                    const autoDismiss = document.querySelector('input[name="auto_dismiss"]').checked;

                    // Remove any existing checkbox values and set proper boolean values
                    formData.delete('is_dismissible');
                    formData.delete('auto_dismiss');
                    formData.append('is_dismissible', isDismissible ? 'true' : 'false');
                    formData.append('auto_dismiss', autoDismiss ? 'true' : 'false');

                    fetch('{{ route('notifications.store') }}', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                    .getAttribute('content'),
                                'Accept': 'application/json'
                            }
                        })
                        .then(response => {
                            if (!response.ok) {
                                return response.json().then(data => {
                                    let errorMessage = data.error || data.message || 'Server error';

                                    // Handle validation errors
                                    if (data.errors) {
                                        const errorMessages = Object.values(data.errors).flat();
                                        errorMessage = errorMessages.join(', ');
                                    }

                                    throw new Error(errorMessage);
                                });
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.success) {
                                // Show success message
                                const toast = document.createElement('div');
                                toast.className = 'toast toast-top toast-center';
                                toast.innerHTML = `
                            <div class="alert alert-success">
                                <i class="fa-sharp fa-check-circle"></i>
                                <span>${data.message}</span>
                            </div>
                        `;
                                document.body.appendChild(toast);

                                // Redirect to notifications index
                                setTimeout(() => {
                                    window.location.href = '{{ route('notifications.index') }}';
                                }, 1500);
                            } else {
                                // Show error message
                                const toast = document.createElement('div');
                                toast.className = 'toast toast-top toast-center';
                                toast.innerHTML = `
                            <div class="alert alert-error">
                                <i class="fa-sharp fa-exclamation-circle"></i>
                                <span>${data.error || 'An error occurred'}</span>
                            </div>
                        `;
                                document.body.appendChild(toast);

                                setTimeout(() => toast.remove(), 5000);

                                // Re-enable submit button
                                submitBtn.disabled = false;
                                submitBtn.innerHTML =
                                    '<i class="fa-sharp fa-solid fa-paper-plane"></i> Send Notification';
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);

                            // Show error message
                            const toast = document.createElement('div');
                            toast.className = 'toast toast-top toast-center';
                            toast.innerHTML = `
                        <div class="alert alert-error">
                            <i class="fa-sharp fa-exclamation-circle"></i>
                            <span>${error.message || 'An error occurred while sending the notification.'}</span>
                        </div>
                    `;
                            document.body.appendChild(toast);

                            setTimeout(() => toast.remove(), 5000);

                            // Re-enable submit button
                            submitBtn.disabled = false;
                            submitBtn.innerHTML =
                                '<i class="fa-sharp fa-solid fa-paper-plane"></i> Send Notification';
                        });
                });
            });
        </script>
    @endpush
</x-app-layout>
