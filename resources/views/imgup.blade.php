<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <x-head :title="'ETRFlow Image Uploader'"/>
<body class="bg-gray-100">
    <div class="min-h-screen flex flex-col items-center justify-center">
        <div class="w-full max-w-md bg-white p-6 rounded shadow-md">

            <h1 class="text-2xl font-bold text-center mb-6">ETRFlow Inventory Quickstart</h1>
            <div class="grid grid-cols-2 gap-4">
                <a href="https://www.passmark.com/products/performancetest/download.php" target="_blank" class="btn btn-primary w-full btn-sm mb-2">
                    <i class="fa-sharp fa-download"></i>
                    PassMark</a>
                <a href="https://www.hwinfo.com/download/" target="_blank" class="btn btn-primary w-full btn-sm mb-2">
                    <i class="fa-sharp fa-download"></i>
                    HWInfo</a>
                    </div>
            </div>
            <div class="w-full max-w-md bg-white p-6 rounded shadow-md mt-4">
            <h2 class="text-2xl font-bold text-center mb-6">Upload Inventory Image</h1>

            @if ($errors->any())
                <div class="bg-red-100 text-red-600 p-3 rounded mb-4">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            @if (session('success'))
                <div class="bg-green-100 text-green-600 p-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            <form action="{{ url('/imgup') }}" method="POST" enctype="multipart/form-data" class="space-y-4">
                @csrf
                <div>
                    <label for="identifier" class="block font-medium text-gray-700">ID or Asset Tag</label>
                    <input type="text" name="identifier" id="identifier" class="input input-bordered w-full" required>
                </div>
                <div>
                    <label for="passcode" class="block font-medium text-gray-700">Passcode</label>
                    <input type="password" name="passcode" id="passcode" class="input input-bordered w-full" required>
                </div>
                <div>
                    <label for="photo" class="block font-medium text-gray-700">Photo</label>
                    <input type="file" name="photo" id="photo" class="file-input file-input-bordered w-full" required>
                </div>
                <div>
                    <button type="submit" class="btn btn-success w-full">
                        <i class="fa-sharp fa-upload"></i>
                        Upload</button>
                </div>
            </form>
        </div>
        <div class="w-full max-w-md bg-white p-6 rounded shadow-md mt-6">
            <h2 class="text-2xl font-bold text-center mb-6">Remember To...</h2>
            <a class="btn btn-primary w-full" href="https://mostafa-abbasi.github.io/KeyboardTester/" target="_blank">
                <i class="fa-sharp fa-keyboard"></i>
                Test Keyboard</a>
                <div class="mt-4"></div>
                <p class="text-lg font-semibold">Is windows activated?</p>
                <p class="text-xs text-gray-600">Run this command in PowerShell if not</p>
                <pre id="powershell-command" class="bg-gray-100 p-4 rounded text-sm font-mono mt-2">irm https://get.activated.win | iex</pre>
                <button onclick="copyToClipboard()" class="btn btn-sm btn-secondary mt-2">
                    <i class="fa-sharp fa-copy"></i>
                    Copy PowerShell Command</button>
                <div class="mt-4"></div>
                <p>Don't forget to test all ports and buttons!</p>
        </div>
    </div>
    <script>
        function copyToClipboard() {
            const command = document.getElementById('powershell-command').innerText;
            navigator.clipboard.writeText(command).then(() => {
                alert('Command copied to clipboard');
            }).catch(err => {
                console.error('Failed to copy: ', err);
            });
        }
    </script>
</body>
</html>