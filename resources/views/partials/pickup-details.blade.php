{{-- 
    Reusable Pickup Details Partial
    
    Props:
    - $pickupData: Array of pickup details (from getPickupDetailsArray())
    - $title: Optional title for the section (default: "Pickup Details")
    - $showMetadata: Whether to show metadata like submission info (default: false)
    - $compact: Whether to use compact display (default: false)
--}}

@props([
    'pickupData' => [],
    'title' => 'Pickup Details',
    'showMetadata' => false,
    'compact' => false,
    'distanceData' => null,
    'warehouseLocation' => null
])

@php
    // Ensure we have the expected structure
    $contact = $pickupData['contact'] ?? [];
    $pickup = $pickupData['pickup'] ?? [];
    $event = $pickupData['event'] ?? [];
    $meta = $pickupData['meta'] ?? [];
    $submission = $pickupData['submission'] ?? [];
    $guidedDetails = $pickupData['guided_details'] ?? [];
    $structuredDetails = $pickupData['structured_details'] ?? [];
@endphp

<div class="card bg-base-100 shadow-md">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
        <div class="flex items-center gap-3">
            <div class="avatar avatar-placeholder">
                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                    <i class="fa-sharp fa-truck text-sm"></i>
                </div>
            </div>
            <h4 class="text-lg font-semibold text-base-content">{{ $title }}</h4>
        </div>
    </div>

    <!-- Content Section -->
    <div class="p-6">
        @if(!$compact)
            <!-- Full Details View -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-6">
                <!-- Pickup Address -->
                @if(!empty($pickup['address']))
                    <div class="flex items-start gap-2">
                        <i class="fa-sharp fa-location-dot text-secondary mt-0.5"></i>
                        <div>
                            <span class="font-medium">Pickup Address:</span>
                            <div class="text-base-content">{{ $pickup['address'] }}</div>
                            @php
                                $warehouseLocation = \App\Models\GlobalConfig::getValue('warehouse_location');
                            @endphp
                            @if($warehouseLocation)
                                <div class="mt-2">
                                    <a href="https://www.google.com/maps/dir/{{ urlencode($warehouseLocation) }}/{{ urlencode($pickup['address']) }}" 
                                       target="_blank" 
                                       class="btn btn-sm btn-outline btn-secondary gap-2">
                                        <i class="fa-sharp fa-route"></i>
                                        Get Directions
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- Items to Pickup -->
                @if(!empty($pickup['items']))
                    <div class="flex items-start gap-2">
                        <i class="fa-sharp fa-box text-secondary mt-0.5"></i>
                        <div>
                            <span class="font-medium">Items to Pickup:</span>
                            <div class="text-base-content">{{ $pickup['items'] }}</div>
                        </div>
                    </div>
                @endif

                <!-- Quantity -->
                @if(!empty($pickup['quantity']))
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-hashtag text-secondary"></i>
                        <span class="font-medium">Quantity:</span>
                        <span>{{ $pickup['quantity'] }}</span>
                    </div>
                @endif

                <!-- Preferred Date -->
                @if(!empty($pickup['preferred_date']))
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-calendar text-secondary"></i>
                        <span class="font-medium">Preferred Date:</span>
                        @php
                            $timezone = \App\Models\GlobalConfig::getTimeZone();
                        @endphp
                        <span>{{ \Carbon\Carbon::parse($pickup['preferred_date'])->setTimezone($timezone)->format('M j, Y g:i A') }}</span>
                    </div>
                @endif

                <!-- Staff Needed (Event Details) -->
                @if(!empty($event['staff_needed']))
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-users text-secondary"></i>
                        <span class="font-medium">Staff Needed:</span>
                        <span>{{ $event['staff_needed'] }}</span>
                    </div>
                @endif

                <!-- Assigned Driver (Event Details) -->
                @if(!empty($event['assigned_driver_id']))
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-user text-secondary"></i>
                        <span class="font-medium">Assigned Driver:</span>
                        <span>
                            @php
                                $driver = \App\Models\User::find($event['assigned_driver_id']);
                            @endphp
                            {{ $driver ? $driver->name : 'Driver #' . $event['assigned_driver_id'] }}
                        </span>
                    </div>
                @endif

                <!-- Distance and Travel Time (if available) -->
                @if(isset($distanceData) && $distanceData)
                    @if($distanceData['success'])
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-route text-secondary"></i>
                            <span class="font-medium">Distance from Warehouse:</span>
                            <span>{{ $distanceData['distance_text'] }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-clock text-secondary"></i>
                            <span class="font-medium">Estimated Travel Time:</span>
                            <span>{{ $distanceData['duration_text'] }}</span>
                        </div>
                    @else
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-route text-warning-content"></i>
                            <span class="font-medium">Distance from Warehouse:</span>
                            <span class="text-warning-content">{{ $distanceData['distance_text'] ?? 'Could not calculate' }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-clock text-warning-content"></i>
                            <span class="font-medium">Estimated Travel Time:</span>
                            <span class="text-warning-content">{{ $distanceData['duration_text'] ?? 'Could not calculate' }}</span>
                        </div>
                    @endif
                @elseif(isset($warehouseLocation) && empty($warehouseLocation))
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-route text-base-content/50"></i>
                        <span class="font-medium">Distance from Warehouse:</span>
                        <span class="text-base-content/50">Warehouse location not configured</span>
                    </div>
                @endif
            </div>

            <!-- Property Location Details -->
            @if(!empty($pickup['property_location_details']))
                <div class="border-t border-base-300/50 pt-4 mb-4">
                    <div class="flex items-start gap-2">
                        <i class="fa-sharp fa-map-location-dot text-secondary mt-0.5"></i>
                        <div>
                            <span class="font-medium">Property Location Details:</span>
                            <div class="text-base-content mt-1">{{ $pickup['property_location_details'] }}</div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Guided Pickup Details -->
            @if(!empty($guidedDetails) || !empty($structuredDetails))
                <div class="border-t border-base-300/50 pt-4 mb-4">
                    <div class="flex items-start gap-2">
                        <i class="fa-sharp fa-clipboard-list text-secondary mt-0.5"></i>
                        <div class="w-full">
                            <span class="font-medium">Pickup Details:</span>
                            <div class="text-base-content mt-2 space-y-3">

                                <!-- Load Size -->
                                @if(!empty($guidedDetails['load_size']) || !empty($structuredDetails['load_info']['size']))
                                    <div class="flex items-start gap-2">
                                        <i class="fa-sharp fa-truck-loading text-primary text-sm mt-0.5"></i>
                                        <div>
                                            <span class="font-medium text-sm">Load Size:</span>
                                            @php
                                                $loadSize = $guidedDetails['load_size'] ?? $structuredDetails['load_info']['size'] ?? null;
                                                $loadDescription = $structuredDetails['load_info']['description'] ?? null;
                                                $loadLabels = [
                                                    'small' => 'Small Load',
                                                    'medium' => 'Medium Load',
                                                    'large' => 'Large or Heavy Load'
                                                ];
                                            @endphp
                                            <div class="text-sm">
                                                <span class="badge badge-primary badge-sm">{{ $loadLabels[$loadSize] ?? ucfirst($loadSize) }}</span>
                                                @if($loadDescription)
                                                    <div class="text-xs text-base-content/70 mt-1">{{ $loadDescription }}</div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <!-- Item Types -->
                                @if(!empty($guidedDetails['item_types']) || !empty($structuredDetails['items']['types']))
                                    <div class="flex items-start gap-2">
                                        <i class="fa-sharp fa-boxes text-primary text-sm mt-0.5"></i>
                                        <div>
                                            <span class="font-medium text-sm">Item Types:</span>
                                            @php
                                                $itemTypes = $guidedDetails['item_types'] ?? $structuredDetails['items']['types'] ?? [];
                                                $itemLabels = $structuredDetails['items']['type_labels'] ?? [];
                                                if (empty($itemLabels) && !empty($itemTypes)) {
                                                    $typeLabels = [
                                                        'small_electronics' => 'Small Electronics',
                                                        'appliances' => 'Appliances',
                                                        'peripheral_devices' => 'Peripheral Devices',
                                                        'batteries' => 'Batteries',
                                                        'crt_tvs' => 'CRT TVs',
                                                        'flatscreen_tvs' => 'Flatscreen TV(s)',
                                                        'tote_swap' => 'Tote Swap',
                                                        'gaylord_swap' => 'Gaylord Swap',
                                                        'servers' => 'Servers',
                                                        'laptops' => 'Laptops',
                                                        'desktops' => 'Desktops',
                                                        'large_appliances' => 'Large Appliances',
                                                        'other' => 'Other',
                                                    ];
                                                    $itemLabels = array_map(fn($type) => $typeLabels[$type] ?? ucfirst($type), $itemTypes);
                                                }
                                            @endphp
                                            <div class="text-sm">
                                                <div class="flex flex-wrap gap-1 mt-1">
                                                    @foreach($itemLabels as $label)
                                                        <span class="badge badge-outline badge-sm">{{ $label }}</span>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <!-- Item Specifics -->
                                @if(!empty($guidedDetails['item_specifics']) || !empty($structuredDetails['items']['specifics']))
                                    <div class="flex items-start gap-2">
                                        <i class="fa-sharp fa-list-ul text-primary text-sm mt-0.5"></i>
                                        <div>
                                            <span class="font-medium text-sm">Item Specifics:</span>
                                            <div class="text-sm mt-1">{{ $guidedDetails['item_specifics'] ?? $structuredDetails['items']['specifics'] }}</div>
                                        </div>
                                    </div>
                                @endif

                                <!-- Accessibility Level -->
                                @if(!empty($guidedDetails['accessibility_level']) || !empty($structuredDetails['accessibility']['level']))
                                    <div class="flex items-start gap-2">
                                        <i class="fa-sharp fa-universal-access text-primary text-sm mt-0.5"></i>
                                        <div>
                                            <span class="font-medium text-sm">Accessibility:</span>
                                            @php
                                                $accessLevel = $guidedDetails['accessibility_level'] ?? $structuredDetails['accessibility']['level'] ?? null;
                                                $accessDescription = $structuredDetails['accessibility']['description'] ?? null;
                                                $accessLabels = [
                                                    'easy' => 'Easy Access',
                                                    'moderate' => 'Moderate Access',
                                                    'difficult' => 'Difficult Access'
                                                ];
                                                $accessColors = [
                                                    'easy' => 'badge-success',
                                                    'moderate' => 'badge-warning',
                                                    'difficult' => 'badge-error'
                                                ];
                                            @endphp
                                            <div class="text-sm">
                                                <span class="badge {{ $accessColors[$accessLevel] ?? 'badge-neutral' }} badge-sm">{{ $accessLabels[$accessLevel] ?? ucfirst($accessLevel) }}</span>
                                                @if($accessDescription)
                                                    <div class="text-xs text-base-content/70 mt-1">{{ $accessDescription }}</div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <!-- Driver Instructions -->
                                @if(!empty($guidedDetails['driver_instructions']) || !empty($structuredDetails['instructions']['driver_arrival']))
                                    <div class="flex items-start gap-2">
                                        <i class="fa-sharp fa-clipboard-list text-primary text-sm mt-0.5"></i>
                                        <div>
                                            <span class="font-medium text-sm">Driver Instructions:</span>
                                            <div class="text-sm mt-1">{{ $guidedDetails['driver_instructions'] ?? $structuredDetails['instructions']['driver_arrival'] }}</div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Additional Notes -->
            @if(!empty($pickup['other_notes']) || !empty($pickup['notes']))
                <div class="border-t border-base-300/50 pt-4 mb-4">
                    <div class="flex items-start gap-2">
                        <i class="fa-sharp fa-note-sticky text-secondary mt-0.5"></i>
                        <div>
                            <span class="font-medium">Additional Notes:</span>
                            <div class="text-base-content mt-1">
                                {{ $pickup['other_notes'] ?? $pickup['notes'] }}
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Contact Information (if different from customer) -->
            @if(!empty($contact['name']) || !empty($contact['phone']) || !empty($contact['email']))
                <div class="border-t border-base-300/50 pt-4 mb-4">
                    <div class="flex items-start gap-2">
                        <i class="fa-sharp fa-address-card text-secondary mt-0.5"></i>
                        <div>
                            <span class="font-medium">Contact Information:</span>
                            <div class="text-base-content mt-1 space-y-1">
                                @if(!empty($contact['name']))
                                    <div><strong>Name:</strong> {{ $contact['name'] }}</div>
                                @endif
                                @if(!empty($contact['business_name']))
                                    <div><strong>Business:</strong> {{ $contact['business_name'] }}</div>
                                @endif
                                @if(!empty($contact['phone']))
                                    <div><strong>Phone:</strong> <a href="tel:{{ $contact['phone'] }}" class="link link-primary">{{ $contact['phone'] }}</a></div>
                                @endif
                                @if(!empty($contact['email']))
                                    <div><strong>Email:</strong> <a href="mailto:{{ $contact['email'] }}" class="link link-primary">{{ $contact['email'] }}</a></div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Metadata (if requested) -->
            @if($showMetadata && (!empty($meta) || !empty($submission)))
                <div class="border-t border-base-300/50 pt-4">
                    <div class="flex items-start gap-2">
                        <i class="fa-sharp fa-info-circle text-secondary mt-0.5"></i>
                        <div>
                            <span class="font-medium">Request Information:</span>
                            <div class="text-base-content mt-1 text-xs space-y-1">
                                @if(!empty($meta['request_id']))
                                    <div><strong>Request ID:</strong> #{{ $meta['request_id'] }}</div>
                                @endif
                                @if(!empty($meta['submitted_at']))
                                    @php
                                        $timezone = \App\Models\GlobalConfig::getTimeZone();
                                    @endphp
                                    <div><strong>Submitted:</strong> {{ \Carbon\Carbon::parse($meta['submitted_at'])->setTimezone($timezone)->format('M j, Y g:i A') }}</div>
                                @endif
                                @if(!empty($submission['source']))
                                    <div><strong>Source:</strong> {{ ucfirst(str_replace('_', ' ', $submission['source'])) }}</div>
                                @endif
                                @if(!empty($event['created_by']))
                                    @php
                                        $creator = \App\Models\User::find($event['created_by']);
                                    @endphp
                                    <div><strong>Event Created By:</strong> {{ $creator ? $creator->name : 'User #' . $event['created_by'] }}</div>
                                @endif
                                @if(!empty($event['created_at']))
                                    @php
                                        $timezone = \App\Models\GlobalConfig::getTimeZone();
                                    @endphp
                                    <div><strong>Event Created:</strong> {{ \Carbon\Carbon::parse($event['created_at'])->setTimezone($timezone)->format('M j, Y g:i A') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        @else
            <!-- Compact View -->
            <div class="space-y-2 text-sm">
                @if(!empty($pickup['items']))
                    <div><strong>Items:</strong> {{ Str::limit($pickup['items'], 50) }}</div>
                @endif
                @if(!empty($pickup['quantity']))
                    <div><strong>Quantity:</strong> {{ $pickup['quantity'] }}</div>
                @endif
                @if(!empty($guidedDetails['load_size']) || !empty($structuredDetails['load_info']['size']))
                    @php
                        $loadSize = $guidedDetails['load_size'] ?? $structuredDetails['load_info']['size'] ?? null;
                        $loadLabels = ['small' => 'Small Load', 'medium' => 'Medium Load', 'large' => 'Large Load'];
                    @endphp
                    <div><strong>Load Size:</strong> {{ $loadLabels[$loadSize] ?? ucfirst($loadSize) }}</div>
                @endif
                @if(!empty($guidedDetails['item_specifics']) || !empty($structuredDetails['items']['specifics']))
                    <div><strong>Item Specifics:</strong> {{ Str::limit($guidedDetails['item_specifics'] ?? $structuredDetails['items']['specifics'], 50) }}</div>
                @endif
                @if(!empty($pickup['address']))
                    <div><strong>Address:</strong> {{ Str::limit($pickup['address'], 50) }}</div>
                @endif
                @if(!empty($pickup['other_notes']) || !empty($pickup['notes']))
                    <div><strong>Notes:</strong> {{ Str::limit($pickup['other_notes'] ?? $pickup['notes'], 50) }}</div>
                @endif
            </div>
        @endif
    </div>
</div>
