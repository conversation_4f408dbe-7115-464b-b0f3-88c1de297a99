<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f4f4f4;
            padding: 20px;
            text-align: center;
            border-radius: 5px;
        }
        .content {
            margin: 20px 0;
        }
        .details {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .detail-row {
            margin: 10px 0;
        }
        .label {
            font-weight: bold;
            color: #555;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #777;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
        }
        .button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ $title }}</h1>
    </div>

    <div class="content">
        <p>A pickup request has been {{ $type === 'new_request' ? 'submitted' : 'updated' }}.</p>

        <div class="details">
            <h2>Pickup Request Details</h2>
            
            <div class="detail-row">
                <span class="label">Request ID:</span> #{{ $pickupRequest->id }}
            </div>

            <div class="detail-row">
                <span class="label">Status:</span> {{ ucfirst($pickupRequest->status) }}
            </div>

            <div class="detail-row">
                <span class="label">Contact Name:</span> {{ $pickupRequest->contact_name }}
            </div>

            @if($pickupRequest->business_name)
            <div class="detail-row">
                <span class="label">Business Name:</span> {{ $pickupRequest->business_name }}
            </div>
            @endif

            <div class="detail-row">
                <span class="label">Email:</span> {{ $pickupRequest->email }}
            </div>

            <div class="detail-row">
                <span class="label">Phone:</span> {{ $pickupRequest->phone }}
            </div>

            <div class="detail-row">
                <span class="label">Pickup Date/Time:</span> {{ $pickupRequest->preferred_pickup_date->format('l, F j, Y \a\t g:i A') }}
            </div>

            <div class="detail-row">
                <span class="label">Pickup Address:</span><br>
                {{ $pickupRequest->pickup_address }}
            </div>

            @if($pickupRequest->property_location_details)
            <div class="detail-row">
                <span class="label">Property Location Details:</span><br>
                {{ $pickupRequest->property_location_details }}
            </div>
            @endif

            @if($pickupRequest->load_size)
            <div class="detail-row">
                <span class="label">Load Size:</span> {{ ucfirst($pickupRequest->load_size) }}
            </div>
            @endif

            @if($pickupRequest->item_types && count($pickupRequest->item_types) > 0)
            <div class="detail-row">
                <span class="label">Item Types:</span><br>
                @foreach($pickupRequest->item_types as $itemType)
                    • {{ str_replace('_', ' ', ucwords($itemType)) }}<br>
                @endforeach
            </div>
            @endif

            @if($pickupRequest->item_specifics)
            <div class="detail-row">
                <span class="label">Item Specifics:</span><br>
                {{ $pickupRequest->item_specifics }}
            </div>
            @endif

            @if($pickupRequest->accessibility_level)
            <div class="detail-row">
                <span class="label">Accessibility Level:</span> {{ ucfirst($pickupRequest->accessibility_level) }}
            </div>
            @endif

            @if($pickupRequest->driver_instructions)
            <div class="detail-row">
                <span class="label">Driver Instructions:</span><br>
                {{ $pickupRequest->driver_instructions }}
            </div>
            @endif

            @if($pickupRequest->other_notes)
            <div class="detail-row">
                <span class="label">Other Notes:</span><br>
                {{ $pickupRequest->other_notes }}
            </div>
            @endif
        </div>

        <div style="text-align: center;">
            <a href="{{ url('/pickup-requests/' . $pickupRequest->id) }}" class="button">View Pickup Request</a>
        </div>
    </div>

    <div class="footer">
        <p>This is an automated notification from {{ config('app.name') }}.</p>
        <p>{{ now()->format('F j, Y \a\t g:i A') }}</p>
    </div>
</body>
</html>