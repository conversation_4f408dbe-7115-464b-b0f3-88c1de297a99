<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Timeclock Report - {{ $reportDate->format('M d, Y') }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }

        h1 {
            font-size: 18px;
            margin-bottom: 5px;
        }

        h2 {
            font-size: 16px;
            margin-top: 20px;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        h3 {
            font-size: 14px;
            margin-top: 15px;
            margin-bottom: 5px;
        }

        p {
            margin: 5px 0;
        }

        .header {
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }

        .summary {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
        }

        .employee-section {
            margin-bottom: 30px;
            page-break-after: always;
            border: 1px solid #ccc;
            padding: 15px;
        }

        .employee-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            background-color: #f5f5f5;
            padding: 10px;
            border: 1px solid #ddd;
        }

        .summary-stats {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 15px;
            gap: 10px;
        }

        .stat-box {
            border: 1px solid #ddd;
            padding: 8px;
            flex: 1;
            min-width: 120px;
        }

        .stat-title {
            font-weight: bold;
            font-size: 11px;
            color: #666;
            margin-bottom: 3px;
        }

        .stat-value {
            font-size: 14px;
            font-weight: bold;
        }

        .stat-desc {
            font-size: 10px;
            color: #666;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }

        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .week-header {
            background-color: #f5f5f5;
            padding: 8px;
            margin-top: 15px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
        }

        .week-total-row {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .billable-highlight {
            font-weight: bold;
        }

        .footer {
            margin-top: 20px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
            font-size: 10px;
            text-align: center;
        }

        .punches-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            margin-bottom: 15px;
        }

        .punches-table th, .punches-table td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }

        .punches-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .mtd-header {
            background-color: #f5f5f5;
            padding: 8px;
            margin-top: 15px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Daily Timeclock Report</h1>
        <p>Date: {{ $reportDate->format('M d, Y') }}</p>
    </div>

    <div class="summary">
        <h2>Summary for {{ $reportDate->format('l, F j, Y') }}</h2>
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-title">Total Employees</div>
                <div class="stat-value">{{ count($reportData) }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-title">Hours Worked</div>
                <div class="stat-value">{{ number_format($totalHoursWorked, 2) }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-title">Break Time</div>
                <div class="stat-value">{{ number_format($totalBreakHours, 2) }}</div>
            </div>

            @php
                $employeesWithPunches = 0;
                $totalPunches = 0;
                foreach ($reportData as $data) {
                    if (count($data['punches']) > 0) {
                        $employeesWithPunches++;
                        $totalPunches += count($data['punches']);
                    }
                }
            @endphp

            <div class="stat-box">
                <div class="stat-title">Employees with Punches</div>
                <div class="stat-value">{{ $employeesWithPunches }} of {{ count($reportData) }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-title">Total Punches</div>
                <div class="stat-value">{{ $totalPunches }}</div>
            </div>
        </div>
    </div>

    @foreach($reportData as $data)
    <div class="employee-section">
        <div class="employee-header">
            <div>
                <h2 style="margin: 0;">{{ $data['user']->name }}</h2>
                <p style="margin: 0; font-size: 11px;">{{ $data['user']->email }}</p>
            </div>
            <div>
                @php
                    // Calculate billable hours (work + PTO)
                    $sickTime = isset($data['time_card']->total_sick_time_decimal) ? $data['time_card']->total_sick_time_decimal : 0;
                    $vacationTime = isset($data['time_card']->total_vacation_time_decimal) ? $data['time_card']->total_vacation_time_decimal : 0;
                    $ptoTime = $sickTime + $vacationTime;
                    $billableHours = $data['hours_worked'] + $ptoTime;
                @endphp
                <span style="font-weight: bold;">Billable Hours:</span>
                <span style="font-size: 16px; font-weight: bold;">{{ number_format($billableHours, 2) }}</span>
            </div>
        </div>

        <h3>Yesterday's Summary ({{ $reportDate->format('D, M d') }})</h3>
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-title">Work Hours</div>
                <div class="stat-value">{{ number_format($data['hours_worked'], 2) }}</div>
                <div class="stat-desc">{{ $data['time_card']->total_hours }}</div>
            </div>

            <div class="stat-box">
                <div class="stat-title">Break Time</div>
                <div class="stat-value">{{ number_format($data['break_hours'], 2) }}</div>
                <div class="stat-desc">{{ $data['time_card']->total_break_hours }}</div>
            </div>

            <div class="stat-box">
                <div class="stat-title">Sick Time</div>
                <div class="stat-value">{{ number_format($sickTime, 2) }}</div>
                <div class="stat-desc">{{ $data['time_card']->total_sick_time ?? '00:00:00' }}</div>
            </div>

            <div class="stat-box">
                <div class="stat-title">Vacation Time</div>
                <div class="stat-value">{{ number_format($vacationTime, 2) }}</div>
                <div class="stat-desc">{{ $data['time_card']->total_vacation_time ?? '00:00:00' }}</div>
            </div>
        </div>

        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-title">First Clock In</div>
                <div class="stat-value">{{ $data['first_clock_in'] ? $data['first_clock_in']->format('g:i A') : 'N/A' }}</div>
            </div>

            <div class="stat-box">
                <div class="stat-title">Last Clock Out</div>
                <div class="stat-value">{{ $data['last_clock_out'] ? $data['last_clock_out']->format('g:i A') : 'N/A' }}</div>
            </div>

            <div class="stat-box">
                <div class="stat-title">Total PTO</div>
                <div class="stat-value">{{ number_format($ptoTime, 2) }}</div>
            </div>

            <div class="stat-box">
                <div class="stat-title">Billable Hours</div>
                <div class="stat-value billable-highlight">{{ number_format($billableHours, 2) }}</div>
            </div>
        </div>

        @php
            $punchCount = count($data['punches']);
        @endphp

        @if($punchCount > 0)
        <h3>Time Punches for {{ $reportDate->format('D, M d') }}</h3>
        <table class="punches-table">
            <thead>
                <tr>
                    <th>Time</th>
                    <th>Type</th>
                    <th>Notes</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['punches'] as $punch)
                <tr>
                    <td>{{ $punch->punch_time->format('g:i:s A') }}</td>
                    <td>
                        @if($punch->type == 'clock_in')
                            <span style="color: #10b981;">Clock In</span>
                        @elseif($punch->type == 'clock_out')
                            <span style="color: #ef4444;">Clock Out</span>
                        @elseif($punch->type == 'break_out')
                            <span style="color: #f59e0b;">Break Start</span>
                        @elseif($punch->type == 'break_in')
                            <span style="color: #3b82f6;">Break End</span>
                        @elseif($punch->type == 'sick_time')
                            <span style="color: #8b5cf6;">Sick Time</span>
                        @elseif($punch->type == 'vacation_time')
                            <span style="color: #ec4899;">Vacation Time</span>
                        @else
                            {{ $punch->type_name }}
                        @endif
                    </td>
                    <td>{{ $punch->notes ?? '-' }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
        @else
        <p style="color: #ef4444; font-weight: bold; margin-top: 10px;">No time punches recorded for {{ $reportDate->format('D, M d') }}.</p>
        @endif

        <!-- Month-to-Date Section -->
        <div class="mtd-header">
            Month-to-Date Summary ({{ $reportDate->copy()->startOfMonth()->format('M d') }} - {{ $reportDate->format('M d') }})
        </div>

        @php
            // Calculate MTD data using the user's ID
            // This would normally be done in the controller, but we're adding it here for the template
            $mtdStartDate = $reportDate->copy()->startOfMonth();
            $mtdEndDate = $reportDate->copy();

            // Get all time cards for the month to date
            $mtdTimeCards = App\Models\TimeCard::where('user_id', $data['user']->id)
                ->whereBetween('date', [$mtdStartDate, $mtdEndDate])
                ->get();

            // Calculate totals
            $mtdTotalHours = 0;
            $mtdBreakHours = 0;
            $mtdSickTime = 0;
            $mtdVacationTime = 0;

            foreach ($mtdTimeCards as $timeCard) {
                // Convert HH:MM:SS to decimal hours
                $mtdTotalHours += $timeCard->timeToDecimalHours($timeCard->total_hours);
                $mtdBreakHours += $timeCard->timeToDecimalHours($timeCard->total_break_hours);
                $mtdSickTime += $timeCard->timeToDecimalHours($timeCard->total_sick_time);
                $mtdVacationTime += $timeCard->timeToDecimalHours($timeCard->total_vacation_time);
            }

            $mtdPtoTime = $mtdSickTime + $mtdVacationTime;
            $mtdBillableHours = $mtdTotalHours + $mtdPtoTime;

            // Calculate overtime (hours over 40 per week)
            $mtdOvertimeHours = 0;
            $mtdRegularHours = 0;

            // Group time cards by week
            $weeklyTimeCards = [];
            foreach ($mtdTimeCards as $timeCard) {
                $weekStartDate = $timeCard->date->copy()->startOfWeek(\Carbon\Carbon::SUNDAY)->format('Y-m-d');
                if (!isset($weeklyTimeCards[$weekStartDate])) {
                    $weeklyTimeCards[$weekStartDate] = 0;
                }

                $weeklyTimeCards[$weekStartDate] += $timeCard->timeToDecimalHours($timeCard->total_hours);
            }

            // Calculate overtime for each week
            foreach ($weeklyTimeCards as $weekHours) {
                if ($weekHours > 40) {
                    $weekOvertimeHours = $weekHours - 40;
                    $weekRegularHours = 40;
                    $mtdOvertimeHours += $weekOvertimeHours;
                    $mtdRegularHours += $weekRegularHours;
                } else {
                    $mtdRegularHours += $weekHours;
                }
            }
        @endphp

        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-title">Regular Hours</div>
                <div class="stat-value">{{ number_format($mtdRegularHours, 2) }}</div>
                <div class="stat-desc">Work hours (≤ 40/week)</div>
            </div>

            <div class="stat-box">
                <div class="stat-title">Overtime</div>
                <div class="stat-value">{{ number_format($mtdOvertimeHours, 2) }}</div>
                <div class="stat-desc">Hours > 40/week</div>
            </div>

            <div class="stat-box">
                <div class="stat-title">Sick Time</div>
                <div class="stat-value">{{ number_format($mtdSickTime, 2) }}</div>
                <div class="stat-desc">Paid sick leave</div>
            </div>

            <div class="stat-box">
                <div class="stat-title">Vacation</div>
                <div class="stat-value">{{ number_format($mtdVacationTime, 2) }}</div>
                <div class="stat-desc">Paid vacation time</div>
            </div>
        </div>

        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-title">Total Work Hours</div>
                <div class="stat-value">{{ number_format($mtdTotalHours, 2) }}</div>
            </div>

            <div class="stat-box">
                <div class="stat-title">Total Break Hours</div>
                <div class="stat-value">{{ number_format($mtdBreakHours, 2) }}</div>
            </div>

            <div class="stat-box">
                <div class="stat-title">Total PTO Hours</div>
                <div class="stat-value">{{ number_format($mtdPtoTime, 2) }}</div>
            </div>

            <div class="stat-box">
                <div class="stat-title">Total Billable</div>
                <div class="stat-value billable-highlight">{{ number_format($mtdBillableHours, 2) }}</div>
            </div>
        </div>
    </div>
    @endforeach

    <div class="footer">
        <p>This is an automated report generated by the ETRFlow system.</p>
        <p>Report generated on {{ now()->format('F j, Y \a\t g:i A') }}</p>
    </div>
</body>
</html>
