<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Your Pickup Appointment - {{ config('app.name') }}</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @vite(['resources/css/app.css', 'resources/js/app.jsx'])
    <link href="{{ asset('fontawesome/css/fontawesome.min.css') }}" rel="stylesheet">
    <link href="{{ asset('fontawesome/css/sharp-solid.min.css') }}" rel="stylesheet">
    <link rel="icon" type="image/webp" href="{{ asset('etrflow-circlepadded.webp') }}">
</head>
<body class="bg-base-200 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="bg-base-100 border border-base-300 p-8 mb-8">
                <div class="inline-flex items-center justify-center w-12 h-12 bg-primary text-primary-content mb-4">
                    <i class="fa-sharp fa-edit text-lg"></i>
                </div>
                <h1 class="text-2xl lg:text-3xl font-bold text-base-content mb-3">Edit Your Pickup Appointment</h1>
                <div class="flex items-center justify-center gap-3">
                    <span class="text-base-content/60">Request #{{ $pickupRequest->id }}</span>
                    @php
                        $statusClasses = [
                            'pending' => 'badge-info',
                            'confirmed' => 'badge-success',
                            'completed' => 'badge-success',
                            'cancelled' => 'badge-error'
                        ];
                    @endphp
                    <div class="badge {{ $statusClasses[$pickupRequest->status] ?? 'badge-neutral' }} badge-lg">
                        {{ $pickupRequest->getFormattedStatus() }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Display -->
        @if($errors->any())
            <div class="alert alert-error mb-6 max-w-4xl mx-auto">
                <div class="flex items-center gap-2">
                    <i class="fa-sharp fa-exclamation-triangle"></i>
                    <div>
                        <h4 class="font-bold">There were some errors with your submission:</h4>
                        <ul class="list-disc list-inside mt-2">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        @endif

        @if(session('success'))
            <div class="alert alert-success mb-6 max-w-4xl mx-auto">
                <div class="flex items-center gap-2">
                    <i class="fa-sharp fa-check-circle"></i>
                    <span>{{ session('success') }}</span>
                </div>
            </div>
        @endif

        <!-- Edit Form -->
        <div class="max-w-4xl mx-auto">
            <form action="{{ route('pickup.manage.edit.update', $token) }}" method="POST" id="appointment-edit-form" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Contact Information -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-address-card text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Contact Information</h4>
                        </div>
                    </div>

                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <fieldset class="fieldset">
                                <legend class="fieldset-legend">
                                    <i class="fa-sharp fa-user text-secondary mr-2"></i>
                                    Contact Name *
                                </legend>
                                <input type="text" name="contact_name" id="contact_name"
                                       class="input input-bordered w-full"
                                       placeholder="Contact person's full name"
                                       value="{{ old('contact_name', $pickupRequest->contact_name) }}" required>
                                @error('contact_name')
                                    <div class="text-error text-sm mt-1">{{ $message }}</div>
                                @enderror
                            </fieldset>

                            <fieldset class="fieldset">
                                <legend class="fieldset-legend">
                                    <i class="fa-sharp fa-building text-secondary mr-2"></i>
                                    Business Name
                                </legend>
                                <input type="text" name="business_name" id="business_name"
                                       class="input input-bordered w-full"
                                       placeholder="Optional business name"
                                       value="{{ old('business_name', $pickupRequest->business_name) }}">
                                @error('business_name')
                                    <div class="text-error text-sm mt-1">{{ $message }}</div>
                                @enderror
                            </fieldset>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <fieldset class="fieldset">
                                <legend class="fieldset-legend">
                                    <i class="fa-sharp fa-envelope text-secondary mr-2"></i>
                                    Email Address *
                                </legend>
                                <input type="email" name="email" id="email"
                                       class="input input-bordered w-full"
                                       placeholder="<EMAIL>"
                                       value="{{ old('email', $pickupRequest->email) }}" required>
                                @error('email')
                                    <div class="text-error text-sm mt-1">{{ $message }}</div>
                                @enderror
                            </fieldset>

                            <fieldset class="fieldset">
                                <legend class="fieldset-legend">
                                    <i class="fa-sharp fa-phone text-secondary mr-2"></i>
                                    Phone Number *
                                </legend>
                                <input type="tel" name="phone" id="phone"
                                       class="input input-bordered w-full"
                                       placeholder="(*************"
                                       value="{{ old('phone', $pickupRequest->phone) }}" required>
                                @error('phone')
                                    <div class="text-error text-sm mt-1">{{ $message }}</div>
                                @enderror
                            </fieldset>
                        </div>
                    </div>
                </div>

                <!-- Pickup Address -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-map-marker-alt text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Pickup Address</h4>
                        </div>
                    </div>

                    <div class="p-6 space-y-4">
                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-map-marker-alt text-success mr-2"></i>
                                Pickup Address *
                            </legend>
                            <textarea name="pickup_address" id="pickup_address"
                                      class="textarea textarea-bordered h-24 w-full"
                                      placeholder="Enter the full pickup address"
                                      required>{{ old('pickup_address', $pickupRequest->pickup_address) }}</textarea>
                            @error('pickup_address')
                                <div class="text-error text-sm mt-1">{{ $message }}</div>
                            @enderror
                        </fieldset>

                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-home text-success mr-2"></i>
                                Where on the property are the items located? *
                            </legend>
                            <textarea name="property_location_details" id="property_location_details"
                                      class="textarea textarea-bordered h-24 w-full"
                                      placeholder="Please describe the location and accessibility (e.g., garage, basement, second floor, stairs involved, etc.). Note: Extra fees may apply for moving large items from difficult locations."
                                      required>{{ old('property_location_details', $pickupRequest->property_location_details) }}</textarea>
                            @error('property_location_details')
                                <div class="text-error text-sm mt-1">{{ $message }}</div>
                            @enderror
                        </fieldset>

                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-sticky-note text-success mr-2"></i>
                                Other things to know
                            </legend>
                            <textarea name="other_notes" id="other_notes"
                                      class="textarea textarea-bordered h-24 w-full"
                                      placeholder="Any security codes, special instructions, who to call when we arrive, or other important details">{{ old('other_notes', $pickupRequest->other_notes) }}</textarea>
                            <div class="text-sm text-base-content/60 mt-1">Optional</div>
                            @error('other_notes')
                                <div class="text-error text-sm mt-1">{{ $message }}</div>
                            @enderror
                        </fieldset>
                    </div>
                </div>

                <!-- Load Size Selection -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-warning/10 to-warning/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-warning text-warning-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-boxes text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Load Size</h4>
                        </div>
                    </div>

                    <div class="p-6">
                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-truck-loading text-warning mr-2"></i>
                                How much are we picking up? *
                            </legend>
                            <div class="space-y-4">
                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-3">
                                        <input type="radio" name="load_size" value="small" class="radio radio-primary"
                                               {{ old('load_size', $pickupRequest->load_size) == 'small' ? 'checked' : '' }} required>
                                        <div class="flex-1">
                                            <div class="font-bold text-base-content">Small Load</div>
                                            <p class="text-sm text-base-content/80 leading-relaxed mt-1">
                                                One or two flatscreen TVs, a few computers, small appliances, microwave, etc. (fits in midsize SUV or less)
                                            </p>
                                        </div>
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-3">
                                        <input type="radio" name="load_size" value="medium" class="radio radio-primary"
                                               {{ old('load_size', $pickupRequest->load_size) == 'medium' ? 'checked' : '' }} required>
                                        <div class="flex-1">
                                            <div class="font-bold text-base-content">Medium Load</div>
                                            <p class="text-sm text-base-content/80 leading-relaxed mt-1">
                                                A few pallets, totes, gaylords that are ready to be picked up, but can still be reasonably moved by 1 person
                                            </p>
                                        </div>
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-3">
                                        <input type="radio" name="load_size" value="large" class="radio radio-primary"
                                               {{ old('load_size', $pickupRequest->load_size) == 'large' ? 'checked' : '' }} required>
                                        <div class="flex-1">
                                            <div class="font-bold text-base-content">Large or Heavy Load</div>
                                            <p class="text-sm text-base-content/80 leading-relaxed mt-1">
                                                Heavy objects like a piano, conference system, slot machines, etc. that likely require 2 or more people to pickup
                                            </p>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            @error('load_size')
                                <div class="text-error text-sm mt-2">{{ $message }}</div>
                            @enderror
                        </fieldset>
                    </div>
                </div>

                <!-- Item Types -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-info text-info-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-list text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Item Types</h4>
                        </div>
                    </div>

                    <div class="p-6">
                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-boxes text-info mr-2"></i>
                                What types of items are we picking up? *
                            </legend>
                            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                @php
                                    $itemTypes = old('item_types', $pickupRequest->item_types ?? []);
                                    if (is_string($itemTypes)) {
                                        $itemTypes = json_decode($itemTypes, true) ?? [];
                                    }
                                @endphp

                                @foreach($pickupItemTypes as $itemType)
                                    <div class="form-control">
                                        <label class="item-type-button cursor-pointer p-3 border-2 border-base-300 rounded-lg hover:border-primary hover:bg-primary/5 transition-all duration-200 flex flex-col items-center text-center {{ in_array($itemType['value'], $itemTypes) ? 'border-primary bg-primary/10' : '' }}">
                                            <input type="checkbox" name="item_types[]" value="{{ $itemType['value'] }}"
                                                   class="hidden item-type-checkbox"
                                                   {{ in_array($itemType['value'], $itemTypes) ? 'checked' : '' }}>
                                            <div class="relative">
                                                <i class="{{ $itemType['icon'] ?? 'fa-sharp fa-question' }} text-2xl text-base-content mb-2"></i>
                                                <div class="checkmark absolute -top-1 -right-1 w-5 h-5 bg-primary text-primary-content rounded-full items-center justify-center text-xs {{ in_array($itemType['value'], $itemTypes) ? 'flex' : 'hidden' }}">
                                                    <i class="fa-sharp fa-check"></i>
                                                </div>
                                            </div>
                                            <span class="label-text text-xs font-medium">{{ $itemType['name'] }}</span>
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                            @error('item_types')
                                <div class="text-error text-sm mt-1">{{ $message }}</div>
                            @enderror
                            <div class="text-sm text-base-content/60 mt-2">
                                <i class="fa-sharp fa-info-circle mr-1"></i>
                                Select all that apply
                            </div>
                        </fieldset>
                    </div>
                </div>

                <!-- Item Specifics -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-accent text-accent-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-list-ul text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Additional Details</h4>
                        </div>
                    </div>

                    <div class="p-6 space-y-4">
                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-list-ul text-accent mr-2"></i>
                                Additional Item Specifics *
                            </legend>
                            <textarea name="item_specifics" id="item_specifics"
                                      class="textarea textarea-bordered h-32 w-full"
                                      placeholder="Please provide additional details about the items (e.g., swap out 4 totes of computers, one 55&quot; flatscreen, roughly 30 desktop PCs, etc.)" 
                                      required>{{ old('item_specifics', $pickupRequest->item_specifics) }}</textarea>
                            @error('item_specifics')
                                <div class="text-error text-sm mt-1">{{ $message }}</div>
                            @enderror
                        </fieldset>

                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-universal-access text-accent mr-2"></i>
                                Are the items easily accessible? *
                            </legend>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <label class="card bg-green-50 hover:bg-green-100 border-2 border-green-200 hover:border-green-300 cursor-pointer transition-all duration-200 p-4 {{ old('accessibility_level', $pickupRequest->accessibility_level) == 'easy' ? 'ring-2 ring-green-500 bg-green-100' : '' }}">
                                    <input type="radio" name="accessibility_level" value="easy" class="radio radio-success sr-only"
                                           {{ old('accessibility_level', $pickupRequest->accessibility_level) == 'easy' ? 'checked' : '' }} required>
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-green-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                                            <i class="fa-sharp fa-face-smile text-xl"></i>
                                        </div>
                                        <div class="font-bold text-green-800 mb-2">Easy Access</div>
                                        <p class="text-sm text-green-700 leading-relaxed">
                                            First level storage room, garage, outside, front room, etc.
                                        </p>
                                    </div>
                                </label>

                                <label class="card bg-orange-50 hover:bg-orange-100 border-2 border-orange-200 hover:border-orange-300 cursor-pointer transition-all duration-200 p-4 {{ old('accessibility_level', $pickupRequest->accessibility_level) == 'moderate' ? 'ring-2 ring-orange-500 bg-orange-100' : '' }}">
                                    <input type="radio" name="accessibility_level" value="moderate" class="radio radio-warning sr-only"
                                           {{ old('accessibility_level', $pickupRequest->accessibility_level) == 'moderate' ? 'checked' : '' }} required>
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                                            <i class="fa-sharp fa-face-meh text-xl"></i>
                                        </div>
                                        <div class="font-bold text-orange-800 mb-2">Moderate Access</div>
                                        <p class="text-sm text-orange-700 leading-relaxed">
                                            Some stairs or obstacles, but manageable
                                        </p>
                                    </div>
                                </label>

                                <label class="card bg-red-50 hover:bg-red-100 border-2 border-red-200 hover:border-red-300 cursor-pointer transition-all duration-200 p-4 {{ old('accessibility_level', $pickupRequest->accessibility_level) == 'difficult' ? 'ring-2 ring-red-500 bg-red-100' : '' }}">
                                    <input type="radio" name="accessibility_level" value="difficult" class="radio radio-error sr-only"
                                           {{ old('accessibility_level', $pickupRequest->accessibility_level) == 'difficult' ? 'checked' : '' }} required>
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-red-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                                            <i class="fa-sharp fa-face-tired text-xl"></i>
                                        </div>
                                        <div class="font-bold text-red-800 mb-2">Difficult Access</div>
                                        <p class="text-sm text-red-700 leading-relaxed">
                                            Multiple stairs, tight spaces, or heavy obstacles
                                        </p>
                                    </div>
                                </label>
                            </div>
                            @error('accessibility_level')
                                <div class="text-error text-sm mt-2">{{ $message }}</div>
                            @enderror
                        </fieldset>

                        <fieldset class="fieldset">
                            <legend class="fieldset-legend">
                                <i class="fa-sharp fa-route text-accent mr-2"></i>
                                Driver Instructions
                            </legend>
                            <textarea name="driver_instructions" id="driver_instructions"
                                      class="textarea textarea-bordered h-24 w-full"
                                      placeholder="Any special instructions for the driver (e.g., call when arriving, use back entrance, etc.)">{{ old('driver_instructions', $pickupRequest->driver_instructions) }}</textarea>
                            <div class="text-sm text-base-content/60 mt-1">Optional</div>
                            @error('driver_instructions')
                                <div class="text-error text-sm mt-1">{{ $message }}</div>
                            @enderror
                        </fieldset>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg p-6">
                    <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content w-10 rounded-lg">
                                    <i class="fa-sharp fa-save text-sm"></i>
                                </div>
                            </div>
                            <div class="text-center sm:text-left">
                                <p class="font-medium text-base-content">Update Your Appointment</p>
                                <p class="text-sm text-base-content/60">Changes will be saved to your pickup request</p>
                            </div>
                        </div>
                        <div class="flex gap-3 w-full sm:w-auto">
                            <a href="{{ route('pickup.manage', $token) }}" class="btn btn-outline btn-lg gap-2 flex-1 sm:flex-none">
                                <i class="fa-sharp fa-arrow-left"></i>
                                Back to Appointment
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                                <i class="fa-sharp fa-save"></i>
                                Save Changes
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- App Dialog Component -->
    <x-app-dialog />

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Item type selection functionality
            const itemTypeButtons = document.querySelectorAll('.item-type-button');
            
            itemTypeButtons.forEach(button => {
                const checkbox = button.querySelector('.item-type-checkbox');
                const checkmark = button.querySelector('.checkmark');
                
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    // Toggle checkbox state
                    checkbox.checked = !checkbox.checked;
                    
                    // Update visual state
                    if (checkbox.checked) {
                        button.classList.add('border-primary', 'bg-primary/10');
                        button.classList.remove('border-base-300');
                        checkmark.classList.remove('hidden');
                        checkmark.classList.add('flex');
                    } else {
                        button.classList.remove('border-primary', 'bg-primary/10');
                        button.classList.add('border-base-300');
                        checkmark.classList.add('hidden');
                        checkmark.classList.remove('flex');
                    }
                });
            });
            
            // Accessibility level selection
            const accessibilityRadios = document.querySelectorAll('input[name="accessibility_level"]');
            
            accessibilityRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    // Remove selected state from all cards
                    document.querySelectorAll('label[class*="bg-green-50"], label[class*="bg-orange-50"], label[class*="bg-red-50"]').forEach(label => {
                        label.classList.remove('ring-2', 'ring-offset-2', 'ring-green-500', 'ring-orange-500', 'ring-red-500');
                        label.classList.remove('bg-green-100', 'bg-orange-100', 'bg-red-100');
                        if (label.classList.contains('bg-green-50')) label.classList.add('bg-green-50');
                        if (label.classList.contains('bg-orange-50')) label.classList.add('bg-orange-50');
                        if (label.classList.contains('bg-red-50')) label.classList.add('bg-red-50');
                    });
                    
                    // Add selected state to current card
                    const selectedLabel = this.closest('label');
                    selectedLabel.classList.add('ring-2', 'ring-offset-2');
                    if (selectedLabel.classList.contains('bg-green-50')) {
                        selectedLabel.classList.add('ring-green-500', 'bg-green-100');
                        selectedLabel.classList.remove('bg-green-50');
                    } else if (selectedLabel.classList.contains('bg-orange-50')) {
                        selectedLabel.classList.add('ring-orange-500', 'bg-orange-100');
                        selectedLabel.classList.remove('bg-orange-50');
                    } else if (selectedLabel.classList.contains('bg-red-50')) {
                        selectedLabel.classList.add('ring-red-500', 'bg-red-100');
                        selectedLabel.classList.remove('bg-red-50');
                    }
                });
            });
        });
    </script>
</body>
</html>