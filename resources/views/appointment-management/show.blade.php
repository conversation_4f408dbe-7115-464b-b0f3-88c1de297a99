@php
use Illuminate\Support\Facades\Storage;
@endphp
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Your Pickup Appointment - {{ config('app.name') }}</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @vite(['resources/css/app.css', 'resources/js/app.jsx'])
    <link href="{{ asset('fontawesome/css/fontawesome.min.css') }}" rel="stylesheet">
    <link href="{{ asset('fontawesome/css/sharp-solid.min.css') }}" rel="stylesheet">
    <link rel="icon" type="image/webp" href="{{ asset('etrflow-circlepadded.webp') }}">
    <!-- Include browser-image-compression from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/browser-image-compression@2.0.2/dist/browser-image-compression.min.js"></script>
    <script src="{{ asset('js/image-dropzone.js') }}"></script>

</head>
<body class="bg-base-200 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="bg-base-100 border border-base-300 p-8 mb-8">
                <div class="inline-flex items-center justify-center w-12 h-12 bg-primary text-primary-content mb-4">
                    <i class="fa-sharp fa-calendar-check text-lg"></i>
                </div>
                <h1 class="text-2xl lg:text-3xl font-bold text-base-content mb-3">Manage Your Pickup Appointment</h1>
                <div class="flex items-center justify-center gap-3">
                    <span class="text-base-content/60">Request #{{ $pickupRequest->id }}</span>
                    @php
                        $statusClasses = [
                            'pending' => 'badge-info',
                            'confirmed' => 'badge-success',
                            'completed' => 'badge-success',
                            'cancelled' => 'badge-error'
                        ];
                    @endphp
                    <div class="badge {{ $statusClasses[$pickupRequest->status] ?? 'badge-neutral' }} badge-lg">
                        {{ $pickupRequest->getFormattedStatus() }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Appointment Details -->
        <div class="max-w-4xl mx-auto">
            <div class="bg-base-100 border border-primary">
                <!-- Header Section -->
                <div class="bg-primary text-primary-content px-6 py-4 border-b border-base-300">
                    <h2 class="text-lg font-semibold text-primary-content">Your Pickup Details</h2>
                    <p class="text-sm text-primary-content/80 mt-1">Review your scheduled pickup information</p>
                </div>

                <!-- Content Section -->
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Contact Information -->
                        <div>
                            <h3 class="text-base font-medium text-base-content mb-4 border-b border-base-300 pb-2">Contact Information</h3>
                            <div class="space-y-4">
                                <div class="flex items-center gap-3">
                                    <i class="fa-sharp fa-user text-base-content/60 w-4 text-center"></i>
                                    <div>
                                        <span class="text-xs text-base-content/60 block">Name</span>
                                        <span class="font-medium text-base-content">{{ $pickupRequest->contact_name }}</span>
                                    </div>
                                </div>
                                @if($pickupRequest->business_name)
                                    <div class="flex items-center gap-3">
                                        <i class="fa-sharp fa-building text-base-content/60 w-4 text-center"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Business</span>
                                            <span class="font-medium text-base-content">{{ $pickupRequest->business_name }}</span>
                                        </div>
                                    </div>
                                @endif
                                <div class="flex items-center gap-3">
                                    <i class="fa-sharp fa-envelope text-base-content/60 w-4 text-center"></i>
                                    <div>
                                        <span class="text-xs text-base-content/60 block">Email</span>
                                        <span class="font-medium text-base-content break-all">{{ $pickupRequest->email }}</span>
                                    </div>
                                </div>
                                <div class="flex items-center gap-3">
                                    <i class="fa-sharp fa-phone text-base-content/60 w-4 text-center"></i>
                                    <div>
                                        <span class="text-xs text-base-content/60 block">Phone</span>
                                        <span class="font-medium text-base-content">{{ $pickupRequest->phone }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pickup Information -->
                        <div>
                            <h3 class="text-base font-medium text-base-content mb-4 border-b border-base-300 pb-2">Pickup Information</h3>
                            <div class="space-y-4">
                                @if($pickupRequest->event)
                                    <div class="flex items-start gap-3">
                                        <i class="fa-sharp fa-calendar text-base-content/60 w-4 text-center mt-1"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Scheduled Date & Time</span>
                                            @php
                                                $timezone = \App\Models\GlobalConfig::getTimeZone();
                                            @endphp
                                            <span class="font-medium text-base-content block">{{ $pickupRequest->event->start_date->setTimezone($timezone)->format('l, F j, Y') }}</span>
                                            <span class="text-primary font-semibold">{{ $pickupRequest->event->start_date->setTimezone($timezone)->format('g:i A') }}</span>
                                        </div>
                                    </div>
                                @endif
                                <div class="flex items-start gap-3">
                                    <i class="fa-sharp fa-map-marker-alt text-base-content/60 w-4 text-center mt-1"></i>
                                    <div>
                                        <span class="text-xs text-base-content/60 block">Pickup Address</span>
                                        <span class="font-medium text-base-content">{{ $pickupRequest->pickup_address }}</span>
                                    </div>
                                </div>
                                @if($pickupRequest->event && $pickupRequest->event->assignedDriver)
                                    <div class="flex items-center gap-3">
                                        <i class="fa-sharp fa-user-tie text-base-content/60 w-4 text-center"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Assigned Driver</span>
                                            <span class="font-medium text-base-content">{{ $pickupRequest->event->assignedDriver->name }}</span>
                                        </div>
                                    </div>
                                @endif
                                @if($pickupRequest->load_size)
                                    <div class="flex items-center gap-3">
                                        <i class="fa-sharp fa-boxes text-base-content/60 w-4 text-center"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Load Size</span>
                                            <span class="font-medium text-base-content capitalize">{{ str_replace('_', ' ', $pickupRequest->load_size) }}</span>
                                        </div>
                                    </div>
                                @endif
                                @if($pickupRequest->item_types)
                                    <div class="flex items-start gap-3">
                                        <i class="fa-sharp fa-list text-base-content/60 w-4 text-center mt-1"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Items</span>
                                            <span class="font-medium text-base-content">{{ is_array($pickupRequest->item_types) ? implode(', ', array_map(fn($type) => str_replace('_', ' ', $type), $pickupRequest->item_types)) : $pickupRequest->item_types }}</span>
                                        </div>
                                    </div>
                                @endif
                                @if($pickupRequest->item_specifics)
                                    <div class="flex items-start gap-3">
                                        <i class="fa-sharp fa-list-ul text-base-content/60 w-4 text-center mt-1"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Additional Item Specifics</span>
                                            <span class="font-medium text-base-content">{{ $pickupRequest->item_specifics }}</span>
                                        </div>
                                    </div>
                                @endif
                                @if($pickupRequest->accessibility_level)
                                    <div class="flex items-center gap-3">
                                        <i class="fa-sharp fa-universal-access text-base-content/60 w-4 text-center"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Accessibility Level</span>
                                            <span class="font-medium text-base-content capitalize">{{ str_replace('_', ' ', $pickupRequest->accessibility_level) }}</span>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    @if($pickupRequest->driver_instructions || $pickupRequest->other_notes)
                        <div class="mt-8">
                            <div class="bg-base-200 p-6 border border-base-300">
                                <h3 class="text-base font-medium text-base-content mb-4 border-b border-base-300 pb-2">Special Instructions</h3>
                                @if($pickupRequest->driver_instructions)
                                    <div class="mb-4">
                                        <span class="text-xs text-base-content/60 block mb-1">Driver Instructions</span>
                                        <p class="text-base-content">{{ $pickupRequest->driver_instructions }}</p>
                                    </div>
                                @endif
                                @if($pickupRequest->other_notes)
                                    <div>
                                        <span class="text-xs text-base-content/60 block mb-1">Additional Notes</span>
                                        <p class="text-base-content">{{ $pickupRequest->other_notes }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- Image Upload Section -->
                    <div class="mt-8">
                        <div class="bg-base-200 p-6 border border-base-300">
                            <div class="flex items-center justify-between mb-4 border-b border-base-300 pb-2">
                                <h3 class="text-base font-medium text-base-content">Photos</h3>
                                <span class="text-xs text-base-content/60">Help us understand your pickup needs</span>
                            </div>
                            
                            @php
                                $existingImages = $pickupRequest->images->map(function($image) {
                                    return [
                                        'id' => $image->id,
                                        'src' => Storage::disk('public')->url($image->image_path),
                                        'alt' => 'Pickup item photo',
                                        'name' => $image->og_filename ?? 'Image'
                                    ];
                                })->toArray();
                            @endphp
                            
                            <x-image-dropzone
                                id="appointment-images"
                                name="images[]"
                                :max-files="10"
                                :max-filesize="10"
                                :max-width="1250"
                                :max-height="1250"
                                :client-resize="true"
                                accepted-formats="image/jpeg,image/jpg,image/png,image/webp"
                                :existing-images="$existingImages"
                                :session-based="false"
                                upload-url="{{ route('pickup.manage.upload-image', $token) }}"
                                delete-url="/pickup/manage/{{ $token }}/delete-image/{id}"
                                :multiple="true"
                                :immediate-upload="true"
                                :show-previews="true"
                                label="Pickup Photos"
                                help-text="Drop images here or click to upload"
                            />
                            <div class="text-xs text-base-content/60 mt-2">
                                <i class="fa-sharp fa-info-circle mr-1"></i>
                                Photos help us better understand your pickup needs and prepare accordingly.
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-base-100 border border-neutral mt-8">
                <!-- Header Section -->
                <div class="bg-neutral px-6 py-4 border-b border-base-300">
                    <h3 class="text-lg font-semibold text-neutral-content">Manage Your Appointment</h3>
                    <p class="text-sm text-neutral-content/80 mt-1">Take action on your scheduled pickup</p>
                </div>

                <!-- Content Section -->
                <div class="p-6">
                    <!-- Status Alert -->
                    @if($pickupRequest->status === 'confirmed')
                        <div class="bg-success/10 border border-success/30 p-4 mb-6 flex items-center gap-3">
                            <i class="fa-sharp fa-check-circle text-success"></i>
                            <div>
                                <p class="font-medium text-success-content">Appointment Confirmed</p>
                                <p class="text-sm text-base-content/70">Your appointment has been confirmed and is scheduled as planned. No further action is required.</p>
                            </div>
                        </div>
                    @elseif($pickupRequest->status === 'cancelled')
                        <div class="bg-error/10 border border-error/30 p-4 mb-6 flex items-center gap-3">
                            <i class="fa-sharp fa-times-circle text-error"></i>
                            <div>
                                <p class="font-medium text-error">Appointment Cancelled</p>
                                <p class="text-sm text-base-content/70">This appointment has been cancelled.</p>
                            </div>
                        </div>
                    @elseif($pickupRequest->canCustomerConfirm())
                        <div class="bg-warning/10 border border-warning/30 p-4 mb-6 flex items-center gap-3">
                            <i class="fa-sharp fa-clock text-warning-content"></i>
                            <div>
                                <p class="font-medium text-warning-content">Confirmation Required</p>
                                <p class="text-sm text-base-content/70">Your pickup is coming up soon! <strong>Please confirm your appointment to ensure we're ready for your pickup by clicking the "Confirm Pickup" button.</strong></p>
                            </div>
                        </div>
                    @else
                        <div class="bg-info/10 border border-info/30 p-4 mb-6 flex items-center gap-3">
                            <i class="fa-sharp fa-info-circle text-info"></i>
                            <div>
                                <p class="font-medium text-info">Appointment Scheduled</p>
                                <p class="text-sm text-base-content/70">Your appointment is scheduled. You will need to confirm that you're ready for the pickup 24 hours before the scheduled time. The confirmation option will be available 72 hours before your pickup time.</p>
                            </div>
                        </div>
                    @endif

                    <!-- Action Buttons -->
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-base-content/70 mb-3">Appointment Actions</h4>
                            <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
                                <a href="{{ route('pickup.manage.edit', $token) }}" class="btn btn-primary gap-2 shadow-lg">
                                    <i class="fa-sharp fa-edit"></i>
                                    Edit Pickup Details
                                </a>
                                
                                @if($pickupRequest->canCustomerConfirm())
                                    <button class="btn btn-success gap-2 shadow-lg" onclick="confirmAppointment()">
                                        <i class="fa-sharp fa-check"></i>
                                        Confirm Pickup
                                    </button>
                                @else
                                    <button class="btn btn-outline btn-disabled gap-2" disabled>
                                        <i class="fa-sharp fa-check"></i>
                                        Confirm Pickup
                                    </button>
                                @endif
                                
                                @if($pickupRequest->canBeCancelled())
                                    <button class="btn btn-error btn-outline gap-2" onclick="cancelAppointment()">
                                        <i class="fa-sharp fa-times-circle"></i>
                                        Cancel Appointment
                                    </button>
                                @else
                                    <button class="btn btn-outline btn-disabled gap-2" disabled>
                                        <i class="fa-sharp fa-times-circle"></i>
                                        Cancel Appointment
                                    </button>
                                @endif
                            </div>
                        </div>
                        <p>To reschedule without canceling, please call us directly.</p>

                        <!-- Contact Support -->
                        <div class="border-t border-base-300/50 pt-6">
                            <h4 class="text-sm font-medium text-base-content/70 mb-3 text-center">Need Help?</h4>
                            <div class="flex flex-wrap justify-center gap-2">
                                @if($contactEmail)
                                    <a href="mailto:{{ $contactEmail }}" class="btn btn-primary btn-sm gap-2 shadow-sm">
                                        <i class="fa-sharp fa-envelope"></i>
                                        Email Us
                                    </a>
                                @endif
                                
                                @if($contactPhone)
                                    <a href="tel:{{ $contactPhone }}" class="btn btn-success btn-sm gap-2 shadow-sm">
                                        <i class="fa-sharp fa-phone"></i>
                                        Call Us
                                    </a>
                                @endif
                                
                                @if($websiteLink)
                                    <a href="{{ $websiteLink }}" target="_blank" rel="noopener noreferrer" class="btn btn-secondary btn-sm gap-2 shadow-sm">
                                        <i class="fa-sharp fa-external-link"></i>
                                        Visit Website
                                    </a>
                                @endif
                                
                                @if(!$contactEmail && !$contactPhone && !$websiteLink)
                                    <a href="mailto:{{ config('mail.from.address') }}" class="btn btn-primary btn-sm gap-2 shadow-sm">
                                        <i class="fa-sharp fa-envelope"></i>
                                        Contact Us
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="text-center mt-8">
                <p class="text-base-content/60 text-sm">
                    <i class="fa-sharp fa-shield-alt mr-1"></i>
                    This is a secure link for managing your pickup appointment.
                </p>
                <p class="text-base-content/60 text-xs mt-2">
                    &copy; {{ date('Y') }} {{ config('app.name') }} - Pickup Management System
                </p>
            </div>
        </div>
    </div>

    <!-- App Dialog Component -->
    <x-app-dialog />

    <script>
        // Initialize image dropzone components
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all image dropzone components on the page
            const dropzoneComponents = document.querySelectorAll('.image-dropzone-component');
            dropzoneComponents.forEach(component => {
                const dropzone = new ImageDropzone(component);
                
                // Listen for successful uploads (built-in component will handle display)
                component.addEventListener('image-uploaded', function(event) {
                    console.log('Image uploaded successfully:', event.detail);
                    showSuccessMessage('Image uploaded successfully!');
                });
                
                // Handle delete button clicks for existing images
                component.addEventListener('click', async function(e) {
                    if (e.target.closest('.remove-image-btn')) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        const btn = e.target.closest('.remove-image-btn');
                        const imageId = btn.dataset.imageId;
                        
                        if (imageId) {
                            await handleImageDelete(imageId, btn);
                        }
                    }
                });
            });
        });

        // Handle image deletion for immediate uploads
        async function handleImageDelete(imageId, btn) {
            if (!confirm('Are you sure you want to delete this image?')) {
                return;
            }
            
            const originalText = btn.innerHTML;
            btn.disabled = true;
            btn.innerHTML = '<span class="loading loading-spinner loading-xs"></span>';
            
            try {
                const response = await fetch(`/pickup/manage/{{ $token }}/delete-image/${imageId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // Find the dropzone component and update its internal state
                    const dropzoneComponent = document.querySelector('.image-dropzone-component');
                    const dropzoneInstance = dropzoneComponent._dropzoneInstance;
                    
                    if (dropzoneInstance) {
                        // Remove from the dropzone's internal uploadedImages array
                        dropzoneInstance.uploadedImages = dropzoneInstance.uploadedImages.filter(img => img.id != imageId);
                        
                        // Let the dropzone component update its own display
                        dropzoneInstance.updateDisplay();
                    } else {
                        // Fallback: manual DOM manipulation if dropzone instance not found
                        const imageContainer = btn.closest('.relative.group');
                        if (imageContainer) {
                            imageContainer.remove();
                        }
                        
                        // Update the photo count
                        const photoCountElement = document.querySelector('#queue-appointment-images .photo-count');
                        const remainingImages = document.querySelectorAll('#preview-appointment-images .relative.group');
                        const currentCount = remainingImages.length;
                        
                        if (photoCountElement) {
                            photoCountElement.textContent = `${currentCount} ${currentCount !== 1 ? 'images' : 'image'} selected`;
                        }
                        
                        // Hide the queue container if no images remain
                        const queueContainer = document.querySelector('#queue-appointment-images');
                        if (queueContainer && currentCount === 0) {
                            queueContainer.classList.add('hidden');
                        }
                    }
                    
                    showSuccessMessage('Image deleted successfully!');
                } else {
                    showErrorMessage(result.message || 'Failed to delete image');
                }
            } catch (error) {
                console.error('Error deleting image:', error);
                showErrorMessage('An error occurred while deleting the image');
            } finally {
                btn.disabled = false;
                btn.innerHTML = originalText;
            }
        }

        // Auto-prompt for confirmation if coming from email link
        @if(isset($showConfirmPrompt) && $showConfirmPrompt)
        window.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                showConfirm({
                    title: 'Confirm Your Pickup',
                    message: 'You clicked the confirmation link in your email. Would you like to confirm your pickup appointment now?',
                    confirmText: 'Yes, Confirm',
                    cancelText: 'Not Now',
                    icon: 'fa-calendar-check',
                    iconColor: 'bg-success/10 text-success',
                    confirmClass: 'btn-success'
                }).then(confirmed => {
                    if (confirmed) {
                        confirmAppointment();
                    }
                });
            }, 500);
        });
        @endif

        async function confirmAppointment() {
            const confirmed = await showConfirm({
                title: 'Confirm Your Pickup',
                message: 'Are you sure you want to confirm this pickup appointment? This will notify our team that you\'ll be ready for pickup as scheduled.',
                confirmText: 'Yes, Confirm Pickup',
                cancelText: 'Cancel',
                icon: 'fa-check-circle',
                iconColor: 'bg-success/10 text-success',
                confirmClass: 'btn-success'
            });

            if (!confirmed) return;

            const button = event.target;
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Confirming...';

            try {
                const response = await fetch(window.location.href, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        action: 'confirm'
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                if (data.success) {
                    window.location.reload();
                } else {
                    await showError({
                        title: 'Confirmation Failed',
                        message: data.message || 'An error occurred while confirming your appointment.'
                    });
                    button.disabled = false;
                    button.innerHTML = originalText;
                }
            } catch (error) {
                console.error('Error:', error);
                await showError({
                    title: 'Network Error',
                    message: 'An error occurred while confirming your appointment. Please try again.'
                });
                button.disabled = false;
                button.innerHTML = originalText;
            }
        }

        async function cancelAppointment() {
            const confirmed = await showConfirm({
                title: 'Cancel Your Pickup',
                message: 'Are you sure you want to cancel this pickup appointment? This action cannot be undone and you\'ll need to request a new pickup if needed.',
                confirmText: 'Continue to Cancel',
                cancelText: 'Keep Appointment',
                icon: 'fa-times-circle',
                iconColor: 'bg-error/10 text-error',
                confirmClass: 'btn-error'
            });

            if (!confirmed) return;

            // Show cancellation reason dialog
            const reasonDialog = document.createElement('dialog');
            reasonDialog.className = 'modal';
            reasonDialog.innerHTML = `
                <div class="modal-box">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-error text-error-content w-10 rounded-lg">
                                <i class="fa-sharp fa-times text-lg"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">Cancel Your Pickup</h3>
                            <p class="text-base-content/70">Please tell us why you're cancelling (optional)</p>
                        </div>
                    </div>
                    
                    <div class="form-control mb-6">
                        <label class="label">
                            <span class="label-text font-medium">Cancellation Reason</span>
                        </label>
                        <textarea 
                            id="guest-cancellation-reason" 
                            class="textarea textarea-bordered h-24 resize-none" 
                            placeholder="Let us know why you need to cancel (optional)..."
                        ></textarea>
                        <div class="label">
                            <span class="label-text-alt text-base-content/60">This helps us improve our service</span>
                        </div>
                    </div>
                    
                    <div class="modal-action">
                        <button type="button" class="btn btn-outline" onclick="this.closest('dialog').close()">
                            <i class="fa-sharp fa-times"></i>
                            Back
                        </button>
                        <button type="button" class="btn btn-error gap-2" id="confirm-guest-cancellation">
                            <i class="fa-sharp fa-times-circle"></i>
                            Cancel Appointment
                        </button>
                    </div>
                </div>
                <form method="dialog" class="modal-backdrop">
                    <button>close</button>
                </form>
            `;
            
            document.body.appendChild(reasonDialog);
            reasonDialog.showModal();

            // Handle confirm cancellation
            const confirmButton = reasonDialog.querySelector('#confirm-guest-cancellation');
            const reasonTextarea = reasonDialog.querySelector('#guest-cancellation-reason');
            
            confirmButton.addEventListener('click', async () => {
                const reason = reasonTextarea.value.trim() || 'Customer initiated cancellation through online portal.';

                confirmButton.disabled = true;
                confirmButton.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Cancelling...';

                try {
                    const response = await fetch(window.location.href, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            action: 'cancel',
                            reason: reason
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    if (data.success) {
                        reasonDialog.close();
                        window.location.reload();
                    } else {
                        await showError({
                            title: 'Cancellation Failed',
                            message: data.message || 'An error occurred while cancelling your appointment.'
                        });
                        confirmButton.disabled = false;
                        confirmButton.innerHTML = '<i class="fa-sharp fa-times-circle"></i> Cancel Appointment';
                    }
                } catch (error) {
                    console.error('Error:', error);
                    await showError({
                        title: 'Network Error',
                        message: 'An error occurred while cancelling your appointment. Please try again.'
                    });
                    confirmButton.disabled = false;
                    confirmButton.innerHTML = '<i class="fa-sharp fa-times-circle"></i> Cancel Appointment';
                }
            });

            // Remove dialog when closed
            reasonDialog.addEventListener('close', () => {
                document.body.removeChild(reasonDialog);
            });
        }


        function showSuccessMessage(message) {
            // Create temporary success message
            const successDiv = document.createElement('div');
            successDiv.className = 'alert alert-success mb-4 max-w-4xl mx-auto';
            successDiv.innerHTML = `
                <div class="flex items-center gap-2">
                    <i class="fa-sharp fa-check-circle"></i>
                    <span>${message}</span>
                </div>
            `;
            
            // Insert at top of container
            const container = document.querySelector('.container');
            if (container) {
                container.insertBefore(successDiv, container.firstChild);
                
                // Remove after 3 seconds
                setTimeout(() => {
                    successDiv.remove();
                }, 3000);
            }
        }

        function showErrorMessage(message) {
            // Create temporary error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-error mb-4 max-w-4xl mx-auto';
            errorDiv.innerHTML = `
                <div class="flex items-center gap-2">
                    <i class="fa-sharp fa-exclamation-circle"></i>
                    <span>${message}</span>
                </div>
            `;
            
            // Insert at top of container
            const container = document.querySelector('.container');
            if (container) {
                container.insertBefore(errorDiv, container.firstChild);
                
                // Remove after 5 seconds
                setTimeout(() => {
                    errorDiv.remove();
                }, 5000);
            }
        }

    </script>
</body>
</html>
