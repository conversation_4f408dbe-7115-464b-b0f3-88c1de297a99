<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pickup Confirmed - {{ config('app.name') }}</title>
    @vite(['resources/css/app.css', 'resources/js/app.jsx'])
    <link href="{{ asset('fontawesome/css/fontawesome.min.css') }}" rel="stylesheet">
    <link href="{{ asset('fontawesome/css/sharp-solid.min.css') }}" rel="stylesheet">
    <link rel="icon" type="image/webp" href="{{ asset('etrflow-circlepadded.webp') }}">
</head>
<body class="bg-base-200 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="bg-base-100 border border-base-300 p-8 mb-8">
                <div class="inline-flex items-center justify-center w-12 h-12 bg-success text-success-content mb-4">
                    <i class="fa-sharp fa-check text-lg"></i>
                </div>
                <h1 class="text-2xl lg:text-3xl font-bold text-base-content mb-3">Pickup Confirmed!</h1>
                <div class="flex items-center justify-center gap-3">
                    <span class="text-base-content/60">Request #{{ $pickupRequest->id }}</span>
                    <div class="badge badge-success badge-lg">
                        Confirmed
                    </div>
                </div>
            </div>
        </div>

        <!-- Appointment Details -->
        <div class="max-w-4xl mx-auto">
            <div class="bg-base-100 border border-primary">
                <!-- Header Section -->
                <div class="bg-primary text-primary-content px-6 py-4 border-b border-base-300">
                    <h2 class="text-lg font-semibold text-primary-content">Your Confirmed Pickup Details</h2>
                    <p class="text-sm text-primary-content/80 mt-1">Your appointment has been successfully confirmed</p>
                </div>

                <!-- Content Section -->
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Contact Information -->
                        <div>
                            <h3 class="text-base font-medium text-base-content mb-4 border-b border-base-300 pb-2">Contact Information</h3>
                            <div class="space-y-4">
                                <div class="flex items-center gap-3">
                                    <i class="fa-sharp fa-user text-base-content/60 w-4 text-center"></i>
                                    <div>
                                        <span class="text-xs text-base-content/60 block">Name</span>
                                        <span class="font-medium text-base-content">{{ $pickupRequest->contact_name }}</span>
                                    </div>
                                </div>
                                @if($pickupRequest->business_name)
                                    <div class="flex items-center gap-3">
                                        <i class="fa-sharp fa-building text-base-content/60 w-4 text-center"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Business</span>
                                            <span class="font-medium text-base-content">{{ $pickupRequest->business_name }}</span>
                                        </div>
                                    </div>
                                @endif
                                <div class="flex items-center gap-3">
                                    <i class="fa-sharp fa-envelope text-base-content/60 w-4 text-center"></i>
                                    <div>
                                        <span class="text-xs text-base-content/60 block">Email</span>
                                        <span class="font-medium text-base-content break-all">{{ $pickupRequest->email }}</span>
                                    </div>
                                </div>
                                <div class="flex items-center gap-3">
                                    <i class="fa-sharp fa-phone text-base-content/60 w-4 text-center"></i>
                                    <div>
                                        <span class="text-xs text-base-content/60 block">Phone</span>
                                        <span class="font-medium text-base-content">{{ $pickupRequest->phone }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pickup Information -->
                        <div>
                            <h3 class="text-base font-medium text-base-content mb-4 border-b border-base-300 pb-2">Pickup Information</h3>
                            <div class="space-y-4">
                                @if($pickupRequest->event)
                                    <div class="flex items-start gap-3">
                                        <i class="fa-sharp fa-calendar text-base-content/60 w-4 text-center mt-1"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Scheduled Date & Time</span>
                                            @php
                                                $timezone = \App\Models\GlobalConfig::getTimeZone();
                                            @endphp
                                            <span class="font-medium text-base-content block">{{ $pickupRequest->event->start_date->setTimezone($timezone)->format('l, F j, Y') }}</span>
                                            <span class="text-primary font-semibold">{{ $pickupRequest->event->start_date->setTimezone($timezone)->format('g:i A') }}</span>
                                        </div>
                                    </div>
                                @endif
                                <div class="flex items-start gap-3">
                                    <i class="fa-sharp fa-map-marker-alt text-base-content/60 w-4 text-center mt-1"></i>
                                    <div>
                                        <span class="text-xs text-base-content/60 block">Pickup Address</span>
                                        <span class="font-medium text-base-content">{{ $pickupRequest->pickup_address }}</span>
                                    </div>
                                </div>
                                @if($pickupRequest->event && $pickupRequest->event->assignedDriver)
                                    <div class="flex items-center gap-3">
                                        <i class="fa-sharp fa-user-tie text-base-content/60 w-4 text-center"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Assigned Driver</span>
                                            <span class="font-medium text-base-content">{{ $pickupRequest->event->assignedDriver->name }}</span>
                                        </div>
                                    </div>
                                @endif
                                @if($pickupRequest->load_size)
                                    <div class="flex items-center gap-3">
                                        <i class="fa-sharp fa-boxes text-base-content/60 w-4 text-center"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Load Size</span>
                                            <span class="font-medium text-base-content capitalize">{{ str_replace('_', ' ', $pickupRequest->load_size) }}</span>
                                        </div>
                                    </div>
                                @endif
                                @if($pickupRequest->item_types)
                                    <div class="flex items-start gap-3">
                                        <i class="fa-sharp fa-list text-base-content/60 w-4 text-center mt-1"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Items</span>
                                            <span class="font-medium text-base-content">{{ is_array($pickupRequest->item_types) ? implode(', ', array_map(fn($type) => str_replace('_', ' ', $type), $pickupRequest->item_types)) : $pickupRequest->item_types }}</span>
                                        </div>
                                    </div>
                                @endif
                                @if($pickupRequest->item_specifics)
                                    <div class="flex items-start gap-3">
                                        <i class="fa-sharp fa-list-ul text-base-content/60 w-4 text-center mt-1"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Additional Item Specifics</span>
                                            <span class="font-medium text-base-content">{{ $pickupRequest->item_specifics }}</span>
                                        </div>
                                    </div>
                                @endif
                                @if($pickupRequest->accessibility_level)
                                    <div class="flex items-center gap-3">
                                        <i class="fa-sharp fa-universal-access text-base-content/60 w-4 text-center"></i>
                                        <div>
                                            <span class="text-xs text-base-content/60 block">Accessibility Level</span>
                                            <span class="font-medium text-base-content capitalize">{{ str_replace('_', ' ', $pickupRequest->accessibility_level) }}</span>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    @if($pickupRequest->driver_instructions || $pickupRequest->other_notes)
                        <div class="mt-8">
                            <div class="bg-base-200 p-6 border border-base-300">
                                <h3 class="text-base font-medium text-base-content mb-4 border-b border-base-300 pb-2">Special Instructions</h3>
                                @if($pickupRequest->driver_instructions)
                                    <div class="mb-4">
                                        <span class="text-xs text-base-content/60 block mb-1">Driver Instructions</span>
                                        <p class="text-base-content">{{ $pickupRequest->driver_instructions }}</p>
                                    </div>
                                @endif
                                @if($pickupRequest->other_notes)
                                    <div>
                                        <span class="text-xs text-base-content/60 block mb-1">Additional Notes</span>
                                        <p class="text-base-content">{{ $pickupRequest->other_notes }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- What's Next Section -->
            <div class="bg-primary-100 border border-primary mt-8">
                <!-- Header Section -->
                <div class="bg-primary text-primary-content px-6 py-4 border-b border-base-300">
                    <h3 class="text-lg font-semibold text-primary-content">What Happens Next?</h3>
                    <p class="text-sm text-primary-content/80 mt-1">Here's what you can expect with your confirmed pickup</p>
                </div>
                
                <!-- Content Section -->
                <div class="p-6">
                    <div class="space-y-6">
                        <div class="flex items-start gap-4">
                            <div class="bg-primary text-primary-content rounded-lg w-8 h-8 flex items-center justify-center flex-shrink-0">
                                <span class="text-xs font-bold">1</span>
                            </div>
                            <div>
                                <h4 class="font-medium text-base-content">We'll arrive on time</h4>
                                <p class="text-base-content/70 text-sm">Our team will arrive within +/- 15 minutes of the scheduled time. We will call you as soon as possible if there are any delays.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start gap-4">
                            <div class="bg-primary text-primary-content rounded-lg w-8 h-8 flex items-center justify-center flex-shrink-0">
                                <span class="text-xs font-bold">2</span>
                            </div>
                            <div>
                                <h4 class="font-medium text-base-content">We'll collect your electronics</h4>
                                <p class="text-base-content/70 text-sm">Our team will safely collect your electronics for secure recycling and data destruction services.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start gap-4">
                            <div class="bg-primary text-primary-content rounded-lg w-8 h-8 flex items-center justify-center flex-shrink-0">
                                <span class="text-xs font-bold">3</span>
                            </div>
                            <div>
                                <h4 class="font-medium text-base-content">Billing and documentation</h4>
                                <p class="text-base-content/70 text-sm">Small and residential pickups may be invoiced on-site. Business pickups will receive an invoice within 5 business days. Documentation such as certificates of destruction will be provided as applicable.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div class="bg-base-100 border border-neutral mt-8">
                <!-- Header Section -->
                <div class="bg-neutral px-6 py-4 border-b border-base-300">
                    <h3 class="text-lg font-semibold text-neutral-content">Need Help?</h3>
                    <p class="text-sm text-neutral-content/80 mt-1">Contact us or manage your appointment</p>
                </div>
                
                <!-- Content Section -->
                <div class="p-6">
                    <p class="text-base-content/70 mb-6">If you need to make changes to your appointment or have questions, please contact us:</p>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                        @if($contactEmail)
                        <a href="mailto:{{ $contactEmail }}" class="btn btn-primary btn-sm gap-2 shadow-sm">
                            <i class="fa-sharp fa-envelope"></i>
                            Email Us
                        </a>
                        @endif
                        @if($contactPhone)
                        <a href="tel:{{ $contactPhone }}" class="btn btn-success btn-sm gap-2 shadow-sm">
                            <i class="fa-sharp fa-phone"></i>
                            Call Us
                        </a>
                        @endif
                    </div>
                    
                    <div class="border-t border-base-300/50 pt-6 text-center">
                        <a href="{{ route('pickup.manage', $token) }}" class="btn btn-outline btn-primary gap-2">
                            <i class="fa-sharp fa-cog"></i>
                            Manage Appointment
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="text-center mt-8">
                <p class="text-base-content/60 text-sm">
                    <i class="fa-sharp fa-shield-alt mr-1"></i>
                    This is a secure confirmation page for your pickup appointment.
                </p>
                <p class="text-base-content/60 text-xs mt-2">
                    &copy; {{ date('Y') }} {{ config('app.name') }} - Pickup Management System
                </p>
            </div>
        </div>
    </div>
</body>
</html>