<x-app-layout
    page-title="Permissions Management"
    page-icon="fa-sharp fa-shield-check">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Header Card -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-base-content">Permissions Management</h1>
                            <p class="text-base-content/70 mt-1 text-lg">Manage user group permissions by component</p>
                        </div>
                        <div class="hidden lg:flex items-center gap-2 text-base-content/60">
                            <div class="stat">
                                <div class="stat-title text-xs">Total Scopes</div>
                                <div class="stat-value text-2xl">{{ count($groupedPermissions) }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Permissions by Component -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-shield-check text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Permissions by Component</h4>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        @foreach ($groupedPermissions as $scope => $permissions)
                            @php
                                $scopeDescriptions = [
                                    'general' => 'Core system access and administrative privileges',
                                    'inventory' => 'Manage inventory items, descriptions, and stock',
                                    'documents' => 'Handle document creation, editing, and management',
                                    'images' => 'Upload, edit, and manage image galleries',
                                    'users' => 'Create and manage user accounts and profiles',
                                    'customers' => 'Manage customer accounts and information',
                                    'invoices' => 'Create, edit, and manage billing invoices',
                                    'admin' => 'System administration and configuration settings',
                                    'discounts' => 'Create and manage discount codes and promotions',
                                    'reports' => 'Generate and view system reports',
                                    'tasks' => 'Create, assign, and manage tasks and workflows',
                                    'certificates' => 'Manage certificates of destruction and custody chains',
                                    'calendar' => 'Manage calendars, events, and scheduling',
                                    'timeclock' => 'Track time, manage timecards, and PTO requests',
                                    'pickups' => 'Handle pickup requests and calendar management'
                                ];
                                $scopeIcons = [
                                    'general' => 'fa-sharp fa-home',
                                    'inventory' => 'fa-sharp fa-boxes-stacked',
                                    'documents' => 'fa-sharp fa-file-text',
                                    'images' => 'fa-sharp fa-images',
                                    'users' => 'fa-sharp fa-users',
                                    'customers' => 'fa-sharp fa-user-tie',
                                    'invoices' => 'fa-sharp fa-file-invoice',
                                    'admin' => 'fa-sharp fa-cog',
                                    'discounts' => 'fa-sharp fa-percent',
                                    'reports' => 'fa-sharp fa-chart-bar',
                                    'tasks' => 'fa-sharp fa-tasks',
                                    'certificates' => 'fa-sharp fa-certificate',
                                    'calendar' => 'fa-sharp fa-calendar',
                                    'timeclock' => 'fa-sharp fa-clock',
                                    'pickups' => 'fa-sharp fa-truck'
                                ];
                            @endphp
                            <div class="collapse collapse-arrow border border-base-300 bg-base-50">
                                <input type="checkbox" name="accordion-{{ $scope }}" />
                                <div class="collapse-title">
                                    <div class="flex items-center gap-3">
                                        <div class="avatar avatar-placeholder">
                                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                                <i class="{{ $scopeIcons[$scope] ?? 'fa-sharp fa-shield' }} text-sm"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <h5 class="text-lg font-semibold text-base-content">{{ ucfirst($scope) }}</h5>
                                            <p class="text-sm text-base-content/60">{{ $scopeDescriptions[$scope] ?? 'Manage ' . $scope . ' related permissions' }}</p>
                                            <span class="text-xs text-base-content/50">({{ count($permissions) }} permissions)</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="collapse-content">
                                    <div class="pt-4 space-y-3">
                                        @foreach ($permissions as $permission)
                                            <div class="p-4 bg-base-100 rounded-lg border border-base-200 hover:bg-base-200/50 transition-all duration-200">
                                                <!-- Mobile Layout -->
                                                <div class="lg:hidden">
                                                    <div class="flex items-start justify-between mb-3">
                                                        <div class="flex-1 min-w-0">
                                                            <h6 class="font-medium text-base-content">{{ $permission->description }}</h6>
                                                            <p class="text-xs text-base-content/60 mt-1">{{ $permission->name }}</p>
                                                        </div>
                                                        <button class="btn btn-ghost btn-xs btn-square ml-2"
                                                                onclick="openPermissionModal('{{ $permission->id }}')">
                                                            <i class="fa-sharp fa-pen-to-square text-primary"></i>
                                                        </button>
                                                    </div>
                                                    <div class="flex flex-wrap gap-2">
                                                        @foreach ($permission->userGroups as $group)
                                                            <span class="badge badge-primary badge-sm cursor-pointer hover:badge-primary"
                                                                  onclick="openPermissionModal('{{ $permission->id }}')">
                                                                {{ $group->name }}
                                                            </span>
                                                        @endforeach
                                                        @if($permission->userGroups->isEmpty())
                                                            <span class="badge badge-ghost badge-sm">No groups assigned</span>
                                                        @endif
                                                    </div>
                                                </div>

                                                <!-- Desktop Layout -->
                                                <div class="hidden lg:flex lg:items-center lg:justify-between">
                                                    <div class="flex-1 min-w-0">
                                                        <h6 class="font-medium text-base-content">{{ $permission->description }}</h6>
                                                        <p class="text-sm text-base-content/60">{{ $permission->name }}</p>
                                                    </div>
                                                    <div class="flex items-center gap-3 ml-6">
                                                        <div class="flex flex-wrap gap-2">
                                                            @foreach ($permission->userGroups as $group)
                                                                <span class="badge badge-primary cursor-pointer hover:badge-primary"
                                                                      onclick="openPermissionModal('{{ $permission->id }}')">
                                                                    {{ $group->name }}
                                                                </span>
                                                            @endforeach
                                                            @if($permission->userGroups->isEmpty())
                                                                <span class="badge badge-ghost">No groups assigned</span>
                                                            @endif
                                                        </div>
                                                        <button class="btn btn-ghost btn-sm btn-square"
                                                                onclick="openPermissionModal('{{ $permission->id }}')">
                                                            <i class="fa-sharp fa-pen-to-square text-primary"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Permission Edit Modal -->
    <dialog id="permission-modal" class="modal">
        <div class="modal-box bg-base-100 max-w-2xl">
            <div class="bg-gradient-to-r from-primary/10 to-primary/5 -m-6 mb-6 px-6 py-4 border-b border-base-300/50">
                <div class="flex items-center gap-3">
                    <div class="avatar avatar-placeholder">
                        <div class="bg-primary text-primary-content w-8 rounded-lg">
                            <i class="fa-sharp fa-shield-check text-sm"></i>
                        </div>
                    </div>
                    <h3 class="text-lg font-semibold text-base-content" id="modal-title">Edit Permission</h3>
                </div>
            </div>

            <div id="modal-content" class="space-y-6">
                <div class="bg-base-50 rounded-lg p-4 border border-base-200">
                    <div class="flex items-center gap-3 mb-2">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-info text-info-content w-6 rounded">
                                <i class="fa-sharp fa-info-circle text-xs"></i>
                            </div>
                        </div>
                        <h4 class="font-medium text-base-content">Permission Details</h4>
                    </div>
                    <p class="text-base-content/70" id="permission-description"></p>
                </div>

                <div>
                    <div class="flex items-center gap-3 mb-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-secondary text-secondary-content w-6 rounded">
                                <i class="fa-sharp fa-users text-xs"></i>
                            </div>
                        </div>
                        <h4 class="font-medium text-base-content">User Groups</h4>
                    </div>
                    <div id="user-groups-container" class="space-y-3">
                        <!-- User groups will be populated here -->
                    </div>
                </div>
            </div>

            <div class="modal-action mt-8">
                <form method="dialog">
                    <button class="btn btn-primary btn-lg gap-2">
                        <i class="fa-sharp fa-check"></i>
                        Done
                    </button>
                </form>
            </div>
        </div>
    </dialog>

    @push('scripts')
    <script>
        // Store all user groups for easy access
        const userGroups = @json($userGroups);
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        let currentPermissionId = null;

        // Function to open the permission modal
        function openPermissionModal(permissionId) {
            currentPermissionId = permissionId;
            const modal = document.getElementById('permission-modal');

            // Fetch permission details
            fetch(`/permissions/${permissionId}/details`)
                .then(response => response.json())
                .then(data => {
                    // Update modal content
                    document.getElementById('modal-title').textContent = `Edit Permission: ${data.permission.name}`;
                    document.getElementById('permission-description').textContent = data.permission.description;

                    // Populate user groups
                    const container = document.getElementById('user-groups-container');
                    container.innerHTML = '';

                    // Get the IDs of the user groups assigned to this permission
                    const assignedGroupIds = data.permission.user_groups.map(group => group.id);

                    // Create a checkbox for each user group
                    userGroups.forEach(group => {
                        const isAdmin = group.name === 'Admin';
                        const isChecked = assignedGroupIds.includes(group.id);

                        const div = document.createElement('div');
                        div.className = `p-3 bg-base-100 rounded-lg border border-base-200 hover:bg-base-200/50 transition-all duration-200 ${isAdmin ? 'opacity-75' : ''}`;
                        div.innerHTML = `
                            <label class="flex items-center gap-3 cursor-pointer">
                                <input type="checkbox" class="checkbox checkbox-primary"
                                       data-group-id="${group.id}"
                                       ${isChecked ? 'checked' : ''}
                                       ${isAdmin ? 'disabled' : ''}
                                       onchange="updatePermission(this)">
                                <div class="flex-1">
                                    <div class="flex items-center gap-2">
                                        <span class="font-medium text-base-content">${group.name}</span>
                                        ${isAdmin ? '<span class="badge badge-primary badge-xs">Always enabled</span>' : ''}
                                    </div>
                                    ${group.description ? `<p class="text-sm text-base-content/60 mt-1">${group.description}</p>` : ''}
                                </div>
                            </label>
                        `;
                        container.appendChild(div);
                    });

                    // Show the modal
                    modal.showModal();
                })
                .catch(error => {
                    console.error('Error fetching permission details:', error);
                    alert('Failed to load permission details. Please try again.');
                });
        }

        // Function to update permission
        function updatePermission(checkbox) {
            const groupId = checkbox.dataset.groupId;
            const isChecked = checkbox.checked;
            const labelContainer = checkbox.nextElementSibling.querySelector('.font-medium');

            // Show loading state
            checkbox.disabled = true;
            const originalLabel = labelContainer.textContent;
            labelContainer.innerHTML = `<span class="loading loading-spinner loading-xs mr-2"></span>${originalLabel}`;

            // Send update to server
            fetch(`/permissions/${currentPermissionId}/update-groups`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    group_id: groupId,
                    action: isChecked ? 'add' : 'remove'
                })
            })
            .then(response => response.json())
            .then(data => {
                // Reset loading state
                checkbox.disabled = false;
                labelContainer.textContent = originalLabel;

                // Update the badges in the permission cards
                updatePermissionBadges(currentPermissionId, data.user_groups);
            })
            .catch(error => {
                console.error('Error updating permission:', error);
                alert('Failed to update permission. Please try again.');

                // Reset checkbox state and loading
                checkbox.checked = !isChecked;
                checkbox.disabled = false;
                labelContainer.textContent = originalLabel;
            });
        }

        // Function to update the badges in the permission cards
        function updatePermissionBadges(permissionId, userGroups) {
            // Find all badge containers for this permission (mobile and desktop)
            const permissionCards = document.querySelectorAll(`[onclick*="openPermissionModal('${permissionId}')"]`);

            permissionCards.forEach(card => {
                const permissionCard = card.closest('.p-4');
                if (!permissionCard) return;

                // Update mobile layout badges
                const mobileBadgeContainer = permissionCard.querySelector('.lg\\:hidden .flex-wrap');
                if (mobileBadgeContainer) {
                    updateBadgeContainer(mobileBadgeContainer, userGroups, permissionId, 'badge-sm');
                }

                // Update desktop layout badges
                const desktopBadgeContainer = permissionCard.querySelector('.lg\\:flex .flex-wrap');
                if (desktopBadgeContainer) {
                    updateBadgeContainer(desktopBadgeContainer, userGroups, permissionId, '');
                }
            });
        }

        // Helper function to update badge containers
        function updateBadgeContainer(container, userGroups, permissionId, sizeClass) {
            // Clear existing badges (but keep the edit button)
            const badges = container.querySelectorAll('.badge');
            badges.forEach(badge => badge.remove());

            // Add new badges
            userGroups.forEach(group => {
                const badge = document.createElement('span');
                badge.className = `badge badge-primary cursor-pointer hover:badge-primary ${sizeClass}`;
                badge.textContent = group.name;
                badge.setAttribute('onclick', `openPermissionModal('${permissionId}')`);
                container.insertBefore(badge, container.lastElementChild);
            });

            // Add "No groups assigned" badge if empty
            if (userGroups.length === 0) {
                const emptyBadge = document.createElement('span');
                emptyBadge.className = `badge badge-ghost ${sizeClass}`;
                emptyBadge.textContent = 'No groups assigned';
                container.insertBefore(emptyBadge, container.lastElementChild);
            }
        }
    </script>
    @endpush
</x-app-layout>
