<x-app-layout :title="'Edit Task'">
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Edit Task
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 p-6">
                <h3 class="text-lg font-semibold mb-4">Edit Task</h3>

                <form action="{{ route('tasks.update', $task->id) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <!-- Task Title -->
                    <div class="form-control mb-4">
                        <label for="title" class="label">
                            <span class="label-text">Task Title</span>
                        </label>
                        <input type="text" name="title" id="title" class="input input-bordered"
                            value="{{ old('title', $task->title) }}" required>
                    </div>

                    <!-- Description -->
                    <div class="form-control mb-4">
                        <label for="description" class="label">
                            <span class="label-text">Description</span>
                        </label>
                        <textarea name="description" id="description" class="textarea textarea-bordered">{{ old('description', $task->description) }}</textarea>
                    </div>

                    <!-- Due Type -->
                    <div class="form-control mb-4">
                        <label class="label">
                            <span class="label-text">Due at...</span>
                        </label>
                        <div class="flex gap-4">
                            <label class="flex items-center gap-2">
                                <input type="radio" name="due_type" value="specific_time" class="radio"
                                    {{ old('due_type', $task->due_type) == 'specific_time' ? 'checked' : '' }}>
                                Specific Time of Day
                            </label>
                            <label class="flex items-center gap-2">
                                <input type="radio" name="due_type" value="any_time" class="radio"
                                    {{ old('due_type', $task->due_type) == 'any_time' ? 'checked' : '' }}>
                                Any Time That Day
                            </label>
                        </div>
                    </div>

                    <!-- Due Time -->
                    <div id="due_time_section" class="form-control mb-4">
                        <label for="due_time" class="label">
                            <span class="label-text">Due Time</span>
                        </label>
                        <input type="time" name="due_time" id="due_time" class="input input-bordered"
                            value="{{ old('due_time', $task->recurrences->due_time ?? '') }}">
                    </div>

                    <!-- Repeat Interval -->
                    <div class="form-control mb-4">
                        <label class="label">
                            <span class="label-text">Repeat Interval</span>
                        </label>
                        <select name="repeat_interval" id="repeat_interval" class="select select-bordered">
                            <option value="once"
                                {{ old('repeat_interval', $task->recurrences->repeat_interval ?? 'once') == 'once' ? 'selected' : '' }}>
                                Once
                            </option>
                            <option value="daily"
                                {{ old('repeat_interval', $task->recurrences->repeat_interval ?? '') == 'daily' ? 'selected' : '' }}>
                                Daily
                            </option>
                            {{-- <option value="weekly"
                                {{ old('repeat_interval', $task->recurrences->repeat_interval ?? '') == 'weekly' ? 'selected' : '' }}>
                                Weekly
                            </option> --}}
                            {{-- <option value="monthly"
                                {{ old('repeat_interval', $task->recurrences->repeat_interval ?? '') == 'monthly' ? 'selected' : '' }}>
                                Monthly
                            </option> --}}
                            <option value="custom"
                                {{ old('repeat_interval', $task->recurrences->repeat_interval ?? '') == 'custom' ? 'selected' : '' }}>
                                Custom
                            </option>
                        </select>
                    </div>

                    <!-- Start Date -->
                    <div id="start_date_section" class="form-control mb-4">
                        <label for="start_date" class="label">
                            <span class="label-text">Start Date</span>
                        </label>
                        <input type="date" name="start_date" id="start_date" class="input input-bordered"
                            value="{{ old('start_date', $task->start_date ? $task->start_date->format('Y-m-d') : '') }}">
                    </div>

                    <!-- End Date -->
                    <div class="form-control mb-4">
                        <label for="end_date" id="end_date_label" class="label">
                            <span class="label-text">End Date</span>
                        </label>
                        <input type="date" name="end_date" id="end_date" class="input input-bordered"
                            value="{{ old('end_date', $task->end_date ? $task->end_date->format('Y-m-d') : '') }}">
                    </div>

                    <div class="form-control mb-4">
                        <!-- Repeat Days -->
                        <div id="repeat_days_section" class="form-control mb-4 hidden">
                            <label class="label">
                                <span class="label-text">Repeat Days</span>
                            </label>
                            <div class="flex gap-2">
                                @foreach (['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                    <label class="flex items-center gap-2">
                                        <input type="checkbox" name="repeat_days[]" value="{{ $day }}"
                                            class="checkbox"
                                            {{ in_array($day, old('repeat_days', $task->recurrences->repeat_days ?? [])) ? 'checked' : '' }}>
                                        {{ $day }}
                                    </label>
                                @endforeach
                            </div>
                        </div>
                    
                        <!-- Excluded Dates -->
                        <div id="excluded_dates_section" class="form-control mb-4 hidden">
                            <label class="label">
                                <span class="label-text">Excluded Dates</span>
                            </label>
                            <input type="text" id="excluded_dates_input" class="input input-bordered w-full mb-2"
                                placeholder="Add dates">
                            <ul id="excluded_dates_list" class="list-disc pl-5 text-sm">
                                @foreach (old('excluded_dates', $task->recurrences->excluded_dates ?? []) as $date)
                                    <li>
                                        <span>{{ $date }}</span>
                                        <button type="button" class="btn btn-xs btn-error remove-date">Remove</button>
                                        <input type="hidden" name="excluded_dates[]" value="{{ $date }}">
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                    


            <!-- Assignees -->
            @perms('create_tasks')
            <div class="form-control mb-4">
                <label class="label">
                    <span class="label-text">Assignees</span>
                </label>
                <div class="flex items-center gap-4">
                    <!-- User Selection -->
                    <select name="assignees[]" class="select select-bordered w-full" multiple>
                        <option disabled>Users</option>
                        @foreach ($users as $user)
                            <option value="user:{{ $user->id }}"
                                {{ in_array("user:{$user->id}", old('assignees', $task->assignees->map(fn($assignee) => "user:{$assignee->assignee_id}")->toArray())) ? 'selected' : '' }}>
                                {{ $user->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            @endperms


            <!-- Save Button -->
            <div class="flex justify-end">
                <button type="submit" class="btn btn-primary">Save Task</button>
            </div>
            </form>
        </div>
    </div>
    </div>


@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    const repeatInterval = document.getElementById('repeat_interval');
    const repeatDaysSection = document.getElementById('repeat_days_section');
    const excludedDatesSection = document.getElementById('excluded_dates_section');
    const startDateSection = document.getElementById('start_date_section');
    const endDateLabel = document.getElementById('end_date_label');
    const dueType = document.querySelectorAll('input[name="due_type"]');
    const dueTimeSection = document.getElementById('due_time_section');

    const toggleCustomFields = () => {
        const value = repeatInterval.value;
        const showCustomFields = value === 'custom';
        const showExcludedDates = value !== 'once';
        const showStartDate = value !== 'once';
        const endDateLabelText = value === 'once' ? 'Due Date' : 'End Date';

        repeatDaysSection.classList.toggle('hidden', !showCustomFields);
        excludedDatesSection.classList.toggle('hidden', !showExcludedDates);
        startDateSection.classList.toggle('hidden', !showStartDate);
        endDateLabel.textContent = endDateLabelText;
    };

    const toggleDueTime = () => {
        const value = document.querySelector('input[name="due_type"]:checked').value;
        dueTimeSection.classList.toggle('hidden', value === 'any_time');
    };

    repeatInterval.addEventListener('change', toggleCustomFields);
    dueType.forEach(radio => radio.addEventListener('change', toggleDueTime));

    toggleCustomFields(); // Initial load
    toggleDueTime(); // Initial load
});
</script>
@endpush


</x-app-layout>