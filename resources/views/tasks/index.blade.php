<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Tasks
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 p-6 shadow-lg">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">Task List</h3>
                    <a href="{{ route('tasks.create') }}" class="btn btn-primary">Create New Task</a>
                </div>

                @if($tasks->isEmpty())
                    <p class="text-gray-500">No tasks available.</p>
                @else
                    <div class="overflow-x-auto">
                        <table class="table w-full table-zebra">
                            <thead>
                                <tr>
                                    <th>
                                        <a href="{{ route('tasks.index', ['sort' => 'title', 'order' => $order === 'asc' ? 'desc' : 'asc']) }}" class="link">
                                            Title {{ $sort === 'title' ? ($order === 'asc' ? '▲' : '▼') : '' }}
                                        </a>
                                    </th>
                                    <th>
                                        <a href="{{ route('tasks.index', ['sort' => 'due_type', 'order' => $order === 'asc' ? 'desc' : 'asc']) }}" class="link">
                                            Due Type {{ $sort === 'due_type' ? ($order === 'asc' ? '▲' : '▼') : '' }}
                                        </a>
                                    </th>
                                    <th>
                                        <a href="{{ route('tasks.index', ['sort' => 'type', 'order' => $order === 'asc' ? 'desc' : 'asc']) }}" class="link">
                                            Type {{ $sort === 'type' ? ($order === 'asc' ? '▲' : '▼') : '' }}
                                        </a>
                                    </th>
                                    <th>
                                        <a href="{{ route('tasks.index', ['sort' => 'end_date', 'order' => $order === 'asc' ? 'desc' : 'asc']) }}" class="link">
                                            End Date {{ $sort === 'end_date' ? ($order === 'asc' ? '▲' : '▼') : '' }}
                                        </a>
                                    </th>
                                    <th>Created By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($tasks as $task)
                                    <tr>
                                        <td>
                                            <a href="{{ route('tasks.edit', $task) }}" class="link link-primary">
                                                {{ $task->title }}
                                            </a>
                                        </td>
                                        <td>{{ ucfirst(str_replace('_', ' ', $task->due_type)) }}</td>
                                        <td>{{ ucfirst($task->type) }}</td>
                                        <td>{{ $task->end_date ? $task->end_date->format('M d, Y') : 'N/A' }}</td>
                                        <td>{{ $task->creator->name ?? 'Unknown' }}</td>
                                        <td>
                                            <div class="flex space-x-2">
                                                <a href="{{ route('tasks.edit', $task) }}" class="btn btn-sm btn-warning">Edit</a>
                                                <form action="{{ route('tasks.destroy', $task) }}" method="POST">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-error"
                                                            onclick="return confirm('Are you sure?')">
                                                        Delete
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        <x-pagination :paginator="$tasks" :pagination="$pagination" />
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
