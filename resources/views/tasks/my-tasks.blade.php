<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Assigned Tasks For {{ auth()->user()->name }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @perms('create_tasks, create_edit_own_tasks')
            <a href="{{ route('tasks.create') }}" class="btn btn-primary mb-4 btn-sm">
                <i class="fa-solid fa-plus"></i>
                Add New Task</a>
            @endperms
            <div class="mb-4 flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
                <div class="card bg-base-100 p-4 shadow-md">
                    <h3 class="text-sm font-semibold mb-2">Select Week</h3>
                    <div class="flex gap-2">
                        <a href="{{ route('my-tasks', ['week' => $previousWeek]) }}" class="btn btn-sm btn-secondary">Previous</a>
                        <a href="{{ route('my-tasks', ['week' => $currentWeek]) }}" class="btn btn-sm btn-secondary">Current</a>
                        <a href="{{ route('my-tasks', ['week' => $nextWeek]) }}" class="btn btn-sm btn-secondary">Next</a>
                    </div>
                </div>
                <div class="card bg-base-100 p-4 shadow-md">
                    <h3 class="text-sm font-semibold mb-2">Select Range</h3>
                    <div class="flex gap-2">
                        <input type="date" id="start-date" class="input input-bordered input-sm" value="{{ $startDate }}">
                        <input type="date" id="end-date" class="input input-bordered input-sm" value="{{ $endDate }}">
                        <button id="select-range-btn" class="btn btn-primary btn-sm">Select Range</button>
                    </div>
                </div>
            </div>

            @if (empty($taskOccurrences))
                <div class="card bg-base-100 p-4 shadow-md">
                    <p class="text-gray-500">You have no tasks scheduled for the selected period.</p>
                </div>
            @else
                @php
                    $today = now()->toDateString();
                    $tomorrow = now()->addDay()->toDateString();
                    $yesterday = now()->subDay()->toDateString();
                    $period = \Carbon\CarbonPeriod::create($startDate, $endDate);
                @endphp

                @foreach ($period as $date)
                    @php
                        $dateString = $date->toDateString();
                        $tasks = $taskOccurrences[$dateString] ?? [];
                        $completed = $tasks['total_complete'] ?? 0;
                        $total = $tasks['total_tasks'] ?? 0;
                        $isToday = $dateString === $today;
                        $isTomorrow = $dateString === $tomorrow;
                        $isYesterday = $dateString === $yesterday;
                        $dateTitle = $isToday ? 'Today' : ($isTomorrow ? 'Tomorrow' : ($isYesterday ? 'Yesterday' : ''));
                        $formattedDate = $dateTitle ? "$dateTitle - " . $date->format('D, M j, Y') : $date->format('D, M j, Y');
                    @endphp
                    <livewire:task-day :date="$dateString" :tasks="$tasks" :formattedDate="$formattedDate" :isToday="$isToday" />
                @endforeach
            @endif
        </div>
    </div>

    <script>
        document.getElementById('select-range-btn').addEventListener('click', function () {
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            if (startDate && endDate) {
                window.location.href = `{{ route('my-tasks') }}?start=${startDate}&end=${endDate}`;
            }
        });
    </script>
</x-app-layout>
