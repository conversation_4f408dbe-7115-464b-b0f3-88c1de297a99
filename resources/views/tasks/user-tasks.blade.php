<x-app-layout :title="__('Tasks')">
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Tasks For {{ $user->name }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (empty($taskOccurrences))
                <div class="card bg-base-100 p-4 shadow-md">
                    <p class="text-gray-500">{{ $user->name }} has no tasks scheduled for the upcoming week.</p>
                </div>
            @else
                @php
                    $previousDaysTasks = [];
                    $upcomingTasks = [];
                    $today = now()->toDateString();

                    foreach ($taskOccurrences as $date => $tasks) {
                        if ($date < $today) {
                            $previousDaysTasks[$date] = $tasks;
                        } else {
                            $upcomingTasks[$date] = $tasks;
                        }
                    }

                    function getGradientColor($completed, $total) {
                        if ($total == 0) {
                            return 'rgb(113, 0, 0)'; // Default red for no tasks
                        }

                        $percent = ($completed / $total) * 100;

                        $startColor = ['r' => 113, 'g' => 0, 'b' => 0]; // Red
                        $midColor = ['r' => 111, 'g' => 100, 'b' => 0]; // Yellow
                        $endColor = ['r' => 0, 'g' => 121, 'b' => 16];  // Green

                        if ($percent <= 50) {
                            $ratio = $percent / 50;
                            $r = round($startColor['r'] + $ratio * ($midColor['r'] - $startColor['r']));
                            $g = round($startColor['g'] + $ratio * ($midColor['g'] - $startColor['g']));
                            $b = round($startColor['b'] + $ratio * ($midColor['b'] - $startColor['b']));
                        } else {
                            $ratio = ($percent - 50) / 50;
                            $r = round($midColor['r'] + $ratio * ($endColor['r'] - $midColor['r']));
                            $g = round($midColor['g'] + $ratio * ($endColor['g'] - $midColor['g']));
                            $b = round($midColor['b'] + $ratio * ($endColor['b'] - $midColor['b']));
                        }

                        return "rgb($r, $g, $b)";
                    }
                @endphp

                <!-- Previous Days -->
                @if (!empty($previousDaysTasks))
                    <div tabindex="0" class="collapse bg-base-200 mb-6 shadow-lg">
                        <input type="checkbox" />
                        <div class="collapse-title text-lg font-semibold">
                            Previous Days
                            @php
                                $completedPrevious = collect($previousDaysTasks)->sum('total_complete');
                                $totalPrevious = collect($previousDaysTasks)->sum('total_tasks');
                            @endphp
                            <span class="badge text-white ml-2 px-2 py-1"
                                style="background-color: {{ getGradientColor($completedPrevious, $totalPrevious) }};">
                                <span id="previous-tasks-completed">{{ $completedPrevious }}</span>/<span
                                    id="previous-tasks-total">{{ $totalPrevious }}</span>
                                <span class="ps-1">Tasks Completed</span>
                            </span>
                        </div>
                        <div class="collapse-content space-y-4">
                            @foreach ($previousDaysTasks as $date => $tasks)
                                <div tabindex="0"
                                    class="collapse collapse-arrow border border-base-300 bg-base-100 shadow-md">
                                    <input type="checkbox" />
                                    <div class="collapse-title text-lg font-semibold flex justify-between items-center">
                                        <span>{{ \Carbon\Carbon::parse($date)->format('D, M j, Y') }}</span>
                                        @php
                                            $completed = $tasks['total_complete'];
                                            $total = $tasks['total_tasks'];
                                        @endphp
                                        <div class="badge text-white px-2 py-1"
                                            style="background-color: {{ getGradientColor($completed, $total) }};">
                                            <span id="completed-{{ $date }}">{{ $completed }}</span>/<span
                                                id="total-{{ $date }}">{{ $total }}</span>
                                        </div>
                                    </div>
                                    <div class="collapse-content space-y-4">
                                        @foreach ($tasks['tasks'] as $task)
                                            <li class="flex justify-between items-center p-4 bg-base-100 rounded-lg shadow-md hover:bg-gray-200 transition-all"
                                                data-task-id="{{ $task['task_id'] }}">
                                                <div class="flex-1">
                                                    <h3 class="task-title font-semibold text-lg {{ $task['completed_count'] > 0 ? 'line-through text-gray-400' : '' }}">
                                                        {{ $task['title'] }}
                                                    </h3>
                                                    <p class="text-sm text-gray-500">
                                                        Due:
                                                        {{ $task['due_time'] === 'Any time' ? 'Any time' : \Carbon\Carbon::parse($task['due_time'])->format('h:i A') }}
                                                    </p>
                                                    @if (!empty($task['description']))
                                                        <p class="text-sm text-gray-600">{{ $task['description'] }}</p>
                                                    @endif
                                                </div>
                                            </li>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Upcoming Days -->
                @foreach ($upcomingTasks as $date => $tasks)
                    @php
                        $completed = $tasks['total_complete'];
                        $total = $tasks['total_tasks'];
                        $isToday = $date === now()->toDateString();
                        $isTomorrow = $date === now()->addDay()->toDateString();
                        $formattedDate = $isToday
                            ? 'Today, ' . now()->format('D M j, Y')
                            : ($isTomorrow
                                ? 'Tomorrow, ' . now()->addDay()->format('D M j, Y')
                                : \Carbon\Carbon::parse($date)->format('D, M j, Y'));
                    @endphp
                    <div tabindex="0"
                        class="collapse collapse-arrow border border-base-300 bg-base-200 mb-6 shadow-lg">
                        <input type="checkbox" {{ $isToday || $isTomorrow ? 'checked' : '' }} />
                        <div class="collapse-title text-lg font-semibold flex justify-between items-center">
                            <span>{{ $formattedDate }}</span>
                            <div class="badge text-white px-2 py-1"
                                style="background-color: {{ getGradientColor($completed, $total) }};">
                                <span id="completed-{{ $date }}">{{ $completed }}</span>/<span
                                    id="total-{{ $date }}">{{ $total }}</span>
                                <span class="ps-1">Tasks Completed</span>
                            </div>
                        </div>
                        <div class="collapse-content space-y-4">
                            @foreach ($tasks['tasks'] as $task)
                                <li class="flex justify-between items-center p-4 bg-base-100 rounded-lg shadow-md hover:bg-gray-200 transition-all"
                                    data-task-id="{{ $task['task_id'] }}">
                                    <div class="flex-1">
                                        <h3 class="task-title font-semibold text-lg {{ $task['completed_count'] > 0 ? 'line-through text-gray-400' : '' }}">
                                            {{ $task['title'] }}
                                        </h3>
                                        <p class="text-sm text-gray-500">
                                            Due:
                                            {{ $task['due_time'] === 'Any time' ? 'Any time' : \Carbon\Carbon::parse($task['due_time'])->format('h:i A') }}
                                        </p>
                                        @if (!empty($task['description']))
                                            <p class="text-sm text-gray-600">{{ $task['description'] }}</p>
                                        @endif
                                    </div>
                                </li>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            @endif
        </div>
    </div>
</x-app-layout>
