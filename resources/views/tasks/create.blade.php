<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Create Task
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 p-6 shadow-lg">
                <h3 class="text-lg font-bold mb-4">Create New Task</h3>
                <form method="POST" action="{{ route('tasks.store') }}">
                    @csrf
                    <div class="form-control mb-4">
                        <label class="label" for="title">
                            <span class="label-text font-bold">Task Title</span>
                        </label>
                        <input type="text" name="title" id="title" class="input input-bordered w-full" 
                               value="{{ old('title') }}" required>
                    </div>

                    <div class="form-control mb-4">
                        <label class="label" for="description">
                            <span class="label-text font-bold">Task Description</span>
                        </label>
                        <textarea name="description" id="description" class="textarea textarea-bordered w-full" rows="4">{{ old('description') }}</textarea>
                    </div>

                    <div class="form-control mb-4">
                        <label class="label" for="due_type">
                            <span class="label-text font-bold">Due Type</span>
                        </label>
                        <select name="due_type" id="due_type" class="select select-bordered w-full">
                            <option value="specific_time" {{ old('due_type') == 'specific_time' ? 'selected' : '' }}>Specific Time</option>
                            <option value="any_time" {{ old('due_type') == 'any_time' ? 'selected' : '' }}>Any Time</option>
                        </select>
                    </div>

                    <div class="form-control mb-4">
                        <label class="label" for="type">
                            <span class="label-text font-bold">Task Type</span>
                        </label>
                        <select name="type" id="type" class="select select-bordered w-full">
                            <option value="simple" {{ old('type') == 'simple' ? 'selected' : '' }}>Simple</option>
                            <option value="checklist" {{ old('type') == 'checklist' ? 'selected' : '' }}>Checklist</option>
                        </select>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="btn btn-primary">Create Task</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
