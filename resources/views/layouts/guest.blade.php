<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-theme="corporate">
<x-head :title="$title ?? config('app.name', 'ETRFlow')" />

<body class="font-sans antialiased">
    <!-- Google reCAPTCHA v3 -->
    @if(\App\Models\GlobalConfig::isRecaptchaConfigured())
        <script src="https://www.google.com/recaptcha/api.js?render={{ \App\Models\GlobalConfig::getRecaptchaSiteKey() }}"></script>
        <script>
            // Initialize reCAPTCHA when ready
            window.recaptchaReady = false;
            window.recaptchaSiteKey = '{{ \App\Models\GlobalConfig::getRecaptchaSiteKey() }}';

            // Function to execute reCAPTCHA
            window.executeRecaptcha = function(action) {
                return new Promise((resolve, reject) => {
                    if (!window.grecaptcha) {
                        reject(new Error('reCAPTCHA not loaded'));
                        return;
                    }

                    grecaptcha.ready(function() {
                        grecaptcha.execute(window.recaptchaSiteKey, {action: action})
                            .then(function(token) {
                                resolve(token);
                            })
                            .catch(function(error) {
                                reject(error);
                            });
                    });
                });
            };

            // Mark reCAPTCHA as ready when loaded
            document.addEventListener('DOMContentLoaded', function() {
                if (window.grecaptcha) {
                    grecaptcha.ready(function() {
                        window.recaptchaReady = true;
                        console.log('reCAPTCHA v3 loaded successfully');
                    });
                }
            });
        </script>
    @endif

    <div class="font-sans antialiased">
        {{ $slot }}
    </div>

    @stack('scripts')
</body>
</html>
