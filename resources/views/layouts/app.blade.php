<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-theme="corporate">
<x-head :title="$pageTitle ?? config('app.name', 'ETRFlow')" />


<body class="font-sans antialiased flex flex-col min-h-screen">
    <div id="bannerContainer" class="transition-transform duration-300">
        <x-banner />
    </div>

    <!-- Page Heading - Full Width -->
    @if (isset($header) || isset($pageTitle))
        <x-app-header
            :page-title="$pageTitle ?? null"
            :page-icon="$pageIcon ?? null"
            :action-buttons="$actionButtons ?? []"
            :primary-buttons="$primaryButtons ?? []"
            :breadcrumbs="$breadcrumbs ?? []">
            @if(isset($header))
                {{ $header }}
            @endif
        </x-app-header>
    @endif

    <div class="flex-1 bg-base-200 overflow-hidden flex flex-col min-h-0">
        <x-drawer-menu>
            <!-- Page Content -->
            <main class="px-2">
                {{ $slot }}
            </main>

        @if (session('success'))
            <!-- Toast -->
            <div id="toast" class="toast toast-end">
                <div class="alert alert-success shadow-lg">
                    <div>
                        <i class="fa-sharp fa-check-circle text-success-content"></i>
                        <span>{{ session('success') }}</span>
                    </div>
                </div>
            </div>
        @endif


<!-- Hidden Toast that may be reused by any component -->
<div id="dynamicToast" class="toast toast-end hidden">
    <div class="alert alert-success shadow-lg">
        <div>
            <i class="fa-sharp fa-check-circle text-success-content"></i>
            <span id="dynamicToastMessage"></span>
        </div>
    </div>
</div>


        @stack('modals')

        <!-- Universal App Dialog Component -->
        <x-app-dialog />

        </x-drawer-menu>
    </div>

    <!-- Mobile Dock -->
    <x-mobile-dock />

    <!-- Global Notification System -->
    <x-notification-system />

    <script>
        // Auto-hide toast after 5 seconds
        document.addEventListener('DOMContentLoaded', () => {
            const toast = document.getElementById('toast');
            if (toast) {
                setTimeout(() => {
                    toast.remove();
                }, 5000);
            }

            // Header functionality moved to app-header component
            
            // Global AJAX handler for disabled user accounts
            // Set up global error handler for fetch requests
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                return originalFetch.apply(this, args).then(response => {
                    if (response.status === 401 && response.headers.get('content-type')?.includes('application/json')) {
                        return response.json().then(data => {
                            if (data.error && data.redirect) {
                                window.location.href = data.redirect;
                                return Promise.reject(new Error(data.error));
                            }
                            return Promise.reject(response);
                        });
                    }
                    return response;
                });
            };
            
            // Handle XMLHttpRequest (for jQuery and other libraries)
            const originalXHROpen = XMLHttpRequest.prototype.open;
            XMLHttpRequest.prototype.open = function(...args) {
                this.addEventListener('load', function() {
                    if (this.status === 401 && this.getResponseHeader('content-type')?.includes('application/json')) {
                        try {
                            const data = JSON.parse(this.responseText);
                            if (data.error && data.redirect) {
                                window.location.href = data.redirect;
                                return;
                            }
                        } catch (e) {
                            // Not JSON, continue normally
                        }
                    }
                });
                return originalXHROpen.apply(this, args);
            };
        });
    </script>

    <!-- Browser Push Notifications -->
    <script src="{{ asset('js/browser-notifications.js') }}"></script>
    
    @stack('scripts')
</body>



</html>
