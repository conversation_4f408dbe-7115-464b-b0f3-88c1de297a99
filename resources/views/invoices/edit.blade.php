<x-app-layout
    page-title="Edit Invoice: {{ $invoice->invoice_number }}"
    page-icon="fa-sharp fa-edit"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('invoices.index'),
            'text' => 'Back to Invoices'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'View Invoice',
            'route' => route('invoices.show', $invoice),
            'icon' => 'fa-sharp fa-eye',
            'class' => 'btn btn-info btn-sm gap-2'
        ],
        [
            'name' => 'PDF Export',
            'route' => route('invoices.pdf', $invoice),
            'icon' => 'fa-sharp fa-file-pdf',
            'class' => 'btn btn-secondary btn-sm gap-2',
            'target' => '_blank'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Invoices', 'route' => 'invoices.index', 'icon' => 'fa-file-invoice'],
        ['name' => 'Edit Invoice: ' . $invoice->invoice_number, 'icon' => 'fa-edit']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Left Column -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Invoice & Payment Details Card -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="px-6 py-4 border-b border-base-300">
                            <h4 class="text-lg font-semibold text-base-content">Invoice & Payment Details</h4>
                        </div>
                        <div class="p-6">
                            <form action="{{ route('invoices.update', $invoice) }}" method="POST" id="invoiceForm">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="invoice_id" value="{{ $invoice->id }}">
                                
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <!-- Left Section: Customer & Date -->
                                    <div class="space-y-4">
                                        <!-- Customer Search -->
                                        <div>
                                            <label>
                                                <span class="label-text">
                                                    <i class="fa-sharp fa-user text-primary mr-2"></i>
                                                    Customer <span class="text-error font-bold">*</span>
                                                </span>
                                            </label>
                                            <x-dynamic-customer-search 
                                                id="invoice_customer_search"
                                                name="customer_id"
                                                placeholder="Search for customer..."
                                                action="form"
                                                :selected-id="$invoice->customer_id"
                                                :selected-name="$invoice->customer->name"
                                            />
                                            <div class="mt-2">
                                                <a href="{{ route('customers.show', $invoice->customer) }}"
                                                    class="text-primary text-sm hover:link-hover" target="_blank">
                                                    <i class="fa-sharp fa-square-arrow-up-right"></i>
                                                    View Customer Profile
                                                </a>
                                            </div>
                                        </div>

                                        <!-- Invoice Date -->
                                        <div>
                                            <label for="invoice_date">
                                                <span class="label-text">
                                                    <i class="fa-sharp fa-calendar text-primary mr-2"></i>
                                                    Invoice Date <span class="text-error font-bold">*</span>
                                                </span>
                                            </label>
                                            <input type="date" name="invoice_date" id="invoice_date"
                                                class="input input-bordered w-full auto-save-invoice"
                                                value="{{ old('invoice_date', $invoice->invoice_date ? $invoice->invoice_date->format('Y-m-d') : '') }}"
                                                required>
                                            <div>
                                                <span class="label-text-alt text-base-content/70">Defaults to today if left blank</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Right Section: Payment Details -->
                                    <div class="space-y-4">
                                        <!-- Status -->
                                        <div>
                                            <label for="status">
                                                <span class="label-text">
                                                    <i class="fa-sharp fa-flag text-primary mr-2"></i>
                                                    Status <span class="text-error font-bold">*</span>
                                                </span>
                                            </label>
                                            <select name="status" id="status"
                                                class="select select-bordered w-full auto-save-invoice" required>
                                                <option value="Pending"
                                                    {{ old('status', $invoice->status) === 'Pending' ? 'selected' : '' }}>
                                                    Pending
                                                </option>
                                                <option value="Paid"
                                                    {{ old('status', $invoice->status) === 'Paid' ? 'selected' : '' }}>
                                                    Paid
                                                </option>
                                                <option value="Fulfilled"
                                                    {{ old('status', $invoice->status) === 'Fulfilled' ? 'selected' : '' }}>
                                                    Fulfilled
                                                </option>
                                            </select>

                                        </div>

                                        <!-- Payment Method -->
                                        <div>
                                            <label for="payment_method">
                                                <span class="label-text">
                                                    <i class="fa-sharp fa-wallet text-primary mr-2"></i>
                                                    Payment Method <span class="text-error font-bold">*</span>
                                                </span>
                                            </label>
                                            <select name="payment_method" id="payment_method"
                                                class="select select-bordered w-full auto-save-invoice" required>
                                                <option value="">Select a payment method</option>
                                                <option value="Cash"
                                                    {{ old('payment_method', $invoice->payment_method) === 'Cash' ? 'selected' : '' }}>
                                                    Cash
                                                </option>
                                                <option value="Check"
                                                    {{ old('payment_method', $invoice->payment_method) === 'Check' ? 'selected' : '' }}>
                                                    Check
                                                </option>
                                                <option value="Credit Card"
                                                    {{ old('payment_method', $invoice->payment_method) === 'Credit Card' ? 'selected' : '' }}>
                                                    Credit Card
                                                </option>
                                                <option value="Bank Transfer"
                                                    {{ old('payment_method', $invoice->payment_method) === 'Bank Transfer' ? 'selected' : '' }}>
                                                    Bank Transfer
                                                </option>
                                                <option value="Website"
                                                    {{ old('payment_method', $invoice->payment_method) === 'Website' ? 'selected' : '' }}>
                                                    Website
                                                </option>
                                                <option value="eBay"
                                                    {{ old('payment_method', $invoice->payment_method) === 'eBay' ? 'selected' : '' }}>
                                                    eBay
                                                </option>
                                                <option value="Other"
                                                    {{ old('payment_method', $invoice->payment_method) === 'Other' ? 'selected' : '' }}>
                                                    Other (Please Note)
                                                </option>
                                            </select>
                                            <div class="">
                                                <span class="label-text-alt text-base-content/70">If using "Other", please add details in the notes section</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>



                    <!-- Add Items -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="px-6 py-4 border-b border-base-300">
                            <h4 class="text-lg font-semibold text-base-content">Add Items</h4>
                        </div>
                        <div class="p-6">

                            <form id="lineItemForm" action="{{ route('line-items.store') }}" method="POST"
                                class="space-y-6">
                                @csrf
                                <input type="hidden" name="invoice_id" value="{{ $invoice->id }}">
                                <input type="hidden" name="item_id" id="item_id" value="{{ old('item_id') }}">

                                <!-- Item Selection -->
                                <div class="space-y-4">
                                    <h5 class="text-md font-medium text-base-content">Select Item</h5>

                                    <div>
                                        <label class="label">
                                            <span class="label-text font-medium">Quick Add Popular Items</span>
                                        </label>
                                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                                            @foreach ($popular_items as $item)
                                                <button type="button" class="btn btn-sm btn-secondary quick-add-item"
                                                    data-item-id="{{ $item->id }}">
                                                    {{ $item->name }} ({{ $item->asset_tag }})
                                                </button>
                                            @endforeach
                                        </div>
                                    </div>

                                    <div id="item_search_field">
                                        <label class="label" for="item_search">
                                            <span class="label-text font-medium">Search Inventory</span>
                                        </label>
                                        <div class="relative">
                                            <input type="text" id="item_search"
                                                placeholder="Start typing item name or asset tag..."
                                                class="input input-bordered w-full" />
                                            <ul id="item_search_results"
                                                class="absolute z-10 bg-base-100 border border-base-300 mt-1 w-full hidden shadow-lg rounded-md">
                                                <!-- Search results will be dynamically added here -->
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="form-control">
                                        <label class="label cursor-pointer justify-start">
                                            <input type="checkbox" class="toggle toggle-primary mr-3"
                                                id="is_custom_item" name="is_custom_item">
                                            <span class="label-text font-medium">Create Custom Item</span>
                                        </label>
                                        <div class="label">
                                            <span class="label-text-alt text-base-content/70">Use this option when the item is not in inventory</span>
                                        </div>
                                    </div>

                                    <div class="hidden" id="item_name_field">
                                        <label class="label" for="item_name">
                                            <span class="label-text font-medium">Custom Item Name</span>
                                        </label>
                                        <input type="text" name="item_name" id="item_name"
                                            class="input input-bordered"
                                            placeholder="Enter a name for this custom item" />
                                    </div>

                                    <div class="hidden" id="category_id_field">
                                        <label class="label" for="category_id">
                                            <span class="label-text font-medium">Item Category</span>
                                        </label>
                                        <select name="category_id" id="category_id"
                                            class="select select-bordered w-full">
                                            @foreach ($inventoryCategories as $category)
                                                <option value="{{ $category->id }}">
                                                    {{ $category->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <div class="divider my-6"></div>

                                <!-- Item Details -->
                                <div class="space-y-4">
                                    <h5 class="text-md font-medium text-base-content">Item Details</h5>

                                    <div>
                                        <label class="label" for="description">
                                            <span class="label-text font-medium">Description</span>
                                        </label>
                                        <input type="text" name="description" id="description"
                                            class="input input-bordered w-full"
                                            placeholder="Add details about this item" />
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="label" for="quantity">
                                                <span class="label-text font-medium">Quantity / Weight</span>
                                            </label>
                                            <input type="number" name="quantity" id="quantity"
                                                class="input input-bordered w-full" required />
                                            <div>
                                                <span id="quantityTotalAvailable" class="label-text-alt text-base-content/70">
                                                    Total Available: 0
                                                </span>
                                            </div>
                                        </div>

                                        <div>
                                            <label class="label" for="price" id="price-label">
                                                <span class="label-text font-medium">Price per unit</span>
                                            </label>
                                            <input type="number" step="0.01" name="price" id="price"
                                                class="input input-bordered w-full" required />
                                            <div class="flex items-center mt-2">
                                                <label for="is_payout" class="flex items-center cursor-pointer">
                                                    <input type="checkbox" name="is_payout" id="is_payout"
                                                        class="checkbox checkbox-primary checkbox-sm mr-2" />
                                                    <span class="text-sm font-medium">This is a payout (buying from
                                                        customer)</span>
                                                </label>
                                                <div class="tooltip ml-1"
                                                    data-tip="Check this when buying items from customers">
                                                    <i
                                                        class="fa-sharp fa-circle-info text-neutral-content text-sm"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <span id="priceDetails" class="label-text-alt text-base-content/70">
                                                    Suggested price: $0.00
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="divider my-6"></div>

                                <!-- Pricing & Department -->
                                <div class="space-y-4">
                                    <h5 class="text-md font-medium text-base-content">Pricing & Department</h5>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="label" for="tax_policy_id">
                                                <span class="label-text font-medium">Tax Policy</span>
                                            </label>
                                            <select name="tax_policy_id" id="tax_policy_id"
                                                class="select select-bordered w-full">
                                                <option value="">Select a tax policy</option>
                                                @foreach ($taxPolicies as $policy)
                                                    <option value="{{ $policy->id }}"
                                                        data-rate="{{ $policy->rate }}">
                                                        {{ $policy->name }} ({{ $policy->rate }}%)
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <div>
                                            <label class="label" for="department_id">
                                                <span class="label-text font-medium">Department <span class="text-error font-bold">*</span></span>
                                            </label>
                                            <select name="department_id" id="department_id"
                                                class="select select-bordered w-full" required>
                                                <option value="">Select a department</option>
                                                @foreach ($departments as $department)
                                                    <option value="{{ $department->id }}">
                                                        {{ $department->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                        <div>
                                            <label class="label" for="tax">
                                                <span class="label-text font-medium">Tax Amount</span>
                                            </label>
                                            <input type="number" step="0.01" name="tax" id="tax"
                                                class="input input-bordered bg-base-200" readonly />
                                            <div>
                                                <span class="label-text-alt text-base-content/70">
                                                    Calculated automatically based on tax policy
                                                </span>
                                            </div>
                                        </div>

                                        <div>
                                            <label class="label" for="subtotal">
                                                <span class="label-text font-medium">Subtotal</span>
                                            </label>
                                            <input type="number" step="0.01" name="subtotal" id="subtotal"
                                                class="input input-bordered bg-base-200" readonly />
                                            <div>
                                                <span class="label-text-alt text-base-content/70">
                                                    Price × Quantity + Tax
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="divider my-6"></div>
                                <div class="flex justify-end">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        Add Item to Invoice
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <!-- Apply Discounts -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="px-6 py-4 border-b border-base-300">
                            <h4 class="text-lg font-semibold text-base-content">Apply Discounts</h4>
                        </div>
                        <div class="p-6">
                            <div class="p-6 space-y-4">
                                <h5 class="text-md font-medium text-base-content">Available Customer Discounts</h5>
                                <div id="available-discounts"
                                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                    <!-- Discounts will be dynamically listed here -->
                                </div>
                                <div id="unusable-discounts"
                                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mt-4">
                                    <!-- Unusable discounts will be dynamically added here -->
                                </div>

                                <p id="no-discounts" class="text-sm text-base-content/70 hidden italic">No discounts available for this customer.</p>
                            </div>
                        </div>
                    </div>
                    <!-- Notes & Attachments -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="px-6 py-4 border-b border-base-300">
                            <h4 class="text-lg font-semibold text-base-content">Notes & Attachments</h4>
                        </div>
                        <div class="p-6 space-y-8">
                            <!-- Invoice Notes -->
                            <div>
                                <h5 class="text-md font-medium text-base-content mb-3">Invoice Notes</h5>
                                <div>
                                    <div id="editor"></div>
                                    <textarea name="notes" id="notes" class="hidden input w-full auto-save-invoice"
                                        placeholder="Add any notes or details about this invoice...">{{ old('notes', $invoice->notes) }}</textarea>
                                    <div class="mt-2">
                                        <span class="text-xs text-base-content/70">Notes are saved automatically as you type</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Attachments -->
                            <div>
                                <h5 class="text-md font-medium text-base-content mb-3">File Attachments</h5>
                                <div class="p-4 bg-base-50 rounded-lg border border-base-200 mb-4">
                                    <h6 class="font-medium text-base-content mb-3">Upload New File</h6>

                                    <form action="{{ route('invoices.files.upload', $invoice) }}" method="POST"
                                        enctype="multipart/form-data" id="attachment-form" class="space-y-4">
                                        @csrf
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <label class="label" for="file">
                                                    <span class="label-text font-medium">Select File</span>
                                                </label>
                                            <input type="file" name="file" id="file"
                                                class="file-input file-input-bordered w-full"
                                                accept=".pdf,.jpg,.jpeg,.png,.gif" required />
                                                <div class="label">
                                                    <span class="label-text-alt text-base-content/70">
                                                        Max: 10MB. Accepted: PDF, JPG, PNG, GIF
                                                    </span>
                                                </div>
                                            </div>

                                            <div>
                                                <label class="label" for="file_type">
                                                    <span class="label-text font-medium">File Type</span>
                                                </label>
                                            <select name="file_type" id="file_type"
                                                class="select select-bordered w-full">
                                                <option value="photo_id">Photo ID</option>
                                                <option value="item_photo">Item Photo</option>
                                                <option value="receipt">Receipt</option>
                                                <option value="other">Other</option>
                                            </select>
                                                <div class="label">
                                                    <span class="label-text-alt text-base-content/70">
                                                        Photo ID is required for payouts
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <div>
                                            <label class="label" for="description">
                                                <span class="label-text font-medium">Description</span>
                                            </label>
                                        <input type="text" name="description" id="file_description"
                                            class="input input-bordered w-full"
                                            placeholder="Optional description of this file" />
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <div id="upload-progress" class="hidden w-1/2">
                                            <div class="w-full bg-base-200 rounded-full h-2.5">
                                                <div id="upload-progress-bar" class="bg-primary h-2.5 rounded-full"
                                                    style="width: 0%"></div>
                                            </div>
                                            <p id="upload-status" class="text-xs text-neutral-content mt-1">
                                                Uploading...</p>
                                        </div>
                                            <div class="text-right">
                                                <button type="submit" class="btn btn-primary btn-sm" id="upload-file-btn">
                                                    Upload File
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <div class="mt-4">
                                    <h6 class="font-medium text-base-content mb-3">Current Files</h6>

                                <div id="file-list" class="space-y-3">
                                    @if ($invoice->files->isEmpty())
                                        <p class="text-neutral-content italic">No files have been attached to this
                                            invoice.</p>
                                    @else
                                        @foreach ($invoice->files as $file)
                                            <div class="file-item p-3 border rounded-lg bg-base-100 flex items-center justify-between"
                                                id="file-{{ $file->id }}">
                                                <div class="flex items-center">
                                                    @if (in_array($file->extension, ['jpg', 'jpeg', 'png', 'gif']))
                                                        <div
                                                            class="w-16 h-16 mr-3 flex-shrink-0 overflow-hidden rounded-md border border-base-300">
                                                            <img src="{{ route('files.view', $file) }}"
                                                                alt="{{ $file->description }}"
                                                                class="h-full w-full object-cover object-center">
                                                        </div>
                                                    @else
                                                        <div
                                                            class="w-16 h-16 mr-3 flex-shrink-0 flex items-center justify-center rounded-md border border-base-300 bg-base-200">
                                                            <i
                                                                class="fa-sharp {{ $file->icon }} text-2xl text-neutral-content"></i>
                                                        </div>
                                                    @endif

                                                    <div class="flex-1 min-w-0">
                                                        <p class="text-sm font-medium text-base-content truncate">
                                                            {{ $file->original_filename }}</p>
                                                        <p class="text-xs text-neutral-content">
                                                            {{ $file->human_readable_size }} •
                                                            {{ $file->metadata['file_type'] ?? 'Other' }}</p>
                                                        <p class="text-xs text-neutral-content">
                                                            {{ $file->description }}</p>
                                                    </div>
                                                </div>

                                                <div class="flex space-x-2">
                                                    <div class="tooltip" data-tip="Edit Description">
                                                        <button type="button"
                                                            class="btn btn-xs btn-outline btn-info edit-file-notes"
                                                            data-file-id="{{ $file->id }}"
                                                            data-description="{{ $file->description }}">
                                                            <i class="fa-sharp fa-note"></i>
                                                        </button>
                                                    </div>
                                                    <div class="tooltip" data-tip="Download File">
                                                        <a href="{{ route('files.download', $file) }}"
                                                            class="btn btn-xs btn-outline btn-primary">
                                                            <i class="fa-sharp fa-download"></i>
                                                        </a>
                                                    </div>
                                                    <div class="tooltip" data-tip="Delete File">
                                                        <form action="{{ route('files.destroy', $file) }}"
                                                            method="POST" class="inline delete-file-form">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit"
                                                                class="btn btn-xs btn-outline btn-error">
                                                                <i class="fa-sharp fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @importQuill
                </div>
                
                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Customer Cart -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="px-6 py-4 border-b border-base-300">
                            <h4 class="text-lg font-semibold text-base-content">Customer Cart</h4>
                        </div>
                        <div class="">
                            <div class="space-y-3">
                                @if ($invoice->lineItems->isEmpty())
                                    <div class="text-center text-base-content/70 py-8">
                                        <div class="flex flex-col items-center gap-4">
                                            <div class="avatar avatar-placeholder">
                                                <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                                    <i class="fa-sharp fa-shopping-cart text-2xl"></i>
                                                </div>
                                            </div>
                                            <div class="text-center">
                                                <h3 class="text-lg font-medium text-base-content/80">No items added yet</h3>
                                                <p class="text-sm text-base-content/60 mt-1">Use the form on the left to add items to this invoice</p>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                @foreach ($invoice->lineItems as $lineItem)
                                    <div class="p-2 border-b border-neutral/50 bg-gradient-to-b from-base-100 to-base-200">
                                        <!-- Item Title -->
                                        <div class="mb-2 flex justify-between items-center">
                                            <h4 class="text-lg font-bold">
                                                {{ $lineItem->inventory->name ?? 'N/A' }}
                                            </h4>
                                            <span class="text-base font-bold text-primary">
                                                ${{ number_format($lineItem->subtotal, 2) }}
                                            </span>
                                        </div>

                                        <!-- Notes (if any) -->
                                        @if ($lineItem->description)
                                            <p class="text-sm text-base-content mb-2">
                                                <i class="fa-sharp fa-note text-base-content mr-1"></i>
                                                {{ $lineItem->description }}
                                            </p>
                                        @endif

                                        <!-- Amount Details -->
                                        <div class="grid grid-cols-2 gap-2 text-sm text-base-content mt-2">
                                            <div>
                                                <p>
                                                    <span class="text-base-content">Quantity:</span>
                                                    <strong>{{ $lineItem->quantity }}</strong>
                                                    @if (
                                                        $lineItem->inventory &&
                                                            $lineItem->inventory->category &&
                                                            $lineItem->inventory->category->quantity_type != 'individual')
                                                        {{ $lineItem->inventory->unit }}(s)
                                                    @endif
                                                </p>
                                                <p>
                                                    <span class="text-base-content">Price:</span>
                                                    ${{ number_format($lineItem->price, 2) }}/unit
                                                </p>
                                            </div>
                                            <div>
                                                <p>
                                                    <span class="text-base-content">Tax:</span>
                                                    ${{ number_format($lineItem->tax, 2) }}
                                                    <span
                                                        class="text-xs text-base-content">({{ $lineItem->taxPolicy->name }})</span>
                                                </p>
                                                <p>
                                                    <span class="text-base-content">Discount:</span>
                                                    -${{ number_format($lineItem->discount, 2) }}
                                                </p>
                                                <p class="text-xs">
                                                    <span class="text-base-content">Department:</span>
                                                    {{ $lineItem->department->name }}
                                                </p>
                                            </div>
                                        </div>

                                        <!-- Actions Row -->
                                        <div class="flex items-center justify-end mt-3 space-x-2">
                                            <!-- Edit Notes Button -->
                                            <div class="tooltip" data-tip="Edit Notes">
                                                <button type="button"
                                                    class="btn btn-sm btn-outline btn-info lineitem-note-edit-btn"
                                                    data-lineitem-id="{{ $lineItem->id }}">
                                                    <i class="fa-sharp fa-note"></i>
                                                </button>
                                            </div>

                                            <!-- Delete Button -->
                                            <div class="tooltip" data-tip="Remove Item">
                                                <form action="{{ route('line-items.destroy', $lineItem) }}"
                                                    method="POST" class="inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline btn-error"
                                                        onclick="return confirm('Are you sure you want to remove this item?')">
                                                        <i class="fa-sharp fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>

                                        <!-- Edit Notes Form -->
                                        <div class="hidden lineitem-details-edit mt-3 bg-base-200 p-3 rounded-lg"
                                            data-lineitem-id="{{ $lineItem->id }}">
                                            <form action="{{ route('line-items.update', $lineItem) }}"
                                                method="POST">
                                                @csrf
                                                @method('PUT')
                                                <div class="flex items-center space-x-2">
                                                    <input type="text" name="description"
                                                        id="description_{{ $lineItem->id }}"
                                                        value="{{ $lineItem->description }}"
                                                        placeholder="Add notes or details..."
                                                        class="input input-bordered w-full">
                                                    <button type="submit" class="btn btn-primary btn-sm">
                                                        <i class="fa-sharp fa-save"></i> Save
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Summary -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="px-6 py-4 border-b border-base-300">
                            <h4 class="text-lg font-semibold text-base-content">Invoice Summary</h4>
                        </div>
                        <div class="p-2">
                            <div class="">
                                <table class="table w-full">
                                    <tbody>
                                        <tr>
                                            <td class="font-medium text-base-content">Total Price</td>
                                            <td class="text-right">${{ number_format($invoice->total_price, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td class="font-medium text-base-content">Total Tax</td>
                                            <td class="text-right">${{ number_format($invoice->total_tax, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td class="font-medium text-base-content">Discounts Applied</td>
                                            <td class="text-right">
                                                <ul id="invoice-discounts-list" class="space-y-1">
                                                    @if ($invoice->discounts->isEmpty())
                                                        <li class="text-neutral-content italic">None</li>
                                                    @endif
                                                    @foreach ($invoice->discounts as $discount)
                                                        <li id="discount-{{ $discount->id }}"
                                                            class="flex items-center justify-between">
                                                            <span>
                                                                {{ $discount->name }}:
                                                                <span
                                                                    class="font-medium">{{ $discount->type === 'percent' ? $discount->amount . '%' : '$' . $discount->amount }}</span>
                                                            </span>
                                                            <div class="tooltip" data-tip="Remove Discount">
                                                                <button type="button"
                                                                    class="btn btn-xs btn-outline btn-error ml-2"
                                                                    onclick="removeInvoiceDiscount({{ $discount->id }}, {{ $invoice->id }})">
                                                                    <i class="fa-sharp fa-times"></i>
                                                                </button>
                                                            </div>
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="font-medium text-base-content">Total Discount</td>
                                            <td id="total_discount" class="text-right text-success">
                                                -${{ number_format($invoice->total_discount, 2) }}</td>
                                        </tr>
                                        <tr class="border-t-2 border-base-300">
                                            <td class="font-medium text-lg">Final Price</td>
                                            <td id="final_price"
                                                class="font-bold text-lg text-right {{ $invoice->final_price < 0 ? 'text-error' : 'text-primary' }}">
                                                ${{ number_format($invoice->final_price, 2) }}
                                            </td>
                                        </tr>
                                        @if ($invoice->final_price < 0)
                                            <tr id="negative-price-warning">
                                                <td colspan="2"
                                                    class="bg-error bg-opacity-10 p-3 text-center">
                                                    <div class="flex items-center justify-center text-error-content">
                                                        <i class="fa-sharp fa-circle-exclamation mr-2"></i>
                                                        <span class="font-medium">This is a payout - you owe the
                                                            customer money</span>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    @push('scripts')
        <script>
            // Route helper function for JavaScript
            function route(name, params) {
                const routes = {
                    'files.view': '/files/{file}/view',
                    'files.download': '/files/{file}/download',
                    'files.destroy': '/files/{file}'
                };

                let url = routes[name] || '';
                if (typeof params === 'object') {
                    for (const key in params) {
                        url = url.replace(`{${key}}`, params[key]);
                    }
                } else {
                    url = url.replace('{file}', params);
                }

                return url;
            }
            document.addEventListener('DOMContentLoaded', function() {
                // No collapse component to handle anymore

                // Handle file description editing
                const editFileNotesBtns = document.querySelectorAll('.edit-file-notes');
                editFileNotesBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        const fileId = this.dataset.fileId;
                        const currentDescription = this.dataset.description || '';
                        const newDescription = prompt('Edit file description:', currentDescription);

                        if (newDescription !== null) {
                            updateFileNotes(fileId, newDescription);
                        }
                    });
                });

                // Handle file deletion confirmation
                const deleteFileForms = document.querySelectorAll('.delete-file-form');
                deleteFileForms.forEach(form => {
                    form.addEventListener('submit', function(e) {
                        if (!confirm(
                                'Are you sure you want to delete this file? This action cannot be undone.'
                                )) {
                            e.preventDefault();
                        }
                    });
                });

                // Function to update file notes
                async function updateFileNotes(fileId, description) {
                    try {
                        const response = await fetch(`/files/${fileId}/update-notes`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify({
                                description
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            // Update the description in the UI
                            const fileItem = document.getElementById(`file-${fileId}`);
                            if (fileItem) {
                                const descriptionElement = fileItem.querySelector('.flex-1 p:last-child');
                                if (descriptionElement) {
                                    descriptionElement.textContent = description;
                                }

                                // Update the data attribute for future edits
                                const editBtn = fileItem.querySelector('.edit-file-notes');
                                if (editBtn) {
                                    editBtn.dataset.description = description;
                                }
                            }

                            // Show success message
                            showToast('File description updated successfully', 'success');
                        } else {
                            showToast('Failed to update file description', 'error');
                        }
                    } catch (error) {
                        console.error('Error updating file notes:', error);
                        showToast('An error occurred while updating the file description', 'error');
                    }
                }

                // Function to show toast notifications
                function showToast(message, type = 'info') {
                    const toast = document.createElement('div');
                    toast.className = `fixed bottom-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                        type === 'success' ? 'bg-success text-white' :
                        type === 'error' ? 'bg-error text-white' :
                        'bg-info text-white'
                    }`;
                    toast.textContent = message;
                    document.body.appendChild(toast);

                    setTimeout(() => {
                        toast.classList.add('opacity-0', 'transition-opacity', 'duration-500');
                        setTimeout(() => {
                            document.body.removeChild(toast);
                        }, 500);
                    }, 3000);
                }

                // No special JavaScript needed for file uploads - using standard form submission
                // Select all toggle buttons
                const toggleButtons = document.querySelectorAll('.lineitem-note-edit-btn');

                toggleButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        // Get the line item ID from the data attribute
                        const lineItemId = this.dataset.lineitemId;

                        // Find the corresponding details row
                        const detailsRow = document.querySelector(
                            `.lineitem-details-edit[data-lineitem-id="${lineItemId}"]`);

                        if (detailsRow) {
                            // Toggle the visibility of the details row
                            if (detailsRow.classList.contains('hidden')) {
                                detailsRow.classList.remove('hidden');
                            } else {
                                detailsRow.classList.add('hidden');
                            }
                        }
                    });
                });

                // Define the debounce function if not already defined
                function debounce(func, delay = 500) {
                    let timer;
                    return function(...args) {
                        clearTimeout(timer);
                        timer = setTimeout(() => func.apply(this, args), delay);
                    };
                }

                // Initialize Quill editor
                var quill = new Quill('#editor', {
                    theme: 'snow',
                    placeholder: 'Add any notes or details about this invoice...',
                });

                // Load existing notes into Quill
                quill.root.innerHTML = document.getElementById('notes').value;

                // Update hidden textarea with Quill content on text-change
                quill.on('text-change', function() {
                    document.getElementById('notes').value = quill.root.innerHTML;
                });

                // Save on Quill text-change (debounced)
                const debouncedSaveInvoice = debounce(() => {
                    const notesField = document.getElementById('notes');
                    notesField.dispatchEvent(new Event('input')); // Trigger auto-save logic
                }, 500);

                quill.on('text-change', debouncedSaveInvoice);
            });

            //Global

            async function removeInvoiceDiscount(discountId, invoiceId) {
                if (!confirm("Are you sure you want to remove this discount?")) return;

                try {
                    const response = await fetch("{{ route('invoice-discounts.removeById') }}", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                            "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content,
                        },
                        body: JSON.stringify({
                            discount_id: discountId,
                            invoice_id: invoiceId
                        }),
                    });
                    const data = await response.json();

                    if (data.success) {
                        // reload
                        location.reload();
                    } else {
                        alert("Failed to remove discount. " + (data.message || ""));
                    }
                } catch (error) {
                    console.error("Error removing discount:", error);
                    alert("An error occurred while trying to remove the discount.");
                }
            }

            document.addEventListener("DOMContentLoaded", function() {
                // Get customer data for discount loading
                const customers = @json($customers);
                const customerIdInput = document.querySelector('input[name="customer_id"]'); // Updated to use the dynamic component
                const itemSearchInput = document.getElementById("item_search");
                const itemSearchResults = document.getElementById("item_search_results");
                const itemIdInput = document.getElementById("item_id");
                const priceInput = document.getElementById("price");
                const descriptionInput = document.getElementById("description");
                const quantityInput = document.getElementById("quantity");
                const quantityAvailable = document.getElementById("quantityTotalAvailable");
                const taxPolicySelect = document.getElementById("tax_policy_id");
                const departmentSelect = document.getElementById("department_id");

                let lineitemQuantityType = "";

                // Listen for customer selection from the dynamic component
                document.addEventListener('customerSelected', function(event) {
                    loadDiscountOptions(); // Reload discounts when the customer changes
                    
                    // Trigger auto-save when customer changes
                    if (typeof debouncedSaveInvoice === 'function') {
                        debouncedSaveInvoice();
                    }
                });

                document.addEventListener('customerDeselected', function(event) {
                    // Clear discounts when customer is deselected
                    availableDiscountsContainer.innerHTML = "";
                    noDiscountsMessage.classList.remove("hidden");
                    
                    // Trigger auto-save when customer is deselected
                    if (typeof debouncedSaveInvoice === 'function') {
                        debouncedSaveInvoice();
                    }
                });

                // Discounts Management
                const availableDiscountsContainer = document.getElementById("available-discounts");
                const noDiscountsMessage = document.getElementById("no-discounts");
                const invoiceId = "{{ $invoice->id }}";
                const usedDiscounts = @json($invoice->discounts->pluck('id'));


                function loadDiscountOptions() {
                    availableDiscountsContainer.innerHTML = "";
                    noDiscountsMessage.classList.add("hidden");

                    const customerId = customerIdInput.value;
                    if (customerId) {
                        fetch(`/customers/${customerId}/eligible-discounts`)
                            .then(response => response.json())
                            .then(data => {
                                const {
                                    usable,
                                    unusable
                                } = data;

                                // Handle usable discounts
                                if (usable.length > 0) {
                                    usable.forEach(discount => {
                                        if (usedDiscounts.includes(discount.id)) return;

                                        const discountButton = document.createElement("button");
                                        discountButton.type = "button";
                                        discountButton.classList.add("btn", "btn-lg", "btn-success",
                                            "w-full");
                                        discountButton.innerHTML = `
                        <div>
                            <strong>${discount.name}</strong><br>
                            <span>${discount.type === "percent" ? `${discount.amount}% off` : `$${discount.amount} off`}</span>
                            ${discount.category_name ? `<span class="text-sm text-gray-500">(Category: ${discount.category_name})</span>` : ""}
                            <span class="text-xs text-gray-300">Uses: ${discount.maximum_uses ? `${discount.usage_count || 0} / ${discount.maximum_uses}` : `${discount.usage_count || 0} / Unlimited`}</span>
                        </div>`;
                                        discountButton.addEventListener("click", () =>
                                            applyDiscountToInvoice(discount.id));
                                        availableDiscountsContainer.appendChild(discountButton);
                                    });
                                }

                                // Handle no usable discounts
                                if (usable.length === 0 && unusable.length === 0) {
                                    noDiscountsMessage.classList.remove("hidden");
                                }

                                // Optionally handle unusable discounts (if you want to show them differently)
                                // You can add another container for unusable discounts if needed
                                if (unusable.length > 0) {
                                    const unusableDiscountsContainer = document.getElementById(
                                        "unusable-discounts");
                                    unusable.forEach(discount => {
                                        const discountCard = document.createElement("div");
                                        discountCard.classList.add("card", "bg-base-200", "shadow-md",
                                            "p-4");

                                        discountCard.innerHTML = `
        <div>
            <strong>${discount.name}</strong><br>
            <span>${discount.type === "percent" ? `${discount.amount}% off` : `$${discount.amount} off`}</span>
            ${discount.category_name ? `<span class="text-sm text-neutral-content">(Category: ${discount.category_name})</span>` : ""}
            <span class="text-xs text-neutral-content text-error">Already Used</span>
        </div>`;
                                        unusableDiscountsContainer.appendChild(discountCard);
                                    });
                                }

                            })
                            .catch(error => console.error("Error fetching discounts:", error));
                    } else {
                        noDiscountsMessage.classList.remove("hidden");
                    }


                }




                async function applyDiscountToInvoice(discountId) {
                    try {
                        const response = await fetch(`/invoices/apply-discount`, {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content,
                            },
                            body: JSON.stringify({
                                discount_id: discountId,
                                invoice_id: invoiceId
                            }),
                        });
                        const data = await response.json();
                        if (data.success) location.reload();
                        else alert("Error applying discount: " + data.message);
                    } catch (error) {
                        console.error("Error applying discount:", error);
                    }
                }

                function clearLineItemInput() {
                    document.getElementById("item_id").value = "";
                    document.getElementById("item_name").value = "";
                    document.getElementById("price").value = "";
                    document.getElementById("description").value = "";
                    document.getElementById("quantity").value = "";
                    document.getElementById("tax_policy_id").value = "";
                    document.getElementById("tax").value = "";
                    document.getElementById("subtotal").value = "";
                    document.getElementById("department_id").value = "";
                    document.getElementById("category_id").value = "";
                }

                function populateItemDetails(item) {

                    console.log('populating item details', item);

                    /* Populate item details in the form */

                    itemSearchInput.value = `${item.name} (${item.asset_tag})`;
                    itemSearchResults.classList.add("hidden");

                    // Populate form fields
                    itemIdInput.value = item.id;
                    priceInput.value = item.suggested_price || 0;
                    descriptionInput.value = item.description || "";


                    // Determine quantity_type
                    lineitemQuantityType = item.category?.quantity_type;
                    updateQuantityTypeAttribute(item.category?.quantity_type);

                    if (lineitemQuantityType === "individual") {
                        console.log("Individual");
                        quantityInput.value = 1;
                        quantityInput.classList.add("bg-base-200");
                        quantity = 1;
                        quantityInput.readOnly = true; // Disable editing

                        quantityAvailable.textContent = `Total Available: ${item.quantity || 0}`;
                    } else if (lineitemQuantityType === "unlimited") {
                        quantityInput.value = 0;
                        quantity = 0;
                        quantityInput.readOnly = false; // enable editing
                        quantityInput.classList.remove("bg-base-200");
                        quantityAvailable.textContent = "Total Available: Unlimited";
                    } else if (lineitemQuantityType === "unit") {
                        console.log("Unit");
                        quantityInput.value = 1; // Default to 1, but can be changed
                        quantityInput.readOnly = false; // Enable editing
                        quantityInput.classList.remove("bg-base-200");
                        quantity = 1;

                        // For unit type, we show unlimited availability
                        quantityAvailable.textContent = "Total Available: Unlimited";
                    } else {
                        console.log("Else");
                        quantityInput.readOnly = false; // Enable editing
                        quantityInput.classList.remove("bg-base-200");
                        quantityInput.value = ""; // Reset quantity

                        quantityAvailable.textContent = `Total Available: ${item.quantity || 0}`;
                    }
                    // Set default tax policy if available
                    const taxPolicyId = item.category?.tax_policy_id;
                    if (taxPolicyId) {
                        taxPolicySelect.value = taxPolicyId;
                        taxPolicySelect.dispatchEvent(new Event("change")); // Trigger tax calculation
                    }

                    // Set default department if available
                    const departmentId = item.category?.department_id;
                    if (departmentId) {
                        departmentSelect.value = departmentId;
                    }

                    // Calculate taxes and subtotal
                    calculateTaxes();
                }

                // Custom Line Item Toggle
                const isCustomItemCheckbox = document.getElementById("is_custom_item");
                const itemNameField = document.getElementById("item_name_field");
                const itemSearchField = document.getElementById("item_search_field");
                const categoryIdField = document.getElementById("category_id_field");

                isCustomItemCheckbox.addEventListener("change", function() {
                    if (this.checked) {
                        itemNameField.classList.remove("hidden");
                        itemNameField.querySelector("input").required = true;
                        itemSearchField.classList.add("hidden");
                        categoryIdField.classList.remove("hidden");
                        document.getElementById("category_id").required = true;
                        clearLineItemInput();
                        quantityInput.readOnly = false; // Enable editing
                        quantityInput.classList.remove("bg-base-200");
                        quantityInput.value = "1"; // Reset quantity
                        quantityAvailable.textContent = "Total Available: Unlimited";
                        // default tax policy 1
                        taxPolicySelect.value = "1";

                        // Set the quantity type based on the selected category
                        const categorySelect = document.getElementById("category_id");
                        const selectedOption = categorySelect.options[categorySelect.selectedIndex];
                        const categoryId = selectedOption.value;

                        // Fetch the category details to get the quantity_type
                        fetch(`/inventory/categories/${categoryId}/json`)
                            .then(response => response.json())
                            .then(category => {
                                lineitemQuantityType = category.quantity_type;
                                updateQuantityTypeAttribute(category.quantity_type);
                                console.log('Custom item category type:', lineitemQuantityType);

                                // Update the quantity field based on the category type
                                if (lineitemQuantityType === "unit") {
                                    quantityInput.readOnly = false;
                                    quantityInput.classList.remove("bg-base-200");
                                    quantityAvailable.textContent = "Total Available: Unlimited";
                                } else if (lineitemQuantityType === "individual") {
                                    quantityInput.value = 1;
                                    quantityInput.readOnly = true;
                                    quantityInput.classList.add("bg-base-200");
                                }
                            })
                            .catch(error => {
                                console.error('Error fetching category details:', error);
                            });
                    } else {
                        itemNameField.classList.add("hidden");
                        categoryIdField.classList.add("hidden");
                        itemSearchField.classList.remove("hidden");
                        document.getElementById("category_id").required = false;
                        itemNameField.querySelector("input").required = false;
                        clearLineItemInput();
                    }
                });

                // Add event listener for category changes
                document.getElementById("category_id").addEventListener("change", function() {
                    const categoryId = this.value;

                    // Fetch the category details to get the quantity_type
                    fetch(`/inventory/categories/${categoryId}/json`)
                        .then(response => response.json())
                        .then(category => {
                            lineitemQuantityType = category.quantity_type;
                            updateQuantityTypeAttribute(category.quantity_type);
                            console.log('Category changed, new type:', lineitemQuantityType);

                            // Update the quantity field based on the category type
                            if (lineitemQuantityType === "unit") {
                                quantityInput.readOnly = false;
                                quantityInput.classList.remove("bg-base-200");
                                quantityAvailable.textContent = "Total Available: Unlimited";
                            } else if (lineitemQuantityType === "individual") {
                                quantityInput.value = 1;
                                quantityInput.readOnly = true;
                                quantityInput.classList.add("bg-base-200");
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching category details:', error);
                        });
                });





                // Item Search and Dynamic Population

                let quantity = 0;

                itemSearchInput.addEventListener("input", function() {
                    const query = itemSearchInput.value.trim();


                    if (!query) {
                        itemSearchResults.classList.add("hidden");
                        itemSearchResults.innerHTML = "";
                        return;
                    }

                    fetch(`/inventory/search?query=${encodeURIComponent(query)}&active=true`, {
                            method: 'GET',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                    .content, // Include CSRF Token
                                'Accept': 'application/json', // Ensure JSON response
                            },
                        })
                        .then(response => response.json())
                        .then(items => {
                            itemSearchResults.innerHTML = "";
                            if (items.length > 0) {
                                items.forEach(item => {
                                    const listItem = document.createElement("li");
                                    listItem.className = "p-2 cursor-pointer hover:bg-base-200";
                                    listItem.textContent =
                                        `${item.name} (${item.asset_tag}) - ${item.location} (MSRP $${item.suggested_price || 0})`;
                                    listItem.addEventListener("click", function() {
                                        populateItemDetails(item);
                                    });
                                    itemSearchResults.appendChild(listItem);
                                });
                                itemSearchResults.classList.remove("hidden");
                            } else {
                                itemSearchResults.innerHTML =
                                    "<li class='p-2 text-neutral-content'>No items found.</li>";
                                itemSearchResults.classList.remove("hidden");
                            }
                        })
                        .catch(error => {
                            console.error("Error fetching search results:", error);
                            itemSearchResults.innerHTML =
                                "<li class='p-2 text-error'>Error fetching results.</li>";
                            itemSearchResults.classList.remove("hidden");
                        });

                });



                // Close search results when clicking outside
                document.addEventListener("click", function(e) {
                    if (!itemSearchResults.contains(e.target) && e.target !== itemSearchInput) {
                        itemSearchResults.classList.add("hidden");
                    }
                });



                // Calculate taxes and subtotal
                function calculateTaxes() {
                    const price = parseFloat(priceInput.value) || 0;
                    const quantity = parseInt(quantityInput.value) || 0;
                    const isPayout = document.getElementById('is_payout').checked;

                    // For display purposes, we always use positive numbers in the calculation
                    // The actual negative value will be handled by the server
                    const displayPrice = Math.abs(price);
                    const subtotal = displayPrice * quantity;
                    const taxRate = parseFloat(taxPolicySelect.selectedOptions[0]?.dataset?.rate || 0);
                    const tax = subtotal * (taxRate / 100);

                    document.getElementById("tax").value = tax.toFixed(2);
                    document.getElementById("subtotal").value = (subtotal + tax).toFixed(2);

                    // Update UI based on payout status
                    if (isPayout) {
                        // Update price label to indicate payout
                        const priceLabel = document.getElementById('price-label');
                        if (priceLabel) {
                            priceLabel.innerHTML = `<span class="label-text font-medium">Payout amount per unit</span>`;
                        }

                        // Add a warning class to the price field
                        priceInput.classList.add('border-warning');

                        // Update the price details text
                        const priceDetails = document.getElementById('priceDetails');
                        if (priceDetails) {
                            priceDetails.innerHTML =
                                '<span class="text-warning font-medium">This is a payout - money will be paid to the customer</span>';
                        }
                    } else {
                        // Reset price label
                        const priceLabel = document.getElementById('price-label');
                        if (priceLabel) {
                            priceLabel.innerHTML = `<span class="label-text font-medium">Price per unit</span>`;
                        }

                        // Remove warning class from price field
                        priceInput.classList.remove('border-warning');

                        // Reset the price details text
                        const priceDetails = document.getElementById('priceDetails');
                        if (priceDetails) {
                            priceDetails.textContent = 'Suggested price: $0.00';
                        }
                    }
                }

                priceInput.addEventListener("input", calculateTaxes);
                taxPolicySelect.addEventListener("change", calculateTaxes);
                document.getElementById('is_payout').addEventListener("change", calculateTaxes);

                // Store the current item's quantity type in a data attribute on the quantity input
                // This ensures we always have access to it even if the variable gets reset
                function updateQuantityTypeAttribute(type) {
                    quantityInput.dataset.quantityType = type;
                    console.log('Updated quantity type attribute to:', type);
                }

                // Helper function to get the current quantity type
                function getCurrentQuantityType() {
                    // First try to get it from the data attribute (most reliable)
                    const dataType = quantityInput.dataset.quantityType;
                    if (dataType) {
                        return dataType;
                    }
                    // Fall back to the variable if needed
                    return lineitemQuantityType;
                }

                // Quantity input validation and tax calculation
                quantityInput.addEventListener("input", function() {
                    const currentType = getCurrentQuantityType();
                    console.log('Quantity input changed:', this.value, 'Type from attribute:', quantityInput
                        .dataset.quantityType, 'Type from var:', lineitemQuantityType);

                    // For individual items, always set quantity to 1
                    if (currentType === "individual") {
                        this.value = 1;
                        quantity = 1;
                        console.log('Set to 1 (individual)');
                    }
                    // For unit type items, allow any positive quantity
                    else if (currentType === "unit") {
                        // Ensure quantity is positive
                        if (parseInt(this.value) < 1) {
                            this.value = 1;
                            console.log('Set to minimum 1 (unit)');
                        } else {
                            console.log('Keeping value for unit type:', this.value);
                        }
                    }
                    // For other items (with limited quantity), enforce the maximum
                    else if (currentType !== "unlimited") {
                        const availableText = quantityAvailable.textContent;
                        console.log('Available text:', availableText);

                        // Check if it says "Unlimited"
                        if (availableText.includes("Unlimited")) {
                            console.log('Unlimited quantity available');
                            // No need to limit the quantity
                        } else {
                            const maxQuantity = parseInt(availableText.replace("Total Available: ", "")) || 0;
                            console.log('Max quantity:', maxQuantity);
                            if (parseInt(this.value) > maxQuantity) {
                                this.value = maxQuantity;
                                console.log('Set to max:', maxQuantity);
                            }
                        }
                    }

                    quantity = parseInt(this.value) || 0;
                    console.log('Final quantity:', quantity);

                    // Update tax and subtotal
                    calculateTaxes();
                });

                // Load discounts on page load
                loadDiscountOptions();

                const form = document.getElementById("invoiceForm");
                const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

                // Function to save the invoice
                async function saveInvoice() {
                    const formData = new FormData(form);

                    // Collect form data as an object
                    const data = Object.fromEntries(formData.entries());

                    try {
                        const response = await fetch(form.action, {
                            method: "PUT",
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRF-TOKEN": csrfToken,
                            },
                            body: JSON.stringify(data),
                        });

                        if (!response.ok) {
                            const errorText = await response.text();
                            try {
                                const errorData = JSON.parse(errorText);
                                showToast(errorData.message || "An error occurred while saving.", "error");
                            } catch {
                                showToast("An unexpected error occurred.", "error");
                                console.error("Unexpected response:", errorText);
                            }
                            return;
                        }

                        const result = await response.json();

                        if (result.success) {
                            showToast("Invoice updated successfully!", "success");
                        } else {
                            showToast(result.message || "Failed to save the invoice.", "error");
                        }
                    } catch (error) {
                        console.error("Error saving invoice:", error);
                        showToast("An error occurred while saving the invoice.", "error");
                    }
                }

                // Debounce function to avoid frequent API calls
                function debounce(func, delay = 500) {
                    let timer;
                    return function(...args) {
                        clearTimeout(timer);
                        timer = setTimeout(() => func.apply(this, args), delay);
                    };
                }

                const debouncedSaveInvoice = debounce(saveInvoice);

                // Attach event listeners to auto-save fields
                document.querySelectorAll(".auto-save-invoice").forEach((field) => {
                    field.addEventListener("input", debouncedSaveInvoice);
                    field.addEventListener("change", debouncedSaveInvoice);
                });

                // Function to show a toast notification
                // Function to show a toast notification
                function showToast(message, type = "info") {
                    // Create the toast container if it doesn't already exist
                    let toastContainer = document.querySelector(".toast-container");
                    if (!toastContainer) {
                        toastContainer = document.createElement("div");
                        toastContainer.className = "toast toast-top toast-end"; // DaisyUI positioning classes
                        document.body.appendChild(toastContainer);
                    }

                    // Create the toast message
                    const toastMessage = document.createElement("div");
                    toastMessage.className = `alert alert-${type} shadow-lg`; // DaisyUI alert styles
                    toastMessage.innerHTML = `
        <span>${message}</span>
    `;

                    // Append the toast message to the container
                    toastContainer.appendChild(toastMessage);

                    // Automatically remove the toast after 3 seconds
                    setTimeout(() => {
                        toastMessage.remove();
                        // Remove the container if it has no children left
                        if (!toastContainer.hasChildNodes()) {
                            toastContainer.remove();
                        }
                    }, 3000);
                }


                // Quick Add Item
                const quickAddButtons = document.querySelectorAll(".quick-add-item");

                quickAddButtons.forEach(button => {
                    button.addEventListener("click", function() {
                        //get data-item-id
                        const itemId = this.dataset.itemId;
                        console.log(itemId);

                        // search item by id

                        fetch(`/inventory/search?id=${itemId}`, {
                                method: 'GET',
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector(
                                            'meta[name="csrf-token"]')
                                        .content, // Include CSRF Token
                                    'Accept': 'application/json', // Ensure JSON response
                                },
                            })
                            .then(response => response.json())
                            .then(items => {
                                if (items.length > 0) {
                                    // Successfully found items, populate details and hide search results
                                    items.forEach(item => {
                                        populateItemDetails(item);
                                    });
                                    // Hide search results when using quick add
                                    itemSearchResults.classList.add("hidden");
                                    itemSearchResults.innerHTML = "";
                                } else {
                                    // No items found
                                    itemSearchResults.innerHTML =
                                        "<li class='p-2 text-neutral-content'>No items found.</li>";
                                    itemSearchResults.classList.remove("hidden");
                                }

                                console.log(items);

                            })
                            .catch(error => {
                                console.error("Error fetching search results:", error);
                                // Show a toast notification instead of search results
                                showToast("Error fetching item details. Please try again.",
                                "error");
                                // Keep search results hidden
                                itemSearchResults.classList.add("hidden");
                            });




                    });
                });

            });
        </script>
    @endpush

</x-app-layout>
