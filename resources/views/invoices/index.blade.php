<x-app-layout
    page-title="Invoices"
    page-icon="fa-sharp fa-file-invoice"
    :primary-buttons="[
        [
            'type' => 'create',
            'route' => route('invoices.create'),
            'text' => 'New Invoice'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'New Payout',
            'route' => route('payouts.create'),
            'icon' => 'fa-sharp fa-money-bill-transfer',
            'class' => 'btn btn-secondary btn-sm gap-2'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Invoices', 'icon' => 'fa-file-invoice']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-lg">
                <!-- Header Section -->
                <div class="card-body">
                    <div class="mb-6">
                        <h3 class="text-2xl font-bold text-base-content">Invoice Management</h3>
                        <p class="text-base-content/70 mt-1">Manage all invoices and payouts in the system.</p>
                    </div>

                    <!-- Search Section -->
                    <div class="mb-6">
                        <form action="{{ route('invoices.search') }}" method="GET" class="flex gap-2">
                            <div class="flex-1">
                                <input type="text" name="query" placeholder="Search by customer name, email, or invoice ID..."
                                       class="input input-bordered w-full" value="{{ request('query') }}">
                            </div>
                            <button type="submit" class="btn btn-outline">
                                <i class="fa-sharp fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>
                <!-- Table Section -->
                <div class="overflow-x-auto">
                    <table class="table table-zebra">
                        <thead>
                            <tr class="border-base-300">
                                @php
                                    $columns = [
                                        'id' => 'ID',
                                        'customer' => 'Customer',
                                        'invoice_date' => 'Invoice Date',
                                        'total_price' => 'Total Price',
                                        'final_price' => 'Final Price',
                                        'status' => 'Status',
                                    ];
                                @endphp
                                @foreach ($columns as $key => $label)
                                    <th class="text-base-content font-semibold">
                                        <a href="{{ route('invoices.index', array_merge(request()->except(['page', 'sort', 'order']), [
                                            'sort' => $key,
                                            'order' => $sort === $key && $order === 'asc' ? 'desc' : 'asc',
                                            'pagination' => $pagination, // Preserve the current pagination value
                                        ])) }}" class="flex items-center hover:text-primary transition-colors">
                                            {{ $label }}
                                            @if ($sort === $key)
                                                <i class="fa-sharp {{ $order === 'asc' ? 'fa-sort-up' : 'fa-sort-down' }} ml-2 text-primary"></i>
                                            @else
                                                <i class="fa-sharp fa-sort ml-2 opacity-50"></i>
                                            @endif
                                        </a>
                                    </th>
                                @endforeach
                                <th class="text-base-content font-semibold">Actions</th>
                            </tr>
                        </thead>

                        <tbody>
                            @foreach ($invoices as $invoice)
                                <tr class="hover:bg-base-200/50 transition-colors">
                                    <td class="font-mono text-sm">
                                        <a href="{{ route('invoices.show', $invoice) }}" class="link-accent font-medium">
                                    INV-{{ $invoice->id }}
                                    </a>
                                </td>
                                    <td>
                                        @if($invoice->customer)
                                            <div class="flex flex-col">
                                                <a href="{{ route('customers.show', $invoice->customer) }}" class="link-accent font-medium">
                                                    {{ $invoice->customer->name }}
                                                </a>
                                                @if($invoice->customer->trashed())
                                                    <span class="badge badge-error badge-xs mt-1">Deleted</span>
                                                @endif
                                            </div>
                                        @else
                                            <span class="text-base-content/50 italic">No Customer</span>
                                        @endif
                                    </td>
                                    <td class="text-base-content">{{ $invoice->invoice_date->format('M d, Y') }}</td>
                                    <td class="font-mono text-base-content">${{ number_format($invoice->total_price, 2) }}</td>
                                    <td class="font-mono">
                                        <span class="{{ $invoice->final_price < 0 ? 'text-error' : 'text-base-content' }}">
                                            ${{ number_format($invoice->final_price, 2) }}
                                        </span>
                                        @if($invoice->final_price < 0)
                                            <span class="badge badge-error badge-xs ml-1">Payout</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-outline">{{ $invoice->status }}</span>
                                    </td>
                                    <td>
                                        <div class="flex gap-1">
                                            <div class="tooltip" data-tip="View Invoice">
                                                <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-xs btn-ghost">
                                                    <i class="fa-sharp fa-eye"></i>
                                                </a>
                                            </div>
                                            <div class="tooltip" data-tip="Edit Invoice">
                                                <a href="{{ route('invoices.edit', $invoice) }}" class="btn btn-xs btn-ghost">
                                                    <i class="fa-sharp fa-pen-to-square"></i>
                                                </a>
                                            </div>
                                            <div class="tooltip" data-tip="Delete Invoice">
                                                <form action="{{ route('invoices.destroy', $invoice) }}" method="POST" class="inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-xs btn-ghost text-error hover:bg-error hover:text-error-content"
                                                            onclick="return confirm('Are you sure you want to delete this invoice? This action cannot be undone.')">
                                                        <i class="fa-sharp fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination Section -->
                <div class="card-body pt-0">
                    <x-pagination :paginator="$invoices" :pagination="$pagination" />
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
