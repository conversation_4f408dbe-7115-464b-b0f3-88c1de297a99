<x-app-layout
    page-title="Create New Invoice"
    page-icon="fa-sharp fa-plus"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('invoices.index'),
            'text' => 'Back to Invoices'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Invoices', 'route' => 'invoices.index', 'icon' => 'fa-file-invoice'],
        ['name' => 'Create New Invoice', 'icon' => 'fa-plus']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-lg p-6">
                <span class="mb-4">Select or create a customer if you know their details. Otherwise, click next to use the General Customer profile.</span>
                <form action="{{ route('invoices.store') }}" method="POST">
                    @csrf

                    <!-- Customer Selection -->
                    <div class="mb-4">
                        <label for="customerSearch" class="block text-sm font-medium text-base-content">Customer</label>
                        <div class="relative">
                            <!-- Dynamic Search Component -->
                            <x-dynamic-customer-search
                                id="customerSearch"
                                name="customer_id"
                                placeholder="Search for customers by name, business name, email, phone, or nickname..."
                                action="form"
                                quickCreateButtonId="quickCreateCustomerBtn"
                                :selectedId="$selectedCustomerId"
                                selectedName="{{ $selectedCustomer ? $selectedCustomer->name : '' }}"
                            />
                            <!-- Quick Create Button -->
                            <x-quick-create-customer
                                id="customerSearch"
                                name="customer_id"
                            />
                        </div>
                    </div>

                    <!-- Invoice Date -->
                    <div class="mb-4">
                        <label for="invoice_date" class="block text-sm text-base-content"><span class="font-medium">Invoice Date</span> <span class="text-xs italic">For online and in store sales, this should always match the payment or order date.</span></label>
                        <input type="date" name="invoice_date" id="invoice_date" class="input input-bordered w-full" value="{{ date('Y-m-d') }}" required>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">

                        <button type="submit" class="btn btn-info">
                            <i class="fa-sharp fa-arrow-right"></i>
                            Next
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
