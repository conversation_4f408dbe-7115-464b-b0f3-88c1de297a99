<x-app-layout
    page-title="Invoice: {{ $invoice->invoice_number }}"
    page-icon="fa-sharp fa-file-invoice"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('invoices.index'),
            'text' => 'Back to Invoices'
        ],
        [
            'type' => 'edit',
            'route' => route('invoices.edit', $invoice),
            'text' => 'Edit Invoice'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'View PDF',
            'route' => route('invoices.pdf', $invoice),
            'icon' => 'fa-sharp fa-file-pdf',
            'class' => 'btn btn-info btn-sm gap-2',
            'target' => '_blank'
        ],
        [
            'name' => 'Duplicate Invoice',
            'route' => route('invoices.create', ['duplicate' => $invoice->id]),
            'icon' => 'fa-sharp fa-copy',
            'class' => 'btn btn-secondary btn-sm gap-2'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Invoices', 'route' => 'invoices.index', 'icon' => 'fa-file-invoice'],
        ['name' => 'Invoice: ' . $invoice->invoice_number, 'icon' => 'fa-file-invoice']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
            
            <!-- Invoice Overview -->
            <div class="card bg-base-100 shadow-md">
                <div class="px-6 py-4 border-b border-base-300">
                    <h4 class="text-lg font-semibold text-base-content">Invoice Overview</h4>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Customer Information -->
                        <div>
                            <h5 class="text-md font-medium text-base-content mb-4">Customer Information</h5>
                            <div class="space-y-3 text-sm">
                                <div class="flex items-center gap-3">
                                    <span class="font-medium min-w-16">Name:</span>
                                    <a href="{{ route('customers.show', $invoice->customer) }}" class="link link-primary">
                                        {{ $invoice->customer->name }}
                                    </a>
                                </div>
                                <div class="flex items-center gap-3">
                                    <span class="font-medium min-w-16">Email:</span>
                                    <span>{{ $invoice->customer->email }}</span>
                                </div>
                                <div class="flex items-center gap-3">
                                    <span class="font-medium min-w-16">Phone:</span>
                                    <span>{{ $invoice->customer->phone }}</span>
                                </div>
                                <div class="flex items-center gap-3">
                                    <span class="font-medium min-w-16">Address:</span>
                                    <span>{{ $invoice->customer->address }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Invoice Details -->
                        <div>
                            <h5 class="text-md font-medium text-base-content mb-4">Invoice Details</h5>
                            <div class="space-y-3 text-sm">
                                <div class="flex items-center gap-3">
                                    <span class="font-medium min-w-24">Invoice Date:</span>
                                    <span>{{ $invoice->invoice_date->format('F d, Y') }}</span>
                                </div>
                                <div class="flex items-center gap-3">
                                    <span class="font-medium min-w-24">Status:</span>
                                    <span class="badge badge-sm">{{ $invoice->status }}</span>
                                </div>
                                <div class="flex items-center gap-3">
                                    <span class="font-medium min-w-24">Payment Method:</span>
                                    <span>{{ $invoice->payment_method }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Line Items -->
            <div class="card bg-base-100 shadow-md">
                <div class="px-6 py-4 border-b border-base-300">
                    <h4 class="text-lg font-semibold text-base-content">Line Items</h4>
                </div>
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="table table-zebra w-full">
                            <thead>
                                <tr>
                                    <th>Item Name</th>
                                    <th>Description</th>
                                    <th>Quantity</th>
                                    <th>Price</th>
                                    <th>Tax</th>
                                    <th>Discount</th>
                                    <th>Subtotal</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($invoice->lineItems as $lineItem)
                                <tr>
                                    <td>{{ $lineItem->inventory->name ?? 'N/A' }}</td>
                                    <td>{{ $lineItem->description ?? 'No details provided' }}</td>
                                    <td>{{ $lineItem->quantity }}</td>
                                    <td>${{ number_format($lineItem->price, 2) }}</td>
                                    <td>${{ number_format($lineItem->tax, 2) }}</td>
                                    <td>${{ number_format($lineItem->discount, 2) }}</td>
                                    <td>${{ number_format($lineItem->subtotal, 2) }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Invoice Summary -->
            <div class="card bg-base-100 shadow-md">
                <div class="px-6 py-4 border-b border-base-300">
                    <h4 class="text-lg font-semibold text-base-content">Invoice Summary</h4>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Discounts Applied -->
                        @if($invoice->discounts->isNotEmpty())
                        <div>
                            <h5 class="text-md font-medium text-base-content mb-4">Discounts Applied</h5>
                            <div class="space-y-2">
                                @foreach($invoice->discounts as $discount)
                                <div class="flex items-center justify-between">
                                    <span class="text-sm">{{ $discount->name }}</span>
                                    <span class="badge badge-sm">
                                        {{ $discount->type === 'percent' ? $discount->amount . '%' : '$' . $discount->amount }}
                                    </span>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Invoice Totals -->
                        <div>
                            <h5 class="text-md font-medium text-base-content mb-4">Invoice Totals</h5>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="font-medium">Total Price:</span>
                                    <span class="font-semibold">${{ number_format($invoice->total_price, 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium">Total Tax:</span>
                                    <span class="font-semibold">${{ number_format($invoice->total_tax, 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium">Total Discount:</span>
                                    <span class="font-semibold">-${{ number_format($invoice->total_discount, 2) }}</span>
                                </div>
                                <div class="divider my-2"></div>
                                <div class="flex justify-between">
                                    <span class="font-bold text-lg">Final Price:</span>
                                    <span class="font-bold text-lg {{ $invoice->final_price < 0 ? 'text-error' : 'text-primary' }}">
                                        ${{ number_format($invoice->final_price, 2) }}
                                    </span>
                                </div>
                                @if($invoice->final_price < 0)
                                <div class="alert alert-warning">
                                    <span class="text-sm">This is a payout - you owe the customer money</span>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Attachments -->
            <div class="card bg-base-100 shadow-md">
                <div class="px-6 py-4 border-b border-base-300">
                    <h4 class="text-lg font-semibold text-base-content">Invoice Attachments</h4>
                </div>
                <div class="p-6">
                    @if($invoice->files->isEmpty())
                        <div class="text-center text-base-content/70 py-8">
                            <div class="flex flex-col items-center gap-4">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                        <i class="fa-sharp fa-file text-2xl"></i>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <h3 class="text-lg font-medium text-base-content/80">No files attached</h3>
                                    <p class="text-sm text-base-content/60 mt-1">No files have been attached to this invoice.</p>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($invoice->files as $file)
                                <div class="card bg-base-100 shadow border border-base-300 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200">
                                    <div class="p-4">
                                        @if(in_array($file->extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']))
                                            <div class="w-full h-40 overflow-hidden rounded-md border border-base-300 mb-3">
                                                <img src="{{ $file->getThumbnailUrl(300, 160) }}" alt="{{ $file->description }}" class="h-full w-full object-cover object-center">
                                            </div>
                                        @else
                                            <div class="w-full h-40 flex items-center justify-center rounded-md border border-base-300 bg-base-200 mb-3">
                                                <i class="fa-sharp {{ $file->icon }} text-5xl text-base-content/50"></i>
                                            </div>
                                        @endif

                                        <div class="mt-2">
                                            <h4 class="font-medium text-base-content truncate">{{ $file->original_filename }}</h4>
                                            <p class="text-sm text-base-content/70">{{ $file->human_readable_size }} • {{ $file->metadata['file_type'] ?? 'Other' }}</p>
                                            @if($file->description)
                                                <p class="text-sm text-base-content mt-2">{{ $file->description }}</p>
                                            @endif
                                        </div>

                                        <div class="mt-4 flex gap-2">
                                            <a href="{{ route('files.download', $file) }}" class="btn btn-sm btn-primary flex-1">
                                                Download
                                            </a>
                                            <a href="{{ route('files.view', $file) }}" class="btn btn-sm btn-secondary flex-1" target="_blank">
                                                View
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

