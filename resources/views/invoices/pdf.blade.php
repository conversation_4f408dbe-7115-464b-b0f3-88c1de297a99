<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice #{{ $invoice->id }}</title>
    <style>
        @page {
            margin: 0.5in;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 10pt;
            line-height: 1.3;
            color: #333;
        }
        .header {
            border-bottom: 2px solid #3B82F6;
            padding-bottom: 12px;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            color: #1F2937;
            font-size: 20pt;
        }
        .header .subtitle {
            margin-top: 3px;
            color: #6B7280;
            font-size: 11pt;
        }
        .invoice-info {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .invoice-info .left {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }
        .invoice-info .right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            text-align: right;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 12pt;
            font-weight: bold;
            color: #1F2937;
            margin-bottom: 8px;
            padding-bottom: 3px;
            border-bottom: 1px solid #E5E7EB;
        }
        .info-grid {
            display: table;
            width: 100%;
            margin-bottom: 10px;
        }
        .info-row {
            display: table-row;
        }
        .info-label {
            display: table-cell;
            width: 120px;
            padding: 2px 0;
            font-weight: bold;
            color: #4B5563;
            font-size: 9pt;
        }
        .info-value {
            display: table-cell;
            padding: 2px 0;
            color: #1F2937;
            font-size: 9pt;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            font-size: 9pt;
        }
        .items-table th {
            background-color: #F3F4F6;
            border: 1px solid #D1D5DB;
            padding: 6px;
            text-align: left;
            font-weight: bold;
            color: #374151;
        }
        .items-table td {
            border: 1px solid #D1D5DB;
            padding: 5px;
            color: #374151;
        }
        .items-table tr:nth-child(even) {
            background-color: #F9FAFB;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .totals-section {
            margin-top: 15px;
            border-top: 2px solid #E5E7EB;
            padding-top: 12px;
        }
        .totals-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10pt;
        }
        .totals-table td {
            padding: 4px;
            border: none;
        }
        .totals-table .label {
            text-align: right;
            font-weight: bold;
            color: #4B5563;
            width: 70%;
        }
        .totals-table .value {
            text-align: right;
            color: #1F2937;
            width: 30%;
        }
        .total-final {
            font-size: 11pt;
            font-weight: bold;
            background-color: #F3F4F6;
            border-top: 2px solid #374151;
        }
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 8pt;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-paid {
            background-color: #D1FAE5;
            color: #065F46;
        }
        .status-pending {
            background-color: #FEF3C7;
            color: #92400E;
        }
        .status-overdue {
            background-color: #FEE2E2;
            color: #991B1B;
        }
        .footer {
            margin-top: 30px;
            padding-top: 12px;
            border-top: 1px solid #E5E7EB;
            display: table;
            width: 100%;
            font-size: 8pt;
            color: #6B7280;
        }
        .footer-left {
            display: table-cell;
            width: 50%;
            vertical-align: middle;
        }
        .footer-right {
            display: table-cell;
            width: 50%;
            vertical-align: middle;
            text-align: right;
        }
        .footer-logo {
            max-height: 60px;
            max-width: 250px;
        }
        .notes-section {
            margin-top: 20px;
            padding: 10px;
            background-color: #F9FAFB;
            border-left: 3px solid #3B82F6;
        }
        .notes-section h3 {
            margin: 0 0 8px 0;
            color: #1F2937;
            font-size: 11pt;
        }
        .notes-content {
            font-size: 9pt;
            line-height: 1.4;
        }
        .notes-content p {
            margin: 0 0 8px 0;
        }
        .notes-content ul, .notes-content ol {
            margin: 0 0 8px 0;
            padding-left: 20px;
        }
        .notes-content li {
            margin-bottom: 3px;
        }
        .notes-content strong {
            font-weight: bold;
        }
        .notes-content em {
            font-style: italic;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>Invoice #{{ $invoice->id }}</h1>
        <div class="subtitle">
            {{ config('app.name') }}
            @if(isset($contactInfo) && array_filter($contactInfo))
                <br>
                @if($contactInfo['email'])
                    {{ $contactInfo['email'] }}
                @endif
                @if($contactInfo['phone'])
                    @if($contactInfo['email']) • @endif
                    {{ $contactInfo['phone'] }}
                @endif
                @if($contactInfo['website'])
                    @if($contactInfo['email'] || $contactInfo['phone']) • @endif
                    {{ $contactInfo['website'] }}
                @endif
            @endif
        </div>
    </div>

    <!-- Invoice Info -->
    <div class="invoice-info">
        <div class="left">
            <div class="section-title">Bill To</div>
            <div class="info-grid">
                <div class="info-row">
                    <div class="info-label">Customer:</div>
                    <div class="info-value">{{ $invoice->customer->name }}</div>
                </div>
                @if($invoice->customer->email)
                <div class="info-row">
                    <div class="info-label">Email:</div>
                    <div class="info-value">{{ $invoice->customer->email }}</div>
                </div>
                @endif
                @if($invoice->customer->phone)
                <div class="info-row">
                    <div class="info-label">Phone:</div>
                    <div class="info-value">{{ $invoice->customer->phone }}</div>
                </div>
                @endif
                @if($invoice->customer->address)
                <div class="info-row">
                    <div class="info-label">Address:</div>
                    <div class="info-value">{{ $invoice->customer->address }}</div>
                </div>
                @endif
            </div>
        </div>
        <div class="right">
            <div class="info-grid">
                <div class="info-row">
                    <div class="info-label">Invoice Date:</div>
                    <div class="info-value">{{ $invoice->invoice_date ? $invoice->invoice_date->format('F j, Y') : 'N/A' }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Status:</div>
                    <div class="info-value">
                        <span class="status-badge status-{{ strtolower($invoice->status ?? 'pending') }}">
                            {{ $invoice->status ?? 'Pending' }}
                        </span>
                    </div>
                </div>
                @if($invoice->payment_method)
                <div class="info-row">
                    <div class="info-label">Payment Method:</div>
                    <div class="info-value">{{ $invoice->payment_method }}</div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Line Items -->
    <div class="section">
        <div class="section-title">Items</div>
        <table class="items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th class="text-center">Quantity</th>
                    <th class="text-right">Unit Price</th>
                    <th class="text-right">Tax</th>
                    <th class="text-right">Discount</th>
                    <th class="text-right">Total</th>
                </tr>
            </thead>
            <tbody>
                @forelse($invoice->lineItems as $lineItem)
                <tr>
                    <td>
                        <strong>{{ $lineItem->name }}</strong>
                        @if($lineItem->description)
                            <br><small style="color: #6B7280;">{{ $lineItem->description }}</small>
                        @endif
                        @if($lineItem->inventory)
                            <br><small style="color: #6B7280;">Asset: {{ $lineItem->inventory->asset_tag }}</small>
                        @endif
                    </td>
                    <td class="text-center">{{ $lineItem->quantity }}</td>
                    <td class="text-right">${{ number_format($lineItem->unit_price, 2) }}</td>
                    <td class="text-right">${{ number_format($lineItem->tax_amount, 2) }}</td>
                    <td class="text-right">${{ number_format($lineItem->discount_amount, 2) }}</td>
                    <td class="text-right">${{ number_format($lineItem->total_price, 2) }}</td>
                </tr>
                @empty
                <tr>
                    <td colspan="6" class="text-center" style="color: #6B7280; font-style: italic;">No items added to this invoice</td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Discounts Applied -->
    @if($invoice->invoiceDiscounts->count() > 0)
    <div class="section">
        <div class="section-title">Applied Discounts</div>
        <table class="items-table">
            <thead>
                <tr>
                    <th>Discount</th>
                    <th>Type</th>
                    <th class="text-right">Amount</th>
                </tr>
            </thead>
            <tbody>
                @foreach($invoice->invoiceDiscounts as $invoiceDiscount)
                <tr>
                    <td>{{ $invoiceDiscount->discount->name }}</td>
                    <td>{{ ucfirst($invoiceDiscount->discount->type) }}</td>
                    <td class="text-right">-${{ number_format($invoiceDiscount->discount_amount, 2) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- Totals -->
    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td class="label">Subtotal:</td>
                <td class="value">${{ number_format($invoice->total_price, 2) }}</td>
            </tr>
            <tr>
                <td class="label">Tax:</td>
                <td class="value">${{ number_format($invoice->total_tax, 2) }}</td>
            </tr>
            <tr>
                <td class="label">Discount:</td>
                <td class="value">-${{ number_format($invoice->total_discount, 2) }}</td>
            </tr>
            <tr class="total-final">
                <td class="label">Total:</td>
                <td class="value">${{ number_format($invoice->final_price, 2) }}</td>
            </tr>
        </table>
    </div>

    <!-- Notes -->
    @if($invoice->notes)
    <div class="notes-section">
        <h3>Notes</h3>
        <div class="notes-content">
            {!! $invoice->notes !!}
        </div>
    </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <div class="footer-left">
            @if(isset($logoImageData) && $logoImageData)
                <img src="{{ $logoImageData }}" alt="{{ config('app.name') }} Logo" class="footer-logo">
            @endif
        </div>
        <div class="footer-right">
            <p>Generated on {{ now()->format('F j, Y g:i A') }}</p>
            <p>Invoice #{{ $invoice->id }} - {{ config('app.name') }}</p>
        </div>
    </div>
</body>
</html>