<x-app-layout
    page-title="Image Manager"
    page-icon="fa-sharp fa-images"
    :primary-buttons="[
        [
            'type' => 'create',
            'route' => route('images.upload'),
            'text' => 'Upload Images',
            'permission' => 'create_image_items'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Images', 'icon' => 'fa-images']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
            
            <!-- Header Card -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-base-content">Image Manager</h1>
                            <p class="text-base-content/70 mt-1 text-lg">Manage all uploaded images across the system</p>
                        </div>
                        <div class="hidden lg:flex items-center gap-2 text-base-content/60">
                            <div class="stat">
                                <div class="stat-title text-xs">Total Images</div>
                                <div class="stat-value text-2xl">{{ $images->total() }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search & Filter Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-magnifying-glass text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Search & Filter</h4>
                    </div>
                </div>
                <div class="p-6">
                    <form method="GET" action="{{ route('images.index') }}" class="flex flex-col lg:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" 
                                   name="search" 
                                   value="{{ request('search') }}" 
                                   placeholder="Search by filename, title, or description..." 
                                   class="input input-bordered w-full">
                        </div>
                        <div class="flex gap-2">
                            <button type="submit" class="btn btn-primary gap-2">
                                <i class="fa-sharp fa-search"></i>
                                Search
                            </button>
                            @if(request('search'))
                                <a href="{{ route('images.index') }}" class="btn gap-2">
                                    <i class="fa-sharp fa-times"></i>
                                    Clear
                                </a>
                            @endif
                        </div>
                    </form>
                </div>
                
                <!-- Sorting Controls -->
                <div class="px-6 py-4 border-t border-base-300/50 bg-base-50">
                    <form method="GET" class="flex flex-wrap items-center gap-4">
                        @if(request('search'))
                            <input type="hidden" name="search" value="{{ request('search') }}">
                        @endif
                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium text-base-content/70">Sort by:</label>
                            <select name="sort" class="select select-bordered select-sm w-auto min-w-32" onchange="this.form.submit()">
                                <option value="created_at" {{ request('sort', 'created_at') === 'created_at' ? 'selected' : '' }}>Upload Date</option>
                                <option value="title" {{ request('sort') === 'title' ? 'selected' : '' }}>Title</option>
                                <option value="og_filename" {{ request('sort') === 'og_filename' ? 'selected' : '' }}>Filename</option>
                            </select>
                        </div>
                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium text-base-content/70">Order:</label>
                            <select name="direction" class="select select-bordered select-sm w-auto" onchange="this.form.submit()">
                                <option value="desc" {{ request('direction', 'desc') === 'desc' ? 'selected' : '' }}>Newest First</option>
                                <option value="asc" {{ request('direction') === 'asc' ? 'selected' : '' }}>Oldest First</option>
                            </select>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Images Grid -->
            <div class="card bg-base-100 shadow-md">
                <div class="p-6">
                    @if($images->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($images as $image)
                                <div class="card bg-base-100 border border-base-300 shadow-sm hover:shadow-lg transition-all duration-200">
                                    <!-- Image Preview -->
                                    <figure class="relative aspect-video bg-base-200">
                                        <img src="{{ $image->getImageSrc('sm') }}" 
                                             alt="{{ $image->alt_text ?: $image->title ?: 'Image' }}"
                                             class="w-full h-full object-cover"
                                             loading="lazy">
                                        
                                        <!-- Context Badge -->
                                        @if($image->context_type)
                                            <div class="absolute top-2 left-2">
                                                <div class="badge badge-sm bg-base-100/90 backdrop-blur">
                                                    <i class="fa-sharp fa-{{ $image->context_type === 'inventory' ? 'box' : 'link' }} mr-1"></i>
                                                    {{ ucfirst($image->context_type) }}
                                                </div>
                                            </div>
                                        @endif
                                    </figure>
                                    
                                    <div class="card-body p-4">
                                        <!-- Title/Filename -->
                                        <h3 class="card-title text-base">
                                            {{ $image->title ?: $image->og_filename }}
                                        </h3>
                                        
                                        <!-- File Details -->
                                        <div class="text-sm text-base-content/70 space-y-1 mt-2">
                                            <div class="flex items-center gap-2">
                                                <i class="fa-sharp fa-file text-xs"></i>
                                                <span class="truncate">{{ $image->og_filename }}</span>
                                            </div>
                                            <div class="flex items-center gap-2">
                                                <i class="fa-sharp fa-calendar text-xs"></i>
                                                <span>{{ $image->created_at->format('M d, Y g:i A') }}</span>
                                            </div>
                                            @if($image->uploaded_by)
                                                <div class="flex items-center gap-2">
                                                    <i class="fa-sharp fa-user text-xs"></i>
                                                    <span>{{ $image->uploader->name ?? 'Unknown' }}</span>
                                                </div>
                                            @endif
                                        </div>
                                        
                                        <!-- Actions -->
                                        <div class="card-actions justify-end mt-4">
                                            <a href="{{ route('images.show', $image) }}" 
                                               class="btn btn-sm btn-ghost gap-1"
                                               title="View Details">
                                                <i class="fa-sharp fa-eye"></i>
                                            </a>
                                            @perms('edit_image_items')
                                                <a href="{{ route('images.edit', $image) }}" 
                                                   class="btn btn-sm btn-ghost gap-1"
                                                   title="Edit">
                                                    <i class="fa-sharp fa-edit"></i>
                                                </a>
                                            @endperms
                                            <a href="{{ route('images.download', $image) }}" 
                                               class="btn btn-sm btn-ghost gap-1"
                                               title="Download">
                                                <i class="fa-sharp fa-download"></i>
                                            </a>
                                            @perms('delete_image_items')
                                                <form action="{{ route('images.destroy', $image) }}" 
                                                      method="POST" 
                                                      class="inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this image?');">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-ghost text-error gap-1"
                                                            title="Delete">
                                                        <i class="fa-sharp fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endperms
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <!-- Empty State -->
                        <div class="text-center text-base-content/70 py-12">
                            <div class="flex flex-col items-center gap-4">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                        <i class="fa-sharp fa-images text-2xl"></i>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <h3 class="text-lg font-medium text-base-content/80">No images found</h3>
                                    <p class="text-sm text-base-content/60 mt-1">
                                        @if(request('search'))
                                            No images match your search criteria
                                        @else
                                            Upload your first image to get started
                                        @endif
                                    </p>
                                </div>
                                @if(request('search'))
                                    <a href="{{ route('images.index') }}" class="btn btn-sm gap-2">
                                        <i class="fa-sharp fa-times"></i>
                                        Clear Search
                                    </a>
                                @else
                                    @perms('create_image_items')
                                        <a href="{{ route('images.upload') }}" 
                                           class="btn btn-primary btn-sm gap-2">
                                            <i class="fa-sharp fa-upload"></i>
                                            Upload First Image
                                        </a>
                                    @endperms
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Pagination -->
            @if($images->hasPages())
                <div class="card bg-base-100 shadow-md">
                    <div class="px-6 py-4 border-t border-base-300/50 bg-base-50">
                        <x-pagination :paginator="$images" :pagination="$pagination" />
                    </div>
                </div>
            @endif
        </div>
    </div>

</x-app-layout>
