<x-app-layout :title="'Edit Image'">
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit Image') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto px-0 md:px-4">
            <div class="card mx-auto p-2 md:p-6 bg-base-100 rounded-lg shadow-lg overflow-hidden">
                
                <!-- Back and Save Buttons -->
                <div class="flex flex-wrap justify-between items-center mb-6">
                    <div class="text-gray-700 text-sm">
                        <a href="{{ $previousUrl }}" class="text-blue-500 hover:underline flex items-center space-x-2">
                            <i class="fa-sharp fa-arrow-left"></i>
                            <span>Back</span>
                        </a>
                        <p><span class="font-bold">Note:</span> Changes are permanent and cannot be undone.</p>
                    </div>
                    <button id="custom-save-button" class="btn btn-success">
                        <i class="fa-sharp fa-save"></i>
                        Save Changes
                    </button>
                </div>

                <!-- Editable Details Section -->
                <div class="grid grid-cols-1">
                    <!-- Title and Description Inputs -->
                    <div>
                        <label class="block mb-4">
                            <span class="font-bold text-gray-700">Title</span>
                            <input 
                                type="text" 
                                id="imageTitle" 
                                class="input input-bordered w-full" 
                                value="{{ $image->title ?? '' }}"
                            >
                        </label>

                    </div>

                    <!-- Image Editor Container -->
                    <div>
                        <div id="editor-container"
                            data-image-id="{{ $image->id }}"
                            data-image-url="{{ asset('storage/' . $image->image_path) }}"
                            data-return-url="{{ $previousUrl }}"
                            class="rounded-md shadow-sm border border-gray-300 overflow-hidden">
                        </div>
                    </div>

                    <label class="block mb-4">
                        <span class="font-bold text-gray-700">Description</span>
                        <textarea 
                            id="imageDescription" 
                            rows="6" 
                            class="textarea textarea-bordered w-full">{{ $image->description ?? '' }}</textarea>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- Filerobot Image Editor CDN -->
    <script src="https://scaleflex.cloudimg.io/v7/plugins/filerobot-image-editor/latest/filerobot-image-editor.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const editorContainer = document.querySelector('#editor-container');
            const imageId = editorContainer.getAttribute('data-image-id');
            const imageUrl = editorContainer.getAttribute('data-image-url');
            const returnUrl = editorContainer.getAttribute('data-return-url'); 
            const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

            const titleInput = document.getElementById('imageTitle');
            const descriptionTextarea = document.getElementById('imageDescription');

            const config = {
                source: imageUrl,
                removeSaveButton: true,
                closeButton: false,
                annotationsCommon: { fill: '#ff0000' },
                Text: { text: 'Type Something' },
                Rotate: { angle: 90, componentType: 'slider' },
                tabsIds: [
                    window.FilerobotImageEditor.TABS.ADJUST,
                    window.FilerobotImageEditor.TABS.ANNOTATE,
                    window.FilerobotImageEditor.TABS.WATERMARK,
                ],
                defaultTabId: window.FilerobotImageEditor.TABS.ANNOTATE,
                defaultToolId: window.FilerobotImageEditor.TOOLS.TEXT,
                previewPixelRatio: 5,
                savingPixelRatio: 10,
                defaultSavedImageQuality: 1,
                onSave: null,
            };

            const filerobotImageEditor = new window.FilerobotImageEditor(editorContainer, config);

            // Set dynamic height for the editor container
            editorContainer.style.height = '80vh';

            filerobotImageEditor.render({
                onClose: () => {
                    console.log('Editor closed.');
                },
            });

            const saveButton = document.getElementById('custom-save-button');
            saveButton.addEventListener('click', () => {
                const editedImage = filerobotImageEditor.getCurrentImgData();

                if (!editedImage || !editedImage.imageData) {
                    alert('No changes to save.');
                    return;
                }

                const formData = new FormData();
                formData.append('id', imageId);
                formData.append('image', editedImage.imageData.imageBase64);
                formData.append('title', titleInput.value.trim());
                formData.append('description', descriptionTextarea.value.trim());

                fetch('/images/update', {
                        method: 'POST',
                        headers: { 'X-CSRF-TOKEN': csrfToken },
                        body: formData,
                    })
                    .then((response) => {
                        if (response.ok) {
                            window.location.href = returnUrl || `/images/${imageId}`;
                        } else {
                            alert('Failed to save changes.');
                        }
                    })
                    .catch((error) => {
                        console.error('Error saving image:', error);
                        alert('An error occurred while saving the image.');
                    });
            });
        });
    </script>
</x-app-layout>
