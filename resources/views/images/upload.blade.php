<x-app-layout
    page-title="Upload Images"
    page-icon="fa-sharp fa-upload"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('images.index'),
            'text' => 'Back to Images'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Images', 'route' => 'images.index', 'icon' => 'fa-images'],
        ['name' => 'Upload', 'icon' => 'fa-upload']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
            
            <!-- Introduction Card -->
            <div class="bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
                <!-- Decorative Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 left-0 w-32 h-32 bg-primary rounded-full -translate-x-16 -translate-y-16"></div>
                    <div class="absolute bottom-0 right-0 w-24 h-24 bg-secondary rounded-full translate-x-12 translate-y-12"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-start gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-gradient-to-br from-primary to-primary-focus text-primary-content w-12 rounded-xl shadow-lg">
                                <i class="fa-sharp fa-cloud-upload text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-2xl font-bold text-base-content mb-2">Upload Images</h3>
                            <p class="text-base-content/70 text-base leading-relaxed">
                                Upload multiple images at once. Images will be automatically compressed and converted to WebP format for optimal storage and performance.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upload Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-images text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Select Images</h4>
                    </div>
                </div>
                <div class="p-6">
                    <x-image-dropzone
                        id="image-upload"
                        name="images[]"
                        :max-files="20"
                        :max-filesize="12"
                        :max-width="2600"
                        :max-height="2600"
                        :client-resize="true"
                        accepted-formats="image/jpeg,image/jpg,image/png,image/webp"
                        :multiple="true"
                        :immediate-upload="true"
                        label=""
                        help-text="Drag and drop images here or click to browse"
                        sub-text="You can upload up to 20 images at once. Each file can be up to 12MB. Images will be automatically compressed and converted to WebP format."
                    />
                </div>
            </div>

            <!-- Information Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-info text-info-content w-8 rounded-lg">
                                <i class="fa-sharp fa-info-circle text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Upload Information</h4>
                    </div>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex items-start gap-3">
                            <i class="fa-sharp fa-check-circle text-success mt-1"></i>
                            <div>
                                <p class="font-medium text-base-content">Automatic Compression</p>
                                <p class="text-sm text-base-content/70">Images are automatically compressed for optimal file size</p>
                            </div>
                        </div>
                        <div class="flex items-start gap-3">
                            <i class="fa-sharp fa-check-circle text-success mt-1"></i>
                            <div>
                                <p class="font-medium text-base-content">WebP Conversion</p>
                                <p class="text-sm text-base-content/70">All images are converted to WebP format for better performance</p>
                            </div>
                        </div>
                        <div class="flex items-start gap-3">
                            <i class="fa-sharp fa-check-circle text-success mt-1"></i>
                            <div>
                                <p class="font-medium text-base-content">Multiple Sizes</p>
                                <p class="text-sm text-base-content/70">Thumbnails are automatically generated for faster loading</p>
                            </div>
                        </div>
                        <div class="flex items-start gap-3">
                            <i class="fa-sharp fa-check-circle text-success mt-1"></i>
                            <div>
                                <p class="font-medium text-base-content">Batch Upload</p>
                                <p class="text-sm text-base-content/70">Upload up to 20 images at once</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="divider"></div>
                    
                    <div class="alert alert-info">
                        <i class="fa-sharp fa-info-circle"></i>
                        <div>
                            <h3 class="font-bold">Supported Formats</h3>
                            <p class="text-sm">JPEG, JPG, PNG, and WebP images are supported. Other formats will be rejected.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="flex justify-center">
                <a href="{{ route('images.index') }}" class="btn btn-lg gap-2">
                    <i class="fa-sharp fa-arrow-left"></i>
                    Back to Image Manager
                </a>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Listen for successful uploads and redirect to index
        document.addEventListener('DOMContentLoaded', () => {
            const dropzoneElement = document.querySelector('[data-dropzone-id="dropzone-image-upload"]');
            if (dropzoneElement) {
                let uploadedCount = 0;
                let totalUploads = 0;
                let hasUploaded = false;
                
                dropzoneElement.addEventListener('files-selected', function(e) {
                    totalUploads = e.detail.count || 0;
                    uploadedCount = 0;
                });
                
                dropzoneElement.addEventListener('image-uploaded', function(e) {
                    uploadedCount++;
                    hasUploaded = true;
                    
                    if (uploadedCount >= totalUploads && totalUploads > 0) {
                        // Show success message
                        showToast('success', `Successfully uploaded ${uploadedCount} image${uploadedCount > 1 ? 's' : ''}!`, 3000);
                        
                        // Redirect to index after a delay
                        setTimeout(() => {
                            window.location.href = '{{ route('images.index') }}';
                        }, 1500);
                    }
                });
            }
        });
    </script>
    @endpush
</x-app-layout>