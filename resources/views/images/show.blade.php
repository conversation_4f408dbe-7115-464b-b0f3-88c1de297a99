<x-app-layout
    page-title="Image Details"
    page-icon="fa-sharp fa-image"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('images.index'),
            'text' => 'Back to Images'
        ],
        [
            'type' => 'edit',
            'route' => route('images.edit', $image),
            'text' => 'Edit Image',
            'permission' => 'edit_image_items'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'Download',
            'route' => route('images.download', $image),
            'icon' => 'fa-sharp fa-download',
            'class' => 'btn btn-accent btn-sm gap-2'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Images', 'route' => 'images.index', 'icon' => 'fa-images'],
        ['name' => $image->title ?: $image->og_filename, 'icon' => 'fa-image']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
            
            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                
                <!-- Left Column: Image Preview -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-image text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Image Preview</h4>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="aspect-video bg-base-200 rounded-lg overflow-hidden">
                            <img src="{{ $image->getImageSrc('full') }}" 
                                 alt="{{ $image->alt_text ?: $image->title ?: 'Image' }}"
                                 class="w-full h-full object-contain">
                        </div>
                        
                        <!-- Context Badge -->
                        @if($image->context_type)
                            <div class="mt-4">
                                <div class="badge badge-lg">
                                    <i class="fa-sharp fa-{{ $image->context_type === 'inventory' ? 'box' : 'link' }} mr-2"></i>
                                    {{ ucfirst($image->context_type) }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
                
                <!-- Right Column: Details -->
                <div class="space-y-6">
                    <!-- Editable Details Card -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                            <i class="fa-sharp fa-edit text-sm"></i>
                                        </div>
                                    </div>
                                    <h4 class="text-lg font-semibold text-base-content">Details</h4>
                                </div>
                                <div class="save-indicator hidden">
                                    <i class="fa-sharp fa-spinner fa-spin text-secondary"></i>
                                </div>
                            </div>
                        </div>
                        <div class="p-6 space-y-4">
                            @perms('edit_image_items')
                                <div>
                                    <label class="label">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-heading text-secondary mr-2"></i>
                                            Title
                                        </span>
                                    </label>
                                    <input type="text" 
                                           id="imageTitle" 
                                           class="input input-bordered w-full"
                                           value="{{ $image->title ?? '' }}"
                                           placeholder="Enter image title...">
                                </div>

                                <div>
                                    <label class="label">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-align-left text-secondary mr-2"></i>
                                            Description
                                        </span>
                                    </label>
                                    <textarea id="imageDescription" 
                                              rows="4" 
                                              class="textarea textarea-bordered w-full"
                                              placeholder="Enter image description...">{{ $image->description ?? '' }}</textarea>
                                </div>
                                
                                <div class="text-sm text-base-content/60 italic">
                                    <i class="fa-sharp fa-info-circle mr-1"></i>
                                    Changes save automatically as you type
                                </div>
                            @else
                                <div>
                                    <p class="font-medium text-base-content">{{ $image->title ?: 'No title' }}</p>
                                    @if($image->description)
                                        <p class="text-base-content/70 mt-2">{{ $image->description }}</p>
                                    @endif
                                </div>
                            @endperms
                        </div>
                    </div>
                    
                    <!-- File Information Card -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-info text-info-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-info-circle text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">File Information</h4>
                            </div>
                        </div>
                        <div class="p-6 space-y-3">
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-sharp fa-file text-info"></i>
                                <span class="font-medium">Original Filename:</span>
                                <span class="text-base-content/70">{{ $image->og_filename }}</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-sharp fa-calendar text-info"></i>
                                <span class="font-medium">Uploaded:</span>
                                <span class="text-base-content/70">{{ $image->created_at->format('M d, Y g:i A') }}</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-sharp fa-user text-info"></i>
                                <span class="font-medium">Uploaded by:</span>
                                <span class="text-base-content/70">{{ $image->uploader->name ?? 'Unknown' }}</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-sharp fa-link text-info"></i>
                                <span class="font-medium">Path:</span>
                                <span class="text-base-content/70 font-mono text-xs break-all">{{ $image->image_path }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Usage Information -->
            @if(count($usages) > 0)
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-link text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Where This Image Is Used</h4>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            @foreach($usages as $usage)
                                <div class="flex items-center justify-between p-4 bg-base-200/50 rounded-lg hover:bg-base-200 transition-colors">
                                    <div class="flex items-center gap-3">
                                        <div class="avatar avatar-placeholder">
                                            <div class="bg-base-300 text-base-content w-10 rounded-lg">
                                                <i class="fa-sharp {{ $usage['icon'] }}"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <p class="font-medium text-base-content">{{ $usage['description'] }}</p>
                                            @if($usage['type'] === 'inventory' && isset($usage['model']))
                                                <p class="text-sm text-base-content/70">
                                                    Asset Tag: {{ $usage['model']->asset_tag }}
                                                </p>
                                            @endif
                                        </div>
                                    </div>
                                    <a href="{{ $usage['link'] }}" 
                                       class="btn btn-sm btn-primary gap-1"
                                       target="_blank">
                                        <i class="fa-sharp fa-external-link-alt"></i>
                                        View
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @else
                <div class="alert alert-info">
                    <i class="fa-sharp fa-info-circle"></i>
                    <span>This image is not currently linked to any items in the system.</span>
                </div>
            @endif
            
            <!-- Thumbnails Preview -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-accent text-accent-content w-8 rounded-lg">
                                <i class="fa-sharp fa-images text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Generated Thumbnails</h4>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
                        @php
                            $sizes = ['sm' => 'Small', 'md' => 'Medium', 'lg' => 'Large'];
                        @endphp
                        @foreach($sizes as $size => $label)
                            <a href="{{ $image->getImageSrc($size) }}" 
                               target="_blank"
                               class="group relative overflow-hidden rounded-lg bg-base-200 hover:shadow-lg transition-all">
                                <img src="{{ $image->getImageSrc($size) }}" 
                                     alt="{{ $image->alt_text ?: 'Thumbnail' }}"
                                     class="w-full h-auto">
                                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <p class="text-white text-sm font-medium text-center">{{ $label }}</p>
                                </div>
                            </a>
                        @endforeach
                        <a href="{{ $image->getImageSrc('full') }}" 
                           target="_blank"
                           class="group relative overflow-hidden rounded-lg bg-base-200 hover:shadow-lg transition-all">
                            <img src="{{ $image->getImageSrc('sm') }}" 
                                 alt="{{ $image->alt_text ?: 'Full size' }}"
                                 class="w-full h-auto">
                            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <p class="text-white text-sm font-medium text-center">Full Size</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Auto-Save Status Card -->
            @perms('edit_image_items')
                <div class="text-center">
                    <span id="saveStatusIndicator" class="text-sm text-base-content/60"></span>
                </div>
            @endperms
        </div>
    </div>

    @perms('edit_image_items')
        @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                const titleInput = document.getElementById('imageTitle');
                const descriptionTextarea = document.getElementById('imageDescription');
                const statusIndicator = document.getElementById('saveStatusIndicator');
                const saveIndicator = document.querySelector('.save-indicator');

                let debounceTimer;

                const updateSaveStatus = (status = 'default', message = '') => {
                    switch(status) {
                        case 'saving':
                            statusIndicator.textContent = 'Saving changes...';
                            statusIndicator.className = 'text-sm text-info';
                            saveIndicator?.classList.remove('hidden');
                            break;
                        case 'saved':
                            statusIndicator.textContent = 'All changes saved';
                            statusIndicator.className = 'text-sm text-success';
                            saveIndicator?.classList.add('hidden');
                            setTimeout(() => {
                                statusIndicator.textContent = '';
                            }, 3000);
                            break;
                        case 'error':
                            statusIndicator.textContent = message || 'Error saving changes';
                            statusIndicator.className = 'text-sm text-error';
                            saveIndicator?.classList.add('hidden');
                            break;
                        default:
                            statusIndicator.textContent = '';
                            saveIndicator?.classList.add('hidden');
                    }
                };

                const updateImageDetails = () => {
                    updateSaveStatus('saving');

                    const title = titleInput.value.trim();
                    const description = descriptionTextarea.value.trim();

                    fetch('/images/update', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        },
                        body: JSON.stringify({
                            id: {{ $image->id }},
                            title: title,
                            description: description,
                        }),
                    })
                    .then(response => {
                        if (response.ok) {
                            updateSaveStatus('saved');
                        } else {
                            updateSaveStatus('error', 'Failed to save changes');
                        }
                    })
                    .catch(error => {
                        console.error('Error updating image details:', error);
                        updateSaveStatus('error');
                    });
                };

                const handleInput = () => {
                    clearTimeout(debounceTimer);
                    updateSaveStatus('default');
                    debounceTimer = setTimeout(() => {
                        updateImageDetails();
                    }, 500);
                };

                titleInput?.addEventListener('input', handleInput);
                descriptionTextarea?.addEventListener('input', handleInput);
            });
        </script>
        @endpush
    @endperms
</x-app-layout>
