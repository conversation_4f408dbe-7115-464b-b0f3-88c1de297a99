<!-- Customer Form Fields -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">

            <!-- Name Field -->
            <div>
                <label class="label" for="name">
                    <span class="label-text">
                        <i class="fa-sharp fa-solid fa-building text-primary mr-2" id="nameIcon"></i>
                        <span id="nameLabel">Business Name</span> <span class="text-error font-bold">*</span>
                    </span>
                </label>
                <input type="text" name="name" id="name" class="input input-bordered w-full"
                       value="{{ old('name', $customer->name ?? '') }}" placeholder="Enter business or customer name..." required>
                @error('name')
                <div class="label">
                    <span class="label-text-alt text-error flex items-center gap-1">
                        <i class="fa-sharp fa-exclamation-triangle"></i>
                        {{ $message }}
                    </span>
                </div>
                @enderror
            </div>

    <!-- Type Field -->
    <div>
        <label class="label">
            <span class="label-text">
                <i class="fa-sharp fa-tag text-primary mr-2"></i>
                Customer Type <span class="text-error font-bold">*</span>
            </span>
        </label>
        <div class="grid grid-cols-2 gap-2">
            <label class="flex items-center gap-2 p-3 bg-base-200/50 border border-base-300 rounded-lg hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                <input type="radio" name="type" value="Business" class="radio radio-primary radio-sm" {{ old('type', $customer->type ?? 'Business') == 'Business' ? 'checked' : '' }} required>
                <div class="flex items-center gap-2">
                    <i class="fa-sharp fa-building text-primary text-sm"></i>
                    <span class="font-medium text-base-content">Business</span>
                </div>
            </label>

            <label class="flex items-center gap-2 p-3 bg-base-200/50 border border-base-300 rounded-lg hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                <input type="radio" name="type" value="Residential Customer" class="radio radio-primary radio-sm" {{ old('type', $customer->type ?? '') == 'Residential Customer' ? 'checked' : '' }}>
                <div class="flex items-center gap-2">
                    <i class="fa-sharp fa-home text-primary text-sm"></i>
                    <span class="font-medium text-base-content">Residential</span>
                </div>
            </label>

            <label class="flex items-center gap-2 p-3 bg-base-200/50 border border-base-300 rounded-lg hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                <input type="radio" name="type" value="Online Customer" class="radio radio-primary radio-sm" {{ old('type', $customer->type ?? '') == 'Online Customer' ? 'checked' : '' }}>
                <div class="flex items-center gap-2">
                    <i class="fa-sharp fa-globe text-primary text-sm"></i>
                    <span class="font-medium text-base-content">Online</span>
                </div>
            </label>

            <label class="flex items-center gap-2 p-3 bg-base-200/50 border border-base-300 rounded-lg hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                <input type="radio" name="type" value="Bulk Buyer" class="radio radio-primary radio-sm" {{ old('type', $customer->type ?? '') == 'Bulk Buyer' ? 'checked' : '' }}>
                <div class="flex items-center gap-2">
                    <i class="fa-sharp fa-boxes text-primary text-sm"></i>
                    <span class="font-medium text-base-content">Bulk Buyer</span>
                </div>
            </label>
        </div>
        @error('type')
        <div class="label">
            <span class="label-text-alt text-error flex items-center gap-1">
                <i class="fa-sharp fa-exclamation-triangle"></i>
                {{ $message }}
            </span>
        </div>
        @enderror
    </div>

    <!-- Nickname Field -->
    <div id="nicknameField" class="hidden">
        <label class="label" for="nickname">
            <span class="label-text">
                <i class="fa-sharp fa-user-tag text-primary mr-2"></i>
                Nickname / Username
            </span>
        </label>
        <input type="text" name="nickname" id="nickname" class="input input-bordered input-sm w-full"
               value="{{ old('nickname', $customer->nickname ?? '') }}" placeholder="Enter nickname or username...">
        @error('nickname')
        <div class="label">
            <span class="label-text-alt text-error flex items-center gap-1">
                <i class="fa-sharp fa-exclamation-triangle"></i>
                {{ $message }}
            </span>
        </div>
        @enderror
    </div>

    <!-- Primary Contact Field -->
    <div id="contactField">
        <label class="label" for="contact">
            <span class="label-text">
                <i class="fa-sharp fa-user text-primary mr-2"></i>
                Primary Contact
            </span>
        </label>
        <input type="text" name="contact" id="contact" class="input input-bordered input-sm w-full"
               value="{{ old('contact', $customer->contact ?? '') }}" placeholder="Enter primary contact person...">
        @error('contact')
        <div class="label">
            <span class="label-text-alt text-error flex items-center gap-1">
                <i class="fa-sharp fa-exclamation-triangle"></i>
                {{ $message }}
            </span>
        </div>
        @enderror
    </div>

    <!-- Website Field -->
    <div id="websiteField">
        <label class="label" for="website">
            <span class="label-text">
                <i class="fa-sharp fa-globe text-primary mr-2"></i>
                Website
            </span>
        </label>
        <input type="text" name="website" id="website" class="input input-bordered input-sm w-full"
               value="{{ old('website', $customer->website ?? '') }}" placeholder="https://example.com">
        @error('website')
        <div class="label">
            <span class="label-text-alt text-error flex items-center gap-1">
                <i class="fa-sharp fa-exclamation-triangle"></i>
                {{ $message }}
            </span>
        </div>
        @enderror
    </div>

    <!-- Email Field -->
    <div>
        <label class="label" for="email">
            <span class="label-text">
                <i class="fa-sharp fa-envelope text-primary mr-2"></i>
                Email Address
            </span>
        </label>
        <input type="email" name="email" id="email" class="input input-bordered input-sm w-full"
               value="{{ old('email', $customer->email ?? '') }}" placeholder="Enter email address...">
        @error('email')
        <div class="label">
            <span class="label-text-alt text-error flex items-center gap-1">
                <i class="fa-sharp fa-exclamation-triangle"></i>
                {{ $message }}
            </span>
        </div>
        @enderror
    </div>

    <!-- Phone Field -->
    <div>
        <label class="label" for="phone">
            <span class="label-text">
                <i class="fa-sharp fa-phone text-primary mr-2"></i>
                Phone Number
            </span>
        </label>
        <input type="text" name="phone" id="phone" class="input input-bordered input-sm w-full"
               value="{{ old('phone', $customer->phone ?? '') }}" placeholder="Enter phone number (any format)">
        @error('phone')
        <div class="label">
            <span class="label-text-alt text-error flex items-center gap-1">
                <i class="fa-sharp fa-exclamation-triangle"></i>
                {{ $message }}
            </span>
        </div>
        @enderror
    </div>

    <!-- Address Field with Google Maps Autocomplete -->
    <div class="md:col-span-2">
        <label class="label" for="address">
            <span class="label-text">
                <i class="fa-sharp fa-map-marker-alt text-primary mr-2"></i>
                Address
            </span>
        </label>
        </div>
    <div class="md:col-span-2">
        <x-google-maps-autocomplete
            id="address"
            name="address"
            :value="old('address', $customer->address ?? '')"
            placeholder="Start typing to search for an address..."
            type="textarea"
            rows="2"
            class="textarea"
            label="Address"
            icon="fa-map-marker-alt"
            iconColor="text-primary"
            :restrictions="['us', 'ca']"
            :showDisplayMode="false"
        />
    </div>


</div>

<!-- Notes Field (Outside Grid) -->
<div class="mt-6">
    <label class="label" for="notes">
        <span class="label-text">
            <i class="fa-sharp fa-sticky-note text-primary mr-2"></i>
            Additional Notes
        </span>
    </label>
    <!-- Quill Editor Container -->
    <div id="editor" class="w-full border border-base-300 rounded-lg" style="height: 120px;"></div>
    <!-- Hidden Input Field to Store Quill Content -->
    <input type="hidden" name="notes" id="notes" value="{{ old('notes', $customer->notes ?? '') }}">
    @error('notes')
    <div class="label">
        <span class="label-text-alt text-error flex items-center gap-1">
            <i class="fa-sharp fa-exclamation-triangle"></i>
            {{ $message }}
        </span>
    </div>
    @enderror
</div>



<script>
    // Initialize Quill Editor
    document.addEventListener('DOMContentLoaded', function() {
        const quill = new Quill('#editor', {
            theme: 'snow',
            placeholder: 'Enter any additional notes about this customer...',
            modules: {
                toolbar: [
                    ['bold', 'italic', 'underline'],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    ['clean']
                ]
            }
        });

        // Set the initial content if the "notes" field already has data
        const notesInput = document.getElementById('notes');
        const initialContent = notesInput.value;
        if (initialContent) {
            quill.root.innerHTML = initialContent;
        }

        // Synchronize Quill content to the hidden input field
        quill.on('text-change', function() {
            notesInput.value = quill.root.innerHTML;
        });

        // Show/Hide Fields based on Customer Type
        const typeFields = document.querySelectorAll('input[name="type"]');
        const nicknameField = document.getElementById('nicknameField');
        const contactField = document.getElementById('contactField');
        const websiteField = document.getElementById('websiteField');
        const nameIcon = document.getElementById('nameIcon');
        const nameLabel = document.getElementById('nameLabel');

        // Function to update fields based on customer type
        function updateFieldsForType(type) {
            if (type === 'Business') {
                nicknameField.classList.add('hidden');
                contactField.classList.remove('hidden');
                websiteField.classList.remove('hidden');
                nameIcon.classList.remove('fa-user');
                nameIcon.classList.add('fa-building');
                nameLabel.textContent = 'Business Name';
            } else {
                nicknameField.classList.remove('hidden');
                contactField.classList.add('hidden');
                websiteField.classList.add('hidden');
                nameIcon.classList.remove('fa-building');
                nameIcon.classList.add('fa-user');
                nameLabel.textContent = 'Customer Name';
            }
        }

        // Set initial state based on checked radio button
        const checkedType = document.querySelector('input[name="type"]:checked');
        if (checkedType) {
            updateFieldsForType(checkedType.value);
        }

        // Add event listeners to radio buttons
        typeFields.forEach((radio) => {
            radio.addEventListener('change', function() {
                updateFieldsForType(this.value);
            });
        });
    });
</script>

@importQuill
