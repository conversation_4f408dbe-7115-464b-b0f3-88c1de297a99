<x-app-layout
    page-title="Customers"
    page-icon="fa-sharp fa-users"
    :primary-buttons="[
        [
            'type' => 'create',
            'route' => route('customers.create'),
            'text' => 'New Customer'
        ]
    ]"
    :action-buttons="[
        [
            'name' => request()->has('trashed') && request()->input('trashed') === 'true' ? 'View Active' : 'View Trashed',
            'route' => request()->has('trashed') && request()->input('trashed') === 'true'
                ? route('customers.index', request()->except(['trashed', 'page']))
                : route('customers.index', array_merge(request()->except(['trashed', 'page']), ['trashed' => 'true'])),
            'icon' => request()->has('trashed') && request()->input('trashed') === 'true' ? 'fa-sharp fa-eye' : 'fa-sharp fa-trash',
            'class' => 'btn ' . (request()->has('trashed') && request()->input('trashed') === 'true' ? 'btn-primary' : 'btn-secondary') . ' btn-sm gap-2'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Customers', 'icon' => 'fa-users']
    ]">

    <div class="py-6 lg:py-8 space-y-8">
        <!-- Page Content -->
        <div class="mx-auto sm:px-2 lg:px-4 space-y-8">
            <!-- Header Card -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-base-content">Customer Management</h1>
                            <p class="text-base-content/70 mt-1 text-lg">Manage all customers and their information in the system.</p>
                        </div>
                        <div class="hidden lg:flex items-center gap-4 text-base-content/60">
                            <div class="stat">
                                <div class="stat-title text-xs">Total Customers</div>
                                <div class="stat-value text-2xl">{{ $customers->total() }}</div>
                            </div>
                            @perms('manage_settings')
                            <div class="dropdown dropdown-end">
                                <div tabindex="0" role="button" class="btn btn-outline btn-sm gap-2">
                                    <i class="fa-sharp fa-download"></i>
                                    Export
                                    <i class="fa-sharp fa-chevron-down text-xs"></i>
                                </div>
                                <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow-lg border border-base-300">
                                    <li>
                                        <a href="{{ route('admin.tools.customer-export.quick-excel') }}" class="gap-2">
                                            <i class="fa-sharp fa-file-excel text-green-600"></i>
                                            Quick Export (Excel)
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('admin.tools.customer-export.quick-csv') }}" class="gap-2">
                                            <i class="fa-sharp fa-file-csv text-blue-600"></i>
                                            Quick Export (CSV)
                                        </a>
                                    </li>
                                    <li><hr class="my-1"></li>
                                    <li>
                                        <a href="{{ route('admin.tools.customer-export.index') }}" class="gap-2">
                                            <i class="fa-sharp fa-cog"></i>
                                            Custom Export
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            @endperms
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search & Filters Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-magnifying-glass text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Search & Filter</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    <!-- Search Section -->
                    <div>
                        <label class="label">
                            <span class="label-text font-medium">Quick Search</span>
                            <span class="label-text-alt">Search by name, email, phone, or nickname</span>
                        </label>
                        <x-dynamic-customer-search
                            id="customerSearch"
                            name="customer_id"
                            placeholder="Search for customers by name, email, phone, or nickname..."
                            action="navigate"
                        />
                    </div>

                    <!-- Filters Section -->
                    
                    <form method="GET" action="{{ route('customers.index') }}" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- Preserve existing query parameters except the ones we're setting -->
                        @foreach(request()->except(['type', 'contract_status', 'page']) as $key => $value)
                            <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                        @endforeach

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">Customer Type</span>
                                <span class="label-text-alt">
                                    @if(request('type'))
                                        <span class="badge badge-primary badge-xs">Filtered</span>
                                    @endif
                                </span>
                            </label>
                            <select name="type" class="select select-bordered w-full" onchange="this.form.submit()">
                                <option value="">All Types</option>
                                <option value="eBay Customer" {{ request('type') === 'eBay Customer' ? 'selected' : '' }}>eBay Customer</option>
                                <option value="Website Customer" {{ request('type') === 'Website Customer' ? 'selected' : '' }}>Website Customer</option>
                                <option value="Facebook Customer" {{ request('type') === 'Facebook Customer' ? 'selected' : '' }}>Facebook Customer</option>
                                <option value="Bulk Buyer" {{ request('type') === 'Bulk Buyer' ? 'selected' : '' }}>Bulk Buyer</option>
                                <option value="Business" {{ request('type') === 'Business' ? 'selected' : '' }}>Business</option>
                            </select>
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">Contract Status</span>
                                <span class="label-text-alt">
                                    @if(request('contract_status'))
                                        <span class="badge badge-primary badge-xs">Filtered</span>
                                    @endif
                                </span>
                            </label>
                            <select name="contract_status" class="select select-bordered w-full" onchange="this.form.submit()">
                                <option value="">All Status</option>
                                <option value="with_contract" {{ request('contract_status') === 'with_contract' ? 'selected' : '' }}>With Contract</option>
                                <option value="without_contract" {{ request('contract_status') === 'without_contract' ? 'selected' : '' }}>Without Contract</option>
                            </select>
                        </div>

                        <div class="form-control lg:flex lg:justify-end lg:items-end">
                            <div class="flex gap-2">
                                @if(request()->hasAny(['type', 'contract_status']))
                                    <a href="{{ route('customers.index', request()->except(['type', 'contract_status', 'page'])) }}" 
                                       class="btn btn-ghost btn-sm gap-2">
                                        <i class="fa-sharp fa-xmark"></i>
                                        Clear Filters
                                    </a>
                                @endif
                                <button type="submit" class="btn btn-primary btn-sm gap-2">
                                    <i class="fa-sharp fa-magnifying-glass"></i>
                                    Apply Filters
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Customer Directory Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-list text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Customer Directory</h4>
                        </div>
                        <div class="flex items-center gap-2 text-sm text-base-content/60">
                            <span>Showing {{ $customers->firstItem() ?? 0 }}-{{ $customers->lastItem() ?? 0 }} of {{ $customers->total() }}</span>
                        </div>
                    </div>
                </div>

                <!-- Sorting Controls -->
                <div class="px-6 py-4 border-b border-base-300/50 bg-base-50">
                    <form method="GET" action="{{ route('customers.index') }}" class="flex flex-wrap items-center gap-4">
                        <!-- Preserve existing query parameters except sort and order -->
                        @foreach(request()->except(['sort', 'order', 'page']) as $key => $value)
                            <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                        @endforeach

                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium text-base-content/70">Sort by:</label>
                            <select name="sort" class="select select-bordered select-sm w-auto min-w-32" onchange="this.form.submit()">
                                <option value="id" {{ $sort === 'id' ? 'selected' : '' }}>ID</option>
                                <option value="name" {{ $sort === 'name' ? 'selected' : '' }}>Name</option>
                                <option value="total_purchases_value" {{ $sort === 'total_purchases_value' ? 'selected' : '' }}>Revenue</option>
                                <option value="created_at" {{ $sort === 'created_at' ? 'selected' : '' }}>Created</option>
                            </select>
                        </div>

                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium text-base-content/70">Order:</label>
                            <select name="order" class="select select-bordered select-sm w-auto" onchange="this.form.submit()">
                                <option value="asc" {{ $order === 'asc' ? 'selected' : '' }}>Ascending</option>
                                <option value="desc" {{ $order === 'desc' ? 'selected' : '' }}>Descending</option>
                            </select>
                        </div>
                    </form>
                </div>
                
                <!-- Customer Cards Section -->
                <div class="divide-y divide-base-200/50">

                    @forelse ($customers as $customer)
                        <!-- Customer Card Row -->
                        <div class="p-3 lg:p-4 hover:bg-base-200/50 transition-all duration-200 {{ $loop->even ? 'bg-base-100' : 'bg-base-200/30' }}">
                            <!-- Mobile Layout -->
                            <div class="lg:hidden">
                                <!-- Customer Name & ID -->
                                <div class="flex items-center gap-2 mb-2">
                                    <div class="font-mono text-xs text-base-content/60">
                                        <a href="{{ route('customers.show', $customer) }}" class="link link-primary font-medium hover:link-hover">
                                            #{{ $customer->id }}
                                        </a>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <a href="{{ route('customers.show', $customer) }}" class="link-primary font-medium hover:link-hover truncate text-base text-lg">
                                            {{ $customer->name }}
                                        </a>
                                        @if($customer->trashed())
                                            <span class="badge badge-error badge-xs ml-2">Deleted</span>
                                        @endif
                                    </div>
                                </div>

                                <!-- Two Column Details -->
                                <div class="grid grid-cols-2 gap-3 mb-3 text-xs">
                                    <!-- Left Column -->
                                    <div class="space-y-1">
                                        <!-- Revenue -->
                                        <div class="flex items-center gap-1">
                                       
                                            <span class="font-semibold">${{ number_format($customer->total_purchases_value, 2) }}</span>
                                        </div>

                                        <!-- Email -->
                                        @if($customer->email)
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-envelope text-primary text-xs"></i>
                                                <a href="mailto:{{ $customer->email }}" class="link link-hover truncate hover:text-primary transition-colors text-xs">
                                                    {{ Str::limit($customer->email, 20) }}
                                                </a>
                                            </div>
                                        @endif

                                        <!-- Customer Type -->
                                        <div class="mt-1">
                                            <span class="badge badge-outline badge-xs">{{ $customer->type }}</span>
                                        </div>
                                    </div>

                                    <!-- Right Column -->
                                    <div class="space-y-1">
                                        <!-- Created Date -->
                                        <div class="text-base-content/60">
                                            {{ $customer->created_at->format('m/d/y') }}
                                        </div>

                                        <!-- Phone -->
                                        @if($customer->phone)
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-phone text-success text-xs"></i>
                                                <a href="tel:{{ $customer->phone }}" class="link link-hover hover:text-success transition-colors text-xs">
                                                    {{ $customer->phone }}
                                                </a>
                                            </div>
                                        @endif

                                        <!-- Contract Status -->
                                        <div class="flex items-center gap-1">
                                            @if($customer->hasActiveContract)
                                                <div class="tooltip" data-tip="Has contract">
                                                    <div class="badge badge-warning badge-xs">
                                                        <i class="fa-sharp fa-solid fa-star"></i>
                                                    </div>
                                                </div>
                                                <span class="text-xs text-base-content/60">Contract</span>
                                            @else
                                                <div class="tooltip" data-tip="No contract">
                                                    <div class="badge badge-ghost badge-xs">
                                                        <i class="fa-sharp fa-regular fa-star"></i>
                                                    </div>
                                                </div>
                                                <span class="text-xs text-base-content/60">No Contract</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Contact Info (if exists) -->
                                @if($customer->nickname || $customer->contact)
                                    <div class="mb-3 text-xs space-y-1">
                                        @if($customer->contact)
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-user text-info text-xs"></i>
                                                <span class="text-base-content/80">{{ $customer->contact }}</span>
                                            </div>
                                        @endif
                                        @if($customer->nickname)
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-user-tag text-info text-xs"></i>
                                                <span class="text-base-content/80">{{ $customer->nickname }}</span>
                                            </div>
                                        @endif
                                    </div>
                                @endif

                                <!-- No Contact Info Warning -->
                                @if(!$customer->email && !$customer->phone && !$customer->contact && !$customer->nickname)
                                    <div class="flex items-center gap-1 mb-3 text-xs">
                                        <i class="fa-sharp fa-circle-exclamation text-error text-xs"></i>
                                        <span class="text-base-content/80">No Contact Info</span>
                                    </div>
                                @endif

                                <!-- Mobile Action Bar -->
                                <div class="flex gap-2 pt-2 border-t border-base-200/50">
                                    @if($customer->trashed())
                                        <form action="{{ route('customers.restore', $customer->id) }}" method="POST" class="flex-1">
                                            @csrf
                                            <button type="submit" class="btn btn-success btn-sm w-full gap-2">
                                                <i class="fa-sharp fa-undo"></i>
                                                Restore
                                            </button>
                                        </form>
                                    @else
                                        <a href="{{ route('customers.show', $customer) }}" class="btn btn-primary btn-sm flex-1 gap-1">
                                            <i class="fa-sharp fa-eye"></i>
                                            View
                                        </a>
                                        <a href="{{ route('customers.edit', $customer) }}" class="btn btn-warning btn-sm flex-1 gap-1">
                                            <i class="fa-sharp fa-pen-to-square"></i>
                                            Edit
                                        </a>
                                        @perms('delete_customer_accounts')
                                        <form action="{{ route('customers.destroy', $customer) }}" method="POST" class="flex-1">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-error btn-sm w-full gap-1"
                                                    onclick="return confirm('Are you sure you want to delete this customer? This action cannot be undone.')">
                                                <i class="fa-sharp fa-trash"></i>
                                                Delete
                                            </button>
                                        </form>
                                        @endperms
                                    @endif
                                </div>
                            </div>

                            <!-- Desktop Layout -->
                            <div class="hidden lg:grid lg:grid-cols-12 gap-4 items-center">
                                <!-- ID & Name Column (Mobile: Full width, Desktop: 3 cols) -->
                                <div class="lg:col-span-3">
                                    <div class="flex items-center gap-3">
                                        <div class="font-mono text-sm text-base-content/60 min-w-8">
                                            <a href="{{ route('customers.show', $customer) }}" class="link link-primary font-medium hover:link-hover">
                                                #{{ $customer->id }}
                                            </a>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <div class="flex flex-col">
                                                <a href="{{ route('customers.show', $customer) }}" class="link-primary font-medium hover:link-hover truncate text-base">
                                                    {{ $customer->name }}
                                                </a>
                                                @if($customer->trashed())
                                                    <span class="badge badge-error badge-xs mt-1 w-fit">Deleted</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Revenue & Date Column (Mobile: Full width, Desktop: 2 cols) -->
                                <div class="lg:col-span-2">
                                    <div class="flex flex-col gap-1">
                                        <div class="flex items-center gap-1">
                                           
                                            <span class="font-semibold text-sm">${{ number_format($customer->total_purchases_value, 2) }}</span>
                                        </div>
                                        <div class="text-xs text-base-content/60">
                                            {{ $customer->created_at->format('m/d/y') }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Details Column (Mobile: Full width, Desktop: 4 cols) -->
                                <div class="lg:col-span-4">
                                    <div class="flex flex-col gap-1 text-xs">
                                        @if($customer->email)
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-envelope text-primary text-xs"></i>
                                                <a href="mailto:{{ $customer->email }}" class="link link-hover truncate hover:text-primary transition-colors">
                                                    {{ $customer->email }}
                                                </a>
                                            </div>
                                        @endif
                                        @if($customer->phone)
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-phone text-success text-xs"></i>
                                                <a href="tel:{{ $customer->phone }}" class="link link-hover hover:text-success transition-colors">
                                                    {{ $customer->phone }}
                                                </a>
                                            </div>
                                        @endif
                                        
                                        @if($customer->contact)
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-user text-info text-xs"></i>
                                                <span class="text-base-content/80 truncate">{{ $customer->contact }}</span>
                                            </div>
                                        @endif
                                        @if($customer->nickname)
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-user-tag text-info text-xs"></i>
                                                <span class="text-base-content/80 truncate">{{ $customer->nickname }}</span>
                                            </div>
                                        @endif
                                        <!-- if none of the above...    -->
                                         @if(!$customer->email && !$customer->phone && !$customer->contact && !$customer->nickname)
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-circle-exclamation text-error text-xs"></i>
                                                <span class="text-base-content/80 truncate">No Contact Info</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                <!-- Type & Contract Column (Mobile: Full width, Desktop: 2 cols) -->
                                <div class="lg:col-span-2">
                                    <div class="flex flex-col gap-2">
                                        <span class="badge badge-outline badge-sm w-fit">{{ $customer->type }}</span>
                                        <div class="flex items-center gap-1">
                                            @if($customer->hasActiveContract)
                                                <div class="tooltip" data-tip="Has contract">
                                                    <div class="badge badge-warning badge-xs">
                                                        <i class="fa-sharp fa-solid fa-star"></i>
                                                    </div>
                                                </div>
                                                <span class="text-xs text-base-content/60">Contract</span>
                                            @else
                                                <div class="tooltip" data-tip="No contract">
                                                    <div class="badge badge-ghost badge-xs">
                                                        <i class="fa-sharp fa-regular fa-star"></i>
                                                    </div>
                                                </div>
                                                <span class="text-xs text-base-content/60">No Contract</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions Column (Mobile: Full width, Desktop: 1 col) -->
                                <div class="lg:col-span-1">
                                    <div class="flex gap-1 justify-center">
                                        @if($customer->trashed())
                                            <div class="tooltip" data-tip="Restore Customer">
                                                <form action="{{ route('customers.restore', $customer->id) }}" method="POST" class="inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-circle btn-ghost text-success hover:bg-success hover:text-success-content transition-all">
                                                        <i class="fa-sharp fa-undo"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        @else
                                            <div class="tooltip" data-tip="View Customer">
                                                <a href="{{ route('customers.show', $customer) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-primary hover:text-primary-content transition-all">
                                                    <i class="fa-sharp fa-eye"></i>
                                                </a>
                                            </div>
                                            <div class="tooltip" data-tip="Edit Customer">
                                                <a href="{{ route('customers.edit', $customer) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-warning hover:text-warning-content transition-all">
                                                    <i class="fa-sharp fa-pen-to-square"></i>
                                                </a>
                                            </div>
                                            @perms('delete_customer_accounts')
                                            <div class="tooltip" data-tip="Delete Customer">
                                                <form action="{{ route('customers.destroy', $customer) }}" method="POST" class="inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-circle btn-ghost text-error hover:bg-error hover:text-error-content transition-all"
                                                            onclick="return confirm('Are you sure you want to delete this customer? This action cannot be undone.')">
                                                        <i class="fa-sharp fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                            @endperms
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <!-- Empty State -->
                        <div class="text-center text-base-content/70 py-12">
                            <div class="flex flex-col items-center gap-4">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                        <i class="fa-sharp fa-users text-2xl"></i>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <h3 class="text-lg font-medium text-base-content/80">No customers found</h3>
                                    <p class="text-sm text-base-content/60 mt-1">
                                        @if(request()->hasAny(['type', 'contract_status', 'search']))
                                            Try adjusting your search or filter criteria
                                        @else
                                            Get started by creating your first customer
                                        @endif
                                    </p>
                                </div>
                                @if(!request()->hasAny(['type', 'contract_status', 'search']))
                                    <a href="{{ route('customers.create') }}" class="btn btn-primary btn-sm gap-2">
                                        <i class="fa-sharp fa-plus"></i>
                                        Add First Customer
                                    </a>
                                @endif
                            </div>
                        </div>
                    @endforelse
                </div>

                <!-- Pagination Section -->
                @if($customers->hasPages())
                    <div class="px-6 py-4 border-t border-base-300/50 bg-base-50">
                        <x-pagination :paginator="$customers" :pagination="$pagination" />
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
