<x-app-layout
    page-title="Customer Details: {{ $customer->name }}"
    page-icon="fa-sharp fa-user"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('customers.index'),
            'text' => 'Back to Customers'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'Edit Customer',
            'route' => route('customers.edit', $customer),
            'icon' => 'fa-sharp fa-edit',
            'class' => 'btn btn-primary btn-sm gap-2',
            'permission' => 'edit_customer_accounts'
        ],
        [
            'name' => 'Delete Customer',
            'route' => route('customers.destroy', $customer),
            'icon' => 'fa-sharp fa-trash',
            'class' => 'btn btn-error btn-sm gap-2',
            'confirm' => 'Are you sure you want to delete this customer? This action cannot be undone.',
            'permission' => 'delete_customer_accounts'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Customers', 'route' => 'customers.index', 'icon' => 'fa-users'],
        ['name' => $customer->name, 'icon' => 'fa-user']
    ]">

    <div class="py-6 lg:py-8">
        <!-- Page Content -->
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Customer Details Section -->
            <div class="card bg-base-100 shadow-md">
                <!-- Header Section -->
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-user text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">{{ $customer->name }}</h4>
                        </div>

                        <!-- Contract Status & Actions -->
                        <div class="flex items-center gap-3">
                            @if(!$hasActiveContract && isset($activeContractDiscountId))
                                <a href="{{ route('customer_discounts.create', ['customer_id' => $customer->id, 'discount_id' => $activeContractDiscountId]) }}"
                                   class="btn btn-xs btn-outline btn-primary">
                                    <i class="fa-sharp fa-file-contract mr-1"></i> Add Contract
                                </a>
                            @endif

                            <div class="tooltip" data-tip="{{ $hasActiveContract ? 'Customer has an active contract' : 'No active contract' }}">
                                @if($hasActiveContract)
                                    <span class="text-warning">
                                        <i class="fa-sharp fa-solid fa-star text-xl"></i>
                                    </span>
                                @else
                                    <span class="text-base-content/30">
                                        <i class="fa-sharp fa-regular fa-star text-xl"></i>
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="p-6 space-y-6">
                    <!-- Customer Information Grid -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        @if($customer->type === 'Business' && $customer->contact)
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user text-primary"></i>
                            <span class="font-medium">Contact:</span>
                            <span>{{ $customer->contact }}</span>
                        </div>
                        @endif

                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user-tag text-primary"></i>
                            <span class="font-medium">Nickname:</span>
                            <span>{{ $customer->nickname ?: 'N/A' }}</span>
                        </div>

                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-envelope text-primary"></i>
                            <span class="font-medium">Email:</span>
                            <span>{{ $customer->email }}</span>
                        </div>

                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-phone text-primary"></i>
                            <span class="font-medium">Phone:</span>
                            <span>{{ $customer->phone ?: 'None provided' }}</span>
                        </div>

                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-tag text-primary"></i>
                            <span class="font-medium">Type:</span>
                            <span class="badge badge-outline badge-sm">{{ $customer->type }}</span>
                        </div>

                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-dollar-sign text-primary"></i>
                            <span class="font-medium">Revenue:</span>
                            <span class="font-semibold text-success">${{ number_format($customer->invoices->sum('final_price'), 2) }}</span>
                        </div>

                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-map-marker-alt text-primary"></i>
                            <span class="font-medium">Address:</span>
                            <span>{{ $customer->address ?: 'N/A' }}</span>
                        </div>

                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-calendar text-primary"></i>
                            <span class="font-medium">Added:</span>
                            <span>{{ $customer->created_at->format('M j, Y') }}</span>
                        </div>
                    </div>

                    <!-- Customer Notes Section -->
                    @if($customer->notes)
                    <div class="divider my-4">
                        <span class="text-base-content/50 font-medium">Customer Notes</span>
                    </div>

                    <div class="bg-base-200/50 rounded-lg p-4">
                        <div class="flex items-start gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-info text-info-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-sticky-note text-sm"></i>
                                </div>
                            </div>
                            <div class="flex-1">
                                <h5 class="font-medium text-base-content mb-2">Notes</h5>
                                <div class="text-base-content/80 leading-relaxed">{!! $customer->notes !!}</div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Photo ID Section -->
            @php
                $photoIds = $customer->files()
                    ->where(function($query) {
                        $query->where('description', 'LIKE', '%photo_id%')
                              ->orWhere('description', 'LIKE', '%Customer ID%')
                              ->orWhere('description', 'LIKE', '%ID%')
                              ->orWhere('metadata->file_type', 'photo_id');
                    })
                    ->orderBy('created_at', 'desc')
                    ->get();
            @endphp

            <div class="card bg-base-100 shadow-md">
                <!-- Header Section -->
                <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-info text-info-content w-8 rounded-lg">
                                <i class="fa-sharp fa-id-card text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Photo IDs on File</h4>
                        @if($photoIds->count() > 0)
                            <span class="badge badge-success ml-2">{{ $photoIds->count() }}</span>
                        @endif
                    </div>
                </div>

                <!-- Content Section -->
                <div class="p-6">
                    @if($photoIds->isEmpty())
                        <div class="text-center text-base-content/70 py-8">
                            <div class="flex flex-col items-center gap-4">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                        <i class="fa-sharp fa-id-card text-2xl"></i>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <h3 class="text-lg font-medium text-base-content/80">No Photo IDs Found</h3>
                                    <p class="text-sm text-base-content/60 mt-1">Photo IDs can be uploaded through the Document Management section below.</p>
                                </div>
                            </div>
                        </div>
                    @else
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        @foreach($photoIds as $photoId)
                            <div class="relative group">
                                @if($photoId->is_image)
                                    <!-- Image thumbnail with lightbox -->
                                    <div class="photo-id-lightbox block relative cursor-pointer"
                                         data-src="{{ route('files.view', $photoId) }}"
                                         data-filename="{{ $photoId->original_filename }}">
                                        <img src="{{ route('files.view', ['file' => $photoId->id, 'width' => 200, 'height' => 150]) }}"
                                             alt="{{ $photoId->original_filename }}"
                                             class="w-full h-24 object-cover rounded-lg border border-base-300 hover:border-primary transition-colors cursor-pointer shadow-sm hover:shadow-md">
                                        <!-- Hover overlay -->
                                        <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-opacity rounded-lg flex items-center justify-center">
                                            <i class="fa-sharp fa-search-plus text-white opacity-0 group-hover:opacity-100 transition-opacity text-lg"></i>
                                        </div>
                                    </div>
                                @else
                                    <!-- PDF or other file type -->
                                    <a href="{{ route('files.view', $photoId) }}" target="_blank" class="block">
                                        <div class="w-full h-24 bg-base-200 rounded-lg border border-base-300 hover:border-primary transition-colors flex flex-col items-center justify-center cursor-pointer shadow-sm hover:shadow-md">
                                            <i class="fa-sharp fa-file-pdf text-error text-2xl mb-1"></i>
                                            <span class="text-xs text-center text-base-content/80">PDF</span>
                                        </div>
                                    </a>
                                @endif

                                <!-- File info overlay -->
                                <div class="absolute bottom-0 left-0 right-0 bg-black/10 text-white text-xs p-2 rounded-b-lg">
                                    <div class="truncate font-medium">{{ $photoId->original_filename }}</div>
                                    <div class="text-white/80">{{ $photoId->created_at->format('M j, Y') }}</div>
                                </div>

                                <!-- Actions dropdown -->
                                <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <div class="dropdown dropdown-end">
                                        <div tabindex="0" role="button" class="btn btn-xs btn-circle bg-black/50 border-none text-white hover:bg-black/70">
                                            <i class="fa-sharp fa-ellipsis-vertical"></i>
                                        </div>
                                        <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-32">
                                            <li><a href="{{ route('files.view', $photoId) }}" target="_blank" class="text-sm"><i class="fa-sharp fa-eye"></i> View</a></li>
                                            <li><a href="{{ route('files.download', $photoId) }}" class="text-sm"><i class="fa-sharp fa-download"></i> Download</a></li>
                                            @if(Auth::id() === $photoId->uploaded_by || Auth::user()->isAdmin())
                                                <li><a onclick="deletePhotoId('{{ $photoId->id }}')" class="text-sm text-error"><i class="fa-sharp fa-trash"></i> Delete</a></li>
                                            @endif
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    @endif
                </div>
            </div>

            <!-- Customer Discounts Section -->
            <div class="card bg-base-100 shadow-md">
                <!-- Header Section -->
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-percent text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Customer Discounts</h4>
                        </div>
                        @perms('create_discounts')
                        <a href="{{ route('customer_discounts.create', ['customer_id' => $customer->id]) }}" class="btn btn-sm btn-primary gap-2">
                            <i class="fa-sharp fa-plus"></i> Add Discount
                        </a>
                        @endperms
                    </div>
                </div>

                <!-- Content Section -->
                <div class="p-6">

                    @php
                        $customerDiscounts = \App\Models\CustomerDiscount::where('customer_id', $customer->id)->get();
                    @endphp

                    @if($customerDiscounts->isEmpty())
                        <div class="text-center text-base-content/70 py-8">
                            <div class="flex flex-col items-center gap-4">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                        <i class="fa-sharp fa-percent text-2xl"></i>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <h3 class="text-lg font-medium text-base-content/80">No Discounts Assigned</h3>
                                    <p class="text-sm text-base-content/60 mt-1">This customer has no assigned discounts.</p>
                                </div>
                                @perms('create_discounts')
                                <a href="{{ route('customer_discounts.create', ['customer_id' => $customer->id]) }}" class="btn btn-primary btn-sm gap-2">
                                    <i class="fa-sharp fa-plus"></i>
                                    Add First Discount
                                </a>
                                @endperms
                            </div>
                        </div>
                    @else
                        <div class="space-y-4">
                            @foreach($customerDiscounts as $customerDiscount)
                                <div class="bg-base-200/30 rounded-lg p-4 hover:bg-base-200/50 transition-all duration-200">
                                    <div class="flex items-start justify-between mb-3">
                                        <div>
                                            <h5 class="font-semibold text-base-content">{{ $customerDiscount->discount->name }}</h5>
                                            <p class="text-sm text-base-content/70">
                                                {{ ucfirst($customerDiscount->discount->type) }}
                                                ({{ $customerDiscount->discount->scope == 'invoice' ? 'Entire Invoice' : 'Line Item' }})
                                            </p>
                                        </div>
                                        <div class="text-right">
                                            @if($customerDiscount->isActive())
                                                <span class="badge badge-success">Active</span>
                                            @else
                                                <span class="badge badge-error">Expired</span>
                                            @endif
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-tag text-secondary"></i>
                                            <span class="font-medium">Amount:</span>
                                            <span class="font-semibold">
                                                @if($customerDiscount->discount->type == 'percent')
                                                    {{ $customerDiscount->discount->amount }}%
                                                @else
                                                    ${{ number_format($customerDiscount->discount->amount, 2) }}
                                                @endif
                                            </span>
                                        </div>

                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-calendar text-secondary"></i>
                                            <span class="font-medium">Period:</span>
                                            <span>
                                                @if($customerDiscount->start_date && $customerDiscount->end_date)
                                                    {{ $customerDiscount->start_date->format('M j') }} - {{ $customerDiscount->end_date->format('M j, Y') }}
                                                @elseif($customerDiscount->start_date)
                                                    From {{ $customerDiscount->start_date->format('M j, Y') }}
                                                @elseif($customerDiscount->end_date)
                                                    Until {{ $customerDiscount->end_date->format('M j, Y') }}
                                                @else
                                                    No restriction
                                                @endif
                                            </span>
                                        </div>

                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-chart-bar text-secondary"></i>
                                            <span class="font-medium">Usage:</span>
                                            <span>
                                                @if($customerDiscount->maximum_uses === null)
                                                    {{ $customerDiscount->usage_count }} used (Unlimited)
                                                @else
                                                    {{ $customerDiscount->usage_count }}/{{ $customerDiscount->maximum_uses }}
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>

            <!-- Certificates of Destruction Section -->
            <div class="card bg-base-100 shadow-md">
                <!-- Header Section -->
                <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-accent text-accent-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-certificate text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Certificates of Destruction</h4>
                        </div>
                        <a href="{{ route('certificates.create') }}" class="btn btn-sm btn-primary gap-2">
                            <i class="fa-sharp fa-plus"></i> New Certificate
                        </a>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="p-6">
                    @if($customer->certificates->isEmpty())
                        <div class="text-center text-base-content/70 py-8">
                            <div class="flex flex-col items-center gap-4">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                        <i class="fa-sharp fa-certificate text-2xl"></i>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <h3 class="text-lg font-medium text-base-content/80">No Certificates Found</h3>
                                    <p class="text-sm text-base-content/60 mt-1">This customer has no certificates of destruction.</p>
                                </div>
                                <a href="{{ route('certificates.create') }}" class="btn btn-primary btn-sm gap-2">
                                    <i class="fa-sharp fa-plus"></i>
                                    Create First Certificate
                                </a>
                            </div>
                        </div>
                    @else
                        <div class="space-y-4">
                            @foreach($customer->certificates->sortByDesc('created_at') as $certificate)
                                <div class="bg-base-200/30 rounded-lg p-4 hover:bg-base-200/50 transition-all duration-200">
                                    <div class="flex items-start justify-between mb-3">
                                        <div>
                                            <a href="{{ route('certificates.show', $certificate) }}" class="link link-primary font-semibold text-lg hover:link-hover">
                                                {{ $certificate->certificate_number }}
                                            </a>
                                            <div class="mt-1">
                                                @if($certificate->status === 'pending')
                                                    <span class="badge badge-primary">Pending</span>
                                                @elseif($certificate->status === 'verifying')
                                                    <span class="badge badge-warning">Verifying</span>
                                                @elseif($certificate->status === 'completed')
                                                    <span class="badge badge-success">Complete</span>
                                                @else
                                                    <span class="badge badge-neutral">{{ $certificate->status }}</span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="flex gap-2">
                                            <div class="tooltip" data-tip="View Certificate">
                                                <a href="{{ route('certificates.show', $certificate) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-primary hover:text-primary-content transition-all">
                                                    <i class="fa-sharp fa-eye"></i>
                                                </a>
                                            </div>
                                            <div class="tooltip" data-tip="Edit Certificate">
                                                <a href="{{ route('certificates.edit', $certificate) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-primary hover:text-primary-content transition-all">
                                                    <i class="fa-sharp fa-pen-to-square"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-hard-drive text-accent"></i>
                                            <span class="font-medium">Devices:</span>
                                            <span class="font-semibold">{{ $certificate->devices->count() }}</span>
                                        </div>

                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-calendar-clock text-accent"></i>
                                            <span class="font-medium">Scheduled:</span>
                                            <span>{{ $certificate->scheduled_destruction_date ? $certificate->scheduled_destruction_date->format('M j, Y') : 'Not scheduled' }}</span>
                                        </div>

                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-calendar-check text-accent"></i>
                                            <span class="font-medium">Completed:</span>
                                            <span>{{ $certificate->actual_destruction_date ? $certificate->actual_destruction_date->format('M j, Y') : 'Not completed' }}</span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>

            <!-- Document Management Component -->
            <x-document-manager :model="$customer" modelType="customer" />

            <!-- Activity Log Widget -->
            <x-activity-log-widget :model="$customer" />

            <!-- Form Submissions Section -->
            @php
                $formSubmissions = \App\Models\FormSubmission::where('customer_id', $customer->id)
                    ->with(['form'])
                    ->orderBy('created_at', 'desc')
                    ->get();
                $approvedSubmissions = $formSubmissions->filter(function($submission) {
                    return $submission->isApproved();
                });
            @endphp
            
            @if($formSubmissions->isNotEmpty())
                <div class="card bg-base-100 shadow-md">
                    <!-- Header Section -->
                    <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-info text-info-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-file-lines text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Form Submissions</h4>
                                @if($approvedSubmissions->isNotEmpty())
                                    <span class="badge badge-success badge-sm">{{ $approvedSubmissions->count() }} Approved</span>
                                @endif
                            </div>
                            @perms('view_form_submissions')
                                <a href="{{ route('form-submissions.index', ['customer_id' => $customer->id]) }}" class="btn btn-sm btn-outline btn-info gap-2">
                                    <i class="fa-sharp fa-list"></i> View All
                                </a>
                            @endperms
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="p-6">
                        <div class="space-y-4">
                            @foreach($formSubmissions->take(5) as $submission)
                                <div class="bg-base-200/30 rounded-lg p-4 hover:bg-base-200/50 transition-all duration-200">
                                    <div class="flex items-start justify-between mb-2">
                                        <div>
                                            <a href="{{ route('form-submissions.show', $submission) }}" class="link link-primary font-semibold hover:link-hover">
                                                {{ $submission->form->name }}
                                            </a>
                                            <div class="flex items-center gap-3 mt-1">
                                                <span class="badge {{ $submission->getStatusBadgeClass() }} badge-sm">
                                                    {{ \App\Models\FormSubmission::STATUSES[$submission->status] }}
                                                </span>
                                                @if($submission->form->requires_customer_link)
                                                    <span class="badge badge-accent badge-outline badge-sm">
                                                        <i class="fa-sharp fa-user-tie mr-1"></i>
                                                        Customer Linked
                                                    </span>
                                                @endif
                                                @if($submission->form->requires_approval)
                                                    <span class="badge badge-secondary badge-outline badge-sm">
                                                        <i class="fa-sharp fa-check-circle mr-1"></i>
                                                        Requires Approval
                                                    </span>
                                                @endif
                                                @if($submission->signed_at)
                                                    <span class="badge badge-warning badge-outline badge-sm">
                                                        <i class="fa-sharp fa-signature mr-1"></i>
                                                        Signed
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="flex gap-2">
                                            <div class="tooltip" data-tip="View Submission">
                                                <a href="{{ route('form-submissions.show', $submission) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-info hover:text-info-content transition-all">
                                                    <i class="fa-sharp fa-eye"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm mt-3">
                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-calendar text-info"></i>
                                            <span class="font-medium">Submitted:</span>
                                            <span>{{ $submission->created_at->format('M j, Y g:i A') }}</span>
                                        </div>
                                        @if($submission->approved_at)
                                            <div class="flex items-center gap-2">
                                                <i class="fa-sharp fa-check-circle text-success"></i>
                                                <span class="font-medium">Approved:</span>
                                                <span>{{ $submission->approved_at->format('M j, Y') }}</span>
                                            </div>
                                        @endif
                                    </div>

                                    @if($submission->form->applies_discount_on_approval && $submission->isApproved() && $submission->form->discount)
                                        <div class="mt-3 p-3 bg-success/10 rounded-lg border border-success/20">
                                            <div class="flex items-center gap-2 text-sm">
                                                <i class="fa-sharp fa-circle-check text-success"></i>
                                                <span class="font-medium text-success">Discount Applied</span>
                                                <span class="text-base-content/70">- {{ $submission->form->discount->name }}</span>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @endforeach

                            @if($formSubmissions->count() > 5)
                                <div class="text-center mt-4">
                                    <a href="{{ route('form-submissions.index', ['customer_id' => $customer->id]) }}" class="link link-info text-sm">
                                        View all {{ $formSubmissions->count() }} submissions →
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif

            <!-- Invoice History Section -->
            <div class="card bg-base-100 shadow-md">
                <!-- Header Section -->
                <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-file-invoice text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Invoice History</h4>
                        </div>
                        <a href="{{ route('invoices.create', ['customer_id' => $customer->id]) }}" class="btn btn-sm btn-primary gap-2">
                            <i class="fa-sharp fa-plus"></i> New Invoice
                        </a>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="p-6">
                    @if($customer->invoices->isEmpty())
                        <div class="text-center text-base-content/70 py-8">
                            <div class="flex flex-col items-center gap-4">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                        <i class="fa-sharp fa-file-invoice text-2xl"></i>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <h3 class="text-lg font-medium text-base-content/80">No Invoices Found</h3>
                                    <p class="text-sm text-base-content/60 mt-1">This customer has no invoices.</p>
                                </div>
                                <a href="{{ route('invoices.create', ['customer_id' => $customer->id]) }}" class="btn btn-primary btn-sm gap-2">
                                    <i class="fa-sharp fa-plus"></i>
                                    Create First Invoice
                                </a>
                            </div>
                        </div>
                    @else
                        @php
                            // Get the 10 most recent invoices
                            $recentInvoices = $customer->invoices->sortByDesc('invoice_date')->take(10);
                            $totalInvoices = $customer->invoices->count();
                        @endphp

                        <div class="space-y-4">
                            @foreach($recentInvoices as $invoice)
                                <div class="bg-base-200/30 rounded-lg p-4 hover:bg-base-200/50 transition-all duration-200">
                                    <div class="flex items-start justify-between mb-3">
                                        <div>
                                            <a href="{{ route('invoices.edit', $invoice) }}" class="link link-primary font-semibold text-lg hover:link-hover">
                                                Invoice #{{ $invoice->id }}
                                            </a>
                                            <p class="text-sm text-base-content/70 mt-1">
                                                {{ $invoice->invoice_date->format('M j, Y') }}
                                            </p>
                                        </div>
                                        <div class="text-right">
                                            <span class="badge badge-outline badge-sm">{{ $invoice->status }}</span>
                                            <div class="tooltip" data-tip="View Invoice">
                                                <a href="{{ route('invoices.edit', $invoice) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-primary hover:text-primary-content transition-all ml-2">
                                                    <i class="fa-sharp fa-eye"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-calculator text-success"></i>
                                            <span class="font-medium">Subtotal:</span>
                                            <span class="font-semibold">${{ number_format($invoice->total_price, 2) }}</span>
                                        </div>

                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-receipt text-success"></i>
                                            <span class="font-medium">Tax:</span>
                                            <span>${{ number_format($invoice->total_tax, 2) }}</span>
                                        </div>

                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-dollar-sign text-success"></i>
                                            <span class="font-medium">Total:</span>
                                            <span class="font-bold text-success">${{ number_format($invoice->final_price, 2) }}</span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        @if($totalInvoices > 10)
                            <div class="divider my-6">
                                <span class="text-base-content/50 font-medium">Showing 10 of {{ $totalInvoices }} invoices</span>
                            </div>
                            <div class="text-center">
                                <a href="{{ route('invoices.index', ['customer_id' => $customer->id]) }}" class="btn btn-outline btn-lg gap-2">
                                    <i class="fa-sharp fa-list"></i>
                                    View All Invoices
                                </a>
                            </div>
                        @endif
                    @endif
                </div>
            </div>

        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('[DEBUG] Customer show page loaded');
            console.log('[DEBUG] Current URL:', window.location.href);
            console.log('[DEBUG] Referrer:', document.referrer);
            
            // Initialize custom lightbox for photo IDs
            initializePhotoIdLightbox();
        });

        // Function to initialize custom lightbox
        function initializePhotoIdLightbox() {
            const lightboxElements = document.querySelectorAll('.photo-id-lightbox');
            console.log('Initializing custom lightbox for', lightboxElements.length, 'photo IDs');

            if (lightboxElements.length > 0) {
                lightboxElements.forEach(element => {
                    element.addEventListener('click', function() {
                        const imageUrl = this.getAttribute('data-src');
                        const filename = this.getAttribute('data-filename');

                        console.log('Opening lightbox for:', filename, imageUrl);

                        // Create a simple lightbox overlay
                        createCustomLightbox(imageUrl, filename);
                    });
                });

                console.log('Custom lightbox handlers added successfully');
            } else {
                console.log('No lightbox elements found');
            }
        }

        // Function to create a simple custom lightbox
        function createCustomLightbox(imageUrl, filename) {
            // Remove any existing lightbox
            const existingLightbox = document.getElementById('custom-lightbox');
            if (existingLightbox) {
                existingLightbox.remove();
            }

            // Create lightbox overlay
            const lightboxOverlay = document.createElement('div');
            lightboxOverlay.id = 'custom-lightbox';
            lightboxOverlay.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4';
            lightboxOverlay.style.zIndex = '9999';

            // Create lightbox content
            lightboxOverlay.innerHTML = `
                <div class="relative max-w-4xl max-h-full">
                    <button class="absolute -top-10 right-0 text-white text-2xl hover:text-gray-300 z-10" onclick="this.closest('#custom-lightbox').remove()">
                        <i class="fa-sharp fa-times"></i>
                    </button>
                    <img src="${imageUrl}" alt="${filename}" class="max-w-full max-h-full object-contain rounded-lg shadow-lg">
                    <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-center p-2 rounded-b-lg">
                        ${filename}
                    </div>
                </div>
            `;

            // Close on overlay click
            lightboxOverlay.addEventListener('click', function(e) {
                if (e.target === lightboxOverlay) {
                    lightboxOverlay.remove();
                }
            });

            // Close on escape key
            const escapeHandler = function(e) {
                if (e.key === 'Escape') {
                    lightboxOverlay.remove();
                    document.removeEventListener('keydown', escapeHandler);
                }
            };
            document.addEventListener('keydown', escapeHandler);

            // Add to page
            document.body.appendChild(lightboxOverlay);
        }

        // Function to delete photo ID
        function deletePhotoId(fileId) {
            console.log('[DEBUG] deletePhotoId called for file:', fileId);
            
            if (!confirm('Are you sure you want to delete this photo ID? This action cannot be undone.')) {
                console.log('[DEBUG] User cancelled photo ID deletion');
                return;
            }

            console.log('[DEBUG] User confirmed photo ID deletion');
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            console.log('[DEBUG] Sending DELETE request for file:', fileId);
            fetch(`/files/${fileId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                cache: 'no-cache',
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('[DEBUG] File delete response received:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('[DEBUG] File delete response data:', data);
                if (data.success) {
                    console.log('[DEBUG] File deleted successfully, reloading page...');
                    // Reload the page to update the photo ID section
                    window.location.reload();
                } else {
                    console.log('[DEBUG] File delete failed:', data.message);
                    alert('Error deleting photo ID: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('[DEBUG] Error in file deletion:', error);
                alert('Error deleting photo ID. Please try again.');
            });
        }
    </script>
    @endpush
</x-app-layout>
