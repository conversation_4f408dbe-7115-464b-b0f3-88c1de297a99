<x-app-layout
    page-title="Edit Customer: {{ $customer->name }}"
    page-icon="fa-sharp fa-user-edit"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('customers.index'),
            'text' => 'Back to Customers'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'View Customer',
            'route' => route('customers.show', $customer),
            'icon' => 'fa-sharp fa-eye',
            'class' => 'btn btn-info btn-sm gap-2'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Customers', 'route' => 'customers.index', 'icon' => 'fa-users'],
        ['name' => 'Edit: ' . $customer->name, 'icon' => 'fa-user-edit']
    ]">

    @importQuill

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Auto-Save Status Alert (Hidden by default, shown only for errors) -->
            <div id="saveStatus" class="hidden">
                <div class="alert alert-error">
                    <i class="fa-sharp fa-circle-xmark mr-2"></i>
                    <span id="saveStatusText">Error saving changes</span>
                </div>
            </div>

            <form action="{{ route('customers.update', $customer->id) }}" method="POST" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- 2x2 Grid Layout for form fields -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Customer Identity Section (spans 2 rows in first column) -->
                    <div class="lg:row-span-2">
                        <div class="card bg-base-100 shadow-md h-full">
                            <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <div class="avatar avatar-placeholder">
                                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                                <i class="fa-sharp fa-user text-sm"></i>
                                            </div>
                                        </div>
                                        <h4 class="text-lg font-semibold text-base-content">Customer Identity</h4>
                                    </div>
                                    <div class="card-saving-indicator hidden">
                                        <i class="fa-sharp fa-spinner fa-spin text-primary"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6 space-y-6">

                                <!-- Name Field -->
                                <div>
                                    <label class="label" for="name">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-user text-primary mr-2"></i>
                                            Customer Name <span class="text-error font-bold">*</span>
                                        </span>
                                    </label>
                                    <div class="relative">
                                        <input type="text" name="name" id="name" value="{{ old('name', $customer->name) }}"
                                            class="input input-bordered w-full auto-save" data-field="name"
                                            placeholder="Enter customer name..." required>
                                        <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                            <i class="fa-sharp fa-spinner fa-spin text-primary"></i>
                                        </span>
                                    </div>
                                    @error('name')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>

                                <!-- Type Field -->
                                <div>
                                    <label class="label">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-users text-secondary mr-2"></i>
                                            Customer Type <span class="text-error font-bold">*</span>
                                        </span>
                                    </label>
                                    <div class="space-y-3 relative">
                                        <!-- Residential Customer -->
                                        <div class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                                            <label class="flex items-start cursor-pointer p-4">
                                                <input type="radio" name="type" value="Residential Customer"
                                                    {{ old('type', $customer->type) === 'Residential Customer' ? 'checked' : '' }}
                                                    class="radio radio-primary mt-1 mr-3 flex-shrink-0 auto-save-radio" data-field="type" required>
                                                <div class="flex-1 min-w-0">
                                                    <div class="flex items-center gap-2 mb-1">
                                                        <i class="fa-sharp fa-home text-primary"></i>
                                                        <span class="text-base font-medium text-base-content">Residential Customer</span>
                                                    </div>
                                                    <p class="text-sm text-base-content/70">Individual customers for personal use</p>
                                                </div>
                                            </label>
                                        </div>

                                        <!-- Online Customer -->
                                        <div class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                                            <label class="flex items-start cursor-pointer p-4">
                                                <input type="radio" name="type" value="Online Customer"
                                                    {{ old('type', $customer->type) === 'Online Customer' ? 'checked' : '' }}
                                                    class="radio radio-primary mt-1 mr-3 flex-shrink-0 auto-save-radio" data-field="type" required>
                                                <div class="flex-1 min-w-0">
                                                    <div class="flex items-center gap-2 mb-1">
                                                        <i class="fa-sharp fa-globe text-primary"></i>
                                                        <span class="text-base font-medium text-base-content">Online Customer</span>
                                                    </div>
                                                    <p class="text-sm text-base-content/70">Customers who purchase through online platforms</p>
                                                </div>
                                            </label>
                                        </div>

                                        <!-- Bulk Buyer -->
                                        <div class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                                            <label class="flex items-start cursor-pointer p-4">
                                                <input type="radio" name="type" value="Bulk Buyer"
                                                    {{ old('type', $customer->type) === 'Bulk Buyer' ? 'checked' : '' }}
                                                    class="radio radio-primary mt-1 mr-3 flex-shrink-0 auto-save-radio" data-field="type" required>
                                                <div class="flex-1 min-w-0">
                                                    <div class="flex items-center gap-2 mb-1">
                                                        <i class="fa-sharp fa-boxes text-primary"></i>
                                                        <span class="text-base font-medium text-base-content">Bulk Buyer</span>
                                                    </div>
                                                    <p class="text-sm text-base-content/70">Customers who purchase large quantities</p>
                                                </div>
                                            </label>
                                        </div>

                                        <!-- Business -->
                                        <div class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
                                            <label class="flex items-start cursor-pointer p-4">
                                                <input type="radio" name="type" value="Business"
                                                    {{ old('type', $customer->type) === 'Business' ? 'checked' : '' }}
                                                    class="radio radio-primary mt-1 mr-3 flex-shrink-0 auto-save-radio" data-field="type" required>
                                                <div class="flex-1 min-w-0">
                                                    <div class="flex items-center gap-2 mb-1">
                                                        <i class="fa-sharp fa-building text-primary"></i>
                                                        <span class="text-base font-medium text-base-content">Business</span>
                                                    </div>
                                                    <p class="text-sm text-base-content/70">Corporate customers and business entities</p>
                                                </div>
                                            </label>
                                        </div>

                                        <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                            <i class="fa-sharp fa-spinner fa-spin text-primary"></i>
                                        </span>
                                    </div>
                                    @error('type')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>

                                <!-- Primary Contact Field For Businesses -->
                                <div class="{{ old('type', $customer->type) === 'Business' ? '' : 'hidden' }}" id="contactField">
                                    <label class="label" for="contact">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-user text-accent mr-2"></i>
                                            Primary Business Contact
                                        </span>
                                    </label>
                                    <div class="relative">
                                        <input type="text" name="contact" id="contact" value="{{ old('contact', $customer->contact) }}"
                                            class="input input-bordered w-full auto-save" data-field="contact"
                                            placeholder="Enter primary contact name...">
                                        <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                            <i class="fa-sharp fa-spinner fa-spin text-primary"></i>
                                        </span>
                                    </div>
                                    @error('contact')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>

                                <!-- Nickname Field -->
                                <div class="{{ old('type', $customer->type) === 'Business' ? 'hidden' : '' }}" id="nicknameField">
                                    <label class="label" for="nickname">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-user-tag text-accent mr-2"></i>
                                            Nickname / Username
                                        </span>
                                    </label>
                                    <div class="relative">
                                        <input type="text" name="nickname" id="nickname" value="{{ old('nickname', $customer->nickname) }}"
                                            class="input input-bordered w-full auto-save" data-field="nickname"
                                            placeholder="Enter nickname or username...">
                                        <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                            <i class="fa-sharp fa-spinner fa-spin text-primary"></i>
                                        </span>
                                    </div>
                                    @error('nickname')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information Section (col 2, row 1) -->
                    <div>
                        <div class="card bg-base-100 shadow-md h-full">
                            <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <div class="avatar avatar-placeholder">
                                            <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                                <i class="fa-sharp fa-address-book text-sm"></i>
                                            </div>
                                        </div>
                                        <h4 class="text-lg font-semibold text-base-content">Contact Information</h4>
                                    </div>
                                    <div class="card-saving-indicator hidden">
                                        <i class="fa-sharp fa-spinner fa-spin text-secondary"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6 space-y-6">

                                <!-- Email Field -->
                                <div>
                                    <label class="label" for="email">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-envelope text-secondary mr-2"></i>
                                            Email Address
                                        </span>
                                    </label>
                                    <div class="relative">
                                        <input type="email" name="email" id="email" value="{{ old('email', $customer->email) }}"
                                            class="input input-bordered w-full auto-save" data-field="email"
                                            placeholder="Enter email address...">
                                        <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                            <i class="fa-sharp fa-spinner fa-spin text-primary"></i>
                                        </span>
                                    </div>
                                    @error('email')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>

                                <!-- Phone Field -->
                                <div>
                                    <label class="label" for="phone">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-phone text-secondary mr-2"></i>
                                            Phone Number
                                        </span>
                                    </label>
                                    <div class="relative">
                                        <input type="text" name="phone" id="phone"
                                            value="{{ old('phone', $customer->phone) }}"
                                            class="input input-bordered w-full auto-save"
                                            data-field="phone"
                                            placeholder="Enter phone number (any format)">
                                        <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                            <i class="fa-sharp fa-spinner fa-spin text-primary"></i>
                                        </span>
                                    </div>
                                    @error('phone')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>

                                <!-- Website field -->
                                <div>
                                    <label class="label" for="website">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-globe text-secondary mr-2"></i>
                                            Website
                                        </span>
                                    </label>
                                    <div class="relative">
                                        <input type="text" name="website" id="website" value="{{ old('website', $customer->website) }}"
                                            class="input input-bordered w-full auto-save" data-field="website"
                                            placeholder="Enter website URL...">
                                        <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                            <i class="fa-sharp fa-spinner fa-spin text-primary"></i>
                                        </span>
                                    </div>
                                    @error('website')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Address Section (col 2, row 2) -->
                    <div>
                        <div class="card bg-base-100 shadow-md h-full">
                            <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <div class="avatar avatar-placeholder">
                                            <div class="bg-info text-info-content w-8 rounded-lg">
                                                <i class="fa-sharp fa-map-marker-alt text-sm"></i>
                                            </div>
                                        </div>
                                        <h4 class="text-lg font-semibold text-base-content">Address Information</h4>
                                    </div>
                                    <div class="card-saving-indicator hidden">
                                        <i class="fa-sharp fa-spinner fa-spin text-info"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">

                                <!-- Address Field with Google Maps Autocomplete -->
                                <div>
                                    <x-google-maps-autocomplete
                                        id="address"
                                        name="address"
                                        :value="old('address', $customer->address ?? '')"
                                        placeholder="Start typing to search for an address or business name..."
                                        type="textarea"
                                        label="Full Address"
                                        icon="fa-map-marker-alt"
                                        iconColor="text-info"
                                        :restrictions="['us', 'ca']"
                                        :showDisplayMode="true"
                                        displayLabel="Customer Address"
                                        changeButtonText="Change"
                                        :autoSave="true"
                                        autoSaveField="address"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes Section (spans both columns in row 3) -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-accent text-accent-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-sticky-note text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Additional Notes</h4>
                            </div>
                            <div class="card-saving-indicator hidden">
                                <i class="fa-sharp fa-spinner fa-spin text-accent"></i>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">

                        <!-- Notes Field -->
                        <div>
                            <label class="label" for="notes">
                                <span class="label-text">
                                    <i class="fa-sharp fa-sticky-note text-accent mr-2"></i>
                                    Additional Information
                                </span>
                            </label>

                            <!-- Quill Editor Container -->
                            <div class="relative">
                                <div id="editor" class="w-full h-48 rounded"></div>
                                <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                    <i class="fa-sharp fa-spinner fa-spin text-primary"></i>
                                </span>
                            </div>

                            <!-- Hidden Input Field to Store Quill Content -->
                            <input type="hidden" name="notes" id="notes" value="{{ old('notes', $customer->notes) }}">

                            @error('notes')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Auto-Save Status & Manual Save Section -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-save text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Save Changes</h4>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-300 text-base-content w-10 rounded-lg">
                                        <i class="fa-sharp fa-info-circle text-sm"></i>
                                    </div>
                                </div>
                                <div class="text-center sm:text-left">
                                    <p id="saveStatusIndicator" class="font-medium text-base-content">Changes will save automatically</p>
                                    <p class="text-sm text-base-content/60">Your changes are saved as you type for accessibility</p>
                                </div>
                            </div>
                            <div class="flex gap-3 w-full sm:w-auto">
                                <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                                    <i class="fa-sharp fa-save"></i>
                                    Save All Changes
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Document Management Component -->
            <x-document-manager :model="$customer" modelType="customer" />
        </div>
    </div>

    <script>
        // Initialize Quill Editor and other functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Quill Editor
            const quill = new Quill('#editor', {
                theme: 'snow',
            });

            // Set the initial content if the "notes" field already has data
            const initialContent = document.getElementById('notes').value;
            quill.root.innerHTML = initialContent;

            // Synchronize Quill content to the hidden input field
            const notesInput = document.getElementById('notes');
            quill.on('text-change', function() {
                notesInput.value = quill.root.innerHTML;
            });

            // Only show primary contact field if business type is selected
            const typeFields = document.querySelectorAll('input[name="type"]');
            const contactField = document.getElementById('contactField');
            const nicknameField = document.getElementById('nicknameField');

            typeFields.forEach(function(typeField) {
                typeField.addEventListener('change', function() {
                    console.log('something changed');
                    if (this.value === 'Business') {
                        contactField.classList.remove('hidden');
                        nicknameField.classList.add('hidden');
                    } else {
                        contactField.classList.add('hidden');
                        nicknameField.classList.remove('hidden');
                    }
                });
            });



            // Auto-save functionality
            const autoSaveInputs = document.querySelectorAll('.auto-save');
            const autoSaveRadios = document.querySelectorAll('.auto-save-radio');
            const saveStatus = document.getElementById('saveStatus'); // For error messages only
            const saveStatusText = document.getElementById('saveStatusText');
            const saveStatusIndicator = document.getElementById('saveStatusIndicator');
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            const customerId = '{{ $customer->id }}';
            let saveTimeout;
            let statusResetTimeout;

            // Function to show the save status
            function showSaveStatus(message, isSuccess = true) {
                // Clear any existing timeout for status reset
                clearTimeout(statusResetTimeout);

                if (isSuccess) {
                    // Update the text above the save button
                    saveStatusIndicator.textContent = message;
                    saveStatusIndicator.classList.remove('text-base-content', 'text-error', 'text-info');
                    saveStatusIndicator.classList.add('text-success');

                    // Reset to default message after 3 seconds
                    statusResetTimeout = setTimeout(() => {
                        saveStatusIndicator.textContent = 'Changes will save automatically';
                        saveStatusIndicator.classList.remove('text-success', 'text-error', 'text-info');
                        saveStatusIndicator.classList.add('text-base-content');
                    }, 3000);
                } else {
                    // For errors, show the error alert at the top
                    saveStatus.classList.remove('hidden');
                    saveStatusText.textContent = message;

                    // Also update the text above the save button
                    saveStatusIndicator.textContent = 'Error saving changes';
                    saveStatusIndicator.classList.remove('text-base-content', 'text-success', 'text-info');
                    saveStatusIndicator.classList.add('text-error');

                    // Hide the error alert after 5 seconds
                    setTimeout(() => {
                        saveStatus.classList.add('hidden');
                    }, 5000);

                    // Reset to default message after 5 seconds
                    statusResetTimeout = setTimeout(() => {
                        saveStatusIndicator.textContent = 'Changes will save automatically';
                        saveStatusIndicator.classList.remove('text-success', 'text-error', 'text-info');
                        saveStatusIndicator.classList.add('text-base-content');
                    }, 5000);
                }
            }

            // Function to get the card saving indicator for a field
            function getCardIndicator(fieldElement) {
                const card = fieldElement.closest('.card');
                return card ? card.querySelector('.card-saving-indicator') : null;
            }



            // Function to save a field
            function saveField(field, value, indicator) {
                let valueToLogAndSend = value;
                if (typeof value === 'string' && value.toLowerCase() === 'undefined') {
                    console.warn(`[Save Field] Original value for field "${field}" was the string "undefined". Sanitizing to empty string for save.`);
                    valueToLogAndSend = '';
                }

                console.log(`[Save Field] Attempting to save field: "${field}" with value:`, valueToLogAndSend);
                const route = `{{ route('customers.autoSave', $customer) }}`;
                console.log(`[Save Field] Target route:`, route);
                const requestBody = {
                    field: field,
                    value: valueToLogAndSend // Use the potentially sanitized value
                };
                console.log(`[Save Field] Request body:`, JSON.stringify(requestBody));

                // Show saving indicator next to field (if indicator exists)
                if (indicator) {
                    indicator.classList.remove('hidden');
                }

                // Show card-level saving indicator
                const cardIndicator = indicator ? getCardIndicator(indicator) : null;
                if (cardIndicator) {
                    cardIndicator.classList.remove('hidden');
                }

                // Update status text to "Saving Changes..."
                saveStatusIndicator.textContent = 'Saving Changes...';
                saveStatusIndicator.classList.remove('text-base-content', 'text-success', 'text-error');
                saveStatusIndicator.classList.add('text-info');

                fetch(`{{ route('customers.autoSave', $customer) }}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'X-Auto-Save': 'true'
                    },
                    body: JSON.stringify(requestBody) // This now sends the sanitized value
                })
                .then(response => response.json())
                .then(data => {
                    // Hide the field indicator (if it exists)
                    if (indicator) {
                        indicator.classList.add('hidden');
                    }

                    // Hide card-level saving indicator
                    const cardIndicator = indicator ? getCardIndicator(indicator) : null;
                    if (cardIndicator) {
                        cardIndicator.classList.add('hidden');
                    }

                    if (data.success) {
                        // Show success message
                        showSaveStatus('All changes saved.');
                    } else {
                        // Show error message
                        showSaveStatus(data.message || 'Error saving changes', false);
                    }
                })
                .catch(error => {
                    // Hide the field indicator (if it exists)
                    if (indicator) {
                        indicator.classList.add('hidden');
                    }

                    // Hide card-level saving indicator
                    const cardIndicator = indicator ? getCardIndicator(indicator) : null;
                    if (cardIndicator) {
                        cardIndicator.classList.add('hidden');
                    }

                    // Show error message
                    showSaveStatus('Error saving changes: ' + error.message, false);
                });
            }

            // Add event listeners to text inputs and textareas
            autoSaveInputs.forEach(input => {
                input.addEventListener('input', function() {
                    const field = this.dataset.field;
                    const value = this.value;
                    const indicator = this.parentNode.querySelector('.saving-indicator');

                    // Clear any existing timeout
                    clearTimeout(saveTimeout);

                    // Set a new timeout to save after 500ms of inactivity
                    saveTimeout = setTimeout(() => {
                        saveField(field, value, indicator);
                    }, 500);
                });
            });

            // Add event listeners to radio buttons
            autoSaveRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    const field = this.dataset.field;
                    const value = this.value;
                    const indicator = this.closest('.relative').querySelector('.saving-indicator');

                    // Save immediately for radio buttons
                    saveField(field, value, indicator);
                });
            });

            // Handle Quill editor for notes auto-save
            const indicator = document.querySelector('#editor').closest('.relative').querySelector('.saving-indicator');
            let quillSaveTimeout;

            // Add auto-save to Quill editor
            quill.on('text-change', function() {
                // Clear any existing timeout
                clearTimeout(quillSaveTimeout);

                // Set a new timeout to save after 1 second of inactivity
                quillSaveTimeout = setTimeout(() => {
                    const field = 'notes';
                    const value = notesInput.value;

                    if (indicator) {
                        saveField(field, value, indicator);
                    }
                }, 1000);
            });

            // Listen for address selection from the Google Maps component
            document.addEventListener('addressSelected', function(event) {
                const formattedAddress = event.detail.formattedAddress;
                const input = event.detail.input;

                // Check if this is the address field
                if (input && input.name === 'address' && formattedAddress) {
                    // Find the saving indicator for the address field
                    const addressContainer = input.closest('[id*="address"]');
                    const indicator = addressContainer ? addressContainer.querySelector('.saving-indicator') : null;

                    // Trigger the existing saveField function
                    saveField('address', formattedAddress, indicator);
                }
            });


        });
    </script>



</x-app-layout>