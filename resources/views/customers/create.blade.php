<x-app-layout
    page-title="Add New Customer"
    page-icon="fa-sharp fa-user-plus"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('customers.index'),
            'text' => 'Back to Customers'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Customers', 'route' => 'customers.index', 'icon' => 'fa-users'],
        ['name' => 'Add New Customer', 'icon' => 'fa-user-plus']
    ]">

    <div class="py-6 lg:py-8">
        <!-- Page Content -->
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Introduction Card -->
            <div class="bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
                <!-- Decorative Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 left-0 w-32 h-32 bg-primary rounded-full -translate-x-16 -translate-y-16"></div>
                    <div class="absolute bottom-0 right-0 w-24 h-24 bg-secondary rounded-full translate-x-12 translate-y-12"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-start gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-gradient-to-br from-primary to-primary-focus text-primary-content w-12 rounded-xl shadow-lg">
                                <i class="fa-sharp fa-user-plus text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-2xl font-bold text-base-content mb-2">Add New Customer</h3>
                            <p class="text-base-content/70 text-base leading-relaxed">Create a new customer account by filling out the information below. All fields marked with an asterisk (*) are required.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Form -->
            <form action="{{ route('customers.store') }}" method="POST" class="space-y-6">
                @csrf

                <!-- Customer Information Card -->
                <div class="card bg-base-100 shadow-md">
                    <!-- Header Section -->
                    <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-user text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Customer Information</h4>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="p-6">
                        @include('customers.partials.form')
                    </div>
                </div>


                <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg p-6">
                    <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-base-300 text-base-content w-10 rounded-lg">
                                    <i class="fa-sharp fa-info-circle text-sm"></i>
                                </div>
                            </div>
                            <div class="text-center sm:text-left">
                                <p class="font-medium text-base-content">Review your information</p>
                                <p class="text-sm text-base-content/60">Make sure all details are correct before saving</p>
                            </div>
                        </div>
                        <div class="flex gap-3 w-full sm:w-auto">
                            <a href="{{ route('customers.index') }}" class="btn btn-outline btn-lg gap-2 flex-1 sm:flex-none">
                                <i class="fa-sharp fa-arrow-left"></i>
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                                <i class="fa-sharp fa-save"></i>
                                Save Customer
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

</x-app-layout>
