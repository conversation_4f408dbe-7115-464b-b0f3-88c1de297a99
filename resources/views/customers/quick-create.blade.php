<x-head/>

<body class="bg-base-200">
    <div class="py-6 lg:py-8">
        <!-- Page Content -->
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Header Card -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body">
                    <div class="flex items-center gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-12 rounded-lg">
                                <i class="fa-sharp fa-user-plus text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-base-content">Quick Create Customer</h1>
                            <p class="text-base-content/70 mt-1 text-lg">Quickly add a new customer to the system</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Form -->
            <form action="{{ route('customers.store') }}" method="POST" class="space-y-6">
                @csrf

                <!-- Customer Information Card -->
                <div class="card bg-base-100 shadow-md">
                    <!-- Header Section -->
                    <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-user text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Customer Information</h4>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="p-6">
                        @include('customers.partials.form')
                    </div>
                </div>

                <!-- Submit Section -->
                <div class="divider my-8">
                    <span class="text-base-content/50 font-medium">Ready to create?</span>
                </div>

                <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg p-6">
                    <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-base-300 text-base-content w-10 rounded-lg">
                                    <i class="fa-sharp fa-info-circle text-sm"></i>
                                </div>
                            </div>
                            <div class="text-center sm:text-left">
                                <p class="font-medium text-base-content">Review your information</p>
                                <p class="text-sm text-base-content/60">Make sure all details are correct before saving</p>
                            </div>
                        </div>
                        <div class="flex gap-3 w-full sm:w-auto">
                            <button type="button" onclick="window.close()" class="btn btn-outline btn-lg gap-2 flex-1 sm:flex-none">
                                <i class="fa-sharp fa-times"></i>
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                                <i class="fa-sharp fa-save"></i>
                                Save Customer
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            <script>
                // When the quick create form loads, check if there's a search text to use
                document.addEventListener('DOMContentLoaded', function() {
                    // Check for the global variable set by the search component
                    if (window.lastSearchText) {
                        const nameField = document.getElementById('name');
                        if (nameField) {
                            nameField.value = window.lastSearchText;
                            console.log('Quick create form: Set name field to:', window.lastSearchText);
                        }
                    }
                });
            </script>
        </div>
    </div>
</body>