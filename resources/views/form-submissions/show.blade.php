<x-app-layout
    page-title="Review Submission #{{ $formSubmission->id }}"
    page-icon="fa-sharp fa-eye"
    :action-buttons="array_filter([
        ['name' => 'Export Data', 'route' => route('form-submissions.export', $formSubmission), 'icon' => 'fa-sharp fa-download', 'class' => 'btn btn-outline btn-sm gap-2', 'permission' => 'export_form_submissions'],
        ['name' => 'Download PDF', 'route' => route('form-submissions.pdf', $formSubmission), 'icon' => 'fa-sharp fa-file-pdf', 'class' => 'btn btn-outline btn-sm gap-2', 'permission' => 'view_form_submissions'],
        $formSubmission->isApproved() && $formSubmission->submitter_email ? ['name' => 'Email Customer PDF', 'onclick' => 'emailCustomerPdf(event)', 'icon' => 'fa-sharp fa-envelope', 'class' => 'btn btn-primary btn-sm gap-2', 'permission' => 'view_form_submissions'] : null
    ])"
    :breadcrumbs="[
        ['name' => 'Form Submissions', 'route' => 'form-submissions.index'],
        ['name' => 'Submission #' . $formSubmission->id]
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Status & Actions Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-{{ $formSubmission->status === 'approved' ? 'success' : ($formSubmission->status === 'rejected' ? 'error' : 'warning') }}/10 to-{{ $formSubmission->status === 'approved' ? 'success' : ($formSubmission->status === 'rejected' ? 'error' : 'warning') }}/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-{{ $formSubmission->status === 'approved' ? 'success' : ($formSubmission->status === 'rejected' ? 'error' : 'warning') }} text-{{ $formSubmission->status === 'approved' ? 'success' : ($formSubmission->status === 'rejected' ? 'error' : 'warning') }}-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-{{ $formSubmission->status === 'approved' ? 'check' : ($formSubmission->status === 'rejected' ? 'times' : 'clock') }} text-sm"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-base-content">Submission Status</h4>
                                <span class="badge {{ $formSubmission->getStatusBadgeClass() }}">
                                    {{ \App\Models\FormSubmission::STATUSES[$formSubmission->status] }}
                                </span>
                            </div>
                        </div>
                        @perms('manage_form_submissions')
                            @if($formSubmission->isPending())
                                <div class="flex gap-2">
                                    @if($formSubmission->form->requires_customer_link && !$formSubmission->customer_id)
                                        {{-- For service agreements without customer, show disabled approve button with tooltip --}}
                                        <div class="tooltip" data-tip="Link to customer first">
                                            <button class="btn btn-success btn-sm gap-2 btn-disabled">
                                                <i class="fa-sharp fa-check"></i>
                                                Approve
                                            </button>
                                        </div>
                                    @else
                                        {{-- Show approve button normally --}}
                                        <button onclick="approveModal.showModal()" class="btn btn-success btn-sm gap-2">
                                            <i class="fa-sharp fa-check"></i>
                                            Approve
                                        </button>
                                    @endif

                                    {{-- Always show reject button --}}
                                    <button onclick="rejectModal.showModal()" class="btn btn-error btn-sm gap-2">
                                        <i class="fa-sharp fa-times"></i>
                                        Reject
                                    </button>
                                </div>

                                
                            @endif
                        @endperms
                    </div>

                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-calendar text-primary"></i>
                            <span class="font-medium">Submitted:</span>
                            <span>{{ $formSubmission->created_at->format('M j, Y g:i A') }}</span>
                        </div>
                        @if($formSubmission->reviewed_at)
                            <div class="flex items-center gap-2">
                                <i class="fa-sharp fa-user-check text-secondary"></i>
                                <span class="font-medium">Reviewed:</span>
                                <span>{{ $formSubmission->reviewed_at->format('M j, Y g:i A') }}</span>
                            </div>
                        @endif
                        @if($formSubmission->approved_at)
                            <div class="flex items-center gap-2">
                                <i class="fa-sharp fa-check-circle text-success"></i>
                                <span class="font-medium">Approved:</span>
                                <span>{{ $formSubmission->approved_at->format('M j, Y g:i A') }}</span>
                            </div>
                        @endif
                                            
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
                
                <!-- Main Content -->
                <div class="xl:col-span-2 space-y-6">
                    
                    <!-- Form Information -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-primary text-primary-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-file-lines text-sm"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-base-content">{{ $formSubmission->form->name }}</h4>
                                    @if($formSubmission->form->applies_discount_on_approval)
                                        <span class="badge badge-info badge-sm">Discount on Approval</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            @if($formSubmission->form->description)
                                <p class="text-base-content/70 mb-4">{{ $formSubmission->form->description }}</p>
                            @endif
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div class="flex items-center gap-2">
                                    <i class="fa-sharp fa-cog text-primary"></i>
                                    <span class="font-medium">Form Features:</span>
                                    <div class="flex gap-1">
                                        @if($formSubmission->form->requires_customer_link)
                                            <span class="badge badge-xs badge-primary">Customer Link</span>
                                        @endif
                                        @if($formSubmission->form->requires_approval)
                                            <span class="badge badge-xs badge-secondary">Requires Approval</span>
                                        @endif
                                        @if($formSubmission->form->applies_discount_on_approval)
                                            <span class="badge badge-xs badge-accent">Discount on Approval</span>
                                        @endif
                                        @if(!$formSubmission->form->requires_customer_link && !$formSubmission->form->requires_approval && !$formSubmission->form->applies_discount_on_approval)
                                            <span class="text-base-content/60">Standard Form</span>
                                        @endif
                                    </div>
                                </div>
                                @if($formSubmission->form->requires_signature)
                                    <div class="flex items-center gap-2">
                                        <i class="fa-sharp fa-signature text-primary"></i>
                                        <span class="font-medium">Signature:</span>
                                        <span class="{{ $formSubmission->signed_at ? 'text-success' : 'text-error' }}">
                                            {{ $formSubmission->signed_at ? 'Signed' : 'Not Signed' }}
                                        </span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>


                    <!-- Form Data -->
                    @if($formSubmission->form_data && count($formSubmission->form_data) > 0)
                        <div class="card bg-base-100 shadow-md">
                            <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                                <div class="flex items-center gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                            <i class="fa-sharp fa-list text-sm"></i>
                                        </div>
                                    </div>
                                    <h4 class="text-lg font-semibold text-base-content">Submitted Form Data</h4>
                                </div>
                            </div>
                            <div class="p-6 space-y-4">
                                @php
                                    // Get field definitions for better display
                                    $fieldsByName = $formSubmission->form->fields->keyBy('name');
                                    $displayedFields = [];
                                @endphp

                                {{-- First, display HTML fields and regular fields in the order they appear in the form --}}
                                @foreach($formSubmission->form->fields->sortBy('order') as $field)
                                    @if($field->type === 'html')
                                        {{-- Display processed HTML content with replaced variables --}}
                                        <div class="border-b border-base-200 pb-4 last:border-0">
                                            @php
                                                // Check if form has save_html_content enabled and we have saved content
                                                $savedHtmlContent = null;
                                                if ($formSubmission->form->save_html_content && $formSubmission->hasSavedHtmlContent()) {
                                                    $savedHtmlContent = $formSubmission->getSavedHtmlContentForField($field->id);
                                                }
                                            @endphp
                                            
                                            @if($savedHtmlContent && isset($savedHtmlContent['processed_content']))
                                                {{-- Use saved HTML content from submission time --}}
                                                <div class="prose prose-sm max-w-none">
                                                    {!! $savedHtmlContent['processed_content'] !!}
                                                </div>
                                                <div class="mt-3 pt-3 border-t border-primary/20">
                                                    <p class="text-xs text-base-content/60">
                                                        <i class="fa-sharp fa-archive text-primary mr-1"></i>
                                                        Content saved at submission time: {{ isset($savedHtmlContent['processed_at']) ? \Carbon\Carbon::parse($savedHtmlContent['processed_at'])->format('M j, Y g:i A') : 'Unknown' }}
                                                    </p>
                                                </div>
                                            @else
                                                {{-- Fallback to real-time processing --}}
                                                <div class="prose prose-sm max-w-none">
                                                    {!! $formSubmission->processHtmlContent($field->content) !!}
                                                </div>
                                                @if($formSubmission->form->save_html_content)
                                                    <div class="mt-3 pt-3 border-t border-warning/20">
                                                        <p class="text-xs text-base-content/60">
                                                            <i class="fa-sharp fa-exclamation-triangle text-warning mr-1"></i>
                                                            Content processed in real-time (no saved content found)
                                                        </p>
                                                    </div>
                                                @endif
                                            @endif
                                        </div>
                                    @elseif($field->type !== 'html' && $field->name && isset($formSubmission->form_data[$field->name]))
                                        @php $displayedFields[] = $field->name; @endphp
                                        <div class="border-b border-base-200 pb-4 last:border-0">
                                            <label class="text-sm font-medium text-base-content/80 block mb-1">
                                                {{ $field->label ?: $field->name }}
                                                @if($field->required)
                                                    <span class="text-error">*</span>
                                                @endif
                                            </label>
                                            <div class="mt-1">
                                                @php
                                                    $value = $formSubmission->form_data[$field->name];
                                                @endphp
                                                
                                                @if(is_array($value))
                                                    <div class="flex flex-wrap gap-2">
                                                        @foreach($value as $item)
                                                            <span class="badge badge-primary badge-outline">{{ $item }}</span>
                                                        @endforeach
                                                    </div>
                                                @elseif($field->type === 'textarea' || strlen($value) > 100)
                                                    <div class="text-base-content bg-base-50 border border-base-300 rounded-lg px-4 py-3 whitespace-pre-wrap">{{ $value ?: '(No response)' }}</div>
                                                @else
                                                    <p class="text-base-content bg-base-50 border border-base-300 rounded-lg px-4 py-2">
                                                        {{ $value ?: '(No response)' }}
                                                    </p>
                                                @endif
                                            </div>
                                        </div>
                                    @endif
                                @endforeach

                                {{-- Then, display any additional fields not defined in the form structure --}}
                                @foreach($formSubmission->form_data as $fieldName => $value)
                                    @if(!in_array($fieldName, $displayedFields) && !in_array($fieldName, ['_token', 'signature_data', 'city', 'state', 'zip']))
                                        @if(str_starts_with($fieldName, '_html_field_'))
                                            {{-- Display saved HTML content --}}
                                            <div class="border-b border-base-200 pb-4 last:border-0">
                                                <label class="text-sm font-medium text-base-content/80 block mb-1">
                                                    <i class="fa-sharp fa-code text-warning mr-1"></i>
                                                    {{ $value['field_label'] ?? 'HTML Content' }} (Saved at Submission)
                                                </label>
                                                <div class="mt-1">
                                                    <div class="bg-warning/5 border border-warning/20 rounded-lg p-4">
                                                        <div class="prose prose-sm max-w-none">
                                                            {!! $value['processed_content'] !!}
                                                        </div>
                                                        <div class="mt-3 pt-3 border-t border-warning/20">
                                                            <p class="text-xs text-base-content/60">
                                                                <i class="fa-sharp fa-clock mr-1"></i>
                                                                Content processed and saved at: {{ isset($value['processed_at']) ? \Carbon\Carbon::parse($value['processed_at'])->format('M j, Y g:i A') : 'Unknown' }}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @else
                                            {{-- Regular field display --}}
                                            <div class="border-b border-base-200 pb-4 last:border-0">
                                                <label class="text-sm font-medium text-base-content/80 block mb-1">
                                                    {{ ucwords(str_replace('_', ' ', $fieldName)) }}
                                                </label>
                                                <div class="mt-1">
                                                    @if(is_array($value))
                                                        <div class="flex flex-wrap gap-2">
                                                            @foreach($value as $item)
                                                                <span class="badge badge-primary badge-outline">{{ $item }}</span>
                                                            @endforeach
                                                        </div>
                                                    @elseif(is_bool($value))
                                                        <span class="badge {{ $value ? 'badge-success' : 'badge-error' }}">
                                                            {{ $value ? 'Yes' : 'No' }}
                                                        </span>
                                                    @elseif(strlen($value) > 100)
                                                        <div class="text-base-content bg-base-50 border border-base-300 rounded-lg px-4 py-3 whitespace-pre-wrap">{{ $value }}</div>
                                                    @else
                                                        <p class="text-base-content bg-base-50 border border-base-300 rounded-lg px-4 py-2">
                                                            {{ $value ?: '(No response)' }}
                                                        </p>
                                                    @endif
                                                </div>
                                            </div>
                                        @endif
                                    @endif
                                @endforeach

                                {{-- Show raw data for debugging if needed --}}
                                @if(Auth::user() && method_exists(Auth::user(), 'isAdmin') && Auth::user()->isAdmin())
                                    <details class="mt-6">
                                        <summary class="cursor-pointer text-sm text-base-content/60 hover:text-base-content">
                                            <i class="fa-sharp fa-code mr-1"></i>
                                            View Raw Data (Admin Only)
                                        </summary>
                                        <div class="mt-2 p-4 bg-base-200 rounded-lg">
                                            <pre class="text-xs overflow-x-auto">{{ json_encode($formSubmission->form_data, JSON_PRETTY_PRINT) }}</pre>
                                        </div>
                                    </details>
                                @endif
                            </div>
                        </div>
                    @else
                        <div class="card bg-base-100 shadow-md">
                            <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                                <div class="flex items-center gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                            <i class="fa-sharp fa-list text-sm"></i>
                                        </div>
                                    </div>
                                    <h4 class="text-lg font-semibold text-base-content">Form Data</h4>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="text-center text-base-content/60 py-8">
                                    <i class="fa-sharp fa-inbox text-4xl mb-2"></i>
                                    <p>No form data was submitted</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Signature -->
                    @if($formSubmission->signature_data)
                        <div class="card bg-base-100 shadow-md">
                            <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                                <div class="flex items-center gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-accent text-accent-content w-8 rounded-lg">
                                            <i class="fa-sharp fa-signature text-sm"></i>
                                        </div>
                                    </div>
                                    <h4 class="text-lg font-semibold text-base-content">Digital Signature</h4>
                                </div>
                            </div>
                            <div class="p-6 space-y-6">
                                <!-- Signature Details -->
                                @if($formSubmission->metadata && (isset($formSubmission->metadata['signature_full_name']) || isset($formSubmission->metadata['signature_title']) || isset($formSubmission->metadata['signature_date'])))
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                        @if(isset($formSubmission->metadata['signature_full_name']))
                                            <div>
                                                <label class="text-sm font-medium text-base-content/80 block mb-1">
                                                    <i class="fa-sharp fa-user text-primary mr-1"></i>
                                                    Signatory Name
                                                </label>
                                                <p class="text-base-content bg-base-50 border border-base-300 rounded-lg px-3 py-2">
                                                    {{ $formSubmission->metadata['signature_full_name'] }}
                                                </p>
                                            </div>
                                        @endif

                                        @if(isset($formSubmission->metadata['signature_title']))
                                            <div>
                                                <label class="text-sm font-medium text-base-content/80 block mb-1">
                                                    <i class="fa-sharp fa-briefcase text-primary mr-1"></i>
                                                    Title/Position
                                                </label>
                                                <p class="text-base-content bg-base-50 border border-base-300 rounded-lg px-3 py-2">
                                                    {{ $formSubmission->metadata['signature_title'] }}
                                                </p>
                                            </div>
                                        @endif

                                        @if(isset($formSubmission->metadata['signature_date']))
                                            <div>
                                                <label class="text-sm font-medium text-base-content/80 block mb-1">
                                                    <i class="fa-sharp fa-calendar text-primary mr-1"></i>
                                                    Signature Date
                                                </label>
                                                <p class="text-base-content bg-base-50 border border-base-300 rounded-lg px-3 py-2">
                                                    {{ \Carbon\Carbon::parse($formSubmission->metadata['signature_date'])->format('M j, Y') }}
                                                </p>
                                            </div>
                                        @endif
                                    </div>
                                @endif

                                <!-- Digital Signature Image -->
                                <div class="bg-white border border-base-300 rounded-lg p-4 text-center">
                                    <img src="{{ $formSubmission->signature_data }}" alt="Digital Signature" class="max-w-full h-auto mx-auto">
                                    <p class="text-sm text-base-content/60 mt-2">
                                        <i class="fa-sharp fa-clock text-info mr-1"></i>
                                        Electronically signed on {{ $formSubmission->signed_at->format('M j, Y g:i A') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif

                </div>

                <!-- Sidebar -->
                <div class="xl:col-span-1 space-y-6">

                    <!-- Submitter Information -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-info text-info-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-user text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Submitter</h4>
                            </div>
                        </div>
                        <div class="p-6 space-y-4">
                            @php
                                // Determine if this is a business submission
                                $customerType = $formSubmission->getFormDataValue('customer_type', 'Residential');
                                $businessName = $formSubmission->getFormDataValue('business_name');
                                $isBusiness = $customerType === 'Business' || !empty($businessName);
                            @endphp

                            @if($isBusiness)
                                <!-- Business Customer -->
                                <div>
                                    <label class="text-sm font-medium text-base-content/80">Business Name</label>
                                    <p class="text-base-content">{{ $formSubmission->submitter_name }}</p>
                                </div>
                                @if($formSubmission->submitter_contact)
                                    <div>
                                        <label class="text-sm font-medium text-base-content/80">Contact Person</label>
                                        <p class="text-base-content">{{ $formSubmission->submitter_contact }}</p>
                                    </div>
                                @endif
                                <div>
                                    <label class="text-sm font-medium text-base-content/80">Customer Type</label>
                                    <p class="text-base-content">
                                        <span class="badge badge-sm badge-primary">
                                            <i class="fa-sharp fa-building mr-1"></i>
                                            Business Customer
                                        </span>
                                    </p>
                                </div>
                            @else
                                <!-- Residential Customer -->
                                <div>
                                    <label class="text-sm font-medium text-base-content/80">Full Name</label>
                                    <p class="text-base-content">{{ $formSubmission->submitter_name }}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-base-content/80">Customer Type</label>
                                    <p class="text-base-content">
                                        <span class="badge badge-sm badge-secondary">
                                            <i class="fa-sharp fa-user mr-1"></i>
                                            Residential Customer
                                        </span>
                                    </p>
                                </div>
                            @endif

                            <div>
                                <label class="text-sm font-medium text-base-content/80">Email</label>
                                <p class="text-base-content">{{ $formSubmission->submitter_email }}</p>
                            </div>
                            @if($formSubmission->submitter_phone)
                                <div>
                                    <label class="text-sm font-medium text-base-content/80">Phone</label>
                                    <p class="text-base-content">{{ $formSubmission->submitter_phone }}</p>
                                </div>
                            @endif
                            <div>
                                <label class="text-sm font-medium text-base-content/80">IP Address</label>
                                <p class="text-base-content font-mono text-sm">{{ $formSubmission->ip_address }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Linking -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-success text-success-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-link text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Customer</h4>
                            </div>
                        </div>
                        <div class="p-6 space-y-4">
                            @if($formSubmission->customer)
                                <div class="space-y-3">
                                    @if($formSubmission->customer->type === 'Business' && $formSubmission->customer->contact)
                                        <div class="font-medium">
                                            <a href="{{ route('customers.show', $formSubmission->customer) }}" class="link link-primary">
                                                {{ $formSubmission->customer->name }}
                                            </a>
                                        </div>
                                        <div class="text-sm text-base-content/70">
                                            Contact: {{ $formSubmission->customer->contact }}
                                        </div>
                                    @else
                                        <div class="font-medium">
                                            <a href="{{ route('customers.show', $formSubmission->customer) }}" class="link link-primary">
                                                {{ $formSubmission->customer->name }}
                                            </a>
                                        </div>
                                    @endif
                                    
                                    <div>
                                        <span class="badge badge-success gap-2">
                                            <i class="fa-sharp fa-link text-xs"></i>
                                            Linked
                                        </span>
                                    </div>
                                    
                                    @perms('manage_form_submissions')
                                        <div>
                                            <button onclick="unlinkCustomer()" class="btn btn-xs btn-warning gap-1">
                                                <i class="fa-sharp fa-unlink text-xs"></i>
                                                Unlink
                                            </button>
                                        </div>
                                    @endperms
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    <div>
                                        <h4 class="font-bold">Not Linked</h4>
                                        <div class="text-sm">This submission is not linked to a customer account</div>
                                    </div>
                                </div>

                                @perms('manage_form_submissions')
                                    <!-- Customer Search and Selection -->
                                    <div class="space-y-4">
                                        <div class="flex items-center gap-2">
                                            <i class="fa-sharp fa-circle-info text-primary"></i>
                                            <p class="text-sm text-base-content/70">
                                                Search for and link this submission to an existing customer, or create a new customer account.
                                            </p>
                                        </div>

                                        <form id="linkCustomerForm" method="POST" action="{{ route('form-submissions.link-customer', $formSubmission) }}">
                                            @csrf
                                            <fieldset class="fieldset">
                                                <legend class="fieldset-legend">
                                                    <i class="fa-sharp fa-search text-primary mr-2"></i>
                                                    Select Customer
                                                </legend>
                                                <x-dynamic-customer-search
                                                    id="submissionCustomerSearch"
                                                    name="customer_id"
                                                    placeholder="Search by business name, contact name, email, or phone..."
                                                    action="form"
                                                    quickCreateButtonId="quickCreateSubmissionCustomerBtn"
                                                    :selectedId="null"
                                                    :selectedName="null"
                                                />
                                            </fieldset>

                                            <div class="flex gap-2 mt-4">
                                                <button type="submit" class="btn btn-primary btn-sm gap-2" id="linkCustomerBtn" style="display: none;">
                                                    <i class="fa-sharp fa-link"></i>
                                                    Link to Customer
                                                </button>
                                            </div>
                                        </form>

                                        <!-- Quick Create Button -->
                                        <x-quick-create-customer
                                            id="submissionCustomerSearch"
                                            name="customer_id"
                                        />

                                        @if($potentialCustomers->count() > 0)
                                            <div class="divider text-sm">Or select from potential matches</div>
                                            <div>
                                                <h5 class="font-medium text-base-content mb-2">Potential Matches</h5>
                                                <div class="space-y-2">
                                                    @foreach($potentialCustomers as $customer)
                                                        <div class="flex items-center justify-between p-3 bg-base-200 rounded-lg">
                                                            <div>
                                                                <p class="font-medium">{{ $customer->name }}</p>
                                                                <p class="text-sm text-base-content/60">{{ $customer->email }}</p>
                                                                @if($customer->phone)
                                                                    <p class="text-sm text-base-content/60">{{ $customer->phone }}</p>
                                                                @endif
                                                            </div>
                                                            <button onclick="linkToCustomer({{ $customer->id }})" class="btn btn-xs btn-primary gap-1">
                                                                <i class="fa-sharp fa-link"></i>
                                                                Link
                                                            </button>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                @endperms
                            @endif
                        </div>
                    </div>

                    <!-- Review History -->
                    @if($formSubmission->reviewer || $formSubmission->approver)
                        <div class="card bg-base-100 shadow-md">
                            <div class="bg-gradient-to-r from-warning/10 to-warning/5 px-6 py-4 border-b border-base-300/50">
                                <div class="flex items-center gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-warning text-warning-content w-8 rounded-lg">
                                            <i class="fa-sharp fa-history text-sm"></i>
                                        </div>
                                    </div>
                                    <h4 class="text-lg font-semibold text-base-content">Review History</h4>
                                </div>
                            </div>
                            <div class="p-6 space-y-4">
                                @if($formSubmission->reviewer)
                                    <div>
                                        <label class="text-sm font-medium text-base-content/80">Reviewed By</label>
                                        <p class="text-base-content">{{ $formSubmission->reviewer->name }}</p>
                                        <p class="text-sm text-base-content/60">{{ $formSubmission->reviewed_at->format('M j, Y g:i A') }}</p>
                                    </div>
                                @endif
                                @if($formSubmission->approver)
                                    <div>
                                        <label class="text-sm font-medium text-base-content/80">{{ $formSubmission->isApproved() ? 'Approved' : 'Rejected' }} By</label>
                                        <p class="text-base-content">{{ $formSubmission->approver->name }}</p>
                                        <p class="text-sm text-base-content/60">{{ $formSubmission->approved_at->format('M j, Y g:i A') }}</p>
                                    </div>
                                @endif
                                @if($formSubmission->review_notes)
                                    <div>
                                        <label class="text-sm font-medium text-base-content/80">Notes</label>
                                        <p class="text-base-content bg-base-200 rounded p-2 text-sm">{{ $formSubmission->review_notes }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                </div>
            </div>

        </div>
    </div>

    <!-- Approve Modal -->
    @perms('manage_form_submissions')
        <dialog id="approveModal" class="modal">
            <div class="modal-box">
                <h3 class="font-bold text-lg mb-4">Approve Submission</h3>
                
                <form method="POST" action="{{ route('form-submissions.approve', $formSubmission) }}">
                    @csrf
                    <div class="space-y-4">
                        @if(!$formSubmission->customer && $potentialCustomers->count() > 0)
                            <div>
                                <label class="label">
                                    <span class="label-text">Link to existing customer</span>
                                </label>
                                <select name="customer_id" class="select select-bordered w-full">
                                    <option value="">Don't link to existing customer</option>
                                    @foreach($potentialCustomers as $customer)
                                        <option value="{{ $customer->id }}">{{ $customer->name }} ({{ $customer->email }})</option>
                                    @endforeach
                                </select>
                            </div>
                        @endif

                        @if(!$formSubmission->customer)
                            <div>
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="checkbox" name="create_customer" value="1" class="checkbox checkbox-primary">
                                    <span class="label-text">Create new customer from submission data</span>
                                </label>
                            </div>
                        @endif

                        <div class="alert alert-success">
                            <i class="fa-sharp fa-info-circle"></i>
                            <div>
                                @if($formSubmission->form->applies_discount_on_approval)
                                    <h4 class="font-bold">Approval with Discount</h4>
                                    <div class="text-sm">Approving this submission will automatically apply the configured discount to the customer's account.</div>
                                @else
                                    <h4 class="font-bold">Approve Submission</h4>
                                    <div class="text-sm">This will mark the submission as approved and notify the submitter.</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="modal-action">
                        <button type="button" class="btn btn-outline" onclick="approveModal.close()">Cancel</button>
                        <button type="submit" class="btn btn-success">Approve Submission</button>
                    </div>
                </form>
            </div>
        </dialog>

        <!-- Reject Modal -->
        <dialog id="rejectModal" class="modal">
            <div class="modal-box">
                <h3 class="font-bold text-lg mb-4">Reject Submission</h3>
                
                <form method="POST" action="{{ route('form-submissions.reject', $formSubmission) }}">
                    @csrf
                    <div class="space-y-4">
                        <div>
                            <label class="label">
                                <span class="label-text">Reason for rejection <span class="text-error">*</span></span>
                            </label>
                            <textarea name="review_notes" rows="4" class="textarea textarea-bordered w-full" 
                                      placeholder="Please provide a reason for rejecting this submission..." required></textarea>
                        </div>

                        <div class="alert alert-error">
                            <i class="fa-sharp fa-exclamation-triangle"></i>
                            <div>
                                <h4 class="font-bold">Reject Submission</h4>
                                <div class="text-sm">This will mark the submission as rejected. The submitter will be notified with your reason.</div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-action">
                        <button type="button" class="btn btn-outline" onclick="rejectModal.close()">Cancel</button>
                        <button type="submit" class="btn btn-error">Reject Submission</button>
                    </div>
                </form>
            </div>
        </dialog>

    @endperms

    <script>
        @perms('manage_form_submissions')
        function linkToCustomer(customerId) {
            showConfirm({
                title: 'Link Customer',
                message: 'Are you sure you want to link this submission to the selected customer?',
                confirmText: 'Link Customer',
                confirmClass: 'btn-primary',
                icon: 'fa-link',
                iconColor: 'bg-primary/10 text-primary'
            }).then(confirmed => {
                if (confirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `{{ route('form-submissions.link-customer', $formSubmission) }}`;
                    
                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                    form.appendChild(csrfToken);
                    
                    const customerIdInput = document.createElement('input');
                    customerIdInput.type = 'hidden';
                    customerIdInput.name = 'customer_id';
                    customerIdInput.value = customerId;
                    form.appendChild(customerIdInput);
                    
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }

        function unlinkCustomer() {
            showConfirm({
                title: 'Unlink Customer',
                message: 'Are you sure you want to unlink this submission from the customer? This will remove the customer association but keep the submission data.',
                confirmText: 'Unlink',
                confirmClass: 'btn-warning',
                icon: 'fa-unlink',
                iconColor: 'bg-warning/10 text-warning'
            }).then(confirmed => {
                if (confirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `{{ route('form-submissions.link-customer', $formSubmission) }}`;
                    
                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                    form.appendChild(csrfToken);
                    
                    // Send empty customer_id to unlink
                    const customerIdInput = document.createElement('input');
                    customerIdInput.type = 'hidden';
                    customerIdInput.name = 'customer_id';
                    customerIdInput.value = '';
                    form.appendChild(customerIdInput);
                    
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }

        // Auto-populate quick create customer modal with form submission data
        document.addEventListener('DOMContentLoaded', function() {
            const quickCreateBtn = document.getElementById('quickCreateCustomerBtn');
            const linkCustomerBtn = document.getElementById('linkCustomerBtn');
            
            // Override the quick create button click to populate with submission data
            if (quickCreateBtn) {
                quickCreateBtn.addEventListener('click', function() {
                    // Set form submission data for auto-population
                    window.formSubmissionData = {
                        name: @json($formSubmission->submitter_name),  // This is already business name OR person name
                        contact: @json($formSubmission->submitter_contact),  // This is the contact person (if business)
                        email: @json($formSubmission->submitter_email),
                        phone: @json($formSubmission->submitter_phone),
                        customer_type: @json($formSubmission->getFormDataValue('customer_type', 'Residential Customer')),
                        address: @json($formSubmission->getFormDataValue('address')),
                        type: @json($formSubmission->submitter_contact ? 'Business' : 'Residential')
                    };
                    
                    // Debug logging
                    console.log('Form submission data for auto-population:', {
                        submitter_name: @json($formSubmission->submitter_name),
                        submitter_contact: @json($formSubmission->submitter_contact),
                        submitter_email: @json($formSubmission->submitter_email),
                        business_name_from_form_data: @json($formSubmission->getFormDataValue('business_name')),
                        final_data: window.formSubmissionData
                    });
                    
                    // Wait for modal to open then populate fields
                    setTimeout(function() {
                        const modal = document.getElementById('quickCreateCustomerModal');
                        if (modal && window.formSubmissionData) {
                            const form = modal.querySelector('#quickCreateCustomerForm');
                            if (form) {
                                // Populate all the fields
                                Object.keys(window.formSubmissionData).forEach(function(key) {
                                    const field = form.querySelector(`[name="${key}"]`);
                                    if (field && window.formSubmissionData[key]) {
                                        console.log(`Setting field [${key}] to:`, window.formSubmissionData[key]);
                                        field.value = window.formSubmissionData[key];
                                    } else if (field && !window.formSubmissionData[key]) {
                                        console.log(`Field [${key}] found but no value to set`);
                                    } else if (!field) {
                                        console.log(`Field [${key}] not found in form`);
                                    }
                                });
                            }
                        }
                    }, 200);
                });
            }
            
            // Show/hide Link Customer button based on selection
            function toggleLinkButton() {
                const customerSearch = document.querySelector('#submissionCustomerSearch');
                const hiddenInput = document.querySelector('input[name="customer_id"]');
                
                if (linkCustomerBtn && hiddenInput) {
                    if (hiddenInput.value && hiddenInput.value.trim() !== '') {
                        linkCustomerBtn.style.display = 'inline-flex';
                    } else {
                        linkCustomerBtn.style.display = 'none';
                    }
                }
            }
            
            // Listen for changes in customer selection
            const hiddenCustomerInput = document.querySelector('input[name="customer_id"]');
            if (hiddenCustomerInput) {
                // Use MutationObserver to watch for value changes
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                            toggleLinkButton();
                        }
                    });
                });
                
                observer.observe(hiddenCustomerInput, {
                    attributes: true,
                    attributeFilter: ['value']
                });
                
                // Also listen for input events
                hiddenCustomerInput.addEventListener('input', toggleLinkButton);
                hiddenCustomerInput.addEventListener('change', toggleLinkButton);
                
                // Initial check
                toggleLinkButton();
            }
        });

        function confirmCreateCustomer() {
            // Submit the approve form with create_customer option
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `{{ route('form-submissions.approve', $formSubmission) }}`;
            
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            form.appendChild(csrfToken);
            
            const createCustomerInput = document.createElement('input');
            createCustomerInput.type = 'hidden';
            createCustomerInput.name = 'create_customer';
            createCustomerInput.value = '1';
            form.appendChild(createCustomerInput);
            
            document.body.appendChild(form);
            form.submit();
        }

        // Email Customer PDF functionality
        async function emailCustomerPdf(event) {
            const customerEmail = @json($formSubmission->submitter_email);
            const customerName = @json($formSubmission->submitter_name);
            
            const confirmed = await showConfirm({
                title: 'Email PDF to Customer',
                message: `Are you sure you want to email a copy of the PDF to ${customerName} at ${customerEmail}?`,
                confirmText: 'Send Email',
                confirmClass: 'btn-primary',
                icon: 'fa-envelope',
                iconColor: 'bg-primary/10 text-primary'
            });

            if (confirmed) {
                try {
                    // Show loading state
                    const button = event ? event.target : document.querySelector('[onclick*="emailCustomerPdf"]');
                    const originalHTML = button.innerHTML;
                    button.disabled = true;
                    button.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Sending...';

                    const response = await fetch('{{ route("form-submissions.email-pdf", $formSubmission) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'Accept': 'application/json'
                        }
                    });

                    const result = await response.json();

                    if (result.success) {
                        showSuccess({
                            title: 'Email Sent Successfully',
                            message: result.message
                        });
                    } else {
                        showError({
                            title: 'Email Failed',
                            message: result.message
                        });
                    }
                } catch (error) {
                    console.error('Error sending email:', error);
                    showError({
                        title: 'Email Failed',
                        message: 'An error occurred while sending the email. Please try again.'
                    });
                } finally {
                    // Reset button state to original
                    const button = event ? event.target : document.querySelector('[onclick*="emailCustomerPdf"]');
                    button.disabled = false;
                    button.innerHTML = '<i class="fa-sharp fa-envelope"></i> Email Customer PDF';
                }
            }
        }
        @endperms
    </script>
</x-app-layout>