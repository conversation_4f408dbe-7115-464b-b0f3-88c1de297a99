<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Form Submission #{{ $submission->id }}</title>
    <style>
        @page {
            margin: 0.75in;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.5;
            color: #333;
        }
        .header {
            border-bottom: 2px solid #3B82F6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            color: #1F2937;
            font-size: 24pt;
        }
        .header .subtitle {
            margin-top: 5px;
            color: #6B7280;
            font-size: 14pt;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 16pt;
            font-weight: bold;
            color: #1F2937;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #E5E7EB;
        }
        .info-grid {
            display: table;
            width: 100%;
            margin-bottom: 15px;
        }
        .info-row {
            display: table-row;
        }
        .info-label {
            display: table-cell;
            width: 200px;
            padding: 5px 0;
            font-weight: bold;
            color: #4B5563;
        }
        .info-value {
            display: table-cell;
            padding: 5px 0;
            color: #1F2937;
        }
        .status-badge {
            display: inline-block;
            padding: 3px 10px;
            border-radius: 4px;
            font-size: 10pt;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-submitted {
            background-color: #FEF3C7;
            color: #92400E;
        }
        .status-reviewing {
            background-color: #DBEAFE;
            color: #1E40AF;
        }
        .status-approved {
            background-color: #D1FAE5;
            color: #065F46;
        }
        .status-rejected {
            background-color: #FEE2E2;
            color: #991B1B;
        }
        .field-group {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #F9FAFB;
            border: 1px solid #E5E7EB;
            border-radius: 4px;
        }
        .field-label {
            font-weight: bold;
            color: #4B5563;
            margin-bottom: 5px;
        }
        .field-value {
            color: #1F2937;
            white-space: pre-wrap;
        }
        .html-section {
            margin: 20px 0;
            padding: 15px;
            background-color: #F3F4F6;
            border-left: 4px solid #3B82F6;
        }
        .section-header {
            background-color: #EBF8FF;
            padding: 10px;
            margin: 20px -10px 10px -10px;
            font-weight: bold;
            color: #1E40AF;
        }
        .signature-section {
            margin-top: 40px;
            padding: 20px;
            border: 2px solid #E5E7EB;
            border-radius: 4px;
            background-color: #F9FAFB;
        }
        .signature-image {
            margin: 15px 0;
            text-align: center;
        }
        .signature-image img {
            max-width: 300px;
            max-height: 150px;
            border: 1px solid #D1D5DB;
            background-color: white;
        }
        .signature-details {
            margin-top: 10px;
        }
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #E5E7EB;
            text-align: center;
            font-size: 10pt;
            color: #6B7280;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            text-align: left;
            padding: 8px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>{{ $submission->form->name }}</h1>
        <div class="subtitle">Submission #{{ $submission->id }}</div>
    </div>

    <!-- Submission Status -->
    <div class="section">
        <div class="section-title">Submission Status</div>
        <div class="info-grid">
            <div class="info-row">
                <div class="info-label">Status:</div>
                <div class="info-value">
                    <span class="status-badge status-{{ $submission->status }}">
                        {{ \App\Models\FormSubmission::STATUSES[$submission->status] }}
                    </span>
                </div>
            </div>
            <div class="info-row">
                <div class="info-label">Submitted:</div>
                <div class="info-value">{{ $submission->created_at->format('F j, Y g:i A') }}</div>
            </div>
            @if($submission->reviewed_at)
            <div class="info-row">
                <div class="info-label">Reviewed:</div>
                <div class="info-value">{{ $submission->reviewed_at->format('F j, Y g:i A') }}</div>
            </div>
            @endif
            @if($submission->approved_at)
            <div class="info-row">
                <div class="info-label">Approved:</div>
                <div class="info-value">{{ $submission->approved_at->format('F j, Y g:i A') }}</div>
            </div>
            @endif
        </div>
    </div>

    <!-- Contact Information -->
    <div class="section">
        <div class="section-title">Contact Information</div>
        <div class="info-grid">
            <div class="info-row">
                <div class="info-label">Name:</div>
                <div class="info-value">{{ $submission->submitter_name }}</div>
            </div>
            @if($submission->submitter_contact)
            <div class="info-row">
                <div class="info-label">Contact Person:</div>
                <div class="info-value">{{ $submission->submitter_contact }}</div>
            </div>
            @endif
            @if($submission->submitter_email)
            <div class="info-row">
                <div class="info-label">Email:</div>
                <div class="info-value">{{ $submission->submitter_email }}</div>
            </div>
            @endif
            @if($submission->submitter_phone)
            <div class="info-row">
                <div class="info-label">Phone:</div>
                <div class="info-value">{{ $submission->submitter_phone }}</div>
            </div>
            @endif
            @if($submission->form_data['address'] ?? null)
            <div class="info-row">
                <div class="info-label">Address:</div>
                <div class="info-value">{{ $submission->form_data['address'] }}</div>
            </div>
            @endif
        </div>
    </div>

    <!-- Customer Information -->
    @if($submission->customer)
    <div class="section">
        <div class="section-title">Linked Customer</div>
        <div class="info-grid">
            <div class="info-row">
                <div class="info-label">Customer:</div>
                <div class="info-value">{{ $submission->customer->name }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Type:</div>
                <div class="info-value">{{ $submission->customer->type }}</div>
            </div>
        </div>
    </div>
    @endif

    <!-- Form Fields -->
    <div class="section">
        <div class="section-title">Form Responses</div>
        @foreach($fieldData as $field)
            @if($field['type'] === 'section')
                <div class="section-header">{{ $field['label'] }}</div>
            @elseif($field['type'] === 'html')
                <div class="html-section">
                    @php
                        // Check if we have saved HTML content for this field
                        $savedHtmlContent = null;
                        if ($submission->hasSavedHtmlContent()) {
                            $savedHtmlContent = $submission->getSavedHtmlContentForField($field['id']);
                        }
                    @endphp
                    @if($savedHtmlContent && isset($savedHtmlContent['processed_content']))
                        {{-- Use the saved processed content from submission time --}}
                        {!! $savedHtmlContent['processed_content'] !!}
                    @else
                        {{-- Fallback to real-time processing --}}
                        {!! $submission->processHtmlContent($field['content']) !!}
                    @endif
                </div>
            @else
                <div class="field-group">
                    <div class="field-label">{{ $field['label'] }}:</div>
                    <div class="field-value">
                        @if($field['type'] === 'checkbox')
                            {{ $field['value'] ? 'Yes' : 'No' }}
                        @elseif($field['type'] === 'select' || $field['type'] === 'radio')
                            {{ $field['value'] }}
                        @elseif($field['type'] === 'textarea' || $field['type'] === 'rich_text')
                            {!! nl2br(e($field['value'])) !!}
                        @else
                            {{ $field['value'] }}
                        @endif
                    </div>
                </div>
            @endif
        @endforeach
    </div>

    <!-- Signature Section -->
    @if($signatureImagePath && file_exists($signatureImagePath))
    <div class="signature-section">
        <div class="section-title">Digital Signature</div>
        <div class="signature-image">
            <img src="{{ $signatureImagePath }}" alt="Signature" style="max-width: 300px; max-height: 150px;">
        </div>
        <div class="signature-details">
            <div class="info-grid">
                @if($submission->metadata['signature_full_name'] ?? null)
                <div class="info-row">
                    <div class="info-label">Signed By:</div>
                    <div class="info-value">{{ $submission->metadata['signature_full_name'] }}</div>
                </div>
                @endif
                @if($submission->metadata['signature_title'] ?? null)
                <div class="info-row">
                    <div class="info-label">Title:</div>
                    <div class="info-value">{{ $submission->metadata['signature_title'] }}</div>
                </div>
                @endif
                @if($submission->signed_at)
                <div class="info-row">
                    <div class="info-label">Signed At:</div>
                    <div class="info-value">{{ $submission->signed_at->format('F j, Y g:i A') }}</div>
                </div>
                @endif
            </div>
        </div>
    </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>Generated on {{ now()->format('F j, Y g:i A') }}</p>
        <p>Form Submission #{{ $submission->id }} - {{ config('app.name') }}</p>
    </div>
</body>
</html>