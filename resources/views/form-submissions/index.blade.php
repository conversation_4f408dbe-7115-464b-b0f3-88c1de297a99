<x-app-layout
    page-title="Form Submissions"
    page-icon="fa-sharp fa-paper-plane"
    :breadcrumbs="[
        ['name' => 'Form Submissions']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-8">

            <!-- Header Card -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-base-content">Form Submissions</h1>
                            <p class="text-base-content/70 mt-1 text-lg">Review and manage form submissions from customers</p>
                        </div>
                        <div class="hidden lg:flex items-center gap-2 text-base-content/60">
                            <div class="stat">
                                <div class="stat-title text-xs">Total Submissions</div>
                                <div class="stat-value text-2xl">{{ $submissions->total() }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search & Filter Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-magnifying-glass text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Search & Filter</h4>
                    </div>
                </div>
                <div class="p-6 space-y-6">
                    <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                        <div>
                            <label class="label" for="search">
                                <span class="label-text">
                                    <i class="fa-sharp fa-magnifying-glass text-primary mr-2"></i>
                                    Search
                                </span>
                            </label>
                            <input type="text" name="search" id="search" class="input input-bordered w-full"
                                   value="{{ request('search') }}" placeholder="Name, email, phone, ID...">
                        </div>
                        
                        <div>
                            <label class="label" for="form_id">
                                <span class="label-text">
                                    <i class="fa-sharp fa-file-lines text-primary mr-2"></i>
                                    Form
                                </span>
                            </label>
                            <select name="form_id" id="form_id" class="select select-bordered w-full">
                                <option value="">All Forms</option>
                                @foreach($forms as $form)
                                    <option value="{{ $form->id }}" {{ request('form_id') == $form->id ? 'selected' : '' }}>
                                        {{ $form->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label class="label" for="status">
                                <span class="label-text">
                                    <i class="fa-sharp fa-flag text-primary mr-2"></i>
                                    Status
                                </span>
                            </label>
                            <select name="status" id="status" class="select select-bordered w-full">
                                <option value="">All Status</option>
                                @foreach(\App\Models\FormSubmission::STATUSES as $value => $label)
                                    <option value="{{ $value }}" {{ request('status') === $value ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label class="label" for="start_date">
                                <span class="label-text">
                                    <i class="fa-sharp fa-calendar text-primary mr-2"></i>
                                    Date Range
                                </span>
                            </label>
                            <input type="date" name="start_date" id="start_date" class="input input-bordered w-full"
                                   value="{{ request('start_date') }}">
                        </div>

                        <div>
                            <label class="label" for="end_date">
                                <span class="label-text">&nbsp;</span>
                            </label>
                            <input type="date" name="end_date" id="end_date" class="input input-bordered w-full"
                                   value="{{ request('end_date') }}">
                        </div>
                        
                        <div class="md:col-span-2 lg:col-span-3 xl:col-span-1 flex items-end">
                            <button type="submit" class="btn btn-primary gap-2 w-full">
                                <i class="fa-sharp fa-search"></i>
                                Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Submissions Data Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-paper-plane text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Submissions</h4>
                        </div>
                        @perms('manage_form_submissions')
                            <div class="flex gap-2">
                                <button onclick="openBulkActionModal()" class="btn btn-outline btn-sm gap-2">
                                    <i class="fa-sharp fa-check-double"></i>
                                    Bulk Actions
                                </button>
                            </div>
                        @endperms
                    </div>
                </div>

                @if($submissions->count() > 0)
                    <!-- Desktop Table Headers -->
                    <div class="hidden lg:grid lg:grid-cols-12 gap-2 items-center text-sm font-semibold text-base-content/80 bg-base-200/30 p-4 border-b border-base-200">
                        <div class="col-span-1">ID</div>
                        <div class="col-span-3">Submitter</div>
                        <div class="col-span-2">Form</div>
                        <div class="col-span-1 text-center">Status</div>
                        <div class="col-span-2 text-center">Customer</div>
                        <div class="col-span-1 text-center">Signed</div>
                        <div class="col-span-1 text-center">Date</div>
                        <div class="col-span-1 text-center">Actions</div>
                    </div>
                    
                    <div class="divide-y divide-base-200/50">
                        @foreach ($submissions as $submission)
                        <div class="p-3 lg:p-4 hover:bg-base-200/50 transition-all duration-200 {{ $loop->even ? 'bg-base-100' : 'bg-base-200/30' }}">
                            <!-- Mobile Layout -->
                            <div class="lg:hidden">
                                <div class="flex items-center gap-2 mb-2">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center gap-1">
                                            <span class="text-xs text-base-content/60 font-mono">#{{ $submission->id }}</span>
                                            <a href="{{ route('form-submissions.show', $submission) }}" class="link-primary font-medium hover:link-hover text-lg">
                                                {{ $submission->submitter_name }}
                                            </a>
                                        </div>
                                        <p class="text-sm text-base-content/60">{{ $submission->form->name }}</p>
                                    </div>
                                    <div class="badge badge-xs {{ $submission->getStatusBadgeClass() }} whitespace-nowrap">
                                        {{ \App\Models\FormSubmission::STATUSES[$submission->status] }}
                                    </div>
                                </div>
                                <!-- Two Column Details -->
                                <div class="grid grid-cols-2 gap-3 mb-3 text-xs">
                                    <div class="space-y-1">
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-envelope text-primary"></i>
                                            <span class="font-medium">Email:</span>
                                            <span class="truncate">{{ $submission->submitter_email }}</span>
                                        </div>
                                        @if($submission->submitter_phone)
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-phone text-primary"></i>
                                                <span class="font-medium">Phone:</span>
                                                <span>{{ $submission->submitter_phone }}</span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="space-y-1">
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-calendar text-primary"></i>
                                            <span class="font-medium">Submitted:</span>
                                            <span>{{ $submission->created_at->format('M j') }}</span>
                                        </div>
                                        @if($submission->customer)
                                            <div class="flex items-center gap-1">
                                                <i class="fa-sharp fa-user text-success"></i>
                                                <span class="font-medium">Customer:</span>
                                                <span>Linked</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                <!-- Mobile Action Bar -->
                                <div class="flex gap-2 pt-2 border-t border-base-200/50">
                                    <a href="{{ route('form-submissions.show', $submission) }}" class="btn btn-primary btn-sm flex-1 gap-1">
                                        <i class="fa-sharp fa-eye"></i>
                                        Review
                                    </a>
                                </div>
                            </div>

                            <!-- Desktop Layout -->
                            <div class="hidden lg:grid lg:grid-cols-12 gap-2 items-center text-sm">
                                <div class="col-span-1 flex items-center gap-1">
                                    @perms('manage_form_submissions')
                                        <input type="checkbox" class="checkbox checkbox-primary checkbox-sm bulk-select" 
                                               value="{{ $submission->id }}">
                                    @endperms
                                    <span class="text-xs text-base-content/60 font-mono">#{{ $submission->id }}</span>
                                </div>
                                <div class="col-span-3">
                                    <a href="{{ route('form-submissions.show', $submission) }}" class="link-primary font-medium hover:link-hover">
                                        {{ $submission->submitter_name }}
                                    </a>
                                    <p class="text-xs text-base-content/60 truncate">{{ $submission->submitter_email }}</p>
                                </div>
                                <div class="col-span-2">
                                    <span class="text-sm">{{ $submission->form->name }}</span>
                                    @if($submission->form->applies_discount_on_approval)
                                        <span class="badge badge-xs badge-info ml-1">Discount</span>
                                    @endif
                                </div>
                                <div class="col-span-1 text-center">
                                    <span class="badge badge-xs {{ $submission->getStatusBadgeClass() }} whitespace-nowrap">
                                        {{ \App\Models\FormSubmission::STATUSES[$submission->status] }}
                                    </span>
                                </div>
                                <div class="col-span-2 text-center">
                                    @if($submission->customer)
                                        <a href="{{ route('customers.show', $submission->customer) }}" class="link link-success text-xs">
                                            {{ $submission->customer->name }}
                                        </a>
                                    @else
                                        <span class="text-base-content/50 text-xs">Not linked</span>
                                    @endif
                                </div>
                                <div class="col-span-1 text-center">
                                    @if($submission->signed_at)
                                        <i class="fa-sharp fa-signature text-success" title="Digitally signed"></i>
                                    @else
                                        <span class="text-base-content/40">-</span>
                                    @endif
                                </div>
                                <div class="col-span-1 text-xs text-base-content/60">
                                    {{ $submission->created_at->format('M j, Y') }}
                                </div>
                                <div class="col-span-1">
                                    <div class="flex justify-center">
                                        <div class="tooltip" data-tip="Review Submission">
                                            <a href="{{ route('form-submissions.show', $submission) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-primary hover:text-primary-content transition-all">
                                                <i class="fa-sharp fa-eye"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    @if($submissions->hasPages())
                        <div class="px-6 py-4 border-t border-base-300/50 bg-base-50">
                            <x-pagination :paginator="$submissions" :pagination="20" />
                        </div>
                    @endif
                @else
                    <!-- Empty State -->
                    <div class="text-center text-base-content/70 py-12">
                        <div class="flex flex-col items-center gap-4">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                    <i class="fa-sharp fa-paper-plane text-2xl"></i>
                                </div>
                            </div>
                            <div class="text-center">
                                <h3 class="text-lg font-medium text-base-content/80">No submissions found</h3>
                                <p class="text-sm text-base-content/60 mt-1">Form submissions will appear here when customers submit forms</p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

        </div>
    </div>

    <!-- Bulk Action Modal -->
    @perms('manage_form_submissions')
        <dialog id="bulkActionModal" class="modal">
            <div class="modal-box">
                <h3 class="font-bold text-lg mb-4">Bulk Actions</h3>
                
                <form method="POST" action="{{ route('form-submissions.bulk-action') }}" id="bulkActionForm">
                    @csrf
                    <div class="space-y-4">
                        <div>
                            <label class="label" for="bulk_action">
                                <span class="label-text">Action</span>
                            </label>
                            <select name="action" id="bulk_action" class="select select-bordered w-full" required>
                                <option value="">Choose action...</option>
                                <option value="approve">Approve selected submissions</option>
                                <option value="reject">Reject selected submissions</option>
                                <option value="delete">Delete selected submissions</option>
                            </select>
                        </div>

                        <div id="selectedCount" class="text-sm text-base-content/60">
                            No submissions selected
                        </div>
                    </div>

                    <div class="modal-action">
                        <button type="button" class="btn btn-outline" onclick="bulkActionModal.close()">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="executeBulkAction" disabled>Execute Action</button>
                    </div>
                </form>
            </div>
        </dialog>
    @endperms

    <script>
        @perms('manage_form_submissions')
        function openBulkActionModal() {
            updateSelectedCount();
            document.getElementById('bulkActionModal').showModal();
        }

        function updateSelectedCount() {
            const selected = document.querySelectorAll('.bulk-select:checked');
            const count = selected.length;
            const countDiv = document.getElementById('selectedCount');
            const executeBtn = document.getElementById('executeBulkAction');
            
            if (count === 0) {
                countDiv.textContent = 'No submissions selected';
                executeBtn.disabled = true;
            } else {
                countDiv.textContent = `${count} submission${count === 1 ? '' : 's'} selected`;
                executeBtn.disabled = false;
            }
        }

        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('bulk-select')) {
                updateSelectedCount();
            }
        });

        document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
            const selected = Array.from(document.querySelectorAll('.bulk-select:checked')).map(cb => cb.value);
            
            if (selected.length === 0) {
                e.preventDefault();
                alert('Please select at least one submission.');
                return;
            }

            // Add selected IDs to form
            selected.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'submission_ids[]';
                input.value = id;
                this.appendChild(input);
            });

            const action = document.getElementById('bulk_action').value;
            if (action === 'delete' && !confirm(`Are you sure you want to delete ${selected.length} submission(s)? This cannot be undone.`)) {
                e.preventDefault();
            }
        });

        @endperms
    </script>
</x-app-layout>