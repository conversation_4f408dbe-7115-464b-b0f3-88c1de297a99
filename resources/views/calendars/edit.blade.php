<x-app-layout
    page-title="Edit Calendar: {{ $calendar->title }}"
    page-icon="fa-sharp fa-pen-to-square"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('calendars.index'),
            'text' => 'Back to Calendars'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'View Calendar',
            'route' => route('calendars.show', $calendar->id),
            'icon' => 'fa-sharp fa-eye',
            'class' => 'btn btn-info btn-sm gap-2'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Calendars', 'route' => 'calendars.index', 'icon' => 'fa-calendar'],
        ['name' => $calendar->title, 'route' => 'calendars.show', 'params' => $calendar->id, 'icon' => 'fa-calendar-check'],
        ['name' => 'Edit', 'icon' => 'fa-pen-to-square']
    ]">

    <div class="py-6 lg:py-8 space-y-8">
        <!-- Page Content -->
        <div class="mx-auto sm:px-2 lg:px-4 space-y-8">
            <!-- Edit Calendar Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-warning/10 to-warning/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-warning text-warning-content w-8 rounded-lg">
                                <i class="fa-sharp fa-pen-to-square text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Edit Calendar: {{ $calendar->title }}</h4>
                    </div>
                </div>

                <div class="p-6">
                <form action="{{ route('calendars.update', $calendar->id) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Calendar Title -->
                        <div class="form-control">
                            <label for="title" class="label">
                                <span class="label-text">Calendar Title</span>
                            </label>
                            <input type="text" name="title" id="title" class="input input-bordered @error('title') input-error @enderror" value="{{ old('title', $calendar->title) }}" required>
                            @error('title')
                                <span class="text-error text-sm mt-1">{{ $message }}</span>
                            @enderror
                        </div>
                        
                        <!-- Calendar Color -->
                        <div class="form-control">
                            <label for="color" class="label">
                                <span class="label-text">Calendar Color</span>
                            </label>
                            <div class="flex space-x-2">
                                <input type="color" name="color" id="color" class="h-10 w-10 rounded @error('color') border-error @enderror" value="{{ old('color', $calendar->color) }}">
                                <input type="text" id="color-hex" class="input input-bordered flex-1" value="{{ $calendar->color }}" readonly>
                            </div>
                            @error('color')
                                <span class="text-error text-sm mt-1">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Calendar Description -->
                    <div class="form-control mt-4">
                        <label for="description" class="label">
                            <span class="label-text">Description</span>
                        </label>
                        <textarea name="description" id="description" rows="3" class="textarea textarea-bordered @error('description') textarea-error @enderror">{{ old('description', $calendar->description) }}</textarea>
                        @error('description')
                            <span class="text-error text-sm mt-1">{{ $message }}</span>
                        @enderror
                    </div>
                    
                    <!-- Public Calendar -->
                    <div class="form-control mt-4">
                        <label class="cursor-pointer label justify-start">
                            <input type="checkbox" name="is_public" value="1" class="checkbox checkbox-primary mr-2" {{ old('is_public', $calendar->is_public) ? 'checked' : '' }}>
                            <span class="label-text">Make this calendar public (visible to all users)</span>
                        </label>
                        @error('is_public')
                            <span class="text-error text-sm mt-1">{{ $message }}</span>
                        @enderror
                    </div>
                    
                    <!-- Calendar Owner Information -->
                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-lg font-medium mb-2">Calendar Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">Owner</p>
                                <p class="font-medium">{{ $calendar->owner->name }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Created</p>
                                <p class="font-medium">{{ $calendar->created_at->format('M d, Y') }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Share with Users -->
                    <div class="mt-6">
                        <h3 class="text-lg font-medium mb-2">Share with Users</h3>
                        <p class="text-sm text-gray-500 mb-4">You can share this calendar with other users and set their permissions.</p>
                        
                        <div id="shared-users-container">
                            @foreach($calendar->shares as $share)
                                <div class="shared-user-row grid grid-cols-1 md:grid-cols-3 gap-4 mb-2 p-2 border rounded">
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text">User</span>
                                        </label>
                                        <select name="shared_users[]" class="select select-bordered w-full">
                                            <option value="">Select User</option>
                                            @foreach ($users as $user)
                                                <option value="{{ $user->id }}" {{ $share->user_id == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text">Permission</span>
                                        </label>
                                        <select name="permissions[]" class="select select-bordered w-full">
                                            <option value="view" {{ $share->permission == 'view' ? 'selected' : '' }}>View Only</option>
                                            <option value="edit" {{ $share->permission == 'edit' ? 'selected' : '' }}>Edit Events</option>
                                            <option value="manage" {{ $share->permission == 'manage' ? 'selected' : '' }}>Manage Calendar</option>
                                        </select>
                                    </div>
                                    
                                    <div class="flex items-end mb-2">
                                        <button type="button" class="remove-user-btn btn btn-error btn-sm">
                                            <i class="fa-sharp fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        
                        <button type="button" id="add-user-btn" class="btn btn-outline btn-sm mt-2">
                            <i class="fa-sharp fa-plus mr-1"></i> Add User
                        </button>
                    </div>
                    
                    <div class="mt-8 flex justify-end">
                        <button type="submit" class="btn btn-primary gap-2">
                            <i class="fa-sharp fa-save"></i> Update Calendar
                        </button>
                    </div>
                </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- User Template (Hidden) -->
    <template id="user-row-template">
        <div class="shared-user-row grid grid-cols-1 md:grid-cols-3 gap-4 mb-2 p-2 border rounded">
            <div class="form-control">
                <label class="label">
                    <span class="label-text">User</span>
                </label>
                <select name="shared_users[]" class="select select-bordered w-full">
                    <option value="">Select User</option>
                    @foreach ($users as $user)
                        <option value="{{ $user->id }}">{{ $user->name }}</option>
                    @endforeach
                </select>
            </div>
            
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Permission</span>
                </label>
                <select name="permissions[]" class="select select-bordered w-full">
                    <option value="view">View Only</option>
                    <option value="edit">Edit Events</option>
                    <option value="manage">Manage Calendar</option>
                </select>
            </div>
            
            <div class="flex items-end mb-2">
                <button type="button" class="remove-user-btn btn btn-error btn-sm">
                    <i class="fa-sharp fa-trash"></i>
                </button>
            </div>
        </div>
    </template>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Color picker synchronization
            const colorPicker = document.getElementById('color');
            const colorHex = document.getElementById('color-hex');
            
            colorPicker.addEventListener('input', function() {
                colorHex.value = this.value;
            });
            
            // Shared users functionality
            const addUserBtn = document.getElementById('add-user-btn');
            const sharedUsersContainer = document.getElementById('shared-users-container');
            const userRowTemplate = document.getElementById('user-row-template');
            
            // Add event listeners to existing remove buttons
            document.querySelectorAll('.remove-user-btn').forEach(button => {
                button.addEventListener('click', function() {
                    this.closest('.shared-user-row').remove();
                });
            });
            
            addUserBtn.addEventListener('click', function() {
                const userRow = document.importNode(userRowTemplate.content, true);
                sharedUsersContainer.appendChild(userRow);
                
                // Add event listener to the remove button
                const removeBtn = sharedUsersContainer.lastElementChild.querySelector('.remove-user-btn');
                removeBtn.addEventListener('click', function() {
                    this.closest('.shared-user-row').remove();
                });
            });
        });
    </script>
</x-app-layout>
