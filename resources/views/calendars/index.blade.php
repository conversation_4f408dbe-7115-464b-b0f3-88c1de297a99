<x-app-layout
    page-title="Calendars"
    page-icon="fa-sharp fa-calendar"
    :primary-buttons="[
        [
            'type' => 'create',
            'route' => route('calendars.create'),
            'text' => 'New Calendar'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Calendars', 'icon' => 'fa-calendar']
    ]">

    <div class="py-6 lg:py-8 space-y-8">
        <!-- Page Content -->
        <div class="mx-auto sm:px-2 lg:px-4 space-y-8 max-w-7xl" >
            @if (session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            <!-- Header Card -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-base-content">Calendar Management</h1>
                            <p class="text-base-content/70 mt-1 text-lg">Manage your calendars, shared calendars, and public calendars.</p>
                        </div>
                        <div class="hidden lg:flex items-center gap-2 text-base-content/60">
                            <div class="stat">
                                <div class="stat-title text-xs">My Calendars</div>
                                <div class="stat-value text-2xl">{{ $ownedCalendars->count() }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- My Calendars Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-calendar-check text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">My Calendars</h4>
                        </div>
                        <div class="flex items-center gap-2 text-sm text-base-content/60">
                            <span>{{ $ownedCalendars->count() }} calendar{{ $ownedCalendars->count() !== 1 ? 's' : '' }}</span>
                        </div>
                    </div>
                </div>

                <!-- My Calendars Section -->
                <div class="divide-y divide-base-200/50">
                    @forelse ($ownedCalendars as $calendar)
                        <!-- Calendar Card Row -->
                        <div class="p-3 lg:p-4 hover:bg-base-200/50 transition-all duration-200 {{ $loop->even ? 'bg-base-100' : 'bg-base-200/30' }}">
                            <!-- Mobile Layout -->
                            <div class="lg:hidden">
                                <!-- Calendar Title -->
                                <div class="flex items-center gap-2 mb-2">
                                    <div class="w-4 h-4 rounded-full flex-shrink-0" style="background-color: {{ $calendar->color }};"></div>
                                    <div class="flex-1 min-w-0">
                                        <a href="{{ route('calendars.show', $calendar->id) }}" class="link-primary font-medium hover:link-hover truncate text-lg">
                                            {{ $calendar->title }}
                                        </a>
                                    </div>
                                    <div class="badge badge-sm {{ $calendar->is_public ? 'badge-success' : 'badge-ghost' }}">
                                        {{ $calendar->is_public ? 'Public' : 'Private' }}
                                    </div>
                                </div>

                                <!-- Description -->
                                @if($calendar->description)
                                    <div class="mb-3 text-sm text-base-content/70">
                                        {{ Str::limit($calendar->description, 100) }}
                                    </div>
                                @endif

                                <!-- Mobile Action Bar -->
                                <div class="flex gap-2 pt-2 border-t border-base-200/50">
                                    <a href="{{ route('calendars.show', $calendar->id) }}" class="btn btn-primary btn-sm flex-1 gap-1">
                                        <i class="fa-sharp fa-eye"></i>
                                        View
                                    </a>
                                    <a href="{{ route('calendars.edit', $calendar->id) }}" class="btn btn-warning btn-sm flex-1 gap-1">
                                        <i class="fa-sharp fa-pen-to-square"></i>
                                        Edit
                                    </a>
                                    <form action="{{ route('calendars.destroy', $calendar->id) }}" method="POST" class="flex-1">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-error btn-sm w-full gap-1"
                                                onclick="return confirm('Are you sure you want to delete this calendar? This action cannot be undone.')">
                                            <i class="fa-sharp fa-trash"></i>
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- Desktop Layout -->
                            <div class="hidden lg:grid lg:grid-cols-12 gap-4 items-center">
                                <!-- Color & Title Column (4 cols) -->
                                <div class="lg:col-span-4">
                                    <div class="flex items-center gap-3">
                                        <div class="w-4 h-4 rounded-full flex-shrink-0" style="background-color: {{ $calendar->color }};"></div>
                                        <div class="flex-1 min-w-0">
                                            <a href="{{ route('calendars.show', $calendar->id) }}" class="link-primary font-medium hover:link-hover truncate text-base">
                                                {{ $calendar->title }}
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <!-- Description Column (4 cols) -->
                                <div class="lg:col-span-4">
                                    <div class="text-sm text-base-content/70 truncate">
                                        {{ $calendar->description ?: 'No description' }}
                                    </div>
                                </div>

                                <!-- Visibility Column (2 cols) -->
                                <div class="lg:col-span-2">
                                    <div class="badge badge-sm {{ $calendar->is_public ? 'badge-success' : 'badge-ghost' }}">
                                        {{ $calendar->is_public ? 'Public' : 'Private' }}
                                    </div>
                                </div>

                                <!-- Actions Column (2 cols) -->
                                <div class="lg:col-span-2">
                                    <div class="flex gap-1 justify-center">
                                        <div class="tooltip" data-tip="View Calendar">
                                            <a href="{{ route('calendars.show', $calendar->id) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-primary hover:text-primary-content transition-all">
                                                <i class="fa-sharp fa-eye"></i>
                                            </a>
                                        </div>
                                        <div class="tooltip" data-tip="Edit Calendar">
                                            <a href="{{ route('calendars.edit', $calendar->id) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-warning hover:text-warning-content transition-all">
                                                <i class="fa-sharp fa-pen-to-square"></i>
                                            </a>
                                        </div>
                                        <div class="tooltip" data-tip="Delete Calendar">
                                            <form action="{{ route('calendars.destroy', $calendar->id) }}" method="POST" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-circle btn-ghost text-error hover:bg-error hover:text-error-content transition-all"
                                                        onclick="return confirm('Are you sure you want to delete this calendar? This action cannot be undone.')">
                                                    <i class="fa-sharp fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <!-- Empty State -->
                        <div class="text-center text-base-content/70 py-12">
                            <div class="flex flex-col items-center gap-4">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                        <i class="fa-sharp fa-calendar text-2xl"></i>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <h3 class="text-lg font-medium text-base-content/80">No calendars found</h3>
                                    <p class="text-sm text-base-content/60 mt-1">
                                        Get started by creating your first calendar
                                    </p>
                                </div>
                                <a href="{{ route('calendars.create') }}" class="btn btn-primary btn-sm gap-2">
                                    <i class="fa-sharp fa-plus"></i>
                                    Create First Calendar
                                </a>
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Shared Calendars Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-share text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Shared With Me</h4>
                        </div>
                        <div class="flex items-center gap-2 text-sm text-base-content/60">
                            <span>{{ $sharedCalendars->count() }} calendar{{ $sharedCalendars->count() !== 1 ? 's' : '' }}</span>
                        </div>
                    </div>
                </div>

                <!-- Shared Calendars Section -->
                <div class="divide-y divide-base-200/50">
                    @forelse ($sharedCalendars as $calendar)
                        <!-- Calendar Card Row -->
                        <div class="p-3 lg:p-4 hover:bg-base-200/50 transition-all duration-200 {{ $loop->even ? 'bg-base-100' : 'bg-base-200/30' }}">
                            <!-- Mobile Layout -->
                            <div class="lg:hidden">
                                <!-- Calendar Title -->
                                <div class="flex items-center gap-2 mb-2">
                                    <div class="w-4 h-4 rounded-full flex-shrink-0" style="background-color: {{ $calendar->color }};"></div>
                                    <div class="flex-1 min-w-0">
                                        <a href="{{ route('calendars.show', $calendar->id) }}" class="link-primary font-medium hover:link-hover truncate text-lg">
                                            {{ $calendar->title }}
                                        </a>
                                    </div>
                                    @php
                                        $share = $calendar->shares->first();
                                        $permission = $share ? ucfirst($share->permission) : 'View';
                                    @endphp
                                    <div class="badge badge-sm badge-info">
                                        {{ $permission }}
                                    </div>
                                </div>

                                <!-- Owner & Description -->
                                <div class="mb-3 text-sm">
                                    <div class="flex items-center gap-1 mb-1">
                                        <i class="fa-sharp fa-user text-info text-xs"></i>
                                        <span class="text-base-content/80">{{ $calendar->owner->name }}</span>
                                    </div>
                                    @if($calendar->description)
                                        <div class="text-base-content/70">
                                            {{ Str::limit($calendar->description, 100) }}
                                        </div>
                                    @endif
                                </div>

                                <!-- Mobile Action Bar -->
                                <div class="flex gap-2 pt-2 border-t border-base-200/50">
                                    <a href="{{ route('calendars.show', $calendar->id) }}" class="btn btn-primary btn-sm flex-1 gap-1">
                                        <i class="fa-sharp fa-eye"></i>
                                        View
                                    </a>
                                    @if ($share && in_array($share->permission, ['edit', 'manage']))
                                        <a href="{{ route('calendars.edit', $calendar->id) }}" class="btn btn-warning btn-sm flex-1 gap-1">
                                            <i class="fa-sharp fa-pen-to-square"></i>
                                            Edit
                                        </a>
                                    @endif
                                </div>
                            </div>

                            <!-- Desktop Layout -->
                            <div class="hidden lg:grid lg:grid-cols-12 gap-4 items-center">
                                <!-- Color & Title Column (3 cols) -->
                                <div class="lg:col-span-3">
                                    <div class="flex items-center gap-3">
                                        <div class="w-4 h-4 rounded-full flex-shrink-0" style="background-color: {{ $calendar->color }};"></div>
                                        <div class="flex-1 min-w-0">
                                            <a href="{{ route('calendars.show', $calendar->id) }}" class="link-primary font-medium hover:link-hover truncate text-base">
                                                {{ $calendar->title }}
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <!-- Owner Column (2 cols) -->
                                <div class="lg:col-span-2">
                                    <div class="flex items-center gap-1 text-sm">
                                        <i class="fa-sharp fa-user text-info text-xs"></i>
                                        <span class="text-base-content/80 truncate">{{ $calendar->owner->name }}</span>
                                    </div>
                                </div>

                                <!-- Description Column (4 cols) -->
                                <div class="lg:col-span-4">
                                    <div class="text-sm text-base-content/70 truncate">
                                        {{ $calendar->description ?: 'No description' }}
                                    </div>
                                </div>

                                <!-- Permission Column (1 col) -->
                                <div class="lg:col-span-1">
                                    <div class="badge badge-sm badge-info">
                                        {{ $permission }}
                                    </div>
                                </div>

                                <!-- Actions Column (2 cols) -->
                                <div class="lg:col-span-2">
                                    <div class="flex gap-1 justify-center">
                                        <div class="tooltip" data-tip="View Calendar">
                                            <a href="{{ route('calendars.show', $calendar->id) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-primary hover:text-primary-content transition-all">
                                                <i class="fa-sharp fa-eye"></i>
                                            </a>
                                        </div>
                                        @if ($share && in_array($share->permission, ['edit', 'manage']))
                                            <div class="tooltip" data-tip="Edit Calendar">
                                                <a href="{{ route('calendars.edit', $calendar->id) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-warning hover:text-warning-content transition-all">
                                                    <i class="fa-sharp fa-pen-to-square"></i>
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <!-- Empty State -->
                        <div class="text-center text-base-content/70 py-12">
                            <div class="flex flex-col items-center gap-4">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                        <i class="fa-sharp fa-share text-2xl"></i>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <h3 class="text-lg font-medium text-base-content/80">No shared calendars</h3>
                                    <p class="text-sm text-base-content/60 mt-1">
                                        No calendars have been shared with you yet
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Public Calendars Card -->
            @if (!$publicCalendars->isEmpty())
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-accent text-accent-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-globe text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Public Calendars</h4>
                            </div>
                            <div class="flex items-center gap-2 text-sm text-base-content/60">
                                <span>{{ $publicCalendars->count() }} calendar{{ $publicCalendars->count() !== 1 ? 's' : '' }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Public Calendars Section -->
                    <div class="divide-y divide-base-200/50">
                        @foreach ($publicCalendars as $calendar)
                            <!-- Calendar Card Row -->
                            <div class="p-3 lg:p-4 hover:bg-base-200/50 transition-all duration-200 {{ $loop->even ? 'bg-base-100' : 'bg-base-200/30' }}">
                                <!-- Mobile Layout -->
                                <div class="lg:hidden">
                                    <!-- Calendar Title -->
                                    <div class="flex items-center gap-2 mb-2">
                                        <div class="w-4 h-4 rounded-full flex-shrink-0" style="background-color: {{ $calendar->color }};"></div>
                                        <div class="flex-1 min-w-0">
                                            <a href="{{ route('calendars.show', $calendar->id) }}" class="link-primary font-medium hover:link-hover truncate text-lg">
                                                {{ $calendar->title }}
                                            </a>
                                        </div>
                                        <div class="badge badge-sm badge-success">
                                            Public
                                        </div>
                                    </div>

                                    <!-- Owner & Description -->
                                    <div class="mb-3 text-sm">
                                        <div class="flex items-center gap-1 mb-1">
                                            <i class="fa-sharp fa-user text-info text-xs"></i>
                                            <span class="text-base-content/80">{{ $calendar->owner->name }}</span>
                                        </div>
                                        @if($calendar->description)
                                            <div class="text-base-content/70">
                                                {{ Str::limit($calendar->description, 100) }}
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Mobile Action Bar -->
                                    <div class="flex gap-2 pt-2 border-t border-base-200/50">
                                        <a href="{{ route('calendars.show', $calendar->id) }}" class="btn btn-primary btn-sm flex-1 gap-1">
                                            <i class="fa-sharp fa-eye"></i>
                                            View Calendar
                                        </a>
                                    </div>
                                </div>

                                <!-- Desktop Layout -->
                                <div class="hidden lg:grid lg:grid-cols-12 gap-4 items-center">
                                    <!-- Color & Title Column (4 cols) -->
                                    <div class="lg:col-span-4">
                                        <div class="flex items-center gap-3">
                                            <div class="w-4 h-4 rounded-full flex-shrink-0" style="background-color: {{ $calendar->color }};"></div>
                                            <div class="flex-1 min-w-0">
                                                <a href="{{ route('calendars.show', $calendar->id) }}" class="link-primary font-medium hover:link-hover truncate text-base">
                                                    {{ $calendar->title }}
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Owner Column (3 cols) -->
                                    <div class="lg:col-span-3">
                                        <div class="flex items-center gap-1 text-sm">
                                            <i class="fa-sharp fa-user text-info text-xs"></i>
                                            <span class="text-base-content/80 truncate">{{ $calendar->owner->name }}</span>
                                        </div>
                                    </div>

                                    <!-- Description Column (4 cols) -->
                                    <div class="lg:col-span-4">
                                        <div class="text-sm text-base-content/70 truncate">
                                            {{ $calendar->description ?: 'No description' }}
                                        </div>
                                    </div>

                                    <!-- Actions Column (1 col) -->
                                    <div class="lg:col-span-1">
                                        <div class="flex gap-1 justify-center">
                                            <div class="tooltip" data-tip="View Calendar">
                                                <a href="{{ route('calendars.show', $calendar->id) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-primary hover:text-primary-content transition-all">
                                                    <i class="fa-sharp fa-eye"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
