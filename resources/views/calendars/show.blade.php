@php
    $primaryButtons = [
        [
            'type' => 'back-to-index',
            'route' => route('calendars.index'),
            'text' => 'Back to Calendars'
        ]
    ];

    $actionButtons = [];
    if($calendar->owner_id == Auth::id() || $calendar->shares->where('user_id', Auth::id())->where('permission', 'in', ['edit', 'manage'])->count() > 0) {
        $actionButtons[] = [
            'name' => 'Edit Calendar',
            'route' => route('calendars.edit', $calendar->id),
            'icon' => 'fa-sharp fa-pen-to-square',
            'class' => 'btn btn-warning btn-sm gap-2'
        ];
    }
    //if this is a pickup calendar, show route to pickup request index
    if($isPickupCalendar) {
        $actionButtons[] = [
            'name' => 'Pickup Requests',
            'route' => route('pickup-requests.index'),
            'icon' => 'fa-sharp fa-truck-pickup',
            'class' => 'btn btn-info btn-sm gap-2'
        ];
    }
@endphp

<x-app-layout
    page-title="{{ $calendar->title }}"
    page-icon="fa-sharp fa-calendar"
    :primary-buttons="$primaryButtons"
    :action-buttons="$actionButtons"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Calendars', 'route' => 'calendars.index', 'icon' => 'fa-calendar'],
        ['name' => $calendar->title, 'icon' => 'fa-calendar-check']
    ]">

    <div class="py-2 sm:py-4 lg:py-6 space-y-4 sm:space-y-6 lg:space-y-8">
        <!-- Page Content -->
        <div class="mx-auto px-2 sm:px-4 lg:px-6 space-y-4 sm:space-y-6 lg:space-y-8 max-w-7xl">
            <!-- Calendar Info Card - Collapsible on mobile -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-3 sm:px-6 py-3 sm:py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-2 sm:gap-3">
                        <div class="w-5 h-5 sm:w-6 sm:h-6 rounded-full flex-shrink-0" style="background-color: {{ $calendar->color }};"></div>
                        <div class="flex-1 min-w-0">
                            <h4 class="text-base sm:text-lg font-semibold text-base-content truncate">{{ $calendar->title }}</h4>
                            @if($calendar->description)
                                <p class="text-base-content/70 mt-1 text-sm hidden sm:block">{{ $calendar->description }}</p>
                            @else
                                <p class="text-base-content/50 mt-1 italic text-sm hidden sm:block">No description provided</p>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="p-3 sm:p-6">
                    <div class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-user text-info text-sm"></i>
                            <div class="min-w-0">
                                <div class="text-xs sm:text-sm text-base-content/60">Owner</div>
                                <div class="font-medium text-sm sm:text-base truncate">{{ $calendar->owner->name }}</div>
                            </div>
                        </div>

                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-{{ $calendar->is_public ? 'globe' : 'lock' }} text-{{ $calendar->is_public ? 'success' : 'warning' }} text-sm"></i>
                            <div class="min-w-0">
                                <div class="text-xs sm:text-sm text-base-content/60">Visibility</div>
                                <div class="badge badge-xs sm:badge-sm {{ $calendar->is_public ? 'badge-success' : 'badge-warning' }}">
                                    {{ $calendar->is_public ? 'Public' : 'Private' }}
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-calendar-plus text-primary text-sm"></i>
                            <div class="min-w-0">
                                <div class="text-xs sm:text-sm text-base-content/60">Created</div>
                                <div class="font-medium text-sm sm:text-base">{{ $calendar->created_at->format('M d, Y') }}</div>
                            </div>
                        </div>

                        <div class="flex items-center gap-2">
                            <i class="fa-sharp fa-palette text-accent text-sm"></i>
                            <div class="min-w-0">
                                <div class="text-xs sm:text-sm text-base-content/60">Color</div>
                                <div class="flex items-center gap-1 sm:gap-2">
                                    <div class="w-3 h-3 sm:w-4 sm:h-4 rounded-full" style="background-color: {{ $calendar->color }};"></div>
                                    <span class="font-mono text-xs sm:text-sm truncate">{{ $calendar->color }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Shared Users Card - Hidden on mobile by default -->
            @if($calendar->shares->count() > 0)
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-3 sm:px-6 py-3 sm:py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-2 sm:gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-6 sm:w-8 rounded-lg">
                                    <i class="fa-sharp fa-users text-xs sm:text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-base sm:text-lg font-semibold text-base-content">Shared With</h4>
                            <span class="badge badge-xs sm:badge-sm">{{ $calendar->shares->count() }} user{{ $calendar->shares->count() !== 1 ? 's' : '' }}</span>
                        </div>
                    </div>

                    <div class="divide-y divide-base-200/50 max-h-48 sm:max-h-none overflow-y-auto">
                        @foreach($calendar->shares as $share)
                            <div class="p-3 sm:p-4 hover:bg-base-200/50 transition-all duration-200">
                                <div class="flex items-center justify-between gap-2">
                                    <div class="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                                        <div class="avatar avatar-placeholder">
                                            <div class="bg-base-200 text-base-content w-6 sm:w-8 rounded-lg">
                                                <i class="fa-sharp fa-user text-xs sm:text-sm"></i>
                                            </div>
                                        </div>
                                        <div class="min-w-0 flex-1">
                                            <div class="font-medium text-sm sm:text-base truncate">{{ $share->user->name }}</div>
                                            <div class="text-xs sm:text-sm text-base-content/60 truncate">{{ $share->user->email }}</div>
                                        </div>
                                    </div>
                                    <div class="badge {{ $share->permission == 'view' ? 'badge-info' : ($share->permission == 'edit' ? 'badge-warning' : 'badge-success') }} badge-xs sm:badge-sm flex-shrink-0">
                                        {{ ucfirst($share->permission) }}
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Calendar Display Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-3 sm:px-6 py-3 sm:py-4 border-b border-base-300/50">
                    <div class="flex items-center justify-between gap-2">
                        <div class="flex items-center gap-2 sm:gap-3 min-w-0">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-accent text-accent-content w-6 sm:w-8 rounded-lg">
                                    <i class="fa-sharp fa-calendar-days text-xs sm:text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-base sm:text-lg font-semibold text-base-content">Calendar View</h4>
                        </div>
                        @if($calendar->owner_id == Auth::id() || $calendar->shares->where('user_id', Auth::id())->where('permission', 'in', ['edit', 'manage'])->count() > 0)
                            @if($isPickupCalendar)
                            <div class="flex flex-col sm:flex-row gap-1 sm:gap-2">
                                <a href="/pickup-requests/create" class="btn btn-primary btn-xs sm:btn-sm gap-1 sm:gap-2">
                                    <i class="fa-sharp fa-plus text-xs"></i>
                                    <span class="hidden sm:inline">Create Pickup Request</span>
                                    <span class="sm:hidden">Create</span>
                                </a>
                                <button id="add-blockout-event-btn" class="btn btn-error btn-xs sm:btn-sm gap-1 sm:gap-2">
                                    <i class="fa-sharp fa-ban text-xs"></i>
                                    <span class="hidden sm:inline">Add Blockout</span>
                                    <span class="sm:hidden">Block</span>
                                </button>
                            </div>
                            @else
                                <button id="add-event-btn" class="btn btn-primary btn-xs sm:btn-sm gap-1 sm:gap-2">
                                    <i class="fa-sharp fa-plus text-xs"></i>
                                    <span class="hidden sm:inline">Add Event</span>
                                    <span class="sm:hidden">Add</span>
                                </button>
                            @endif
                        @endif
                    </div>
                </div>

                <div class="p-2 sm:p-4 lg:p-6">
                    <!-- Calendar container with improved height for desktop and mobile optimization -->
                    <div id="calendar" class="bg-base-100 rounded-lg min-h-[500px]"></div>
                </div>
            </div>
        </div>
    </div>

    @if($isPickupCalendar)
        <!-- Include Pickup Event Modal -->
        <x-pickup-event-modal />
        <!-- Include Pickup Event Show Modal -->
        <x-pickup-event-show-modal />
        <!-- Include Blockout Event Modal -->
        @include('components.blockout-event-modal')
        <!-- Include Drag-to-Create Dialog -->
        @include('calendars.partials.drag-to-create-dialog')
    @endif

    <!-- Include Event Show Modal -->
    <x-event-show-modal />

    <!-- Event Modal -->
    <div id="event-modal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg mb-4" id="event-modal-title">Add Event</h3>
            <form id="event-form">
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Event Title</span>
                    </label>
                    <input type="text" id="event-title" class="input input-bordered" required>
                </div>

                <div class="form-control mt-4">
                    <label class="label">
                        <span class="label-text">Description</span>
                    </label>
                    <textarea id="event-description" class="textarea textarea-bordered" rows="3"></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Start Date</span>
                        </label>
                        <input type="datetime-local" id="event-start" class="input input-bordered" required>
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">End Date</span>
                        </label>
                        <input type="datetime-local" id="event-end" class="input input-bordered">
                    </div>
                </div>

                <div class="form-control mt-4">
                    <label class="cursor-pointer label justify-start">
                        <input type="checkbox" id="event-all-day" class="checkbox checkbox-primary mr-2">
                        <span class="label-text">All Day Event</span>
                    </label>
                </div>

                <div class="form-control mt-4">
                    <label class="label">
                        <span class="label-text">Location</span>
                    </label>
                    <input type="text" id="event-location" class="input input-bordered">
                </div>

                <div class="form-control mt-4">
                    <label class="label">
                        <span class="label-text">Color</span>
                    </label>
                    <div class="flex space-x-2">
                        <input type="color" id="event-color" class="h-10 w-10 rounded" value="#3788d8">
                        <input type="text" id="event-color-hex" class="input input-bordered flex-1" value="#3788d8" readonly>
                    </div>
                </div>

                <div class="form-control mt-4">
                    <label class="cursor-pointer label justify-start">
                        <input type="checkbox" id="event-recurring" class="checkbox checkbox-primary mr-2">
                        <span class="label-text">Recurring Event</span>
                    </label>
                </div>

                <div id="recurrence-options" class="mt-4 p-4 border rounded-lg" style="display: none;">
                    <h4 class="font-medium mb-2">Recurrence Options</h4>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Frequency</span>
                        </label>
                        <select id="recurrence-frequency" class="select select-bordered w-full">
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                            <option value="yearly">Yearly</option>
                        </select>
                    </div>

                    <div class="form-control mt-2">
                        <label class="label">
                            <span class="label-text">Repeat every</span>
                        </label>
                        <div class="flex items-center">
                            <input type="number" id="recurrence-interval" class="input input-bordered w-20" min="1" value="1">
                            <span class="ml-2" id="interval-label">day(s)</span>
                        </div>
                    </div>

                    <div id="weekly-options" class="form-control mt-2" style="display: none;">
                        <label class="label">
                            <span class="label-text">Repeat on</span>
                        </label>
                        <div class="flex flex-wrap gap-2">
                            <label class="cursor-pointer label justify-start">
                                <input type="checkbox" class="checkbox checkbox-sm checkbox-primary mr-1" value="0">
                                <span class="label-text">Sun</span>
                            </label>
                            <label class="cursor-pointer label justify-start">
                                <input type="checkbox" class="checkbox checkbox-sm checkbox-primary mr-1" value="1">
                                <span class="label-text">Mon</span>
                            </label>
                            <label class="cursor-pointer label justify-start">
                                <input type="checkbox" class="checkbox checkbox-sm checkbox-primary mr-1" value="2">
                                <span class="label-text">Tue</span>
                            </label>
                            <label class="cursor-pointer label justify-start">
                                <input type="checkbox" class="checkbox checkbox-sm checkbox-primary mr-1" value="3">
                                <span class="label-text">Wed</span>
                            </label>
                            <label class="cursor-pointer label justify-start">
                                <input type="checkbox" class="checkbox checkbox-sm checkbox-primary mr-1" value="4">
                                <span class="label-text">Thu</span>
                            </label>
                            <label class="cursor-pointer label justify-start">
                                <input type="checkbox" class="checkbox checkbox-sm checkbox-primary mr-1" value="5">
                                <span class="label-text">Fri</span>
                            </label>
                            <label class="cursor-pointer label justify-start">
                                <input type="checkbox" class="checkbox checkbox-sm checkbox-primary mr-1" value="6">
                                <span class="label-text">Sat</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-control mt-2">
                        <label class="label">
                            <span class="label-text">Ends</span>
                        </label>
                        <div class="flex items-center space-x-2">
                            <select id="recurrence-end-type" class="select select-bordered">
                                <option value="never">Never</option>
                                <option value="after">After</option>
                                <option value="on-date">On date</option>
                            </select>

                            <div id="recurrence-count-container" style="display: none;">
                                <input type="number" id="recurrence-count" class="input input-bordered w-20" min="1" value="10">
                                <span class="ml-1">occurrences</span>
                            </div>

                            <div id="recurrence-until-container" style="display: none;">
                                <input type="date" id="recurrence-until" class="input input-bordered">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-action mt-6">
                    <button type="button" class="btn btn-error" id="delete-event-btn" style="display: none;"><i class="fa-sharp fa-trash mr-2"></i>Delete</button>
                    <button type="button" class="btn btn-ghost" onclick="closeEventModal()"><i class="fa-sharp fa-times mr-2"></i>Cancel</button>
                    <button type="submit" class="btn btn-primary" id="save-event-btn"><i class="fa-sharp fa-save mr-2"></i>Save</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Include FullCalendar library -->
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css' rel='stylesheet' />
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js'></script>

    <!-- Mobile-optimized calendar styles -->
    <style>
        /* Mobile calendar optimizations */
        @media (max-width: 767px) {
            /* Ensure calendar fits in container */
            .fc {
                font-size: 0.875rem;
            }

            /* Optimize header toolbar for mobile */
            .fc-header-toolbar {
                flex-direction: column;
                gap: 0.5rem;
            }

            .fc-toolbar-chunk {
                display: flex;
                justify-content: center;
                align-items: center;
            }

            /* Make buttons smaller on mobile */
            .fc-button {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }

            /* Optimize event display */
            .fc-event {
                font-size: 0.75rem;
                padding: 1px 2px;
            }

            /* List view optimizations - enhanced for mobile */
            .fc-list-event {
                padding: 0.75rem 0.5rem;
                border-left: 4px solid var(--fc-event-border-color, #3788d8);
                margin-bottom: 2px;
            }

            .fc-list-event-title {
                font-size: 0.875rem;
                font-weight: 500;
                line-height: 1.4;
            }

            .fc-list-event-time {
                font-size: 0.75rem;
                font-weight: 600;
                color: #6b7280;
            }

            .fc-list-day-cushion {
                background-color: #f3f4f6;
                padding: 0.5rem;
                font-weight: 600;
                font-size: 0.875rem;
                border-bottom: 1px solid #e5e7eb;
            }

            .fc-list-table {
                border: none;
            }

            .fc-list-day-side-text {
                font-size: 0.75rem;
                color: #6b7280;
            }

            /* Day grid optimizations */
            .fc-daygrid-day-number {
                font-size: 0.875rem;
                padding: 0.25rem;
            }

            /* Time grid optimizations */
            .fc-timegrid-slot-label {
                font-size: 0.75rem;
            }

            /* Ensure calendar scrolls properly */
            .fc-scroller {
                overflow-x: auto !important;
            }
        }

        /* Ensure calendar container takes full height */
        #calendar {
            width: 100%;
            height: 80vh !important;
        }
        

        /* Improve touch targets on mobile */
        @media (max-width: 767px) {
            .fc-event, .fc-daygrid-day, .fc-timegrid-slot {
                min-height: 44px; /* iOS recommended touch target size */
            }
        }
    </style>

    <script>
        // Time zone handling approach:
        // 1. FullCalendar is configured to use the browser's local time zone (timeZone: 'local')
        // 2. Dates from the server are automatically converted to local time for display
        // 3. When sending dates to the server, we send them in local format - Laravel handles timezone conversion
        // This ensures consistent time zone handling throughout the application

        // Global calendar variable for access by pickup event modal
        let calendar;

        document.addEventListener('DOMContentLoaded', function() {
            // Set global calendar ID for pickup events
            window.currentCalendarId = {{ $calendar->id }};

            // Set global permission flag for edit functionality
            window.canEditEvents = {{ ($calendar->owner_id == Auth::id() || $calendar->shares->where('user_id', Auth::id())->whereIn('permission', ['edit', 'manage'])->count() > 0) ? 'true' : 'false' }};

            // Log time zone information for debugging
            console.log('Browser time zone:', Intl.DateTimeFormat().resolvedOptions().timeZone);
            console.log('Server time zone:', '{{ \App\Models\GlobalConfig::getTimeZone() }}');

            // Create a test date to verify time zone handling
            const now = new Date();
            console.log('Current time:', now);
            console.log('Current time ISO:', now.toISOString());
            console.log('Current time components:', {
                year: now.getFullYear(),
                month: now.getMonth() + 1,
                day: now.getDate(),
                hours: now.getHours(),
                minutes: now.getMinutes(),
                utcHours: now.getUTCHours(),
                utcMinutes: now.getUTCMinutes(),
                timezoneOffset: now.getTimezoneOffset()
            });
            // Helper function to show toast notifications
            function showToast(message, type = 'success') {
                const toast = document.getElementById('dynamicToast');
                const alertDiv = toast.querySelector('.alert');
                const icon = toast.querySelector('i');
                const toastMessage = document.getElementById('dynamicToastMessage');

                // Set message
                toastMessage.textContent = message;

                // Update toast type
                alertDiv.className = 'alert shadow-lg';
                icon.className = 'fa-sharp';

                if (type === 'success') {
                    alertDiv.classList.add('alert-success');
                    icon.classList.add('fa-check-circle', 'text-green-500');
                } else if (type === 'error') {
                    alertDiv.classList.add('alert-error');
                    icon.classList.add('fa-circle-xmark', 'text-red-500');
                } else if (type === 'warning') {
                    alertDiv.classList.add('alert-warning');
                    icon.classList.add('fa-triangle-exclamation', 'text-yellow-500');
                } else if (type === 'info') {
                    alertDiv.classList.add('alert-info');
                    icon.classList.add('fa-circle-info', 'text-blue-500');
                }

                // Show toast
                toast.classList.remove('hidden');

                // Hide toast after 5 seconds
                setTimeout(() => {
                    toast.classList.add('hidden');
                }, 5000);
            }

            // Function to generate background events for availability windows
            function generateAvailabilityBackgroundEvents(viewStart, viewEnd, availabilityWindows) {
                const backgroundEvents = [];

                if (!availabilityWindows || Object.keys(availabilityWindows).length === 0) {
                    return backgroundEvents; // No windows configured
                }

                // Iterate through each day in the view range
                const currentDate = new Date(viewStart);
                while (currentDate <= viewEnd) {
                    const dayOfWeek = currentDate.getDay().toString(); // 0=Sunday, 1=Monday, etc.
                    const dayWindows = availabilityWindows[dayOfWeek];

                    if (!dayWindows || dayWindows.length === 0) {
                        // No availability windows for this day - mark entire day as unavailable
                        backgroundEvents.push({
                            title: 'Unavailable',
                            start: new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate()),
                            end: new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() + 1),
                            display: 'background',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)', // Light red background
                            borderColor: 'rgba(239, 68, 68, 0.3)',
                            classNames: ['unavailable-time']
                        });
                    } else {
                        // Sort windows by start time to ensure proper ordering
                        dayWindows.sort((a, b) => {
                            if (!a.start || !b.start) return 0;
                            return a.start.localeCompare(b.start);
                        });

                        // Create background events for unavailable periods
                        // Start of day to first window
                        if (dayWindows.length > 0 && dayWindows[0].start) {
                            const firstWindowStart = dayWindows[0].start.split(':');
                            const firstWindowStartTime = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate(),
                                parseInt(firstWindowStart[0]), parseInt(firstWindowStart[1]));
                            const startOfDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());

                            if (firstWindowStartTime > startOfDay) {
                                backgroundEvents.push({
                                    title: 'Unavailable',
                                    start: startOfDay,
                                    end: firstWindowStartTime,
                                    display: 'background',
                                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                                    borderColor: 'rgba(239, 68, 68, 0.3)',
                                    classNames: ['unavailable-time']
                                });
                            }
                        }

                        // Between windows
                        for (let i = 0; i < dayWindows.length - 1; i++) {
                            const currentWindowEnd = dayWindows[i].end.split(':');
                            const nextWindowStart = dayWindows[i + 1].start.split(':');

                            const currentEndTime = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate(),
                                parseInt(currentWindowEnd[0]), parseInt(currentWindowEnd[1]));
                            const nextStartTime = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate(),
                                parseInt(nextWindowStart[0]), parseInt(nextWindowStart[1]));

                            if (nextStartTime > currentEndTime) {
                                backgroundEvents.push({
                                    title: 'Unavailable',
                                    start: currentEndTime,
                                    end: nextStartTime,
                                    display: 'background',
                                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                                    borderColor: 'rgba(239, 68, 68, 0.3)',
                                    classNames: ['unavailable-time']
                                });
                            }
                        }

                        // Last window to end of day (23:59)
                        if (dayWindows.length > 0 && dayWindows[dayWindows.length - 1].end) {
                            const lastWindowEnd = dayWindows[dayWindows.length - 1].end.split(':');
                            const lastWindowEndTime = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate(),
                                parseInt(lastWindowEnd[0]), parseInt(lastWindowEnd[1]));
                            const endOfDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate(), 23, 59, 59);

                            if (lastWindowEndTime < endOfDay) {
                                backgroundEvents.push({
                                    title: 'Unavailable',
                                    start: lastWindowEndTime,
                                    end: new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() + 1), // Next day start
                                    display: 'background',
                                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                                    borderColor: 'rgba(239, 68, 68, 0.3)',
                                    classNames: ['unavailable-time']
                                });
                            }
                        }
                    }

                    // Move to next day
                    currentDate.setDate(currentDate.getDate() + 1);
                }

                return backgroundEvents;
            }

            // Function to check if an entire event time range is within availability windows
            function checkEventAvailability(startDateTime, endDateTime, availabilityWindows) {
                if (!availabilityWindows || Object.keys(availabilityWindows).length === 0) {
                    return true; // No windows configured, allow all times
                }

                // If no end time provided, use start time + default duration
                if (!endDateTime) {
                    const defaultDuration = {{ \App\Models\GlobalConfig::getPickupEventDuration() }}; // minutes
                    endDateTime = new Date(startDateTime.getTime() + (defaultDuration * 60 * 1000));
                }

                const dayOfWeek = startDateTime.getDay().toString();
                const dayWindows = availabilityWindows[dayOfWeek];

                if (!dayWindows || dayWindows.length === 0) {
                    return false; // No windows for this day
                }

                const startTimeString = startDateTime.getHours().toString().padStart(2, '0') + ':' +
                                       startDateTime.getMinutes().toString().padStart(2, '0');
                const endTimeString = endDateTime.getHours().toString().padStart(2, '0') + ':' +
                                     endDateTime.getMinutes().toString().padStart(2, '0');

                console.log('Checking event availability:', {
                    day: dayOfWeek,
                    startTime: startTimeString,
                    endTime: endTimeString,
                    windows: dayWindows
                });

                // Check if the entire event fits within any availability window
                for (const window of dayWindows) {
                    if (window.start && window.end) {
                        // Event must start at or after window start AND end at or before window end
                        const fitsInWindow = startTimeString >= window.start && endTimeString <= window.end;
                        console.log(`Window ${window.start}-${window.end}: fits=${fitsInWindow}`);
                        if (fitsInWindow) {
                            return true;
                        }
                    }
                }

                console.log('Event does not fit in any availability window');
                return false;
            }

            // Legacy function for backward compatibility - now uses the event-based check
            function checkTimeAvailability(dateTime, availabilityWindows) {
                return checkEventAvailability(dateTime, null, availabilityWindows);
            }

            // Log the current time in different formats for debugging
            const testDate = new Date('2023-04-15T09:00:00Z'); // 9 AM UTC
            console.log('Test date (9 AM UTC):', testDate);
            console.log('Test date local hours:', testDate.getHours());
            console.log('Test date UTC hours:', testDate.getUTCHours());
            console.log('Expected local hours (MDT = UTC-6):', 3); // 9 AM UTC = 3 AM MDT

            // Initialize FullCalendar
            const calendarEl = document.getElementById('calendar');

            // Set initial date if provided
            @if(isset($initialDate) && $initialDate)
                const initialDate = '{{ $initialDate }}';
                console.log('Setting initial date to:', initialDate);
            @else
                const initialDate = null;
            @endif

            calendar = new FullCalendar.Calendar(calendarEl, {
                // Mobile-responsive initial view - defaults to list view on mobile
                initialView: window.innerWidth < 768 ? 'listWeek' : (initialDate ? 'timeGridWeek' : 'dayGridMonth'),
                initialDate: initialDate || undefined,
                // Mobile-optimized header toolbar - simplified for mobile
                headerToolbar: {
                    left: window.innerWidth < 768 ? 'prev,next' : 'prev,next today',
                    center: 'title',
                    right: window.innerWidth < 768 ? 'listWeek,dayGridMonth' : 'dayGridMonth,timeGridWeek,timeGridDay,listMonth'
                },
                // Custom button text for mobile
                buttonText: {
                    listWeek: window.innerWidth < 768 ? 'Weekly List' : 'List',
                    dayGridMonth: window.innerWidth < 768 ? 'Month' : 'Month',
                    timeGridWeek: 'Week',
                    timeGridDay: 'Day',
                    listMonth: 'List',
                    today: 'Today',
                    prev: 'Prev',
                    next: 'Next'
                },
                // Mobile-specific settings
                height: window.innerWidth < 768 ? 'auto' : '600px',
                aspectRatio: window.innerWidth < 768 ? 1.0 : undefined, // Only use aspect ratio on mobile
                timeZone: 'local', // Use the browser's local timezone
                displayEventTime: true,
                displayEventEnd: true,
                // Mobile event display optimization
                dayMaxEvents: window.innerWidth < 768 ? 2 : true,
                moreLinkClick: 'popover',
                eventDisplay: window.innerWidth < 768 ? 'block' : 'auto',
                events: function(info, successCallback, failureCallback) {
                    // Fetch events from the server for the current view's date range
                    fetch(`/events?calendar_id={{ $calendar->id }}&start=${info.startStr}&end=${info.endStr}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Network response was not ok');
                            }
                            return response.json();
                        })
                        .then(data => {
                            @if($isPickupCalendar)
                                // Add availability window background events for pickup calendar
                                const availabilityWindows = @json(\App\Models\GlobalConfig::getPickupAvailabilityWindows());
                                const backgroundEvents = generateAvailabilityBackgroundEvents(info.start, info.end, availabilityWindows);
                                data = data.concat(backgroundEvents);
                            @endif
                            successCallback(data);
                        })
                        .catch(error => {
                            console.error('Error fetching events:', error);
                            failureCallback(error);
                        });
                },
                editable: {{ ($calendar->owner_id == Auth::id() || $calendar->shares->where('user_id', Auth::id())->whereIn('permission', ['edit', 'manage'])->count() > 0) ? 'true' : 'false' }},
                selectable: {{ ($calendar->owner_id == Auth::id() || $calendar->shares->where('user_id', Auth::id())->whereIn('permission', ['edit', 'manage'])->count() > 0) ? 'true' : 'false' }},
                selectMirror: true,
                eventTimeFormat: {
                    hour: 'numeric',
                    minute: 'numeric',
                    meridiem: 'short'
                },
                // Mobile-specific view settings
                views: {
                    listWeek: {
                        titleFormat: { month: 'short', day: 'numeric' }
                    },
                    dayGridMonth: {
                        dayMaxEvents: window.innerWidth < 768 ? 2 : 4
                    },
                    timeGridWeek: {
                        slotMinTime: '06:00:00',
                        slotMaxTime: '22:00:00',
                        allDaySlot: window.innerWidth >= 768
                    },
                    timeGridDay: {
                        slotMinTime: '06:00:00',
                        slotMaxTime: '22:00:00'
                    }
                },
                select: function(info) {
                    // Only allow event creation if user has permission
                    @if($calendar->owner_id == Auth::id() || $calendar->shares->where('user_id', Auth::id())->whereIn('permission', ['edit', 'manage'])->count() > 0)
                    // Log the selected dates for debugging
                    console.log('Selected start:', info.start);
                    console.log('Selected end:', info.end);
                    console.log('Selected start ISO:', info.start.toISOString());
                    console.log('Selected end ISO:', info.end.toISOString());
                    console.log('Selected start components:', {
                        year: info.start.getFullYear(),
                        month: info.start.getMonth() + 1,
                        day: info.start.getDate(),
                        hours: info.start.getHours(),
                        minutes: info.start.getMinutes(),
                        utcHours: info.start.getUTCHours(),
                        utcMinutes: info.start.getUTCMinutes(),
                        timezoneOffset: info.start.getTimezoneOffset()
                    });

                    // With timeZone: 'local', FullCalendar provides dates in the local timezone
                    // We can use these dates directly
                    const localStart = info.start;
                    const localEnd = info.end;

                    console.log('Local start for modal:', localStart);
                    console.log('Local end for modal:', localEnd);
                    console.log('Local start components:', {
                        year: localStart.getFullYear(),
                        month: localStart.getMonth() + 1,
                        day: localStart.getDate(),
                        hours: localStart.getHours(),
                        minutes: localStart.getMinutes()
                    });

                    @if($isPickupCalendar)
                        // For pickup calendars, show drag-to-create dialog
                        showDragToCreateDialog(localStart, localEnd);
                    @else
                        openEventModal('add', {
                            start: localStart,
                            end: localEnd,
                            allDay: info.allDay
                        });
                    @endif
                    @endif
                },
                eventClick: function(info) {
                    // Log the clicked event for debugging
                    console.log('Clicked event:', info.event);
                    console.log('Event start:', info.event.start);
                    console.log('Event end:', info.event.end);
                    console.log('Event start ISO:', info.event.start.toISOString());
                    console.log('Event start components:', {
                        year: info.event.start.getFullYear(),
                        month: info.event.start.getMonth() + 1,
                        day: info.event.start.getDate(),
                        hours: info.event.start.getHours(),
                        minutes: info.event.start.getMinutes(),
                        utcHours: info.event.start.getUTCHours(),
                        utcMinutes: info.event.start.getUTCMinutes()
                    });

                    // Fetch full event details
                    fetch(`/events/${info.event.id}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Network response was not ok');
                            }
                            return response.json();
                        })
                        .then(eventData => {
                            // Log the raw event data for debugging
                            console.log('Raw event data:', eventData);
                            console.log('Server timezone:', '{{ \App\Models\GlobalConfig::getTimeZone() }}');

                            // Parse the dates from the server
                            // The server sends dates in UTC format (with Z suffix)
                            const startDateStr = eventData.start_date;
                            const endDateStr = eventData.end_date;

                            console.log('Start date string from server:', startDateStr);
                            console.log('End date string from server:', endDateStr);

                            // Parse dates from the server
                            // The server sends dates in UTC format (with Z suffix)
                            const extractTimeComponents = (dateStr) => {
                                if (!dateStr) return null;

                                // Parse the UTC date string
                                const d = new Date(dateStr);

                                console.log('UTC date string from server:', dateStr);
                                console.log('Parsed date object:', d);
                                console.log('UTC components:', {
                                    year: d.getUTCFullYear(),
                                    month: d.getUTCMonth() + 1,
                                    day: d.getUTCDate(),
                                    hours: d.getUTCHours(),
                                    minutes: d.getUTCMinutes()
                                });
                                console.log('Local components:', {
                                    year: d.getFullYear(),
                                    month: d.getMonth() + 1,
                                    day: d.getDate(),
                                    hours: d.getHours(),
                                    minutes: d.getMinutes()
                                });

                                // When the server sends a UTC date (with Z suffix),
                                // JavaScript automatically converts it to local time
                                // We can use this date object directly
                                return d;
                            };

                            // Create date objects without timezone conversion
                            const startDate = extractTimeComponents(startDateStr);
                            const endDate = extractTimeComponents(endDateStr);

                            // Log the date objects and their components for debugging
                            console.log('Start date object:', startDate);
                            console.log('Start date components:', {
                                year: startDate.getFullYear(),
                                month: startDate.getMonth() + 1,
                                day: startDate.getDate(),
                                hours: startDate.getHours(),
                                minutes: startDate.getMinutes(),
                                timezoneOffset: startDate.getTimezoneOffset()
                            });

                            console.log('Start date object:', startDate);
                            console.log('End date object:', endDate);

                            @if($isPickupCalendar)
                                // Check if this is a blockout event
                                if (eventData.is_blockout_event) {
                                    openBlockoutEventModal('edit', {
                                        id: eventData.id,
                                        title: eventData.title,
                                        description: eventData.description || '',
                                        location: eventData.location || '',
                                        start: startDate,
                                        end: endDate,
                                        allDay: eventData.all_day
                                    });
                                }
                                // Check if this is a pickup event
                                else if (eventData.is_pickup_event) {
                                    openPickupEventShowModal({
                                        id: eventData.id,
                                        title: eventData.title,
                                        description: eventData.description || '',
                                        start: startDate,
                                        end: endDate,
                                        allDay: eventData.all_day,
                                        location: eventData.location || '',
                                        color: eventData.color || '{{ $calendar->color }}',
                                        // Pickup-specific fields from pickup request
                                        customer_id: eventData.customer_id,
                                        customer_name: eventData.customer?.name || 'Unknown Customer',
                                        pickup_request_id: eventData.pickup_request_id,
                                        pickup_request_status: eventData.pickup_request_status,
                                        pickup_details: eventData.pickup_details,
                                        assigned_driver: eventData.assigned_driver,
                                        // Backward compatibility fields
                                        pickup_address: eventData.pickup_address,
                                        pickup_items: eventData.pickup_items,
                                        contact_name: eventData.contact_name,
                                        contact_phone: eventData.contact_phone,
                                        contact_email: eventData.contact_email,
                                        pickup_notes: eventData.pickup_notes
                                    });
                                } else {
                                    openEventShowModal({
                                        id: eventData.id,
                                        title: eventData.title,
                                        description: eventData.description || '',
                                        start: startDate,
                                        end: endDate,
                                        allDay: eventData.all_day,
                                        location: eventData.location || '',
                                        color: eventData.color || '{{ $calendar->color }}',
                                        recurrence: eventData.recurrence
                                    });
                                }
                            @else
                                openEventShowModal({
                                    id: eventData.id,
                                    title: eventData.title,
                                    description: eventData.description || '',
                                    start: startDate,
                                    end: endDate,
                                    allDay: eventData.all_day,
                                    location: eventData.location || '',
                                    color: eventData.color || '{{ $calendar->color }}',
                                    recurrence: eventData.recurrence
                                });
                            @endif
                        })
                        .catch(error => {
                            console.error('Error fetching event details:', error);
                            alert('Could not load event details. Please try again.');
                        });
                },
                eventDrop: function(info) {
                    // Handle event drag and drop (update date/time)
                    @if($calendar->owner_id == Auth::id() || $calendar->shares->where('user_id', Auth::id())->whereIn('permission', ['edit', 'manage'])->count() > 0)
                    updateEventDates(info.event);
                    @else
                    info.revert();
                    @endif
                },
                eventResize: function(info) {
                    // Handle event resize (update duration)
                    @if($calendar->owner_id == Auth::id() || $calendar->shares->where('user_id', Auth::id())->whereIn('permission', ['edit', 'manage'])->count() > 0)
                    updateEventDates(info.event);
                    @else
                    info.revert();
                    @endif
                }
            });

            // Render the calendar
            calendar.render();


            // Handle window resize for mobile responsiveness
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(function() {
                    // Update calendar options based on new screen size
                    const isMobile = window.innerWidth < 768;

                    // Update header toolbar - simplified for mobile
                    calendar.setOption('headerToolbar', {
                        left: isMobile ? 'prev,next' : 'prev,next today',
                        center: 'title',
                        right: isMobile ? 'listWeek,dayGridMonth' : 'dayGridMonth,timeGridWeek,timeGridDay,listMonth'
                    });

                    // Update button text for mobile
                    calendar.setOption('buttonText', {
                        listWeek: isMobile ? 'Weekly List' : 'List',
                        dayGridMonth: isMobile ? 'Month' : 'Month',
                        timeGridWeek: 'Week',
                        timeGridDay: 'Day',
                        listMonth: 'List',
                        today: 'Today',
                        prev: 'Prev',
                        next: 'Next'
                    });

                    // Update height and aspect ratio
                    calendar.setOption('height', isMobile ? 'auto' : '200px');
                    calendar.setOption('aspectRatio', isMobile ? 1.0 : undefined);

                    // Update event time format
                    calendar.setOption('eventTimeFormat', {
                        hour: 'numeric',
                        minute: 'numeric',
                        meridiem: 'short'
                    });

                    // Update view-specific settings
                    calendar.setOption('views', {
                        listWeek: {
                            titleFormat: { month: 'short', day: 'numeric' }
                        },
                        dayGridMonth: {
                            dayMaxEvents: isMobile ? 2 : 4
                        },
                        timeGridWeek: {
                            slotMinTime: '06:00:00',
                            slotMaxTime: '22:00:00',
                            allDaySlot: !isMobile
                        },
                        timeGridDay: {
                            slotMinTime: '06:00:00',
                            slotMaxTime: '22:00:00'
                        }
                    });

                    // Switch to mobile-friendly view if needed - prefer list view on mobile
                    const currentView = calendar.view.type;
                    if (isMobile && (currentView === 'timeGridWeek' || currentView === 'timeGridDay' || currentView === 'dayGridMonth')) {
                        calendar.changeView('listWeek');
                    } else if (!isMobile && (currentView === 'listWeek' || currentView === 'listMonth')) {
                        calendar.changeView('dayGridMonth');
                    }
                }, 250);
            });

            // Function to update event dates after drag or resize
            function updateEventDates(event) {
                // Log the event object for debugging
                console.log('Updating event dates for event:', event);
                console.log('Event start:', event.start);
                console.log('Event end:', event.end);

                // Format dates for the server - send in local format
                // Laravel handles timezone conversion automatically
                const formatDateForServer = (date) => {
                    if (!date) return null;

                    // Format as local datetime string (YYYY-MM-DDTHH:mm:ss)
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    const seconds = String(date.getSeconds()).padStart(2, '0');
                    
                    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
                };

                const startDate = formatDateForServer(event.start);
                const endDate = event.end ? formatDateForServer(event.end) : null;

                console.log('Formatted start date:', startDate);
                console.log('Formatted end date:', endDate);

                const eventData = {
                    title: event.title,
                    start_date: startDate,
                    end_date: endDate,
                    all_day: event.allDay
                };

                fetch(`/events/${event.id}/dates`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(eventData)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Event updated successfully:', data);
                })
                .catch(error => {
                    console.error('Error updating event:', error);
                    showToast('Could not update event. Please try again.', 'error');
                    calendar.refetchEvents(); // Refresh events to revert changes
                });
            }

            // Make calendar globally accessible for pickup event modal
            window.calendar = calendar;

            @if($isPickupCalendar)
            // Make availability checking functions globally accessible for pickup modal
            window.checkEventAvailability = checkEventAvailability;
            window.availabilityWindows = @json(\App\Models\GlobalConfig::getPickupAvailabilityWindows());
            @endif

            // Event Modal Functionality
            const eventModal = document.getElementById('event-modal');
            const eventForm = document.getElementById('event-form');
            const eventModalTitle = document.getElementById('event-modal-title');
            const eventTitleInput = document.getElementById('event-title');
            const eventDescriptionInput = document.getElementById('event-description');
            const eventStartInput = document.getElementById('event-start');
            const eventEndInput = document.getElementById('event-end');
            const eventAllDayInput = document.getElementById('event-all-day');
            const eventLocationInput = document.getElementById('event-location');
            const eventColorInput = document.getElementById('event-color');
            const eventColorHexInput = document.getElementById('event-color-hex');
            const deleteEventBtn = document.getElementById('delete-event-btn');
            const addEventBtn = document.getElementById('add-event-btn');

            let currentEvent = null;
            let currentAction = 'add';

            // Color picker synchronization
            eventColorInput.addEventListener('input', function() {
                eventColorHexInput.value = this.value;
            });

            // Recurrence options handling
            const eventRecurringCheckbox = document.getElementById('event-recurring');
            const recurrenceOptions = document.getElementById('recurrence-options');
            const recurrenceFrequency = document.getElementById('recurrence-frequency');
            const weeklyOptions = document.getElementById('weekly-options');
            const intervalLabel = document.getElementById('interval-label');
            const recurrenceEndType = document.getElementById('recurrence-end-type');
            const recurrenceCountContainer = document.getElementById('recurrence-count-container');
            const recurrenceUntilContainer = document.getElementById('recurrence-until-container');

            // Toggle recurrence options visibility
            eventRecurringCheckbox.addEventListener('change', function() {
                recurrenceOptions.style.display = this.checked ? 'block' : 'none';
            });

            // Update interval label based on frequency
            recurrenceFrequency.addEventListener('change', function() {
                const frequency = this.value;

                // Show/hide weekly options
                weeklyOptions.style.display = frequency === 'weekly' ? 'block' : 'none';

                // Update interval label
                switch (frequency) {
                    case 'daily':
                        intervalLabel.textContent = 'day(s)';
                        break;
                    case 'weekly':
                        intervalLabel.textContent = 'week(s)';
                        break;
                    case 'monthly':
                        intervalLabel.textContent = 'month(s)';
                        break;
                    case 'yearly':
                        intervalLabel.textContent = 'year(s)';
                        break;
                }
            });

            // Toggle end date options
            recurrenceEndType.addEventListener('change', function() {
                const endType = this.value;

                recurrenceCountContainer.style.display = endType === 'after' ? 'block' : 'none';
                recurrenceUntilContainer.style.display = endType === 'on-date' ? 'block' : 'none';
            });

            // Add Event button click
            if (addEventBtn) {
                addEventBtn.addEventListener('click', function() {
                    // Create dates in local time zone
                    const now = new Date();
                    const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);

                    // Log the dates for debugging
                    console.log('Add event - now:', now);
                    console.log('Add event - one hour later:', oneHourLater);
                    console.log('Now components:', {
                        year: now.getFullYear(),
                        month: now.getMonth() + 1,
                        day: now.getDate(),
                        hours: now.getHours(),
                        minutes: now.getMinutes(),
                        timezoneOffset: now.getTimezoneOffset()
                    });

                    // Create date objects in local time
                    // We'll use these directly since they're already in local time
                    const localNow = now;
                    const localOneHourLater = oneHourLater;

                    // Log the local time components for debugging
                    console.log('Local now components:', {
                        year: localNow.getFullYear(),
                        month: localNow.getMonth() + 1,
                        day: localNow.getDate(),
                        hours: localNow.getHours(),
                        minutes: localNow.getMinutes()
                    });

                    console.log('Local now for modal:', localNow);
                    console.log('Local one hour later for modal:', localOneHourLater);

                    openEventModal('add', {
                        start: localNow,
                        end: localOneHourLater,
                        allDay: false
                    });
                });
            }

            // Add Blockout Event button click handler
            const addBlockoutEventBtn = document.getElementById('add-blockout-event-btn');
            if (addBlockoutEventBtn) {
                addBlockoutEventBtn.addEventListener('click', function() {
                    // Create dates in local time zone
                    const now = new Date();
                    const defaultDuration = {{ \App\Models\GlobalConfig::getPickupEventDuration() }}; // minutes
                    const endTime = new Date(now.getTime() + (defaultDuration * 60 * 1000));

                    openBlockoutEventModal('add', {
                        start: now,
                        end: endTime,
                        allDay: false
                    });
                });
            }

            // Event form submission
            eventForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get form values
                const title = eventTitleInput.value;
                const description = eventDescriptionInput.value;
                const startDateStr = eventStartInput.value; // Format: YYYY-MM-DDTHH:MM (local time)
                const endDateStr = eventEndInput.value;
                const allDay = eventAllDayInput.checked;
                const location = eventLocationInput.value;
                const color = eventColorInput.value;

                // Log the form input values for debugging
                console.log('Form start date (local):', startDateStr);
                console.log('Form end date (local):', endDateStr);

                // Create Date objects from the form values (in local time)
                const startDate = startDateStr ? new Date(startDateStr) : null;
                const endDate = endDateStr ? new Date(endDateStr) : null;

                // Log the date objects for debugging
                console.log('Start date object from form:', startDate);
                console.log('End date object from form:', endDate);

                // No timezone conversion needed - Laravel handles it automatically
                // Send the local datetime strings as-is
                console.log('Start date for server:', startDateStr);
                console.log('End date for server:', endDateStr);

                // Prepare event data
                // Send dates in local format - Laravel will handle timezone conversion
                const eventData = {
                    title: title,
                    description: description,
                    start_date: startDateStr, // Send local datetime string
                    end_date: endDateStr || null, // Send local datetime string
                    all_day: allDay,
                    location: location,
                    color: color,
                    calendar_id: {{ $calendar->id }}
                };

                // Add recurrence data if enabled
                if (eventRecurringCheckbox.checked) {
                    const frequency = recurrenceFrequency.value;
                    const interval = parseInt(document.getElementById('recurrence-interval').value) || 1;
                    const endType = recurrenceEndType.value;

                    // Prepare recurrence object
                    const recurrence = {
                        frequency: frequency,
                        interval: interval
                    };

                    // Add days of week for weekly recurrence
                    if (frequency === 'weekly') {
                        const daysOfWeek = [];
                        document.querySelectorAll('#weekly-options input[type="checkbox"]:checked').forEach(checkbox => {
                            daysOfWeek.push(parseInt(checkbox.value));
                        });

                        if (daysOfWeek.length > 0) {
                            recurrence.days_of_week = daysOfWeek;
                        }
                    }

                    // Add end conditions
                    if (endType === 'after') {
                        recurrence.count = parseInt(document.getElementById('recurrence-count').value) || 10;
                    } else if (endType === 'on-date') {
                        recurrence.until_date = document.getElementById('recurrence-until').value;
                    }

                    eventData.recurrence = recurrence;
                }

                // Show loading state
                const saveButton = document.getElementById('save-event-btn');
                const originalButtonText = saveButton.innerHTML;
                saveButton.innerHTML = '<i class="fa-sharp fa-spinner fa-spin mr-2"></i> Saving...';
                saveButton.disabled = true;

                try {
                    if (currentAction === 'add') {
                        // Create new event
                        fetch('/events', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify(eventData)
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Network response was not ok');
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Event created successfully:', data);
                            // Show success toast
                            showToast('Event created successfully!');

                            // Refresh events and close modal
                            calendar.refetchEvents();
                            eventModal.classList.remove('modal-open');
                            eventForm.reset();
                            currentEvent = null;
                        })
                        .catch(error => {
                            console.error('Error creating event:', error);
                            showToast('Could not create event. Please try again.', 'error');
                        })
                        .finally(() => {
                            // Always reset the button state
                            saveButton.innerHTML = originalButtonText;
                            saveButton.disabled = false;
                        });
                    } else if (currentAction === 'edit' && currentEvent) {
                        // Update existing event
                        fetch(`/events/${currentEvent.id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify(eventData)
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Network response was not ok');
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Event updated successfully:', data);
                            // Show success toast
                            showToast('Event updated successfully!');

                            // Refresh events and close modal
                            calendar.refetchEvents();
                            eventModal.classList.remove('modal-open');
                            eventForm.reset();
                            currentEvent = null;
                        })
                        .catch(error => {
                            console.error('Error updating event:', error);
                            showToast('Could not update event. Please try again.', 'error');
                        })
                        .finally(() => {
                            // Always reset the button state
                            saveButton.innerHTML = originalButtonText;
                            saveButton.disabled = false;
                        });
                    }
                } catch (error) {
                    console.error('Unexpected error:', error);
                    alert('An unexpected error occurred. Please try again.');
                    saveButton.innerHTML = originalButtonText;
                    saveButton.disabled = false;
                }
            });

            // Delete event button click
            deleteEventBtn.addEventListener('click', function() {
                if (currentEvent && currentEvent.id) {
                    if (confirm('Are you sure you want to delete this event?')) {
                        // Show loading state
                        const deleteButton = document.getElementById('delete-event-btn');
                        const originalButtonText = deleteButton.innerHTML;
                        deleteButton.innerHTML = '<i class="fa-sharp fa-spinner fa-spin mr-2"></i> Deleting...';
                        deleteButton.disabled = true;

                        try {
                            // Delete the event via AJAX
                            fetch(`/events/${currentEvent.id}`, {
                                method: 'DELETE',
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                }
                            })
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error('Network response was not ok');
                                }
                                return response.json();
                            })
                            .then(data => {
                                console.log('Event deleted successfully:', data);

                                // Show success toast
                                showToast('Event deleted successfully!');

                                // Refresh events and close modal
                                calendar.refetchEvents();
                                eventModal.classList.remove('modal-open');
                                eventForm.reset();
                                currentEvent = null;
                            })
                            .catch(error => {
                                console.error('Error deleting event:', error);
                                showToast('Could not delete event. Please try again.', 'error');
                            })
                            .finally(() => {
                                // Always reset the button state
                                deleteButton.innerHTML = originalButtonText;
                                deleteButton.disabled = false;
                            });
                        } catch (error) {
                            console.error('Unexpected error:', error);
                            alert('An unexpected error occurred. Please try again.');
                            deleteButton.innerHTML = originalButtonText;
                            deleteButton.disabled = false;
                        }
                    }
                }
            });

            // Open event modal
            window.openEventModal = function(action, eventData) {
                currentAction = action;
                currentEvent = eventData;

                eventModalTitle.textContent = action === 'add' ? 'Add Event' : 'Edit Event';
                eventTitleInput.value = eventData.title || '';
                eventDescriptionInput.value = eventData.description || '';

                // Format date for datetime-local input
                const formatDate = (date) => {
                    if (!date) return '';

                    // Create a new date object to ensure we're working with the correct time
                    // The date parameter could be a string or a Date object
                    const d = new Date(date);

                    // Log the input date and browser's timezone for debugging
                    console.log('Input date:', date);
                    console.log('Browser timezone:', Intl.DateTimeFormat().resolvedOptions().timeZone);
                    console.log('Date object created:', d);
                    console.log('Date object ISO:', d.toISOString());
                    console.log('Date object local string:', d.toString());
                    console.log('Date object components:', {
                        year: d.getFullYear(),
                        month: d.getMonth() + 1,
                        day: d.getDate(),
                        hours: d.getHours(),
                        minutes: d.getMinutes(),
                        utcHours: d.getUTCHours(),
                        utcMinutes: d.getUTCMinutes()
                    });

                    // For datetime-local input, we need to use the local date and time
                    // Get local parts of the date
                    const year = d.getFullYear();
                    const month = String(d.getMonth() + 1).padStart(2, '0');
                    const day = String(d.getDate()).padStart(2, '0');
                    const hours = String(d.getHours()).padStart(2, '0');
                    const minutes = String(d.getMinutes()).padStart(2, '0');

                    // Format as YYYY-MM-DDTHH:MM
                    const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;

                    console.log('Formatted date for input:', formattedDate);
                    return formattedDate;
                };

                eventStartInput.value = formatDate(eventData.start);
                eventEndInput.value = formatDate(eventData.end);
                eventAllDayInput.checked = eventData.allDay || false;
                eventLocationInput.value = eventData.location || '';
                eventColorInput.value = eventData.color || '#3788d8';
                eventColorHexInput.value = eventData.color || '#3788d8';

                // Reset recurrence options
                eventRecurringCheckbox.checked = false;
                recurrenceOptions.style.display = 'none';
                recurrenceFrequency.value = 'daily';
                document.getElementById('recurrence-interval').value = '1';
                intervalLabel.textContent = 'day(s)';
                weeklyOptions.style.display = 'none';
                document.querySelectorAll('#weekly-options input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = false;
                });
                recurrenceEndType.value = 'never';
                recurrenceCountContainer.style.display = 'none';
                recurrenceUntilContainer.style.display = 'none';
                document.getElementById('recurrence-count').value = '10';
                document.getElementById('recurrence-until').value = '';

                // Set recurrence data if available
                if (eventData.recurrence) {
                    eventRecurringCheckbox.checked = true;
                    recurrenceOptions.style.display = 'block';

                    // Set frequency
                    recurrenceFrequency.value = eventData.recurrence.frequency;
                    document.getElementById('recurrence-interval').value = eventData.recurrence.interval || 1;

                    // Update interval label
                    switch (eventData.recurrence.frequency) {
                        case 'daily':
                            intervalLabel.textContent = 'day(s)';
                            break;
                        case 'weekly':
                            intervalLabel.textContent = 'week(s)';
                            weeklyOptions.style.display = 'block';

                            // Check days of week
                            if (eventData.recurrence.days_of_week && eventData.recurrence.days_of_week.length > 0) {
                                eventData.recurrence.days_of_week.forEach(day => {
                                    const checkbox = document.querySelector(`#weekly-options input[value="${day}"]`);
                                    if (checkbox) checkbox.checked = true;
                                });
                            }
                            break;
                        case 'monthly':
                            intervalLabel.textContent = 'month(s)';
                            break;
                        case 'yearly':
                            intervalLabel.textContent = 'year(s)';
                            break;
                    }

                    // Set end conditions
                    if (eventData.recurrence.count) {
                        recurrenceEndType.value = 'after';
                        recurrenceCountContainer.style.display = 'block';
                        document.getElementById('recurrence-count').value = eventData.recurrence.count;
                    } else if (eventData.recurrence.until_date) {
                        recurrenceEndType.value = 'on-date';
                        recurrenceUntilContainer.style.display = 'block';
                        document.getElementById('recurrence-until').value = eventData.recurrence.until_date.substring(0, 10); // YYYY-MM-DD
                    }
                }

                // Show/hide delete button
                deleteEventBtn.style.display = action === 'edit' ? 'block' : 'none';

                // Show modal
                eventModal.classList.add('modal-open');
            };

            // Close event modal
            window.closeEventModal = function() {
                // Reset form and state
                eventModal.classList.remove('modal-open');
                eventForm.reset();
                currentEvent = null;

                // Reset button states
                const saveButton = document.getElementById('save-event-btn');
                const deleteButton = document.getElementById('delete-event-btn');

                if (saveButton) {
                    saveButton.innerHTML = '<i class="fa-sharp fa-save mr-2"></i> Save';
                    saveButton.disabled = false;
                }

                if (deleteButton) {
                    deleteButton.innerHTML = 'Delete';
                    deleteButton.disabled = false;
                }

                // Reset recurrence options
                if (eventRecurringCheckbox) {
                    eventRecurringCheckbox.checked = false;
                    recurrenceOptions.style.display = 'none';
                }
            };
        });
    </script>
</x-app-layout>
