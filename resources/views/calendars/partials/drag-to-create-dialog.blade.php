<!-- Drag-to-Create Dialog for Pickup Calendar -->
<dialog id="dragToCreateDialog" class="modal">
    <div class="modal-box">
        <!-- Dialog Icon -->
        <div class="flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full bg-primary/10">
            <i class="fa-sharp fa-calendar-plus text-primary text-2xl"></i>
        </div>
        
        <!-- Dialog Title -->
        <h3 class="text-lg font-bold text-center mb-4">Create Event</h3>
        
        <!-- Time Range Display -->
        <div class="bg-base-200 rounded-lg p-4 mb-6 text-center">
            <div class="text-sm text-base-content/60 mb-1">Selected Time Range</div>
            <div class="font-medium text-base-content" id="dragTimeRange">
                <!-- Time range will be populated by JavaScript -->
            </div>
        </div>
        
        <!-- Dialog Content -->
        <div class="py-4 text-center">
            <p class="text-base-content/70 mb-6">What would you like to create for this time slot?</p>
            
            <!-- Action Buttons -->
            <div class="space-y-3">
                <button type="button" class="btn btn-primary btn-block gap-2" id="createPickupRequestBtn">
                    <i class="fa-sharp fa-truck-pickup"></i>
                    Create Pickup Request
                </button>
                
                <button type="button" class="btn btn-error btn-block gap-2" id="blockOutTimeBtn">
                    <i class="fa-sharp fa-ban"></i>
                    Block Out Time
                </button>
                
                <button type="button" class="btn btn-ghost btn-block gap-2" id="cancelDragCreateBtn">
                    <i class="fa-sharp fa-times"></i>
                    Cancel
                </button>
            </div>
        </div>
    </div>
    
    <!-- Backdrop to close modal -->
    <form method="dialog" class="modal-backdrop">
        <button type="submit">close</button>
    </form>
</dialog>

<script>
// Global variables for drag-to-create functionality
let dragCreateStartTime = null;
let dragCreateEndTime = null;

// Function to show the drag-to-create dialog
function showDragToCreateDialog(startTime, endTime) {
    dragCreateStartTime = startTime;
    dragCreateEndTime = endTime;
    
    // Format the time range for display
    const timeRange = formatTimeRange(startTime, endTime);
    document.getElementById('dragTimeRange').textContent = timeRange;
    
    // Show the dialog
    document.getElementById('dragToCreateDialog').showModal();
}

// Function to format time range for display
function formatTimeRange(startTime, endTime) {
    const options = {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    };
    
    const startStr = startTime.toLocaleString('en-US', options);
    const endStr = endTime.toLocaleString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
    
    return `${startStr} - ${endStr}`;
}

// Event listeners for dialog buttons
document.addEventListener('DOMContentLoaded', function() {
    // Create Pickup Request button
    document.getElementById('createPickupRequestBtn').addEventListener('click', function() {
        if (dragCreateStartTime && dragCreateEndTime) {
            // Send local datetime format - Laravel handles timezone conversion automatically
            // Format as YYYY-MM-DDTHH:mm:ss
            const formatLocalDateTime = (date) => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
            };

            const startParam = formatLocalDateTime(dragCreateStartTime);
            const endParam = formatLocalDateTime(dragCreateEndTime);

            console.log('Drag-to-create times:', {
                startLocal: dragCreateStartTime,
                endLocal: dragCreateEndTime,
                startParam: startParam,
                endParam: endParam,
                browserTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                serverTimezone: '{{ \App\Models\GlobalConfig::getTimeZone() }}',
                startLocalComponents: {
                    year: dragCreateStartTime.getFullYear(),
                    month: dragCreateStartTime.getMonth() + 1,
                    day: dragCreateStartTime.getDate(),
                    hours: dragCreateStartTime.getHours(),
                    minutes: dragCreateStartTime.getMinutes()
                },
                note: 'Sending local datetime format - Laravel handles timezone conversion automatically'
            });

            // Redirect to pickup request create page with time parameters
            window.location.href = `/pickup-requests/create?start=${encodeURIComponent(startParam)}&end=${encodeURIComponent(endParam)}`;
        }

        // Close dialog
        document.getElementById('dragToCreateDialog').close();
    });
    
    // Block Out Time button
    document.getElementById('blockOutTimeBtn').addEventListener('click', function() {
        if (dragCreateStartTime && dragCreateEndTime) {
            // Open the blockout event modal with the selected time
            openBlockoutEventModal('add', {
                start: dragCreateStartTime,
                end: dragCreateEndTime,
                allDay: false
            });
        }
        
        // Close dialog
        document.getElementById('dragToCreateDialog').close();
    });
    
    // Cancel button
    document.getElementById('cancelDragCreateBtn').addEventListener('click', function() {
        document.getElementById('dragToCreateDialog').close();
    });
    
    // Close dialog when backdrop is clicked
    document.getElementById('dragToCreateDialog').addEventListener('close', function() {
        dragCreateStartTime = null;
        dragCreateEndTime = null;
    });
});
</script>
