<x-app-layout
    page-title="Create Calendar"
    page-icon="fa-sharp fa-calendar-plus"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('calendars.index'),
            'text' => 'Back to Calendars'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Calendars', 'route' => 'calendars.index', 'icon' => 'fa-calendar'],
        ['name' => 'Create Calendar', 'icon' => 'fa-calendar-plus']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Introduction Card -->
            <div class="bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
                <!-- Decorative Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 left-0 w-32 h-32 bg-primary rounded-full -translate-x-16 -translate-y-16"></div>
                    <div class="absolute bottom-0 right-0 w-24 h-24 bg-secondary rounded-full translate-x-12 translate-y-12"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-start gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-gradient-to-br from-primary to-primary-focus text-primary-content w-12 rounded-xl shadow-lg">
                                <i class="fa-sharp fa-calendar-plus text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-2xl font-bold text-base-content mb-2">Create New Calendar</h3>
                            <p class="text-base-content/70 text-base leading-relaxed">Set up a new calendar to organize your events and share them with your team. You can customize the appearance, set privacy options, and manage user permissions.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create Calendar Form Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-calendar-plus text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Calendar Details</h4>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                <form id="calendar-form" action="{{ route('calendars.store') }}" method="POST">
                    @csrf
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Calendar Title -->
                        <div>
                            <label class="label" for="title">
                                <span class="label-text">
                                    <i class="fa-sharp fa-calendar text-primary mr-2"></i>
                                    Calendar Title <span class="text-error font-bold">*</span>
                                </span>
                            </label>
                            <input type="text" name="title" id="title" class="input input-bordered w-full @error('title') input-error @enderror"
                                   value="{{ old('title') }}" placeholder="Enter calendar title..." required>
                            @error('title')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>

                        <!-- Calendar Color -->
                        <div>
                            <label class="label" for="color">
                                <span class="label-text">
                                    <i class="fa-sharp fa-palette text-primary mr-2"></i>
                                    Calendar Color <span class="text-error font-bold">*</span>
                                </span>
                            </label>
                            <div class="flex space-x-2">
                                <input type="color" name="color" id="color" class="h-10 w-10 rounded @error('color') border-error @enderror" value="{{ old('color', '#3788d8') }}">
                                <input type="text" id="color-hex" class="input input-bordered flex-1" value="#3788d8" readonly>
                            </div>
                            @error('color')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>
                    </div>

                    <!-- Calendar Description -->
                    <div>
                        <label class="label" for="description">
                            <span class="label-text">
                                <i class="fa-sharp fa-file-text text-primary mr-2"></i>
                                Description
                            </span>
                        </label>
                        <textarea name="description" id="description" rows="3" class="textarea textarea-bordered w-full @error('description') textarea-error @enderror"
                                  placeholder="Optional description for your calendar...">{{ old('description') }}</textarea>
                        @error('description')
                        <div class="label">
                            <span class="label-text-alt text-error flex items-center gap-1">
                                <i class="fa-sharp fa-exclamation-triangle"></i>
                                {{ $message }}
                            </span>
                        </div>
                        @enderror
                    </div>

                    <!-- Public Calendar -->
                    <div>
                        <label class="label">
                            <span class="label-text">
                                <i class="fa-sharp fa-globe text-primary mr-2"></i>
                                Privacy Settings
                            </span>
                        </label>
                        <label class="cursor-pointer label justify-start">
                            <input type="checkbox" name="is_public" value="1" class="checkbox checkbox-primary mr-3" {{ old('is_public') ? 'checked' : '' }}>
                            <span class="label-text">Make this calendar public (visible to all users)</span>
                        </label>
                        @error('is_public')
                        <div class="label">
                            <span class="label-text-alt text-error flex items-center gap-1">
                                <i class="fa-sharp fa-exclamation-triangle"></i>
                                {{ $message }}
                            </span>
                        </div>
                        @enderror
                    </div>

                    <!-- Share with Users Section -->
                    <div>
                        <label class="label">
                            <span class="label-text">
                                <i class="fa-sharp fa-users text-primary mr-2"></i>
                                Share with Users
                            </span>
                        </label>
                        <p class="text-sm text-base-content/70 mb-4">You can share this calendar with other users and set their permissions. Users will be able to view or edit events based on the permission level you assign.</p>

                        <div id="shared-users-container">
                            <!-- Shared users will be added here dynamically -->
                        </div>

                        <button type="button" id="add-user-btn" class="btn btn-outline btn-sm gap-2 mt-2">
                            <i class="fa-sharp fa-plus"></i> Add User
                        </button>
                    </div>
                </form>
                </div>
            </div>

            <!-- Submit Section -->
            <div class="divider my-8">
                <span class="text-base-content/50 font-medium">Ready to create?</span>
            </div>

            <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg p-6">
                <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-base-300 text-base-content w-10 rounded-lg">
                                <i class="fa-sharp fa-info-circle text-sm"></i>
                            </div>
                        </div>
                        <div class="text-center sm:text-left">
                            <p class="font-medium text-base-content">Review your calendar settings</p>
                            <p class="text-sm text-base-content/60">Make sure all details are correct before creating</p>
                        </div>
                    </div>
                    <div class="flex gap-3 w-full sm:w-auto">
                        <a href="{{ route('calendars.index') }}" class="btn btn-outline btn-lg gap-2 flex-1 sm:flex-none">
                            <i class="fa-sharp fa-arrow-left"></i>
                            Cancel
                        </a>
                        <button type="submit" form="calendar-form" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                            <i class="fa-sharp fa-calendar-plus"></i>
                            Create Calendar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- User Template (Hidden) -->
    <template id="user-row-template">
        <div class="shared-user-row bg-base-200/30 rounded-lg border border-base-300/50 p-4 mb-3">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="label">
                        <span class="label-text">
                            <i class="fa-sharp fa-user text-primary mr-2"></i>
                            User
                        </span>
                    </label>
                    <select name="shared_users[]" class="select select-bordered w-full">
                        <option value="">Select User</option>
                        @foreach ($users as $user)
                            <option value="{{ $user->id }}">{{ $user->name }}</option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label class="label">
                        <span class="label-text">
                            <i class="fa-sharp fa-shield text-primary mr-2"></i>
                            Permission
                        </span>
                    </label>
                    <select name="permissions[]" class="select select-bordered w-full">
                        <option value="view">View Only</option>
                        <option value="edit">Edit Events</option>
                        <option value="manage">Manage Calendar</option>
                    </select>
                </div>

                <div class="flex items-end">
                    <button type="button" class="remove-user-btn btn btn-error btn-sm gap-2 w-full">
                        <i class="fa-sharp fa-trash"></i>
                        Remove
                    </button>
                </div>
            </div>
        </div>
    </template>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Color picker synchronization
            const colorPicker = document.getElementById('color');
            const colorHex = document.getElementById('color-hex');
            
            colorPicker.addEventListener('input', function() {
                colorHex.value = this.value;
            });
            
            // Shared users functionality
            const addUserBtn = document.getElementById('add-user-btn');
            const sharedUsersContainer = document.getElementById('shared-users-container');
            const userRowTemplate = document.getElementById('user-row-template');
            
            addUserBtn.addEventListener('click', function() {
                const userRow = document.importNode(userRowTemplate.content, true);
                sharedUsersContainer.appendChild(userRow);
                
                // Add event listener to the remove button
                const removeBtn = sharedUsersContainer.lastElementChild.querySelector('.remove-user-btn');
                removeBtn.addEventListener('click', function() {
                    this.closest('.shared-user-row').remove();
                });
            });
        });
    </script>
</x-app-layout>
