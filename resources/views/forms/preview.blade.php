<x-app-layout
    page-title="Preview: {{ $form->name }}"
    page-icon="fa-sharp fa-eye"
    :action-buttons="[
        ['name' => 'Form Builder', 'route' => route('forms.builder', $form), 'icon' => 'fa-sharp fa-edit', 'class' => 'btn btn-secondary btn-sm gap-2'],
        ['name' => 'Edit Settings', 'route' => route('forms.edit', $form), 'icon' => 'fa-sharp fa-cog', 'class' => 'btn btn-info btn-sm gap-2'],
        ['name' => 'View Public Form', 'route' => $form->getPublicUrl(), 'icon' => 'fa-sharp fa-external-link', 'class' => 'btn btn-accent btn-sm gap-2', 'target' => '_blank']
    ]"
    :breadcrumbs="[
        ['name' => 'Custom Forms', 'route' => 'forms.index'],
        ['name' => $form->name, 'route' => 'forms.show', 'params' => [$form]],
        ['name' => 'Preview']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Introduction Card -->
            <div class="bg-gradient-to-br from-info/5 via-accent/5 to-primary/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
                <div class="relative z-10">
                    <div class="flex items-start gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-gradient-to-br from-info to-info-focus text-info-content w-12 rounded-xl shadow-lg">
                                <i class="fa-sharp fa-eye text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-2xl font-bold text-base-content mb-2">Form Preview</h3>
                            <p class="text-base-content/70 text-base leading-relaxed">This is how your form will appear to visitors. This preview is for demonstration purposes only and cannot be submitted.</p>
                        </div>
                    </div>
                </div>
            </div>

            @if($form->fields->count() === 0)
                <!-- Empty State -->
                <div class="card bg-base-100 shadow-md">
                    <div class="p-12 text-center">
                        <div class="avatar avatar-placeholder mb-4">
                            <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                <i class="fa-sharp fa-plus text-2xl"></i>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-base-content/80 mb-2">No fields to preview</h3>
                        <p class="text-sm text-base-content/60 mb-4">Add fields to your form using the form builder to see a preview</p>
                        @perms('manage_forms')
                            <a href="{{ route('forms.builder', $form) }}" class="btn btn-primary gap-2">
                                <i class="fa-sharp fa-edit"></i>
                                Open Form Builder
                            </a>
                        @endperms
                    </div>
                </div>
            @else
                <!-- Form Preview Container -->
                <div class="card bg-base-100 shadow-lg">
                    <!-- Form Header -->
                    <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-8 border-b border-base-300/50">
                        <div class="text-center">
                            <h1 class="text-3xl font-bold text-base-content mb-2">{{ $form->name }}</h1>
                            @if($form->description)
                                <p class="text-base-content/70 text-lg">{{ $form->description }}</p>
                            @endif
                            <div class="flex justify-center items-center gap-4 mt-4">
                                <div class="badge badge-lg {{ $form->is_active ? 'badge-success' : 'badge-error' }}">
                                    {{ $form->is_active ? 'Active' : 'Inactive' }}
                                </div>
                                @if($form->requires_signature)
                                    <div class="badge badge-lg badge-warning">
                                        <i class="fa-sharp fa-signature mr-1"></i>
                                        Signature Required
                                    </div>
                                @endif
                                @if($form->applies_discount_on_approval)
                                    <div class="badge badge-lg badge-accent">
                                        <i class="fa-sharp fa-percent mr-1"></i>
                                        Discount on Approval
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Form Fields Preview -->
                    <div class="p-6 lg:p-8">
                        <form class="space-y-6" onsubmit="return false;">
                            @foreach($form->fields->sortBy('order') as $field)
                                <div class="form-field">
                                    @if($field->type === 'html')
                                        <div class="html-content-styled">
                                            {!! $field->content !!}
                                        </div>
                                    @else
                                        @if($field->label)
                                            <label class="label">
                                                <span class="label-text font-medium text-lg">
                                                    {{ $field->label }}
                                                    @if($field->required)
                                                        <span class="text-error">*</span>
                                                    @endif
                                                </span>
                                            </label>
                                        @endif

                                        @if($field->type === 'text' || $field->type === 'email' || $field->type === 'phone' || $field->type === 'number' || $field->type === 'date')
                                            <input type="{{ $field->type }}" 
                                                   class="input input-bordered w-full input-lg"
                                                   placeholder="{{ $field->placeholder }}"
                                                   {{ $field->required ? 'required' : '' }}
                                                   disabled>
                                        @elseif($field->type === 'textarea')
                                            <textarea class="textarea textarea-bordered w-full min-h-24"
                                                      placeholder="{{ $field->placeholder }}"
                                                      {{ $field->required ? 'required' : '' }}
                                                      disabled></textarea>
                                        @elseif($field->type === 'select')
                                            <select class="select select-bordered w-full select-lg" 
                                                    {{ $field->required ? 'required' : '' }}
                                                    disabled>
                                                <option value="">{{ $field->placeholder ?: 'Choose an option...' }}</option>
                                                @if($field->options)
                                                    @foreach($field->getOptionsArray() as $option)
                                                        <option value="{{ is_array($option) ? $option['value'] ?? $option['label'] : $option }}">
                                                            {{ is_array($option) ? $option['label'] : $option }}
                                                        </option>
                                                    @endforeach
                                                @endif
                                            </select>
                                        @elseif($field->type === 'radio')
                                            <div class="space-y-3">
                                                @if($field->options)
                                                    @foreach($field->getOptionsArray() as $index => $option)
                                                        <label class="flex items-center gap-3 cursor-pointer">
                                                            <input type="radio" 
                                                                   name="{{ $field->name }}"
                                                                   value="{{ is_array($option) ? $option['value'] ?? $option['label'] : $option }}"
                                                                   class="radio radio-primary"
                                                                   {{ $field->required ? 'required' : '' }}
                                                                   disabled>
                                                            <span class="text-base">{{ is_array($option) ? $option['label'] : $option }}</span>
                                                        </label>
                                                    @endforeach
                                                @endif
                                            </div>
                                        @elseif($field->type === 'checkbox')
                                            <div class="space-y-3">
                                                @if($field->options)
                                                    @foreach($field->getOptionsArray() as $index => $option)
                                                        <label class="flex items-center gap-3 cursor-pointer">
                                                            <input type="checkbox"
                                                                   name="{{ $field->name }}[]"
                                                                   value="{{ is_array($option) ? $option['value'] ?? $option['label'] : $option }}"
                                                                   class="checkbox checkbox-primary"
                                                                   disabled>
                                                            <span class="text-base">{{ is_array($option) ? $option['label'] : $option }}</span>
                                                        </label>
                                                    @endforeach
                                                @endif
                                            </div>
                                        @elseif($field->type === 'signature')
                                            <div class="border-2 border-dashed border-base-300 rounded-lg p-12 text-center bg-base-50">
                                                <i class="fa-sharp fa-signature text-6xl text-base-content/30 mb-4"></i>
                                                <h4 class="text-lg font-medium text-base-content/60 mb-2">Digital Signature Area</h4>
                                                <p class="text-sm text-base-content/50">Visitors will be able to sign here using their mouse or touch device</p>
                                            </div>
                                        @endif

                                        @if($field->help_text)
                                            <div class="label">
                                                <span class="label-text-alt text-base-content/60">{{ $field->help_text }}</span>
                                            </div>
                                        @endif
                                    @endif
                                </div>
                            @endforeach

                            <!-- Preview Submit Section -->
                            <div class="divider"></div>
                            <div class="text-center space-y-4">
                                <button type="button" class="btn btn-primary btn-lg gap-2" disabled>
                                    <i class="fa-sharp fa-paper-plane"></i>
                                    Submit {{ $form->name }}
                                </button>
                                <p class="text-sm text-base-content/60">
                                    <i class="fa-sharp fa-info-circle mr-1"></i>
                                    This is a preview - the form cannot be submitted from this page
                                </p>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Preview Information -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-info text-info-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-lightbulb text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Preview Information</h4>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h5 class="font-medium text-base-content mb-2">Form Statistics</h5>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-base-content/60">Total Fields:</span>
                                        <span class="font-medium">{{ $form->fields->count() }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-base-content/60">Required Fields:</span>
                                        <span class="font-medium">{{ $form->fields->where('required', true)->count() }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-base-content/60">HTML Blocks:</span>
                                        <span class="font-medium">{{ $form->fields->where('type', 'html')->count() }}</span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h5 class="font-medium text-base-content mb-2">Next Steps</h5>
                                <div class="space-y-2">
                                    @perms('manage_forms')
                                        <a href="{{ route('forms.builder', $form) }}" class="btn btn-outline btn-sm gap-2 w-full">
                                            <i class="fa-sharp fa-edit"></i>
                                            Edit Fields
                                        </a>
                                        <a href="{{ route('forms.edit', $form) }}" class="btn btn-outline btn-sm gap-2 w-full">
                                            <i class="fa-sharp fa-cog"></i>
                                            Form Settings
                                        </a>
                                    @endperms
                                    <a href="{{ $form->getPublicUrl() }}" target="_blank" class="btn btn-primary btn-sm gap-2 w-full">
                                        <i class="fa-sharp fa-external-link"></i>
                                        View Live Form
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

        </div>
    </div>

    <script>
        // Apply HTML content styling to all HTML sections
        document.addEventListener('DOMContentLoaded', function() {
            // Apply .html-content-styled class to all HTML content sections
            const htmlContentElements = document.querySelectorAll('div[class*="html"], .prose');
            htmlContentElements.forEach(element => {
                if (!element.classList.contains('html-content-styled')) {
                    element.classList.add('html-content-styled');
                }
                
                // Fix common encoding issues with quotes and apostrophes
                let content = element.innerHTML;
                const fixedContent = content
                    .replace(/â€™/g, "'")  // Fix apostrophes
                    .replace(/â€œ/g, '"')  // Fix opening quotes
                    .replace(/â€/g, '"')   // Fix closing quotes
                    .replace(/â€"/g, "–")  // Fix en-dash
                    .replace(/â€"/g, "—")  // Fix em-dash
                    .replace(/â€¦/g, "…")  // Fix ellipsis
                    .replace(/Â/g, "");    // Remove extraneous Â characters
                    
                if (fixedContent !== content) {
                    element.innerHTML = fixedContent;
                }
            });
        });
    </script>
</x-app-layout>