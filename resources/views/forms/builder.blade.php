<x-app-layout
    page-title="Form Builder: {{ $form->name }}"
    page-icon="fa-sharp fa-edit"
    :action-buttons="[
        ['name' => 'Preview Form', 'route' => route('forms.preview', $form), 'icon' => 'fa-sharp fa-eye', 'class' => 'btn btn-info btn-sm gap-2'],
        ['name' => 'Form Settings', 'route' => route('forms.edit', $form), 'icon' => 'fa-sharp fa-cog', 'class' => 'btn btn-secondary btn-sm gap-2']
    ]"
    :breadcrumbs="[
        ['name' => 'Custom Forms', 'route' => 'forms.index'],
        ['name' => $form->name, 'route' => 'forms.show', 'params' => [$form]],
        ['name' => 'Form Builder']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Introduction Card -->
            <div class="bg-gradient-to-br from-secondary/5 via-accent/5 to-primary/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
                <div class="relative z-10">
                    <div class="flex items-start gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-gradient-to-br from-secondary to-secondary-focus text-secondary-content w-12 rounded-xl shadow-lg">
                                <i class="fa-sharp fa-edit text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-2xl font-bold text-base-content mb-2">Form Builder</h3>
                            <p class="text-base-content/70 text-base leading-relaxed">Design your form by adding fields, HTML content blocks, and organizing the layout. Drag and drop to reorder fields, and use the toolbar to add new elements.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
                
                <!-- Field Palette -->
                <div class="xl:col-span-1">
                    <div class="card bg-base-100 shadow-md sticky top-4">
                        <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-accent text-accent-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-plus text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Add Fields</h4>
                            </div>
                        </div>
                        <div class="p-4 space-y-3">
                            <!-- Field Type Buttons -->
                            @foreach(\App\Models\FormField::TYPES as $type => $label)
                                <button type="button" 
                                        class="btn btn-outline btn-sm w-full gap-2 justify-start field-type-btn"
                                        data-type="{{ $type }}">
                                    @php
                                        $iconMap = [
                                            'text' => 'text-width',
                                            'textarea' => 'align-left',
                                            'select' => 'list',
                                            'radio' => 'circle-dot',
                                            'checkbox' => 'square-check',
                                            'html' => 'code',
                                            'signature' => 'signature',
                                            'email' => 'envelope',
                                            'phone' => 'phone',
                                            'number' => 'hashtag',
                                            'date' => 'calendar'
                                        ];
                                        $icon = $iconMap[$type] ?? 'question';
                                    @endphp
                                    <i class="fa-sharp fa-{{ $icon }}"></i>
                                    {{ $label }}
                                </button>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Form Builder Area -->
                <div class="xl:col-span-3">
                    <!-- Form Preview Header -->
                    <div class="card bg-base-100 shadow-md mb-6">
                        <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-primary text-primary-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-file-lines text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">{{ $form->name }}</h4>
                            </div>
                        </div>
                        @if($form->description)
                            <div class="p-6">
                                <p class="text-base-content/70">{{ $form->description }}</p>
                            </div>
                        @endif
                    </div>

                    <!-- Form Fields Container -->
                    <div id="form-fields-container" class="space-y-4 min-h-96">
                        @if($form->fields->count() > 0)
                            @foreach($form->fields as $field)
                                <div class="field-item card bg-base-100 shadow-sm border border-base-200 hover:shadow-md transition-all duration-200" 
                                     data-field-id="{{ $field->id }}" data-order="{{ $field->order }}">
                                    <div class="card-body p-4">
                                        <div class="flex items-center justify-between mb-3">
                                            <div class="flex items-center gap-2">
                                                <div class="drag-handle cursor-move">
                                                    <i class="fa-sharp fa-grip-vertical text-base-content/40"></i>
                                                </div>
                                                <span class="badge badge-sm badge-outline">{{ \App\Models\FormField::TYPES[$field->type] ?? $field->type }}</span>
                                                @if($field->required)
                                                    <span class="badge badge-sm badge-error">Required</span>
                                                @endif
                                            </div>
                                            <div class="flex gap-1">
                                                <button type="button" class="btn btn-ghost btn-xs edit-field-btn" data-field-id="{{ $field->id }}">
                                                    <i class="fa-sharp fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-ghost btn-xs text-error delete-field-btn" data-field-id="{{ $field->id }}">
                                                    <i class="fa-sharp fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Field Preview -->
                                        <div class="field-preview">
                                            @if($field->type === 'html')
                                                <div class="prose max-w-none html-preview" data-field-id="{{ $field->id }}">
                                                    {!! $field->content !!}
                                                </div>
                                                <div id="html-editor-{{ $field->id }}" class="hidden mt-3">
                                                    <textarea id="html-content-{{ $field->id }}" class="w-full">{!! $field->content !!}</textarea>
                                                    <div class="flex gap-2 mt-3">
                                                        <button type="button" class="btn btn-primary btn-sm save-html-btn" data-field-id="{{ $field->id }}">
                                                            <i class="fa-sharp fa-save"></i>
                                                            Save
                                                        </button>
                                                        <button type="button" class="btn btn-outline btn-sm cancel-html-btn" data-field-id="{{ $field->id }}">
                                                            Cancel
                                                        </button>
                                                    </div>
                                                </div>
                                            @else
                                                @if($field->label)
                                                    <label class="label">
                                                        <span class="label-text font-medium">
                                                            {{ $field->label }}
                                                            @if($field->required)
                                                                <span class="text-error">*</span>
                                                            @endif
                                                        </span>
                                                    </label>
                                                @endif

                                                @if($field->type === 'text' || $field->type === 'email' || $field->type === 'phone' || $field->type === 'number' || $field->type === 'date')
                                                    <input type="{{ $field->type }}" class="input input-bordered w-full input-disabled" 
                                                           placeholder="{{ $field->placeholder }}" disabled>
                                                @elseif($field->type === 'textarea')
                                                    <textarea class="textarea textarea-bordered w-full" placeholder="{{ $field->placeholder }}" disabled></textarea>
                                                @elseif($field->type === 'select')
                                                    <select class="select select-bordered w-full" disabled>
                                                        <option>{{ $field->placeholder ?: 'Choose an option...' }}</option>
                                                        @if($field->options)
                                                            @foreach($field->getOptionsArray() as $option)
                                                                <option>{{ is_array($option) ? $option['label'] : $option }}</option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                @elseif($field->type === 'radio')
                                                    <div class="space-y-2">
                                                        @if($field->options)
                                                            @foreach($field->getOptionsArray() as $index => $option)
                                                                <label class="flex items-center gap-2">
                                                                    <input type="radio" class="radio radio-primary radio-sm" disabled>
                                                                    <span class="text-sm">{{ is_array($option) ? $option['label'] : $option }}</span>
                                                                </label>
                                                            @endforeach
                                                        @endif
                                                    </div>
                                                @elseif($field->type === 'checkbox')
                                                    <div class="space-y-2">
                                                        @if($field->options)
                                                            @foreach($field->getOptionsArray() as $index => $option)
                                                                <label class="flex items-center gap-2">
                                                                    <input type="checkbox" class="checkbox checkbox-primary checkbox-sm" disabled>
                                                                    <span class="text-sm">{{ is_array($option) ? $option['label'] : $option }}</span>
                                                                </label>
                                                            @endforeach
                                                        @endif
                                                    </div>
                                                @elseif($field->type === 'signature')
                                                    <div class="border border-dashed border-base-300 rounded-lg p-8 text-center">
                                                        <i class="fa-sharp fa-signature text-3xl text-base-content/40 mb-2"></i>
                                                        <p class="text-sm text-base-content/60">Signature Area</p>
                                                    </div>
                                                @endif

                                                @if($field->help_text)
                                                    <div class="label">
                                                        <span class="label-text-alt text-base-content/60">{{ $field->help_text }}</span>
                                                    </div>
                                                @endif
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <!-- Empty State -->
                            <div class="text-center py-12 border-2 border-dashed border-base-300 rounded-lg">
                                <div class="avatar avatar-placeholder mb-4">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                        <i class="fa-sharp fa-plus text-2xl"></i>
                                    </div>
                                </div>
                                <h3 class="text-lg font-medium text-base-content/80 mb-2">No fields added yet</h3>
                                <p class="text-sm text-base-content/60">Click on a field type from the left panel to add it to your form</p>
                            </div>
                        @endif
                    </div>

                    <!-- Save Section -->
                    <div class="card bg-base-100 shadow-md mt-6">
                        <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-success text-success-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-save text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Save Changes</h4>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                                <div class="flex items-center gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-base-300 text-base-content w-10 rounded-lg">
                                            <i class="fa-sharp fa-info-circle text-sm"></i>
                                        </div>
                                    </div>
                                    <div class="text-center sm:text-left">
                                        <p id="saveStatusIndicator" class="font-medium text-base-content">Changes will save automatically</p>
                                        <p class="text-sm text-base-content/60">Your changes are saved as you make them</p>
                                    </div>
                                </div>
                                <div class="flex gap-3 w-full sm:w-auto">
                                    <button type="button" id="saveFieldsBtn" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                                        <i class="fa-sharp fa-save"></i>
                                        Save All Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Field Edit Modal -->
    <div id="fieldEditModal" class="modal">
        <div class="modal-backdrop" onclick="closeFieldEditModal()"></div>
        <div class="modal-box w-11/12 max-w-6xl h-[90vh] max-h-[90vh] overflow-y-auto">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2" onclick="closeFieldEditModal()">✕</button>
            <h3 class="font-bold text-lg mb-4">Edit Field</h3>
            
            <div id="fieldEditForm" class="space-y-4">
                <!-- Dynamic form content will be loaded here -->
            </div>

            <div class="modal-action">
                <button type="button" class="btn btn-outline" onclick="closeFieldEditModal()">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveFieldBtn">Save Field</button>
            </div>
        </div>
    </div>


    <!-- Include SortableJS for drag and drop -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    
    <!-- Include Jodit for rich text editing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jodit/4.2.47/es2015/jodit.fat.min.js" integrity="sha512-lO8LIfs1jytwt9MbX5/EBq5z6fN/2Hmiyrb+TdkOTKrhUQp/mmkrp1+Wyw9Tj8kokW6zU8KlH60MPZ1Xmt4JAQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jodit/4.2.47/es2015/jodit.fat.min.css" integrity="sha512-2cfnJ8ZMBqkaNXsi/6ucIcFRvKVFKW69HEP5S7L2fQtAaPrVg5XLkkUgl46kkaN5PPArXwLPCOqhbsAAiHQiXA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    
    <style>
        /* Fix Jodit editor positioning within modal */
        .modal-box .jodit-container {
            position: relative !important;
            z-index: 1 !important;
        }
        
        .modal-box .jodit-toolbar-editor-collection {
            z-index: 10 !important;
        }
        
        .modal-box .jodit-popup {
            z-index: 99999 !important;
        }
        
        .modal-box .jodit-dialog {
            z-index: 99999 !important;
        }
        
        /* Ensure modal itself has appropriate z-index */
        #fieldEditModal {
            z-index: 9998 !important;
        }
        
        /* Fix modal positioning */
        #fieldEditModal.modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: none;
            justify-content: center;
            align-items: center;
        }
        
        #fieldEditModal.modal-open {
            display: flex !important;
        }
        
        .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1;
        }
        
        #fieldEditModal .modal-box {
            position: relative;
            z-index: 2;
        }
        
        /* Force Jodit popups to position correctly within modal */
        .jodit-popup,
        .jodit-dialog,
        .jodit-tooltip {
            position: fixed !important;
            transform: none !important;
        }
        
        /* Ensure Jodit container establishes positioning context */
        #fieldEditModal .jodit-container {
            position: relative !important;
            overflow: visible !important;
        }
        
        /* Override Jodit's positioning calculations */
        .jodit-popup {
            z-index: 99999 !important;
        }
    </style>

    <script>
        let currentFields = @json($form->fields->toArray());
        let nextTempId = -1;
        let autoSaveTimeout = null;
        let isAutoSaving = false;
        let joditEditor = null;
        
        // Initialize HTML preview processing on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Apply .html-content-styled class to all HTML content sections
            const htmlContentElements = document.querySelectorAll('.html-preview');
            htmlContentElements.forEach(element => {
                if (!element.classList.contains('html-content-styled')) {
                    element.classList.add('html-content-styled');
                }
            });

            // Process all existing HTML previews
            document.querySelectorAll('.html-preview').forEach(preview => {
                const originalContent = preview.innerHTML;
                // Fix common encoding issues with quotes and apostrophes
                const fixedContent = originalContent
                    .replace(/â€™/g, "'")  // Fix apostrophes
                    .replace(/â€œ/g, '"')  // Fix opening quotes
                    .replace(/â€/g, '"')   // Fix closing quotes
                    .replace(/â€"/g, "–")  // Fix en-dash
                    .replace(/â€"/g, "—")  // Fix em-dash
                    .replace(/â€¦/g, "…")  // Fix ellipsis
                    .replace(/Â/g, "");    // Remove extraneous Â characters
                preview.innerHTML = processTemplatePreview(fixedContent);
            });
        });

        // Initialize sortable
        const container = document.getElementById('form-fields-container');
        const sortable = Sortable.create(container, {
            handle: '.drag-handle',
            animation: 150,
            onEnd: function(evt) {
                updateFieldOrder();
            }
        });

        // Add field type button handlers
        document.querySelectorAll('.field-type-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                addField(this.dataset.type);
            });
        });

        // Save fields button
        document.getElementById('saveFieldsBtn').addEventListener('click', saveFields);

        // Use event delegation for edit and delete buttons
        container.addEventListener('click', function(event) {
            if (event.target.closest('.edit-field-btn')) {
                const fieldId = event.target.closest('.edit-field-btn').dataset.fieldId;
                editField(fieldId);
            } else if (event.target.closest('.delete-field-btn')) {
                const fieldId = event.target.closest('.delete-field-btn').dataset.fieldId;
                deleteField(fieldId);
            } else if (event.target.closest('.save-html-btn')) {
                const fieldId = event.target.closest('.save-html-btn').dataset.fieldId;
                saveHtmlInline(fieldId);
            } else if (event.target.closest('.cancel-html-btn')) {
                const fieldId = event.target.closest('.cancel-html-btn').dataset.fieldId;
                cancelHtmlInline(fieldId);
            }
        });

        // Modal control functions
        function openFieldEditModal() {
            const modal = document.getElementById('fieldEditModal');
            modal.classList.add('modal-open');
        }
        
        function closeFieldEditModal() {
            const modal = document.getElementById('fieldEditModal');
            modal.classList.remove('modal-open');
            // Clean up Jodit editor when modal closes
            if (joditEditor) {
                joditEditor.destruct();
                joditEditor = null;
            }
        }

        function addField(type) {
            const newField = {
                id: nextTempId--,
                type: type,
                name: `field_${Math.random().toString(36).substr(2, 9)}`,
                label: '',
                content: type === 'html' ? '<p>Enter your HTML content here...</p>' : '',
                placeholder: '',
                help_text: '',
                required: false,
                options: [],
                validation_rules: [],
                order: currentFields.length
            };

            currentFields.push(newField);
            renderFields();
            
            // Auto-save after adding field
            autoSave('Field added');
            
            // Open edit dialog for the new field
            editField(newField.id);
        }

        function editField(fieldId) {
            const field = currentFields.find(f => f.id == fieldId);
            if (!field) return;
            
            // For HTML fields, use inline editing instead
            if (field.type === 'html') {
                editHtmlInline(fieldId);
                return;
            }

            // Destroy existing Jodit editor if it exists
            if (joditEditor) {
                joditEditor.destruct();
                joditEditor = null;
            }

            // Load field edit form (simplified version)
            const form = document.getElementById('fieldEditForm');
            
            form.innerHTML = generateFieldEditForm(field);
            openFieldEditModal();

            // Initialize Jodit for HTML fields
            if (field.type === 'html') {
                setTimeout(() => {
                    const contentTextarea = document.getElementById('field_content');
                    if (contentTextarea) {
                        joditEditor = Jodit.make(contentTextarea, {
                            height: 400,
                            toolbarSticky: false,
                            zIndex: 9999,
                            globalFullSize: false,
                            buttons: [
                                'bold', 'italic', 'underline', '|',
                                'ul', 'ol', '|',
                                'link', 'image', '|',
                                'align', '|',
                                'undo', 'redo', '|',
                                'hr', 'table', '|',
                                'fullsize', 'source'
                            ],
                            // Enable paste functionality
                            allowPasteHTML: true,
                            allowPasteFromWord: true,
                            processPasteHTML: true,
                            processPasteFromWord: true,
                            askBeforePasteHTML: false,
                            askBeforePasteFromWord: false,
                            defaultActionOnPaste: 'insert_as_html',
                            // Additional paste settings
                            pasteHTMLActionList: 'insert_as_html',
                            pasteFromWordActionList: 'insert_as_html',
                            // Enable iframe mode for better compatibility
                            iframe: false,
                            popup: {
                                zIndex: 99999
                            },
                            dialog: {
                                zIndex: 99999
                            },
                            toolbarButtonSize: 'middle',
                            toolbarInline: false,
                            showCharsCounter: false,
                            showWordsCounter: false,
                            showXPathInStatusbar: false,
                            // Fix positioning within modal
                            ownerWindow: window,
                            ownerDocument: document,
                            shadowRoot: null,
                            appendTo: document.getElementById('fieldEditModal'),
                            // Disable readonly mode
                            readonly: false,
                            disabled: false,
                            // Security settings
                            cleanHTML: {
                                allowTags: 'p,br,strong,em,u,ol,ul,li,a,table,thead,tbody,tr,td,th,h1,h2,h3,h4,h5,h6,blockquote,hr,img',
                                allowAttributes: 'href,src,alt,title,class,style'
                            },
                            // Enable all paste sources
                            allowTabNavigation: true,
                            events: {
                                afterInit: function (editorInstance) {
                                    editorInstance.container.classList.add('html-content-styled');
                                    // Ensure editor is focusable and editable
                                    editorInstance.editor.setAttribute('contenteditable', 'true');
                                    console.log('Jodit editor initialized successfully');

                                    // Focus the editor to ensure it's ready for input
                                    setTimeout(() => {
                                        editorInstance.focus();
                                        console.log('Jodit editor focused');
                                    }, 200);
                                },
                                beforePaste: function (event) {
                                    console.log('Before paste event triggered');
                                    // Allow paste events to proceed
                                    return true;
                                },
                                afterPaste: function (event) {
                                    console.log('Content pasted successfully');
                                },
                                keydown: function (event) {
                                    // Handle Ctrl+V manually if needed
                                    if ((event.ctrlKey || event.metaKey) && event.key === 'v') {
                                        console.log('Paste keyboard shortcut detected');
                                    }
                                }
                            },
                        });
                    }
                }, 100);
            }

            // Set up save button
            document.getElementById('saveFieldBtn').onclick = function() {
                saveFieldEdit(fieldId);
                closeFieldEditModal();
            };
        }

        function generateFieldEditForm(field) {
            let html = `
                <div>
                    <label class="label">
                        <span class="label-text">Field Name (for data)</span>
                    </label>
                    <input type="text" class="input input-bordered w-full" id="field_name" value="${field.name || ''}">
                </div>
            `;

            if (field.type !== 'html') {
                html += `
                    <div>
                        <label class="label">
                            <span class="label-text">Label</span>
                        </label>
                        <input type="text" class="input input-bordered w-full" id="field_label" value="${field.label || ''}">
                    </div>
                    <div>
                        <label class="label">
                            <span class="label-text">Placeholder</span>
                        </label>
                        <input type="text" class="input input-bordered w-full" id="field_placeholder" value="${field.placeholder || ''}">
                    </div>
                    <div>
                        <label class="label">
                            <span class="label-text">Help Text</span>
                        </label>
                        <input type="text" class="input input-bordered w-full" id="field_help_text" value="${field.help_text || ''}">
                    </div>
                    <div>
                        <label class="label cursor-pointer justify-start gap-3">
                            <input type="checkbox" class="checkbox checkbox-primary" id="field_required" ${field.required ? 'checked' : ''}>
                            <span class="label-text">Required field</span>
                        </label>
                    </div>
                `;
            }

            if (field.type === 'html') {
                html += `
                    <div>
                        <label class="label">
                            <span class="label-text">HTML Content</span>
                        </label>
                        <textarea class="textarea textarea-bordered w-full h-40" id="field_content">${field.content || ''}</textarea>
                        <div class="label">
                            <span class="label-text-alt text-info">
                                <i class="fa-sharp fa-info-circle mr-1"></i>
                                Available template variables: {date_today_short}, {date_today_full}, {date_plus_1yr_full}, {business_name}, {full_name}
                            </span>
                        </div>
                    </div>
                `;
            }

            if (['select', 'radio', 'checkbox'].includes(field.type)) {
                html += `
                    <div>
                        <label class="label">
                            <span class="label-text">Options (one per line)</span>
                        </label>
                        <textarea class="textarea textarea-bordered w-full h-24" id="field_options">${(field.options || []).join('\n')}</textarea>
                    </div>
                `;
            }

            return html;
        }

        function saveFieldEdit(fieldId) {
            const field = currentFields.find(f => f.id == fieldId);
            if (!field) return;

            field.name = document.getElementById('field_name').value;
            
            if (field.type !== 'html') {
                field.label = document.getElementById('field_label').value;
                field.placeholder = document.getElementById('field_placeholder').value;
                field.help_text = document.getElementById('field_help_text').value;
                field.required = document.getElementById('field_required').checked;
            }

            if (field.type === 'html') {
                // Get content from Jodit editor if it exists, otherwise from textarea
                if (joditEditor) {
                    field.content = joditEditor.value;
                } else {
                    field.content = document.getElementById('field_content').value;
                }
            }

            if (['select', 'radio', 'checkbox'].includes(field.type)) {
                const optionsText = document.getElementById('field_options').value;
                field.options = optionsText.split('\n').filter(option => option.trim());
            }

            renderFields();
            
            // Auto-save after editing field
            autoSave('Field updated');
        }

        // Inline HTML editing functions
        let inlineJoditEditors = {};
        
        function editHtmlInline(fieldId) {
            const field = currentFields.find(f => f.id == fieldId);
            if (!field) return;
            
            // Hide preview and show editor
            const previewDiv = document.querySelector(`[data-field-id="${fieldId}"] .html-preview`);
            const editorDiv = document.getElementById(`html-editor-${fieldId}`);
            
            if (previewDiv) previewDiv.classList.add('hidden');
            if (editorDiv) editorDiv.classList.remove('hidden');
            
            // Initialize Jodit editor for this field
            const textarea = document.getElementById(`html-content-${fieldId}`);
            if (textarea && !inlineJoditEditors[fieldId]) {
                inlineJoditEditors[fieldId] = Jodit.make(textarea, {
                    height: 300,
                    toolbarSticky: false,
                    zIndex: 1000,
                    buttons: [
                        'bold', 'italic', 'underline', '|',
                        'ul', 'ol', '|',
                        'link', 'image', '|',
                        'align', '|',
                        'undo', 'redo', '|',
                        'hr', 'table', '|',
                        'source'
                    ],
                    allowPasteHTML: true,
                    allowPasteFromWord: true,
                    processPasteHTML: true,
                    processPasteFromWord: true,
                    askBeforePasteHTML: false,
                    askBeforePasteFromWord: false,
                    defaultActionOnPaste: 'insert_as_html',
                    iframe: false,
                    readonly: false,
                    disabled: false,
                    cleanHTML: {
                        allowTags: 'p,br,strong,em,u,ol,ul,li,a,table,thead,tbody,tr,td,th,h1,h2,h3,h4,h5,h6,blockquote,hr,img',
                        allowAttributes: 'href,src,alt,title,class,style'
                    },
                    events: {
                        afterInit: function (editorInstance) {
                            editorInstance.container.classList.add('html-content-styled');
                            editorInstance.editor.setAttribute('contenteditable', 'true');
                            setTimeout(() => {
                                editorInstance.focus();
                            }, 200);
                        }
                    }
                });
            }
        }
        
        function saveHtmlInline(fieldId) {
            const field = currentFields.find(f => f.id == fieldId);
            if (!field) return;
            
            // Get content from Jodit editor
            if (inlineJoditEditors[fieldId]) {
                field.content = inlineJoditEditors[fieldId].value;
                
                // Destroy the editor
                inlineJoditEditors[fieldId].destruct();
                delete inlineJoditEditors[fieldId];
            }
            
            // Update preview and show it
            const previewDiv = document.querySelector(`[data-field-id="${fieldId}"] .html-preview`);
            const editorDiv = document.getElementById(`html-editor-${fieldId}`);
            
            if (previewDiv) {
                previewDiv.innerHTML = processTemplatePreview(field.content || '');
                previewDiv.classList.remove('hidden');
            }
            if (editorDiv) editorDiv.classList.add('hidden');
            
            // Auto-save
            autoSave('HTML content updated');
        }
        
        function cancelHtmlInline(fieldId) {
            // Destroy the editor without saving
            if (inlineJoditEditors[fieldId]) {
                inlineJoditEditors[fieldId].destruct();
                delete inlineJoditEditors[fieldId];
            }
            
            // Show preview and hide editor
            const previewDiv = document.querySelector(`[data-field-id="${fieldId}"] .html-preview`);
            const editorDiv = document.getElementById(`html-editor-${fieldId}`);
            
            if (previewDiv) previewDiv.classList.remove('hidden');
            if (editorDiv) editorDiv.classList.add('hidden');
        }

        function deleteField(fieldId) {
            const field = currentFields.find(f => f.id == fieldId);
            const fieldLabel = field ? (field.label || field.name || 'this field') : 'this field';
            
            showConfirm({
                title: 'Delete Field',
                message: `Are you sure you want to delete "${fieldLabel}"? This action cannot be undone.`,
                confirmText: 'Delete',
                confirmClass: 'btn-error',
                icon: 'fa-trash',
                iconColor: 'bg-error/10 text-error'
            }).then(confirmed => {
                if (confirmed) {
                    currentFields = currentFields.filter(f => f.id != fieldId);
                    renderFields();
                    
                    // Auto-save after deleting field
                    autoSave('Field deleted');
                }
            });
        }

        function updateFieldOrder() {
            const fieldItems = container.querySelectorAll('.field-item');
            fieldItems.forEach((item, index) => {
                const fieldId = item.dataset.fieldId;
                const field = currentFields.find(f => f.id == fieldId);
                if (field) {
                    field.order = index;
                }
            });
            
            // Auto-save after reordering
            autoSave('Field order updated');
        }

        function renderFields() {
            if (currentFields.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-12 border-2 border-dashed border-base-300 rounded-lg">
                        <div class="avatar avatar-placeholder mb-4">
                            <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                <i class="fa-sharp fa-plus text-2xl"></i>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-base-content/80 mb-2">No fields added yet</h3>
                        <p class="text-sm text-base-content/60">Click on a field type from the left panel to add it to your form</p>
                    </div>
                `;
                return;
            }

            // Sort fields by order
            currentFields.sort((a, b) => a.order - b.order);

            container.innerHTML = currentFields.map(field => `
                <div class="field-item card bg-base-100 shadow-sm border border-base-200 hover:shadow-md transition-all duration-200" 
                     data-field-id="${field.id}" data-order="${field.order}">
                    <div class="card-body p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-2">
                                <div class="drag-handle cursor-move">
                                    <i class="fa-sharp fa-grip-vertical text-base-content/40"></i>
                                </div>
                                <span class="badge badge-sm badge-outline">${field.type}</span>
                                ${field.required ? '<span class="badge badge-sm badge-error">Required</span>' : ''}
                            </div>
                            <div class="flex gap-1">
                                <button type="button" class="btn btn-ghost btn-xs edit-field-btn" data-field-id="${field.id}">
                                    <i class="fa-sharp fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-ghost btn-xs text-error delete-field-btn" data-field-id="${field.id}">
                                    <i class="fa-sharp fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="field-preview">
                            ${generateFieldPreview(field)}
                        </div>
                    </div>
                </div>
            `).join('');

            // Event listeners are handled by event delegation above
        }

        function generateFieldPreview(field) {
            if (field.type === 'html') {
                return `
                    <div class="html-content-styled html-preview" data-field-id="${field.id}">${processTemplatePreview(field.content || '')}</div>
                    <div id="html-editor-${field.id}" class="hidden mt-3">
                        <textarea id="html-content-${field.id}" class="w-full">${field.content || ''}</textarea>
                        <div class="flex gap-2 mt-3">
                            <button type="button" class="btn btn-primary btn-sm save-html-btn" data-field-id="${field.id}">
                                <i class="fa-sharp fa-save"></i>
                                Save
                            </button>
                            <button type="button" class="btn btn-outline btn-sm cancel-html-btn" data-field-id="${field.id}">
                                Cancel
                            </button>
                        </div>
                    </div>
                `;
            }

            let html = '';
            
            if (field.label) {
                html += `
                    <label class="label">
                        <span class="label-text font-medium">
                            ${field.label}
                            ${field.required ? '<span class="text-error">*</span>' : ''}
                        </span>
                    </label>
                `;
            }

            if (['text', 'email', 'phone', 'number', 'date'].includes(field.type)) {
                html += `<input type="${field.type}" class="input input-bordered w-full input-disabled" placeholder="${field.placeholder || ''}" disabled>`;
            } else if (field.type === 'textarea') {
                html += `<textarea class="textarea textarea-bordered w-full" placeholder="${field.placeholder || ''}" disabled></textarea>`;
            } else if (field.type === 'select') {
                html += `<select class="select select-bordered w-full" disabled>
                    <option>${field.placeholder || 'Choose an option...'}</option>`;
                if (field.options) {
                    field.options.forEach(option => {
                        html += `<option>${option}</option>`;
                    });
                }
                html += '</select>';
            } else if (field.type === 'radio') {
                html += '<div class="space-y-2">';
                if (field.options) {
                    field.options.forEach(option => {
                        html += `
                            <label class="flex items-center gap-2">
                                <input type="radio" class="radio radio-primary radio-sm" disabled>
                                <span class="text-sm">${option}</span>
                            </label>
                        `;
                    });
                }
                html += '</div>';
            } else if (field.type === 'checkbox') {
                html += '<div class="space-y-2">';
                if (field.options) {
                    field.options.forEach(option => {
                        html += `
                            <label class="flex items-center gap-2">
                                <input type="checkbox" class="checkbox checkbox-primary checkbox-sm" disabled>
                                <span class="text-sm">${option}</span>
                            </label>
                        `;
                    });
                }
                html += '</div>';
            } else if (field.type === 'signature') {
                html += `
                    <div class="border border-dashed border-base-300 rounded-lg p-8 text-center">
                        <i class="fa-sharp fa-signature text-3xl text-base-content/40 mb-2"></i>
                        <p class="text-sm text-base-content/60">Signature Area</p>
                    </div>
                `;
            }

            if (field.help_text) {
                html += `
                    <div class="label">
                        <span class="label-text-alt text-base-content/60">${field.help_text}</span>
                    </div>
                `;
            }

            return html;
        }

        // Process template variables for preview
        function processTemplatePreview(content) {
            // Fix common encoding issues with quotes and apostrophes first
            content = content
                .replace(/â€™/g, "'")  // Fix apostrophes
                .replace(/â€œ/g, '"')  // Fix opening quotes
                .replace(/â€/g, '"')   // Fix closing quotes
                .replace(/â€"/g, "–")  // Fix en-dash
                .replace(/â€"/g, "—")  // Fix em-dash
                .replace(/â€¦/g, "…")  // Fix ellipsis
                .replace(/Â/g, "");    // Remove extraneous Â characters

            const today = new Date();
            const dateShort = (today.getMonth() + 1).toString().padStart(2, '0') + '/' + 
                             today.getDate().toString().padStart(2, '0') + '/' + 
                             today.getFullYear();
            const dateFull = today.toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
            const nextYear = new Date(today);
            nextYear.setFullYear(today.getFullYear() + 1);
            const datePlusOneYear = nextYear.toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
            
            // Replace template variables with preview values
            content = content.replace(/{date_today_short}/g, `<span class="bg-info/20 px-1 rounded" title="Template variable: {date_today_short}">${dateShort}</span>`);
            content = content.replace(/{date_today_full}/g, `<span class="bg-info/20 px-1 rounded" title="Template variable: {date_today_full}">${dateFull}</span>`);
            content = content.replace(/{date_plus_1yr_full}/g, `<span class="bg-info/20 px-1 rounded" title="Template variable: {date_plus_1yr_full}">${datePlusOneYear}</span>`);
            content = content.replace(/{business_name}/g, `<span class="bg-warning/20 px-1 rounded" title="Template variable: {business_name}">BUSINESS_NAME</span>`);
            content = content.replace(/{full_name}/g, `<span class="bg-warning/20 px-1 rounded" title="Template variable: {full_name}">YOUR_NAME</span>`);
            
            return content;
        }

        // Auto-save function with debouncing
        function autoSave(message = 'Changes saved') {
            // Clear existing timeout
            if (autoSaveTimeout) {
                clearTimeout(autoSaveTimeout);
            }
            
            // Don't auto-save if already auto-saving
            if (isAutoSaving) {
                return;
            }
            
            // Update status immediately
            const statusIndicator = document.getElementById('saveStatusIndicator');
            statusIndicator.textContent = 'Saving changes...';
            statusIndicator.className = 'font-medium text-info';
            
            // Debounce auto-save by 1 second
            autoSaveTimeout = setTimeout(() => {
                performSave(true, message);
            }, 1000);
        }

        function saveFields() {
            performSave(false, 'Form fields saved successfully');
        }

        function performSave(isAutoSave = false, successMessage = 'Changes saved') {
            const saveBtn = document.getElementById('saveFieldsBtn');
            const statusIndicator = document.getElementById('saveStatusIndicator');
            
            // Set auto-saving flag
            if (isAutoSave) {
                isAutoSaving = true;
            }
            
            // Only disable manual save button if this is a manual save
            if (!isAutoSave) {
                saveBtn.disabled = true;
                saveBtn.innerHTML = '<i class="fa-sharp fa-spinner fa-spin"></i> Saving...';
            }
            
            statusIndicator.textContent = 'Saving changes...';
            statusIndicator.className = 'font-medium text-info';

            console.log('Saving fields:', currentFields); // Debug logging

            fetch(`{{ route('forms.save-fields', $form) }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    fields: currentFields
                })
            })
            .then(response => {
                console.log('Response status:', response.status); // Debug logging
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    return response.text().then(text => {
                        console.error('Non-JSON response:', text); // Debug logging
                        throw new Error('Server returned non-JSON response');
                    });
                }
                
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data); // Debug logging
                
                if (data.success) {
                    if (isAutoSave) {
                        statusIndicator.textContent = successMessage;
                        statusIndicator.className = 'font-medium text-success';
                        
                        setTimeout(() => {
                            statusIndicator.textContent = 'Changes will save automatically';
                            statusIndicator.className = 'font-medium text-base-content';
                        }, 2000);
                    } else {
                        statusIndicator.textContent = `Saved ${data.field_count || 0} fields successfully`;
                        statusIndicator.className = 'font-medium text-success';
                        
                        setTimeout(() => {
                            statusIndicator.textContent = 'Changes will save automatically';
                            statusIndicator.className = 'font-medium text-base-content';
                        }, 3000);
                    }
                } else {
                    throw new Error(data.message || 'Save failed');
                }
            })
            .catch(error => {
                console.error('Save error:', error); // Debug logging
                statusIndicator.textContent = 'Error saving changes';
                statusIndicator.className = 'font-medium text-error';
                
                // Only show error dialog for manual saves to avoid spam
                if (!isAutoSave) {
                    showError({
                        title: 'Save Failed',
                        message: 'There was an error saving your form fields: ' + error.message
                    });
                }
            })
            .finally(() => {
                // Reset auto-saving flag
                if (isAutoSave) {
                    isAutoSaving = false;
                }
                
                // Only re-enable manual save button if this was a manual save
                if (!isAutoSave) {
                    saveBtn.disabled = false;
                    saveBtn.innerHTML = '<i class="fa-sharp fa-save"></i> Save All Changes';
                }
            });
        }
    </script>
</x-app-layout>