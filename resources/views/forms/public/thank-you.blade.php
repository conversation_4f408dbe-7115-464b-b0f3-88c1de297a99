<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>Thank You - {{ $form->name }} - {{ config('app.name', 'ETRFlow') }}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/webp" href="{{ asset('etrflow-circlepadded.webp') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- FontAwesome -->
        <link href="{{ asset('fontawesome/css/fontawesome.min.css') }}" rel="stylesheet">
    <link href="{{ asset('fontawesome/css/sharp-solid.min.css') }}" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.jsx'])
</head>
<body class="font-sans antialiased bg-base-200">
    <div class="min-h-screen flex items-center justify-center">
        <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            
            <!-- Success Card -->
            <div class="bg-gradient-to-br from-success/5 via-primary/5 to-secondary/5 rounded-2xl border border-base-300/50 p-8 lg:p-12 relative overflow-hidden shadow-xl">
                <!-- Decorative Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 left-0 w-32 h-32 bg-success rounded-full -translate-x-16 -translate-y-16"></div>
                    <div class="absolute bottom-0 right-0 w-24 h-24 bg-primary rounded-full translate-x-12 translate-y-12"></div>
                </div>

                <div class="relative z-10 text-center">
                    <!-- Success Icon -->
                    <div class="avatar avatar-placeholder mb-6">
                        <div class="bg-gradient-to-br from-success to-success-focus text-success-content w-20 h-20 rounded-full shadow-lg">
                            <i class="fa-sharp fa-check text-3xl"></i>
                        </div>
                    </div>

                    <!-- Success Message -->
                    <h1 class="text-3xl font-bold text-base-content mb-4">Thank You!</h1>
                    <p class="text-xl text-base-content/80 mb-6">Your {{ $form->name }} has been submitted successfully.</p>

                    @if($form->thankyou_show_submission_details)
                        <!-- Submission Details -->
                        <div class="bg-base-100/50 backdrop-blur-sm rounded-xl p-6 mb-8 text-left">
                            <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
                                <i class="fa-sharp fa-info-circle text-primary"></i>
                                Submission Details
                            </h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div class="flex items-center gap-2">
                                    <i class="fa-sharp fa-hashtag text-primary"></i>
                                    <span class="font-medium">Reference:</span>
                                    <span class="font-mono">#{{ $submission->id }}</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <i class="fa-sharp fa-calendar text-primary"></i>
                                    <span class="font-medium">Submitted:</span>
                                    <span>{{ $submission->created_at->format('M j, Y g:i A') }}</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <i class="fa-sharp fa-user text-primary"></i>
                                    <span class="font-medium">Name:</span>
                                    <span>{{ $submission->submitter_name }}</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <i class="fa-sharp fa-check-circle text-success"></i>
                                    <span class="font-medium">Status:</span>
                                    <span>Successfully Submitted</span>
                                </div>
                                @if($submission->signed_at)
                                    <div class="flex items-center gap-2 md:col-span-2">
                                        <i class="fa-sharp fa-signature text-success"></i>
                                        <span class="font-medium">Digitally signed:</span>
                                        <span>{{ $submission->signed_at->format('M j, Y g:i A') }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    @if($form->thankyou_show_html_message && $form->thankyou_html_message)
                        <!-- Custom HTML Message -->
                        <div class="bg-base-100/50 backdrop-blur-sm rounded-xl p-6 mb-8 text-left">
                            <div class="prose prose-sm max-w-none">
                                {!! $submission->processHtmlContent($form->thankyou_html_message) !!}
                            </div>
                        </div>
                    @elseif(!$form->thankyou_show_html_message)
                        <!-- Default Next Steps -->
                        <div class="bg-base-100/50 backdrop-blur-sm rounded-xl p-6 mb-8 text-left">
                            <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
                                <i class="fa-sharp fa-list-check text-secondary"></i>
                                What Happens Next?
                            </h3>
                            
                            <div class="space-y-3 text-sm">
                                <div class="flex items-start gap-3">
                                    <div class="badge badge-primary badge-sm mt-1">1</div>
                                    <div>
                                        <p class="font-medium text-base-content">Review Process</p>
                                        <p class="text-base-content/70">Our team will review your submission to ensure all information is complete and accurate.</p>
                                    </div>
                                </div>
                                
                                @if($form->applies_discount_on_approval)
                                    <div class="flex items-start gap-3">
                                        <div class="badge badge-secondary badge-sm mt-1">2</div>
                                        <div>
                                            <p class="font-medium text-base-content">Account Setup</p>
                                            <p class="text-base-content/70">If approved, your account will be set up and any applicable discounts will be applied.</p>
                                        </div>
                                    </div>
                                @endif
                                
                                <div class="flex items-start gap-3">
                                    <div class="badge badge-accent badge-sm mt-1">{{ $form->applies_discount_on_approval ? '3' : '2' }}</div>
                                    <div>
                                        <p class="font-medium text-base-content">Contact & Follow-up</p>
                                        <p class="text-base-content/70">We'll contact you with any questions or to confirm the next steps.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if($form->thankyou_show_need_help)
                        <!-- Contact Information -->
                        <div class="bg-base-100/50 backdrop-blur-sm rounded-xl p-6 text-left">
                            <h3 class="text-lg font-semibold text-base-content mb-3 flex items-center gap-2">
                                <i class="fa-sharp fa-headset text-info"></i>
                                Need Help?
                            </h3>
                            <p class="text-sm text-base-content/70">
                                If you have any questions about your submission or need to make changes, 
                                please contact us and reference your submission #{{ $submission->id }}.
                            </p>
                        </div>
                    @endif

                    <!-- Action Buttons -->
                    @if($form->thankyou_enable_print || $form->thankyou_enable_close)
                        <div class="mt-8 flex flex-wrap gap-3 justify-center">
                            @if($form->thankyou_enable_print)
                                <button onclick="window.print()" class="btn btn-primary gap-2">
                                    <i class="fa-sharp fa-print"></i>
                                    Print Confirmation
                                </button>
                            @endif
                            
                            @if($form->thankyou_enable_close)
                                <button onclick="closeWindow()" class="btn btn-outline gap-2">
                                    <i class="fa-sharp fa-times"></i>
                                    Close Window
                                </button>
                            @endif
                        </div>
                    @endif
                </div>
            </div>

        </div>
    </div>

    <script>
        // Close window functionality
        function closeWindow() {
            // Try to close the window (only works if opened by script)
            if (window.opener || window.history.length === 1) {
                window.close();
            } else {
                // Fallback: go back or to home page
                if (confirm('Close this page? You will be taken to the previous page.')) {
                    // Clear form data from session storage before going back
                    clearFormDataFromPreviousPage();
                    window.history.back();
                }
            }
        }

        // Function to clear form data from the previous page
        function clearFormDataFromPreviousPage() {
            // Set a flag in sessionStorage to indicate form should be cleared
            sessionStorage.setItem('clearFormData', 'true');
        }

        // Auto-print functionality (optional)
        // setTimeout(() => {
        //     if (confirm('Would you like to print this confirmation for your records?')) {
        //         window.print();
        //     }
        // }, 2000);
    </script>
</body>
</html>