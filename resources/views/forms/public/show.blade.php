<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-theme="corporate">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $form->name }} - {{ config('app.name', 'ETRFlow') }}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/webp" href="{{ asset('etrflow-circlepadded.webp') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- FontAwesome -->

        <link href="{{ asset('fontawesome/css/fontawesome.min.css') }}" rel="stylesheet">
    <link href="{{ asset('fontawesome/css/sharp-solid.min.css') }}" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.jsx'])
    
    @if($form->recaptcha_enabled && config('services.recaptcha.site_key'))
        <!-- reCAPTCHA v3 -->
        <script src="https://www.google.com/recaptcha/api.js?render={{ config('services.recaptcha.site_key') }}"></script>
    @endif
</head>
<body class="font-sans antialiased bg-base-200">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-base-100 shadow-sm border-b border-base-300">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex items-center gap-4">
                    <div class="avatar avatar-placeholder">
                        <div class="bg-primary text-primary-content w-12 rounded-xl">
                            <i class="fa fa-sharp fa-file-lines text-xl"></i>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-base-content">{{ $form->name }}</h1>
                        @if($form->description)
                            <p class="text-base-content/70 mt-1">{{ $form->description }}</p>
                        @endif
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="py-8">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                
                <form method="POST" action="{{ route('forms.public.submit', $form->slug) }}" id="formSubmission" autocomplete="off">
                    @csrf

                    @if($form->requires_customer_link || $form->isContactFieldRequired('name') || $form->isContactFieldRequired('email') || $form->isContactFieldRequired('phone') || $form->isContactFieldRequired('address'))
                        <!-- Contact Information Card -->
                        <div class="card bg-base-100 shadow-md mb-6">
                            <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                                <div class="flex items-center gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-primary text-primary-content w-8 rounded-lg">
                                            <i class="fa-sharp fa-user text-sm"></i>
                                        </div>
                                    </div>
                                    <h4 class="text-lg font-semibold text-base-content">Contact Information</h4>
                                </div>
                            </div>
                            <div class="p-6 space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    @if($form->requires_customer_link || $form->isContactFieldRequired('name'))
                                        <!-- Full Name Field -->
                                        <div>
                                            <label for="submitter_name">
                                                <span class="label-text">
                                                    <i class="fa-sharp fa-user text-primary mr-2"></i>
                                                    Full Name <span class="text-error font-bold">*</span>
                                                </span>
                                            </label>
                                            <input type="text" name="submitter_name" id="submitter_name" class="input input-bordered w-full"
                                                   value="{{ old('submitter_name') }}" placeholder="Enter your full name" required autocomplete="off" data-form-field="true">
                                            @error('submitter_name')
                                            <div>
                                                <span class="label-text-alt text-error flex items-center gap-1">
                                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                                    {{ $message }}
                                                </span>
                                            </div>
                                            @enderror
                                        </div>
                                    @endif

                                    @if($form->requires_customer_link || $form->isContactFieldRequired('business_name'))
                                        <!-- Business Name Field -->
                                        <div>
                                            <label  for="business_name">
                                                <span class="label-text">
                                                    <i class="fa-sharp fa-building text-primary mr-2"></i>
                                                    Business Name <span class="text-base-content/60">(Optional)</span>
                                                </span>
                                            </label>
                                            <input type="text" name="business_name" id="business_name" class="input input-bordered w-full"
                                                   value="{{ old('business_name') }}" placeholder="Enter business name if applicable" autocomplete="off" data-form-field="true">
                                            @error('business_name')
                                            <div  >
                                                <span class="label-text-alt text-error flex items-center gap-1">
                                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                                    {{ $message }}
                                                </span>
                                            </div>
                                            @enderror
                                            <div  >
                                                <span class="label-text-alt text-base-content/60 text-xs">
                                                    <i class="fa-sharp fa-info-circle mr-1"></i>
                                                    Leave blank if you are a residential customer
                                                </span>
                                            </div>
                                        </div>
                                    @endif

                                    @if($form->isContactFieldRequired('email'))
                                        <div>
                                            <label   for="submitter_email">
                                                <span class="label-text">
                                                    <i class="fa-sharp fa-envelope text-primary mr-2"></i>
                                                    Email Address <span class="text-error font-bold">*</span>
                                                </span>
                                            </label>
                                            <input type="email" name="submitter_email" id="submitter_email" class="input input-bordered w-full"
                                                   value="{{ old('submitter_email') }}" placeholder="Enter your email address" required autocomplete="off" data-form-field="true">
                                            @error('submitter_email')
                                            <div  >
                                                <span class="label-text-alt text-error flex items-center gap-1">
                                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                                    {{ $message }}
                                                </span>
                                            </div>
                                            @enderror
                                            <div id="email-check-result" class="label hidden">
                                                <span class="label-text-alt text-info flex items-center gap-1">
                                                    <i class="fa-sharp fa-info-circle"></i>
                                                    <span id="email-check-message"></span>
                                                </span>
                                            </div>
                                        </div>
                                    @endif

                                    @if($form->isContactFieldRequired('phone'))
                                        <div>
                                            <label   for="submitter_phone">
                                                <span class="label-text">
                                                    <i class="fa-sharp fa-phone text-primary mr-2"></i>
                                                    Phone Number <span class="text-error font-bold">*</span>
                                                </span>
                                            </label>
                                            <input type="tel" name="submitter_phone" id="submitter_phone" class="input input-bordered w-full"
                                                   value="{{ old('submitter_phone') }}" placeholder="Enter your phone number" required autocomplete="off" data-form-field="true">
                                            @error('submitter_phone')
                                            <div  >
                                                <span class="label-text-alt text-error flex items-center gap-1">
                                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                                    {{ $message }}
                                                </span>
                                            </div>
                                            @enderror
                                        </div>
                                    @endif
                                </div>

                                @if($form->isContactFieldRequired('address'))
                                    <div>
                                        <label   for="address">
                                            <span class="label-text">
                                                <i class="fa-sharp fa-location-dot text-primary mr-2"></i>
                                                Address <span class="text-error font-bold">*</span>
                                            </span>
                                        </label>
                                        <x-google-maps-autocomplete-guest 
                                            id="address"
                                            name="address"
                                            :value="old('address')"
                                            placeholder="Enter address"
                                            type="textarea"
                                            rows="3"
                                            icon="fa-location-dot"
                                            iconColor="text-primary"
                                        />
                                    </div>
                                @endif
                            </div>
                        </div>
                    @else
                        <!-- No contact fields required - use defaults -->
                        <input type="hidden" name="submitter_name" value="Guest Submission">
                        <input type="hidden" name="submitter_email" value="<EMAIL>">
                    @endif

                    <!-- Form Fields -->
                    @if($form->fields->count() > 0)
                        <div class="card bg-base-100 shadow-md mb-6">
                            <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                                <div class="flex items-center gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                            <i class="fa-sharp fa-list text-sm"></i>
                                        </div>
                                    </div>
                                    <h4 class="text-lg font-semibold text-base-content">Form Details</h4>
                                </div>
                            </div>
                            <div class="p-6 space-y-6">
                                @foreach($form->fields->sortBy('order') as $field)
                                    @if($field->type === 'html')
                                        <!-- HTML Content Block -->
                                        <div class="html-content-styled html-template-content" data-template-content="{{ base64_encode($field->content) }}">
                                            {!! $field->content !!}
                                        </div>
                                    @else
                                        <!-- Form Input Field -->
                                        <div>
                                            @if($field->label)
                                                <label   for="fields_{{ $field->name }}">
                                                    <span class="label-text font-medium">
                                                        {{ $field->label }}
                                                        @if($field->required)
                                                            <span class="text-error font-bold">*</span>
                                                        @endif
                                                    </span>
                                                </label>
                                            @endif

                                            @if(in_array($field->type, ['text', 'email', 'phone', 'number', 'date']))
                                                <input type="{{ $field->type }}" 
                                                       name="fields[{{ $field->name }}]" 
                                                       id="fields_{{ $field->name }}" 
                                                       class="input input-bordered w-full"
                                                       value="{{ old('fields.' . $field->name) }}" 
                                                       placeholder="{{ $field->placeholder }}"
                                                       autocomplete="off" data-form-field="true"
                                                       {{ $field->required ? 'required' : '' }}>

                                            @elseif($field->type === 'textarea')
                                                <textarea name="fields[{{ $field->name }}]" 
                                                          id="fields_{{ $field->name }}" 
                                                          rows="4" 
                                                          class="textarea textarea-bordered w-full"
                                                          placeholder="{{ $field->placeholder }}"
                                                          autocomplete="off" data-form-field="true"
                                                          {{ $field->required ? 'required' : '' }}>{{ old('fields.' . $field->name) }}</textarea>

                                            @elseif($field->type === 'select')
                                                <select name="fields[{{ $field->name }}]" 
                                                        id="fields_{{ $field->name }}" 
                                                        class="select select-bordered w-full"
                                                        autocomplete="off" data-form-field="true"
                                                        {{ $field->required ? 'required' : '' }}>
                                                    <option value="">{{ $field->placeholder ?: 'Choose an option...' }}</option>
                                                    @foreach($field->getOptionsArray() as $option)
                                                        <option value="{{ is_array($option) ? $option['value'] : $option }}"
                                                                {{ old('fields.' . $field->name) === (is_array($option) ? $option['value'] : $option) ? 'selected' : '' }}>
                                                            {{ is_array($option) ? $option['label'] : $option }}
                                                        </option>
                                                    @endforeach
                                                </select>

                                            @elseif($field->type === 'radio')
                                                <div class="space-y-3">
                                                    @foreach($field->getOptionsArray() as $index => $option)
                                                        <label class="flex items-start gap-3 cursor-pointer">
                                                            <input type="radio" 
                                                                   name="fields[{{ $field->name }}]" 
                                                                   value="{{ is_array($option) ? $option['value'] : $option }}"
                                                                   class="radio radio-primary mt-0.5"
                                                                   {{ old('fields.' . $field->name) === (is_array($option) ? $option['value'] : $option) ? 'checked' : '' }}
                                                                   {{ $field->required && $loop->first ? 'required' : '' }}>
                                                            <span class="label-text">{{ is_array($option) ? $option['label'] : $option }}</span>
                                                        </label>
                                                    @endforeach
                                                </div>

                                            @elseif($field->type === 'checkbox')
                                                <div class="space-y-3">
                                                    @foreach($field->getOptionsArray() as $index => $option)
                                                        <label class="flex items-start gap-3 cursor-pointer">
                                                            <input type="checkbox" 
                                                                   name="fields[{{ $field->name }}][]" 
                                                                   value="{{ is_array($option) ? $option['value'] : $option }}"
                                                                   class="checkbox checkbox-primary mt-0.5"
                                                                   {{ in_array((is_array($option) ? $option['value'] : $option), old('fields.' . $field->name, [])) ? 'checked' : '' }}>
                                                            <span class="label-text">{{ is_array($option) ? $option['label'] : $option }}</span>
                                                        </label>
                                                    @endforeach
                                                </div>
                                            @endif

                                            @if($field->help_text)
                                                <div  >
                                                    <span class="label-text-alt text-base-content/60">{{ $field->help_text }}</span>
                                                </div>
                                            @endif

                                            @error('fields.' . $field->name)
                                            <div  >
                                                <span class="label-text-alt text-error flex items-center gap-1">
                                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                                    {{ $message }}
                                                </span>
                                            </div>
                                            @enderror
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Signature Section -->
                    @if($form->requires_signature)
                        <div class="card bg-base-100 shadow-md mb-6">
                            <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                                <div class="flex items-center gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-accent text-accent-content w-8 rounded-lg">
                                            <i class="fa-sharp fa-signature text-sm"></i>
                                        </div>
                                    </div>
                                    <h4 class="text-lg font-semibold text-base-content">Digital Signature</h4>
                                </div>
                            </div>
                            <div class="p-6 space-y-6">
                                <!-- Signature Information Fields -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label   for="signature_full_name">
                                            <span class="label-text font-medium">
                                                <i class="fa-sharp fa-user text-primary mr-2"></i>
                                                Full Name <span class="text-error font-bold">*</span>
                                            </span>
                                        </label>
                                        <input type="text" name="signature_full_name" id="signature_full_name" 
                                               class="input input-bordered w-full"
                                               value="{{ old('signature_full_name') }}" 
                                               placeholder="Enter your full legal name" required>
                                        @error('signature_full_name')
                                        <div  >
                                            <span class="label-text-alt text-error flex items-center gap-1">
                                                <i class="fa-sharp fa-exclamation-triangle"></i>
                                                {{ $message }}
                                            </span>
                                        </div>
                                        @enderror
                                    </div>

                                    <div>
                                        <label   for="signature_title">
                                            <span class="label-text font-medium">
                                                <i class="fa-sharp fa-briefcase text-primary mr-2"></i>
                                                Title/Position <span class="text-error font-bold">*</span>
                                            </span>
                                        </label>
                                        <input type="text" name="signature_title" id="signature_title" 
                                               class="input input-bordered w-full"
                                               value="{{ old('signature_title') }}" 
                                               placeholder="e.g., Owner, Manager, Authorized Representative" required>
                                        @error('signature_title')
                                        <div  >
                                            <span class="label-text-alt text-error flex items-center gap-1">
                                                <i class="fa-sharp fa-exclamation-triangle"></i>
                                                {{ $message }}
                                            </span>
                                        </div>
                                        @enderror
                                    </div>

                                    <div>
                                        <div class="mb-2">
                                            <span class="text-sm font-medium text-base-content/80">
                                                <i class="fa-sharp fa-calendar text-primary mr-2"></i>
                                                Date
                                            </span>
                                        </div>
                                        <label class="input input-bordered flex items-center gap-2">
                                            <span class="label text-base-content">
                                                <i class="fa-sharp fa-lock"></i>
                                            </span>
                                            <input type="date" name="signature_date" id="signature_date" 
                                                   class="grow text-base-content"
                                                   value="{{ old('signature_date', date('Y-m-d')) }}" 
                                                   readonly>
                                        </label>
                                        @error('signature_date')
                                        <div  >
                                            <span class="label-text-alt text-error flex items-center gap-1">
                                                <i class="fa-sharp fa-exclamation-triangle"></i>
                                                {{ $message }}
                                            </span>
                                        </div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Digital Signature Canvas -->
                                <div>
                                    <label  >
                                        <span class="label-text font-medium">
                                            <i class="fa-sharp fa-signature text-primary mr-2"></i>
                                            Digital Signature <span class="text-error font-bold">*</span>
                                        </span>
                                    </label>
                                    <div class="border border-base-300 rounded-lg p-4 bg-base-50">
                                        <canvas id="signatureCanvas" width="600" height="200" class="border border-dashed border-base-300 rounded bg-white cursor-crosshair w-full"></canvas>
                                        <div class="mt-3 flex gap-3">
                                            <button type="button" id="clearSignature" class="btn btn-outline btn-sm gap-2">
                                                <i class="fa-sharp fa-eraser"></i>
                                                Clear
                                            </button>
                                            <div class="text-sm text-base-content/60 flex items-center">
                                                <i class="fa-sharp fa-info-circle mr-2"></i>
                                                Sign above with your mouse or finger
                                            </div>
                                        </div>
                                    </div>
                                    <input type="hidden" name="signature" id="signatureData">
                                    @error('signature')
                                    <div  >
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>

                                <!-- Agreement Text -->
                                <div class="bg-info/10 border border-info/20 rounded-lg p-4">
                                    <div class="flex items-start gap-3">
                                        <i class="fa-sharp fa-info-circle text-info mt-1"></i>
                                        <div class="text-sm">
                                            <p class="font-medium text-info mb-1">Electronic Signature Agreement</p>
                                            <p class="text-base-content/70">
                                                By signing above, I acknowledge that my electronic signature is the legal equivalent of my manual signature and that I agree to be bound by the terms of this agreement.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Submit Section -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-success text-success-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-paper-plane text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Submit Form</h4>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="text-center">
                                <p class="text-base-content/70 mb-6">Please review your information above before submitting. Once submitted, your form will be reviewed by our team.</p>
                                
                                @if($form->recaptcha_enabled && config('services.recaptcha.site_key'))
                                    <!-- reCAPTCHA hidden token field -->
                                    <input type="hidden" name="recaptcha_token" id="recaptcha_token">
                                    
                                    <!-- reCAPTCHA notice -->
                                    <div class="text-xs text-base-content/60 mb-4">
                                        <i class="fa-sharp fa-shield-check text-success mr-1"></i>
                                        This form is protected by reCAPTCHA and the Google 
                                        <a href="https://policies.google.com/privacy" target="_blank" class="link link-primary">Privacy Policy</a> and 
                                        <a href="https://policies.google.com/terms" target="_blank" class="link link-primary">Terms of Service</a> apply.
                                    </div>
                                @endif
                                
                                <button type="submit" id="submitButton" class="btn btn-primary btn-lg gap-2 shadow-lg">
                                    <span id="submitText">
                                        <i class="fa-sharp fa-paper-plane"></i>
                                        Submit {{ $form->name }}
                                    </span>
                                    <span id="submitLoading" class="hidden">
                                        <span class="loading loading-bars loading-md"></span>
                                        Submitting...
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>

                </form>

            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-base-100 border-t border-base-300 mt-12">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="text-center text-sm text-base-content/60">
                    <p>&copy; {{ date('Y') }} {{ config('app.name', 'ETRFlow') }}. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Email checking functionality
        let emailCheckTimeout;
        const submitterEmailField = document.getElementById('submitter_email');
        if (submitterEmailField) {
            submitterEmailField.addEventListener('input', function() {
                clearTimeout(emailCheckTimeout);
                const email = this.value;
                
                if (email && email.includes('@')) {
                    emailCheckTimeout = setTimeout(() => {
                        checkEmail(email);
                    }, 1000);
                } else {
                    hideEmailResult();
                }
            });
        }

        function checkEmail(email) {
            fetch('{{ route("forms.public.check-email") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('email-check-result');
                const messageSpan = document.getElementById('email-check-message');
                
                if (data.exists) {
                    messageSpan.textContent = `We found an existing account for ${data.customer.name}. Your submission will be linked to this account.`;
                    resultDiv.classList.remove('hidden');
                    
                    // Optionally pre-fill name and phone if empty
                    const nameField = document.getElementById('submitter_name');
                    const phoneField = document.getElementById('submitter_phone');
                    
                    if (!nameField.value && data.customer.name) {
                        nameField.value = data.customer.name;
                    }
                    if (!phoneField.value && data.customer.phone) {
                        phoneField.value = data.customer.phone;
                    }
                } else {
                    hideEmailResult();
                }
            })
            .catch(error => {
                console.error('Error checking email:', error);
                hideEmailResult();
            });
        }

        function hideEmailResult() {
            document.getElementById('email-check-result').classList.add('hidden');
        }

        @if($form->requires_signature)
        // Signature pad functionality
        const canvas = document.getElementById('signatureCanvas');
        const ctx = canvas.getContext('2d');
        let isDrawing = false;
        let hasSignature = false;

        // Set canvas size
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * 2;
            canvas.height = rect.height * 2;
            ctx.scale(2, 2);
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // Mouse events
        canvas.addEventListener('mousedown', startDrawing);
        canvas.addEventListener('mousemove', draw);
        canvas.addEventListener('mouseup', stopDrawing);
        canvas.addEventListener('mouseout', stopDrawing);

        // Touch events
        canvas.addEventListener('touchstart', handleTouch);
        canvas.addEventListener('touchmove', handleTouch);
        canvas.addEventListener('touchend', stopDrawing);

        function startDrawing(e) {
            isDrawing = true;
            const rect = canvas.getBoundingClientRect();
            ctx.beginPath();
            ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
        }

        function draw(e) {
            if (!isDrawing) return;
            
            const rect = canvas.getBoundingClientRect();
            ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
            ctx.stroke();
            hasSignature = true;
        }

        function stopDrawing() {
            if (isDrawing) {
                isDrawing = false;
                updateSignatureData();
            }
        }

        function handleTouch(e) {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 
                                            e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            canvas.dispatchEvent(mouseEvent);
        }

        function updateSignatureData() {
            if (hasSignature) {
                document.getElementById('signatureData').value = canvas.toDataURL();
            }
        }

        // Clear signature
        document.getElementById('clearSignature').addEventListener('click', function() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            document.getElementById('signatureData').value = '';
            hasSignature = false;
        });

        // Form submission validation
        document.getElementById('formSubmission').addEventListener('submit', function(e) {
            if (hasSignature === false) {
                e.preventDefault();
                alert('Please provide a signature before submitting the form.');
                return false;
            }
            
            updateSignatureData();
        });

        // Synchronize name fields
        function synchronizeNameFields() {
            const contactNameField = document.getElementById('submitter_name');
            const signatureNameField = document.getElementById('signature_full_name');
            
            if (contactNameField && signatureNameField) {
                // Initialize signature field with contact name value
                if (contactNameField.value && !signatureNameField.value) {
                    signatureNameField.value = contactNameField.value;
                }
                
                // Listen for changes in contact name field
                contactNameField.addEventListener('input', function() {
                    signatureNameField.value = this.value;
                });
                
                // Listen for changes in signature name field
                signatureNameField.addEventListener('input', function() {
                    contactNameField.value = this.value;
                });
            }
        }

        // Initialize name synchronization when DOM is ready
        document.addEventListener('DOMContentLoaded', synchronizeNameFields);
        @endif

        // Utility function to fix encoding issues
        function fixEncodingIssues(content) {
            return content
                // Fix straight quote encoding issues (these are the problematic ones)
                .replace(/â€™/g, "'")  // Fix corrupted apostrophes
                .replace(/â€œ/g, '"')  // Fix corrupted opening quotes  
                .replace(/â€/g, '"')   // Fix corrupted closing quotes
                .replace(/â/g, "'")    // Fix corrupted straight apostrophes
                .replace(/â/g, '"')    // Fix corrupted straight quotes
                // Fix other common encoding issues
                .replace(/â€"/g, "–")  // Fix en-dash
                .replace(/â€"/g, "—")  // Fix em-dash
                .replace(/â€¦/g, "…")  // Fix ellipsis
                .replace(/Â/g, "");    // Remove extraneous Â characters
        }

        // Apply HTML content styling to all HTML sections
        document.addEventListener('DOMContentLoaded', function() {
            // Apply .html-content-styled class to all HTML content sections
            const htmlContentElements = document.querySelectorAll('.html-template-content');
            htmlContentElements.forEach(element => {
                if (!element.classList.contains('html-content-styled')) {
                    element.classList.add('html-content-styled');
                }
                
                // Fix encoding issues in existing content
                const currentContent = element.innerHTML;
                const fixedContent = fixEncodingIssues(currentContent);
                if (fixedContent !== currentContent) {
                    element.innerHTML = fixedContent;
                }
            });
        });

        // Template Variable Replacement System
        document.addEventListener('DOMContentLoaded', function() {
            // Store original HTML content for each template block
            const templateBlocks = document.querySelectorAll('.html-template-content');
            const originalContent = new Map();
            
            templateBlocks.forEach(block => {
                const encodedContent = block.getAttribute('data-template-content');
                if (encodedContent) {
                    try {
                        const decodedContent = atob(encodedContent);
                        const fixedContent = fixEncodingIssues(decodedContent);
                        originalContent.set(block, fixedContent);
                    } catch (e) {
                        console.error('Failed to decode template content:', e);
                    }
                }
            });
            
            // Function to update template variables
            function updateTemplateVariables() {
                const today = new Date();
                const dateShort = (today.getMonth() + 1).toString().padStart(2, '0') + '/' + 
                                 today.getDate().toString().padStart(2, '0') + '/' + 
                                 today.getFullYear();
                const dateFull = today.toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });
                const nextYear = new Date(today);
                nextYear.setFullYear(today.getFullYear() + 1);
                const datePlusOneYear = nextYear.toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });
                
                // Get form field values
                const businessNameField = document.getElementById('business_name');
                const fullNameField = document.getElementById('submitter_name');
                
                const businessName = businessNameField ? businessNameField.value : 'BUSINESS_NAME';
                const fullName = fullNameField ? fullNameField.value : 'YOUR_NAME';
                
                // Process each template block
                templateBlocks.forEach(block => {
                    let content = originalContent.get(block) || block.innerHTML;
                    
                    // Fix common encoding issues with quotes and apostrophes
                    content = fixEncodingIssues(content);
                    
                    // Replace date variables
                    content = content.replace(/{date_today_short}/g, dateShort);
                    content = content.replace(/{date_today_full}/g, dateFull);
                    content = content.replace(/{date_plus_1yr_full}/g, datePlusOneYear);
                    
                    // Replace name variables with placeholder or actual value
                    content = content.replace(/{business_name}/g, businessName || 'BUSINESS_NAME');
                    content = content.replace(/{full_name}/g, fullName || 'YOUR_NAME');
                    
                    // Update the HTML
                    block.innerHTML = content;
                });
            }
            
            // Initial update
            updateTemplateVariables();
            
            // Update when business name or full name changes
            const businessNameInput = document.getElementById('business_name');
            const fullNameInput = document.getElementById('submitter_name');
            
            if (businessNameInput) {
                businessNameInput.addEventListener('input', updateTemplateVariables);
            }
            
            if (fullNameInput) {
                fullNameInput.addEventListener('input', updateTemplateVariables);
            }
        });


        // Enhanced form submission handling
        document.getElementById('formSubmission').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitButton = document.getElementById('submitButton');
            const submitText = document.getElementById('submitText');
            const submitLoading = document.getElementById('submitLoading');
            const form = this;
            
            // Disable button and show loading
            submitButton.disabled = true;
            submitText.classList.add('hidden');
            submitLoading.classList.remove('hidden');
            
            try {
                @if($form->recaptcha_enabled && config('services.recaptcha.site_key'))
                // Generate reCAPTCHA token
                const recaptchaToken = await grecaptcha.execute('{{ config('services.recaptcha.site_key') }}', {action: 'form_submit'});
                document.getElementById('recaptcha_token').value = recaptchaToken;
                @endif
                
                const formData = new FormData(form);
                
                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json'
                    }
                });
                
                // Get response text first to see what we're actually getting
                const responseText = await response.text();
                console.log('Response status:', response.status);
                console.log('Response text:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('Failed to parse JSON response:', parseError);
                    console.error('Raw response:', responseText.substring(0, 500) + '...');
                    throw new Error('Server returned invalid response. Please check the browser console for details.');
                }
                
                if (response.ok && data.success) {
                    // Success - show success message
                    showSuccessMessage(data.message || 'Form submitted successfully!');
                    
                    // Optionally redirect after success
                    if (data.redirect_url) {
                        setTimeout(() => {
                            window.location.href = data.redirect_url;
                        }, 2000);
                    }
                } else {
                    // Handle validation errors or other errors
                    if (data.errors) {
                        showValidationErrors(data.errors);
                    } else {
                        showErrorMessage(data.message || 'An error occurred while submitting the form.');
                    }
                }
            } catch (error) {
                console.error('Form submission error:', error);
                showErrorMessage('Network error. Please check your connection and try again.');
            } finally {
                // Re-enable button and hide loading
                submitButton.disabled = false;
                submitText.classList.remove('hidden');
                submitLoading.classList.add('hidden');
            }
        });
        
        function showSuccessMessage(message) {
            // Create success alert
            const successDiv = document.createElement('div');
            successDiv.className = 'alert alert-success mb-6 animate-pulse';
            successDiv.innerHTML = `
                <i class="fa-sharp fa-check-circle"></i>
                <span>${message}</span>
            `;
            
            // Insert at top of form
            const form = document.getElementById('formSubmission');
            form.parentNode.insertBefore(successDiv, form);
            
            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
            
            // Hide form after success
            setTimeout(() => {
                form.style.opacity = '0.5';
                form.style.pointerEvents = 'none';
            }, 1000);
        }
        
        function showErrorMessage(message) {
            // Remove any existing error alerts
            const existingErrors = document.querySelectorAll('.alert-error');
            existingErrors.forEach(error => error.remove());
            
            // Create error alert
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-error mb-6';
            errorDiv.innerHTML = `
                <i class="fa-sharp fa-exclamation-triangle"></i>
                <span>${message}</span>
            `;
            
            // Insert at top of form
            const form = document.getElementById('formSubmission');
            form.parentNode.insertBefore(errorDiv, form);
            
            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
            
            // Auto-hide after 10 seconds
            setTimeout(() => {
                errorDiv.remove();
            }, 10000);
        }
        
        function showValidationErrors(errors) {
            // Clear existing field errors
            document.querySelectorAll('.field-error').forEach(el => el.remove());
            document.querySelectorAll('.input-error, .textarea-error, .select-error').forEach(el => {
                el.classList.remove('input-error', 'textarea-error', 'select-error');
            });
            
            let hasErrors = false;
            let firstErrorElement = null;
            
            Object.keys(errors).forEach(fieldName => {
                const errorMessages = Array.isArray(errors[fieldName]) ? errors[fieldName] : [errors[fieldName]];
                
                // Find the field element
                let fieldElement = document.querySelector(`[name="${fieldName}"]`);
                
                // Handle nested field names like fields.field_name
                if (!fieldElement && fieldName.includes('.')) {
                    const parts = fieldName.split('.');
                    if (parts[0] === 'fields') {
                        fieldElement = document.querySelector(`[name="fields[${parts[1]}]"]`);
                    }
                }
                
                if (fieldElement) {
                    hasErrors = true;
                    
                    // Add error styling
                    if (fieldElement.classList.contains('input')) {
                        fieldElement.classList.add('input-error');
                    } else if (fieldElement.classList.contains('textarea')) {
                        fieldElement.classList.add('textarea-error');
                    } else if (fieldElement.classList.contains('select')) {
                        fieldElement.classList.add('select-error');
                    }
                    
                    // Create error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'field-error';
                    errorDiv.innerHTML = `
                        <span class="label-text-alt text-error flex items-center gap-1">
                            <i class="fa-sharp fa-exclamation-triangle"></i>
                            ${errorMessages.join(', ')}
                        </span>
                    `;
                    
                    // Insert error message after the field
                    fieldElement.parentNode.insertBefore(errorDiv, fieldElement.nextSibling);
                    
                    // Remember first error for scrolling
                    if (!firstErrorElement) {
                        firstErrorElement = fieldElement;
                    }
                }
            });
            
            if (hasErrors) {
                showErrorMessage('Please correct the errors below and try again.');
                
                // Scroll to first error
                if (firstErrorElement) {
                    setTimeout(() => {
                        firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        firstErrorElement.focus();
                    }, 500);
                }
            }
        }

        // Clear form data functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we should clear form data (when returning from thank you page)
            if (sessionStorage.getItem('clearFormData') === 'true') {
                clearAllFormFields();
                sessionStorage.removeItem('clearFormData');
                
                // Show a subtle notification that the form has been cleared
                showSuccessMessage('Form has been cleared for new entry.');
            }
        });

        function clearAllFormFields() {
            // Clear all form fields marked with data-form-field attribute
            const formFields = document.querySelectorAll('[data-form-field="true"]');
            formFields.forEach(field => {
                if (field.type === 'text' || field.type === 'email' || field.type === 'tel' || 
                    field.type === 'number' || field.type === 'date' || field.tagName === 'TEXTAREA') {
                    field.value = '';
                } else if (field.tagName === 'SELECT') {
                    field.selectedIndex = 0;
                } else if (field.type === 'checkbox' || field.type === 'radio') {
                    field.checked = false;
                }
            });

            // Also clear any signature canvases if they exist
            const signatureCanvases = document.querySelectorAll('canvas[data-signature]');
            signatureCanvases.forEach(canvas => {
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
            });

            // Clear browser history state to prevent back button from restoring data
            if (window.history.replaceState) {
                window.history.replaceState({}, document.title, window.location.href);
            }
        }
    </script>

    <!-- Component Scripts -->
    @stack('scripts')
    
    <!-- Component Styles -->
    @stack('styles')
</body>
</html>