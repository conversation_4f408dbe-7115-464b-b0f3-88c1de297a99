<x-app-layout
    page-title="Custom Forms"
    page-icon="fa-sharp fa-file-lines"
    :primary-buttons="[
        ['type' => 'create', 'route' => route('forms.create'), 'text' => 'Create Form', 'permission' => 'manage_forms']
    ]"
    :action-buttons="[
        ['name' => 'View All Submissions', 'route' => route('form-submissions.index'), 'icon' => 'fa-sharp fa-paper-plane', 'class' => 'btn btn-outline btn-sm gap-2', 'permission' => 'view_form_submissions']
    ]"
    :breadcrumbs="[
        ['name' => 'Custom Forms']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-8">

            <!-- Header Card -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-base-content">Custom Forms</h1>
                            <p class="text-base-content/70 mt-1 text-lg">Create and manage dynamic forms for service agreements and data collection</p>
                        </div>
                        <div class="hidden lg:flex items-center gap-2 text-base-content/60">
                            <div class="stat">
                                <div class="stat-title text-xs">Total Forms</div>
                                <div class="stat-value text-2xl">{{ $forms->total() }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search & Filter Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-magnifying-glass text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Search & Filter</h4>
                    </div>
                </div>
                <div class="p-6 space-y-6">
                    <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="label" for="search">
                                <span class="label-text">
                                    <i class="fa-sharp fa-magnifying-glass text-primary mr-2"></i>
                                    Search Forms
                                </span>
                            </label>
                            <input type="text" name="search" id="search" class="input input-bordered w-full"
                                   value="{{ request('search') }}" placeholder="Search by name or description...">
                        </div>
                        
                        <div>
                            <label class="label" for="requires_approval">
                                <span class="label-text">
                                    <i class="fa-sharp fa-check-circle text-primary mr-2"></i>
                                    Approval Required
                                </span>
                            </label>
                            <select name="requires_approval" id="requires_approval" class="select select-bordered w-full">
                                <option value="">All Forms</option>
                                <option value="1" {{ request('requires_approval') === '1' ? 'selected' : '' }}>Requires Approval</option>
                                <option value="0" {{ request('requires_approval') === '0' ? 'selected' : '' }}>No Approval Needed</option>
                            </select>
                        </div>

                        <div>
                            <label class="label" for="is_active">
                                <span class="label-text">
                                    <i class="fa-sharp fa-toggle-on text-primary mr-2"></i>
                                    Status
                                </span>
                            </label>
                            <select name="is_active" id="is_active" class="select select-bordered w-full">
                                <option value="">All Status</option>
                                <option value="1" {{ request('is_active') === '1' ? 'selected' : '' }}>Active</option>
                                <option value="0" {{ request('is_active') === '0' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>

                        <div class="flex items-end">
                            <button type="submit" class="btn btn-primary gap-2 w-full">
                                <i class="fa-sharp fa-search"></i>
                                Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Forms Data Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                <i class="fa-sharp fa-file-lines text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">Forms</h4>
                    </div>
                </div>

                <!-- Sorting Controls -->
                <div class="px-6 py-4 border-b border-base-300/50 bg-base-50">
                    <form method="GET" class="flex flex-wrap items-center gap-4">
                        <input type="hidden" name="search" value="{{ request('search') }}">
                        <input type="hidden" name="type" value="{{ request('type') }}">
                        <input type="hidden" name="is_active" value="{{ request('is_active') }}">
                        
                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium text-base-content/70">Sort by:</label>
                            <select name="sort" class="select select-bordered select-sm w-auto min-w-32" onchange="this.form.submit()">
                                <option value="id" {{ request('sort') === 'id' ? 'selected' : '' }}>ID</option>
                                <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>Name</option>
                                <option value="requires_approval" {{ request('sort') === 'requires_approval' ? 'selected' : '' }}>Approval Required</option>
                                <option value="created_at" {{ request('sort') === 'created_at' ? 'selected' : '' }}>Created</option>
                            </select>
                        </div>
                    </form>
                </div>

                @if($forms->count() > 0)
                    <!-- Desktop Table Headers -->
                    <div class="hidden lg:grid lg:grid-cols-12 gap-3 items-center text-sm font-semibold text-base-content/80 bg-base-200/30 p-4 border-b border-base-200">
                        <div class="col-span-3">Form Name</div>
                        <div class="col-span-2">Requirements</div>
                        <div class="col-span-1 text-center">Status</div>
                        <div class="col-span-1 text-center">Fields</div>
                        <div class="col-span-1 text-center">Submissions</div>
                        <div class="col-span-2">Created</div>
                        <div class="col-span-2 text-center">Actions</div>
                    </div>
                    
                    <div class="divide-y divide-base-200/50">
                        @foreach ($forms as $form)
                        <div class="p-3 lg:p-4 hover:bg-base-200/50 transition-all duration-200 {{ $loop->even ? 'bg-base-100' : 'bg-base-200/30' }}">
                            <!-- Mobile Layout -->
                            <div class="lg:hidden">
                                <div class="flex items-center gap-2 mb-2">
                                    <div class="flex-1 min-w-0">
                                        <a href="{{ route('forms.show', $form) }}" class="link-primary font-medium hover:link-hover text-lg">
                                            {{ $form->name }}
                                        </a>
                                    </div>
                                    <div class="badge badge-sm {{ $form->is_active ? 'badge-success' : 'badge-error' }}">
                                        {{ $form->is_active ? 'Active' : 'Inactive' }}
                                    </div>
                                </div>
                                <!-- Two Column Details -->
                                <div class="grid grid-cols-2 gap-3 mb-3 text-xs">
                                    <div class="space-y-1">
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-check-circle text-primary"></i>
                                            <span class="font-medium">Approval:</span>
                                            <span>{{ $form->requires_approval ? 'Required' : 'Not Required' }}</span>
                                        </div>
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-signature text-primary"></i>
                                            <span class="font-medium">Signature:</span>
                                            <span>{{ $form->requires_signature ? 'Required' : 'Not Required' }}</span>
                                        </div>
                                    </div>
                                    <div class="space-y-1">
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-list text-primary"></i>
                                            <span class="font-medium">Fields:</span>
                                            <span>{{ $form->fields_count ?? 0 }}</span>
                                        </div>
                                        <div class="flex items-center gap-1">
                                            <i class="fa-sharp fa-paper-plane text-primary"></i>
                                            <span class="font-medium">Submissions:</span>
                                            <span>{{ $form->submissions_count ?? 0 }}</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- Mobile Action Bar -->
                                <div class="flex gap-2 pt-2 border-t border-base-200/50">
                                    <a href="{{ route('forms.show', $form) }}" class="btn btn-primary btn-sm flex-1 gap-1">
                                        <i class="fa-sharp fa-eye"></i>
                                        View
                                    </a>
                                    @if(($form->submissions_count ?? 0) > 0)
                                        @perms('view_form_submissions')
                                            <a href="{{ route('form-submissions.index', ['form_id' => $form->id]) }}" class="btn btn-accent btn-sm flex-1 gap-1">
                                                <i class="fa-sharp fa-paper-plane"></i>
                                                Submissions
                                            </a>
                                        @endperms
                                    @endif
                                    @perms('manage_forms')
                                        <a href="{{ route('forms.builder', $form) }}" class="btn btn-secondary btn-sm flex-1 gap-1">
                                            <i class="fa-sharp fa-edit"></i>
                                            Builder
                                        </a>
                                    @endperms
                                </div>
                            </div>

                            <!-- Desktop Layout -->
                            <div class="hidden lg:grid lg:grid-cols-12 gap-3 items-center text-sm">
                                <div class="col-span-3">
                                    <a href="{{ route('forms.show', $form) }}" class="link-primary font-medium hover:link-hover">
                                        {{ $form->name }}
                                    </a>
                                    @if($form->description)
                                        <p class="text-xs text-base-content/60 mt-1">{{ Str::limit($form->description, 60) }}</p>
                                    @endif
                                </div>
                                <div class="col-span-2">
                                    <div class="flex gap-1 flex-wrap">
                                        @if($form->requires_customer_link)
                                            <span class="badge badge-sm badge-accent">Customer Link</span>
                                        @endif
                                        @if($form->requires_approval)
                                            <span class="badge badge-sm badge-secondary">Approval</span>
                                        @endif
                                        @if($form->applies_discount_on_approval)
                                            <span class="badge badge-sm badge-info">Discount</span>
                                        @endif
                                        @if($form->generate_pdf_on_approval)
                                            <span class="badge badge-sm badge-warning">Auto-PDF</span>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-span-1 text-center">
                                    <span class="badge badge-sm {{ $form->is_active ? 'badge-success' : 'badge-error' }}">
                                        {{ $form->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </div>
                                <div class="col-span-1 text-center">
                                    {{ $form->fields_count ?? 0 }}
                                </div>
                                <div class="col-span-1 text-center">
                                    @if(($form->submissions_count ?? 0) > 0)
                                        <a href="{{ route('form-submissions.index', ['form_id' => $form->id]) }}" class="link link-primary hover:link-hover font-medium">
                                            {{ $form->submissions_count ?? 0 }}
                                        </a>
                                    @else
                                        <span class="text-base-content/50">0</span>
                                    @endif
                                </div>
                                <div class="col-span-2 text-xs text-base-content/60">
                                    {{ $form->created_at->format('M j, Y') }}
                                </div>
                                <div class="col-span-2">
                                    <div class="flex gap-1 justify-center">
                                        <div class="tooltip" data-tip="View Form">
                                            <a href="{{ route('forms.show', $form) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-primary hover:text-primary-content transition-all">
                                                <i class="fa-sharp fa-eye"></i>
                                            </a>
                                        </div>
                                        @if(($form->submissions_count ?? 0) > 0)
                                            @perms('view_form_submissions')
                                                <div class="tooltip" data-tip="View Submissions">
                                                    <a href="{{ route('form-submissions.index', ['form_id' => $form->id]) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-accent hover:text-accent-content transition-all">
                                                        <i class="fa-sharp fa-paper-plane"></i>
                                                    </a>
                                                </div>
                                            @endperms
                                        @endif
                                        @perms('manage_forms')
                                            <div class="tooltip" data-tip="Form Builder">
                                                <a href="{{ route('forms.builder', $form) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-secondary hover:text-secondary-content transition-all">
                                                    <i class="fa-sharp fa-edit"></i>
                                                </a>
                                            </div>
                                            <div class="tooltip" data-tip="Edit Form">
                                                <a href="{{ route('forms.edit', $form) }}" class="btn btn-sm btn-circle btn-ghost hover:bg-accent hover:text-accent-content transition-all">
                                                    <i class="fa-sharp fa-cog"></i>
                                                </a>
                                            </div>
                                        @endperms
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    @if($forms->hasPages())
                        <div class="px-6 py-4 border-t border-base-300/50 bg-base-50">
                            <x-pagination :paginator="$forms" :pagination="10" />
                        </div>
                    @endif
                @else
                    <!-- Empty State -->
                    <div class="text-center text-base-content/70 py-12">
                        <div class="flex flex-col items-center gap-4">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                    <i class="fa-sharp fa-file-lines text-2xl"></i>
                                </div>
                            </div>
                            <div class="text-center">
                                <h3 class="text-lg font-medium text-base-content/80">No forms found</h3>
                                <p class="text-sm text-base-content/60 mt-1">Create your first custom form to get started</p>
                            </div>
                            @perms('manage_forms')
                                <a href="{{ route('forms.create') }}" class="btn btn-primary btn-sm gap-2">
                                    <i class="fa-sharp fa-plus"></i>
                                    Create First Form
                                </a>
                            @endperms
                        </div>
                    </div>
                @endif
            </div>

        </div>
    </div>
</x-app-layout>