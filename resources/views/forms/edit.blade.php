<x-app-layout
    page-title="Edit Form: {{ $form->name }}"
    page-icon="fa-sharp fa-edit"
    :action-buttons="[
        ['name' => 'Form Builder', 'route' => route('forms.builder', $form), 'icon' => 'fa-sharp fa-edit', 'class' => 'btn btn-secondary btn-sm gap-2'],
        ['name' => 'Preview Form', 'route' => route('forms.preview', $form), 'icon' => 'fa-sharp fa-eye', 'class' => 'btn btn-info btn-sm gap-2']
    ]"
    :breadcrumbs="[
        ['name' => 'Custom Forms', 'route' => 'forms.index'],
        ['name' => $form->name, 'route' => 'forms.show', 'params' => [$form]],
        ['name' => 'Edit']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

            <!-- Introduction Card -->
            <div class="bg-gradient-to-br from-primary/5 via-accent/5 to-secondary/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
                <div class="relative z-10">
                    <div class="flex items-start gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-gradient-to-br from-primary to-primary-focus text-primary-content w-12 rounded-xl shadow-lg">
                                <i class="fa-sharp fa-edit text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-2xl font-bold text-base-content mb-2">Edit Form Settings</h3>
                            <p class="text-base-content/70 text-base leading-relaxed">Update your form's basic information, behavior settings, and service agreement configuration.</p>
                        </div>
                    </div>
                </div>
            </div>

            <form method="POST" action="{{ route('forms.update', $form) }}" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Basic Information -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-info-circle text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Basic Information</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div>
                            <label class="label">
                                <span class="label-text font-medium">Form Name <span class="text-error">*</span></span>
                            </label>
                            <input type="text" name="name" value="{{ old('name', $form->name) }}" 
                                   class="input input-bordered w-full @error('name') input-error @enderror" required>
                            @error('name')
                                <div class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </div>
                            @enderror
                        </div>

                        <div>
                            <label class="label">
                                <span class="label-text font-medium">Description</span>
                            </label>
                            <textarea name="description" rows="3" 
                                      class="textarea textarea-bordered w-full @error('description') textarea-error @enderror"
                                      placeholder="Brief description of this form's purpose...">{{ old('description', $form->description) }}</textarea>
                            @error('description')
                                <div class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Form Behavior -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-cog text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Form Behavior</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="is_active" value="0">
                                    <input type="checkbox" name="is_active" value="1" 
                                           class="checkbox checkbox-primary" {{ old('is_active', $form->is_active) ? 'checked' : '' }}>
                                    <div>
                                        <span class="label-text font-medium">Active Form</span>
                                        <p class="text-xs text-base-content/60">Allow public submissions</p>
                                    </div>
                                </label>
                            </div>
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="requires_signature" value="0">
                                    <input type="checkbox" name="requires_signature" value="1" 
                                           class="checkbox checkbox-warning" {{ old('requires_signature', $form->requires_signature) ? 'checked' : '' }}>
                                    <div>
                                        <span class="label-text font-medium">Requires Signature</span>
                                        <p class="text-xs text-base-content/60">Digital signature required</p>
                                    </div>
                                </label>
                            </div>
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="requires_customer_link" value="0">
                                    <input type="checkbox" name="requires_customer_link" value="1" 
                                           class="checkbox checkbox-accent" 
                                           {{ old('requires_customer_link', $form->requires_customer_link) ? 'checked' : '' }}
                                           onchange="toggleCustomerLinkSettings(this.checked)">
                                    <div>
                                        <span class="label-text font-medium">Requires Customer Link</span>
                                        <p class="text-xs text-base-content/60">Link to customer accounts</p>
                                    </div>
                                </label>
                            </div>
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="requires_approval" value="0">
                                    <input type="checkbox" name="requires_approval" value="1" 
                                           class="checkbox checkbox-secondary" 
                                           {{ old('requires_approval', $form->requires_approval) ? 'checked' : '' }}
                                           onchange="toggleApprovalSettings(this.checked)">
                                    <div>
                                        <span class="label-text font-medium">Requires Approval</span>
                                        <p class="text-xs text-base-content/60">Admin approval needed</p>
                                    </div>
                                </label>
                            </div>
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="recaptcha_enabled" value="0">
                                    <input type="checkbox" name="recaptcha_enabled" value="1" 
                                           class="checkbox checkbox-info" 
                                           {{ old('recaptcha_enabled', $form->recaptcha_enabled) ? 'checked' : '' }}>
                                    <div>
                                        <span class="label-text font-medium">Enable reCAPTCHA v3</span>
                                        <p class="text-xs text-base-content/60">Protect against spam and bots</p>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Link Settings -->
                <div id="customer-link-settings" class="card bg-base-100 shadow-md" 
                     style="display: {{ old('requires_customer_link', $form->requires_customer_link) ? 'block' : 'none' }}">
                    <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-accent text-accent-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-user-tie text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Customer Link Settings</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div>
                            <h5 class="font-medium text-base-content mb-3">Required Contact Fields</h5>
                            <p class="text-sm text-base-content/60 mb-4">Select which contact fields are required for form submission</p>
                            <div class="alert alert-info mb-4">
                                <i class="fa-sharp fa-info-circle"></i>
                                <div>
                                    <p class="text-sm"><strong>Simplified Contact System:</strong> Forms will show "Full Name" and "Business Name (Optional)" fields.</p>
                                    <p class="text-sm">If business name is filled, customer becomes business type with full name as contact person.</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                @php
                                    $contactFields = old('required_contact_fields', $form->required_contact_fields ?? []);
                                @endphp
                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-3">
                                        <input type="hidden" name="required_contact_fields[name]" value="0">
                                        <input type="checkbox" name="required_contact_fields[name]" value="1" 
                                               class="checkbox checkbox-sm" {{ ($contactFields['name'] ?? true) ? 'checked' : '' }}>
                                        <span class="label-text">Full Name</span>
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-3">
                                        <input type="hidden" name="required_contact_fields[email]" value="0">
                                        <input type="checkbox" name="required_contact_fields[email]" value="1" 
                                               class="checkbox checkbox-sm" {{ ($contactFields['email'] ?? true) ? 'checked' : '' }}>
                                        <span class="label-text">Email Address</span>
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-3">
                                        <input type="hidden" name="required_contact_fields[phone]" value="0">
                                        <input type="checkbox" name="required_contact_fields[phone]" value="1" 
                                               class="checkbox checkbox-sm" {{ ($contactFields['phone'] ?? false) ? 'checked' : '' }}>
                                        <span class="label-text">Phone Number</span>
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-3">
                                        <input type="hidden" name="required_contact_fields[business_name]" value="0">
                                        <input type="checkbox" name="required_contact_fields[business_name]" value="1" 
                                               class="checkbox checkbox-sm" {{ ($contactFields['business_name'] ?? true) ? 'checked' : '' }}>
                                        <span class="label-text">Business Name</span>
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-3">
                                        <input type="hidden" name="required_contact_fields[address]" value="0">
                                        <input type="checkbox" name="required_contact_fields[address]" value="1" 
                                               class="checkbox checkbox-sm" {{ ($contactFields['address'] ?? false) ? 'checked' : '' }}>
                                        <span class="label-text">Address</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Approval Settings -->
                <div id="approval-settings" class="card bg-base-100 shadow-md" 
                     style="display: {{ old('requires_approval', $form->requires_approval) ? 'block' : 'none' }}">
                    <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-check-circle text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Approval Settings</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="form-control">
                            <label class="label cursor-pointer justify-start gap-3">
                                <input type="hidden" name="applies_discount_on_approval" value="0">
                                <input type="checkbox" name="applies_discount_on_approval" value="1" 
                                       class="checkbox checkbox-secondary" 
                                       {{ old('applies_discount_on_approval', $form->applies_discount_on_approval) ? 'checked' : '' }}
                                       onchange="toggleDiscountSettings(this.checked)">
                                <div>
                                    <span class="label-text font-medium">Apply Discount on Approval</span>
                                    <p class="text-xs text-base-content/60">Automatically apply discount when submission is approved</p>
                                </div>
                            </label>
                        </div>

                        <div class="form-control">
                            <label class="label cursor-pointer justify-start gap-3">
                                <input type="hidden" name="generate_pdf_on_approval" value="0">
                                <input type="checkbox" name="generate_pdf_on_approval" value="1" 
                                       class="checkbox checkbox-secondary" 
                                       {{ old('generate_pdf_on_approval', $form->generate_pdf_on_approval) ? 'checked' : '' }}>
                                <div>
                                    <span class="label-text font-medium">Generate PDF on Approval</span>
                                    <p class="text-xs text-base-content/60">Automatically generate and save PDF to customer's documents when approved</p>
                                </div>
                            </label>
                        </div>

                        <div class="form-control">
                            <label class="label cursor-pointer justify-start gap-3">
                                <input type="hidden" name="save_html_content" value="0">
                                <input type="checkbox" name="save_html_content" value="1" 
                                       class="checkbox checkbox-warning" 
                                       {{ old('save_html_content', $form->save_html_content) ? 'checked' : '' }}>
                                <div>
                                    <span class="label-text font-medium">Save HTML Content with Submissions</span>
                                    <p class="text-xs text-base-content/60">Save processed HTML fields (with template variables replaced) in submission data for contracts and agreements</p>
                                </div>
                            </label>
                        </div>

                        <div id="discount-settings" style="display: {{ old('applies_discount_on_approval', $form->applies_discount_on_approval) ? 'block' : 'none' }}">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="label">
                                        <span class="label-text font-medium">Associated Discount</span>
                                    </label>
                                    <select name="discount_id" class="select select-bordered w-full @error('discount_id') select-error @enderror">
                                        <option value="">No discount</option>
                                        @foreach($discounts as $discount)
                                            <option value="{{ $discount->id }}" 
                                                    {{ old('discount_id', $form->discount_id) == $discount->id ? 'selected' : '' }}>
                                                {{ $discount->name }} ({{ $discount->type === 'percentage' ? $discount->value . '%' : '$' . number_format($discount->value, 2) }})
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="label">
                                        <span class="label-text-alt text-base-content/60">Discount automatically applied when submission is approved</span>
                                    </div>
                                    @error('discount_id')
                                        <div class="label">
                                            <span class="label-text-alt text-error">{{ $message }}</span>
                                        </div>
                                    @enderror
                                </div>
                                <div>
                                    <label class="label">
                                        <span class="label-text font-medium">Discount Duration (Months)</span>
                                    </label>
                                    <input type="number" name="agreement_duration_months" min="1" max="120"
                                           value="{{ old('agreement_duration_months', $form->agreement_duration_months) }}"
                                           class="input input-bordered w-full @error('agreement_duration_months') input-error @enderror"
                                           placeholder="Leave blank to use global setting">
                                    <div class="label">
                                        <span class="label-text-alt text-base-content/60">How long the discount remains active</span>
                                    </div>
                                    @error('agreement_duration_months')
                                        <div class="label">
                                            <span class="label-text-alt text-error">{{ $message }}</span>
                                        </div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Email Notification Settings -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-info text-info-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-envelope text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Email Notification Settings</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="form-control">
                            <label class="label cursor-pointer justify-start gap-3">
                                <input type="hidden" name="enable_email_notifications" value="0">
                                <input type="checkbox" name="enable_email_notifications" value="1"
                                       class="checkbox checkbox-info"
                                       {{ old('enable_email_notifications', $form->enable_email_notifications) ? 'checked' : '' }}
                                       onchange="toggleEmailSettings(this.checked)">
                                <div>
                                    <span class="label-text font-medium">Enable Email Notifications</span>
                                    <p class="text-xs text-base-content/60">Send notifications when form is submitted</p>
                                </div>
                            </label>
                        </div>

                        <div id="email-notification-settings"
                             style="display: {{ old('enable_email_notifications', $form->enable_email_notifications) ? 'block' : 'none' }}">
                            <div class="space-y-4">
                                <div>
                                    <label class="label">
                                        <span class="label-text font-medium">Notification Recipients</span>
                                    </label>
                                    <div id="email-inputs-container" class="space-y-3">
                                        @php
                                            $emails = old('notification_emails', $form->notification_emails ?? []);
                                            if (empty($emails)) {
                                                $emails = [''];
                                            }
                                        @endphp
                                        @foreach($emails as $index => $email)
                                            <div class="flex gap-2 email-input-row">
                                                <input type="email" name="notification_emails[]" class="input input-bordered flex-1"
                                                       value="{{ $email }}" placeholder="<EMAIL>">
                                                @if($index > 0 || count($emails) > 1)
                                                    <button type="button" class="btn btn-outline btn-error btn-sm" onclick="removeEmailInput(this)">
                                                        <i class="fa-sharp fa-trash"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-outline btn-info btn-sm gap-2" onclick="addEmailInput()">
                                            <i class="fa-sharp fa-plus"></i>
                                            Add Another Email
                                        </button>
                                    </div>
                                    @error('notification_emails')
                                    <div class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </div>
                                    @enderror
                                    @error('notification_emails.*')
                                    <div class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </div>
                                    @enderror
                                </div>

                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-3">
                                        <input type="hidden" name="include_full_data_in_email" value="0">
                                        <input type="checkbox" name="include_full_data_in_email" value="1"
                                               class="checkbox checkbox-info"
                                               {{ old('include_full_data_in_email', $form->include_full_data_in_email) ? 'checked' : '' }}>
                                        <div>
                                            <span class="label-text font-medium">Include Full Data in Email</span>
                                            <p class="text-xs text-base-content/60">Include all submitted form data (otherwise just link)</p>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Group Notifications -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-accent text-accent-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-users text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">User Group Notifications</h4>
                        </div>
                    </div>
                    <div class="p-6">
                        @php
                            $formSubmissionConfig = $form->notificationConfigs->where('event_type', 'form_submitted')->first();
                        @endphp

                        <x-notification-config
                            event-type="form_submitted"
                            event-label="Form Submission"
                            :config="$formSubmissionConfig"
                            :user-groups="$userGroups"
                            :users="$users"
                            field-prefix="notification_config"
                            :show-advanced="true"
                        />

                        <div class="alert alert-info mt-4">
                            <i class="fa-sharp fa-info-circle"></i>
                            <div>
                                <h3 class="font-bold">User Group Notification Behavior</h3>
                                <div class="text-sm">When enabled, in-app notifications will be sent to selected user groups immediately when someone submits this form. Users will see these notifications in their notification panel and can receive real-time alerts.</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Thank You Page Settings -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-check-circle text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Thank You Page Settings</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="alert alert-info">
                            <i class="fa-sharp fa-info-circle"></i>
                            <div>
                                <p class="text-sm">Customize what appears on the thank you page after form submission. You can show submission details, custom messages, and enable additional features.</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="thankyou_show_submission_details" value="0">
                                    <input type="checkbox" name="thankyou_show_submission_details" value="1" 
                                           class="checkbox checkbox-success" 
                                           {{ old('thankyou_show_submission_details', $form->thankyou_show_submission_details) ? 'checked' : '' }}>
                                    <div>
                                        <span class="label-text font-medium">Show Submission Details</span>
                                        <p class="text-xs text-base-content/60">Display submitted form data to the user</p>
                                    </div>
                                </label>
                            </div>

                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="thankyou_show_html_message" value="0">
                                    <input type="checkbox" name="thankyou_show_html_message" value="1" 
                                           class="checkbox checkbox-primary" 
                                           {{ old('thankyou_show_html_message', $form->thankyou_show_html_message) ? 'checked' : '' }}
                                           onchange="toggleHtmlMessageEditor(this.checked)">
                                    <div>
                                        <span class="label-text font-medium">Show Custom HTML Message</span>
                                        <p class="text-xs text-base-content/60">Replace "What Happens Next" with custom content</p>
                                    </div>
                                </label>
                            </div>

                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="thankyou_show_need_help" value="0">
                                    <input type="checkbox" name="thankyou_show_need_help" value="1" 
                                           class="checkbox checkbox-info" 
                                           {{ old('thankyou_show_need_help', $form->thankyou_show_need_help) ? 'checked' : '' }}>
                                    <div>
                                        <span class="label-text font-medium">Show "Need Help?" Section</span>
                                        <p class="text-xs text-base-content/60">Display contact information section</p>
                                    </div>
                                </label>
                            </div>

                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="thankyou_enable_print" value="0">
                                    <input type="checkbox" name="thankyou_enable_print" value="1" 
                                           class="checkbox checkbox-secondary" 
                                           {{ old('thankyou_enable_print', $form->thankyou_enable_print) ? 'checked' : '' }}>
                                    <div>
                                        <span class="label-text font-medium">Enable Print Confirmation</span>
                                        <p class="text-xs text-base-content/60">Add print button to thank you page</p>
                                    </div>
                                </label>
                            </div>

                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="thankyou_enable_close" value="0">
                                    <input type="checkbox" name="thankyou_enable_close" value="1" 
                                           class="checkbox checkbox-warning" 
                                           {{ old('thankyou_enable_close', $form->thankyou_enable_close) ? 'checked' : '' }}>
                                    <div>
                                        <span class="label-text font-medium">Enable Close Page Feature</span>
                                        <p class="text-xs text-base-content/60">Add close button to close window/tab</p>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- HTML Message Editor -->
                        <div id="htmlMessageEditor" class="mt-6" style="display: {{ old('thankyou_show_html_message', $form->thankyou_show_html_message) ? 'block' : 'none' }}">
                            <label class="label">
                                <span class="label-text font-medium">Custom HTML Message</span>
                            </label>
                            <textarea name="thankyou_html_message" id="thankyouHtmlMessage" class="textarea textarea-bordered w-full h-64">{{ old('thankyou_html_message', $form->thankyou_html_message) }}</textarea>
                            <div class="label">
                                <span class="label-text-alt text-base-content/60">You can use HTML formatting and template variables like {business_name}, {full_name}, etc.</span>
                            </div>
                            @error('thankyou_html_message')
                                <div class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Save Actions -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-save text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Save Changes</h4>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-300 text-base-content w-10 rounded-lg">
                                        <i class="fa-sharp fa-info-circle text-sm"></i>
                                    </div>
                                </div>
                                <div class="text-center sm:text-left">
                                    <p class="font-medium text-base-content">Ready to save your changes?</p>
                                    <p class="text-sm text-base-content/60">Form settings will be updated immediately</p>
                                </div>
                            </div>
                            <div class="flex gap-3 w-full sm:w-auto">
                                <a href="{{ route('forms.show', $form) }}" class="btn btn-outline btn-lg gap-2 flex-1 sm:flex-none">
                                    <i class="fa-sharp fa-times"></i>
                                    Cancel
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                                    <i class="fa-sharp fa-save"></i>
                                    Update Form
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

            </form>

        </div>
    </div>

    <!-- Include Jodit for rich text editing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jodit/4.2.47/es2015/jodit.fat.min.js" integrity="sha512-lO8LIfs1jytwt9MbX5/EBq5z6fN/2Hmiyrb+TdkOTKrhUQp/mmkrp1+Wyw9Tj8kokW6zU8KlH60MPZ1Xmt4JAQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jodit/4.2.47/es2015/jodit.fat.min.css" integrity="sha512-2cfnJ8ZMBqkaNXsi/6ucIcFRvKVFKW69HEP5S7L2fQtAaPrVg5XLkkUgl46kkaN5PPArXwLPCOqhbsAAiHQiXA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <script>
        function toggleCustomerLinkSettings(show) {
            const settingsSection = document.getElementById('customer-link-settings');
            settingsSection.style.display = show ? 'block' : 'none';
        }

        function toggleApprovalSettings(show) {
            const settingsSection = document.getElementById('approval-settings');
            settingsSection.style.display = show ? 'block' : 'none';
        }

        function toggleDiscountSettings(show) {
            const settingsSection = document.getElementById('discount-settings');
            settingsSection.style.display = show ? 'block' : 'none';
        }

        function toggleEmailSettings(show) {
            const settingsSection = document.getElementById('email-notification-settings');
            settingsSection.style.display = show ? 'block' : 'none';
        }

        function addEmailInput() {
            const container = document.getElementById('email-inputs-container');
            const newRow = document.createElement('div');
            newRow.className = 'flex gap-2 email-input-row';
            newRow.innerHTML = `
                <input type="email" name="notification_emails[]" class="input input-bordered flex-1" placeholder="<EMAIL>">
                <button type="button" class="btn btn-outline btn-error btn-sm" onclick="removeEmailInput(this)">
                    <i class="fa-sharp fa-trash"></i>
                </button>
            `;
            container.appendChild(newRow);
        }

        function removeEmailInput(button) {
            const row = button.closest('.email-input-row');
            row.remove();
        }

        function initializeJoditEditor() {
            console.log('🔧 initializeJoditEditor() called');
            
            const textarea = document.getElementById('thankyouHtmlMessage');
            console.log('📝 Textarea element:', textarea);
            console.log('🌐 Jodit available:', typeof Jodit !== 'undefined');
            console.log('⚡ Editor already exists:', !!window.thankyouHtmlEditor);
            
            if (!textarea) {
                console.error('❌ Textarea not found!');
                return;
            }
            
            if (typeof Jodit === 'undefined') {
                console.error('❌ Jodit not loaded!');
                return;
            }
            
            if (window.thankyouHtmlEditor) {
                console.log('⚠️ Editor already exists, skipping initialization');
                return;
            }
            
            const savedContent = textarea.value;
            console.log('💾 Saved content length:', savedContent.length);
            console.log('💾 Saved content preview:', savedContent.substring(0, 100));
            
            try {
                console.log('🚀 Creating Jodit editor...');
                window.thankyouHtmlEditor = Jodit.make(textarea, {
                    height: 300,
                    toolbarSticky: false,
                    buttons: [
                        'bold', 'italic', 'underline', '|',
                        'ul', 'ol', '|',
                        'link', 'image', '|',
                        'align', '|',
                        'undo', 'redo', '|',
                        'hr', 'table', '|',
                        'fullsize', 'source'
                    ],
                    allowPasteHTML: true,
                    allowPasteFromWord: true,
                    processPasteHTML: true,
                    processPasteFromWord: true,
                    askBeforePasteHTML: false,
                    askBeforePasteFromWord: false,
                    defaultActionOnPaste: 'insert_as_html',
                    iframe: false,
                    readonly: false,
                    disabled: false,
                    cleanHTML: {
                        allowTags: 'p,br,strong,em,u,ol,ul,li,a,table,thead,tbody,tr,td,th,h1,h2,h3,h4,h5,h6,blockquote,hr,img,div,span',
                        allowAttributes: 'href,src,alt,title,class,style'
                    },
                    events: {
                        afterInit: function (editor) {
                            console.log('✅ Jodit editor initialized successfully!');
                            // Add styling classes to editor container
                            editor.container.classList.add('html-content-styled');
                            // Set the saved content after editor is fully initialized
                            if (savedContent) {
                                console.log('📥 Setting saved content...');
                                editor.value = savedContent;
                                console.log('✅ Content set successfully');
                            }
                        }
                    }
                });
                console.log('🎉 Jodit editor created successfully!');
            } catch (error) {
                console.error('❌ Error creating Jodit editor:', error);
            }
        }

        function toggleHtmlMessageEditor(show) {
            const editorSection = document.getElementById('htmlMessageEditor');
            editorSection.style.display = show ? 'block' : 'none';
            
            if (show && !window.thankyouHtmlEditor) {
                // Wait for DOM to be ready and Jodit to be loaded
                setTimeout(initializeJoditEditor, 200);
            } else if (!show && window.thankyouHtmlEditor) {
                // Destroy editor when hiding
                window.thankyouHtmlEditor.destruct();
                window.thankyouHtmlEditor = null;
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM Content Loaded');
            console.log('🌐 Jodit available on DOM load:', typeof Jodit !== 'undefined');
            console.log('🔍 Window object keys containing "Jodit":', Object.keys(window).filter(k => k.includes('Jodit')));
            
            // Wait a bit more for external scripts to load
            setTimeout(() => {
                console.log('⏰ After 100ms delay:');
                console.log('🌐 Jodit available after delay:', typeof Jodit !== 'undefined');
            }, 100);
            const customerLinkCheckbox = document.querySelector('input[name="requires_customer_link"]');
            if (customerLinkCheckbox && customerLinkCheckbox.checked) {
                toggleCustomerLinkSettings(true);
            }

            const approvalCheckbox = document.querySelector('input[name="requires_approval"]');
            if (approvalCheckbox && approvalCheckbox.checked) {
                toggleApprovalSettings(true);
            }

            const discountCheckbox = document.querySelector('input[name="applies_discount_on_approval"]');
            if (discountCheckbox && discountCheckbox.checked) {
                toggleDiscountSettings(true);
            }

            const emailNotificationCheckbox = document.querySelector('input[name="enable_email_notifications"]');
            if (emailNotificationCheckbox && emailNotificationCheckbox.checked) {
                toggleEmailSettings(true);
            }

            const htmlMessageCheckbox = document.querySelector('input[name="thankyou_show_html_message"][type="checkbox"]');
            console.log('🔍 HTML message checkbox:', htmlMessageCheckbox);
            console.log('🔍 Checkbox checked:', htmlMessageCheckbox ? htmlMessageCheckbox.checked : 'N/A');
            
            if (htmlMessageCheckbox && htmlMessageCheckbox.checked) {
                console.log('✅ Checkbox is checked, initializing editor on page load...');
                
                // Initialize the editor immediately if checkbox is already checked
                const editorSection = document.getElementById('htmlMessageEditor');
                console.log('🔍 Editor section:', editorSection);
                console.log('🔍 Editor section display before:', editorSection ? editorSection.style.display : 'N/A');
                
                if (editorSection) {
                    editorSection.style.display = 'block';
                    console.log('🔍 Editor section display after:', editorSection.style.display);
                }
                
                // Wait longer for Jodit to be fully loaded on page load
                console.log('⏰ Setting timeout to initialize Jodit in 500ms...');
                setTimeout(() => {
                    console.log('⏰ Timeout triggered, calling initializeJoditEditor...');
                    initializeJoditEditor();
                }, 500);
            } else {
                console.log('❌ Checkbox not checked or not found, skipping editor initialization');
            }

        });
    </script>
</x-app-layout>