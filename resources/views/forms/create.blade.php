<x-app-layout
    page-title="Create Custom Form"
    page-icon="fa-sharp fa-plus-circle"
    :breadcrumbs="[
        ['name' => 'Custom Forms', 'route' => 'forms.index'],
        ['name' => 'Create Form']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Introduction Card -->
            <div class="bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
                <!-- Decorative Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 left-0 w-32 h-32 bg-primary rounded-full -translate-x-16 -translate-y-16"></div>
                    <div class="absolute bottom-0 right-0 w-24 h-24 bg-secondary rounded-full translate-x-12 translate-y-12"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-start gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-gradient-to-br from-primary to-primary-focus text-primary-content w-12 rounded-xl shadow-lg">
                                <i class="fa-sharp fa-plus-circle text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-2xl font-bold text-base-content mb-2">Create New Custom Form</h3>
                            <p class="text-base-content/70 text-base leading-relaxed">Design a custom form to collect information from customers. Forms can be used for service agreements, surveys, applications, and more. After creating the form, you'll be able to add fields and customize the layout.</p>
                        </div>
                    </div>
                </div>
            </div>

            <form method="POST" action="{{ route('forms.store') }}" class="space-y-6">
                @csrf

                <!-- Basic Information -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-info-circle text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Basic Information</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div>
                            <label class="label">
                                <span class="label-text font-medium">Form Name <span class="text-error">*</span></span>
                            </label>
                            <input type="text" name="name" value="{{ old('name') }}" 
                                   class="input input-bordered w-full @error('name') input-error @enderror" 
                                   placeholder="e.g., Service Agreement Form" required>
                            @error('name')
                                <div class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </div>
                            @enderror
                        </div>

                        <div>
                            <label class="label">
                                <span class="label-text font-medium">Description</span>
                            </label>
                            <textarea name="description" rows="3" 
                                      class="textarea textarea-bordered w-full @error('description') textarea-error @enderror"
                                      placeholder="Brief description of this form's purpose...">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Form Behavior -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-cog text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Form Behavior</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="is_active" value="0">
                                    <input type="checkbox" name="is_active" value="1" 
                                           class="checkbox checkbox-primary" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <div>
                                        <span class="label-text font-medium">Active Form</span>
                                        <p class="text-xs text-base-content/60">Allow public submissions</p>
                                    </div>
                                </label>
                            </div>
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="requires_signature" value="0">
                                    <input type="checkbox" name="requires_signature" value="1" 
                                           class="checkbox checkbox-warning" {{ old('requires_signature') ? 'checked' : '' }}>
                                    <div>
                                        <span class="label-text font-medium">Requires Signature</span>
                                        <p class="text-xs text-base-content/60">Digital signature required</p>
                                    </div>
                                </label>
                            </div>
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="requires_customer_link" value="0">
                                    <input type="checkbox" name="requires_customer_link" value="1" 
                                           class="checkbox checkbox-accent" 
                                           {{ old('requires_customer_link') ? 'checked' : '' }}
                                           onchange="toggleCustomerLinkSettings(this.checked)">
                                    <div>
                                        <span class="label-text font-medium">Requires Customer Link</span>
                                        <p class="text-xs text-base-content/60">Link to customer accounts</p>
                                    </div>
                                </label>
                            </div>
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input type="hidden" name="requires_approval" value="0">
                                    <input type="checkbox" name="requires_approval" value="1" 
                                           class="checkbox checkbox-secondary" 
                                           {{ old('requires_approval') ? 'checked' : '' }}
                                           onchange="toggleApprovalSettings(this.checked)">
                                    <div>
                                        <span class="label-text font-medium">Requires Approval</span>
                                        <p class="text-xs text-base-content/60">Admin approval needed</p>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Link Settings -->
                <div id="customer-link-settings" class="card bg-base-100 shadow-md" 
                     style="display: {{ old('requires_customer_link') ? 'block' : 'none' }}">
                    <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-accent text-accent-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-user-tie text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Customer Link Settings</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div>
                            <h5 class="font-medium text-base-content mb-3">Required Contact Fields</h5>
                            <p class="text-sm text-base-content/60 mb-4">Select which contact fields are required for form submission</p>
                            <div class="alert alert-info mb-4">
                                <i class="fa-sharp fa-info-circle"></i>
                                <div>
                                    <p class="text-sm"><strong>Simplified Contact System:</strong> Forms will show "Full Name" and "Business Name (Optional)" fields.</p>
                                    <p class="text-sm">If business name is filled, customer becomes business type with full name as contact person.</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-3">
                                        <input type="hidden" name="required_contact_fields[email]" value="0">
                                        <input type="checkbox" name="required_contact_fields[email]" value="1" 
                                               class="checkbox checkbox-sm" {{ old('required_contact_fields.email', true) ? 'checked' : '' }}>
                                        <span class="label-text">Email Address</span>
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-3">
                                        <input type="hidden" name="required_contact_fields[phone]" value="0">
                                        <input type="checkbox" name="required_contact_fields[phone]" value="1" 
                                               class="checkbox checkbox-sm" {{ old('required_contact_fields.phone') ? 'checked' : '' }}>
                                        <span class="label-text">Phone Number</span>
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-3">
                                        <input type="hidden" name="required_contact_fields[address]" value="0">
                                        <input type="checkbox" name="required_contact_fields[address]" value="1" 
                                               class="checkbox checkbox-sm" {{ old('required_contact_fields.address') ? 'checked' : '' }}>
                                        <span class="label-text">Address</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Approval Settings -->
                <div id="approval-settings" class="card bg-base-100 shadow-md" 
                     style="display: {{ old('requires_approval') ? 'block' : 'none' }}">
                    <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-check-circle text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Approval Settings</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="form-control">
                            <label class="label cursor-pointer justify-start gap-3">
                                <input type="hidden" name="applies_discount_on_approval" value="0">
                                <input type="checkbox" name="applies_discount_on_approval" value="1" 
                                       class="checkbox checkbox-secondary" 
                                       {{ old('applies_discount_on_approval') ? 'checked' : '' }}
                                       onchange="toggleDiscountSettings(this.checked)">
                                <div>
                                    <span class="label-text font-medium">Apply Discount on Approval</span>
                                    <p class="text-xs text-base-content/60">Automatically apply discount when submission is approved</p>
                                </div>
                            </label>
                        </div>

                        <div class="form-control">
                            <label class="label cursor-pointer justify-start gap-3">
                                <input type="hidden" name="generate_pdf_on_approval" value="0">
                                <input type="checkbox" name="generate_pdf_on_approval" value="1" 
                                       class="checkbox checkbox-secondary" 
                                       {{ old('generate_pdf_on_approval') ? 'checked' : '' }}>
                                <div>
                                    <span class="label-text font-medium">Generate PDF on Approval</span>
                                    <p class="text-xs text-base-content/60">Automatically generate and save PDF to customer's documents when approved</p>
                                </div>
                            </label>
                        </div>

                        <div id="discount-settings" style="display: {{ old('applies_discount_on_approval') ? 'block' : 'none' }}">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="label">
                                        <span class="label-text font-medium">Associated Discount</span>
                                    </label>
                                    <select name="discount_id" class="select select-bordered w-full @error('discount_id') select-error @enderror">
                                        <option value="">No discount</option>
                                        @foreach($discounts as $discount)
                                            <option value="{{ $discount->id }}" 
                                                    {{ old('discount_id') == $discount->id ? 'selected' : '' }}>
                                                {{ $discount->name }} ({{ $discount->type === 'percentage' ? $discount->value . '%' : '$' . number_format($discount->value, 2) }})
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="label">
                                        <span class="label-text-alt text-base-content/60">Discount automatically applied when submission is approved</span>
                                    </div>
                                    @error('discount_id')
                                        <div class="label">
                                            <span class="label-text-alt text-error">{{ $message }}</span>
                                        </div>
                                    @enderror
                                </div>
                                <div>
                                    <label class="label">
                                        <span class="label-text font-medium">Discount Duration (Months)</span>
                                    </label>
                                    <input type="number" name="agreement_duration_months" min="1" max="120"
                                           value="{{ old('agreement_duration_months', 12) }}"
                                           class="input input-bordered w-full @error('agreement_duration_months') input-error @enderror"
                                           placeholder="Leave blank to use global setting">
                                    <div class="label">
                                        <span class="label-text-alt text-base-content/60">How long the discount remains active</span>
                                    </div>
                                    @error('agreement_duration_months')
                                        <div class="label">
                                            <span class="label-text-alt text-error">{{ $message }}</span>
                                        </div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Email Notification Settings -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-info text-info-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-envelope text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Email Notification Settings</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="form-control">
                            <label class="label cursor-pointer justify-start gap-3">
                                <input type="hidden" name="enable_email_notifications" value="0">
                                <input type="checkbox" name="enable_email_notifications" value="1"
                                       class="checkbox checkbox-info"
                                       {{ old('enable_email_notifications') ? 'checked' : '' }}
                                       onchange="toggleEmailSettings(this.checked)">
                                <div>
                                    <span class="label-text font-medium">Enable Email Notifications</span>
                                    <p class="text-xs text-base-content/60">Send notifications when form is submitted</p>
                                </div>
                            </label>
                        </div>

                        <div id="email-notification-settings"
                             style="display: {{ old('enable_email_notifications') ? 'block' : 'none' }}">
                            <div class="space-y-4">
                                <div>
                                    <label class="label">
                                        <span class="label-text font-medium">Notification Recipients</span>
                                    </label>
                                    <div id="email-inputs-container" class="space-y-3">
                                        @php
                                            $emails = old('notification_emails', ['']);
                                            if (empty($emails) || (count($emails) === 1 && empty($emails[0]))) {
                                                $emails = [''];
                                            }
                                        @endphp
                                        @foreach($emails as $index => $email)
                                            <div class="flex gap-2 email-input-row">
                                                <input type="email" name="notification_emails[]" class="input input-bordered flex-1"
                                                       value="{{ $email }}" placeholder="<EMAIL>">
                                                @if($index > 0 || count($emails) > 1)
                                                    <button type="button" class="btn btn-outline btn-error btn-sm" onclick="removeEmailInput(this)">
                                                        <i class="fa-sharp fa-trash"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-outline btn-info btn-sm gap-2" onclick="addEmailInput()">
                                            <i class="fa-sharp fa-plus"></i>
                                            Add Another Email
                                        </button>
                                    </div>
                                    @error('notification_emails')
                                    <div class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </div>
                                    @enderror
                                    @error('notification_emails.*')
                                    <div class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </div>
                                    @enderror
                                </div>

                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-3">
                                        <input type="hidden" name="include_full_data_in_email" value="0">
                                        <input type="checkbox" name="include_full_data_in_email" value="1"
                                               class="checkbox checkbox-info"
                                               {{ old('include_full_data_in_email') ? 'checked' : '' }}>
                                        <div>
                                            <span class="label-text font-medium">Include Full Data in Email</span>
                                            <p class="text-xs text-base-content/60">Include all submitted form data (otherwise just link)</p>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Group Notifications -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-accent text-accent-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-users text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">User Group Notifications</h4>
                        </div>
                    </div>
                    <div class="p-6">
                        <x-notification-config
                            event-type="form_submitted"
                            event-label="Form Submission"
                            :user-groups="$userGroups"
                            :users="$users"
                            field-prefix="notification_config"
                            :show-advanced="true"
                        />

                        <div class="alert alert-info mt-4">
                            <i class="fa-sharp fa-info-circle"></i>
                            <div>
                                <h3 class="font-bold">User Group Notification Behavior</h3>
                                <div class="text-sm">When enabled, in-app notifications will be sent to selected user groups immediately when someone submits this form. Users will see these notifications in their notification panel and can receive real-time alerts.</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Actions -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-plus text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Create Form</h4>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-base-300 text-base-content w-10 rounded-lg">
                                        <i class="fa-sharp fa-info-circle text-sm"></i>
                                    </div>
                                </div>
                                <div class="text-center sm:text-left">
                                    <p class="font-medium text-base-content">Ready to create your form?</p>
                                    <p class="text-sm text-base-content/60">You'll be able to add fields after creation</p>
                                </div>
                            </div>
                            <div class="flex gap-3 w-full sm:w-auto">
                                <a href="{{ route('forms.index') }}" class="btn btn-outline btn-lg gap-2 flex-1 sm:flex-none">
                                    <i class="fa-sharp fa-times"></i>
                                    Cancel
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                                    <i class="fa-sharp fa-plus"></i>
                                    Create Form
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

            </form>

        </div>
    </div>

    <script>
        function toggleCustomerLinkSettings(show) {
            const settingsSection = document.getElementById('customer-link-settings');
            settingsSection.style.display = show ? 'block' : 'none';
        }

        function toggleApprovalSettings(show) {
            const settingsSection = document.getElementById('approval-settings');
            settingsSection.style.display = show ? 'block' : 'none';
        }

        function toggleDiscountSettings(show) {
            const settingsSection = document.getElementById('discount-settings');
            settingsSection.style.display = show ? 'block' : 'none';
        }

        function toggleEmailSettings(show) {
            const settingsSection = document.getElementById('email-notification-settings');
            settingsSection.style.display = show ? 'block' : 'none';
        }

        function addEmailInput() {
            const container = document.getElementById('email-inputs-container');
            const newRow = document.createElement('div');
            newRow.className = 'flex gap-2 email-input-row';
            newRow.innerHTML = `
                <input type="email" name="notification_emails[]" class="input input-bordered flex-1" placeholder="<EMAIL>">
                <button type="button" class="btn btn-outline btn-error btn-sm" onclick="removeEmailInput(this)">
                    <i class="fa-sharp fa-trash"></i>
                </button>
            `;
            container.appendChild(newRow);
        }

        function removeEmailInput(button) {
            const row = button.closest('.email-input-row');
            row.remove();
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            const customerLinkCheckbox = document.querySelector('input[name="requires_customer_link"]');
            if (customerLinkCheckbox && customerLinkCheckbox.checked) {
                toggleCustomerLinkSettings(true);
            }

            const approvalCheckbox = document.querySelector('input[name="requires_approval"]');
            if (approvalCheckbox && approvalCheckbox.checked) {
                toggleApprovalSettings(true);
            }

            const discountCheckbox = document.querySelector('input[name="applies_discount_on_approval"]');
            if (discountCheckbox && discountCheckbox.checked) {
                toggleDiscountSettings(true);
            }

            const emailNotificationCheckbox = document.querySelector('input[name="enable_email_notifications"]');
            if (emailNotificationCheckbox && emailNotificationCheckbox.checked) {
                toggleEmailSettings(true);
            }

            // Name and Email fields should always be checked and disabled for customer link forms
            const nameField = document.querySelector('input[name="required_contact_fields[name]"][type="checkbox"]');
            const emailField = document.querySelector('input[name="required_contact_fields[email]"][type="checkbox"]');
            if (nameField) {
                nameField.checked = true;
                nameField.disabled = true;
            }
            if (emailField) {
                emailField.checked = true;
                emailField.disabled = true;
            }
        });
    </script>
</x-app-layout>