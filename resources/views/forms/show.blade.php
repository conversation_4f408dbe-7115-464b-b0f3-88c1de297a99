<x-app-layout
    page-title="{{ $form->name }}"
    page-icon="fa-sharp fa-file-lines"
    :primary-buttons="[
        ['type' => 'edit', 'route' => route('forms.edit', $form), 'text' => 'Edit Form', 'permission' => 'manage_forms'],
        ['type' => 'custom', 'route' => route('forms.builder', $form), 'text' => 'Form Builder', 'permission' => 'manage_forms', 'class' => 'btn btn-secondary btn-sm gap-2']
    ]"
    :action-buttons="[
        ['name' => 'Preview Form', 'route' => route('forms.preview', $form), 'icon' => 'fa-sharp fa-eye', 'class' => 'btn btn-info btn-sm gap-2'],
        ['name' => 'View Public URL', 'route' => $form->getPublicUrl(), 'icon' => 'fa-sharp fa-external-link', 'class' => 'btn btn-accent btn-sm gap-2', 'target' => '_blank']
    ]"
    :breadcrumbs="[
        ['name' => 'Custom Forms', 'route' => 'forms.index'],
        ['name' => $form->name]
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
                
                <!-- Main Content -->
                <div class="xl:col-span-2 space-y-6">
                    
                    <!-- Form Details -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-primary text-primary-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-info-circle text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Form Information</h4>
                            </div>
                        </div>
                        <div class="p-6 space-y-4">
                            @if($form->description)
                                <div>
                                    <label class="text-sm font-medium text-base-content/80">Description</label>
                                    <p class="text-base-content">{{ $form->description }}</p>
                                </div>
                            @endif
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="text-sm font-medium text-base-content/80">Status</label>
                                    <span class="badge {{ $form->is_active ? 'badge-success' : 'badge-error' }}">
                                        {{ $form->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-base-content/80">Total Fields</label>
                                    <p class="text-base-content">{{ $form->fields->count() }}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-base-content/80">Signature Required</label>
                                    <span class="badge {{ $form->requires_signature ? 'badge-warning' : 'badge-ghost' }}">
                                        {{ $form->requires_signature ? 'Yes' : 'No' }}
                                    </span>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-base-content/80">Customer Link Required</label>
                                    <span class="badge {{ $form->requires_customer_link ? 'badge-accent' : 'badge-ghost' }}">
                                        {{ $form->requires_customer_link ? 'Yes' : 'No' }}
                                    </span>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-base-content/80">Approval Required</label>
                                    <span class="badge {{ $form->requires_approval ? 'badge-secondary' : 'badge-ghost' }}">
                                        {{ $form->requires_approval ? 'Yes' : 'No' }}
                                    </span>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-base-content/80">Apply Discount on Approval</label>
                                    <span class="badge {{ $form->applies_discount_on_approval ? 'badge-info' : 'badge-ghost' }}">
                                        {{ $form->applies_discount_on_approval ? 'Yes' : 'No' }}
                                    </span>
                                </div>
                            </div>

                            @if($form->requires_customer_link && $form->required_contact_fields)
                                <div class="divider">Required Contact Fields</div>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($form->getRequiredContactFields() as $field => $required)
                                        @if($required)
                                            <span class="badge badge-primary badge-sm">
                                                {{ ucfirst(str_replace('_', ' ', $field)) }}
                                            </span>
                                        @endif
                                    @endforeach
                                </div>
                            @endif

                            @if($form->applies_discount_on_approval && $form->discount)
                                <div class="divider">Discount Settings</div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="text-sm font-medium text-base-content/80">Associated Discount</label>
                                        <p class="text-base-content">{{ $form->discount->name }}</p>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium text-base-content/80">Discount Duration</label>
                                        <p class="text-base-content">{{ $form->getDiscountDurationMonths() }} months</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Form Fields -->
                    @if($form->fields->count() > 0)
                        <div class="card bg-base-100 shadow-md">
                            <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                                <div class="flex items-center gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-secondary text-secondary-content w-8 rounded-lg">
                                            <i class="fa-sharp fa-list text-sm"></i>
                                        </div>
                                    </div>
                                    <h4 class="text-lg font-semibold text-base-content">Form Fields</h4>
                                </div>
                            </div>
                            <div class="p-6 space-y-4">
                                @foreach($form->fields->sortBy('order') as $field)
                                    <div class="border border-base-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-2">
                                            <div class="flex items-center gap-2">
                                                <span class="badge badge-sm badge-outline">{{ \App\Models\FormField::TYPES[$field->type] ?? $field->type }}</span>
                                                @if($field->required)
                                                    <span class="badge badge-sm badge-error">Required</span>
                                                @endif
                                            </div>
                                            <span class="text-xs text-base-content/60">Order: {{ $field->order }}</span>
                                        </div>
                                        
                                        @if($field->type === 'html')
                                            <div class="prose prose-sm max-w-none">
                                                {!! $field->content !!}
                                            </div>
                                        @else
                                            @if($field->label)
                                                <h5 class="font-medium text-base-content mb-1">{{ $field->label }}</h5>
                                            @endif
                                            @if($field->name)
                                                <p class="text-sm text-base-content/60 font-mono">Field name: {{ $field->name }}</p>
                                            @endif
                                            @if($field->placeholder)
                                                <p class="text-sm text-base-content/60">Placeholder: {{ $field->placeholder }}</p>
                                            @endif
                                            @if($field->help_text)
                                                <p class="text-sm text-base-content/60">Help text: {{ $field->help_text }}</p>
                                            @endif
                                            @if($field->hasOptions() && $field->options)
                                                <div class="mt-2">
                                                    <span class="text-sm font-medium text-base-content/80">Options:</span>
                                                    <div class="flex flex-wrap gap-1 mt-1">
                                                        @foreach($field->getOptionsArray() as $option)
                                                            <span class="badge badge-sm badge-outline">{{ is_array($option) ? $option['label'] : $option }}</span>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            @endif
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <div class="card bg-base-100 shadow-md">
                            <div class="p-6 text-center">
                                <div class="avatar avatar-placeholder mb-4">
                                    <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                                        <i class="fa-sharp fa-plus text-2xl"></i>
                                    </div>
                                </div>
                                <h3 class="text-lg font-medium text-base-content/80 mb-2">No fields added yet</h3>
                                <p class="text-sm text-base-content/60 mb-4">Use the form builder to add fields to this form</p>
                                @perms('manage_forms')
                                    <a href="{{ route('forms.builder', $form) }}" class="btn btn-primary gap-2">
                                        <i class="fa-sharp fa-edit"></i>
                                        Open Form Builder
                                    </a>
                                @endperms
                            </div>
                        </div>
                    @endif

                </div>

                <!-- Sidebar -->
                <div class="xl:col-span-1 space-y-6">

                    <!-- Public Access -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-accent text-accent-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-globe text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Public Access</h4>
                            </div>
                        </div>
                        <div class="p-6 space-y-4">
                            <div>
                                <label class="text-sm font-medium text-base-content/80">Public URL</label>
                                <div class="flex gap-2 mt-1">
                                    <input type="text" value="{{ $form->getPublicUrl() }}" class="input input-bordered input-sm flex-1 font-mono text-xs" readonly>
                                    <button onclick="copyToClipboard('{{ $form->getPublicUrl() }}')" class="btn btn-outline btn-sm">
                                        <i class="fa-sharp fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            <div>
                                <a href="{{ $form->getPublicUrl() }}" target="_blank" class="btn btn-primary btn-sm gap-2 w-full">
                                    <i class="fa-sharp fa-external-link"></i>
                                    View Public Form
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Form Statistics -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                            <div class="flex items-center gap-3">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-info text-info-content w-8 rounded-lg">
                                        <i class="fa-sharp fa-chart-bar text-sm"></i>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-base-content">Statistics</h4>
                            </div>
                        </div>
                        <div class="p-6 space-y-4">
                            <div class="stats stats-vertical shadow w-full">
                                <div class="stat">
                                    <div class="stat-title">Total Submissions</div>
                                    <div class="stat-value text-primary">{{ $form->submissions->count() }}</div>
                                </div>
                                <div class="stat">
                                    <div class="stat-title">Pending Review</div>
                                    <div class="stat-value text-warning">{{ $form->submissions->where('status', 'submitted')->count() + $form->submissions->where('status', 'reviewing')->count() }}</div>
                                </div>
                                <div class="stat">
                                    <div class="stat-title">Approved</div>
                                    <div class="stat-value text-success">{{ $form->submissions->where('status', 'approved')->count() }}</div>
                                </div>
                            </div>
                            
                            @perms('view_form_submissions')
                                <a href="{{ route('form-submissions.index', ['form_id' => $form->id]) }}" class="btn btn-outline btn-sm gap-2 w-full">
                                    <i class="fa-sharp fa-list"></i>
                                    View All Submissions
                                </a>
                            @endperms
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    @perms('manage_forms')
                        <div class="card bg-base-100 shadow-md">
                            <div class="bg-gradient-to-r from-warning/10 to-warning/5 px-6 py-4 border-b border-base-300/50">
                                <div class="flex items-center gap-3">
                                    <div class="avatar avatar-placeholder">
                                        <div class="bg-warning text-warning-content w-8 rounded-lg">
                                            <i class="fa-sharp fa-bolt text-sm"></i>
                                        </div>
                                    </div>
                                    <h4 class="text-lg font-semibold text-base-content">Quick Actions</h4>
                                </div>
                            </div>
                            <div class="p-6 space-y-3">
                                <a href="{{ route('forms.builder', $form) }}" class="btn btn-secondary btn-sm gap-2 w-full">
                                    <i class="fa-sharp fa-edit"></i>
                                    Form Builder
                                </a>
                                <a href="{{ route('forms.preview', $form) }}" class="btn btn-info btn-sm gap-2 w-full">
                                    <i class="fa-sharp fa-eye"></i>
                                    Preview Form
                                </a>
                                <button onclick="cloneForm()" class="btn btn-accent btn-sm gap-2 w-full">
                                    <i class="fa-sharp fa-copy"></i>
                                    Clone Form
                                </button>
                            </div>
                        </div>
                    @endperms

                </div>
            </div>

        </div>
    </div>


    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show a temporary success message
                const button = event.target.closest('button');
                const originalContent = button.innerHTML;
                button.innerHTML = '<i class="fa-sharp fa-check"></i>';
                button.classList.add('btn-success');
                
                setTimeout(() => {
                    button.innerHTML = originalContent;
                    button.classList.remove('btn-success');
                }, 2000);

                showSuccess({
                    title: 'Copied!',
                    message: 'The form URL has been copied to your clipboard.'
                });
            }).catch(function(err) {
                showError({
                    title: 'Copy Failed',
                    message: 'Failed to copy URL to clipboard. Please copy the URL manually.'
                });
            });
        }

        @perms('manage_forms')
        function cloneForm() {
            showConfirm({
                title: 'Clone Form',
                message: 'Are you sure you want to clone this form? A copy will be created that you can modify independently.',
                confirmText: 'Clone Form',
                confirmClass: 'btn-accent',
                icon: 'fa-copy',
                iconColor: 'bg-accent/10 text-accent'
            }).then(confirmed => {
                if (confirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '{{ route("forms.clone", $form) }}';
                    
                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                    form.appendChild(csrfToken);
                    
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }
        @endperms
    </script>
</x-app-layout>