<x-app-layout
    page-title="Customer Discounts"
    page-icon="fa-sharp fa-user-tag"
    :primary-buttons="[
        [
            'type' => 'create',
            'route' => route('customer_discounts.create'),
            'text' => 'Add Customer Discount'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Customer Discounts', 'icon' => 'fa-user-tag']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Search Bar -->
            <div class="mb-6">
                <form method="GET" action="{{ route('customer_discounts.index') }}" class="flex flex-col md:flex-row gap-4">
                    <div class="flex-grow">
                        <div class="relative">
                            <input type="text" name="search" placeholder="Search by customer or discount name..."
                                class="input input-bordered w-full pr-10" value="{{ request('search') }}">
                            <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2">
                                <i class="fa-sharp fa-search text-gray-400"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex flex-wrap gap-2">
                        <select name="customer_id" class="select select-bordered" onchange="this.form.submit()">
                            <option value="">All Customers</option>
                            @foreach($customers as $customer)
                                <option value="{{ $customer->id }}" {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                                    {{ $customer->name }}
                                </option>
                            @endforeach
                        </select>

                        <select name="discount_id" class="select select-bordered" onchange="this.form.submit()">
                            <option value="">All Discounts</option>
                            @foreach($discounts as $discount)
                                <option value="{{ $discount->id }}" {{ request('discount_id') == $discount->id ? 'selected' : '' }}>
                                    {{ $discount->name }}
                                </option>
                            @endforeach
                        </select>

                        <select name="status" class="select select-bordered" onchange="this.form.submit()">
                            <option value="">All Statuses</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="expired" {{ request('status') === 'expired' ? 'selected' : '' }}>Expired</option>
                            <option value="future" {{ request('status') === 'future' ? 'selected' : '' }}>Future</option>
                        </select>

                        @if(request('search') || request('customer_id') || request('discount_id') || request('status'))
                            <a href="{{ route('customer_discounts.index') }}" class="btn btn-ghost">
                                <i class="fa-sharp fa-times"></i> Clear
                            </a>
                        @endif
                    </div>
                </form>
            </div>

            <div class="card bg-base-100 shadow-lg p-6">
                <div class="overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr>
                                @php
                                    $columns = [
                                        'id' => 'ID',
                                        'customer_name' => 'Customer',
                                        'discount_name' => 'Discount',
                                        'start_date' => 'Start Date',
                                        'end_date' => 'End Date',
                                        'usage_count' => 'Usage Count',
                                        'maximum_uses' => 'Maximum Uses',
                                        'created_at' => 'Created At',
                                    ];
                                @endphp
                                @foreach ($columns as $key => $label)
                                    <th>
                                        <a href="{{ route('customer_discounts.index', array_merge(request()->except(['page', 'sort', 'order']), [
                                            'sort' => $key,
                                            'order' => $sort === $key && $order === 'asc' ? 'desc' : 'asc',
                                            'pagination' => $pagination, // Preserve the current pagination value
                                        ])) }}" class="flex items-center">
                                            {{ $label }}
                                            @if ($sort === $key)
                                                <i class="fa-sharp {{ $order === 'asc' ? 'fa-sort-up' : 'fa-sort-down' }} ml-2"></i>
                                            @else
                                                <i class="fa-sharp fa-sort ml-2"></i>
                                            @endif
                                        </a>
                                    </th>
                                @endforeach
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($customerDiscounts as $cd)
                                <tr>
                                    <td>{{ $cd->id }}</td>
                                    <td>
                                        <a href="{{ route('customers.show', $cd->customer_id) }}" class="link link-primary font-bold">
                                            {{ $cd->customer->name }}
                                        </a>
                                    </td>
                                    <td>{{ $cd->discount->name }}</td>
                                    <td>{{ $cd->start_date ? $cd->start_date->format('Y-m-d') : 'N/A' }}</td>
                                    <td>{{ $cd->end_date ? $cd->end_date->format('Y-m-d') : 'N/A' }}</td>
                                    <td>{{ $cd->usage_count }}</td>
                                    <td>{{ $cd->maximum_uses ?? 'Unlimited' }}</td>
                                    <td>{{ $cd->created_at->format('Y-m-d') }}</td>
                                    <td>
                                        @php
                                            $now = now();
                                            $isActive = (!$cd->start_date || $cd->start_date <= $now) &&
                                                       (!$cd->end_date || $cd->end_date >= $now) &&
                                                       ($cd->maximum_uses === null || $cd->usage_count < $cd->maximum_uses);
                                            $isFuture = $cd->start_date && $cd->start_date > $now;
                                            $isExpired = ($cd->end_date && $cd->end_date < $now) ||
                                                        ($cd->maximum_uses !== null && $cd->usage_count >= $cd->maximum_uses);
                                        @endphp

                                        @if($isActive)
                                            <span class="badge badge-success">Active</span>
                                        @elseif($isFuture)
                                            <span class="badge badge-info">Future</span>
                                        @elseif($isExpired)
                                            <span class="badge badge-error">Expired</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="flex space-x-2">
                                            <a href="{{ route('customer_discounts.edit', $cd) }}" class="btn btn-info btn-sm">
                                                <i class="fa-sharp fa-solid fa-pen-to-square"></i> Edit
                                            </a>
                                            <form action="{{ route('customer_discounts.destroy', $cd) }}" method="POST">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-error btn-sm" onclick="return confirm('Are you sure?')">
                                                    <i class="fa-sharp fa-solid fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="10" class="text-center">No Customer Discounts Found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <x-pagination :paginator="$customerDiscounts" :pagination="$pagination" />
            </div>
        </div>
    </div>
</x-app-layout>
