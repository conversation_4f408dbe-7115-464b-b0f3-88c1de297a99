@php
    $backRoute = $selectedCustomer ? route('customers.show', $selectedCustomer) : route('customer_discounts.index');
    $backText = $selectedCustomer ? 'Back to ' . $selectedCustomer->name : 'Back to Customer Discounts';
@endphp

<x-app-layout
    page-title="Create Customer Discount"
    page-icon="fa-sharp fa-plus"
    :primary-buttons="[
        [
            'type' => 'back',
            'route' => $backRoute,
            'text' => $backText
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Customer Discounts', 'route' => 'customer_discounts.index', 'icon' => 'fa-user-tag'],
        ['name' => 'Create Customer Discount', 'icon' => 'fa-plus']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            <div class="card bg-base-100 shadow p-6">
                <form action="{{ route('customer_discounts.store') }}" method="POST">
                    @csrf
                    @if($selectedCustomer)
                        <input type="hidden" name="redirect_to_customer" value="true">
                    @endif
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Customer -->
                        <fieldset class="fieldset">
                            <legend class="font-semibold text-base-content mb-2">Customer</legend>
                            <div class="flex items-center justify-between">
                                <label for="customerSearch" class="label">Customer <span class="text-error">*</span></label>
                                <div class="tooltip tooltip-left" data-tip="Start typing to search for an existing customer">
                                    <i class="fa-sharp fa-circle-info text-base-300"></i>
                                </div>
                            </div>
                            <div class="relative">
                                <!-- Dynamic Search Component -->
                                <x-dynamic-customer-search
                                    id="customerSearch"
                                    name="customer_id"
                                    placeholder="Search for customers by name, business name, email, phone, or nickname..."
                                    action="form"
                                    quickCreateButtonId="quickCreateCustomerBtn"
                                    :selectedId="$selectedCustomerId"
                                    selectedName="{{ $selectedCustomer ? $selectedCustomer->name : '' }}"
                                />
                                <!-- Quick Create Button -->
                                <x-quick-create-customer
                                    id="customerSearch"
                                    name="customer_id"
                                />
                            </div>
                            @error('customer_id')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>

                        <!-- Discount -->
                        <fieldset class="fieldset">
                            <legend class="font-semibold text-base-content mb-2">Discount</legend>
                            <label for="discount_id" class="label">Discount <span class="text-error">*</span></label>
                            <select name="discount_id" id="discount_id" class="select select-bordered bg-base-100 text-base-content" required>
                                <option value="">Select a Discount</option>
                                @foreach($discounts as $discount)
                                    <option value="{{ $discount->id }}" {{ old('discount_id') == $discount->id ? 'selected' : '' }}>{{ $discount->name }}</option>
                                @endforeach
                            </select>
                            @error('discount_id')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>

                        <!-- Start Date -->
                        <fieldset class="fieldset">
                            <legend class="font-semibold text-base-content mb-2">Start Date</legend>
                            <label for="start_date" class="label">Start Date</label>
                            <input type="datetime-local" name="start_date" id="start_date" class="input input-bordered bg-base-100 text-base-content" value="{{ old('start_date') }}">
                            <div class="flex mt-2 space-x-2">
                                <button type="button" class="btn btn-xs btn-outline btn-primary" id="start_date_today">Today</button>
                            </div>
                            @error('start_date')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>

                        <!-- End Date -->
                        <fieldset class="fieldset">
                            <legend class="font-semibold text-base-content mb-2">End Date</legend>
                            <label for="end_date" class="label">End Date</label>
                            <input type="datetime-local" name="end_date" id="end_date" class="input input-bordered bg-base-100 text-base-content" value="{{ old('end_date') }}">
                            <div class="flex mt-2 space-x-2">
                                <button type="button" class="btn btn-xs btn-outline btn-primary" id="end_date_1month">1 Month</button>
                                <button type="button" class="btn btn-xs btn-outline btn-primary" id="end_date_1year">1 Year</button>
                            </div>
                            @error('end_date')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>

                        <!-- Maximum Uses -->
                        <fieldset class="fieldset">
                            <legend class="font-semibold text-base-content mb-2">Maximum Uses</legend>
                            <label for="maximum_uses" class="label">Maximum Uses</label>
                            <input type="number" name="maximum_uses" id="maximum_uses" class="input input-bordered bg-base-100 text-base-content" value="{{ old('maximum_uses') }}">
                            @error('maximum_uses')
                                <p class="text-error text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </fieldset>
                    </div>

                    <!-- Submit Button -->
                    <div class="mt-6">
                        <button type="submit" class="btn btn-primary bg-primary text-primary-content hover:bg-primary-focus">Create Customer Discount</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get references to the date inputs and buttons
            const startDateInput = document.getElementById('start_date');
            const endDateInput = document.getElementById('end_date');
            const startDateTodayBtn = document.getElementById('start_date_today');
            const endDate1MonthBtn = document.getElementById('end_date_1month');
            const endDate1YearBtn = document.getElementById('end_date_1year');

            // Format a date as YYYY-MM-DDT00:00
            function formatDateForInput(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}T00:00`;
            }

            // Set start date to today at midnight
            startDateTodayBtn.addEventListener('click', function() {
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                startDateInput.value = formatDateForInput(today);
            });

            // Set end date to 1 month from today (same day next month or last day if not available)
            endDate1MonthBtn.addEventListener('click', function() {
                // First check if start date is set
                let baseDate;
                if (startDateInput.value) {
                    baseDate = new Date(startDateInput.value);
                } else {
                    baseDate = new Date();
                    baseDate.setHours(0, 0, 0, 0);
                }

                const currentDay = baseDate.getDate();
                const currentMonth = baseDate.getMonth();
                const currentYear = baseDate.getFullYear();

                // Create a date for the first day of the next month
                const nextMonth = new Date(currentYear, currentMonth + 1, 1);

                // Get the last day of the next month
                const lastDayNextMonth = new Date(currentYear, currentMonth + 2, 0).getDate();

                // If the current day exists in the next month, use it, otherwise use the last day
                const targetDay = Math.min(currentDay, lastDayNextMonth);

                // Set the date to the appropriate day in the next month
                nextMonth.setDate(targetDay);

                endDateInput.value = formatDateForInput(nextMonth);
            });

            // Set end date to 1 year from today
            endDate1YearBtn.addEventListener('click', function() {
                // First check if start date is set
                let baseDate;
                if (startDateInput.value) {
                    baseDate = new Date(startDateInput.value);
                } else {
                    baseDate = new Date();
                    baseDate.setHours(0, 0, 0, 0);
                }

                const currentDay = baseDate.getDate();
                const currentMonth = baseDate.getMonth();
                const currentYear = baseDate.getFullYear();

                // Handle February 29 for leap years
                let targetDay = currentDay;
                if (currentMonth === 1 && currentDay === 29 && !isLeapYear(currentYear + 1)) {
                    targetDay = 28;
                }

                const nextYear = new Date(currentYear + 1, currentMonth, targetDay);
                endDateInput.value = formatDateForInput(nextYear);
            });

            // Helper function to check if a year is a leap year
            function isLeapYear(year) {
                return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
            }
        });
    </script>
</x-app-layout>
