<x-app-layout
    page-title="Dashboard"
    page-icon="fa-sharp fa-tachometer-alt">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8 space-y-8">
        <!-- Welcome Section -->
        <div class="card bg-base-100 shadow-md p-6">
            <h2 class="text-2xl font-semibold text-base-content">Hi, {{ Auth::user()->name }}!</h2>
            <p class="text-base-content/70">Welcome to the ETR Flow System.</p>
            <p class="text-sm text-base-content/60 mt-2">The data contained in this system is for internal use only and should be kept strictly confidential.</p>
        </div>

        <!-- Top Row: Quick Links, Customer Search, Time Clock, and Tasks -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <x-dashboard-widget
                title="Quick Links"
                icon="fa-link"
                color="primary">
                <x-dash-quick-links />
            </x-dashboard-widget>
            <div class="lg:col-span-2">
            <x-dashboard-widget
                title="Customer Search"
                icon="fa-search"
                color="secondary">
                <x-dash-customer-search />
            </x-dashboard-widget>
            </div>
            @perms('view_pickup_requests')
                <x-dashboard-widget
                    title="Today's Pickups"
                    icon="fa-truck-pickup"
                    color="info">
                    <x-dash-todays-pickups :todaysPickups="$todaysPickups" />
                </x-dashboard-widget>
            @endperms
            @perms('view_own_timecards')
                <x-dashboard-widget
                    title="Time Clock"
                    icon="fa-clock"
                    color="info">
                    <x-dash-time-clock />
                </x-dashboard-widget>
            @endperms

            @if(\App\Models\GlobalConfig::isTasksEnabled())
                <x-dashboard-widget
                    title="My Tasks"
                    icon="fa-tasks"
                    color="accent">
                    <livewire:user-task-list :user="auth()->user()" />
                </x-dashboard-widget>
            @endif

            @if(auth()->user()->hasPermission('view_reports'))
                <x-dashboard-widget
                    title="Sales Reports"
                    icon="fa-chart-line"
                    color="success">
                    <x-reports-sales />
                </x-dashboard-widget>
            @endif



            @perms('view_inventory_items')
            <x-dashboard-widget
                title="Newest Listings"
                icon="fa-images"
                color="warning">
                <x-dash-newest-listings :newestListings="$newestListings" />
            </x-dashboard-widget>
            @endperms

            @perms('view_invoices')
            <x-dashboard-widget
                title="Latest Sales"
                icon="fa-shopping-cart"
                color="error">
                <x-latest-sales />
            </x-dashboard-widget>
            @endperms
        </div>


    </div>
</x-app-layout>
