@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2>Agreement Details</h2>
            <div>
                @can('edit_agreements')
                <a href="{{ route('agreements.edit', $agreement->id) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-edit"></i> Edit
                </a>
                @endcan
                
                @can('sign_agreements')
                @if(!$agreement->isSigned())
                <a href="{{ route('agreements.sign', $agreement->id) }}" class="btn btn-success btn-sm">
                    <i class="fas fa-signature"></i> Sign
                </a>
                @endif
                @endcan
                
                <a href="{{ route('agreements.pdf', $agreement->id) }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-file-pdf"></i> PDF
                </a>
                
                @can('delete_agreements')
                <form method="POST" action="{{ route('agreements.destroy', $agreement->id) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this agreement?');">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger btn-sm">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </form>
                @endcan
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h3>{{ $agreement->title }}</h3>
                    <div class="mb-4">
                        <span class="badge bg-{{ $agreement->status == 'draft' ? 'warning' : ($agreement->status == 'active' ? 'success' : 'secondary') }}">
                            {{ ucfirst($agreement->status) }}
                        </span>
                        <span class="badge bg-info">{{ ucfirst($agreement->type) }}</span>
                    </div>
                    
                    <div class="mb-3">
                        <h5>Description</h5>
                        <p>{{ $agreement->description }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h5>Content</h5>
                        <div class="border p-3 bg-light">
                            {!! $agreement->content !!}
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-header">
                            <h5>Customer Information</h5>
                        </div>
                        <div class="card-body">
                            @if($agreement->customer)
                                <p><strong>Name:</strong> {{ $agreement->customer->name }}</p>
                                <p><strong>Email:</strong> {{ $agreement->customer->email }}</p>
                                <p><strong>Phone:</strong> {{ $agreement->customer->phone }}</p>
                                @can('view_customer_accounts')
                                <a href="{{ route('customers.show', $agreement->customer->id) }}" class="btn btn-sm btn-outline-primary">
                                    View Customer
                                </a>
                                @endcan
                            @else
                                <p class="text-muted">No customer associated with this agreement.</p>
                            @endif
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5>Signatures</h5>
                        </div>
                        <div class="card-body">
                            @if($agreement->signatures->count() > 0)
                                @foreach($agreement->signatures as $signature)
                                <div class="mb-2 p-2 {{ $signature->is_signed ? 'border-success' : 'border-warning' }} border">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <p class="mb-0"><strong>{{ $signature->user->name }}</strong></p>
                                            <small class="text-muted">{{ $signature->user->email }}</small>
                                        </div>
                                        <div>
                                            @if($signature->is_signed)
                                                <span class="badge bg-success">Signed</span>
                                                <div class="mt-1">{{ $signature->signature_date->format('M d, Y') }}</div>
                                            @else
                                                <span class="badge bg-warning">Pending</span>
                                            @endif
                                        </div>
                                    </div>
                                    
                                    @if($signature->is_signed && $signature->signature_image_url)
                                        <div class="mt-2">
                                            <img src="{{ $signature->signature_image_url }}" alt="Signature" class="img-fluid border" style="max-height: 80px">
                                        </div>
                                    @endif
                                </div>
                                @endforeach
                            @else
                                <p class="text-muted">No signatures required for this agreement.</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Fields Section -->
            @if($agreement->fields->count() > 0)
            <div class="mt-4">
                <h4>Agreement Fields</h4>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Type</th>
                                <th>Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($agreement->fields as $field)
                            <tr>
                                <td>{{ $field->label }}</td>
                                <td>{{ ucfirst($field->field_type) }}</td>
                                <td>
                                    @php
                                        $latestValue = $field->getLatestValue();
                                    @endphp
                                    
                                    @if($latestValue)
                                        @if(is_array($latestValue->value))
                                            {{ implode(', ', $latestValue->value) }}
                                        @else
                                            {{ $latestValue->value }}
                                        @endif
                                    @else
                                        <span class="text-muted">No value</span>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            @endif
        </div>
        <div class="card-footer text-muted">
            <div class="d-flex justify-content-between">
                <div>
                    <small>Created: {{ $agreement->created_at->format('M d, Y H:i') }}</small>
                </div>
                <div>
                    <small>Last Updated: {{ $agreement->updated_at->format('M d, Y H:i') }}</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mb-4">
        <a href="{{ route('agreements.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Agreements
        </a>
    </div>
</div>
@endsection
