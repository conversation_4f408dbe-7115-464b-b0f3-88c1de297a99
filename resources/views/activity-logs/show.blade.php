<x-app-layout
    page-title="Activity Log Details"
    page-icon="fa-sharp fa-eye"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('activity-logs.index'),
            'text' => 'Back to Logs'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Activity Logs', 'route' => 'activity-logs.index', 'icon' => 'fa-history'],
        ['name' => 'Log Details', 'icon' => 'fa-eye']
    ]">

    <div class="py-6">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <!-- Header Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Date & Time</label>
                                <p class="mt-1 text-sm text-gray-900">
                                    {{ $activityLog->created_at->format('F j, Y \a\t g:i A') }}
                                    <span class="text-gray-500">({{ $activityLog->created_at->diffForHumans() }})</span>
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">User</label>
                                <p class="mt-1 text-sm text-gray-900">
                                    @if($activityLog->user)
                                        <div class="flex items-center space-x-2">
                                            <div class="avatar">
                                                <div class="w-8 rounded-full">
                                                    @if($activityLog->user->profile_photo_id)
                                                        <img src="{{ $activityLog->user->profile_photo_url }}" alt="{{ $activityLog->user->name }}" class="w-8 h-8 rounded-full object-cover">
                                                    @else
                                                        <div class="bg-neutral text-neutral-content rounded-full w-8 h-8 flex items-center justify-center">
                                                            <span class="text-xs">{{ substr($activityLog->user->name, 0, 2) }}</span>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                            <span>{{ $activityLog->user->name }}</span>
                                            <span class="text-gray-500">({{ $activityLog->user->email }})</span>
                                        </div>
                                    @else
                                        <span class="text-gray-500">System</span>
                                    @endif
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Component</label>
                                <p class="mt-1">
                                    <span class="badge badge-outline">
                                        {{ ucfirst(str_replace('_', ' ', $activityLog->component)) }}
                                    </span>
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Event Type</label>
                                <p class="mt-1">
                                    <span class="badge 
                                        @if($activityLog->event_type === 'created') badge-success
                                        @elseif($activityLog->event_type === 'updated') badge-warning
                                        @elseif($activityLog->event_type === 'deleted') badge-error
                                        @else badge-info
                                        @endif">
                                        {{ $activityLog->formatted_event_type }}
                                    </span>
                                </p>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Model</label>
                                <p class="mt-1 text-sm text-gray-900">
                                    @if($activityLog->loggable)
                                        {{ $activityLog->model_name }} #{{ $activityLog->loggable_id }}
                                        @if($activityLog->loggable_type === 'App\Models\Customer' && $activityLog->loggable)
                                            <br><span class="text-gray-500">{{ $activityLog->loggable->name }}</span>
                                        @endif
                                    @else
                                        <span class="text-gray-400">No specific model</span>
                                    @endif
                                </p>
                            </div>

                            @if($activityLog->ip_address)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">IP Address</label>
                                    <p class="mt-1 text-sm text-gray-900 font-mono">{{ $activityLog->ip_address }}</p>
                                </div>
                            @endif

                            @if($activityLog->user_agent)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">User Agent</label>
                                    <p class="mt-1 text-sm text-gray-900 break-all">{{ $activityLog->user_agent }}</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mb-8">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-sm text-gray-900">{{ $activityLog->description }}</p>
                        </div>
                    </div>

                    <!-- Changes (for update events) -->
                    @if($activityLog->old_values || $activityLog->new_values)
                        <div class="mb-8">
                            <label class="block text-sm font-medium text-gray-700 mb-4">Changes</label>
                            
                            @if($activityLog->event_type === 'updated' && $activityLog->old_values && $activityLog->new_values)
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <!-- Old Values -->
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-700 mb-2">Previous Values</h4>
                                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                            @foreach($activityLog->old_values as $field => $value)
                                                <div class="mb-2 last:mb-0">
                                                    <span class="font-medium text-red-800">{{ ucfirst(str_replace('_', ' ', $field)) }}:</span>
                                                    <span class="text-red-700">{{ $value ?? 'null' }}</span>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>

                                    <!-- New Values -->
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-700 mb-2">New Values</h4>
                                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                            @foreach($activityLog->new_values as $field => $value)
                                                <div class="mb-2 last:mb-0">
                                                    <span class="font-medium text-green-800">{{ ucfirst(str_replace('_', ' ', $field)) }}:</span>
                                                    <span class="text-green-700">{{ $value ?? 'null' }}</span>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @else
                                <!-- For create/delete events, show all values -->
                                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                    @php
                                        $values = $activityLog->new_values ?: $activityLog->old_values;
                                    @endphp
                                    @if($values)
                                        @foreach($values as $field => $value)
                                            <div class="mb-2 last:mb-0">
                                                <span class="font-medium text-gray-800">{{ ucfirst(str_replace('_', ' ', $field)) }}:</span>
                                                <span class="text-gray-700">{{ $value ?? 'null' }}</span>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                            @endif
                        </div>
                    @endif

                    <!-- Additional Data -->
                    @if($activityLog->additional_data)
                        <div class="mb-8">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Additional Data</label>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <pre class="text-sm text-blue-800 whitespace-pre-wrap">{{ json_encode($activityLog->additional_data, JSON_PRETTY_PRINT) }}</pre>
                            </div>
                        </div>
                    @endif

                    <!-- Related Model Link -->
                    @if($activityLog->loggable)
                        <div class="border-t pt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Related Record</label>
                            @if($activityLog->loggable_type === 'App\Models\Customer')
                                <a href="{{ route('customers.show', $activityLog->loggable) }}" class="btn btn-sm btn-outline">
                                    <i class="fa-sharp fa-external-link-alt"></i> View Customer
                                </a>
                            @else
                                <p class="text-sm text-gray-500">
                                    {{ $activityLog->model_name }} #{{ $activityLog->loggable_id }}
                                    @if($activityLog->event_type === 'deleted')
                                        <span class="text-red-500">(Deleted)</span>
                                    @endif
                                </p>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
