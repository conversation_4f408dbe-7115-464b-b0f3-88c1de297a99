<x-app-layout
    page-title="Activity Logs"
    page-icon="fa-sharp fa-history"
    :action-buttons="[
        [
            'name' => 'Statistics',
            'route' => route('activity-logs.statistics'),
            'icon' => 'fa-sharp fa-chart-bar',
            'class' => 'btn btn-outline btn-sm gap-2'
        ],
        [
            'name' => 'Export',
            'icon' => 'fa-sharp fa-download',
            'class' => 'btn btn-primary btn-sm gap-2',
            'id' => 'export-logs-button'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Activity Logs', 'icon' => 'fa-history']
    ]">

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Filters -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Filters</h3>
                    <form method="GET" action="{{ route('activity-logs.index') }}" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Component</span>
                            </label>
                            <select name="component" class="select select-bordered">
                                <option value="">All Components</option>
                                @foreach($components as $component)
                                    <option value="{{ $component }}" {{ request('component') == $component ? 'selected' : '' }}>
                                        {{ ucfirst(str_replace('_', ' ', $component)) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">User</span>
                            </label>
                            <select name="user_id" class="select select-bordered">
                                <option value="">All Users</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Event Type</span>
                            </label>
                            <select name="event_type" class="select select-bordered">
                                <option value="">All Events</option>
                                @foreach($eventTypes as $eventType)
                                    <option value="{{ $eventType }}" {{ request('event_type') == $eventType ? 'selected' : '' }}>
                                        {{ ucfirst($eventType) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Search</span>
                            </label>
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Search description..." class="input input-bordered">
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Date From</span>
                            </label>
                            <input type="date" name="date_from" value="{{ request('date_from') }}" class="input input-bordered">
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Date To</span>
                            </label>
                            <input type="date" name="date_to" value="{{ request('date_to') }}" class="input input-bordered">
                        </div>

                        <div class="form-control flex justify-end items-end">
                            <div class="flex space-x-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa-sharp fa-search"></i> Filter
                                </button>
                                <a href="{{ route('activity-logs.index') }}" class="btn btn-outline">
                                    <i class="fa-sharp fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">
                            Activity Logs ({{ $logs->total() }} total)
                        </h3>
                    </div>

                    @if($logs->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="table table-zebra w-full">
                                <thead>
                                    <tr>
                                        <th>Date/Time</th>
                                        <th>User</th>
                                        <th>Component</th>
                                        <th>Event</th>
                                        <th>Description</th>
                                        <th>Model</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($logs as $log)
                                        <tr>
                                            <td class="text-sm">
                                                {{ $log->created_at->format('M j, Y') }}<br>
                                                <span class="text-gray-500">{{ $log->created_at->format('g:i A') }}</span>
                                            </td>
                                            <td>
                                                @if($log->user)
                                                    <div class="flex items-center space-x-2">
                                                        <div class="avatar">
                                                            <div class="w-8 rounded-full">
                                                                @if($log->user->profile_photo_id)
                                                                    <img src="{{ $log->user->profile_photo_url }}" alt="{{ $log->user->name }}" class="w-8 h-8 rounded-full object-cover">
                                                                @else
                                                                    <div class="bg-neutral text-neutral-content rounded-full w-8 h-8 flex items-center justify-center">
                                                                        <span class="text-xs">{{ substr($log->user->name, 0, 2) }}</span>
                                                                    </div>
                                                                @endif
                                                            </div>
                                                        </div>
                                                        <span>{{ $log->user->name }}</span>
                                                    </div>
                                                @else
                                                    <span class="text-gray-500">System</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge badge-outline">
                                                    {{ ucfirst(str_replace('_', ' ', $log->component)) }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge 
                                                    @if($log->event_type === 'created') badge-success
                                                    @elseif($log->event_type === 'updated') badge-warning
                                                    @elseif($log->event_type === 'deleted') badge-error
                                                    @else badge-info
                                                    @endif">
                                                    {{ $log->formatted_event_type }}
                                                </span>
                                            </td>
                                            <td class="max-w-xs truncate">{{ $log->description }}</td>
                                            <td>
                                                @if($log->loggable)
                                                    <span class="text-sm text-gray-600">
                                                        {{ $log->model_name }} #{{ $log->loggable_id }}
                                                    </span>
                                                @else
                                                    <span class="text-gray-400">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <a href="{{ route('activity-logs.show', $log) }}" class="btn btn-xs btn-outline">
                                                    <i class="fa-sharp fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fa-sharp fa-history text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500">No activity logs found matching your criteria.</p>
                        </div>
                    @endif
                </div>

                <!-- Pagination Section -->
                @if($logs->hasPages())
                    <div class="px-6 py-4 border-t border-base-300/50 bg-base-50">
                        <x-pagination :paginator="$logs" :pagination="$pagination" />
                    </div>
                @endif
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const exportButton = document.getElementById('export-logs-button');
            if (exportButton) {
                exportButton.addEventListener('click', function() {
                    const params = new URLSearchParams(window.location.search);
                    const exportUrl = '{{ route("activity-logs.export") }}?' + params.toString();
                    window.location.href = exportUrl;
                });
            }
        });
    </script>
</x-app-layout>
