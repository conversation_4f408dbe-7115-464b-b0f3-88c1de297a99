<x-app-layout
    page-title="Activity Log Statistics"
    page-icon="fa-sharp fa-chart-bar"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('activity-logs.index'),
            'text' => 'Back to Logs'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Activity Logs', 'route' => 'activity-logs.index', 'icon' => 'fa-history'],
        ['name' => 'Statistics', 'icon' => 'fa-chart-bar']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Time Period Filter -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Time Period</h3>
                    <form method="GET" action="{{ route('activity-logs.statistics') }}" class="flex items-center space-x-4">
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Days</span>
                            </label>
                            <select name="days" class="select select-bordered" onchange="this.form.submit()">
                                <option value="7" {{ $days == 7 ? 'selected' : '' }}>Last 7 days</option>
                                <option value="30" {{ $days == 30 ? 'selected' : '' }}>Last 30 days</option>
                                <option value="90" {{ $days == 90 ? 'selected' : '' }}>Last 90 days</option>
                                <option value="365" {{ $days == 365 ? 'selected' : '' }}>Last year</option>
                            </select>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Summary Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="stat bg-white shadow-xl rounded-lg">
                    <div class="stat-figure text-primary">
                        <i class="fa-sharp fa-history text-2xl"></i>
                    </div>
                    <div class="stat-title">Total Activities</div>
                    <div class="stat-value text-primary">{{ number_format($stats['total_activities']) }}</div>
                    <div class="stat-desc">Last {{ $days }} days</div>
                </div>

                <div class="stat bg-white shadow-xl rounded-lg">
                    <div class="stat-figure text-success">
                        <i class="fa-sharp fa-plus text-2xl"></i>
                    </div>
                    <div class="stat-title">Created Events</div>
                    <div class="stat-value text-success">
                        {{ $stats['activities_by_type']->where('event_type', 'created')->first()->count ?? 0 }}
                    </div>
                    <div class="stat-desc">New records</div>
                </div>

                <div class="stat bg-white shadow-xl rounded-lg">
                    <div class="stat-figure text-warning">
                        <i class="fa-sharp fa-edit text-2xl"></i>
                    </div>
                    <div class="stat-title">Updated Events</div>
                    <div class="stat-value text-warning">
                        {{ $stats['activities_by_type']->where('event_type', 'updated')->first()->count ?? 0 }}
                    </div>
                    <div class="stat-desc">Modified records</div>
                </div>

                <div class="stat bg-white shadow-xl rounded-lg">
                    <div class="stat-figure text-error">
                        <i class="fa-sharp fa-trash text-2xl"></i>
                    </div>
                    <div class="stat-title">Deleted Events</div>
                    <div class="stat-value text-error">
                        {{ $stats['activities_by_type']->where('event_type', 'deleted')->first()->count ?? 0 }}
                    </div>
                    <div class="stat-desc">Removed records</div>
                </div>
            </div>

            <!-- Charts and Tables -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Activities by Type -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Activities by Type</h3>
                        @if($stats['activities_by_type']->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="table table-zebra w-full">
                                    <thead>
                                        <tr>
                                            <th>Event Type</th>
                                            <th>Count</th>
                                            <th>Percentage</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($stats['activities_by_type'] as $type)
                                            <tr>
                                                <td>
                                                    <span class="badge 
                                                        @if($type->event_type === 'created') badge-success
                                                        @elseif($type->event_type === 'updated') badge-warning
                                                        @elseif($type->event_type === 'deleted') badge-error
                                                        @else badge-info
                                                        @endif">
                                                        {{ ucfirst($type->event_type) }}
                                                    </span>
                                                </td>
                                                <td>{{ number_format($type->count) }}</td>
                                                <td>
                                                    @php
                                                        $percentage = $stats['total_activities'] > 0 ? ($type->count / $stats['total_activities']) * 100 : 0;
                                                    @endphp
                                                    {{ number_format($percentage, 1) }}%
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <p class="text-gray-500">No activity data available.</p>
                        @endif
                    </div>
                </div>

                <!-- Activities by Component -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Activities by Component</h3>
                        @if($stats['activities_by_component']->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="table table-zebra w-full">
                                    <thead>
                                        <tr>
                                            <th>Component</th>
                                            <th>Count</th>
                                            <th>Percentage</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($stats['activities_by_component'] as $component)
                                            <tr>
                                                <td>
                                                    <span class="badge badge-outline">
                                                        {{ ucfirst(str_replace('_', ' ', $component->component)) }}
                                                    </span>
                                                </td>
                                                <td>{{ number_format($component->count) }}</td>
                                                <td>
                                                    @php
                                                        $percentage = $stats['total_activities'] > 0 ? ($component->count / $stats['total_activities']) * 100 : 0;
                                                    @endphp
                                                    {{ number_format($percentage, 1) }}%
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <p class="text-gray-500">No component data available.</p>
                        @endif
                    </div>
                </div>

                <!-- Most Active Users -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Most Active Users</h3>
                        @if($stats['most_active_users']->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="table table-zebra w-full">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Activities</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($stats['most_active_users'] as $userStat)
                                            <tr>
                                                <td>
                                                    @if($userStat->user)
                                                        <div class="flex items-center space-x-2">
                                                            <div class="avatar">
                                                                <div class="w-8 rounded-full">
                                                                    @if($userStat->user->profile_photo_id)
                                                                        <img src="{{ $userStat->user->profile_photo_url }}" alt="{{ $userStat->user->name }}" class="w-8 h-8 rounded-full object-cover">
                                                                    @else
                                                                        <div class="bg-neutral text-neutral-content rounded-full w-8 h-8 flex items-center justify-center">
                                                                            <span class="text-xs">{{ substr($userStat->user->name, 0, 2) }}</span>
                                                                        </div>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                            <span>{{ $userStat->user->name }}</span>
                                                        </div>
                                                    @else
                                                        <span class="text-gray-500">Unknown User</span>
                                                    @endif
                                                </td>
                                                <td>{{ number_format($userStat->count) }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <p class="text-gray-500">No user activity data available.</p>
                        @endif
                    </div>
                </div>

                <!-- Daily Activity Chart -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Daily Activity</h3>
                        @if($stats['daily_activity']->count() > 0)
                            <canvas id="dailyActivityChart" width="400" height="200"></canvas>
                        @else
                            <p class="text-gray-500">No daily activity data available.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if($stats['daily_activity']->count() > 0)
        @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            const ctx = document.getElementById('dailyActivityChart').getContext('2d');
            const dailyActivityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: {!! json_encode($stats['daily_activity']->pluck('date')->map(function($date) { return \Carbon\Carbon::parse($date)->format('M j'); })) !!},
                    datasets: [{
                        label: 'Activities',
                        data: {!! json_encode($stats['daily_activity']->pluck('count')) !!},
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        </script>
        @endpush
    @endif
</x-app-layout>
