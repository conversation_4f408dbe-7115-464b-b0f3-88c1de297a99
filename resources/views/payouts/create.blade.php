<x-app-layout
    page-title="Create Payout"
    page-icon="fa-sharp fa-money-bill-transfer"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('invoices.index'),
            'text' => 'Back to Invoices'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Invoices', 'route' => 'invoices.index', 'icon' => 'fa-file-invoice'],
        ['name' => 'Create Payout', 'icon' => 'fa-money-bill-transfer']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">

            <!-- Introduction Card -->
            <div class="bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
                <!-- Decorative Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 left-0 w-32 h-32 bg-primary rounded-full -translate-x-16 -translate-y-16"></div>
                    <div class="absolute bottom-0 right-0 w-24 h-24 bg-secondary rounded-full translate-x-12 translate-y-12"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-start gap-4">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-primary text-primary-content rounded-full w-16 h-16">
                                <i class="fa-sharp fa-money-bill-transfer text-2xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h1 class="text-2xl lg:text-3xl font-bold text-base-content mb-3">Create Customer Payout</h1>
                            <p class="text-base-content/80 text-lg mb-4">Use this form to create a payout to a customer. A payout is a negative-value invoice where your business pays money to the customer.</p>
                            <div class="bg-warning border border-warning/30 rounded-lg p-4">
                                <div class="flex items-start gap-3">
                                    <i class="fa-sharp fa-exclamation-triangle text-warning-content text-lg flex-shrink-0 mt-0.5"></i>
                                    <div>
                                        <p class="font-semibold text-warning-content mb-1">Important Legal Requirement</p>
                                        <p class="text-base-content/80" id="photo-id-requirement">A government-issued photo ID is required for all payouts to ensure legal compliance and proper documentation.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form action="{{ route('payouts.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                @csrf

                <!-- Customer Information Section -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content rounded-full w-10 h-10">
                                    <i class="fa-sharp fa-user text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Customer Information</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-6">

                        <div>
                            <label class="label" for="customerSearch">
                                <span class="label-text">
                                    <i class="fa-sharp fa-search text-primary mr-2"></i>
                                    Business Name or Customer Name <span class="text-error font-bold">*</span>
                                </span>
                            </label>
                            <p class="text-sm text-base-content/70 mb-2">If they are a business customer, enter their BUSINESS NAME first.</p>
                            <div class="relative">
                                <!-- Dynamic Search Component -->
                                <x-dynamic-customer-search
                                    id="customerSearch"
                                    name="customer_id"
                                    placeholder="Search for customers by name, business name, email, phone, or nickname..."
                                    action="form"
                                    quickCreateButtonId="quickCreateCustomerBtn"
                                />
                                <!-- Quick Create Button -->
                                <x-quick-create-customer
                                    id="customerSearch"
                                    name="customer_id"
                                />
                            </div>
                            @error('customer_id')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>

                        <!-- ID Upload (Required/Optional based on existing license) -->
                        <div id="photo-id-section">
                            <label class="label" for="photo_id">
                                <span class="label-text" id="photo-id-label">
                                    <i class="fa-sharp fa-id-card text-primary mr-2"></i>
                                    Photo ID <span class="text-error font-bold">*</span>
                                </span>
                            </label>
                            <div id="existing-license-notice" class="hidden mb-2 p-2 bg-success/10 border border-success/20 rounded text-sm">
                                <i class="fa-sharp fa-check-circle text-success mr-1"></i>
                                <span class="text-success font-medium">Customer has photo ID on file.</span>
                                <span class="text-base-content/70">Uploading a new photo ID is optional.</span>
                            </div>
                            <input type="file" name="photo_id" id="photo_id" accept=".jpg,.jpeg,.png,.pdf"
                                class="file-input file-input-bordered w-full @error('photo_id') file-input-error @enderror" required>
                            <div class="label">
                                <span class="label-text-alt" id="photo-id-help">
                                    <span class="font-medium" id="photo-id-required-text">Required.</span>
                                    Accepted formats: JPG, PNG, PDF. Max 10MB.
                                </span>
                            </div>
                            @error('photo_id')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror

                            <!-- Preview area for photo ID -->
                            <div id="id_preview" class="mt-3 hidden">
                                <h4 class="text-sm font-medium text-base-content mb-2">ID Preview:</h4>
                                <div id="id_preview_container" class="relative w-full max-w-md mx-auto"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payout Details Section -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-secondary/10 to-secondary/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-secondary text-secondary-content rounded-full w-10 h-10">
                                    <i class="fa-sharp fa-dollar-sign text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Payout Details</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-6">

                        <!-- Product Selection -->
                        <div>
                            <label class="label">
                                <span class="label-text">
                                    <i class="fa-sharp fa-box text-secondary mr-2"></i>
                                    Payout For <span class="text-error font-bold">*</span>
                                </span>
                            </label>

                            <!-- Hidden input to store the selected product ID -->
                            <input type="hidden" name="product_type" id="product_type" value="{{ old('product_type') }}" required>

                            <!-- Product buttons grid -->
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-3 mb-3">
                                @foreach ($payout_products as $item)
                                    <button type="button"
                                        class="product-button btn btn-outline h-auto py-3 flex flex-col items-center justify-center text-center normal-case {{ old('product_type') == $item->id ? 'btn-primary' : '' }}"
                                        data-id="{{ $item->id }}"
                                        data-suggested-price="{{ $item->suggested_price ?? 0 }}"
                                        data-unit="{{ $item->unit ?? 'unit' }}"
                                        data-tax-policy-id="{{ $item->category->tax_policy_id ?? '' }}"
                                        data-department-id="{{ $item->category->department_id ?? '' }}">
                                        <span class="font-medium">{{ $item->name }}</span>
                                        @if(!empty($item->asset_tag))
                                            <span class="text-xs opacity-70 mt-1">({{ $item->asset_tag }})</span>
                                        @endif
                                        @if($item->suggested_price)
                                            <span class="text-xs mt-1">${{ number_format($item->suggested_price, 2) }}</span>
                                        @endif
                                    </button>
                                @endforeach
                            </div>

                            <!-- Custom product link -->
                            <div class="text-center">
                                <button type="button" id="custom-product-link" class="text-sm text-primary hover:text-primary-focus hover:underline inline-flex items-center">
                                    <i class="fa-sharp fa-plus-circle mr-1 text-xs"></i>
                                    Custom Product (Not Listed)
                                </button>
                            </div>

                            @error('product_type')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>

                        <!-- Custom Product Fields (Hidden by default) -->
                        <div id="custom_product_fields" class="{{ old('product_type') == 'custom' ? '' : 'hidden' }}">
                            <div class="p-3 border border-dashed border-base-300 rounded-md bg-base-200/30">
                                <div class="mb-3">
                                    <label class="label" for="custom_product_name">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-tag text-secondary mr-2"></i>
                                            Custom Product Name <span class="text-error font-bold">*</span>
                                        </span>
                                    </label>
                                    <input type="text" name="custom_product_name" id="custom_product_name"
                                        class="input input-bordered w-full @error('custom_product_name') input-error @enderror"
                                        value="{{ old('custom_product_name') }}"
                                        placeholder="Enter product name">
                                    @error('custom_product_name')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>

                                <div>
                                    <label class="label" for="custom_product_category_id">
                                        <span class="label-text">
                                            <i class="fa-sharp fa-folder text-secondary mr-2"></i>
                                            Category <span class="text-error font-bold">*</span>
                                        </span>
                                    </label>
                                    <select name="custom_product_category_id" id="custom_product_category_id"
                                        class="select select-bordered w-full @error('custom_product_category_id') select-error @enderror">
                                        <option value="">-- Select Category --</option>
                                        @foreach ($categories as $category)
                                            <option value="{{ $category->id }}" {{ old('custom_product_category_id') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('custom_product_category_id')
                                    <div class="label">
                                        <span class="label-text-alt text-error flex items-center gap-1">
                                            <i class="fa-sharp fa-exclamation-triangle"></i>
                                            {{ $message }}
                                        </span>
                                    </div>
                                    @enderror
                                </div>

                                <div class="mt-3 flex justify-end">
                                    <button type="button" id="cancel-custom-product" class="btn btn-sm btn-ghost">
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Amount and Quantity -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="label" for="payout_amount">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-dollar-sign text-secondary mr-2"></i>
                                        Amount per Unit <span class="text-error font-bold">*</span>
                                    </span>
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                        <span class="text-base-content/60">$</span>
                                    </div>
                                    <input type="number" name="payout_amount" id="payout_amount" step="0.01" min="0.01"
                                        class="input input-bordered w-full pl-7 @error('payout_amount') input-error @enderror"
                                        value="{{ old('payout_amount') }}" placeholder="0.00" required>
                                </div>
                                @error('payout_amount')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>

                            <div>
                                <label class="label" for="quantity">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-hashtag text-secondary mr-2"></i>
                                        Quantity <span class="text-error font-bold">*</span>
                                    </span>
                                </label>
                                <input type="number" name="quantity" id="quantity" min="1" step="1"
                                    class="input input-bordered w-full @error('quantity') input-error @enderror"
                                    value="{{ old('quantity', 1) }}" placeholder="1" required>
                                @error('quantity')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>
                        </div>

                        <!-- Department and Tax Policy -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="label" for="department_id">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-building text-secondary mr-2"></i>
                                        Department <span class="text-error font-bold">*</span>
                                    </span>
                                </label>
                                <select name="department_id" id="department_id"
                                    class="select select-bordered w-full @error('department_id') select-error @enderror" required>
                                    <option value="">-- Select Department --</option>
                                    @foreach ($departments as $department)
                                        <option value="{{ $department->id }}" {{ old('department_id') == $department->id ? 'selected' : '' }}>
                                            {{ $department->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('department_id')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>

                            <div>
                                <label class="label" for="tax_policy_id">
                                    <span class="label-text">
                                        <i class="fa-sharp fa-percent text-secondary mr-2"></i>
                                        Tax Policy <span class="text-error font-bold">*</span>
                                    </span>
                                </label>
                                <select name="tax_policy_id" id="tax_policy_id"
                                    class="select select-bordered w-full @error('tax_policy_id') select-error @enderror" required>
                                    <option value="{{ $defaultTaxPolicy->id ?? '' }}">
                                        {{ $defaultTaxPolicy->name ?? 'Non-Taxable' }} ({{ $defaultTaxPolicy->rate ?? '0' }}%)
                                    </option>
                                </select>
                                <div class="label">
                                    <span class="label-text-alt">Typically payouts are non-taxable</span>
                                </div>
                                @error('tax_policy_id')
                                <div class="label">
                                    <span class="label-text-alt text-error flex items-center gap-1">
                                        <i class="fa-sharp fa-exclamation-triangle"></i>
                                        {{ $message }}
                                    </span>
                                </div>
                                @enderror
                            </div>
                        </div>

                        <!-- Description -->
                        <div>
                            <label class="label" for="description">
                                <span class="label-text">
                                    <i class="fa-sharp fa-note-sticky text-secondary mr-2"></i>
                                    Description / Notes
                                </span>
                            </label>
                            <textarea name="description" id="description" rows="3"
                                class="w-full textarea textarea-bordered @error('description') textarea-error @enderror"
                                placeholder="Optional description of item condition, serial numbers, or other notes">{{ old('description') }}</textarea>
                            @error('description')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Item Photos Section -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-info text-info-content rounded-full w-10 h-10">
                                    <i class="fa-sharp fa-image text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Item Photos</h4>
                        </div>
                    </div>
                    <div class="p-6 space-y-6">

                        <div>
                            <label class="label" for="item_photos">
                                <span class="label-text">
                                    <i class="fa-sharp fa-camera text-info mr-2"></i>
                                    Upload Item Photos
                                </span>
                            </label>
                            <input type="file" name="item_photos[]" id="item_photos" accept=".jpg,.jpeg,.png"
                                class="file-input file-input-bordered w-full @error('item_photos.*') file-input-error @enderror" multiple>
                            <div class="label">
                                <span class="label-text-alt">
                                    <span class="font-medium">Recommended.</span>
                                    You can select multiple photos. Accepted formats: JPG, PNG. Max 10MB each.
                                </span>
                            </div>
                            @error('item_photos.*')
                            <div class="label">
                                <span class="label-text-alt text-error flex items-center gap-1">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    {{ $message }}
                                </span>
                            </div>
                            @enderror
                        </div>

                        <div id="photo_previews" class="grid grid-cols-2 md:grid-cols-4 gap-2">
                            <!-- Photo previews will be inserted here -->
                        </div>
                    </div>
                </div>

                <!-- Summary Section -->
                <div class="card bg-base-100 shadow-md">
                    <div class="bg-gradient-to-r from-accent/10 to-accent/5 px-6 py-4 border-b border-base-300/50">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-accent text-accent-content rounded-full w-10 h-10">
                                    <i class="fa-sharp fa-receipt text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Payout Summary</h4>
                        </div>
                    </div>
                    <div class="p-6">

                        <div class="flex justify-between items-center text-lg font-medium">
                            <span class="text-base-content">Total To Pay:</span>
                            <span class="text-xl text-error" id="total_payout_amount">$0.00</span>
                        </div>

                        <p class="text-sm text-base-content/60 mt-2 italic">
                            This will be recorded as a negative invoice in your system.
                        </p>
                    </div>
                </div>

                <div class="divider my-8">
                    <span class="text-base-content/50 font-medium">Ready to create payout?</span>
                </div>

                <div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg p-6">
                    <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-success text-success-content rounded-full w-12 h-12">
                                    <i class="fa-sharp fa-money-bill-transfer text-lg"></i>
                                </div>
                            </div>
                            <div>
                                <h3 class="font-semibold text-base-content">Create Customer Payout</h3>
                                <p class="text-sm text-base-content/70">This will generate a negative invoice for the customer</p>
                            </div>
                        </div>
                        <div class="flex gap-3 w-full sm:w-auto">

                            <button type="submit" class="btn btn-success btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                                <i class="fa-sharp fa-dollar-sign"></i>
                                Create Payout
                            </button>
                        </div>
                    </div>
                </div>
            </form>
            </div>
        </div>
    </div>

    <!-- Add JavaScript for the form functionality -->
    @push('scripts')
    <script>
        // Global function to handle customer selection from dynamic search
        window.handleCustomerSelection = function(customer) {
            checkCustomerLicense(customer.id);
        };

        // Function to check if customer has license on file
        function checkCustomerLicense(customerId) {
            if (!customerId) return;

            fetch(`/customers/${customerId}/license-status`)
                .then(response => response.json())
                .then(data => {
                    updatePhotoIdRequirement(data.hasLicense, data.licenseFiles || []);
                })
                .catch(error => {
                    console.error('Error checking license status:', error);
                    // Default to required if there's an error
                    updatePhotoIdRequirement(false, []);
                });
        }

        // Function to update photo ID requirement based on license status
        function updatePhotoIdRequirement(hasLicense, licenseFiles) {
            const photoIdInput = document.getElementById('photo_id');
            const photoIdLabel = document.querySelector('label[for="photo_id"] .label-text');
            const photoIdRequiredText = photoIdLabel?.querySelector('#photo-id-required-text');
            const photoIdRequirement = document.getElementById('photo-id-requirement');
            const existingLicenseNotice = document.getElementById('existing-license-notice');

            if (hasLicense) {
                // Customer has license on file - make photo ID optional
                photoIdInput.removeAttribute('required');
                if (photoIdLabel) {
                    photoIdLabel.innerHTML = '<i class="fa-sharp fa-id-card text-primary mr-2"></i>Photo ID <span class="text-base-content/70">(Optional)</span>';
                }
                if (photoIdRequirement) {
                    photoIdRequirement.innerHTML = '<span class="font-bold">NOTE:</span> Customer has photo ID on file. Uploading a new photo ID is optional.';
                }
                existingLicenseNotice.classList.remove('hidden');

                // Show existing license files if any
                if (licenseFiles.length > 0) {
                    showExistingLicenseFiles(licenseFiles);
                }
            } else {
                // Customer doesn't have license on file - keep photo ID required
                photoIdInput.setAttribute('required', 'required');
                if (photoIdLabel) {
                    photoIdLabel.innerHTML = '<i class="fa-sharp fa-id-card text-primary mr-2"></i>Photo ID <span class="text-error font-bold">*</span>';
                }
                if (photoIdRequirement) {
                    photoIdRequirement.innerHTML = '<span class="font-bold">IMPORTANT:</span> A photo ID is required for all payouts for legal compliance.';
                }
                existingLicenseNotice.classList.add('hidden');

                // Hide existing license files display
                hideExistingLicenseFiles();
            }
        }

        // Function to show existing license files with thumbnails
        function showExistingLicenseFiles(licenseFiles) {
            const existingLicenseNotice = document.getElementById('existing-license-notice');

            if (licenseFiles.length > 0) {
                let filesHtml = '<div class="mt-3"><span class="text-sm font-medium text-base-content mb-2 block">Existing photo IDs on file:</span>';

                // Create thumbnail gallery using only DaisyUI and Tailwind classes
                filesHtml += '<div class="grid grid-cols-2 md:grid-cols-4 gap-2">';

                licenseFiles.forEach(file => {
                    if (file.is_image) {
                        // For images, show thumbnail with lightbox (using div instead of a)
                        filesHtml += `
                            <div class="license-lightbox block relative group cursor-pointer overflow-hidden rounded-lg border border-base-300 hover:border-primary transition-all duration-200 hover:scale-105 hover:shadow-lg"
                                 data-src="${file.full_url}"
                                 data-filename="${file.original_filename}">
                                <div class="w-full h-20 overflow-hidden">
                                    <img src="${file.thumbnail_url}"
                                         alt="${file.original_filename}"
                                         class="w-full h-full object-cover">
                                </div>
                                <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200 rounded-lg flex items-center justify-center">
                                    <i class="fa-sharp fa-search-plus text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"></i>
                                </div>
                                <div class="absolute bottom-0 left-0 right-0 bg-black/70 text-white text-xs p-1 rounded-b-lg truncate">
                                    ${file.original_filename}
                                </div>
                            </div>
                        `;
                    } else {
                        // For PDFs, show icon with download link (keep as link for PDFs)
                        filesHtml += `
                            <a href="${file.full_url}" target="_blank" class="block relative group">
                                <div class="w-full h-20 bg-base-200 rounded-lg border border-base-300 hover:border-primary transition-all duration-200 flex flex-col items-center justify-center cursor-pointer hover:shadow-lg">
                                    <i class="fa-sharp fa-file-pdf text-error text-2xl mb-1"></i>
                                    <span class="text-xs text-center text-base-content/80 px-1 truncate w-full">${file.original_filename}</span>
                                </div>
                            </a>
                        `;
                    }
                });

                filesHtml += '</div></div>';

                existingLicenseNotice.innerHTML = `
                    <i class="fa-sharp fa-check-circle text-success mr-1"></i>
                    <span class="text-success font-medium">Customer has photo ID on file.</span>
                    <span class="text-base-content/70">Uploading a new photo ID is optional.</span>
                    ${filesHtml}
                `;

                // Initialize lightbox for the new thumbnails
                initializeLightbox();

                // Add manual click handlers as fallback
                addManualClickHandlers();
            }
        }

        // Function to hide existing license files display
        function hideExistingLicenseFiles() {
            const existingLicenseNotice = document.getElementById('existing-license-notice');
            existingLicenseNotice.innerHTML = `
                <i class="fa-sharp fa-check-circle text-success mr-1"></i>
                <span class="text-success font-medium">Customer has photo ID on file.</span>
                <span class="text-base-content/70">Uploading a new photo ID is optional.</span>
            `;
        }

        // Function to initialize SimpleLightbox
        function initializeLightbox() {
            // Destroy existing lightbox instance if it exists
            if (window.licenseLightbox) {
                window.licenseLightbox.destroy();
            }

            // Wait a bit for DOM to be ready, then initialize
            setTimeout(() => {
                const lightboxElements = document.querySelectorAll('.license-lightbox');
                console.log('Initializing lightbox for', lightboxElements.length, 'elements');

                if (lightboxElements.length > 0) {
                    // Since we're using divs instead of links, we need to handle clicks manually
                    // and create a simple lightbox experience
                    lightboxElements.forEach(element => {
                        element.addEventListener('click', function() {
                            const imageUrl = this.getAttribute('data-src');
                            const filename = this.getAttribute('data-filename');

                            console.log('Opening lightbox for:', filename, imageUrl);

                            // Create a simple lightbox overlay
                            createSimpleLightbox(imageUrl, filename);
                        });
                    });

                    console.log('Manual lightbox handlers added successfully');
                } else {
                    console.log('No lightbox elements found');
                }
            }, 100);
        }

        // Function to create a simple custom lightbox
        function createSimpleLightbox(imageUrl, filename) {
            // Remove any existing lightbox
            const existingLightbox = document.getElementById('custom-lightbox');
            if (existingLightbox) {
                existingLightbox.remove();
            }

            // Create lightbox overlay
            const lightboxOverlay = document.createElement('div');
            lightboxOverlay.id = 'custom-lightbox';
            lightboxOverlay.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4';
            lightboxOverlay.style.zIndex = '9999';

            // Create lightbox content
            lightboxOverlay.innerHTML = `
                <div class="relative max-w-4xl max-h-full">
                    <button class="absolute -top-10 right-0 text-white text-2xl hover:text-gray-300 z-10" onclick="this.closest('#custom-lightbox').remove()">
                        <i class="fa-sharp fa-times"></i>
                    </button>
                    <img src="${imageUrl}" alt="${filename}" class="max-w-full max-h-full object-contain rounded-lg shadow-lg">
                    <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-center p-2 rounded-b-lg">
                        ${filename}
                    </div>
                </div>
            `;

            // Close on overlay click
            lightboxOverlay.addEventListener('click', function(e) {
                if (e.target === lightboxOverlay) {
                    lightboxOverlay.remove();
                }
            });

            // Close on escape key
            const escapeHandler = function(e) {
                if (e.key === 'Escape') {
                    lightboxOverlay.remove();
                    document.removeEventListener('keydown', escapeHandler);
                }
            };
            document.addEventListener('keydown', escapeHandler);

            // Add to page
            document.body.appendChild(lightboxOverlay);
        }

        // Function to add manual click handlers as fallback (simplified since we're not using SimpleLightbox)
        function addManualClickHandlers() {
            // This function is now redundant since we handle clicks directly in initializeLightbox
            console.log('Manual click handlers are now handled directly in initializeLightbox');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Using custom lightbox implementation instead of SimpleLightbox
            console.log('Using custom lightbox implementation');

            // Product button selection
            const productButtons = document.querySelectorAll('.product-button');
            const productTypeInput = document.getElementById('product_type');
            const customProductFields = document.getElementById('custom_product_fields');
            const customProductLink = document.getElementById('custom-product-link');
            const cancelCustomButton = document.getElementById('cancel-custom-product');
            const payoutAmountInput = document.getElementById('payout_amount');
            const quantityLabel = document.querySelector('label[for="quantity"] .label-text');
            const quantityTooltip = null; // Remove tooltip functionality since we're using the new label format

            // Initialize pre-selected button based on hidden input value
            const initialProductId = productTypeInput.value;
            if (initialProductId && initialProductId !== 'custom') {
                const initialButton = document.querySelector(`.product-button[data-id="${initialProductId}"]`);
                if (initialButton) {
                    selectProductButton(initialButton);
                }
            } else if (initialProductId === 'custom') {
                customProductFields.classList.remove('hidden');
            }

            // Handle product button clicks
            productButtons.forEach(button => {
                button.addEventListener('click', function() {
                    selectProductButton(this);
                    customProductFields.classList.add('hidden');
                    updateProductFields(this);
                });
            });

            // Handle custom product link click
            customProductLink.addEventListener('click', function() {
                // Deselect all product buttons
                productButtons.forEach(btn => {
                    btn.classList.remove('btn-primary');
                    btn.classList.add('btn-outline');
                });

                // Set hidden input to custom
                productTypeInput.value = 'custom';

                // Show custom product fields
                customProductFields.classList.remove('hidden');

                // Reset fields to default
                payoutAmountInput.value = '';
                if (quantityLabel) {
                    quantityLabel.innerHTML = '<i class="fa-sharp fa-hashtag text-secondary mr-2"></i>Quantity <span class="text-error font-bold">*</span>';
                }
            });

            // Handle cancel custom product click
            cancelCustomButton.addEventListener('click', function() {
                customProductFields.classList.add('hidden');
                productTypeInput.value = '';
            });

            function selectProductButton(button) {
                // Deselect all buttons first
                productButtons.forEach(btn => {
                    btn.classList.remove('btn-primary');
                    btn.classList.add('btn-outline');
                });

                // Select the clicked button
                button.classList.remove('btn-outline');
                button.classList.add('btn-primary');

                // Update hidden input with selected product ID
                productTypeInput.value = button.getAttribute('data-id');
            }

            function updateProductFields(button) {
                // Get data from button attributes
                const suggestedPrice = button.getAttribute('data-suggested-price');
                const unit = button.getAttribute('data-unit');
                const taxPolicyId = button.getAttribute('data-tax-policy-id');
                const departmentId = button.getAttribute('data-department-id');

                // Update the price field
                if (suggestedPrice && !payoutAmountInput.value) {
                    payoutAmountInput.value = parseFloat(suggestedPrice).toFixed(2);
                }

                // Update the quantity label with the unit
                if (unit && quantityLabel) {
                    quantityLabel.innerHTML = `<i class="fa-sharp fa-hashtag text-secondary mr-2"></i>Quantity <span class="text-gray-500 text-xs">(${unit})</span> <span class="text-error font-bold">*</span>`;
                } else if (quantityLabel) {
                    quantityLabel.innerHTML = '<i class="fa-sharp fa-hashtag text-secondary mr-2"></i>Quantity <span class="text-error font-bold">*</span>';
                }

                // Update the department if available
                if (departmentId) {
                    const departmentSelect = document.getElementById('department_id');
                    departmentSelect.value = departmentId;
                }

                // Update the tax policy if available
                if (taxPolicyId) {
                    const taxPolicySelect = document.getElementById('tax_policy_id');
                    taxPolicySelect.value = taxPolicyId;
                }

                // Update the total after changing values
                updateTotalAmount();
            }

            // Calculate total amount
            function updateTotalAmount() {
                const amount = parseFloat(document.getElementById('payout_amount').value) || 0;
                const quantity = parseInt(document.getElementById('quantity').value) || 0;
                const total = amount * quantity;

                document.getElementById('total_payout_amount').textContent = '$' + total.toFixed(2);
            }

            // Add event listeners for amount calculations
            document.getElementById('payout_amount').addEventListener('input', updateTotalAmount);
            document.getElementById('quantity').addEventListener('input', updateTotalAmount);

            // Initialize the total
            updateTotalAmount();

            // Handle photo previews
            const photoInput = document.getElementById('item_photos');
            const previewsContainer = document.getElementById('photo_previews');

            // Remove the existing change event listener if any
            const newPhotoInput = photoInput.cloneNode(true);
            photoInput.parentNode.replaceChild(newPhotoInput, photoInput);

            // Redefine photoInput with the new element
            const updatedPhotoInput = document.getElementById('item_photos');

            // Add the event listener to the new element
            updatedPhotoInput.addEventListener('change', function(event) {
                // Clear existing previews
                previewsContainer.innerHTML = '';

                // Track added files to prevent duplicates
                const addedFiles = new Set();

                if (this.files && this.files.length > 0) {
                    for (let i = 0; i < this.files.length; i++) {
                        const file = this.files[i];

                        // Create a unique key for each file
                        const fileKey = `${file.name}-${file.size}-${file.lastModified}`;

                        // Skip if we've already processed this file
                        if (addedFiles.has(fileKey)) {
                            continue;
                        }

                        // Mark as processed
                        addedFiles.add(fileKey);

                        // Only process images
                        if (file.type.startsWith('image/')) {
                            const reader = new FileReader();

                            reader.onload = function(e) {
                                const preview = document.createElement('div');
                                preview.className = 'relative';
                                preview.innerHTML = `
                                    <img src="${e.target.result}" class="w-full h-24 object-cover rounded border border-gray-300">
                                    <span class="absolute bottom-0 left-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-br rounded-tl">
                                        ${file.name.substring(0, 15)}${file.name.length > 15 ? '...' : ''}
                                    </span>
                                `;
                                previewsContainer.appendChild(preview);
                            };

                            // Read the file as a data URL
                            reader.readAsDataURL(file);
                        }
                    }
                }
            });

            // Handle ID photo preview
            const idPhotoInput = document.getElementById('photo_id');
            const idPreviewContainer = document.getElementById('id_preview_container');
            const idPreview = document.getElementById('id_preview');

            // Remove existing event listener to prevent duplicates
            const newIdPhotoInput = idPhotoInput.cloneNode(true);
            idPhotoInput.parentNode.replaceChild(newIdPhotoInput, idPhotoInput);

            // Redefine the variable with the new element
            const updatedIdPhotoInput = document.getElementById('photo_id');

            // Add new event listener
            updatedIdPhotoInput.addEventListener('change', function(event) {
                // Clear existing preview
                idPreviewContainer.innerHTML = '';
                idPreview.classList.add('hidden');

                if (this.files && this.files.length > 0) {
                    const file = this.files[0];

                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            const preview = document.createElement('div');
                            preview.className = 'relative';
                            preview.innerHTML = `
                                <img src="${e.target.result}" class="w-full h-36 object-cover rounded border border-gray-300">
                                <span class="absolute bottom-0 left-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-br rounded-tl">
                                    ${file.name.substring(0, 15)}${file.name.length > 15 ? '...' : ''}
                                </span>
                            `;
                            idPreviewContainer.appendChild(preview);
                            idPreview.classList.remove('hidden');
                        };

                        // Read the file as a data URL
                        reader.readAsDataURL(file);
                    } else if (file.type === 'application/pdf') {
                        // For PDF files show an icon instead
                        const preview = document.createElement('div');
                        preview.className = 'relative flex items-center justify-center';
                        preview.innerHTML = `
                            <div class="bg-gray-100 border border-gray-300 rounded p-4 text-center w-full">
                                <i class="fa-sharp fa-file-pdf text-red-500 text-4xl mb-2"></i>
                                <div class="text-sm text-gray-700 font-medium">
                                    PDF Document<br>
                                    ${file.name.substring(0, 20)}${file.name.length > 20 ? '...' : ''}
                                </div>
                            </div>
                        `;
                        idPreviewContainer.appendChild(preview);
                        idPreview.classList.remove('hidden');
                    }
                }
            });
        });
    </script>
    @endpush
</x-app-layout>