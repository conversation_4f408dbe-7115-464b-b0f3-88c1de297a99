<x-app-layout :title="'Preview Email Template'">
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Preview Email Template
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
    <div class="card bg-base-100 shadow-xl mb-6">
        <div class="card-body">
            <h2 class="card-title text-2xl">
                {{ $emailTemplate->display_name }}
                <span class="badge {{ $emailTemplate->is_active ? 'badge-success' : 'badge-error' }} ml-2">
                    {{ $emailTemplate->is_active ? 'Active' : 'Inactive' }}
                </span>
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                    <p class="text-sm text-gray-600">Subject:</p>
                    <p class="font-medium">{{ $rendered['subject'] }}</p>
                </div>
                @if($rendered['from_name'] || $rendered['from_email'])
                <div>
                    <p class="text-sm text-gray-600">From:</p>
                    <p class="font-medium">
                        {{ $rendered['from_name'] ?: config('mail.from.name') }} 
                        &lt;{{ $rendered['from_email'] ?: config('mail.from.address') }}&gt;
                    </p>
                </div>
                @endif
            </div>

            <div class="flex gap-2 mt-4">
                <a href="{{ route('admin.email-templates.edit', $emailTemplate) }}" 
                   class="btn btn-primary btn-sm">
                    Edit Template
                </a>
                <a href="{{ route('admin.email-templates.index') }}" 
                   class="btn btn-ghost btn-sm">
                    Back to List
                </a>
            </div>
        </div>
    </div>

    {{-- Email Preview --}}
    <div class="card bg-base-100 shadow-md">
        <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
            <div class="flex items-center gap-3">
                <div class="avatar avatar-placeholder">
                    <div class="bg-primary text-primary-content w-8 rounded-lg">
                        <i class="fa-sharp fa-eye text-sm"></i>
                    </div>
                </div>
                <h4 class="text-lg font-semibold text-base-content">Email Preview</h4>
            </div>
        </div>
        <div class="p-6 space-y-6">
            {{-- Email Content Preview --}}
            <div class="bg-white border border-base-300 rounded-lg p-6 shadow-inner" style="max-height: 600px; overflow-y: auto;">
                {!! $rendered['body'] !!}
            </div>
            
            {{-- Show HTML Source Toggle --}}
            <div class="collapse collapse-arrow border border-base-300 bg-base-200">
                <input type="checkbox" /> 
                <div class="collapse-title text-lg font-medium">
                    <i class="fa-sharp fa-code mr-2"></i>
                    View HTML Source
                </div>
                <div class="collapse-content"> 
                    <div class="mockup-code">
                        <pre class="text-xs overflow-x-auto"><code>{{ $rendered['body'] }}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Sample Data Used --}}
    <div class="card bg-base-200 mt-6">
        <div class="card-body">
            <h3 class="card-title text-lg mb-4">Sample Data Used for Preview</h3>
            <div class="overflow-x-auto">
                <table class="table table-compact w-full">
                    <thead>
                        <tr>
                            <th>Variable</th>
                            <th>Sample Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($sampleData as $key => $value)
                        <tr>
                            <td><code class="text-sm">{{{ $key }}}</code></td>
                            <td>{{ $value }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <p class="text-sm text-gray-600 mt-4">
                Note: This preview uses sample data. Actual emails will use real data from the system.
            </p>
        </div>
        </div>
    </div>

</x-app-layout>