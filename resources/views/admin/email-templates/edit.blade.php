<x-app-layout :title="'Edit Email Template'">
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Edit Email Template
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
    <form action="{{ route('admin.email-templates.update', $emailTemplate) }}" method="POST">
        @csrf
        @method('PUT')
        
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title text-2xl mb-4">
                    Edit: {{ $emailTemplate->display_name }}
                </h2>
                <p class="text-gray-600 mb-6">
                    Template Code: <code class="bg-base-200 px-2 py-1 rounded">{{ $emailTemplate->name }}</code>
                </p>

                {{-- Display Name --}}
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">Display Name</span>
                    </label>
                    <input type="text" 
                           name="display_name" 
                           value="{{ old('display_name', $emailTemplate->display_name) }}"
                           class="input input-bordered @error('display_name') input-error @enderror" 
                           required>
                    @error('display_name')
                        <label class="label">
                            <span class="label-text-alt text-error">{{ $message }}</span>
                        </label>
                    @enderror
                </div>

                {{-- Description --}}
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">Description</span>
                    </label>
                    <input type="text" 
                           name="description" 
                           value="{{ old('description', $emailTemplate->description) }}"
                           class="input input-bordered @error('description') input-error @enderror">
                    @error('description')
                        <label class="label">
                            <span class="label-text-alt text-error">{{ $message }}</span>
                        </label>
                    @enderror
                </div>

                {{-- Subject --}}
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">Email Subject</span>
                        <span class="label-text-alt">You can use variables like {app_name}</span>
                    </label>
                    <input type="text" 
                           name="subject" 
                           value="{{ old('subject', $emailTemplate->subject) }}"
                           class="input input-bordered @error('subject') input-error @enderror" 
                           required>
                    @error('subject')
                        <label class="label">
                            <span class="label-text-alt text-error">{{ $message }}</span>
                        </label>
                    @enderror
                </div>

                {{-- From Name --}}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">From Name (Optional)</span>
                        </label>
                        <input type="text" 
                               name="from_name" 
                               value="{{ old('from_name', $emailTemplate->from_name) }}"
                               placeholder="Leave blank to use default"
                               class="input input-bordered @error('from_name') input-error @enderror">
                        @error('from_name')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>

                    {{-- From Email --}}
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">From Email (Optional)</span>
                        </label>
                        <input type="email" 
                               name="from_email" 
                               value="{{ old('from_email', $emailTemplate->from_email) }}"
                               placeholder="Leave blank to use default"
                               class="input input-bordered @error('from_email') input-error @enderror">
                        @error('from_email')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>
                </div>

                {{-- Body HTML --}}
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">Email Body (HTML)</span>
                        <span class="label-text-alt">Monaco Editor with HTML syntax highlighting</span>
                    </label>
                    <textarea name="body_html" 
                              rows="20" 
                              class="textarea textarea-bordered font-mono text-sm @error('body_html') textarea-error @enderror" 
                              required>{{ old('body_html', $emailTemplate->body_html) }}</textarea>
                    @error('body_html')
                        <label class="label">
                            <span class="label-text-alt text-error">{{ $message }}</span>
                        </label>
                    @enderror
                </div>

                {{-- Active Status --}}
                <div class="form-control mb-6">
                    <label class="label cursor-pointer justify-start">
                        <input type="checkbox" 
                               name="is_active" 
                               value="1" 
                               class="checkbox checkbox-primary mr-2"
                               {{ old('is_active', $emailTemplate->is_active) ? 'checked' : '' }}>
                        <span class="label-text">Template is active</span>
                    </label>
                </div>

                {{-- Action Buttons --}}
                <div class="flex justify-between">
                    <a href="{{ route('admin.email-templates.index') }}" class="btn btn-ghost">
                        Cancel
                    </a>
                    <div class="space-x-2">
                        <a href="{{ route('admin.email-templates.preview', $emailTemplate) }}" 
                           class="btn btn-info"
                           target="_blank">
                            Preview
                        </a>
                        <button type="submit" class="btn btn-primary">
                            Update Template
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>

    {{-- Available Variables Reference --}}
    @if($definition && isset($definition['variables']))
    <div class="card bg-base-200 mt-6">
        <div class="card-body">
            <h3 class="card-title text-lg mb-4">Available Variables</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                @foreach($definition['variables'] as $var => $desc)
                <div class="flex">
                    <code class="bg-base-100 px-2 py-1 rounded mr-2 text-sm">{{ $var }}</code>
                    <span class="text-sm text-gray-600">{{ $desc }}</span>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif
        </div>
    </div>

    @vite(['resources/js/monaco-email-editor.js'])
</x-app-layout>