<x-app-layout :title="'Email Templates'">
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Email Templates
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
    {{-- Missing Templates Alert --}}
    @if(count($missingTemplates) > 0)
    <div class="alert alert-warning mb-6">
        <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
        </svg>
        <span class="font-medium">Missing Templates:</span>
        <span>The following templates are defined but not created:</span>
        <div class="mt-2">
            @foreach($missingTemplates as $missing)
                <a href="{{ route('admin.email-templates.create', ['template' => $missing]) }}" 
                   class="inline-block mr-2 mb-2 btn btn-sm btn-primary">
                    Create {{ $definitions[$missing]['display_name'] }}
                </a>
            @endforeach
        </div>
    </div>
    @endif

    {{-- Templates Table --}}
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <div class="flex justify-between items-center mb-4">
                <h2 class="card-title text-2xl">Email Templates</h2>
            </div>

            @if($templates->isEmpty() && count($missingTemplates) === 0)
                <div class="text-center py-8">
                    <p class="text-gray-500">No email templates found.</p>
                </div>
            @else
                <div class="overflow-x-auto">
                    <table class="table table-zebra">
                        <thead>
                            <tr>
                                <th>Template Name</th>
                                <th>Subject</th>
                                <th>Status</th>
                                <th class="text-center">Last Updated</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($templates as $template)
                            <tr>
                                <td>
                                    <div class="font-medium">{{ $template->display_name }}</div>
                                    <div class="text-sm text-gray-500">{{ $template->description }}</div>
                                </td>
                                <td>{{ $template->subject }}</td>
                                <td>
                                    <span class="badge {{ $template->is_active ? 'badge-success' : 'badge-error' }}">
                                        {{ $template->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <div class="text-sm">{{ $template->updated_at->format('M j, Y') }}</div>
                                    <div class="text-xs text-gray-500">{{ $template->updated_at->format('g:i A') }}</div>
                                </td>
                                <td>
                                    <div class="flex justify-center gap-2">
                                        <a href="{{ route('admin.email-templates.edit', $template) }}" 
                                           class="btn btn-sm btn-primary">
                                            Edit
                                        </a>
                                        <a href="{{ route('admin.email-templates.preview', $template) }}" 
                                           class="btn btn-sm btn-info"
                                           target="_blank">
                                            Preview
                                        </a>
                                        <form action="{{ route('admin.email-templates.toggle-status', $template) }}" 
                                              method="POST" 
                                              class="inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" 
                                                    class="btn btn-sm {{ $template->is_active ? 'btn-warning' : 'btn-success' }}">
                                                {{ $template->is_active ? 'Disable' : 'Enable' }}
                                            </button>
                                        </form>
                                        <form action="{{ route('admin.email-templates.destroy', $template) }}" 
                                              method="POST" 
                                              class="inline"
                                              onsubmit="return confirm('Are you sure you want to delete this template?');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-error">
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </div>
    </div>

    {{-- Information Card --}}
    <div class="card bg-base-200 mt-6">
        <div class="card-body">
            <h3 class="card-title text-lg mb-2">Available Template Variables</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                @foreach($definitions as $name => $definition)
                <div>
                    <h4 class="font-semibold mb-2">{{ $definition['display_name'] }}</h4>
                    <div class="space-y-1 text-sm">
                        @foreach($definition['variables'] as $var => $desc)
                        <div class="flex">
                            <code class="bg-base-100 px-2 py-1 rounded mr-2">{{ $var }}</code>
                            <span class="text-gray-600">{{ $desc }}</span>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        </div>
    </div>
</x-app-layout>