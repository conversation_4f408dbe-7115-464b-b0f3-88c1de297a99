<x-app-layout :title="'Create Email Template'">
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Create Email Template
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
    <form action="{{ route('admin.email-templates.store') }}" method="POST">
        @csrf
        <input type="hidden" name="name" value="{{ $templateName }}">
        
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title text-2xl mb-4">
                    Create: {{ $definition['display_name'] }}
                </h2>
                <p class="text-gray-600 mb-6">{{ $definition['description'] }}</p>

                {{-- Display Name --}}
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">Display Name</span>
                    </label>
                    <input type="text" 
                           name="display_name" 
                           value="{{ old('display_name', $definition['display_name']) }}"
                           class="input input-bordered @error('display_name') input-error @enderror" 
                           required>
                    @error('display_name')
                        <label class="label">
                            <span class="label-text-alt text-error">{{ $message }}</span>
                        </label>
                    @enderror
                </div>

                {{-- Description --}}
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">Description</span>
                    </label>
                    <input type="text" 
                           name="description" 
                           value="{{ old('description', $definition['description']) }}"
                           class="input input-bordered @error('description') input-error @enderror">
                    @error('description')
                        <label class="label">
                            <span class="label-text-alt text-error">{{ $message }}</span>
                        </label>
                    @enderror
                </div>

                {{-- Subject --}}
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">Email Subject</span>
                        <span class="label-text-alt">You can use variables like {app_name}</span>
                    </label>
                    <input type="text" 
                           name="subject" 
                           value="{{ old('subject') }}"
                           placeholder="e.g., {app_name} - Daily Timeclock Report for {report_date}"
                           class="input input-bordered @error('subject') input-error @enderror" 
                           required>
                    @error('subject')
                        <label class="label">
                            <span class="label-text-alt text-error">{{ $message }}</span>
                        </label>
                    @enderror
                </div>

                {{-- From Name --}}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">From Name (Optional)</span>
                        </label>
                        <input type="text" 
                               name="from_name" 
                               value="{{ old('from_name') }}"
                               placeholder="Leave blank to use default"
                               class="input input-bordered @error('from_name') input-error @enderror">
                        @error('from_name')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>

                    {{-- From Email --}}
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">From Email (Optional)</span>
                        </label>
                        <input type="email" 
                               name="from_email" 
                               value="{{ old('from_email') }}"
                               placeholder="Leave blank to use default"
                               class="input input-bordered @error('from_email') input-error @enderror">
                        @error('from_email')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>
                </div>

                {{-- Body HTML --}}
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">Email Body (HTML)</span>
                    </label>
                    <textarea name="body_html" 
                              rows="20" 
                              class="textarea textarea-bordered font-mono text-sm @error('body_html') textarea-error @enderror" 
                              required>{{ old('body_html', $this->getDefaultTemplate($templateName)) }}</textarea>
                    @error('body_html')
                        <label class="label">
                            <span class="label-text-alt text-error">{{ $message }}</span>
                        </label>
                    @enderror
                </div>

                {{-- Active Status --}}
                <div class="form-control mb-6">
                    <label class="label cursor-pointer justify-start">
                        <input type="checkbox" 
                               name="is_active" 
                               value="1" 
                               class="checkbox checkbox-primary mr-2"
                               {{ old('is_active', true) ? 'checked' : '' }}>
                        <span class="label-text">Template is active</span>
                    </label>
                </div>

                {{-- Action Buttons --}}
                <div class="flex justify-between">
                    <a href="{{ route('admin.email-templates.index') }}" class="btn btn-ghost">
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        Create Template
                    </button>
                </div>
            </div>
        </div>
    </form>

    {{-- Available Variables Reference --}}
    <div class="card bg-base-200 mt-6">
        <div class="card-body">
            <h3 class="card-title text-lg mb-4">Available Variables</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                @foreach($definition['variables'] as $var => $desc)
                <div class="flex">
                    <code class="bg-base-100 px-2 py-1 rounded mr-2 text-sm">{{ $var }}</code>
                    <span class="text-sm text-gray-600">{{ $desc }}</span>
                </div>
                @endforeach
            </div>
        </div>
        </div>
    </div>
</x-app-layout>

@php
function getDefaultTemplate($templateName) {
    switch($templateName) {
        case 'daily_timeclock_report':
            return '<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f4f4f4; padding: 20px; text-align: center; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f4f4f4; font-weight: bold; }
        .total-row { font-weight: bold; background-color: #f9f9f9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{app_name} - Daily Timeclock Report</h1>
            <p>Report Date: {report_date}</p>
        </div>
        
        <p>Here is the daily timeclock summary for {report_date}:</p>
        
        <table>
            <tr>
                <th>Total Employees</th>
                <td>{employee_count}</td>
            </tr>
            <tr>
                <th>Total Hours Worked</th>
                <td>{total_hours_worked} hours</td>
            </tr>
            <tr>
                <th>Total Break Hours</th>
                <td>{total_break_hours} hours</td>
            </tr>
        </table>
        
        {employee_details}
        
        <p style="margin-top: 30px; color: #666; font-size: 14px;">
            This report was automatically generated on {current_date}.
        </p>
    </div>
</body>
</html>';

        case 'pickup_request_notification':
            return '<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #4a90e2; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .info-table { width: 100%; margin: 20px 0; }
        .info-table td { padding: 8px 0; }
        .info-table td:first-child { font-weight: bold; width: 30%; }
        .btn { display: inline-block; padding: 10px 20px; background-color: #4a90e2; color: white; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{app_name}</h1>
            <h2>{notification_type} Pickup Request</h2>
        </div>
        
        <div class="content">
            <p>A {notification_type} pickup request has been submitted:</p>
            
            <table class="info-table">
                <tr>
                    <td>Request ID:</td>
                    <td>#{pickup_id}</td>
                </tr>
                <tr>
                    <td>Status:</td>
                    <td>{status}</td>
                </tr>
                <tr>
                    <td>Contact Name:</td>
                    <td>{contact_name}</td>
                </tr>
                <tr>
                    <td>Business:</td>
                    <td>{business_name}</td>
                </tr>
                <tr>
                    <td>Email:</td>
                    <td>{email}</td>
                </tr>
                <tr>
                    <td>Phone:</td>
                    <td>{phone}</td>
                </tr>
                <tr>
                    <td>Pickup Date:</td>
                    <td>{pickup_date}</td>
                </tr>
                <tr>
                    <td>Address:</td>
                    <td>{pickup_address}</td>
                </tr>
                <tr>
                    <td>Load Size:</td>
                    <td>{load_size}</td>
                </tr>
                <tr>
                    <td>Item Types:</td>
                    <td>{item_types}</td>
                </tr>
                <tr>
                    <td>Driver Instructions:</td>
                    <td>{driver_instructions}</td>
                </tr>
            </table>
            
            <p style="text-align: center; margin-top: 30px;">
                <a href="{view_url}" class="btn">View Pickup Request</a>
            </p>
        </div>
    </div>
</body>
</html>';

        default:
            return '';
    }
}
@endphp