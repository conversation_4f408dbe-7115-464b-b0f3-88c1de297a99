<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            {{ __('APIs & Integrations') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Page Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-base-content">{{ __('APIs & Integrations') }}</h1>
                <p class="text-base-content/70 mt-2">
                    {{ __('Manage external integrations and API connections for ETRFlow2.') }}
                </p>
            </div>

            <!-- Integration Cards Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                
                <!-- Microsoft 365 Integration -->
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <i class="fa-brands fa-microsoft text-4xl text-primary mr-3"></i>
                                <div>
                                    <h2 class="card-title text-lg">Microsoft 365</h2>
                                    <p class="text-sm text-base-content/70">Outlook Calendar Sync</p>
                                </div>
                            </div>
                            @if ($microsoftIntegration && $microsoftIntegration->sync_enabled)
                                <div class="badge badge-success">Connected</div>
                            @else
                                <div class="badge badge-ghost">Not Connected</div>
                            @endif
                        </div>

                        <p class="text-sm text-base-content/70 mb-4">
                            {{ __('Automatically sync pickup events to a shared Microsoft 365 Outlook calendar that all staff can access.') }}
                        </p>

                        @if ($microsoftIntegration && $microsoftIntegration->sync_enabled)
                            <div class="bg-success/10 border border-success/20 rounded-lg p-3 mb-4">
                                <div class="flex items-center text-success text-sm">
                                    <i class="fa-sharp fa-check-circle mr-2"></i>
                                    <span>Syncing to: {{ $microsoftIntegration->calendar_name }}</span>
                                </div>
                                @if ($microsoftIntegration->last_sync_at)
                                    <div class="text-xs text-base-content/60 mt-1">
                                        Last sync: {{ $microsoftIntegration->last_sync_at->format('M j, Y g:i A') }}
                                    </div>
                                @endif
                            </div>
                        @else
                            <div class="bg-warning/10 border border-warning/20 rounded-lg p-3 mb-4">
                                <div class="flex items-center text-warning text-sm">
                                    <i class="fa-sharp fa-exclamation-triangle mr-2"></i>
                                    <span>Calendar integration not set up</span>
                                </div>
                            </div>
                        @endif

                        <div class="card-actions justify-end">
                            <a href="{{ route('admin.microsoft-integration.index') }}" class="btn btn-primary btn-sm">
                                @if ($microsoftIntegration && $microsoftIntegration->sync_enabled)
                                    <i class="fa-sharp fa-cog mr-2"></i>
                                    {{ __('Manage') }}
                                @else
                                    <i class="fa-sharp fa-plus mr-2"></i>
                                    {{ __('Set Up') }}
                                @endif
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Google Maps Integration (Existing) -->
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <i class="fa-brands fa-google text-4xl text-error mr-3"></i>
                                <div>
                                    <h2 class="card-title text-lg">Google Maps</h2>
                                    <p class="text-sm text-base-content/70">Address Autocomplete</p>
                                </div>
                            </div>
                            @if (config('services.google_maps.api_key'))
                                <div class="badge badge-success">Active</div>
                            @else
                                <div class="badge badge-error">Not Configured</div>
                            @endif
                        </div>

                        <p class="text-sm text-base-content/70 mb-4">
                            {{ __('Provides address autocomplete and location services for pickup requests and customer addresses.') }}
                        </p>

                        @if (config('services.google_maps.api_key'))
                            <div class="bg-success/10 border border-success/20 rounded-lg p-3 mb-4">
                                <div class="flex items-center text-success text-sm">
                                    <i class="fa-sharp fa-check-circle mr-2"></i>
                                    <span>API key configured</span>
                                </div>
                            </div>
                        @else
                            <div class="bg-error/10 border border-error/20 rounded-lg p-3 mb-4">
                                <div class="flex items-center text-error text-sm">
                                    <i class="fa-sharp fa-times-circle mr-2"></i>
                                    <span>API key required in .env file</span>
                                </div>
                            </div>
                        @endif

                        <div class="card-actions justify-end">
                            <a href="{{ route('globalconfig.edit') }}" class="btn btn-primary btn-sm">
                                <i class="fa-sharp fa-cog mr-2"></i>
                                {{ __('Configure') }}
                            </a>
                        </div>
                    </div>
                </div>

                <!-- OpenAI Integration (Existing) -->
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <i class="fa-sharp fa-robot text-4xl text-accent mr-3"></i>
                                <div>
                                    <h2 class="card-title text-lg">OpenAI</h2>
                                    <p class="text-sm text-base-content/70">AI Description Generation</p>
                                </div>
                            </div>
                            @if (config('services.openai.api_key'))
                                <div class="badge badge-success">Active</div>
                            @else
                                <div class="badge badge-ghost">Not Configured</div>
                            @endif
                        </div>

                        <p class="text-sm text-base-content/70 mb-4">
                            {{ __('Generates intelligent descriptions for inventory items using AI technology.') }}
                        </p>

                        @if (config('services.openai.api_key'))
                            <div class="bg-success/10 border border-success/20 rounded-lg p-3 mb-4">
                                <div class="flex items-center text-success text-sm">
                                    <i class="fa-sharp fa-check-circle mr-2"></i>
                                    <span>AI description generation enabled</span>
                                </div>
                            </div>
                        @else
                            <div class="bg-warning/10 border border-warning/20 rounded-lg p-3 mb-4">
                                <div class="flex items-center text-warning text-sm">
                                    <i class="fa-sharp fa-exclamation-triangle mr-2"></i>
                                    <span>Optional - API key not configured</span>
                                </div>
                            </div>
                        @endif

                        <div class="card-actions justify-end">
                            <a href="{{ route('globalconfig.edit') }}" class="btn btn-primary btn-sm">
                                <i class="fa-sharp fa-cog mr-2"></i>
                                {{ __('Configure') }}
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Web Push Notifications (Existing) -->
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <i class="fa-sharp fa-bell text-4xl text-info mr-3"></i>
                                <div>
                                    <h2 class="card-title text-lg">Push Notifications</h2>
                                    <p class="text-sm text-base-content/70">Browser Notifications</p>
                                </div>
                            </div>
                            @if (config('services.webpush.public_key'))
                                <div class="badge badge-success">Active</div>
                            @else
                                <div class="badge badge-ghost">Not Configured</div>
                            @endif
                        </div>

                        <p class="text-sm text-base-content/70 mb-4">
                            {{ __('Send real-time browser notifications to users for important updates and alerts.') }}
                        </p>

                        @if (config('services.webpush.public_key'))
                            <div class="bg-success/10 border border-success/20 rounded-lg p-3 mb-4">
                                <div class="flex items-center text-success text-sm">
                                    <i class="fa-sharp fa-check-circle mr-2"></i>
                                    <span>VAPID keys configured</span>
                                </div>
                            </div>
                        @else
                            <div class="bg-warning/10 border border-warning/20 rounded-lg p-3 mb-4">
                                <div class="flex items-center text-warning text-sm">
                                    <i class="fa-sharp fa-exclamation-triangle mr-2"></i>
                                    <span>VAPID keys required</span>
                                </div>
                            </div>
                        @endif

                        <div class="card-actions justify-end">
                            <a href="{{ route('globalconfig.edit') }}" class="btn btn-primary btn-sm">
                                <i class="fa-sharp fa-cog mr-2"></i>
                                {{ __('Configure') }}
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Future Integration Placeholder -->
                <div class="card bg-base-100 shadow-xl opacity-60">
                    <div class="card-body">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <i class="fa-sharp fa-plus-circle text-4xl text-base-content/30 mr-3"></i>
                                <div>
                                    <h2 class="card-title text-lg text-base-content/60">More Integrations</h2>
                                    <p class="text-sm text-base-content/50">Coming Soon</p>
                                </div>
                            </div>
                        </div>

                        <p class="text-sm text-base-content/50 mb-4">
                            {{ __('Additional integrations and API connections will be added in future updates.') }}
                        </p>

                        <div class="bg-base-200 rounded-lg p-3 mb-4">
                            <div class="flex items-center text-base-content/60 text-sm">
                                <i class="fa-sharp fa-lightbulb mr-2"></i>
                                <span>Suggest an integration</span>
                            </div>
                        </div>

                        <div class="card-actions justify-end">
                            <button class="btn btn-ghost btn-sm" disabled>
                                <i class="fa-sharp fa-clock mr-2"></i>
                                {{ __('Coming Soon') }}
                            </button>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Integration Tips -->
            <div class="mt-12">
                <div class="card bg-info/5 border border-info/20">
                    <div class="card-body">
                        <h3 class="card-title text-info mb-4">
                            <i class="fa-sharp fa-lightbulb mr-2"></i>
                            {{ __('Integration Tips') }}
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <h4 class="font-medium mb-2">{{ __('Best Practices') }}</h4>
                                <ul class="space-y-1 text-base-content/70">
                                    <li>• {{ __('Test integrations in a development environment first') }}</li>
                                    <li>• {{ __('Keep API keys secure and rotate them regularly') }}</li>
                                    <li>• {{ __('Monitor integration logs for any issues') }}</li>
                                    <li>• {{ __('Document any custom configurations') }}</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium mb-2">{{ __('Troubleshooting') }}</h4>
                                <ul class="space-y-1 text-base-content/70">
                                    <li>• {{ __('Check Laravel logs for detailed error messages') }}</li>
                                    <li>• {{ __('Verify environment variables are correctly set') }}</li>
                                    <li>• {{ __('Ensure queue workers are running for background jobs') }}</li>
                                    <li>• {{ __('Review documentation for specific integration requirements') }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</x-app-layout>