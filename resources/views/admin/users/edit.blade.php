<x-app-layout :title="'Edit User'">
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Edit User
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-lg p-6">
                <h3 class="text-xl font-semibold mb-4">Edit User: {{ $user->name }}</h3>

                <form action="{{ route('admin.users.update', $user) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <!-- Name -->
                    <div class="mb-4">
                        <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                        <input type="text" id="name" name="name" class="input input-bordered w-full"
                               value="{{ old('name', $user->name) }}" required>
                        @error('name')
                        <p class="text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Email -->
                    <div class="mb-4">
                        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" id="email" name="email" class="input input-bordered w-full"
                               value="{{ old('email', $user->email) }}" required>
                        @error('email')
                        <p class="text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Enabled -->
                    <div class="mb-4">
                        <label for="enabled" class="block text-sm font-medium text-gray-700">Enabled</label>
                        <select id="enabled" name="enabled" class="select select-bordered w-full">
                            <option value="1" {{ $user->enabled ? 'selected' : '' }}>Yes</option>
                            <option value="0" {{ !$user->enabled ? 'selected' : '' }}>No</option>
                        </select>
                        @error('enabled')
                        <p class="text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Password -->
                    <div class="mb-4">
                        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                        <input type="password" id="password" name="password" class="input input-bordered w-full"
                               placeholder="Leave blank to keep current password">
                        @error('password')
                        <p class="text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Confirm Password -->
                    <div class="mb-4">
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                        <input type="password" id="password_confirmation" name="password_confirmation" class="input input-bordered w-full"
                               placeholder="Re-enter password">
                    </div>

                    <!-- User Groups -->
                    <div class="mb-4">
                        <label for="groups" class="block text-sm font-medium text-gray-700">User Groups</label>
                        <select id="groups" name="groups[]" class="select select-bordered w-full" multiple>
                            @foreach ($userGroups as $group)
                                <option value="{{ $group->id }}"
                                        {{ $user->groups->contains($group->id) ? 'selected' : '' }}>
                                    {{ $group->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('groups')
                        <p class="text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Save Button -->
                    <div class="flex justify-end">
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
