<x-app-layout :title="'User Manager'">
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            User Manager
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold">Users</h3>
                    <a href="{{ route('admin.users.create') }}" class="btn btn-primary btn-sm">
                        <i class="fa-sharp fa-solid fa-plus mr-2"></i>
                        Create User
                    </a>
                </div>
                <div class="overflow-x-auto">
                    <table class="table table-zebra w-full">
                        <thead>
                            <tr>
                                @php
                                    $columns = [
                                        'name' => 'Name',
                                        'email' => 'Email',
                                        'enabled' => 'Enabled',
                                        'created_at' => 'Created At',
                                    ];
                                @endphp
                                @foreach ($columns as $key => $label)
                                    <th>
                                        <a href="" class="flex items-center">
                                            {{ $label }}
                                            @if ($sort === $key)
                                                <i class="fa-sharp {{ $order === 'asc' ? 'fa-sort-up' : 'fa-sort-down' }} ml-2"></i>
                                            @else
                                                <i class="fa-sharp fa-sort ml-2"></i>
                                            @endif
                                        </a>
                                    </th>
                                @endforeach
                                <th>User Groups</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($users as $user)
                                <tr>
                                    <td>{{ $user->name }}</td>
                                    <td>{{ $user->email }}</td>
                                    <td>{{ $user->enabled ? 'Yes' : 'No' }}</td>
                                    <td>{{ $user->created_at->format('Y-m-d H:i:s') }}</td>
                                    <td>
                                        @if ($user->groups->isEmpty())
                                            No Groups
                                        @else
                                            {{ $user->groups->pluck('name')->join(', ') }}
                                        @endif
                                    </td>
                                    <td>
                                        <div class="flex gap-1">
                                            <a href="{{ route('admin.users.show', $user) }}" class="btn btn-sm btn-info">
                                                <i class="fa-sharp fa-solid fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-sm btn-primary">
                                                <i class="fa-sharp fa-solid fa-edit"></i>
                                            </a>
                                            @if(\App\Models\GlobalConfig::isTasksEnabled())
                                                <a href="{{ route('user.tasks', $user->id) }}" class="btn btn-sm btn-secondary">
                                                    <i class="fa-sharp fa-solid fa-tasks"></i>
                                                </a>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center">No users found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <x-pagination :paginator="$users" :pagination="$pagination" />
            </div>
        </div>
    </div>
</x-app-layout>
