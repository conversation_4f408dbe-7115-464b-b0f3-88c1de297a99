<x-app-layout :title="'User Details'">
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            User Details
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-semibold">{{ $user->name }}</h3>
                    <div class="flex gap-2">
                        <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary btn-sm">
                            <i class="fa-sharp fa-solid fa-edit mr-2"></i>
                            Edit User
                        </a>
                        <a href="{{ route('admin.users.index') }}" class="btn btn-ghost btn-sm">
                            <i class="fa-sharp fa-solid fa-arrow-left mr-2"></i>
                            Back to Users
                        </a>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Basic Information -->
                    <div class="card bg-base-200 shadow-md">
                        <div class="card-body">
                            <h4 class="card-title flex items-center text-xl mb-4">
                                <i class="fa-sharp fa-solid fa-user-circle text-primary mr-2"></i>
                                Basic Information
                            </h4>
                            
                            <div class="flex items-center mb-6">
                                <div class="avatar mr-4">
                                    <div class="w-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                                        <img src="{{ $user->profilePhotoUrl }}" alt="{{ $user->name }}" />
                                    </div>
                                </div>
                                <div>
                                    <h5 class="text-lg font-semibold">{{ $user->name }}</h5>
                                    <p class="text-gray-600">{{ $user->email }}</p>
                                    <div class="badge {{ $user->enabled ? 'badge-success' : 'badge-error' }} mt-2">
                                        {{ $user->enabled ? 'Enabled' : 'Disabled' }}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <h6 class="font-semibold">User Groups</h6>
                                    <div class="flex flex-wrap gap-2 mt-2">
                                        @forelse($user->groups as $group)
                                            <span class="badge badge-primary">{{ $group->name }}</span>
                                        @empty
                                            <span class="text-gray-500">No groups assigned</span>
                                        @endforelse
                                    </div>
                                </div>
                                
                                <div>
                                    <h6 class="font-semibold">Role</h6>
                                    <p>{{ ucfirst($user->role) }}</p>
                                </div>
                                
                                <div>
                                    <h6 class="font-semibold">Account Created</h6>
                                    <p>{{ $user->created_at->format('F j, Y, g:i a') }}</p>
                                </div>
                                
                                <div>
                                    <h6 class="font-semibold">Last Updated</h6>
                                    <p>{{ $user->updated_at->format('F j, Y, g:i a') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Emergency Contact Information -->
                    <div class="card bg-base-200 shadow-md">
                        <div class="card-body">
                            <h4 class="card-title flex items-center text-xl mb-4">
                                <i class="fa-sharp fa-solid fa-phone-emergency text-error mr-2"></i>
                                Emergency Contact Information (ICE)
                            </h4>
                            
                            @if($user->emergency_contact_name || $user->emergency_contact_phone)
                                <div class="mb-6">
                                    <h5 class="text-lg font-semibold mb-2">Primary Contact</h5>
                                    <div class="grid grid-cols-1 gap-2">
                                        <div>
                                            <span class="font-semibold">Name:</span> 
                                            {{ $user->emergency_contact_name ?: 'Not provided' }}
                                        </div>
                                        <div>
                                            <span class="font-semibold">Relationship:</span> 
                                            {{ $user->emergency_contact_relationship ?: 'Not provided' }}
                                        </div>
                                        <div>
                                            <span class="font-semibold">Phone:</span> 
                                            {{ $user->emergency_contact_phone ?: 'Not provided' }}
                                        </div>
                                    </div>
                                </div>
                            @else
                                <div class="alert alert-warning mb-4">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    <span>No primary emergency contact information provided</span>
                                </div>
                            @endif
                            
                            @if($user->additional_emergency_contacts)
                                <div class="mb-6">
                                    <h5 class="text-lg font-semibold mb-2">Additional Contacts</h5>
                                    <div class="whitespace-pre-line bg-base-100 p-3 rounded-lg">
                                        {{ $user->additional_emergency_contacts }}
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Health Information -->
                <div class="card bg-base-200 shadow-md mt-8">
                    <div class="card-body">
                        <h4 class="card-title flex items-center text-xl mb-4">
                            <i class="fa-sharp fa-solid fa-notes-medical text-secondary mr-2"></i>
                            Health Information
                            <div class="badge badge-ghost ml-2">Optional</div>
                        </h4>
                        
                        @if($user->allergies || $user->medications || $user->medical_conditions || $user->blood_type || $user->additional_health_info)
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                @if($user->allergies)
                                    <div>
                                        <h6 class="font-semibold">Allergies</h6>
                                        <p class="mt-1">{{ $user->allergies }}</p>
                                    </div>
                                @endif
                                
                                @if($user->medications)
                                    <div>
                                        <h6 class="font-semibold">Medications</h6>
                                        <p class="mt-1 whitespace-pre-line">{{ $user->medications }}</p>
                                    </div>
                                @endif
                                
                                @if($user->medical_conditions)
                                    <div>
                                        <h6 class="font-semibold">Medical Conditions</h6>
                                        <p class="mt-1 whitespace-pre-line">{{ $user->medical_conditions }}</p>
                                    </div>
                                @endif
                                
                                @if($user->blood_type)
                                    <div>
                                        <h6 class="font-semibold">Blood Type</h6>
                                        <p class="mt-1">{{ $user->blood_type }}</p>
                                    </div>
                                @endif
                                
                                @if($user->additional_health_info)
                                    <div class="md:col-span-2">
                                        <h6 class="font-semibold">Additional Health Information</h6>
                                        <p class="mt-1 whitespace-pre-line">{{ $user->additional_health_info }}</p>
                                    </div>
                                @endif
                            </div>
                        @else
                            <div class="alert alert-info">
                                <i class="fa-sharp fa-info-circle"></i>
                                <span>No health information provided</span>
                            </div>
                        @endif
                        
                        <div class="mt-4 text-sm text-gray-500">
                            <p>Note: Health information is provided voluntarily by the user and is only intended for emergency situations.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
