<x-app-layout :title="'Create User'">
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Create User
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="card bg-base-100 shadow-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold">Create New User</h3>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline btn-sm">
                        <i class="fa-sharp fa-solid fa-arrow-left mr-2"></i>
                        Back to Users
                    </a>
                </div>

                <form action="{{ route('admin.users.store') }}" method="POST">
                    @csrf

                    <!-- Name -->
                    <div class="mb-4">
                        <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                        <input type="text" id="name" name="name" class="input input-bordered w-full"
                               value="{{ old('name') }}" required>
                        @error('name')
                        <p class="text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Email -->
                    <div class="mb-4">
                        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" id="email" name="email" class="input input-bordered w-full"
                               value="{{ old('email') }}" required>
                        @error('email')
                        <p class="text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Password -->
                    <div class="mb-4">
                        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                        <input type="password" id="password" name="password" class="input input-bordered w-full"
                               required>
                        @error('password')
                        <p class="text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Confirm Password -->
                    <div class="mb-4">
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                        <input type="password" id="password_confirmation" name="password_confirmation" class="input input-bordered w-full"
                               required>
                    </div>

                    <!-- Enabled Status -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Account Status</label>
                        <div class="flex items-center space-x-4">
                            <label class="cursor-pointer flex items-center">
                                <input type="radio" name="enabled" value="1" class="radio radio-primary" checked>
                                <span class="ml-2">Enabled</span>
                            </label>
                            <label class="cursor-pointer flex items-center">
                                <input type="radio" name="enabled" value="0" class="radio radio-primary">
                                <span class="ml-2">Disabled</span>
                            </label>
                        </div>
                        @error('enabled')
                        <p class="text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- User Groups -->
                    <div class="mb-4">
                        <label for="groups" class="block text-sm font-medium text-gray-700">User Groups</label>
                        <select id="groups" name="groups[]" class="select select-bordered w-full" multiple>
                            @foreach ($userGroups as $group)
                                <option value="{{ $group->id }}"
                                        {{ in_array($group->id, old('groups', [])) ? 'selected' : '' }}>
                                    {{ $group->name }}
                                </option>
                            @endforeach
                        </select>
                        <p class="text-xs text-gray-500 mt-1">Hold Ctrl/Cmd to select multiple groups</p>
                        @error('groups')
                        <p class="text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Save Button -->
                    <div class="flex justify-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fa-sharp fa-solid fa-user-plus mr-2"></i>
                            Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
