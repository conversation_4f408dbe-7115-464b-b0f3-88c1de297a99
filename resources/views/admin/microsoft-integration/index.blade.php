<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            {{ __('Microsoft 365 Integration') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            @if (session('success'))
                <div class="alert alert-success mb-6">
                    <i class="fa-sharp fa-check-circle"></i>
                    <span>{{ session('success') }}</span>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-error mb-6">
                    <i class="fa-sharp fa-exclamation-triangle"></i>
                    <span>{{ session('error') }}</span>
                </div>
            @endif

            @if ($sharedIntegration)
                <!-- Shared Calendar Integration Active -->
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h2 class="card-title flex items-center">
                            <i class="fa-brands fa-microsoft text-primary mr-2"></i>
                            {{ __('Shared Calendar Integration') }}
                            <div class="badge badge-success ml-2">Connected</div>
                        </h2>
                        
                        <p class="text-base-content/70 mb-6">
                            {{ __('ETRFlow2 is connected to Microsoft 365 and syncing pickup events to a shared calendar.') }}
                        </p>

                        <!-- Connection Status -->
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                            <div class="stat bg-base-200 rounded-lg">
                                <div class="stat-title">Calendar</div>
                                <div class="stat-value text-lg">{{ $sharedIntegration->calendar_name }}</div>
                                <div class="stat-desc">
                                    {{ $sharedIntegration->microsoft_user_email }}
                                    @if ($sharedIntegration->outlook_calendar_id)
                                        <br><span class="text-xs">ID: {{ \Illuminate\Support\Str::limit($sharedIntegration->outlook_calendar_id, 20) }}</span>
                                    @endif
                                </div>
                            </div>
                            <div class="stat bg-base-200 rounded-lg">
                                <div class="stat-title">Status</div>
                                <div class="stat-value text-lg {{ $syncStatus['is_active'] ? 'text-success' : 'text-error' }}">
                                    {{ $syncStatus['is_active'] ? 'Active' : 'Inactive' }}
                                </div>
                                <div class="stat-desc">
                                    @if ($syncStatus['last_sync_at'])
                                        Last sync: {{ $syncStatus['last_sync_at'] }}
                                    @else
                                        Never synced
                                    @endif
                                </div>
                            </div>
                            <div class="stat bg-base-200 rounded-lg">
                                <div class="stat-title">Token</div>
                                <div class="stat-value text-lg {{ $syncStatus['is_token_expired'] ? 'text-error' : 'text-success' }}">
                                    {{ $syncStatus['is_token_expired'] ? 'Expired' : 'Valid' }}
                                </div>
                                <div class="stat-desc">
                                    Expires: {{ $syncStatus['token_expires_at'] ? \Carbon\Carbon::parse($syncStatus['token_expires_at'])->format('M j, Y') : 'Unknown' }}
                                </div>
                            </div>
                        </div>

                        @if ($syncStatus['error_message'])
                            <div class="alert alert-error mb-6">
                                <i class="fa-sharp fa-exclamation-triangle"></i>
                                <span>{{ $syncStatus['error_message'] }}</span>
                            </div>
                        @endif

                        <!-- Quick Actions -->
                        <div class="flex flex-wrap gap-4 mb-6">
                            <form action="{{ route('admin.microsoft-integration.test') }}" method="POST" class="inline">
                                @csrf
                                <button type="submit" class="btn btn-info">
                                    <i class="fa-sharp fa-diagnostic mr-2"></i>
                                    {{ __('Test Connection') }}
                                </button>
                            </form>

                            <form action="{{ route('admin.microsoft-integration.sync') }}" method="POST" class="inline">
                                @csrf
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa-sharp fa-sync mr-2"></i>
                                    {{ __('Sync Now') }}
                                </button>
                            </form>

                            <a href="{{ route('admin.microsoft-integration.change-calendar') }}" class="btn btn-secondary">
                                <i class="fa-sharp fa-calendar-alt mr-2"></i>
                                {{ __('Change Calendar') }}
                            </a>

                            <form action="{{ route('admin.microsoft-integration.disconnect') }}" method="POST" class="inline" 
                                  onsubmit="return confirm('Are you sure you want to disconnect the shared calendar? This will stop syncing pickup events to Microsoft 365.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-error">
                                    <i class="fa-sharp fa-unlink mr-2"></i>
                                    {{ __('Disconnect') }}
                                </button>
                            </form>
                        </div>

                        <!-- Settings Form -->
                        <div class="divider">{{ __('Sync Settings') }}</div>
                        
                        <form action="{{ route('admin.microsoft-integration.update') }}" method="POST">
                            @csrf
                            @method('PUT')
                            
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div class="space-y-4">
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text">{{ __('Calendar Name') }}</span>
                                        </label>
                                        <input type="text" name="calendar_name" value="{{ $sharedIntegration->calendar_name }}" 
                                               class="input input-bordered" required>
                                    </div>

                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text">{{ __('Event Category') }}</span>
                                        </label>
                                        <input type="text" name="event_category" 
                                               value="{{ $sharedIntegration->getSyncSettingsWithDefaults()['event_category'] ?? 'ETRFlow Pickup' }}" 
                                               class="input input-bordered" placeholder="ETRFlow Pickup">
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    <div class="form-control">
                                        <label class="label cursor-pointer justify-start">
                                            <input type="checkbox" name="sync_enabled" value="1" 
                                                   {{ $sharedIntegration->sync_enabled ? 'checked' : '' }} class="checkbox mr-3">
                                            <span class="label-text">{{ __('Enable automatic synchronization') }}</span>
                                        </label>
                                    </div>

                                    <div class="form-control">
                                        <label class="label cursor-pointer justify-start">
                                            <input type="checkbox" name="sync_pickup_events" value="1" 
                                                   {{ $sharedIntegration->getSyncSettingsWithDefaults()['sync_pickup_events'] ? 'checked' : '' }} class="checkbox mr-3">
                                            <span class="label-text">{{ __('Sync pickup events') }}</span>
                                        </label>
                                    </div>

                                    <div class="form-control">
                                        <label class="label cursor-pointer justify-start">
                                            <input type="checkbox" name="sync_completed_events" value="1" 
                                                   {{ $sharedIntegration->getSyncSettingsWithDefaults()['sync_completed_events'] ? 'checked' : '' }} class="checkbox mr-3">
                                            <span class="label-text">{{ __('Sync completed events') }}</span>
                                        </label>
                                    </div>

                                    <div class="form-control">
                                        <label class="label cursor-pointer justify-start">
                                            <input type="checkbox" name="include_customer_details" value="1" 
                                                   {{ $sharedIntegration->getSyncSettingsWithDefaults()['include_customer_details'] ? 'checked' : '' }} class="checkbox mr-3">
                                            <span class="label-text">{{ __('Include customer details in events') }}</span>
                                        </label>
                                    </div>

                                    <div class="form-control">
                                        <label class="label cursor-pointer justify-start">
                                            <input type="checkbox" name="include_item_details" value="1" 
                                                   {{ $sharedIntegration->getSyncSettingsWithDefaults()['include_item_details'] ? 'checked' : '' }} class="checkbox mr-3">
                                            <span class="label-text">{{ __('Include item details in events') }}</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-end mt-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa-sharp fa-save mr-2"></i>
                                    {{ __('Save Settings') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

            @else
                <!-- No Shared Calendar Integration -->
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body text-center">
                        <div class="mb-6">
                            <i class="fa-brands fa-microsoft text-6xl text-base-content/30 mb-4"></i>
                            <h2 class="card-title justify-center mb-2">{{ __('Microsoft 365 Integration') }}</h2>
                            <p class="text-base-content/70">
                                {{ __('Connect ETRFlow2 to Microsoft 365 to automatically sync pickup events to a shared Outlook calendar.') }}
                            </p>
                        </div>

                        <div class="space-y-4">
                            <a href="{{ route('admin.microsoft-integration.connect') }}" class="btn btn-primary btn-lg">
                                <i class="fa-brands fa-microsoft mr-2"></i>
                                {{ __('Set Up Shared Calendar Integration') }}
                            </a>
                        </div>

                        <!-- Benefits Info -->
                        <div class="bg-info/10 border border-info/20 rounded-lg p-6 mt-8 text-left">
                            <h4 class="font-medium text-info mb-4 flex items-center">
                                <i class="fa-sharp fa-info-circle mr-2"></i>
                                {{ __('Benefits of Microsoft 365 Integration') }}
                            </h4>
                            <ul class="text-sm text-base-content/70 space-y-2 ml-4">
                                <li>• {{ __('Automatically sync pickup events to a shared Outlook calendar') }}</li>
                                <li>• {{ __('All staff can view pickup schedules in their Outlook') }}</li>
                                <li>• {{ __('Rich event details with customer info and locations') }}</li>
                                <li>• {{ __('Real-time synchronization when pickup events change') }}</li>
                                <li>• {{ __('No individual setup required for staff members') }}</li>
                                <li>• {{ __('Read-only integration - ETRFlow remains the source of truth') }}</li>
                            </ul>
                        </div>

                        <!-- Setup Requirements -->
                        <div class="bg-warning/10 border border-warning/20 rounded-lg p-6 mt-6 text-left">
                            <h4 class="font-medium text-warning mb-4 flex items-center">
                                <i class="fa-sharp fa-exclamation-triangle mr-2"></i>
                                {{ __('Setup Requirements') }}
                            </h4>
                            <ul class="text-sm text-base-content/70 space-y-2 ml-4">
                                <li>• {{ __('Microsoft 365 Business or Enterprise account') }}</li>
                                <li>• {{ __('Admin access to Azure Active Directory') }}</li>
                                <li>• {{ __('Environment variables configured (see documentation)') }}</li>
                                <li>• {{ __('Queue system running for background sync jobs') }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            @endif

        </div>
    </div>
</x-app-layout>