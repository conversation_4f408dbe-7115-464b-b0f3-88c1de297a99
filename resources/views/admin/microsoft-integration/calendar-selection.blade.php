<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            {{ __('Select Microsoft 365 Calendar') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fa-brands fa-microsoft text-primary mr-2"></i>
                        {{ __('Choose Your Calendar') }}
                    </h2>
                    
                    <p class="text-base-content/70 mb-6">
                        {{ __('Select which Microsoft 365 calendar you want to use for syncing ETRFlow2 pickup events. All pickup events will be created in the selected calendar.') }}
                    </p>

                    <!-- Connected Account Info -->
                    <div class="bg-success/10 border border-success/20 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <div class="badge badge-success mr-3">
                                <i class="fa-sharp fa-check mr-1"></i>
                                Connected
                            </div>
                            <div>
                                <p class="font-medium">{{ $userInfo['displayName'] ?? 'Unknown User' }}</p>
                                <p class="text-sm text-base-content/70">{{ $userInfo['mail'] ?? $userInfo['userPrincipalName'] ?? 'Unknown Email' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Calendar Selection Form -->
                    <form action="{{ route('admin.microsoft-integration.complete-setup') }}" method="POST">
                        @csrf
                        
                        <div class="space-y-4 mb-6">
                            @if (count($calendars) > 0)
                                @foreach ($calendars as $calendar)
                                    <div class="form-control">
                                        <label class="label cursor-pointer p-4 border border-base-300 rounded-lg hover:bg-base-200 transition-colors">
                                            <div class="flex items-center flex-1">
                                                <input type="radio" name="calendar_id" value="{{ $calendar['id'] }}" 
                                                       class="radio radio-primary mr-4" required
                                                       onchange="updateCalendarName('{{ $calendar['name'] }}')">
                                                <div class="flex-1">
                                                    <div class="flex items-center justify-between">
                                                        <div>
                                                            <p class="font-medium">{{ $calendar['name'] }}</p>
                                                            @if (isset($calendar['owner']['name']))
                                                                <p class="text-sm text-base-content/70">
                                                                    Owner: {{ $calendar['owner']['name'] }}
                                                                </p>
                                                            @endif
                                                            @if (isset($calendar['isDefaultCalendar']) && $calendar['isDefaultCalendar'])
                                                                <div class="badge badge-info badge-sm mt-1">Default Calendar</div>
                                                            @endif
                                                        </div>
                                                        <div class="text-right">
                                                            @if (isset($calendar['canEdit']) && $calendar['canEdit'])
                                                                <div class="badge badge-success badge-sm">Can Edit</div>
                                                            @else
                                                                <div class="badge badge-warning badge-sm">Read Only</div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                    @if (isset($calendar['color']))
                                                        <div class="flex items-center mt-2">
                                                            <div class="w-4 h-4 rounded mr-2" style="background-color: {{ $calendar['color'] }}"></div>
                                                            <span class="text-xs text-base-content/50">Calendar Color</span>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                @endforeach
                            @else
                                <div class="alert alert-warning">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    <span>{{ __('No calendars found. Please make sure you have calendar access permissions.') }}</span>
                                </div>
                            @endif
                        </div>

                        <!-- Hidden field to store selected calendar name -->
                        <input type="hidden" name="calendar_name" id="selected_calendar_name">

                        <!-- Actions -->
                        <div class="flex flex-col sm:flex-row gap-4 justify-between">
                            <a href="{{ route('admin.microsoft-integration.index') }}" class="btn btn-ghost">
                                <i class="fa-sharp fa-arrow-left mr-2"></i>
                                {{ __('Back') }}
                            </a>
                            
                            @if (count($calendars) > 0)
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa-sharp fa-check mr-2"></i>
                                    {{ __('Complete Setup') }}
                                </button>
                            @else
                                <a href="{{ route('admin.microsoft-integration.connect') }}" class="btn btn-primary">
                                    <i class="fa-sharp fa-refresh mr-2"></i>
                                    {{ __('Try Again') }}
                                </a>
                            @endif
                        </div>
                    </form>

                    <!-- Calendar Selection Tips -->
                    <div class="mt-8">
                        <div class="bg-info/10 border border-info/20 rounded-lg p-4">
                            <h4 class="font-medium text-info mb-2">
                                <i class="fa-sharp fa-lightbulb mr-2"></i>
                                {{ __('Calendar Selection Tips') }}
                            </h4>
                            <ul class="text-sm text-base-content/70 space-y-1 ml-4">
                                <li>• {{ __('Choose a calendar that all staff members can access') }}</li>
                                <li>• {{ __('Make sure the selected calendar allows editing permissions') }}</li>
                                <li>• {{ __('Consider creating a dedicated "ETRFlow Pickups" calendar for better organization') }}</li>
                                <li>• {{ __('The calendar owner will receive notifications for event changes') }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    @push('scripts')
    <script>
        function updateCalendarName(name) {
            document.getElementById('selected_calendar_name').value = name;
        }

        // Auto-select first calendar and set its name if only one is available
        document.addEventListener('DOMContentLoaded', function() {
            const radios = document.querySelectorAll('input[name="calendar_id"]');
            if (radios.length === 1) {
                radios[0].checked = true;
                const calendarName = radios[0].closest('label').querySelector('p.font-medium').textContent;
                updateCalendarName(calendarName);
            }
        });
    </script>
    @endpush
</x-app-layout>