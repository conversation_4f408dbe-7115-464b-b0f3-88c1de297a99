<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            {{ __('Set Up Microsoft 365 Integration') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fa-brands fa-microsoft text-primary mr-2"></i>
                        {{ __('Microsoft 365 Shared Calendar Setup') }}
                    </h2>
                    
                    <p class="text-base-content/70 mb-6">
                        {{ __('This will set up a shared Microsoft 365 calendar for all ETRFlow2 pickup events. All staff will be able to view pickup schedules in their Outlook calendars.') }}
                    </p>

                    <!-- Setup Steps -->
                    <div class="bg-base-200 rounded-lg p-6 mb-6">
                        <h3 class="font-medium mb-4">{{ __('Setup Process') }}</h3>
                        <div class="steps steps-vertical lg:steps-horizontal w-full">
                            <div class="step step-primary">{{ __('Azure Configuration') }}</div>
                            <div class="step step-primary">{{ __('Microsoft Sign-In') }}</div>
                            <div class="step">{{ __('Calendar Setup') }}</div>
                            <div class="step">{{ __('Testing & Activation') }}</div>
                        </div>
                        <p class="text-sm text-base-content/70 mt-4">
                            {{ __('You will be redirected to Microsoft to sign in with an admin account.') }}
                        </p>
                    </div>

                    <!-- Requirements Check -->
                    <div class="bg-warning/10 border border-warning/20 rounded-lg p-6 mb-6">
                        <h4 class="font-medium text-warning mb-4 flex items-center">
                            <i class="fa-sharp fa-exclamation-triangle mr-2"></i>
                            {{ __('Before You Continue') }}
                        </h4>
                        <div class="space-y-2 text-sm">
                            <label class="label cursor-pointer justify-start">
                                <input type="checkbox" class="checkbox checkbox-warning mr-3" required>
                                <span class="label-text">{{ __('Azure app registration is configured with correct permissions') }}</span>
                            </label>
                            <label class="label cursor-pointer justify-start">
                                <input type="checkbox" class="checkbox checkbox-warning mr-3" required>
                                <span class="label-text">{{ __('Environment variables are set in .env file') }}</span>
                            </label>
                            <label class="label cursor-pointer justify-start">
                                <input type="checkbox" class="checkbox checkbox-warning mr-3" required>
                                <span class="label-text">{{ __('You have Microsoft 365 admin privileges') }}</span>
                            </label>
                            <label class="label cursor-pointer justify-start">
                                <input type="checkbox" class="checkbox checkbox-warning mr-3" required>
                                <span class="label-text">{{ __('Queue system is running for background jobs') }}</span>
                            </label>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-between">
                        <a href="{{ route('admin.microsoft-integration.index') }}" class="btn btn-ghost">
                            <i class="fa-sharp fa-arrow-left mr-2"></i>
                            {{ __('Back') }}
                        </a>
                        
                        <div class="flex gap-4">
                            <a href="{{ url('/docs/microsoft-365-integration.md') }}" target="_blank" class="btn btn-info">
                                <i class="fa-sharp fa-book mr-2"></i>
                                {{ __('Setup Guide') }}
                            </a>
                            
                            <a href="{{ route('admin.microsoft-integration.connect') }}" class="btn btn-primary">
                                <i class="fa-brands fa-microsoft mr-2"></i>
                                {{ __('Connect to Microsoft 365') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</x-app-layout>