<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            {{ __('Change Microsoft 365 Calendar') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fa-brands fa-microsoft text-primary mr-2"></i>
                        {{ __('Change Calendar') }}
                    </h2>
                    
                    <p class="text-base-content/70 mb-6">
                        {{ __('Select a different Microsoft 365 calendar for syncing ETRFlow2 pickup events. This will not delete existing events but all future events will be created in the new calendar.') }}
                    </p>

                    <!-- Current Calendar Info -->
                    <div class="bg-info/10 border border-info/20 rounded-lg p-4 mb-6">
                        <h4 class="font-medium text-info mb-2">
                            <i class="fa-sharp fa-info-circle mr-2"></i>
                            {{ __('Current Calendar') }}
                        </h4>
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-medium">{{ $sharedIntegration->calendar_name }}</p>
                                <p class="text-sm text-base-content/70">{{ $sharedIntegration->microsoft_user_email }}</p>
                                @if ($sharedIntegration->outlook_calendar_id)
                                    <p class="text-xs text-base-content/50">ID: {{ $sharedIntegration->outlook_calendar_id }}</p>
                                @endif
                            </div>
                            <div class="badge badge-info">Currently Active</div>
                        </div>
                    </div>

                    <!-- Calendar Selection Form -->
                    <form action="{{ route('admin.microsoft-integration.update-calendar') }}" method="POST">
                        @csrf
                        
                        <div class="space-y-4 mb-6">
                            @if (count($calendars) > 0)
                                @foreach ($calendars as $calendar)
                                    <div class="form-control">
                                        <label class="label cursor-pointer p-4 border border-base-300 rounded-lg hover:bg-base-200 transition-colors
                                               {{ $calendar['id'] === $sharedIntegration->outlook_calendar_id ? 'bg-primary/10 border-primary' : '' }}">
                                            <div class="flex items-center flex-1">
                                                <input type="radio" name="calendar_id" value="{{ $calendar['id'] }}" 
                                                       class="radio radio-primary mr-4" required
                                                       {{ $calendar['id'] === $sharedIntegration->outlook_calendar_id ? 'checked' : '' }}
                                                       onchange="updateCalendarName('{{ $calendar['name'] }}')">
                                                <div class="flex-1">
                                                    <div class="flex items-center justify-between">
                                                        <div>
                                                            <p class="font-medium">{{ $calendar['name'] }}</p>
                                                            @if (isset($calendar['owner']['name']))
                                                                <p class="text-sm text-base-content/70">
                                                                    Owner: {{ $calendar['owner']['name'] }}
                                                                </p>
                                                            @endif
                                                            <div class="flex gap-2 mt-1">
                                                                @if (isset($calendar['isDefaultCalendar']) && $calendar['isDefaultCalendar'])
                                                                    <div class="badge badge-info badge-sm">Default Calendar</div>
                                                                @endif
                                                                @if ($calendar['id'] === $sharedIntegration->outlook_calendar_id)
                                                                    <div class="badge badge-primary badge-sm">Current</div>
                                                                @endif
                                                            </div>
                                                        </div>
                                                        <div class="text-right">
                                                            @if (isset($calendar['canEdit']) && $calendar['canEdit'])
                                                                <div class="badge badge-success badge-sm">Can Edit</div>
                                                            @else
                                                                <div class="badge badge-warning badge-sm">Read Only</div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                    @if (isset($calendar['color']))
                                                        <div class="flex items-center mt-2">
                                                            <div class="w-4 h-4 rounded mr-2" style="background-color: {{ $calendar['color'] }}"></div>
                                                            <span class="text-xs text-base-content/50">Calendar Color</span>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                @endforeach
                            @else
                                <div class="alert alert-warning">
                                    <i class="fa-sharp fa-exclamation-triangle"></i>
                                    <span>{{ __('No calendars found. Please check your Microsoft 365 connection.') }}</span>
                                </div>
                            @endif
                        </div>

                        <!-- Hidden field to store selected calendar name -->
                        <input type="hidden" name="calendar_name" id="selected_calendar_name" value="{{ $sharedIntegration->calendar_name }}">

                        <!-- Actions -->
                        <div class="flex flex-col sm:flex-row gap-4 justify-between">
                            <a href="{{ route('admin.microsoft-integration.index') }}" class="btn btn-ghost">
                                <i class="fa-sharp fa-arrow-left mr-2"></i>
                                {{ __('Back') }}
                            </a>
                            
                            @if (count($calendars) > 0)
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa-sharp fa-save mr-2"></i>
                                    {{ __('Update Calendar') }}
                                </button>
                            @else
                                <form action="{{ route('admin.microsoft-integration.test') }}" method="POST" class="inline">
                                    @csrf
                                    <button type="submit" class="btn btn-info">
                                        <i class="fa-sharp fa-diagnostic mr-2"></i>
                                        {{ __('Test Connection') }}
                                    </button>
                                </form>
                            @endif
                        </div>
                    </form>

                    <!-- Migration Warning -->
                    <div class="mt-8">
                        <div class="bg-warning/10 border border-warning/20 rounded-lg p-4">
                            <h4 class="font-medium text-warning mb-2">
                                <i class="fa-sharp fa-exclamation-triangle mr-2"></i>
                                {{ __('Important Notes') }}
                            </h4>
                            <ul class="text-sm text-base-content/70 space-y-1 ml-4">
                                <li>• {{ __('Existing events will remain in the current calendar') }}</li>
                                <li>• {{ __('Only new events will be created in the selected calendar') }}</li>
                                <li>• {{ __('Consider manually migrating important existing events if needed') }}</li>
                                <li>• {{ __('All staff should be informed of the calendar change') }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    @push('scripts')
    <script>
        function updateCalendarName(name) {
            document.getElementById('selected_calendar_name').value = name;
        }

        // Set the calendar name for the pre-selected current calendar
        document.addEventListener('DOMContentLoaded', function() {
            const currentRadio = document.querySelector('input[name="calendar_id"]:checked');
            if (currentRadio) {
                const calendarName = currentRadio.closest('label').querySelector('p.font-medium').textContent;
                updateCalendarName(calendarName);
            }
        });
    </script>
    @endpush
</x-app-layout>