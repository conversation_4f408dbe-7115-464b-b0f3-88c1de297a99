<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Admin Tools
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Administrative Tools</h3>
                    <p class="mb-6 text-gray-600">This page contains links to specialized administrative tools and utilities.</p>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Customer CSV Import Tool -->
                        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
                            <div class="card-body">
                                <h2 class="card-title">
                                    <i class="fa-sharp fa-file-import text-blue-500 mr-2"></i>
                                    Customer CSV Import
                                </h2>
                                <p>Import customers from a CSV file with column mapping and contract creation.</p>
                                <div class="card-actions justify-end mt-4">
                                    <a href="{{ route('admin.tools.customer-import.index') }}" class="btn btn-primary btn-sm">
                                        Open Tool
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Data Export Tool -->
                        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
                            <div class="card-body">
                                <h2 class="card-title">
                                    <i class="fa-sharp fa-file-export text-green-500 mr-2"></i>
                                    Customer Data Export
                                </h2>
                                <p>Export customer data to Excel or CSV format with customizable field selection.</p>
                                <div class="card-actions justify-end mt-4">
                                    <a href="{{ route('admin.tools.customer-export.index') }}" class="btn btn-primary btn-sm">
                                        Open Tool
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- PHP Information Tool -->
                        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
                            <div class="card-body">
                                <h2 class="card-title">
                                    <i class="fa-sharp fa-info-circle text-orange-500 mr-2"></i>
                                    PHP Information
                                </h2>
                                <p>View comprehensive PHP configuration, version, extensions, and server information.</p>
                                <div class="card-actions justify-end mt-4">
                                    <a href="{{ route('admin.tools.phpinfo') }}" class="btn btn-primary btn-sm">
                                        Open Tool
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- System Statistics Tool -->
                        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
                            <div class="card-body">
                                <h2 class="card-title">
                                    <i class="fa-sharp fa-chart-line text-purple-500 mr-2"></i>
                                    System Statistics
                                </h2>
                                <p>View disk usage, system information, database stats, and server performance metrics.</p>
                                <div class="card-actions justify-end mt-4">
                                    <a href="{{ route('admin.tools.system-stats.index') }}" class="btn btn-primary btn-sm">
                                        Open Tool
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
