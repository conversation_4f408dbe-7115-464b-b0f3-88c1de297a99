<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            System Statistics
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">System Statistics & Disk Usage</h3>
                            <p class="text-gray-600">Overview of system resources and disk space usage</p>
                        </div>
                        <a href="{{ route('admin.tools.index') }}" class="btn btn-outline btn-sm">
                            <i class="fa-sharp fa-arrow-left mr-2"></i>
                            Back to Tools
                        </a>
                    </div>

                    <!-- Disk Usage Section -->
                    <div class="mb-8">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fa-sharp fa-hard-drive text-blue-500 mr-2"></i>
                            Disk Usage
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($stats['disk_usage'] as $directory => $usage)
                                <div class="card bg-base-100 shadow-md">
                                    <div class="card-body">
                                        <h2 class="card-title text-sm">
                                            <i class="fa-sharp fa-folder text-yellow-500 mr-1"></i>
                                            {{ ucfirst(str_replace('_', ' ', $directory)) }}
                                        </h2>
                                        @if($usage['exists'])
                                            <div class="text-2xl font-bold text-blue-600">
                                                {{ $usage['size_human'] }}
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                {{ number_format($usage['size_bytes']) }} bytes
                                            </div>
                                        @else
                                            <div class="text-sm text-gray-500">
                                                Directory not found
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- System Information Section -->
                    <div class="mb-8">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fa-sharp fa-server text-green-500 mr-2"></i>
                            System Information
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div class="card bg-base-100 shadow-md">
                                <div class="card-body">
                                    <h2 class="card-title text-sm">PHP Version</h2>
                                    <div class="text-lg font-semibold text-blue-600">
                                        {{ $stats['system']['php_version'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="card bg-base-100 shadow-md">
                                <div class="card-body">
                                    <h2 class="card-title text-sm">Laravel Version</h2>
                                    <div class="text-lg font-semibold text-red-600">
                                        {{ $stats['system']['laravel_version'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="card bg-base-100 shadow-md">
                                <div class="card-body">
                                    <h2 class="card-title text-sm">Memory Limit</h2>
                                    <div class="text-lg font-semibold text-purple-600">
                                        {{ $stats['system']['memory_limit'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="card bg-base-100 shadow-md">
                                <div class="card-body">
                                    <h2 class="card-title text-sm">Max Execution Time</h2>
                                    <div class="text-lg font-semibold text-orange-600">
                                        {{ $stats['system']['max_execution_time'] }}s
                                    </div>
                                </div>
                            </div>
                            <div class="card bg-base-100 shadow-md">
                                <div class="card-body">
                                    <h2 class="card-title text-sm">Upload Max Size</h2>
                                    <div class="text-lg font-semibold text-teal-600">
                                        {{ $stats['system']['upload_max_filesize'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="card bg-base-100 shadow-md">
                                <div class="card-body">
                                    <h2 class="card-title text-sm">POST Max Size</h2>
                                    <div class="text-lg font-semibold text-indigo-600">
                                        {{ $stats['system']['post_max_size'] }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Database Information Section -->
                    <div class="mb-8">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fa-sharp fa-database text-purple-500 mr-2"></i>
                            Database Information
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="card bg-base-100 shadow-md">
                                <div class="card-body">
                                    <h2 class="card-title text-sm">Connection Type</h2>
                                    <div class="text-lg font-semibold text-green-600">
                                        {{ $stats['database']['connection'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="card bg-base-100 shadow-md">
                                <div class="card-body">
                                    <h2 class="card-title text-sm">Database Name</h2>
                                    <div class="text-lg font-semibold text-blue-600">
                                        {{ $stats['database']['database_name'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="card bg-base-100 shadow-md">
                                <div class="card-body">
                                    <h2 class="card-title text-sm">Tables Count</h2>
                                    <div class="text-lg font-semibold text-purple-600">
                                        {{ $stats['database']['tables_count'] }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Server Information -->
                    @if($stats['system']['server_software'] !== 'N/A')
                    <div class="mb-8">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fa-sharp fa-globe text-orange-500 mr-2"></i>
                            Server Information
                        </h4>
                        <div class="card bg-base-100 shadow-md">
                            <div class="card-body">
                                <h2 class="card-title text-sm">Server Software</h2>
                                <div class="text-lg font-semibold text-gray-700">
                                    {{ $stats['system']['server_software'] }}
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Refresh Button -->
                    <div class="flex justify-center mt-8">
                        <button onclick="window.location.reload()" class="btn btn-primary">
                            <i class="fa-sharp fa-refresh mr-2"></i>
                            Refresh Statistics
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>