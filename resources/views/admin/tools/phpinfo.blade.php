<x-app-layout
    page-title="PHP Information"
    page-icon="fa-sharp fa-info-circle"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('admin.tools.index'),
            'text' => 'Back to Admin Tools'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Admin Tools', 'route' => 'admin.tools.index', 'icon' => 'fa-toolbox'],
        ['name' => 'PHP Information', 'icon' => 'fa-info-circle']
    ]">

    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto px-2 lg:px-8 space-y-6">

            <!-- Header Card -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-info/10 to-info/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center gap-3">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-info text-info-content w-8 rounded-lg">
                                <i class="fa-sharp fa-info-circle text-sm"></i>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-base-content">PHP Configuration Information</h4>
                    </div>
                </div>
                <div class="p-6">
                    <p class="text-base-content/70 mb-4">
                        This page displays comprehensive PHP configuration information including version, extensions, 
                        settings, and environment details. This information is useful for debugging and system administration.
                    </p>
                    
                    <!-- Quick Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="stat bg-base-200 rounded-lg">
                            <div class="stat-title">PHP Version</div>
                            <div class="stat-value text-lg">{{ PHP_VERSION }}</div>
                        </div>
                        <div class="stat bg-base-200 rounded-lg">
                            <div class="stat-title">Server API</div>
                            <div class="stat-value text-lg">{{ php_sapi_name() }}</div>
                        </div>
                        <div class="stat bg-base-200 rounded-lg">
                            <div class="stat-title">Memory Limit</div>
                            <div class="stat-value text-lg">{{ ini_get('memory_limit') }}</div>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fa-sharp fa-exclamation-triangle"></i>
                        <span>
                            <strong>Security Notice:</strong> This page contains sensitive server information. 
                            Only authorized administrators should have access to this tool.
                        </span>
                    </div>
                </div>
            </div>

            <!-- PHP Info Content -->
            <div class="card bg-base-100 shadow-md">
                <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="avatar avatar-placeholder">
                                <div class="bg-primary text-primary-content w-8 rounded-lg">
                                    <i class="fa-sharp fa-server text-sm"></i>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-base-content">Complete PHP Information</h4>
                        </div>
                        <button onclick="toggleFullscreen()" class="btn btn-sm btn-outline">
                            <i class="fa-sharp fa-expand"></i>
                            Fullscreen
                        </button>
                    </div>
                </div>
                <div class="p-0" id="phpinfo-container">
                    <div class="overflow-auto max-h-screen" style="font-family: monospace;">
                        {!! $phpinfo !!}
                    </div>
                </div>
            </div>

        </div>
    </div>

    @push('scripts')
    <script>
        function toggleFullscreen() {
            const container = document.getElementById('phpinfo-container');
            const button = event.target.closest('button');
            
            if (container.classList.contains('fixed')) {
                // Exit fullscreen
                container.classList.remove('fixed', 'inset-0', 'z-50', 'bg-base-100');
                container.querySelector('.overflow-auto').classList.remove('h-screen');
                container.querySelector('.overflow-auto').classList.add('max-h-screen');
                button.innerHTML = '<i class="fa-sharp fa-expand"></i> Fullscreen';
            } else {
                // Enter fullscreen
                container.classList.add('fixed', 'inset-0', 'z-50', 'bg-base-100');
                container.querySelector('.overflow-auto').classList.remove('max-h-screen');
                container.querySelector('.overflow-auto').classList.add('h-screen');
                button.innerHTML = '<i class="fa-sharp fa-compress"></i> Exit Fullscreen';
            }
        }

        // ESC key to exit fullscreen
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const container = document.getElementById('phpinfo-container');
                if (container.classList.contains('fixed')) {
                    toggleFullscreen();
                }
            }
        });
    </script>
    @endpush

    @push('styles')
    <style>
        /* Style the phpinfo output to match our theme */
        #phpinfo-container table {
            @apply w-full border-collapse;
        }
        
        #phpinfo-container th {
            @apply bg-base-200 text-base-content font-semibold p-2 border border-base-300;
        }
        
        #phpinfo-container td {
            @apply p-2 border border-base-300 text-sm;
        }
        
        #phpinfo-container tr:nth-child(even) {
            @apply bg-base-50;
        }
        
        #phpinfo-container h1, #phpinfo-container h2 {
            @apply text-base-content font-bold mb-4 mt-6;
        }
        
        #phpinfo-container h1 {
            @apply text-xl;
        }
        
        #phpinfo-container h2 {
            @apply text-lg;
        }
        
        #phpinfo-container .center {
            @apply text-center;
        }
        
        #phpinfo-container .e {
            @apply bg-base-200 font-medium;
        }
        
        #phpinfo-container .v {
            @apply bg-base-100;
        }
    </style>
    @endpush

</x-app-layout>
