<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Import Results
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Import Results</h3>

                    @if(!empty($results['errors']))
                        <div class="alert alert-error mb-6">
                            <i class="fa-sharp fa-circle-xmark mr-2"></i>
                            <div>
                                <h4 class="font-bold">Errors occurred during import:</h4>
                                <ul class="mt-1 list-disc list-inside">
                                    @foreach($results['errors'] as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    @endif

                    <div class="stats shadow mb-8 w-full">
                        <div class="stat">
                            <div class="stat-figure text-primary">
                                <i class="fa-sharp fa-users text-3xl"></i>
                            </div>
                            <div class="stat-title">Customers Imported</div>
                            <div class="stat-value text-primary">{{ $results['imported'] }}</div>
                            <div class="stat-desc">As {{ $results['customer_type'] }}</div>
                        </div>

                        <div class="stat">
                            <div class="stat-figure text-secondary">
                                <i class="fa-sharp fa-file-contract text-3xl"></i>
                            </div>
                            <div class="stat-title">Contracts Created</div>
                            <div class="stat-value text-secondary">{{ $results['contracts_created'] }}</div>
                        </div>

                        <div class="stat">
                            <div class="stat-figure text-gray-500">
                                <i class="fa-sharp fa-ban text-3xl"></i>
                            </div>
                            <div class="stat-title">Rows Skipped</div>
                            <div class="stat-value">{{ $results['skipped'] }}</div>
                            <div class="stat-desc">Missing required data</div>
                        </div>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
                        <h4 class="font-medium text-gray-700 mb-2">Import Summary:</h4>
                        <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
                            <li>Successfully imported <strong>{{ $results['imported'] }}</strong> customers as <strong>{{ $results['customer_type'] }}</strong></li>
                            <li>Created <strong>{{ $results['contracts_created'] }}</strong> customer contracts</li>
                            <li>Skipped <strong>{{ $results['skipped'] }}</strong> rows due to missing required data or duplicate emails</li>
                            @if($results['duplicate_emails'] > 0)
                                <li>Found <strong>{{ $results['duplicate_emails'] }}</strong> rows with emails that already exist in the database</li>
                            @endif
                            @if($results['no_name'] > 0)
                                <li>Skipped <strong>{{ $results['no_name'] }}</strong> rows because no name, contact, or email could be used</li>
                            @endif
                            @if($results['derived_names'] > 0)
                                <li>Created <strong>{{ $results['derived_names'] }}</strong> customers with names derived from contact or email fields</li>
                            @endif
                            @if($results['invalid_start_dates'] > 0)
                                <li>Skipped creating <strong>{{ $results['invalid_start_dates'] }}</strong> contracts due to missing or invalid start dates</li>
                            @endif
                            @if($results['imported'] > 0)
                                <li>All imported customers can now be found in the <a href="{{ route('customers.index') }}" class="text-blue-600 hover:underline">customers list</a></li>
                            @endif
                        </ul>
                    </div>

                    <div class="flex items-center justify-start mt-6">
                        <a href="{{ route('admin.tools.customer-import.index') }}" class="btn btn-primary">
                            <i class="fa-sharp fa-file-import mr-2"></i> Import More Customers
                        </a>
                        <a href="{{ route('admin.tools.index') }}" class="btn btn-ghost ml-4">
                            Return to Admin Tools
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
