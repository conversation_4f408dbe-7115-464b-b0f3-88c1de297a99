<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Map CSV Columns
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Map CSV Columns to Customer Fields</h3>

                    <div class="mb-6">
                        <p class="text-gray-600 mb-4">
                            Select which CSV column corresponds to each customer field. Then, choose which rows to import.
                            <strong>Note:</strong> Rows without a name will be skipped automatically.
                        </p>
                    </div>

                    <form action="{{ route('admin.tools.customer-import.process') }}" method="POST" id="importForm">
                        @csrf

                        <!-- Customer Type Selection -->
                        <div class="mb-8 bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <h4 class="font-medium text-gray-700 mb-4">Import Settings</h4>

                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text font-medium">Import Customers As <span class="text-red-500">*</span></span>
                                </label>
                                <select name="customer_type" class="select select-bordered w-full max-w-md" required>
                                    <option value="">-- Select Customer Type --</option>
                                    <option value="Residential Customer">Residential Customer</option>
                                    <option value="Online Customer">Online Customer</option>
                                    <option value="Bulk Buyer">Bulk Buyer</option>
                                    <option value="Business">Business</option>
                                </select>
                                <label class="label">
                                    <span class="label-text-alt text-gray-500">All imported customers will be created with this type</span>
                                </label>
                            </div>
                        </div>

                        <!-- Column Mapping Section -->
                        <div class="mb-8">
                            <h4 class="font-medium text-gray-700 mb-4">Column Mapping</h4>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <!-- Name Field (Required) -->
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">Name <span class="text-red-500">*</span></span>
                                    </label>
                                    <select name="mapping[name]" class="select select-bordered w-full" required>
                                        <option value="">-- Select Column --</option>
                                        @foreach($headers as $index => $header)
                                            <option value="{{ $index }}">{{ $header }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Email Field -->
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">Email</span>
                                    </label>
                                    <select name="mapping[email]" class="select select-bordered w-full">
                                        <option value="">-- Select Column --</option>
                                        @foreach($headers as $index => $header)
                                            <option value="{{ $index }}">{{ $header }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Contact Field -->
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">Contact Person</span>
                                    </label>
                                    <select name="mapping[contact]" class="select select-bordered w-full">
                                        <option value="">-- Select Column --</option>
                                        @foreach($headers as $index => $header)
                                            <option value="{{ $index }}">{{ $header }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Phone Field -->
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">Phone</span>
                                    </label>
                                    <select name="mapping[phone]" class="select select-bordered w-full">
                                        <option value="">-- Select Column --</option>
                                        @foreach($headers as $index => $header)
                                            <option value="{{ $index }}">{{ $header }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Contract Start Date Field -->
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">Contract Start Date</span>
                                    </label>
                                    <select name="mapping[contract_start_date]" class="select select-bordered w-full">
                                        <option value="">-- Select Column --</option>
                                        @foreach($headers as $index => $header)
                                            <option value="{{ $index }}">{{ $header }}</option>
                                        @endforeach
                                    </select>
                                    <label class="label">
                                        <span class="label-text-alt text-gray-500">Supports various date formats (MM/DD/YYYY, DD/MM/YYYY, etc.)</span>
                                    </label>
                                </div>

                                <!-- Contract End Date Field -->
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">Contract End Date</span>
                                    </label>
                                    <select name="mapping[contract_end_date]" class="select select-bordered w-full">
                                        <option value="">-- Select Column --</option>
                                        @foreach($headers as $index => $header)
                                            <option value="{{ $index }}">{{ $header }}</option>
                                        @endforeach
                                    </select>
                                    <label class="label">
                                        <span class="label-text-alt text-gray-500">Supports various date formats (MM/DD/YYYY, DD/MM/YYYY, etc.)</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Data Preview and Row Selection Section -->
                        <div class="mb-8">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="font-medium text-gray-700">Data Preview and Row Selection</h4>
                                <div class="flex items-center space-x-4">
                                    <span class="text-sm text-gray-600">{{ $rowCount }} rows found</span>
                                    <div class="form-control">
                                        <label class="cursor-pointer label">
                                            <span class="label-text mr-2">Select All</span>
                                            <input type="checkbox" id="selectAll" class="checkbox checkbox-primary" />
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="overflow-x-auto">
                                <table class="table w-full table-compact">
                                    <thead>
                                        <tr>
                                            <th class="w-12">Select</th>
                                            @foreach($headers as $header)
                                                <th>{{ $header }}</th>
                                            @endforeach
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($data as $rowIndex => $row)
                                            <tr>
                                                <td>
                                                    <input type="checkbox" name="selected_rows[]" value="{{ $rowIndex }}" class="checkbox checkbox-primary row-checkbox" checked>
                                                </td>
                                                @foreach($row as $cell)
                                                    <td>{{ $cell }}</td>
                                                @endforeach
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="flex items-center justify-start mt-6">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa-sharp fa-file-import mr-2"></i> Import Selected Rows
                            </button>
                            <a href="{{ route('admin.tools.customer-import.index') }}" class="btn btn-ghost ml-4">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const rowCheckboxes = document.querySelectorAll('.row-checkbox');

            // Select All functionality
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                rowCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
            });

            // Update Select All state based on individual checkboxes
            function updateSelectAllState() {
                const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
                selectAllCheckbox.checked = checkedCount === rowCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
            }

            rowCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectAllState);
            });

            // Form validation
            document.getElementById('importForm').addEventListener('submit', function(e) {
                const selectedRows = document.querySelectorAll('.row-checkbox:checked').length;
                const nameMapping = document.querySelector('select[name="mapping[name]"]').value;

                if (selectedRows === 0) {
                    e.preventDefault();
                    alert('Please select at least one row to import.');
                    return false;
                }

                if (!nameMapping) {
                    e.preventDefault();
                    alert('Please map the Name field. It is required for import.');
                    return false;
                }

                return true;
            });
        });
    </script>
</x-app-layout>
