<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Customer CSV Import
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Import Customers from CSV</h3>

                    @if(session('error'))
                        <div class="alert alert-error mb-6">
                            <i class="fa-sharp fa-circle-xmark mr-2"></i>
                            {{ session('error') }}
                        </div>
                    @endif

                    <div class="mb-6">
                        <p class="text-gray-600 mb-4">
                            Upload a CSV file containing customer data. In the next step, you'll be able to map the columns
                            to the appropriate fields and select which rows to import.
                        </p>

                        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
                            <h4 class="font-medium text-gray-700 mb-2">CSV Format Guidelines:</h4>
                            <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
                                <li>The CSV file should have a header row</li>
                                <li>At minimum, a column for customer names is required</li>
                                <li>Rows without a name will be skipped</li>
                                <li>You can include columns for: Name, Email, Contact, Phone, Contract Start Date, Contract End Date</li>
                                <li>Date formats can be in various formats (MM/DD/YYYY, DD/MM/YYYY, YYYY-MM-DD, etc.)</li>
                                <li>Maximum file size: 10MB</li>
                            </ul>
                        </div>
                    </div>

                    <form action="{{ route('admin.tools.customer-import.upload') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                        @csrf

                        <div class="form-control">
                            <label for="csv_file" class="label">
                                <span class="label-text font-medium">Select CSV File</span>
                            </label>
                            <input type="file" name="csv_file" id="csv_file" accept=".csv,.txt" class="file-input file-input-bordered w-full max-w-md" required>
                            @error('csv_file')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex items-center justify-start mt-6">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa-sharp fa-upload mr-2"></i> Upload and Continue
                            </button>
                            <a href="{{ route('admin.tools.index') }}" class="btn btn-ghost ml-4">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
