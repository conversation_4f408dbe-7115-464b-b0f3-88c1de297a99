<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Customer Data Export') }}
        </h2>
    </x-slot>

    <div class="py-6 lg:py-8 space-y-8">
        <div class="mx-auto sm:px-2 lg:px-4 space-y-8">
            <!-- Header Card -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-base-content">Customer Data Export</h1>
                            <p class="text-base-content/70 mt-1 text-lg">Export customer data to Excel or CSV format with customizable field selection.</p>
                        </div>
                        <div class="hidden lg:flex items-center gap-2 text-base-content/60">
                            <div class="stat">
                                <div class="stat-title text-xs">Total Customers</div>
                                <div class="stat-value text-2xl">{{ number_format($totalCustomers) }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Export Section -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body">
                    <h2 class="card-title text-xl mb-4">Quick Export (All Fields)</h2>
                    <p class="text-base-content/70 mb-6">Export all customer data with all available fields included.</p>
                    
                    <div class="flex gap-4">
                        <a href="{{ route('admin.tools.customer-export.quick-excel') }}" 
                           class="btn btn-primary gap-2">
                            <i class="fa-sharp fa-file-excel"></i>
                            Export to Excel
                        </a>
                        <a href="{{ route('admin.tools.customer-export.quick-csv') }}" 
                           class="btn btn-outline btn-primary gap-2">
                            <i class="fa-sharp fa-file-csv"></i>
                            Export to CSV
                        </a>
                    </div>
                </div>
            </div>

            <!-- Custom Export Section -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body">
                    <h2 class="card-title text-xl mb-4">Custom Export</h2>
                    <p class="text-base-content/70 mb-6">Select specific fields to include in your export.</p>
                    
                    <form id="exportForm" method="POST">
                        @csrf
                        
                        <!-- Field Selection -->
                        <div class="mb-6">
                            <label class="label">
                                <span class="label-text font-semibold">Select Fields to Export</span>
                            </label>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach($availableFields as $key => $label)
                                    <label class="cursor-pointer label justify-start gap-3">
                                        <input type="checkbox" 
                                               name="fields[]" 
                                               value="{{ $key }}" 
                                               class="checkbox checkbox-primary" 
                                               checked>
                                        <span class="label-text">{{ $label }}</span>
                                    </label>
                                @endforeach
                            </div>
                        </div>

                        <!-- Select All / Deselect All -->
                        <div class="mb-6">
                            <button type="button" id="selectAll" class="btn btn-sm btn-outline">Select All</button>
                            <button type="button" id="deselectAll" class="btn btn-sm btn-outline">Deselect All</button>
                        </div>

                        <!-- Export Buttons -->
                        <div class="flex gap-4">
                            <button type="submit" 
                                    formaction="{{ route('admin.tools.customer-export.excel') }}"
                                    class="btn btn-primary gap-2">
                                <i class="fa-sharp fa-file-excel"></i>
                                Export to Excel
                            </button>
                            <button type="submit" 
                                    formaction="{{ route('admin.tools.customer-export.csv') }}"
                                    class="btn btn-outline btn-primary gap-2">
                                <i class="fa-sharp fa-file-csv"></i>
                                Export to CSV
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Export Information -->
            <div class="card bg-base-100 shadow-sm border border-base-200">
                <div class="card-body">
                    <h2 class="card-title text-xl mb-4">Export Information</h2>
                    
                    <div class="space-y-4">
                        <div class="alert alert-info">
                            <i class="fa-sharp fa-info-circle"></i>
                            <div>
                                <h3 class="font-bold">About the Export</h3>
                                <div class="text-sm">
                                    <ul class="list-disc list-inside mt-2 space-y-1">
                                        <li><strong>Total Revenue:</strong> Sum of all invoice final prices for the customer</li>
                                        <li><strong>Total Purchases:</strong> Count of inventory items purchased by the customer</li>
                                        <li><strong>Data Destruction Certificates:</strong> Count of certificates issued for the customer</li>
                                        <li><strong>File Format:</strong> Excel files (.xlsx) preserve formatting, CSV files (.csv) are plain text</li>
                                        <li><strong>Data Currency:</strong> Export includes real-time data as of the moment of export</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllBtn = document.getElementById('selectAll');
            const deselectAllBtn = document.getElementById('deselectAll');
            const checkboxes = document.querySelectorAll('input[name="fields[]"]');
            const form = document.getElementById('exportForm');

            selectAllBtn.addEventListener('click', function() {
                checkboxes.forEach(checkbox => checkbox.checked = true);
            });

            deselectAllBtn.addEventListener('click', function() {
                checkboxes.forEach(checkbox => checkbox.checked = false);
            });

            // Validate form submission
            form.addEventListener('submit', function(e) {
                const checkedBoxes = document.querySelectorAll('input[name="fields[]"]:checked');
                if (checkedBoxes.length === 0) {
                    e.preventDefault();
                    alert('Please select at least one field to export.');
                }
            });
        });
    </script>
</x-app-layout>
