/* Import TipTap styles first */
@import "./tiptap.css";
@import "tailwindcss";
 @plugin "daisyui" {
   themes: light --default, business, light, corporate;
 }








[x-cloak] {
    display: none;
}

/* Ensure Quill editors have lower z-index than Google Maps autocomplete */
.ql-toolbar,
.ql-container,
.ql-editor {
    z-index: 1000 !important;
}

/* Ensure Google Maps autocomplete has highest z-index */
gmp-place-autocomplete,
.pac-container,
.gm-style,
.gm-style > div,
.gm-style-iw,
.gm-style-iw-c,
.gm-style-iw-d {
    z-index: 10000 !important;
}

/* Custom HTML Content Styling */
.html-content-styled {
    @apply max-w-none;
    
    /* Typography */
    font-size: 0.875rem;
    line-height: 1.625;
    
}

.html-content-styled h1 {
    @apply text-3xl font-bold mb-6 mt-8;
    color: theme('colors.base-content');
}

.html-content-styled h2 {
    @apply text-2xl font-bold mb-4 mt-6;
    color: theme('colors.base-content');
}

.html-content-styled h3 {
    @apply text-xl font-semibold mb-3 mt-5;
    color: theme('colors.base-content');
}

.html-content-styled h4 {
    @apply text-lg font-semibold mb-2 mt-4;
    color: theme('colors.base-content');
}

.html-content-styled h5 {
    @apply text-base font-semibold mb-2 mt-3;
    color: theme('colors.base-content');
}

.html-content-styled h6 {
    @apply text-sm font-semibold mb-2 mt-3;
    color: theme('colors.base-content');
}

.html-content-styled p {
    @apply mb-4;
    color: theme('colors.base-content');
}

.html-content-styled p:last-child {
    @apply mb-0;
}

/* Lists */
.html-content-styled ul {
    @apply mb-4 pl-6;
    list-style-type: disc;
}

.html-content-styled ol {
    @apply mb-4 pl-6;
    list-style-type: decimal;
}

.html-content-styled ul ul,
.html-content-styled ol ul {
    list-style-type: circle;
    @apply mb-0 mt-1;
}

.html-content-styled ul ol,
.html-content-styled ol ol {
    list-style-type: lower-alpha;
    @apply mb-0 mt-1;
}

.html-content-styled li {
    @apply mb-1;
    color: theme('colors.base-content');
}

.html-content-styled li p {
    @apply mb-1;
}

/* Links */
.html-content-styled a {
    @apply text-primary underline font-medium transition-colors;
}

.html-content-styled a:hover {
    opacity: 0.8;
}

/* Text formatting */
.html-content-styled strong,
.html-content-styled b {
    @apply font-bold;
    color: theme('colors.base-content');
}

.html-content-styled em,
.html-content-styled i {
    @apply italic;
}

.html-content-styled u {
    @apply underline;
}

/* Blockquotes */
.html-content-styled blockquote {
    @apply border-l-4 border-primary/30 pl-4 py-2 mb-4 italic bg-base-200/30 text-base-content/80;
}

.html-content-styled blockquote p {
    @apply mb-2;
}

.html-content-styled blockquote p:last-child {
    @apply mb-0;
}

/* Tables */
.html-content-styled table {
    @apply w-full mb-4 border-collapse border border-base-300;
}

.html-content-styled th,
.html-content-styled td {
    @apply px-3 py-2 border border-base-300;
}

.html-content-styled th {
    @apply font-semibold bg-base-200 text-base-content;
}

.html-content-styled td {
    @apply text-base-content;
}

/* Horizontal rules */
.html-content-styled hr {
    @apply my-6 border-0 border-t border-base-300;
}

/* Code */
.html-content-styled code {
    @apply bg-base-200 px-1 py-0.5 rounded text-sm font-mono text-base-content;
}

.html-content-styled pre {
    @apply bg-base-200 p-4 rounded mb-4 overflow-x-auto;
}

.html-content-styled pre code {
    @apply bg-transparent p-0;
}

/* Images */
.html-content-styled img {
    @apply max-w-full h-auto mb-4 rounded;
}

/* Divs and spans inherit base styling */
.html-content-styled div,
.html-content-styled span {
    color: inherit;
}

/* Ensure nested content maintains proper spacing */
.html-content-styled > *:first-child {
    margin-top: 0;
}

.html-content-styled > *:last-child {
    margin-bottom: 0;
}
