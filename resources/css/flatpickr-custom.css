/* Custom styling for Flatpick<PERSON> to match DaisyUI theme - Sleeker and more modern */

.flatpickr-calendar {
    background: white;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    font-size: 0.875rem;
}

.flatpickr-time {
    border-top: 1px solid #e5e7eb;
    background: white;
    height: 36px;
    line-height: 36px;
}

.flatpickr-time input:hover,
.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time input:focus,
.flatpickr-time .flatpickr-am-pm:focus {
    background: #f3f4f6;
}

.flatpickr-time .numInputWrapper span.arrowUp:after {
    border-bottom-color: #3b82f6;
}

.flatpickr-time .numInputWrapper span.arrowDown:after {
    border-top-color: #3b82f6;
}

.flatpickr-time input,
.flatpickr-time .flatpickr-time-separator,
.flatpickr-time .flatpickr-am-pm {
    color: #374151;
    font-size: 0.875rem;
    height: 36px;
    line-height: 36px;
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
    background: white;
    color: #374151;
}

.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
    background: #f3f4f6;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
    color: #374151;
    fill: #374151;
}

.flatpickr-months .flatpickr-prev-month:hover,
.flatpickr-months .flatpickr-next-month:hover {
    color: #3b82f6;
    fill: #3b82f6;
}

.flatpickr-day {
    color: #374151;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
    background: #f3f4f6;
    border-color: #f3f4f6;
    color: #374151;
}

.flatpickr-day.today {
    border-color: #3b82f6;
}

.flatpickr-day.today:hover,
.flatpickr-day.today:focus {
    border-color: #3b82f6;
    background: #3b82f6;
    color: white;
}

.flatpickr-day.disabled,
.flatpickr-day.disabled:hover,
.flatpickr-day.prevMonthDay,
.flatpickr-day.nextMonthDay,
.flatpickr-day.notAllowed,
.flatpickr-day.notAllowed.prevMonthDay,
.flatpickr-day.notAllowed.nextMonthDay {
    color: #9ca3af;
    opacity: 0.5;
}

.flatpickr-time input.flatpickr-hour.selected,
.flatpickr-time input.flatpickr-minute.selected,
.flatpickr-time input.flatpickr-second.selected {
    color: #3b82f6;
    font-weight: bold;
}

/* Specific styling for AM/PM selector */
.flatpickr-time .flatpickr-am-pm {
    background: white;
    color: #374151;
    font-size: 0.875rem;
    font-weight: 500;
    height: 36px;
    line-height: 36px;
}

.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time .flatpickr-am-pm:focus {
    background: #f3f4f6;
    color: #3b82f6;
}

/* Make AM/PM more visible */
.flatpickr-am-pm.selected {
    background-color: #f3f4f6;
    color: #3b82f6;
    font-weight: bold;
}

/* Mobile-specific styles */
@media (max-width: 768px) {
    .flatpickr-calendar {
        font-size: 1rem;
        width: 100%;
        max-width: 320px;
    }

    .flatpickr-time {
        height: 48px;
        line-height: 48px;
    }

    .flatpickr-time input,
    .flatpickr-time .flatpickr-time-separator,
    .flatpickr-time .flatpickr-am-pm {
        font-size: 1rem;
        height: 48px;
        line-height: 48px;
        padding: 0 8px;
    }

    .flatpickr-time .numInputWrapper {
        width: 60px;
    }

    .flatpickr-time .flatpickr-am-pm {
        width: 60px;
        text-align: center;
    }

    /* Make touch targets larger on mobile */
    .flatpickr-time .numInputWrapper span.arrowUp,
    .flatpickr-time .numInputWrapper span.arrowDown {
        width: 20px;
        height: 20px;
    }
}

/* Ensure input mode none works properly */
input[inputmode="none"] {
    caret-color: transparent;
}
