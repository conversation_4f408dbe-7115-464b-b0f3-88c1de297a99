/* TipTap Editor Styles */
.tiptap-editor-container {
    border-radius: 0.5rem;
    overflow: hidden;
}

.tiptap-toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    padding: 0.5rem;
    background-color: var(--fallback-bc,oklch(var(--bc)/0.1));
    border-bottom: 1px solid var(--fallback-bc,oklch(var(--bc)/0.2));
}

.tiptap-toolbar button {
    padding: 0.5rem;
    background-color: var(--fallback-bc,oklch(var(--bc)/0.05));
    border: 1px solid var(--fallback-bc,oklch(var(--bc)/0.2));
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.2s;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
    min-width: 2.5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.tiptap-toolbar button:hover {
    background-color: var(--fallback-bc,oklch(var(--bc)/0.1));
}

.tiptap-toolbar button.is-active {
    background-color: var(--fallback-p,oklch(var(--p)/0.8));
    border-color: var(--fallback-p,oklch(var(--p)));
    color: var(--fallback-pc,oklch(var(--pc)));
}

.tiptap-toolbar button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.ProseMirror {
    padding: 1rem;
    min-height: 12rem;
    outline: none;
}

.ProseMirror p {
    margin-bottom: 0.75rem;
}

.ProseMirror ul, .ProseMirror ol {
    padding-left: 1.5rem;
    margin-bottom: 0.75rem;
}

.ProseMirror ul li, .ProseMirror ol li {
    margin-bottom: 0.25rem;
}

.ProseMirror blockquote {
    border-left: 3px solid var(--fallback-bc,oklch(var(--bc)/0.3));
    padding-left: 1rem;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
}
