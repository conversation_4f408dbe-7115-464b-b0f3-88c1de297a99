import imageCompression from 'browser-image-compression';

document.addEventListener('DOMContentLoaded', function() {
    // Handle both single file (photo_id) and multiple files (item_photos)
    const photoIdInput = document.getElementById('photo_id');
    const itemPhotosInput = document.getElementById('item_photos');
    const additionalPhotosInput = document.getElementById('additional_photos');
    const form = document.querySelector('form[enctype="multipart/form-data"]');

    if (!form) {
        return;
    }

    let isCompressing = false;
    let compressedFiles = new Map(); // Store compressed files by input id
    let submitButtons = []; // Track submit buttons to disable during compression

    // Setup compression for photo ID input (single file)
    if (photoIdInput) {
        setupSingleFileCompression(photoIdInput);
    }

    // Setup compression for item photos input (multiple files)
    if (itemPhotosInput) {
        setupMultipleFileCompression(itemPhotosInput);
    }

    // Setup compression for additional photos input
    if (additionalPhotosInput) {
        setupAdditionalFileCompression(additionalPhotosInput);
    }

    // Find and track submit buttons
    initializeSubmitButtons();

    // Expose compression functions globally for the file queue system
    window.compressNewFiles = compressNewFiles;
    window.updateCompressedFileQueue = updateCompressedFileQueue;

    function setupSingleFileCompression(input) {
        // Create compression status indicator
        const statusIndicator = document.createElement('div');
        statusIndicator.id = `compression-status-${input.id}`;
        statusIndicator.className = 'mt-2 hidden';
        input.parentNode.appendChild(statusIndicator);

        // Handle file selection
        input.addEventListener('change', async function(event) {
            const file = event.target.files[0];

            if (!file) {
                hideCompressionStatus(input.id);
                compressedFiles.delete(input.id);
                return;
            }

            // Only compress image files
            if (!file.type.startsWith('image/')) {
                hideCompressionStatus(input.id);
                compressedFiles.delete(input.id);
                return;
            }

            setCompressionState(true);
            const compressedFile = await compressImage(file, input.id);
            if (compressedFile) {
                compressedFiles.set(input.id, [compressedFile]);
            }
            setCompressionState(false);
        });
    }

    function setupMultipleFileCompression(input) {
        // Create compression status indicator
        const statusIndicator = document.createElement('div');
        statusIndicator.id = `compression-status-${input.id}`;
        statusIndicator.className = 'mt-2 hidden';

        // For item_photos, append to the photo queue area if it exists, otherwise to parent
        const photoQueue = document.getElementById('photoQueue');
        if (input.id === 'item_photos' && photoQueue) {
            photoQueue.appendChild(statusIndicator);
        } else {
            input.parentNode.appendChild(statusIndicator);
        }

        // Handle file selection - this will be triggered by the file queue system
        input.addEventListener('change', async function(event) {
            const files = Array.from(event.target.files);

            if (files.length === 0) {
                hideCompressionStatus(input.id);
                compressedFiles.delete(input.id);
                return;
            }

            // Filter only image files
            const imageFiles = files.filter(file => file.type.startsWith('image/'));

            if (imageFiles.length === 0) {
                hideCompressionStatus(input.id);
                compressedFiles.delete(input.id);
                return;
            }

            setCompressionState(true);

            const compressedFileArray = [];
            for (let i = 0; i < imageFiles.length; i++) {
                const file = imageFiles[i];
                const compressedFile = await compressImage(file, input.id);
                if (compressedFile) {
                    compressedFileArray.push(compressedFile);
                }
            }

            if (compressedFileArray.length > 0) {
                compressedFiles.set(input.id, compressedFileArray);
            }

            setCompressionState(false);
        });
    }

    function setupAdditionalFileCompression(input) {
        // This input is used for adding more files to the queue
        // We'll handle compression through the global functions
        input.addEventListener('change', async function(event) {
            const files = Array.from(event.target.files);
            if (files.length > 0) {
                const imageFiles = files.filter(file => file.type.startsWith('image/'));
                if (imageFiles.length > 0) {
                    await compressNewFiles(imageFiles);
                }
            }
        });
    }

    async function compressNewFiles(files) {
        if (files.length === 0) return [];

        setCompressionState(true);
        const compressedFileArray = [];

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const compressedFile = await compressImage(file, 'item_photos');
            if (compressedFile) {
                compressedFileArray.push(compressedFile);
            }
        }

        setCompressionState(false);
        return compressedFileArray;
    }

    function updateCompressedFileQueue(allFiles) {
        // Update the compressed files map with all files in the queue
        if (allFiles.length > 0) {
            compressedFiles.set('item_photos', allFiles);
        } else {
            compressedFiles.delete('item_photos');
        }
    }

    // Handle form submission
    form.addEventListener('submit', function(event) {
        if (isCompressing) {
            event.preventDefault();
            return;
        }

        // Replace file inputs with compressed files if available
        compressedFiles.forEach((files, inputId) => {
            const input = document.getElementById(inputId);
            if (input && files.length > 0) {
                const dataTransfer = new DataTransfer();
                files.forEach((compressedBlob) => {
                    // Convert compressed blob to File object, preserving original filename
                    const filename = compressedBlob.originalName || compressedBlob.name || 'compressed-image.jpg';

                    const file = new File([compressedBlob], filename, {
                        type: compressedBlob.type,
                        lastModified: Date.now()
                    });

                    dataTransfer.items.add(file);
                });
                input.files = dataTransfer.files;
            }
        });
    });

    async function compressImage(file, inputId) {
        try {
            const options = {
                maxSizeMB: 1,
                maxWidthOrHeight: 1200,
                useWebWorker: true,
                fileType: file.type,
                initialQuality: 0.8
            };

            const compressedFile = await imageCompression(file, options);

            // Preserve the original filename by adding it as a property
            compressedFile.originalName = file.name;

            return compressedFile;

        } catch (error) {
            console.error('Error compressing image:', error);
            // Only show error messages
            showCompressionStatus('Error compressing image. Original file will be used.', 'error', inputId);
            return null;
        }
    }

    function showCompressionStatus(message, type, inputId) {
        const statusIndicator = document.getElementById(`compression-status-${inputId}`);
        if (!statusIndicator) return;

        statusIndicator.className = 'mt-2 alert alert-sm';

        // Remove existing type classes
        statusIndicator.classList.remove('alert-info', 'alert-success', 'alert-warning', 'alert-error');

        // Add appropriate type class
        switch(type) {
            case 'info':
                statusIndicator.classList.add('alert-info');
                statusIndicator.innerHTML = `
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-spinner fa-spin"></i>
                        <span>${message}</span>
                    </div>
                `;
                break;
            case 'success':
                statusIndicator.classList.add('alert-success');
                statusIndicator.innerHTML = `
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-check-circle"></i>
                        <span>${message}</span>
                    </div>
                `;
                break;
            case 'warning':
                statusIndicator.classList.add('alert-warning');
                statusIndicator.innerHTML = `
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-exclamation-triangle"></i>
                        <span>${message}</span>
                    </div>
                `;
                break;
            case 'error':
                statusIndicator.classList.add('alert-error');
                statusIndicator.innerHTML = `
                    <div class="flex items-center gap-2">
                        <i class="fa-sharp fa-exclamation-circle"></i>
                        <span>${message}</span>
                    </div>
                `;
                break;
        }

        statusIndicator.classList.remove('hidden');
    }

    function hideCompressionStatus(inputId) {
        const statusIndicator = document.getElementById(`compression-status-${inputId}`);
        if (statusIndicator) {
            statusIndicator.classList.add('hidden');
        }
    }

    function initializeSubmitButtons() {
        // Find all submit buttons in the form
        const buttons = form.querySelectorAll('button[type="submit"]');
        submitButtons = Array.from(buttons);
    }

    function setCompressionState(compressing) {
        isCompressing = compressing;

        submitButtons.forEach(button => {
            if (compressing) {
                // Disable button and gray it out
                button.disabled = true;
                button.classList.add('btn-disabled');

                // Add spinner inside button on the left side
                let spinner = button.querySelector('.compression-spinner');
                if (!spinner) {
                    spinner = document.createElement('span');
                    spinner.className = 'compression-spinner loading loading-spinner loading-xs mr-2';
                    // Insert at the beginning of the button
                    button.insertBefore(spinner, button.firstChild);
                }
                spinner.classList.remove('hidden');

            } else {
                // Re-enable button and remove gray state
                button.disabled = false;
                button.classList.remove('btn-disabled');

                // Remove spinner from button
                const spinner = button.querySelector('.compression-spinner');
                if (spinner) {
                    spinner.remove();
                }
            }
        });
    }
});
