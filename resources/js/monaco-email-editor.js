// Initialize Monaco Editor for Email Templates
document.addEventListener('DOMContentLoaded', async function() {
    // Dynamically import Monaco Editor for better performance
    const monaco = await import('monaco-editor');
    
    const textareaElement = document.querySelector('textarea[name="body_html"]');
    if (!textareaElement) return;

    // Create editor container
    const editorContainer = document.createElement('div');
    editorContainer.style.height = '500px';
    editorContainer.style.border = '1px solid #e5e7eb';
    editorContainer.style.borderRadius = '8px';
    editorContainer.classList.add('monaco-editor-container');

    // Hide original textarea
    textareaElement.style.display = 'none';
    
    // Insert editor container after the textarea
    textareaElement.parentNode.insertBefore(editorContainer, textareaElement.nextSibling);

    // Configure Monaco Editor
    const editor = monaco.editor.create(editorContainer, {
        value: textareaElement.value,
        language: 'html',
        theme: 'vs-dark',
        automaticLayout: true,
        minimap: {
            enabled: true
        },
        fontSize: 14,
        lineNumbers: 'on',
        wordWrap: 'on',
        formatOnPaste: true,
        formatOnType: true,
        scrollBeyondLastLine: false,
        folding: true,
        bracketMatching: 'always',
        autoClosingBrackets: 'always',
        autoClosingQuotes: 'always',
        autoIndent: 'full',
        tabSize: 2,
        insertSpaces: true,
        detectIndentation: true,
        renderWhitespace: 'boundary',
        renderControlCharacters: true,
        renderIndentGuides: true,
        showFoldingControls: 'always',
        smoothScrolling: true,
        cursorSmoothCaretAnimation: 'on',
        suggest: {
            showKeywords: true,
            showSnippets: true
        }
    });

    // Sync editor content with textarea
    editor.onDidChangeModelContent(function() {
        textareaElement.value = editor.getValue();
    });

    // Add some useful HTML snippets
    monaco.languages.registerCompletionItemProvider('html', {
        provideCompletionItems: function(model, position) {
            const suggestions = [
                {
                    label: 'email-template',
                    kind: monaco.languages.CompletionItemKind.Snippet,
                    insertText: [
                        '<!DOCTYPE html>',
                        '<html>',
                        '<head>',
                        '    <meta charset="UTF-8">',
                        '    <meta name="viewport" content="width=device-width, initial-scale=1.0">',
                        '    <title>Email Template</title>',
                        '</head>',
                        '<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">',
                        '    $0',
                        '</body>',
                        '</html>'
                    ].join('\n'),
                    documentation: 'Basic HTML email template structure'
                },
                {
                    label: 'variable-placeholder',
                    kind: monaco.languages.CompletionItemKind.Snippet,
                    insertText: '{$0}',
                    documentation: 'Template variable placeholder'
                },
                {
                    label: 'email-button',
                    kind: monaco.languages.CompletionItemKind.Snippet,
                    insertText: [
                        '<table role="presentation" style="border-collapse: collapse; margin: 20px 0;">',
                        '    <tr>',
                        '        <td style="border-radius: 4px; background-color: #007bff; padding: 12px 24px;">',
                        '            <a href="$1" style="color: white; text-decoration: none; font-weight: bold;">$0</a>',
                        '        </td>',
                        '    </tr>',
                        '</table>'
                    ].join('\n'),
                    documentation: 'Email-safe button'
                },
                {
                    label: 'email-header',
                    kind: monaco.languages.CompletionItemKind.Snippet,
                    insertText: [
                        '<div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-bottom: 1px solid #dee2e6;">',
                        '    <h1 style="margin: 0; color: #333;">$0</h1>',
                        '</div>'
                    ].join('\n'),
                    documentation: 'Email header section'
                },
                {
                    label: 'email-footer',
                    kind: monaco.languages.CompletionItemKind.Snippet,
                    insertText: [
                        '<div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6; margin-top: 30px;">',
                        '    <p style="margin: 0; font-size: 14px; color: #666;">$0</p>',
                        '</div>'
                    ].join('\n'),
                    documentation: 'Email footer section'
                }
            ];

            return { suggestions: suggestions };
        }
    });

    // Add toolbar for common actions
    const toolbar = document.createElement('div');
    toolbar.style.marginBottom = '10px';
    toolbar.style.display = 'flex';
    toolbar.style.gap = '10px';
    toolbar.style.alignItems = 'center';
    toolbar.classList.add('monaco-toolbar');

    const formatButton = document.createElement('button');
    formatButton.type = 'button';
    formatButton.textContent = 'Format HTML';
    formatButton.classList.add('btn', 'btn-sm', 'btn-outline');
    formatButton.onclick = function() {
        editor.getAction('editor.action.formatDocument').run();
    };

    const themeToggle = document.createElement('button');
    themeToggle.type = 'button';
    themeToggle.textContent = 'Toggle Theme';
    themeToggle.classList.add('btn', 'btn-sm', 'btn-outline');
    themeToggle.onclick = function() {
        const currentTheme = editor.getOption(monaco.editor.EditorOption.theme);
        const newTheme = currentTheme === 'vs-dark' ? 'vs' : 'vs-dark';
        monaco.editor.setTheme(newTheme);
    };

    const previewButton = document.createElement('button');
    previewButton.type = 'button';
    previewButton.textContent = 'Preview HTML';
    previewButton.classList.add('btn', 'btn-sm', 'btn-info');
    previewButton.onclick = function() {
        const html = editor.getValue();
        const previewWindow = window.open('', '_blank');
        previewWindow.document.write(html);
        previewWindow.document.close();
    };

    toolbar.appendChild(formatButton);
    toolbar.appendChild(themeToggle);
    toolbar.appendChild(previewButton);

    editorContainer.parentNode.insertBefore(toolbar, editorContainer);

    // Handle form submission
    const form = textareaElement.closest('form');
    if (form) {
        form.addEventListener('submit', function() {
            textareaElement.value = editor.getValue();
        });
    }

    // Resize observer to handle responsive layout
    const resizeObserver = new ResizeObserver(function() {
        editor.layout();
    });
    resizeObserver.observe(editorContainer);

    // Add keyboard shortcut info
    const shortcutInfo = document.createElement('div');
    shortcutInfo.style.fontSize = '12px';
    shortcutInfo.style.color = '#666';
    shortcutInfo.style.marginTop = '5px';
    shortcutInfo.innerHTML = '<strong>Shortcuts:</strong> Ctrl+Shift+F = Format, Ctrl+/ = Toggle Comment, Ctrl+D = Select Word, Alt+Shift+F = Format Selection';
    editorContainer.parentNode.insertBefore(shortcutInfo, editorContainer.nextSibling);

    // Clean up on page unload
    window.addEventListener('beforeunload', function() {
        if (editor) {
            editor.dispose();
        }
        if (resizeObserver) {
            resizeObserver.disconnect();
        }
    });
});