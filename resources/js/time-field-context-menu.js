/**
 * Time Field Context Menu
 * Provides right-click context menu for time input fields with options to:
 * - Manually input time values
 * - Select time using the clock picker
 */

class TimeFieldContextMenu {
    constructor() {
        console.log('TimeFieldContextMenu constructor called');
        this.contextMenu = null;
        this.currentTarget = null;
        this.manualInputModal = null;
        this.initializeContextMenu();
        this.initializeManualInputModal();
        this.bindEvents();
        console.log('TimeFieldContextMenu initialized');
    }

    initializeContextMenu() {
        // Create context menu element
        this.contextMenu = document.createElement('div');
        this.contextMenu.id = 'time-field-context-menu';
        this.contextMenu.className = 'fixed bg-base-100 border border-base-300 rounded-lg shadow-lg py-2 min-w-[160px] hidden';
        this.contextMenu.style.zIndex = '9999';
        this.contextMenu.innerHTML = `
            <div class="menu-item px-3 py-2 hover:bg-base-200 cursor-pointer flex items-center gap-2" data-action="manual">
                <i class="fa-sharp fa-solid fa-keyboard text-sm"></i>
                <span>Manual Input</span>
            </div>
            <div class="menu-item px-3 py-2 hover:bg-base-200 cursor-pointer flex items-center gap-2" data-action="clock">
                <i class="fa-sharp fa-solid fa-clock text-sm"></i>
                <span>Select On Clock</span>
            </div>
        `;
        document.body.appendChild(this.contextMenu);
    }

    initializeManualInputModal() {
        // Create manual input modal
        this.manualInputModal = document.createElement('div');
        this.manualInputModal.id = 'manual-time-input-modal';
        this.manualInputModal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/50 hidden';
        this.manualInputModal.innerHTML = `
            <div class="card bg-base-100 shadow-xl w-full max-w-md mx-4">
                <div class="card-body">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="card-title text-lg">
                            <i class="fa-sharp fa-solid fa-keyboard text-primary mr-2"></i>
                            Manual Time Input
                        </h3>
                        <button class="btn btn-sm btn-circle btn-ghost" data-action="close">
                            <i class="fa-sharp fa-solid fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="form-control mb-4">
                        <label class="label">
                            <span class="label-text">Enter Time</span>
                        </label>
                        <input type="text" 
                               id="manual-time-input" 
                               class="input input-bordered w-full" 
                               placeholder="e.g., 9:30 AM, 14:45, 2:15:30 PM"
                               autocomplete="off">
                        <div class="label">
                            <span class="label-text-alt text-sm">
                                <i class="fa-sharp fa-solid fa-info-circle mr-1"></i>
                                Supports: 12-hour (9:30 AM), 24-hour (14:45), with seconds (2:15:30 PM)
                            </span>
                        </div>
                    </div>
                    
                    <div class="card-actions justify-end gap-2">
                        <button class="btn btn-ghost" data-action="cancel">Cancel</button>
                        <button class="btn btn-primary" data-action="apply">
                            <i class="fa-sharp fa-solid fa-check mr-1"></i>
                            Apply
                        </button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(this.manualInputModal);
    }

    bindEvents() {
        // Context menu item clicks
        this.contextMenu.addEventListener('click', (e) => {
            const menuItem = e.target.closest('.menu-item');
            if (!menuItem) return;

            e.preventDefault();
            e.stopPropagation();

            const action = menuItem.dataset.action;
            this.hideContextMenu();

            if (action === 'manual') {
                this.showManualInput();
            } else if (action === 'clock') {
                this.triggerClockPicker();
            }
        });

        // Manual input modal events
        this.manualInputModal.addEventListener('click', (e) => {
            const target = e.target.closest('[data-action]');
            if (!target) return;

            e.preventDefault();
            e.stopPropagation();

            const action = target.dataset.action;
            
            if (action === 'close' || action === 'cancel') {
                this.hideManualInput();
            } else if (action === 'apply') {
                this.applyManualTime();
            }
        });

        // Close modal when clicking outside
        this.manualInputModal.addEventListener('click', (e) => {
            if (e.target === this.manualInputModal) {
                this.hideManualInput();
            }
        });

        // Manual input field events
        const manualInput = this.manualInputModal.querySelector('#manual-time-input');
        manualInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.applyManualTime();
            } else if (e.key === 'Escape') {
                this.hideManualInput();
            }
        });

        // Hide context menu when clicking anywhere else
        document.addEventListener('click', () => {
            this.hideContextMenu();
        });

        // Hide context menu on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideContextMenu();
                this.hideManualInput();
            }
        });
    }

    showContextMenu(e, target) {
        console.log('showContextMenu called, preventing default');
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();

        this.currentTarget = target;
        
        // Position the context menu first
        let x = e.clientX;
        let y = e.clientY;

        console.log('Positioning context menu at:', x, y);
        
        this.contextMenu.style.left = `${x}px`;
        this.contextMenu.style.top = `${y}px`;
        
        // Show the context menu
        this.contextMenu.classList.remove('hidden');
        
        console.log('Context menu should now be visible at:', this.contextMenu.style.left, this.contextMenu.style.top);
        console.log('Context menu element:', this.contextMenu);

        // Adjust position if menu would go outside viewport (after showing)
        const menuRect = this.contextMenu.getBoundingClientRect();
        
        if (x + menuRect.width > window.innerWidth) {
            x = window.innerWidth - menuRect.width - 10;
            this.contextMenu.style.left = `${x}px`;
        }
        if (y + menuRect.height > window.innerHeight) {
            y = window.innerHeight - menuRect.height - 10;
            this.contextMenu.style.top = `${y}px`;
        }
    }

    hideContextMenu() {
        this.contextMenu.classList.add('hidden');
    }

    showManualInput() {
        if (!this.currentTarget) return;

        const manualInput = this.manualInputModal.querySelector('#manual-time-input');
        
        // Pre-fill with current value if available
        const currentValue = this.currentTarget.value || '';
        manualInput.value = currentValue;

        this.manualInputModal.classList.remove('hidden');
        
        // Focus and select the input
        setTimeout(() => {
            manualInput.focus();
            manualInput.select();
        }, 100);
    }

    hideManualInput() {
        this.manualInputModal.classList.add('hidden');
        this.currentTarget = null;
    }

    applyManualTime() {
        if (!this.currentTarget) return;

        const manualInput = this.manualInputModal.querySelector('#manual-time-input');
        const inputValue = manualInput.value.trim();

        if (!inputValue) {
            this.showError('Please enter a time value');
            return;
        }

        // Parse and validate the time input
        const parsedTime = this.parseTimeInput(inputValue);
        if (!parsedTime) {
            this.showError('Invalid time format. Please use formats like "9:30 AM", "14:45", or "2:15:30 PM"');
            return;
        }

        // Update the target input
        this.currentTarget.value = parsedTime.display;
        
        // Trigger change event
        const changeEvent = new Event('change', { bubbles: true });
        this.currentTarget.dispatchEvent(changeEvent);

        // If there's a hidden input, update it too
        const hiddenInput = this.getHiddenInput(this.currentTarget);
        if (hiddenInput) {
            hiddenInput.value = parsedTime.value24;
            const hiddenChangeEvent = new Event('change', { bubbles: true });
            hiddenInput.dispatchEvent(hiddenChangeEvent);
        }

        // If there's an associated onTimeChange callback, call it
        if (this.currentTarget.onTimeChange) {
            this.currentTarget.onTimeChange(parsedTime.value24, parsedTime.display);
        }

        this.hideManualInput();
        this.showSuccess('Time updated successfully');
    }

    triggerClockPicker() {
        if (!this.currentTarget) return;

        // Trigger a click event on the target input to open the clock picker
        const clickEvent = new Event('click', { bubbles: true });
        this.currentTarget.dispatchEvent(clickEvent);
    }

    parseTimeInput(input) {
        const timeStr = input.trim().toLowerCase();
        
        // Pattern for 12-hour format with AM/PM (optional seconds)
        const pattern12 = /^(\d{1,2}):(\d{2})(?::(\d{2}))?\s*(am|pm)$/i;
        
        // Pattern for 24-hour format (optional seconds)
        const pattern24 = /^(\d{1,2}):(\d{2})(?::(\d{2}))?$/;

        let hour, minute, second = 0;
        let is12Hour = false;
        let isPM = false;

        const match12 = timeStr.match(pattern12);
        const match24 = timeStr.match(pattern24);

        if (match12) {
            // 12-hour format
            hour = parseInt(match12[1]);
            minute = parseInt(match12[2]);
            second = match12[3] ? parseInt(match12[3]) : 0;
            isPM = match12[4] === 'pm';
            is12Hour = true;

            // Validate 12-hour format
            if (hour < 1 || hour > 12) return null;
        } else if (match24) {
            // 24-hour format
            hour = parseInt(match24[1]);
            minute = parseInt(match24[2]);
            second = match24[3] ? parseInt(match24[3]) : 0;

            // Validate 24-hour format
            if (hour < 0 || hour > 23) return null;
        } else {
            return null; // Invalid format
        }

        // Validate minute and second
        if (minute < 0 || minute > 59 || second < 0 || second > 59) {
            return null;
        }

        // Convert to 24-hour format for internal use
        let hour24 = hour;
        if (is12Hour) {
            if (isPM && hour !== 12) {
                hour24 += 12;
            } else if (!isPM && hour === 12) {
                hour24 = 0;
            }
        }

        // Format for display (12-hour with AM/PM)
        const displayHour = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
        const displayPeriod = hour24 >= 12 ? 'PM' : 'AM';
        const display = `${displayHour}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')} ${displayPeriod}`;

        // Format for 24-hour value
        const value24 = `${hour24.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`;

        return {
            display: display,
            value24: value24,
            hour: hour24,
            minute: minute,
            second: second
        };
    }

    getHiddenInput(input) {
        // Look for associated hidden input
        const hiddenId = input.id + '_hidden';
        return document.getElementById(hiddenId);
    }

    showError(message) {
        this.showToast('error', message);
    }

    showSuccess(message) {
        this.showToast('success', message);
    }

    showToast(type, message) {
        // Try to find the showToast function from timecard-edit.js
        if (typeof showToast === 'function') {
            showToast(type, message);
            return;
        }
        
        // Use existing toast system if available
        if (window.showToast) {
            window.showToast(type, message);
            return;
        }

        // Simple toast fallback
        this.createSimpleToast(type, message);
    }

    createSimpleToast(type, message) {
        // Create a simple toast if no system is available
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast toast-top toast-end z-50';
            document.body.appendChild(toastContainer);
        }

        const toast = document.createElement('div');
        toast.className = `alert ${this.getAlertClass(type)} mb-2`;
        toast.innerHTML = `
            <i class="${this.getIconClass(type)}"></i>
            <span>${message}</span>
        `;

        toastContainer.appendChild(toast);

        // Remove the toast after 3 seconds
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    getAlertClass(type) {
        switch (type) {
            case 'success': return 'alert-success';
            case 'error': return 'alert-error';
            case 'warning': return 'alert-warning';
            case 'info': return 'alert-info';
            default: return 'alert-info';
        }
    }

    getIconClass(type) {
        switch (type) {
            case 'success': return 'fa-sharp fa-solid fa-check-circle';
            case 'error': return 'fa-sharp fa-solid fa-exclamation-circle';
            case 'warning': return 'fa-sharp fa-solid fa-exclamation-triangle';
            case 'info': return 'fa-sharp fa-solid fa-info-circle';
            default: return 'fa-sharp fa-solid fa-info-circle';
        }
    }
}

// Initialize the context menu system
let timeFieldContextMenu = null;

/**
 * Enable context menu for a time input field
 * @param {string|HTMLElement} inputSelector - CSS selector or DOM element
 * @param {Object} options - Configuration options
 */
export function enableTimeFieldContextMenu(inputSelector, options = {}) {
    console.log('enableTimeFieldContextMenu called with:', inputSelector, options);
    
    // Initialize the context menu system if not already done
    if (!timeFieldContextMenu) {
        console.log('Initializing TimeFieldContextMenu');
        timeFieldContextMenu = new TimeFieldContextMenu();
    }

    const input = typeof inputSelector === 'string' ? 
        document.querySelector(inputSelector) : inputSelector;

    if (!input) {
        console.warn('Time field not found:', inputSelector);
        return;
    }

    console.log('Time field found, adding context menu to:', input);

    // Store options on the input element
    input.timeFieldOptions = options;

    // Add right-click event listener
    input.addEventListener('contextmenu', (e) => {
        console.log('Context menu triggered on:', input);
        e.preventDefault();
        e.stopPropagation();
        timeFieldContextMenu.showContextMenu(e, input);
    }, true); // Use capture phase

    // Store callback if provided
    if (options.onTimeChange) {
        input.onTimeChange = options.onTimeChange;
    }

    console.log('Context menu enabled for input:', input.id);
}

/**
 * Disable context menu for a time input field
 * @param {string|HTMLElement} inputSelector - CSS selector or DOM element
 */
export function disableTimeFieldContextMenu(inputSelector) {
    const input = typeof inputSelector === 'string' ? 
        document.querySelector(inputSelector) : inputSelector;

    if (!input) return;

    // Remove the event listener by cloning the element
    const newInput = input.cloneNode(true);
    input.parentNode.replaceChild(newInput, input);
}

// Export the main class for advanced usage
export { TimeFieldContextMenu };