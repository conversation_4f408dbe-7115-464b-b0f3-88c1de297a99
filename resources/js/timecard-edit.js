/**
 * Timecard Edit Page JavaScript
 * Provides dynamic editing functionality for timecard punches
 */

// Make functions globally available
window.editPunchTime = function(element, punchId, currentTime) {
    console.log('editPunchTime called', element, punchId, currentTime);
    
    // Don't do anything if we're already editing
    if (element.querySelector('input')) {
        return;
    }
    
    const currentDisplay = element.textContent.trim();
    
    // Extract time components from ISO string
    let timeValue = '';
    try {
        const date = new Date(currentTime);
        if (!isNaN(date.getTime())) {
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            timeValue = `${hours}:${minutes}:${seconds}`;
        }
    } catch (error) {
        console.error('Error parsing date:', error);
        timeValue = currentTime.substring(11, 19);
    }
    
    // Create time input
    const input = document.createElement('input');
    input.type = 'time';
    input.value = timeValue;
    input.className = 'input input-bordered input-sm w-32';
    input.step = '1';
    
    // Replace content with input
    element.innerHTML = '';
    element.appendChild(input);
    input.focus();
    
    // Handle blur (save)
    input.addEventListener('blur', function() {
        const newTime = input.value;
        if (newTime !== timeValue) {
            savePunchTimeChange(punchId, newTime, element, currentDisplay, currentTime);
        } else {
            element.textContent = currentDisplay;
        }
    });
    
    // Handle keyboard
    input.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            input.blur();
        } else if (e.key === 'Escape') {
            element.textContent = currentDisplay;
        }
    });
};

document.addEventListener('DOMContentLoaded', function() {
    console.log('Timecard edit page loaded');

    // Initialize editable time punches
    initializeEditablePunches();

    // Initialize the add punch form
    initializeAddPunchForm();
    
    // Debug: Check if elements are found
    const editableElements = document.querySelectorAll('.editable-punch-time');
    console.log('Found editable time elements:', editableElements.length);
    editableElements.forEach((el, index) => {
        console.log(`Element ${index}:`, el, 'Dataset:', el.dataset);
    });

    // Using browser-based time input instead of React time picker

    // Add CSRF token to meta if it doesn't exist
    if (!document.querySelector('meta[name="csrf-token"]')) {
        console.warn('CSRF token meta tag not found, adding it');
        const meta = document.createElement('meta');
        meta.name = 'csrf-token';
        meta.content = document.querySelector('input[name="_token"]')?.value || '';
        document.head.appendChild(meta);
    }

    // Create toast container if it doesn't exist
    if (!document.getElementById('toast-container')) {
        const toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast toast-top toast-end z-50';
        document.body.appendChild(toastContainer);
    }

    // Make showToast globally accessible
    window.showToast = showToast;
});

// Using browser-based time input - no external dependencies needed

/**
 * Initialize click-to-edit functionality for time punches
 */
function initializeEditablePunches() {
    const punchTimeElements = document.querySelectorAll('.editable-punch-time');
    const punchNoteElements = document.querySelectorAll('.editable-punch-note');

    console.log('Found', punchTimeElements.length, 'editable time elements');

    // Add click event listeners to punch time elements
    punchTimeElements.forEach(element => {
        console.log('Adding click listener to time element:', element);
        
        element.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('Time element clicked!', this);

            // Don't do anything if we're already editing
            if (element.querySelector('input')) {
                console.log('Already editing, returning');
                return;
            }

            const punchId = this.getAttribute('data-punch-id');
            const currentTime = this.getAttribute('data-punch-time');
            const currentDisplay = this.textContent.trim();

            console.log('Editing time for punch ID:', punchId);
            console.log('Current time from dataset:', currentTime);

            // Extract time components from ISO string
            let timeValue = '';
            let originalDate = null;
            try {
                // Try to parse the ISO date string
                originalDate = new Date(currentTime);
                if (!isNaN(originalDate.getTime())) {
                    // Format as HH:MM
                    const hours = String(originalDate.getHours()).padStart(2, '0');
                    const minutes = String(originalDate.getMinutes()).padStart(2, '0');
                    const seconds = String(originalDate.getSeconds()).padStart(2, '0');
                    timeValue = `${hours}:${minutes}:${seconds}`;
                } else {
                    console.error('Invalid date:', currentTime);
                    timeValue = currentTime.substring(11, 19); // Fallback to string extraction (HH:MM:SS)
                }
            } catch (error) {
                console.error('Error parsing date:', error);
                timeValue = currentTime.substring(11, 19); // Fallback to string extraction (HH:MM:SS)
            }

            console.log('Extracted time value:', timeValue);

            // Create a simple time input
            const input = document.createElement('input');
            input.type = 'time';
            input.value = timeValue;
            input.className = 'input input-bordered input-sm w-32';
            input.step = '1'; // Allow seconds

            // Replace the text with the time input
            this.innerHTML = '';
            this.appendChild(input);

            // Focus the input
            input.focus();

            // Handle input blur (save changes)
            input.addEventListener('blur', function() {
                const newTime = input.value;
                if (newTime !== timeValue) {
                    savePunchTimeChange(punchId, newTime, element, currentDisplay, currentTime);
                } else {
                    // No change, restore original text
                    element.textContent = currentDisplay;
                }
            });

            // Handle enter key to save
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    input.blur();
                } else if (e.key === 'Escape') {
                    // Restore original text without saving
                    element.textContent = currentDisplay;
                }
            });
        });
    });

    // Add click event listeners to punch note elements
    punchNoteElements.forEach(element => {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const punchId = this.getAttribute('data-punch-id');
            const currentNote = this.textContent.trim();

            // Create an input element to replace the text
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentNote;
            input.className = 'input input-bordered input-sm w-full';
            input.placeholder = 'Add a note...';

            // Replace the text with the input
            this.innerHTML = '';
            this.appendChild(input);

            // Focus the input
            input.focus();

            // Handle input blur (save changes)
            input.addEventListener('blur', function() {
                savePunchNoteChange(punchId, input.value, element, currentNote);
            });

            // Handle enter key
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    input.blur();
                } else if (e.key === 'Escape') {
                    // Restore original text without saving
                    element.textContent = currentNote || 'Add a note...';
                }
            });
        });
    });
}

/**
 * Save a punch time change via AJAX
 */
window.savePunchTimeChange = function savePunchTimeChange(punchId, newTime, element, originalDisplay, originalDateTime) {
    console.log('Saving new time:', newTime, 'for punch ID:', punchId);

    // Get the date from the original datetime (we're only changing the time portion)
    let punchDate;
    try {
        // Try to parse the original date
        const date = new Date(originalDateTime);
        if (!isNaN(date.getTime())) {
            // Format as YYYY-MM-DD
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            punchDate = `${year}-${month}-${day}`;
        } else {
            console.error('Invalid original date:', originalDateTime);
            punchDate = originalDateTime.substring(0, 10); // Fallback
        }
    } catch (error) {
        console.error('Error parsing original date:', error);
        punchDate = originalDateTime.substring(0, 10); // Fallback
    }

    // Construct the new datetime string in the format expected by the server
    // newTime is already in HH:MM:SS format, so we don't need to add seconds
    const newDateTime = `${punchDate} ${newTime}`;
    console.log('New datetime to send to server:', newDateTime);

    // Show loading indicator
    element.innerHTML = '<span class="loading loading-spinner loading-xs"></span>';

    // Send AJAX request to update the punch
    fetch(`/api/time-punches/${punchId}/ajax-update`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            punch_time: newDateTime
        })
    })
    .then(response => {
        console.log('Server response status:', response.status);
        if (!response.ok) {
            return response.json().then(errorData => {
                throw new Error(errorData.message || 'Server error: ' + response.status);
            });
        }
        return response.json();
    })
    .then(data => {
        console.log('Server response data:', data);
        if (data.success) {
            // Update the element with the new formatted time
            element.textContent = data.formatted_time;
            element.setAttribute('data-punch-time', data.punch_time);

            // Update the timecard totals
            if (data.timecard) {
                updateTimecardTotals(data.timecard);
            }

            // Show success toast
            showToast('success', 'Time updated successfully');
        } else {
            // Show error and revert to original
            element.textContent = originalDisplay;
            showToast('error', data.message || 'Failed to update time');
        }
    })
    .catch(error => {
        console.error('Error updating punch time:', error);
        element.textContent = originalDisplay;
        showToast('error', 'An error occurred while updating the time: ' + error.message);
    });
}

/**
 * Save a punch note change via AJAX
 */
function savePunchNoteChange(punchId, newNote, element, originalNote) {
    // Show loading indicator
    element.innerHTML = '<span class="loading loading-spinner loading-xs"></span>';

    // Send AJAX request to update the punch
    fetch(`/api/time-punches/${punchId}/ajax-update`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            notes: newNote
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Update the element with the new note
            element.textContent = newNote || 'Add a note...';

            // Show success toast
            showToast('success', 'Note updated successfully');
        } else {
            // Show error and revert to original
            element.textContent = originalNote || 'Add a note...';
            showToast('error', data.message || 'Failed to update note');
        }
    })
    .catch(error => {
        console.error('Error updating punch note:', error);
        element.textContent = originalNote || 'Add a note...';
        showToast('error', 'An error occurred while updating the note');
    });
}

/**
 * Update the timecard totals displayed on the page
 */
function updateTimecardTotals(timecard) {
    const totalHoursElement = document.getElementById('total-hours');
    const breakHoursElement = document.getElementById('break-hours');
    const sickHoursElement = document.getElementById('sick-hours');
    const vacationHoursElement = document.getElementById('vacation-hours');
    const billableHoursElement = document.getElementById('billable-hours');

    if (totalHoursElement) {
        totalHoursElement.textContent = timecard.total_hours;
    }

    if (breakHoursElement) {
        breakHoursElement.textContent = timecard.total_break_hours;
    }

    if (sickHoursElement && timecard.total_sick_time) {
        sickHoursElement.textContent = timecard.total_sick_time;
    }

    if (vacationHoursElement && timecard.total_vacation_time) {
        vacationHoursElement.textContent = timecard.total_vacation_time;
    }

    if (billableHoursElement) {
        // Calculate billable hours (work hours - break hours + PTO)
        const workHours = parseFloat(timecard.net_hours) || 0;
        const sickHours = parseFloat(timecard.total_sick_time) || 0;
        const vacationHours = parseFloat(timecard.total_vacation_time) || 0;
        const billableHours = (workHours + sickHours + vacationHours).toFixed(2);
        billableHoursElement.textContent = billableHours;
    }
}

/**
 * Show a toast message
 * @param {string} type - The type of toast (success, error, info, warning)
 * @param {string} message - The message to display
 */
export function showToast(type, message) {
    const toastContainer = document.getElementById('toast-container');
    if (!toastContainer) return;

    const toast = document.createElement('div');
    toast.className = `alert ${getAlertClass(type)} mb-2`;

    const icon = document.createElement('i');
    icon.className = getIconClass(type);
    toast.appendChild(icon);

    const span = document.createElement('span');
    span.textContent = message;
    toast.appendChild(span);

    toastContainer.appendChild(toast);

    // Remove the toast after 5 seconds
    setTimeout(() => {
        toast.classList.add('opacity-0');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 5000);
}

/**
 * Get the appropriate alert class for the toast type
 */
function getAlertClass(type) {
    switch (type) {
        case 'success': return 'alert-success';
        case 'error': return 'alert-error';
        case 'warning': return 'alert-warning';
        case 'info': return 'alert-info';
        default: return 'alert-info';
    }
}

/**
 * Get the appropriate icon class for the toast type
 */
function getIconClass(type) {
    switch (type) {
        case 'success': return 'fa-sharp fa-solid fa-check-circle';
        case 'error': return 'fa-sharp fa-solid fa-exclamation-circle';
        case 'warning': return 'fa-sharp fa-solid fa-exclamation-triangle';
        case 'info': return 'fa-sharp fa-solid fa-info-circle';
        default: return 'fa-sharp fa-solid fa-info-circle';
    }
}

/**
 * Initialize the add punch form with AJAX submission
 */
function initializeAddPunchForm() {
    const addPunchForm = document.getElementById('add-punch-form');

    if (addPunchForm) {
        // Handle punch type change to show/hide notes hint
        const punchTypeSelect = addPunchForm.querySelector('select[name="type"]');
        const notesHint = document.getElementById('notes-hint');
        const notesTextarea = document.getElementById('punch-notes');

        if (punchTypeSelect && notesHint && notesTextarea) {
            punchTypeSelect.addEventListener('change', function() {
                const isPtoPunch = ['sick_time', 'vacation_time'].includes(this.value);
                notesHint.style.display = isPtoPunch ? 'block' : 'none';

                if (isPtoPunch) {
                    notesTextarea.placeholder = 'Enter hours in HH:MM:SS format (e.g., 8:00:00)';
                    // If the field is empty, pre-fill with a default value
                    if (!notesTextarea.value.trim()) {
                        notesTextarea.value = '8:00:00';
                    }
                } else {
                    notesTextarea.placeholder = 'Add notes about this punch';
                    // Clear the field if it contains a time format
                    if (/^\d{1,2}:\d{2}(:\d{2})?$/.test(notesTextarea.value)) {
                        notesTextarea.value = '';
                    }
                }
            });
        }

        addPunchForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitButton = this.querySelector('button[type="submit"]');

            // Check if this is a PTO punch and validate the notes format
            const punchType = formData.get('type');
            const notes = formData.get('notes');
            const isPtoPunch = ['sick_time', 'vacation_time'].includes(punchType);

            if (isPtoPunch) {
                const timeFormatRegex = /^\d{1,2}:\d{2}(:\d{2})?$/;
                if (!notes || !timeFormatRegex.test(notes)) {
                    showToast('error', 'For PTO entries, you must enter hours in HH:MM:SS format (e.g., 8:00:00)');
                    return;
                }
            }

            // Combine punch_date and punch_time into a full datetime string
            const punchDate = formData.get('punch_date');
            const punchTime = formData.get('punch_time');
            
            console.log('DEBUG: Raw form data:');
            console.log('- punch_date:', punchDate);
            console.log('- punch_time:', punchTime);
            console.log('- punch_time length:', punchTime ? punchTime.length : 'null');
            
            // Handle different time formats (HH:MM or HH:MM:SS)
            let fullDateTime;
            if (punchTime.includes(':') && punchTime.split(':').length === 2) {
                // Browser returned HH:MM format, add seconds
                fullDateTime = `${punchDate} ${punchTime}:00`;
            } else if (punchTime.includes(':') && punchTime.split(':').length === 3) {
                // Browser returned HH:MM:SS format
                fullDateTime = `${punchDate} ${punchTime}`;
            } else {
                // Fallback - assume it needs seconds
                fullDateTime = `${punchDate} ${punchTime}:00`;
            }
            
            console.log('DEBUG: Combined datetime:', fullDateTime);
            
            // Update the FormData with the combined datetime
            formData.set('punch_time', fullDateTime);
            formData.delete('punch_date'); // Remove the separate date field

            // Disable the submit button and show loading
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Adding...';

            // Send AJAX request to add the punch
            fetch(this.action, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                },
                body: formData
            })
            .then(response => {
                console.log('DEBUG: Server response status:', response.status);
                if (!response.ok) {
                    return response.json().then(errorData => {
                        console.log('DEBUG: Server error data:', errorData);
                        throw new Error(errorData.message || `Server error: ${response.status}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Reload the page to show the new punch
                    showToast('success', 'Punch added successfully');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    // Show error
                    submitButton.disabled = false;
                    submitButton.innerHTML = '<i class="fa-sharp fa-solid fa-plus mr-2"></i> Add Punch';
                    showToast('error', data.message || 'Failed to add punch');
                }
            })
            .catch(error => {
                console.error('Error adding punch:', error);
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fa-sharp fa-solid fa-plus mr-2"></i> Add Punch';
                showToast('error', 'An error occurred while adding the punch');
            });
        });
    }
}
