/**
 * Edit Punch Page JavaScript
 * Handles the edit punch form submission
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Edit punch page loaded');

    // Initialize the edit punch form
    initializeEditPunchForm();
});

/**
 * Initialize the edit punch form
 */
function initializeEditPunchForm() {
    const editPunchForm = document.querySelector('form[action^="/time-punches/"]');

    if (editPunchForm) {
        // Handle punch type change to show/hide notes hint
        const punchTypeSelect = document.getElementById('punch-type');
        const notesHint = document.getElementById('notes-hint');
        const notesTextarea = document.getElementById('punch-notes');

        if (punchTypeSelect && notesHint && notesTextarea) {
            punchTypeSelect.addEventListener('change', function() {
                const isPtoPunch = ['sick_time', 'vacation_time'].includes(this.value);
                notesHint.style.display = isPtoPunch ? 'block' : 'none';

                if (isPtoPunch) {
                    notesTextarea.placeholder = 'Enter hours in HH:MM:SS format (e.g., 8:00:00)';
                    // If the field is empty, pre-fill with a default value
                    if (!notesTextarea.value.trim()) {
                        notesTextarea.value = '8:00:00';
                    }
                } else {
                    notesTextarea.placeholder = 'Add notes about this punch';
                    // Clear the field if it contains a time format
                    if (/^\d{1,2}:\d{2}(:\d{2})?$/.test(notesTextarea.value)) {
                        notesTextarea.value = '';
                    }
                }
            });
        }

        editPunchForm.addEventListener('submit', function(e) {
            // Check if this is a PTO punch and validate the notes format
            const punchType = punchTypeSelect ? punchTypeSelect.value : '';
            const notes = notesTextarea ? notesTextarea.value : '';
            const isPtoPunch = ['sick_time', 'vacation_time'].includes(punchType);

            if (isPtoPunch) {
                const timeFormatRegex = /^\d{1,2}:\d{2}(:\d{2})?$/;
                if (!notes || !timeFormatRegex.test(notes)) {
                    e.preventDefault();
                    alert('For PTO entries, you must enter hours in HH:MM:SS format (e.g., 8:00:00)');
                    return;
                }
            }

            // Disable the submit button and show loading
            const submitButton = this.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Saving...';
        });
    }
}
