import PhotoSwipeLightbox from 'photoswipe/lightbox';
import 'photoswipe/style.css';

// Global variable to store the current image ID
let currentImageId = null;

document.addEventListener('DOMContentLoaded', function () {
    const galleryContainer = document.querySelector('#imageGalleryContainer');
    const inventoryId = document.querySelector('meta[name="inventory-id"]').content;

    function loadImages() {
        fetch(`/inventory/${inventoryId}/images`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Images loaded successfully:', data.images);
                    galleryContainer.innerHTML = '';

                    const promises = data.images.map((image, index) => {
                        return new Promise((resolve) => {
                            const img = new Image();
                            img.onload = () => {
                                // Create link element for the lightbox
                                const link = document.createElement('a');
                                link.href = image.webp_path;
                                link.setAttribute('data-pswp-width', img.width);
                                link.setAttribute('data-pswp-height', img.height);
                                link.setAttribute('data-id', image.id); // Add image ID for navigation

                                // Make first image larger and span both columns within the gallery
                                if (index === 0) {
                                    link.classList.add(
                                        'relative',
                                        'overflow-hidden',
                                        'rounded-lg',
                                        'shadow-lg',
                                        'bg-base-200',
                                        'cursor-pointer',
                                        'col-span-2',
                                        'aspect-square'
                                    );
                                } else {
                                    link.classList.add(
                                        'relative',
                                        'overflow-hidden',
                                        'rounded-lg',
                                        'shadow-md',
                                        'bg-base-200',
                                        'cursor-pointer',
                                        'aspect-square'
                                    );
                                }

                                // Create thumbnail image element
                                const thumb = document.createElement('img');
                                thumb.src = image.image_path;
                                thumb.alt = image.alt_text || 'Image'; // Default alt text if none provided
                                thumb.classList.add('object-cover', 'w-full', 'h-full');

                                // Append thumbnail to link and link to gallery container
                                link.appendChild(thumb);
                                galleryContainer.appendChild(link);

                                resolve();
                            };

                            img.onerror = () => {
                                console.error(`Failed to load image at path: ${image.webp_path}`);
                                resolve();
                            };

                            // Load full-size image to fetch dimensions
                            img.src = image.webp_path;
                        });
                    });

                    // Wait for all images to load before initializing PhotoSwipe
                    Promise.all(promises).then(() => {
                        console.log('Gallery items are loaded. Initializing PhotoSwipe.');
                        initializePhotoSwipe();
                    });
                } else {
                    console.error('Failed to load images:', data.message);
                }
            })
            .catch(error => console.error('Error loading images:', error));
    }

    function initializePhotoSwipe() {
        const lightbox = new PhotoSwipeLightbox({
            gallery: '#imageGalleryContainer', // Matches the gallery container
            children: 'a', // Matches child anchor elements
            pswpModule: () => import('photoswipe'), // Lazy-load PhotoSwipe module
        });

        // Track the clicked thumbnail's ID
        galleryContainer.addEventListener('click', (event) => {
            const clickedLink = event.target.closest('a'); // Get the closest anchor element
            if (clickedLink && clickedLink.hasAttribute('data-id')) {
                currentImageId = clickedLink.getAttribute('data-id'); // Set the global variable
                console.log(`Thumbnail clicked! Current Image ID: ${currentImageId}`);
            }
        });

        // Add a custom button to the PhotoSwipe UI for "Show Details"
        lightbox.on('uiRegister', () => {
            // Button to open the show details page
            lightbox.pswp.ui.registerElement({
                name: 'viewImageShowPage',
                ariaLabel: 'View Image Details',
                order: 9,
                isButton: true,
                html: '<a class="text-lg text-white cursor-pointer" title="View Image Details"><i class="fa-sharp fa-info"></i></a>',
                onClick: () => {
                    if (currentImageId) {
                        console.log(`Show Details button clicked! Using Image ID: ${currentImageId}`);
                        const url = `/images/${currentImageId}`;
                        window.open(url, '_blank'); // Open the show details page in a new tab
                    } else {
                        console.error('No image ID found. Ensure an image was clicked before opening the lightbox.');
                    }
                },
            });

            // Button to open the edit page
            lightbox.pswp.ui.registerElement({
                name: 'editImagePage',
                ariaLabel: 'Edit Image',
                order: 10,
                isButton: true,
                html: '<a class="text-lg text-white cursor-pointer" title="Edit Image"><i class="fa-sharp fa-edit"></i></a>',
                onClick: () => {
                    if (currentImageId) {
                        console.log(`Edit button clicked! Using Image ID: ${currentImageId}`);
                        const url = `/images/${currentImageId}/edit`;
                        window.open(url, '_blank'); // Open the edit page in a new tab
                    } else {
                        console.error('No image ID found. Ensure an image was clicked before opening the lightbox.');
                    }
                },
            });
        });

        // Log the image ID when it is opened in the lightbox
        lightbox.on('change', () => {
            const currentIndex = lightbox.pswp?.currIndex;
            const currentItem = lightbox.pswp?.items?.[currentIndex];

            if (!currentItem || !currentItem.element) {
                console.error('Current item or element is undefined.');
                return;
            }

            currentImageId = currentItem.element.getAttribute('data-id'); // Update the global variable
            if (currentImageId) {
                console.log(`Lightbox change event: Current Image ID: ${currentImageId}`);
            } else {
                console.error('Image ID not found for the current lightbox item.');
            }
        });

        lightbox.init();
    }

    // Load images on page load
    loadImages();
});
