<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pickup_requests', function (Blueprint $table) {
            // Add new guided pickup fields
            $table->enum('accessibility_level', ['easy', 'moderate', 'difficult'])->nullable()->after('property_location_details');
            $table->text('driver_instructions')->nullable()->after('accessibility_level');
            $table->enum('load_size', ['small', 'medium', 'large'])->nullable()->after('driver_instructions');
            $table->json('item_types')->nullable()->after('load_size');
            $table->text('item_specifics')->nullable()->after('item_types');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pickup_requests', function (Blueprint $table) {
            // Remove the guided pickup fields
            $table->dropColumn([
                'accessibility_level',
                'driver_instructions',
                'load_size',
                'item_types',
                'item_specifics'
            ]);
        });
    }
};
