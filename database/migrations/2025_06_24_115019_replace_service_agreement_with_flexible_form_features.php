<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            // Add new flexible fields
            $table->boolean('requires_customer_link')->default(false)->after('requires_signature');
            $table->boolean('requires_approval')->default(false)->after('requires_customer_link');
            $table->boolean('applies_discount_on_approval')->default(false)->after('requires_approval');
            
            // Contact field requirements (stored as JSON for flexibility)
            $table->json('required_contact_fields')->nullable()->after('applies_discount_on_approval');
        });

        // Migrate existing service agreement forms to new system
        DB::table('forms')
            ->where('is_service_agreement', true)
            ->orWhere('type', 'service_agreement')
            ->update([
                'requires_customer_link' => true,
                'requires_approval' => true,
                'applies_discount_on_approval' => true,
                'required_contact_fields' => json_encode([
                    'name' => true,     // Business name for companies, Full name for residential
                    'contact' => false, // Contact person for business customers (optional)
                    'email' => true,
                    'phone' => true,
                    'address' => true,
                ])
            ]);

        // Drop old columns after migration
        Schema::table('forms', function (Blueprint $table) {
            $table->dropColumn(['is_service_agreement', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            // Re-add old columns
            $table->string('type')->default('general')->after('description');
            $table->boolean('is_service_agreement')->default(false)->after('requires_signature');
        });

        // Migrate back from new system
        DB::table('forms')
            ->where('requires_customer_link', true)
            ->where('requires_approval', true)
            ->where('applies_discount_on_approval', true)
            ->update([
                'is_service_agreement' => true,
                'type' => 'service_agreement'
            ]);

        Schema::table('forms', function (Blueprint $table) {
            // Drop new columns
            $table->dropColumn([
                'requires_customer_link',
                'requires_approval', 
                'applies_discount_on_approval',
                'required_contact_fields'
            ]);
        });
    }
};