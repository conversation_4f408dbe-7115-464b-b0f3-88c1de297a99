<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('discounts', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');
            $table->enum('type', ['percent', 'fixed']);
            $table->decimal('amount', 10);
            $table->enum('scope', ['invoice', 'line_item']);
            $table->dateTime('start_date')->nullable();
            $table->dateTime('end_date')->nullable();
            $table->boolean('once_per_customer')->default(false);
            $table->decimal('minimum_purchase', 10)->nullable();
            $table->decimal('maximum_discount', 10)->nullable();
            $table->unsignedBigInteger('category_id')->nullable()->index('discounts_category_id_foreign');
            $table->boolean('reusable')->default(true);
            $table->boolean('is_customer_specific')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('discounts');
    }
};
