<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // We need to modify the enum values in the type column
        // First, drop the enum constraint
        DB::statement("ALTER TABLE time_punches MODIFY COLUMN type VARCHAR(20)");

        // Then, add the new enum constraint with the additional values
        DB::statement("ALTER TABLE time_punches MODIFY COLUMN type ENUM('clock_in', 'clock_out', 'break_in', 'break_out', 'sick_time', 'vacation_time')");

        // Add columns to track sick time and vacation time totals in the time_cards table
        Schema::table('time_cards', function (Blueprint $table) {
            $table->string('total_sick_time', 10)->default('00:00:00')->after('total_break_hours');
            $table->string('total_vacation_time', 10)->default('00:00:00')->after('total_sick_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // First, revert any punches with the new types to avoid constraint violations
        DB::table('time_punches')
            ->whereIn('type', ['sick_time', 'vacation_time'])
            ->update(['type' => 'clock_in']);

        // Drop the enum constraint
        DB::statement("ALTER TABLE time_punches MODIFY COLUMN type VARCHAR(20)");

        // Then, add back the original enum constraint
        DB::statement("ALTER TABLE time_punches MODIFY COLUMN type ENUM('clock_in', 'clock_out', 'break_in', 'break_out')");

        // Remove the columns from time_cards table
        Schema::table('time_cards', function (Blueprint $table) {
            $table->dropColumn(['total_sick_time', 'total_vacation_time']);
        });
    }
};
