<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_checklist_items', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('inventory_id')->index('inventory_checklist_items_inventory_id_foreign');
            $table->unsignedBigInteger('checklist_field_id')->index('inventory_checklist_items_checklist_field_id_foreign');
            $table->string('value')->nullable();
            $table->text('long_value')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_checklist_items');
    }
};
