<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Emergency contact information (required)
            $table->string('emergency_contact_name')->nullable();
            $table->string('emergency_contact_relationship')->nullable();
            $table->string('emergency_contact_phone')->nullable();

            // Optional health information
            $table->text('allergies')->nullable();
            $table->text('medications')->nullable();
            $table->text('medical_conditions')->nullable();
            $table->text('blood_type')->nullable();
            $table->text('additional_health_info')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop emergency contact fields
            $table->dropColumn([
                'emergency_contact_name',
                'emergency_contact_relationship',
                'emergency_contact_phone',
            ]);

            // Drop health information fields
            $table->dropColumn([
                'allergies',
                'medications',
                'medical_conditions',
                'blood_type',
                'additional_health_info',
            ]);
        });
    }
};
