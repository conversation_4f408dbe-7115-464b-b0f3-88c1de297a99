<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, expand the enum to include all old and new values
        DB::statement("ALTER TABLE pickup_requests MODIFY COLUMN status ENUM('pending', 'contacted', 'scheduled', 'completed', 'cancelled', 'incoming', 'confirmed') DEFAULT 'pending'");

        // Now update existing status values to map to new system
        DB::table('pickup_requests')->where('status', 'pending')->update(['status' => 'incoming']);
        DB::table('pickup_requests')->where('status', 'contacted')->update(['status' => 'pending']);
        DB::table('pickup_requests')->where('status', 'scheduled')->update(['status' => 'confirmed']);
        // 'completed' and 'cancelled' remain the same

        // Finally, set the enum to only the new values and update default
        DB::statement("ALTER TABLE pickup_requests MODIFY COLUMN status ENUM('incoming', 'pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'incoming'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // First, expand the enum to include all old and new values
        DB::statement("ALTER TABLE pickup_requests MODIFY COLUMN status ENUM('pending', 'contacted', 'scheduled', 'completed', 'cancelled', 'incoming', 'confirmed') DEFAULT 'pending'");

        // Reverse the status mapping
        DB::table('pickup_requests')->where('status', 'incoming')->update(['status' => 'pending']);
        DB::table('pickup_requests')->where('status', 'pending')->update(['status' => 'contacted']);
        DB::table('pickup_requests')->where('status', 'confirmed')->update(['status' => 'scheduled']);

        // Restore original enum constraint
        DB::statement("ALTER TABLE pickup_requests MODIFY COLUMN status ENUM('pending', 'contacted', 'scheduled', 'completed', 'cancelled') DEFAULT 'pending'");
    }
};
