<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('custody_events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('certificate_id')->constrained()->onDelete('cascade');
            $table->string('event_type');
            $table->dateTime('event_date');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('location')->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('previous_event_id')->nullable()->constrained('custody_events')->onDelete('set null');
            $table->timestamps();
            
            // Add index for faster access by certificate
            $table->index('certificate_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('custody_events');
    }
};
