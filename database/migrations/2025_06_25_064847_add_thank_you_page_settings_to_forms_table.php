<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            $table->boolean('thankyou_show_submission_details')->default(true)->after('save_html_content');
            $table->boolean('thankyou_show_html_message')->default(false)->after('thankyou_show_submission_details');
            $table->text('thankyou_html_message')->nullable()->after('thankyou_show_html_message');
            $table->boolean('thankyou_show_need_help')->default(true)->after('thankyou_html_message');
            $table->boolean('thankyou_enable_print')->default(false)->after('thankyou_show_need_help');
            $table->boolean('thankyou_enable_close')->default(false)->after('thankyou_enable_print');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            $table->dropColumn([
                'thankyou_show_submission_details',
                'thankyou_show_html_message',
                'thankyou_html_message',
                'thankyou_show_need_help',
                'thankyou_enable_print',
                'thankyou_enable_close'
            ]);
        });
    }
};
