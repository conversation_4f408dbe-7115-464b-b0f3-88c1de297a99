<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
// database/migrations/xxxx_xx_xx_create_user_preferences_table.php
public function up()
{
    Schema::create('user_preferences', function (Blueprint $table) {
        $table->id();
        $table->foreignId('user_id')->constrained()->onDelete('cascade');
        $table->string('key'); // e.g., 'pagination_inventory', 'sort_inventory'
        $table->text('value'); // Can store JSON or plain text
        $table->timestamps();

        $table->unique(['user_id', 'key']); // Prevent duplicate keys for the same user
    });
}


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_preferences');
    }
};
