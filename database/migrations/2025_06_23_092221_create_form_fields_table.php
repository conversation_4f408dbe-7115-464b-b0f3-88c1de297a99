<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('form_fields', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('form_id');
            $table->string('type'); // 'text', 'textarea', 'select', 'radio', 'checkbox', 'html', 'signature'
            $table->string('name')->nullable(); // Field name for form inputs
            $table->string('label')->nullable();
            $table->text('content')->nullable(); // For HTML blocks
            $table->json('options')->nullable(); // For select, radio, checkbox options
            $table->json('validation_rules')->nullable(); // Laravel validation rules
            $table->boolean('required')->default(false);
            $table->string('placeholder')->nullable();
            $table->text('help_text')->nullable();
            $table->integer('order')->default(0);
            $table->json('settings')->nullable(); // Additional field settings
            $table->timestamps();
            
            $table->foreign('form_id')->references('id')->on('forms')->onDelete('cascade');
            $table->index(['form_id', 'order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('form_fields');
    }
};
