<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('time_cards', function (Blueprint $table) {
            // Change total_hours and total_break_hours from decimal to string
            $table->string('total_hours', 10)->change();
            $table->string('total_break_hours', 10)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('time_cards', function (Blueprint $table) {
            // Change back to decimal
            $table->decimal('total_hours', 8, 2)->change();
            $table->decimal('total_break_hours', 8, 2)->change();
        });
    }
};
