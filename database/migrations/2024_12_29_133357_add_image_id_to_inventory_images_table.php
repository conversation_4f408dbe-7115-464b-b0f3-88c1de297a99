<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddImageIdToInventoryImagesTable extends Migration
{
    public function up()
    {
        Schema::table('inventory_images', function (Blueprint $table) {
            $table->unsignedBigInteger('image_id')->nullable()->after('inventory_id');
            $table->foreign('image_id')->references('id')->on('images')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::table('inventory_images', function (Blueprint $table) {
            $table->dropForeign(['image_id']);
            $table->dropColumn('image_id');
        });
    }
}
