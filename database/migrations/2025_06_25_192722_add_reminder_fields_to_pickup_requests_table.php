<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pickup_requests', function (Blueprint $table) {
            $table->timestamp('reminder_sent_at')->nullable()->after('status');
            $table->string('confirmation_token', 64)->nullable()->after('reminder_sent_at');
            $table->timestamp('confirmed_at')->nullable()->after('confirmation_token');
            $table->timestamp('cancelled_at')->nullable()->after('confirmed_at');
            $table->text('cancellation_reason')->nullable()->after('cancelled_at');
            
            $table->index('confirmation_token');
            $table->index('reminder_sent_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pickup_requests', function (Blueprint $table) {
            $table->dropIndex(['confirmation_token']);
            $table->dropIndex(['reminder_sent_at']);
            
            $table->dropColumn([
                'reminder_sent_at',
                'confirmation_token',
                'confirmed_at',
                'cancelled_at',
                'cancellation_reason'
            ]);
        });
    }
};
