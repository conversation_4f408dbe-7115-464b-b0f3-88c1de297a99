<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('time_punches', function (Blueprint $table) {
            $table->boolean('hidden_in_timeline')->default(false)->after('ip_address');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('time_punches', function (Blueprint $table) {
            $table->dropColumn('hidden_in_timeline');
        });
    }
};
