<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('message');
            $table->enum('urgency', ['low', 'normal', 'high', 'critical'])->default('normal');
            $table->enum('target_type', ['all_users', 'user_group', 'specific_users'])->default('all_users');
            $table->json('target_ids')->nullable(); // Array of user IDs or user group IDs
            $table->string('link_url')->nullable(); // Optional link when notification is clicked
            $table->string('link_text')->nullable(); // Text for the link
            $table->timestamp('expires_at')->nullable(); // Auto-expiry time
            $table->boolean('is_dismissible')->default(true); // Can users dismiss this notification
            $table->boolean('auto_dismiss')->default(false); // Auto-dismiss after being read
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            // Indexes for performance
            $table->index(['target_type', 'urgency']);
            $table->index('expires_at');
            $table->index('created_at');
        });

        // Pivot table to track which users have read/dismissed notifications
        Schema::create('notification_user', function (Blueprint $table) {
            $table->id();
            $table->foreignId('notification_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->timestamp('read_at')->nullable();
            $table->timestamp('dismissed_at')->nullable();
            $table->timestamps();

            // Ensure unique combination and add indexes
            $table->unique(['notification_id', 'user_id']);
            $table->index('read_at');
            $table->index('dismissed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_user');
        Schema::dropIfExists('notifications');
    }
};
