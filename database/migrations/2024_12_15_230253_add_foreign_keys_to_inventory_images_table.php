<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inventory_images', function (Blueprint $table) {
            $table->foreign(['inventory_id'])->references(['id'])->on('inventories')->onUpdate('no action')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inventory_images', function (Blueprint $table) {
            $table->dropForeign('inventory_images_inventory_id_foreign');
        });
    }
};
