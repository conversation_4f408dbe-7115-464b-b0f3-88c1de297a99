<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // Unique identifier like 'daily_timeclock_report'
            $table->string('display_name'); // Human-readable name for admin UI
            $table->string('description')->nullable(); // Description of when this template is used
            $table->string('subject'); // Email subject line with variable support
            $table->string('from_name')->nullable(); // Override from name
            $table->string('from_email')->nullable(); // Override from email
            $table->longText('body_html'); // HTML content of the email
            $table->json('available_variables')->nullable(); // JSON array of available variables and their descriptions
            $table->boolean('is_active')->default(true); // Enable/disable template
            $table->timestamps();
            
            // Index for quick lookups
            $table->index('name');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_templates');
    }
};