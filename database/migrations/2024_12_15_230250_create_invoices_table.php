<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('invoice_number')->unique();
            $table->unsignedBigInteger('customer_id')->index('invoices_customer_id_foreign');
            $table->date('invoice_date');
            $table->decimal('total_price', 10)->default(0);
            $table->decimal('total_tax', 10)->default(0);
            $table->decimal('total_discount', 10)->default(0);
            $table->decimal('final_price', 10)->default(0);
            $table->string('status')->default('Pending');
            $table->timestamps();
            $table->text('payment_method')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
