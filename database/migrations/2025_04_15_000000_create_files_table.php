<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('files', function (Blueprint $table) {
            $table->id();
            $table->string('filename');
            $table->string('original_filename');
            $table->string('filepath');
            $table->string('mime_type');
            $table->bigInteger('size')->unsigned(); // File size in bytes
            $table->string('extension');
            $table->text('description')->nullable();
            $table->foreignId('uploaded_by')->nullable()->constrained('users')->onDelete('set null');
            $table->morphs('fileable'); // Polymorphic relationship
            $table->json('metadata')->nullable(); // For additional file-specific data
            $table->boolean('is_public')->default(false); // Whether the file is publicly accessible
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for better performance
            $table->index('filename');
            $table->index('is_public');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('files');
    }
};
