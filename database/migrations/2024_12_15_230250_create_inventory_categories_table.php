<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_categories', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');
            $table->string('unit')->nullable();
            $table->string('quantity_type')->default('unit');
            $table->string('description')->nullable();
            $table->string('code')->unique();
            $table->boolean('status')->default(true);
            $table->timestamps();
            $table->softDeletes();
            $table->unsignedBigInteger('tax_policy_id')->nullable()->index('inventory_categories_tax_policy_id_foreign');
            $table->unsignedBigInteger('department_id')->default(1)->index('inventory_categories_department_id_foreign');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_categories');
    }
};
