<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');
            $table->string('contact')->nullable();
            $table->string('business_name')->nullable();
            $table->string('nickname')->nullable();
            $table->string('email')->nullable()->unique();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->text('notes')->nullable();
            $table->enum('type', ['eBay Customer', 'Website Customer', 'Facebook Customer', 'Bulk Buyer', 'Business', 'Residential Customer'])->default('Website Customer');
            $table->string('website')->nullable();
            $table->timestamps();
            $table->softDeletes();
            $table->decimal('total_revenue', 10)->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
