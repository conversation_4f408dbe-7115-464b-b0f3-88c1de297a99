<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('devices', function (Blueprint $table) {
            $table->boolean('is_data_storage_device')->default(false);
            $table->string('pc_manufacturer')->nullable();
            $table->string('pc_serial_number')->nullable();
            $table->string('pc_form_factor')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('devices', function (Blueprint $table) {
            $table->dropColumn('is_data_storage_device');
            $table->dropColumn('pc_manufacturer');
            $table->dropColumn('pc_serial_number');
            $table->dropColumn('pc_form_factor');
        });
    }
};