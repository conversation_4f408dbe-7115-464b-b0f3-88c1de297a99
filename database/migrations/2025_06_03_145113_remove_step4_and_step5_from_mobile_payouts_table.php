<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('mobile_payouts', function (Blueprint $table) {
            $table->dropColumn(['step4_completed', 'step5_completed']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mobile_payouts', function (Blueprint $table) {
            $table->boolean('step4_completed')->default(false);
            $table->boolean('step5_completed')->default(false);
        });
    }
};
