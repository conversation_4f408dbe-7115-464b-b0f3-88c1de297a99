<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mobile_payouts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->string('payout_number')->unique(); // Auto-generated payout number
            $table->enum('status', ['in_progress', 'completed', 'cancelled'])->default('in_progress');

            // Step completion tracking
            $table->boolean('step1_completed')->default(false); // Customer selection
            $table->boolean('step2_completed')->default(false); // Photo ID verification
            $table->boolean('step3_completed')->default(false); // Payout details
            $table->boolean('step4_completed')->default(false); // Item photos
            $table->boolean('step5_completed')->default(false); // Review & complete

            // Step data storage
            $table->json('step_data')->nullable(); // Store all step data as JSON

            // Final invoice reference (when completed)
            $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('set null');

            // Metadata
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mobile_payouts');
    }
};
