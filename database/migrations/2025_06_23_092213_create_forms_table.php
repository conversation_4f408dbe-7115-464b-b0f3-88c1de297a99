<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('forms', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('type')->default('general'); // 'service_agreement', 'general', etc.
            $table->boolean('is_active')->default(true);
            $table->boolean('requires_signature')->default(false);
            $table->boolean('is_service_agreement')->default(false);
            $table->json('settings')->nullable(); // For storing form-specific settings
            $table->unsignedBigInteger('discount_id')->nullable();
            $table->integer('agreement_duration_months')->nullable(); // Duration for service agreements
            $table->timestamps();
            
            $table->foreign('discount_id')->references('id')->on('discounts')->onDelete('set null');
            $table->index('slug');
            $table->index('type');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('forms');
    }
};
