<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\EmailTemplate;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the pickup final reminder email template (for already confirmed appointments)
        EmailTemplate::create([
            'name' => 'pickup_final_reminder',
            'display_name' => 'Pickup Final Reminder (Confirmed Appointments)',
            'description' => 'Final reminder email sent 48 hours before pickup for already confirmed appointments',
            'subject' => 'Reminder: Your Pickup is Tomorrow - {app_name}',
            'from_name' => null, // Use system default
            'from_email' => null, // Use system default
            'body_html' => $this->getDefaultHtmlBody(),
            'available_variables' => json_encode([
                '{pickup_id}' => 'Pickup request ID',
                '{contact_name}' => 'Contact person name',
                '{business_name}' => 'Business name (if applicable)',
                '{pickup_date}' => 'Scheduled pickup date and time',
                '{pickup_address}' => 'Pickup location address',
                '{driver_name}' => 'Name of assigned driver (if provided)',
                '{driver_instructions}' => 'Special instructions for driver',
                '{item_types}' => 'Types of items for pickup',
                '{load_size}' => 'Size of the load',
                '{management_url}' => 'URL for customer to manage their appointment',
                '{app_name}' => 'Application name',
                '{current_date}' => 'Current date when email is sent',
                '{hours_until_pickup}' => 'Number of hours until scheduled pickup',
                '{confirmed_date}' => 'Date when appointment was confirmed',
            ]),
            'is_active' => true,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        EmailTemplate::where('name', 'pickup_final_reminder')->delete();
    }

    /**
     * Get the default HTML body for the template
     */
    private function getDefaultHtmlBody(): string
    {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pickup Reminder - Tomorrow</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; }
        .container { background-color: #ffffff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); }
        .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e0e0e0; }
        .header h1 { color: #2c3e50; margin: 0; font-size: 24px; }
        .logo { font-size: 28px; font-weight: bold; color: #3498db; margin-bottom: 10px; }
        .confirmed-badge { display: inline-block; background-color: #27ae60; color: white; padding: 5px 15px; border-radius: 20px; font-size: 14px; margin-top: 10px; }
        .pickup-details { background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #27ae60; }
        .pickup-details h3 { margin-top: 0; color: #2c3e50; }
        .detail-item { margin: 10px 0; display: flex; align-items: flex-start; }
        .detail-label { font-weight: bold; min-width: 100px; color: #34495e; }
        .detail-value { flex: 1; }
        .reminder-notice { background-color: #e8f5e9; border: 1px solid #4caf50; color: #2e7d32; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .reminder-icon { font-size: 18px; margin-right: 8px; }
        .checklist { background-color: #fff9e6; padding: 20px; border-radius: 6px; margin: 20px 0; }
        .checklist h4 { margin-top: 0; color: #2c3e50; }
        .checklist ul { margin: 10px 0; padding-left: 20px; }
        .checklist li { margin: 5px 0; }
        .action-button { text-align: center; margin: 30px 0; }
        .btn { display: inline-block; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px; background-color: #3498db; color: white; transition: background-color 0.3s; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #7f8c8d; font-size: 14px; }
        .contact-info { margin-top: 20px; padding: 15px; background-color: #ecf0f1; border-radius: 6px; }
        @media only screen and (max-width: 600px) {
            body { padding: 10px; }
            .container { padding: 20px; }
            .detail-item { flex-direction: column; }
            .detail-label { min-width: auto; margin-bottom: 5px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{app_name}</div>
            <h1>Your Pickup is Tomorrow!</h1>
            <span class="confirmed-badge">✅ Confirmed Appointment</span>
        </div>

        <p>Dear {contact_name},</p>

        <p>This is a friendly reminder that your electronics pickup is scheduled for <strong>tomorrow</strong>.</p>

        <div class="reminder-notice">
            <span class="reminder-icon">✅</span>
            <strong>Status:</strong> Your appointment has been confirmed. We\'ll see you tomorrow!
        </div>

        <div class="pickup-details">
            <h3>📅 Confirmed Pickup Details</h3>
            <div class="detail-item">
                <div class="detail-label">Date & Time:</div>
                <div class="detail-value"><strong>{pickup_date}</strong></div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Address:</div>
                <div class="detail-value">{pickup_address}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Request ID:</div>
                <div class="detail-value">#{pickup_id}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Business:</div>
                <div class="detail-value">{business_name}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Load Size:</div>
                <div class="detail-value">{load_size}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Items:</div>
                <div class="detail-value">{item_types}</div>
            </div>
        </div>

        <div class="checklist">
            <h4>📋 Preparation Checklist</h4>
            <p>To ensure a smooth pickup tomorrow, please make sure:</p>
            <ul>
                <li>✓ All electronics are gathered in one accessible location</li>
                <li>✓ Items are ready for loading (no need to disconnect or prepare)</li>
                <li>✓ Clear access path to the pickup location</li>
                <li>✓ Someone will be available at the scheduled time</li>
                <li>✓ Any special instructions have been communicated</li>
            </ul>
        </div>

        <div class="action-button">
            <a href="{management_url}" class="btn">🔧 Need to Make Changes?</a>
        </div>

        <p><strong>What to expect:</strong></p>
        <ul>
            <li>Our team will arrive within the scheduled time window</li>
            <li>We\'ll handle all the heavy lifting and loading</li>
            <li>You\'ll receive a receipt and certificate of destruction</li>
            <li>All items will be properly recycled or destroyed</li>
        </ul>

        <div class="contact-info">
            <h4>Questions?</h4>
            <p>If you have any last-minute questions or concerns, please contact us:</p>
            <p>
                📧 Email: <a href="mailto:' . config('mail.from.address') . '">' . config('mail.from.address') . '</a><br>
                🌐 Manage Appointment: <a href="{management_url}">Click Here</a>
            </p>
        </div>

        <div class="footer">
            <p><strong>{app_name}</strong> - Professional Electronics Recycling Services</p>
            <p>Thank you for choosing us for your electronics recycling needs!</p>
            <p>&copy; {current_date} {app_name}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }
};
