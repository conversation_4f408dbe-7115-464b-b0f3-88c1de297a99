<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update customer type from 'Walkin Customer' to 'Residential Customer'
        DB::table('customers')
            ->where('type', 'Walkin Customer')
            ->update(['type' => 'Residential Customer']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert customer type from 'Residential Customer' back to 'Walkin Customer'
        DB::table('customers')
            ->where('type', 'Residential Customer')
            ->update(['type' => 'Walkin Customer']);
    }
};
