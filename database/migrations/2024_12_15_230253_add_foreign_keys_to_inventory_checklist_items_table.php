<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inventory_checklist_items', function (Blueprint $table) {
            $table->foreign(['checklist_field_id'])->references(['id'])->on('checklist_fields')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['inventory_id'])->references(['id'])->on('inventories')->onUpdate('no action')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inventory_checklist_items', function (Blueprint $table) {
            $table->dropForeign('inventory_checklist_items_checklist_field_id_foreign');
            $table->dropForeign('inventory_checklist_items_inventory_id_foreign');
        });
    }
};
