<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing certificate numbers from COC- to DCD- prefix
        DB::table('certificates')
            ->where('certificate_number', 'LIKE', 'COC-%')
            ->update([
                'certificate_number' => DB::raw("REPLACE(certificate_number, 'COC-', 'DCD-')")
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert certificate numbers from DCD- back to COC- prefix
        DB::table('certificates')
            ->where('certificate_number', 'LIKE', 'DCD-%')
            ->update([
                'certificate_number' => DB::raw("REPLACE(certificate_number, 'DCD-', 'COC-')")
            ]);
    }
};
