<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Note: Microsoft calendar configuration will be stored as a key-value pair in global_configs
        // using the existing 'key' and 'value' columns

        // Modify microsoft_calendar_integrations to support a single shared integration
        Schema::table('microsoft_calendar_integrations', function (Blueprint $table) {
            $table->boolean('is_shared_calendar')->default(false)->after('sync_enabled');
            $table->string('calendar_name')->nullable()->after('outlook_calendar_id');
        });

        // Modify events table to store single Microsoft event ID instead of per-user IDs
        Schema::table('events', function (Blueprint $table) {
            $table->string('microsoft_event_id')->nullable()->after('microsoft_event_ids');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Note: Global config entries will need to be manually removed if needed

        Schema::table('microsoft_calendar_integrations', function (Blueprint $table) {
            $table->dropColumn(['is_shared_calendar', 'calendar_name']);
        });

        Schema::table('events', function (Blueprint $table) {
            $table->dropColumn('microsoft_event_id');
        });
    }
};