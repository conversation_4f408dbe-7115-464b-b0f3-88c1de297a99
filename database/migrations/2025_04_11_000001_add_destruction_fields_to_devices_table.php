<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('devices', function (Blueprint $table) {
            if (!Schema::hasColumn('devices', 'is_destroyed')) {
                $table->boolean('is_destroyed')->default(false)->after('model');
            }

            if (!Schema::hasColumn('devices', 'destroyed_at')) {
                $table->timestamp('destroyed_at')->nullable()->after('is_destroyed');
            }

            if (!Schema::hasColumn('devices', 'destroyed_by')) {
                $table->foreignId('destroyed_by')->nullable()->after('destroyed_at')->constrained('users')->onDelete('set null');
            }

            // Skip adding notes column if it already exists
            if (!Schema::hasColumn('devices', 'notes')) {
                $table->text('notes')->nullable()->after('destroyed_by');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('devices', function (Blueprint $table) {
            // Only drop the foreign key if the column exists
            if (Schema::hasColumn('devices', 'destroyed_by')) {
                $table->dropForeign(['destroyed_by']);
            }

            // Drop columns that exist
            $columns = [];

            if (Schema::hasColumn('devices', 'is_destroyed')) {
                $columns[] = 'is_destroyed';
            }

            if (Schema::hasColumn('devices', 'destroyed_at')) {
                $columns[] = 'destroyed_at';
            }

            if (Schema::hasColumn('devices', 'destroyed_by')) {
                $columns[] = 'destroyed_by';
            }

            // Don't drop the notes column if it existed before this migration

            if (!empty($columns)) {
                $table->dropColumn($columns);
            }
        });
    }
};
