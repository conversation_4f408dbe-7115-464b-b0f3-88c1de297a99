<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('task_recurrences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('task_id')->constrained()->onDelete('cascade');
            $table->enum('repeat_interval', ['none', 'daily', 'weekly', 'monthly', 'custom']);
            $table->json('repeat_days')->nullable();
            $table->json('excluded_dates')->nullable();
            $table->integer('custom_interval')->nullable();
            $table->time('due_time')->nullable();
            $table->timestamps();
        });
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
        Schema::dropIfExists('task_recurrences');
    }
};
