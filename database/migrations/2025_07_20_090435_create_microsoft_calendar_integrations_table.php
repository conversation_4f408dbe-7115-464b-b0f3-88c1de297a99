<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('microsoft_calendar_integrations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('access_token')->nullable();
            $table->text('refresh_token')->nullable();
            $table->timestamp('token_expires_at')->nullable();
            $table->string('outlook_calendar_id')->nullable();
            $table->boolean('sync_enabled')->default(false);
            $table->timestamp('last_sync_at')->nullable();
            $table->enum('sync_status', ['pending', 'syncing', 'completed', 'failed'])->default('pending');
            $table->text('error_message')->nullable();
            $table->json('sync_settings')->nullable(); // Store user preferences
            $table->string('microsoft_user_id')->nullable(); // Microsoft Graph user ID
            $table->string('microsoft_user_email')->nullable(); // User's Microsoft email
            $table->timestamps();
            
            $table->unique('user_id'); // One integration per user
            $table->index(['sync_enabled', 'last_sync_at']);
            $table->index('sync_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('microsoft_calendar_integrations');
    }
};
