<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventories', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->index();
            $table->text('description')->nullable();
            $table->string('status');
            $table->unsignedBigInteger('technician_id')->nullable()->index('inventories_technician_id_foreign');
            $table->unsignedBigInteger('inv_category_id')->index('inventories_inv_category_id_foreign');
            $table->string('location')->nullable();
            $table->timestamps();
            $table->softDeletes();
            $table->decimal('suggested_price', 10)->nullable();
            $table->integer('quantity')->default(1);
            $table->decimal('unit_price', 10)->default(0);
            $table->decimal('weight', 10)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventories');
    }
};
