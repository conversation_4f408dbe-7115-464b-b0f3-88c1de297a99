<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('line_item_discounts', function (Blueprint $table) {
            $table->foreign(['discount_id'])->references(['id'])->on('discounts')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['line_item_id'])->references(['id'])->on('line_items')->onUpdate('no action')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('line_item_discounts', function (Blueprint $table) {
            $table->dropForeign('line_item_discounts_discount_id_foreign');
            $table->dropForeign('line_item_discounts_line_item_id_foreign');
        });
    }
};
