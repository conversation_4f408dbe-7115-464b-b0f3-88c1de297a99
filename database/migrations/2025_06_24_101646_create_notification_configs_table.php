<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_configs', function (Blueprint $table) {
            $table->id();
            $table->morphs('configurable'); // configurable_type, configurable_id
            $table->string('event_type'); // e.g., 'form_submitted', 'task_created', 'invoice_generated'
            $table->enum('target_type', ['all_users', 'user_group', 'specific_users'])->default('user_group');
            $table->json('target_ids')->nullable(); // Array of user/group IDs
            $table->enum('urgency', ['low', 'normal', 'high', 'critical'])->default('normal');
            $table->boolean('is_enabled')->default(true);
            $table->string('custom_title')->nullable(); // Optional custom notification title template
            $table->text('custom_message')->nullable(); // Optional custom notification message template
            $table->boolean('include_link')->default(true); // Whether to include a link to the resource
            $table->string('link_text')->nullable(); // Custom link text
            $table->integer('expires_after_days')->nullable(); // Auto-expire after X days
            $table->boolean('is_dismissible')->default(true);
            $table->boolean('auto_dismiss')->default(false);
            $table->timestamps();

            // Indexes for performance (morphs already creates configurable index)
            $table->index(['event_type']);
            $table->index(['is_enabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_configs');
    }
};
