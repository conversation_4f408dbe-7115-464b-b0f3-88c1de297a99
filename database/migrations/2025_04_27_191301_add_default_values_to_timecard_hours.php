<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('time_cards', function (Blueprint $table) {
            // Add default values for total_hours and total_break_hours
            $table->string('total_hours', 10)->default('00:00:00')->change();
            $table->string('total_break_hours', 10)->default('00:00:00')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('time_cards', function (Blueprint $table) {
            // Remove default values
            $table->string('total_hours', 10)->nullable()->change();
            $table->string('total_break_hours', 10)->nullable()->change();
        });
    }
};
