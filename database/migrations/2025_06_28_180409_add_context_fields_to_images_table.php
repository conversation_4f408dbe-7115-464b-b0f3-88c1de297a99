<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('images', function (Blueprint $table) {
            // Context fields to link images to any model
            $table->string('context_type')->nullable()->after('uploaded_by'); // e.g., 'inventory', 'invoice', 'customer'
            $table->unsignedBigInteger('context_id')->nullable()->after('context_type'); // ID of the related object
            $table->integer('order_number')->nullable()->after('context_id'); // For ordering images within a context
            $table->boolean('is_public')->default(false)->after('order_number'); // For public accessibility
            $table->string('session_id')->nullable()->after('is_public'); // For temporary uploads during multi-step forms
            
            // Add composite index for efficient querying
            $table->index(['context_type', 'context_id']);
            $table->index(['session_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('images', function (Blueprint $table) {
            $table->dropIndex(['context_type', 'context_id']);
            $table->dropIndex(['session_id']);
            $table->dropColumn(['context_type', 'context_id', 'order_number', 'is_public', 'session_id']);
        });
    }
};
