<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\EmailTemplate;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the pickup confirmation reminder email template
        EmailTemplate::create([
            'name' => 'pickup_confirmation_reminder',
            'display_name' => 'Pickup Confirmation Reminder (48 Hours)',
            'description' => 'Reminder email sent 48 hours before pickup to prompt customer confirmation',
            'subject' => 'Pickup Confirmation Required - {app_name}',
            'from_name' => '{app_name}',
            'from_email' => config('mail.from.address'),
            'body_html' => $this->getDefaultHtmlBody(),
            'available_variables' => json_encode([
                '{pickup_id}' => 'Pickup request ID',
                '{contact_name}' => 'Contact person name',
                '{business_name}' => 'Business name (if applicable)',
                '{pickup_date}' => 'Scheduled pickup date and time',
                '{pickup_address}' => 'Pickup location address',
                '{driver_name}' => 'Name of assigned driver (if provided)',
                '{driver_instructions}' => 'Special instructions for driver',
                '{item_types}' => 'Types of items for pickup',
                '{load_size}' => 'Size of the load',
                '{management_url}' => 'URL for customer to manage their appointment',
                '{confirm_url}' => 'Direct URL to confirm the appointment',
                '{cancel_url}' => 'Direct URL to cancel the appointment',
                '{app_name}' => 'Application name',
                '{current_date}' => 'Current date when email is sent',
                '{hours_until_pickup}' => 'Number of hours until scheduled pickup',
            ]),
            'is_active' => true,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        EmailTemplate::where('name', 'pickup_confirmation_reminder')->delete();
    }

    /**
     * Get the default HTML body for the template
     */
    private function getDefaultHtmlBody(): string
    {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pickup Confirmation Required</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; }
        .container { background-color: #ffffff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); }
        .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e0e0e0; }
        .header h1 { color: #2c3e50; margin: 0; font-size: 24px; }
        .logo { font-size: 28px; font-weight: bold; color: #3498db; margin-bottom: 10px; }
        .pickup-details { background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #3498db; }
        .pickup-details h3 { margin-top: 0; color: #2c3e50; }
        .detail-item { margin: 10px 0; display: flex; align-items: flex-start; }
        .detail-label { font-weight: bold; min-width: 100px; color: #34495e; }
        .detail-value { flex: 1; }
        .alert { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .alert-icon { font-size: 18px; margin-right: 8px; }
        .action-buttons { text-align: center; margin: 30px 0; }
        .btn { display: inline-block; padding: 12px 24px; margin: 0 10px; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px; transition: background-color 0.3s; }
        .btn-confirm { background-color: #27ae60; color: white; }
        .btn-manage { background-color: #3498db; color: white; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #7f8c8d; font-size: 14px; }
        .contact-info { margin-top: 20px; padding: 15px; background-color: #ecf0f1; border-radius: 6px; }
        @media only screen and (max-width: 600px) {
            body { padding: 10px; }
            .container { padding: 20px; }
            .btn { display: block; margin: 10px 0; }
            .detail-item { flex-direction: column; }
            .detail-label { min-width: auto; margin-bottom: 5px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{app_name}</div>
            <h1>Pickup Confirmation Required</h1>
        </div>

        <p>Dear {contact_name},</p>

        <p>Your electronics pickup is scheduled for <strong>{pickup_date}</strong>, which is less than 48 hours away.</p>

        <div class="alert">
            <span class="alert-icon">⏰</span>
            <strong>Action Required:</strong> Please confirm your pickup appointment to ensure we\'re prepared for your scheduled service.
        </div>

        <div class="pickup-details">
            <h3>📅 Pickup Details</h3>
            <div class="detail-item">
                <div class="detail-label">Date & Time:</div>
                <div class="detail-value">{pickup_date}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Address:</div>
                <div class="detail-value">{pickup_address}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Request ID:</div>
                <div class="detail-value">#{pickup_id}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Business:</div>
                <div class="detail-value">{business_name}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Load Size:</div>
                <div class="detail-value">{load_size}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Items:</div>
                <div class="detail-value">{item_types}</div>
            </div>
        </div>

        <div class="action-buttons">
            <a href="{confirm_url}" class="btn btn-confirm">✅ Confirm Pickup</a>
            <a href="{management_url}" class="btn btn-manage">🔧 Manage Appointment</a>
        </div>

        <p><strong>What happens next?</strong></p>
        <ul>
            <li>✅ <strong>If you confirm:</strong> We\'ll proceed with your scheduled pickup as planned</li>
            <li>❌ <strong>If you need to cancel:</strong> Use the "Manage Appointment" link above</li>
            <li>📞 <strong>If you have questions:</strong> Contact us using the information below</li>
        </ul>

        <div class="contact-info">
            <h4>Need Help?</h4>
            <p>If you have any questions or need to make changes to your pickup, please contact us:</p>
            <p>
                📧 Email: <a href="mailto:' . config('mail.from.address') . '">' . config('mail.from.address') . '</a><br>
                🌐 Appointment Management: <a href="{management_url}">Click Here</a>
            </p>
        </div>

        <div class="footer">
            <p><strong>{app_name}</strong> - Professional Electronics Recycling Services</p>
            <p>This is an automated reminder. Please do not reply directly to this email.</p>
            <p>&copy; {current_date} {app_name}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }
};
