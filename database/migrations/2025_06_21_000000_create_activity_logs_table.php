<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_logs', function (Blueprint $table) {
            $table->id();
            
            // Polymorphic relationship to any model
            $table->string('loggable_type')->nullable(); // Model class name (e.g., App\Models\Customer)
            $table->unsignedBigInteger('loggable_id')->nullable(); // Model ID
            $table->index(['loggable_type', 'loggable_id']);
            
            // User who performed the action
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            
            // Event information
            $table->string('event_type'); // create, update, delete, custom_action, etc.
            $table->string('component'); // customers, inventory, invoices, etc.
            $table->text('description'); // Human-readable description
            
            // Data storage - flexible for different types of logs
            $table->json('old_values')->nullable(); // Previous values for updates
            $table->json('new_values')->nullable(); // New values for creates/updates
            $table->json('additional_data')->nullable(); // Any extra data specific to the event
            
            // Request context
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            
            // Timestamps
            $table->timestamps();
            
            // Indexes for common queries
            $table->index('event_type');
            $table->index('component');
            $table->index('user_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_logs');
    }
};
