<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pickup_requests', function (Blueprint $table) {
            // Make legacy fields nullable since we now have guided pickup fields
            $table->text('pickup_items')->nullable()->change();
            $table->text('pickup_quantity')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pickup_requests', function (Blueprint $table) {
            // Revert to non-nullable (but this might fail if there are null values)
            $table->text('pickup_items')->nullable(false)->change();
            $table->text('pickup_quantity')->nullable(false)->change();
        });
    }
};
