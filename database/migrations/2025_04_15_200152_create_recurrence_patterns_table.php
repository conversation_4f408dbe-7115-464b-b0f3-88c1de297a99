<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recurrence_patterns', function (Blueprint $table) {
            $table->id();
            $table->enum('frequency', ['daily', 'weekly', 'monthly', 'yearly', 'custom']);
            $table->integer('interval')->default(1); // Every X days/weeks/months/years
            $table->json('days_of_week')->nullable(); // For weekly: [0,1,2,3,4,5,6] where 0=Sunday
            $table->json('days_of_month')->nullable(); // For monthly: [1,15] for 1st and 15th
            $table->json('months_of_year')->nullable(); // For yearly: [0,6] for January and July
            $table->integer('week_of_month')->nullable(); // 1=first, 2=second, -1=last
            $table->date('until_date')->nullable(); // Recur until this date
            $table->integer('count')->nullable(); // Recur this many times
            $table->json('exception_dates')->nullable(); // Dates to skip
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recurrence_patterns');
    }
};
