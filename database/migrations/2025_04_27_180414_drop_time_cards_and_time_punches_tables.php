<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First drop time_punches table because it has foreign key references to time_cards
        Schema::dropIfExists('time_punches');

        // Then drop time_cards table
        Schema::dropIfExists('time_cards');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate time_cards table
        Schema::create('time_cards', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->date('date');
            $table->text('notes')->nullable();
            $table->decimal('total_hours', 8, 2)->default(0);
            $table->decimal('total_break_hours', 8, 2)->default(0);
            $table->timestamps();
            $table->softDeletes();

            // Add a unique constraint for user_id and date
            $table->unique(['user_id', 'date']);
        });

        // Recreate time_punches table
        Schema::create('time_punches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('time_card_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['clock_in', 'clock_out']);
            $table->boolean('is_break')->default(false);
            $table->dateTime('punch_time');
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('original_punch_id')->nullable();
            $table->unsignedBigInteger('replaced_by')->nullable();
            $table->unsignedBigInteger('edited_by')->nullable();
            $table->string('ip_address')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign key for original punch
            $table->foreign('original_punch_id')->references('id')->on('time_punches')->onDelete('set null');
            // Foreign key for replaced by
            $table->foreign('replaced_by')->references('id')->on('time_punches')->onDelete('set null');
            // Foreign key for edited by
            $table->foreign('edited_by')->references('id')->on('users')->onDelete('set null');
        });
    }
};
