<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('certificates', function (Blueprint $table) {
            $table->integer('client_manifest_drive_count')->nullable();
            $table->integer('client_manifest_device_count')->nullable();
            $table->integer('etech_verified_drive_count')->nullable();
            $table->integer('etech_verified_device_count')->nullable();
            $table->string('service_selection')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('certificates', function (Blueprint $table) {
            $table->dropColumn([
                'client_manifest_drive_count',
                'client_manifest_device_count',
                'etech_verified_drive_count',
                'etech_verified_device_count',
                'service_selection'
            ]);
        });
    }
};