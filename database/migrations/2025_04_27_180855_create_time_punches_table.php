<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('time_punches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('time_card_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['clock_in', 'clock_out', 'break_in', 'break_out']);
            $table->dateTime('punch_time');
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('original_punch_id')->nullable();
            $table->unsignedBigInteger('replaced_by')->nullable();
            $table->unsignedBigInteger('edited_by')->nullable();
            $table->string('ip_address')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('time_punches');
    }
};
