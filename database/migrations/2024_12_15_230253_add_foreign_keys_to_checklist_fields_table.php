<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('checklist_fields', function (Blueprint $table) {
            $table->foreign(['category_id'])->references(['id'])->on('inventory_categories')->onUpdate('no action')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('checklist_fields', function (Blueprint $table) {
            $table->dropForeign('checklist_fields_category_id_foreign');
        });
    }
};
