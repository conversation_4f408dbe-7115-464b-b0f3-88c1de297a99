<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            $table->boolean('enable_email_notifications')->default(false);
            $table->json('notification_emails')->nullable();
            $table->boolean('include_full_data_in_email')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            $table->dropColumn([
                'enable_email_notifications',
                'notification_emails',
                'include_full_data_in_email'
            ]);
        });
    }
};
