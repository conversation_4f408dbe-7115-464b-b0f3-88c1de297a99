<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class UpdateRepeatIntervalEnumInTaskRecurrences extends Migration
{
    public function up()
    {
        // Update existing invalid default values
        DB::table('task_recurrences')->where('repeat_interval', 'none')->update(['repeat_interval' => 'once']);

        // Modify the ENUM column
        Schema::table('task_recurrences', function (Blueprint $table) {
            $table->enum('repeat_interval', ['once', 'daily', 'weekly', 'monthly', 'custom'])
                  ->default('once') // Set a valid default
                  ->change();
        });
    }

    public function down()
    {
        // Revert ENUM changes
        Schema::table('task_recurrences', function (Blueprint $table) {
            $table->enum('repeat_interval', ['none', 'daily', 'weekly', 'monthly', 'custom'])
                  ->default('none')
                  ->change();
        });

        // Restore the old default value
        DB::table('task_recurrences')->where('repeat_interval', 'once')->update(['repeat_interval' => 'none']);
    }
}
