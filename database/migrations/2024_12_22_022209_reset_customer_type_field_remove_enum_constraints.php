<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //turn enum into string
        Schema::table('customers', function (Blueprint $table) {
            $table->string('type')->change();
        });

        





    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //back into enum('eBay Customer','Website Customer','Facebook Customer','Bulk Buyer','Business','Residential Customer')
        Schema::table('customers', function (Blueprint $table) {
            $table->enum('type', ['eBay Customer', 'Website Customer', 'Facebook Customer', 'Bulk Buyer', 'Business', 'Residential Customer'])->change();
        });

    }
};
