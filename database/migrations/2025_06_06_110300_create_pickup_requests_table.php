<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pickup_requests', function (Blueprint $table) {
            $table->id();
            $table->string('contact_name');
            $table->string('business_name')->nullable();
            $table->string('email');
            $table->string('phone');
            $table->text('pickup_address');
            $table->text('pickup_items');
            $table->text('pickup_quantity');
            $table->text('property_location_details');
            $table->text('other_notes')->nullable();
            $table->json('pickup_details')->nullable(); // JSON storage for flexible pickup details
            $table->datetime('preferred_pickup_date')->nullable();
            $table->enum('status', ['incoming', 'pending', 'confirmed', 'completed', 'cancelled'])->default('incoming');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('event_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamp('submitted_at')->useCurrent();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pickup_requests');
    }
};


