<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pickup_requests', function (Blueprint $table) {
            if (!Schema::hasColumn('pickup_requests', 'management_token')) {
                $table->string('management_token', 20)->nullable()->unique()->after('pickup_details');
            }
            if (!Schema::hasColumn('pickup_requests', 'management_token_expires_at')) {
                $table->timestamp('management_token_expires_at')->nullable()->after('management_token');
            }
        });

        // Add index separately to avoid issues if columns already exist
        if (!Schema::hasIndex('pickup_requests', 'pickup_requests_mgmt_token_idx')) {
            Schema::table('pickup_requests', function (Blueprint $table) {
                $table->index(['management_token', 'management_token_expires_at'], 'pickup_requests_mgmt_token_idx');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pickup_requests', function (Blueprint $table) {
            if (Schema::hasIndex('pickup_requests', 'pickup_requests_mgmt_token_idx')) {
                $table->dropIndex('pickup_requests_mgmt_token_idx');
            }
            if (Schema::hasColumn('pickup_requests', 'management_token')) {
                $table->dropColumn('management_token');
            }
            if (Schema::hasColumn('pickup_requests', 'management_token_expires_at')) {
                $table->dropColumn('management_token_expires_at');
            }
        });
    }
};
