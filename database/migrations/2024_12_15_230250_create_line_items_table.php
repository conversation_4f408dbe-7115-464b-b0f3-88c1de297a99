<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('line_items', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('invoice_id')->index('line_items_invoice_id_foreign');
            $table->unsignedBigInteger('item_id');
            $table->text('description')->nullable();
            $table->integer('quantity');
            $table->decimal('price');
            $table->decimal('tax', 10)->default(0);
            $table->decimal('discount')->nullable();
            $table->decimal('subtotal');
            $table->timestamps();
            $table->unsignedBigInteger('tax_policy_id')->nullable()->index('line_items_tax_policy_id_foreign');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('line_items');
    }
};
