<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the events table exists
        if (Schema::hasTable('events')) {
            Schema::table('events', function (Blueprint $table) {
                // Add calendar-related columns if they don't exist
                if (!Schema::hasColumn('events', 'calendar_id')) {
                    $table->foreignId('calendar_id')->nullable()->constrained()->onDelete('cascade');
                }

                if (!Schema::hasColumn('events', 'start_date')) {
                    $table->dateTime('start_date')->nullable();
                }

                if (!Schema::hasColumn('events', 'end_date')) {
                    $table->dateTime('end_date')->nullable();
                }

                if (!Schema::hasColumn('events', 'all_day')) {
                    $table->boolean('all_day')->default(false);
                }

                if (!Schema::hasColumn('events', 'recurrence_pattern_id')) {
                    $table->foreignId('recurrence_pattern_id')->nullable()->constrained()->nullOnDelete();
                }

                if (!Schema::hasColumn('events', 'location')) {
                    $table->string('location')->nullable();
                }

                if (!Schema::hasColumn('events', 'color')) {
                    $table->string('color', 7)->nullable();
                }

                if (!Schema::hasColumn('events', 'is_active')) {
                    $table->boolean('is_active')->default(true);
                }

                if (!Schema::hasColumn('events', 'deleted_at')) {
                    $table->softDeletes();
                }
            });
        } else {
            // Create the events table if it doesn't exist
            Schema::create('events', function (Blueprint $table) {
                $table->id();
                $table->foreignId('calendar_id')->constrained()->onDelete('cascade');
                $table->string('title');
                $table->text('description')->nullable();
                $table->dateTime('start_date');
                $table->dateTime('end_date')->nullable();
                $table->boolean('all_day')->default(false);
                $table->foreignId('creator_id')->constrained('users');
                $table->foreignId('recurrence_pattern_id')->nullable()->constrained()->nullOnDelete();
                $table->string('location')->nullable();
                $table->string('color', 7)->nullable();
                $table->boolean('is_active')->default(true);
                $table->softDeletes();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We won't remove columns from an existing events table for safety
        // If we created the events table, drop it
        if (!Schema::hasTable('events_original')) {
            Schema::dropIfExists('events');
        }
    }
};
