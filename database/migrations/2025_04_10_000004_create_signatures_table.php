<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('signatures', function (Blueprint $table) {
            $table->id();
            $table->foreignId('certificate_id')->constrained()->onDelete('cascade');
            $table->foreignId('custody_event_id')->nullable()->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('signatory_id')->nullable(); // ID of the user or customer
            $table->string('signatory_name');
            $table->string('signatory_title')->nullable();
            $table->string('signatory_organization')->nullable();
            $table->text('signature_data'); // Base64 encoded signature or token
            $table->string('signature_image_path')->nullable(); // Path to the saved signature image file
            $table->dateTime('signature_date');
            $table->string('ip_address')->nullable();
            $table->string('role'); // client, driver, warehouse, technician, witness, supervisor
            $table->string('verification_code')->nullable(); // Code for external verification
            $table->timestamps();
            
            // Add index for faster access
            $table->index('signatory_id');
            $table->index('certificate_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('signatures');
    }
};
