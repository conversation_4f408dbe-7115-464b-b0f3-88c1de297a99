<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Migrate existing inventory images to the new context-based system
        $inventoryImages = DB::table('inventory_images')
            ->join('images', 'inventory_images.image_id', '=', 'images.id')
            ->select(
                'images.id as image_id',
                'inventory_images.inventory_id',
                'inventory_images.order'
            )
            ->get();

        foreach ($inventoryImages as $inventoryImage) {
            DB::table('images')
                ->where('id', $inventoryImage->image_id)
                ->update([
                    'context_type' => 'inventory',
                    'context_id' => $inventoryImage->inventory_id,
                    'order_number' => $inventoryImage->order,
                    'is_public' => false, // Inventory images are typically private
                    'updated_at' => now()
                ]);
        }

        // Log the migration results
        $migratedCount = $inventoryImages->count();
        if ($migratedCount > 0) {
            \Illuminate\Support\Facades\Log::info("Migrated {$migratedCount} inventory images to context-based system");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reset context fields for inventory images
        DB::table('images')
            ->where('context_type', 'inventory')
            ->update([
                'context_type' => null,
                'context_id' => null,
                'order_number' => null,
                'is_public' => false,
                'session_id' => null,
                'updated_at' => now()
            ]);
    }
};
