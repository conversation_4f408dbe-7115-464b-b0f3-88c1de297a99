<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('events', function (Blueprint $table) {
            // Pickup-specific fields
            $table->boolean('is_pickup_event')->default(false)->after('is_active');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null')->after('is_pickup_event');
            $table->text('pickup_address')->nullable()->after('customer_id');
            $table->text('pickup_items')->nullable()->after('pickup_address');
            $table->integer('staff_needed')->nullable()->after('pickup_items');
            $table->foreignId('assigned_driver_id')->nullable()->constrained('users')->onDelete('set null')->after('staff_needed');
            $table->string('contact_name')->nullable()->after('assigned_driver_id');
            $table->string('contact_phone')->nullable()->after('contact_name');
            $table->string('contact_email')->nullable()->after('contact_phone');
            $table->text('pickup_notes')->nullable()->after('contact_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('events', function (Blueprint $table) {
            $table->dropForeign(['customer_id']);
            $table->dropForeign(['assigned_driver_id']);
            $table->dropColumn([
                'is_pickup_event',
                'customer_id',
                'pickup_address',
                'pickup_items',
                'staff_needed',
                'assigned_driver_id',
                'contact_name',
                'contact_phone',
                'contact_email',
                'pickup_notes'
            ]);
        });
    }
};
