<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('certificates', function (Blueprint $table) {
            $table->id();
            $table->string('certificate_number')->unique();
            $table->foreignId('customer_id')->constrained()->onDelete('restrict');
            $table->foreignId('certifying_user_id')->nullable()->constrained('users')->onDelete('restrict');
            $table->string('status')->default('pending'); // pending, in_progress, completed, cancelled
            $table->dateTime('scheduled_destruction_date')->nullable();
            $table->dateTime('actual_destruction_date')->nullable();
            $table->string('destruction_method')->nullable();
            $table->string('destruction_location')->nullable();
            $table->text('notes')->nullable();
            $table->string('manifest_path')->nullable(); // Path to uploaded manifest file
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('certificates');
    }
};
