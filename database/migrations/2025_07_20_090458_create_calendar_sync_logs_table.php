<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('calendar_sync_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('microsoft_calendar_integration_id')->constrained()->onDelete('cascade');
            $table->foreignId('event_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('microsoft_event_id')->nullable(); // Outlook event ID
            $table->enum('action', ['create', 'update', 'delete', 'sync_all']);
            $table->enum('status', ['pending', 'success', 'failed', 'skipped']);
            $table->text('error_message')->nullable();
            $table->json('request_data')->nullable(); // Data sent to Microsoft
            $table->json('response_data')->nullable(); // Response from Microsoft
            $table->timestamp('started_at');
            $table->timestamp('completed_at')->nullable();
            $table->integer('retry_count')->default(0);
            $table->timestamps();
            
            $table->index(['microsoft_calendar_integration_id', 'status'], 'sync_logs_integration_status_idx');
            $table->index(['event_id', 'status'], 'sync_logs_event_status_idx');
            $table->index(['action', 'status'], 'sync_logs_action_status_idx');
            $table->index('started_at', 'sync_logs_started_at_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('calendar_sync_logs');
    }
};
