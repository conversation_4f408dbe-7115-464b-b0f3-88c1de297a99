<?php

namespace Database\Factories;

use App\Models\Inventory;
use App\Models\User;
use App\Models\InventoryCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

class InventoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Inventory::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->word(),
            'description' => $this->faker->sentence(),
            'status' => $this->faker->randomElement(array_keys(Inventory::STATUS)),
            'technician_id' => User::inRandomOrder()->first()->id ?? User::factory(),
            'inv_category_id' => InventoryCategory::inRandomOrder()->first()->id ?? InventoryCategory::factory(),
            'location' => $this->faker->word(),
            'suggested_price' => $this->faker->randomFloat(2, 10, 1000),
            'quantity' => $this->faker->numberBetween(1, 100),
            'unit_price' => $this->faker->randomFloat(2, 1, 100),
            'unit' => $this->faker->randomElement(array_keys(Inventory::UNITS)),
            'weight' => $this->faker->randomFloat(2, 1, 100),
            'condition' => $this->faker->word(),
            'asset_tag' => $this->faker->unique()->ean8(),
        ];
    }
}