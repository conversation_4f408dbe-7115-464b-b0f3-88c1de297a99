<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UncategorizedInventoryCategory extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create an uncategorized category in inventory_categories table
        $category = new \App\Models\InventoryCategory();
        $category->id = 1;
        $category->name = 'Uncategorized';
        $category->quantity_type = 'individual';
        $category->description = 'Items that have not been categorized, custom line items.';
        $category->code = 'CSTM';
        $category->status = 1;
        $category->tax_policy_id = 1;
        $category->department_id = 1;
        $category->ai_promptable = 0;
        $category->save();


    }
}
