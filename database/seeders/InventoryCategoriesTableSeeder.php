<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class InventoryCategoriesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('inventory_categories')->delete();
        
        \DB::table('inventory_categories')->insert(array (
            0 => 
            array (
                'id' => 3,
                'name' => 'Laptops',
                'quantity_type' => 'individual',
                'description' => 'All laptops here.',
                'code' => 'LPT',
                'status' => 1,
                'created_at' => '2024-12-08 03:54:53',
                'updated_at' => '2024-12-14 20:14:50',
                'deleted_at' => NULL,
                'tax_policy_id' => 2,
                'department_id' => 2,
                'ai_promptable' => 0,
            ),
            1 => 
            array (
                'id' => 4,
                'name' => 'Other Electronics',
                'quantity_type' => 'individual',
                'description' => 'Electronics that do not fit in other categories',
                'code' => 'ELC',
                'status' => 1,
                'created_at' => '2024-12-09 01:23:19',
                'updated_at' => '2024-12-14 22:19:50',
                'deleted_at' => NULL,
                'tax_policy_id' => 2,
                'department_id' => 1,
                'ai_promptable' => 0,
            ),
            2 => 
            array (
                'id' => 5,
                'name' => 'Desktops and AIOs',
                'quantity_type' => 'individual',
                'description' => 'Desktops including small-form-factor, micro-form-factor, towers, and all-in-one PCs go here',
                'code' => 'DSK',
                'status' => 1,
                'created_at' => '2024-12-09 01:23:37',
                'updated_at' => '2024-12-14 22:20:10',
                'deleted_at' => NULL,
                'tax_policy_id' => 2,
                'department_id' => 1,
                'ai_promptable' => 0,
            ),
            3 => 
            array (
                'id' => 6,
                'name' => 'Commodity By Weight',
                'quantity_type' => 'weight',
                'description' => 'Gaylords full of commodities, sold by the bin and weight.',
                'code' => 'CMWT',
                'status' => 1,
                'created_at' => '2024-12-14 20:21:16',
                'updated_at' => '2024-12-14 20:21:48',
                'deleted_at' => NULL,
                'tax_policy_id' => 1,
                'department_id' => 2,
                'ai_promptable' => 0,
            ),
            4 => 
            array (
                'id' => 7,
                'name' => 'Commodity By Weight Oz',
                'quantity_type' => 'weight',
                'description' => 'commodities by weight in ounces',
                'code' => 'CMDWTOZ',
                'status' => 1,
                'created_at' => '2024-12-15 03:14:08',
                'updated_at' => '2024-12-15 03:14:17',
                'deleted_at' => '2024-12-15 03:14:17',
                'tax_policy_id' => 2,
                'department_id' => 2,
                'ai_promptable' => 0,
            ),
            5 => 
            array (
                'id' => 8,
                'name' => 'Bulk Priced Per Piece',
                'quantity_type' => 'unit',
                'description' => 'bulk items priced per piece',
                'code' => 'PRIPER',
                'status' => 1,
                'created_at' => '2024-12-15 15:39:32',
                'updated_at' => '2024-12-15 15:39:32',
                'deleted_at' => NULL,
                'tax_policy_id' => 2,
                'department_id' => 1,
                'ai_promptable' => 0,
            ),
            6 => 
            array (
                'id' => 9,
                'name' => 'Services',
                'quantity_type' => 'unit',
                'description' => NULL,
                'code' => 'SVC',
                'status' => 1,
                'created_at' => '2024-12-15 19:27:23',
                'updated_at' => '2024-12-15 19:27:23',
                'deleted_at' => NULL,
                'tax_policy_id' => 1,
                'department_id' => 1,
                'ai_promptable' => 0,
            ),
        ));
        
        
    }
}