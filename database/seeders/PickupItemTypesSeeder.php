<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\GlobalConfig;

class PickupItemTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if pickup item types are already configured
        $existingItemTypes = GlobalConfig::getPickupItemTypes();

        if (empty($existingItemTypes)) {
            // Insert default pickup item types
            $defaultItemTypes = GlobalConfig::getDefaultPickupItemTypes();
            GlobalConfig::setPickupItemTypes($defaultItemTypes);

            $this->command->info('Default pickup item types have been seeded.');
        } else {
            $this->command->info('Pickup item types already exist. Skipping seeding.');
        }
    }
}
