<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class TaxPoliciesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('tax_policies')->delete();
        
        \DB::table('tax_policies')->insert(array (
            0 => 
            array (
                'id' => 1,
                'created_at' => '2024-12-14 17:21:18',
                'updated_at' => '2024-12-14 17:21:18',
                'name' => 'No Tax',
                'rate' => '0.00',
                'description' => 'No applicable tax for this policy.',
                'deleted_at' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'created_at' => '2024-12-14 17:21:18',
                'updated_at' => '2024-12-14 17:21:18',
                'name' => '2024 Colorado Springs Retail Sales Tax',
                'rate' => '8.20',
                'description' => 'Retail sales tax rate applicable for 2024 in Colorado Springs.',
                'deleted_at' => NULL,
            ),
        ));
        
        
    }
}