<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\UserGroup;
use App\Models\Permission;
class UserRolesSeeder extends Seeder
{
// DatabaseSeeder.php



public function run()
{
    $adminGroup = UserGroup::firstOrCreate(['name' => 'Admin'], ['description' => 'Full access']);
    $staffGroup = UserGroup::firstOrCreate(['name' => 'Staff'], ['description' => 'Almost full access except for global settings and user management']);
    $observerGroup = UserGroup::firstOrCreate(['name' => 'Observer'], ['description' => 'Read-only access to everything']);
    $financeGroup = UserGroup::firstOrCreate(['name' => 'Finance'], ['description' => 'Access to financial reports and settings']);
    $warehouseGroup = UserGroup::firstOrCreate(['name' => 'Warehouse'], ['description' => 'Access to inventory and warehouse settings']);

    $this->command->info('User groups created/verified successfully.');
}

}
