<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\UserGroup;
use App\Models\Permission;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Artisan;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if user with ID 1 already exists
        $existingUser = User::find(1);
        
        if ($existingUser) {
            $this->command->info('User with ID 1 already exists. Skipping admin user creation.');
            return;
        }

        // Get admin credentials from environment variables
        $adminEmail = env('ADMIN_USERNAME', '<EMAIL>');
        $defaultPassword = env('DEFAULT_PASSWORD', 'admin123');

        // Create the admin user with ID 1
        $user = new User();
        $user->id = 1; // Important: This must be ID 1 for other seeders to work
        $user->name = 'Admin User';
        $user->email = $adminEmail;
        $user->password = Hash::make($defaultPassword);
        $user->role = 'admin';
        $user->enabled = true;
        $user->email_verified_at = now();
        $user->save();

        $this->command->info("Admin user created successfully:");
        $this->command->info("Email: {$adminEmail}");
        $this->command->info("Password: {$defaultPassword}");

        // Sync permissions first to ensure Admin group and permissions exist
        $this->command->info("Syncing permissions...");
        Artisan::call('permissions:sync');
        $this->command->info("Permissions synced successfully.");

        // Add user to Admin group
        $adminGroup = UserGroup::where('name', 'Admin')->first();
        if ($adminGroup) {
            // Check if user is already in the group
            if (!$user->groups()->where('user_group_id', $adminGroup->id)->exists()) {
                $user->groups()->attach($adminGroup->id);
                $this->command->info("User added to Admin group successfully.");
            } else {
                $this->command->info("User is already in the Admin group.");
            }
        } else {
            $this->command->error("Admin group not found. Please check the UserRolesSeeder.");
        }

        $this->command->warn("Please change the default password after first login!");
    }
}
