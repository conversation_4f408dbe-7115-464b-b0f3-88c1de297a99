<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Customer;
use App\Models\Inventory;
use App\Models\UserGroup;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Artisan;

class DevTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $email = $this->command->ask('Enter the email for the admin user', '<EMAIL>');
        $password = $this->command->secret('Enter the password for the admin user');

        if (empty($password)) {
            $this->command->error('Password cannot be empty.');
            return;
        }

        $user = User::updateOrCreate(
            ['email' => $email],
            [
                'name' => 'Admin User',
                'password' => Hash::make($password),
                'role' => 'admin',
                'enabled' => true,
                'email_verified_at' => now(),
            ]
        );

        $this->command->info("Admin user created/updated successfully for email: {$email}");

        // Sync permissions to ensure Admin group and permissions exist
        $this->command->info("Syncing permissions...");
        Artisan::call('permissions:sync');
        $this->command->info("Permissions synced successfully.");

        // Add user to Admin group
        $adminGroup = UserGroup::where('name', 'Admin')->first();
        if ($adminGroup) {
            if (!$user->groups()->where('user_group_id', $adminGroup->id)->exists()) {
                $user->groups()->attach($adminGroup->id);
                $this->command->info("User added to Admin group successfully.");
            } else {
                $this->command->info("User is already in the Admin group.");
            }
        } else {
            $this->command->error("Admin group not found. Please check the UserRolesSeeder.");
        }

        $this->command->info('Creating 10 test customers...');
        Customer::factory()->count(10)->create();
        $this->command->info('10 test customers created successfully!');

        $this->command->info('Creating 10 test inventory items...');
        Inventory::factory()->count(10)->create();
        $this->command->info('10 test inventory items created successfully!');
    }
}