<?php

namespace Database\Seeders;

use App\Models\EmailTemplate;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EmailTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get template definitions
        $definitions = EmailTemplate::templateDefinitions();

        foreach ($definitions as $name => $definition) {
            // Only create if it doesn't exist
            if (!EmailTemplate::where('name', $name)->exists()) {
                EmailTemplate::create([
                    'name' => $name,
                    'display_name' => $definition['display_name'],
                    'description' => $definition['description'],
                    'subject' => $this->getDefaultSubject($name),
                    'from_name' => null, // Use system default
                    'from_email' => null, // Use system default
                    'body_html' => $this->getDefaultTemplate($name),
                    'available_variables' => $definition['variables'],
                    'is_active' => true,
                ]);
            }
        }
    }

    /**
     * Get default subject for template
     */
    private function getDefaultSubject($templateName): string
    {
        switch ($templateName) {
            case 'daily_timeclock_report':
                return '{app_name} - Daily Timeclock Report for {report_date}';
                
            case 'pickup_request_notification':
                return '{app_name} - {notification_type} Pickup Request #{pickup_id}';

            case 'pickup_confirmation':
                return 'Your pickup is scheduled - {pickup_date}';

            case 'pickup_confirmation_reminder':
                return 'Pickup Confirmation Required - {app_name}';

            case 'pickup_final_reminder':
                return 'Pickup Reminder - Service scheduled for {pickup_date}';

            case 'form_submission_notification':
                return '{app_name} - New Form Submission: {form_name}';

            case 'form_submission_notification_full':
                return '{app_name} - New Form Submission: {form_name} (Full Data)';

            case 'form_submission_pdf_delivery':
                return 'Your {form_name} submission has been approved - PDF attached';

            case 'pickup_cancellation':
                return 'Pickup Appointment Cancelled - Request #{pickup_id}';

            default:
                return '{app_name} - Notification';
        }
    }

    /**
     * Get default HTML template
     */
    private function getDefaultTemplate($templateName): string
    {
        switch ($templateName) {
            case 'daily_timeclock_report':
                return '<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f4f4f4; padding: 20px; text-align: center; border-radius: 5px; }
        .summary-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .summary-table th, .summary-table td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        .summary-table th { background-color: #f4f4f4; font-weight: bold; }
        .footer { margin-top: 30px; color: #666; font-size: 14px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 style="color: #ffffff; margin: 0;">{app_name}</h1>
            <h2 style="color: #333333; margin: 10px 0;">Daily Timeclock Report</h2>
            <p style="color: #333333;"><strong>Report Date:</strong> {report_date}</p>
        </div>
        
        <p>Here is the daily timeclock summary for {report_date}:</p>
        
        <table class="summary-table">
            <tr>
                <th>Summary</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Total Employees</td>
                <td>{employee_count}</td>
            </tr>
            <tr>
                <td>Total Hours Worked</td>
                <td>{total_hours_worked} hours</td>
            </tr>
            <tr>
                <td>Total Break Hours</td>
                <td>{total_break_hours} hours</td>
            </tr>
        </table>
        
        <h3>Employee Details</h3>
        {employee_details}
        
        <div class="footer">
            <p>This report was automatically generated on {current_date}.</p>
            <p>&copy; {app_name} - Automated Timeclock Reporting System</p>
        </div>
    </div>
</body>
</html>';

            case 'pickup_request_notification':
                return '<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #4a90e2; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { padding: 20px; background-color: #f9f9f9; border-radius: 0 0 5px 5px; }
        .info-table { width: 100%; margin: 20px 0; border-collapse: collapse; }
        .info-table td { padding: 8px 0; vertical-align: top; }
        .info-table td:first-child { font-weight: bold; width: 35%; color: #555; }
        .btn { display: inline-block; padding: 12px 24px; background-color: #4a90e2; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { margin-top: 20px; color: #666; font-size: 12px; text-align: center; }
        .status-badge { padding: 4px 8px; border-radius: 3px; font-size: 12px; font-weight: bold; }
        .status-pending { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 style="color: #ffffff; margin: 0;">{app_name}</h1>
            <h2 style="color: #ffffff; margin: 10px 0;">{notification_type} Pickup Request</h2>
            <p style="color: #ffffff; margin: 0;">Request ID: <strong style="color: #ffffff;">#{pickup_id}</strong></p>
        </div>
        
        <div class="content">
            <p>A <strong>{notification_type}</strong> pickup request has been submitted. Here are the details:</p>
            
            <table class="info-table">
                <tr>
                    <td>Status:</td>
                    <td><span class="status-badge status-pending">{status}</span></td>
                </tr>
                <tr>
                    <td>Contact Name:</td>
                    <td>{contact_name}</td>
                </tr>
                <tr>
                    <td>Business:</td>
                    <td>{business_name}</td>
                </tr>
                <tr>
                    <td>Email:</td>
                    <td><a href="mailto:{email}">{email}</a></td>
                </tr>
                <tr>
                    <td>Phone:</td>
                    <td><a href="tel:{phone}">{phone}</a></td>
                </tr>
                <tr>
                    <td>Pickup Date:</td>
                    <td>{pickup_date}</td>
                </tr>
                <tr>
                    <td>Address:</td>
                    <td>{pickup_address}</td>
                </tr>
                <tr>
                    <td>Load Size:</td>
                    <td>{load_size}</td>
                </tr>
                <tr>
                    <td>Item Types:</td>
                    <td>{item_types}</td>
                </tr>
                <tr>
                    <td>Driver Instructions:</td>
                    <td>{driver_instructions}</td>
                </tr>
            </table>

            <div style="text-align: center;">
                <a href="{view_url}" class="btn" style="display: inline-block; padding: 12px 24px; background-color: #4a90e2; color: #ffffff; text-decoration: none; border-radius: 5px; margin: 20px 0;">View Full Pickup Request</a>
            </div>

            <div class="footer">
                <p>This notification was automatically generated on {current_date}.</p>
                <p>&copy; {app_name} - Pickup Request Management System</p>
            </div>
        </div>
    </div>
</body>
</html>';

            case 'pickup_confirmation':
                return '<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { padding: 20px; background-color: #f9f9f9; border-radius: 0 0 5px 5px; }
        .info-table { width: 100%; margin: 20px 0; border-collapse: collapse; }
        .info-table td { padding: 8px 0; vertical-align: top; }
        .info-table td:first-child { font-weight: bold; width: 35%; color: #555; }
        .btn { display: inline-block; padding: 12px 24px; background-color: #28a745; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .btn-secondary { background-color: #6c757d; }
        .footer { margin-top: 20px; color: #666; font-size: 12px; text-align: center; }
        .highlight { background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 style="color: #ffffff; margin: 0;">✓ Pickup Scheduled</h1>
            <p style="color: #ffffff; margin: 10px 0;">Your pickup has been successfully scheduled</p>
        </div>

<div class="content">
    <p>Hello {contact_name},</p>
    <p>Good news — your pickup request with E-Tech Recyclers has been approved!</p>
    <p><strong>Please remember to confirm your appointment 24 to 72 hours before the scheduled time.</strong> If not confirmed, it may be cancelled.</p>
    <p>You’ll get a reminder email 48 hours before the pickup. That email will include a simple link to confirm your appointment. Or, you can call or email us any time to confirm.</p>
    <p>To view or manage your appointment, use this <a href="{management_url}">management link</a>. If it says your appointment is confirmed, you’re all set and we’ll be there as scheduled.</p>

    <p>Pickup details:</p>

    <div class="highlight">
        <strong>📅 Pickup Date & Time:</strong> {pickup_date}
    </div>

    <table class="info-table">
        <tr><td>Request ID:</td><td>#{pickup_id}</td></tr>
        <tr><td>Contact:</td><td>{contact_name}</td></tr>
        <tr><td>Business:</td><td>{business_name}</td></tr>
        <tr><td>Pickup Address:</td><td>{pickup_address}</td></tr>
        <tr><td>Items:</td><td>{item_types}</td></tr>
        <tr><td>Load Size:</td><td>{load_size}</td></tr>
        <tr><td>Assigned Driver:</td><td>{driver_name}</td></tr>
        <tr><td>Special Instructions:</td><td>{driver_instructions}</td></tr>
    </table>

    <div style="text-align: center; margin: 30px 0;">
        <a href="{management_url}" class="btn" style="display: inline-block; padding: 12px 24px; background-color: #28a745; color: #ffffff; text-decoration: none; border-radius: 5px; margin: 20px 0;">Manage Your Appointment</a>
    </div>

    <p><strong>With the link above, you can:</strong></p>
    <ul>
        <li>View your appointment details</li>
        <li>Reschedule your pickup</li>
        <li>Cancel if needed</li>
        <li>Reach out with any questions</li>
    </ul>

    <p>If you have any questions or need help, just use the link above or contact us directly.</p>

    <p>Thanks for choosing {app_name}!</p>

    <div class="footer">
        <p>This confirmation was sent on {current_date}.</p>
        <p>&copy; {app_name} - Pickup Scheduling System</p>
    </div>
</div>

    </div>
</body>
</html>';

            case 'pickup_confirmation_reminder':
                return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pickup Confirmation Required</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .email-wrapper { max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; }
        .container { background-color: #ffffff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); }
        .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e0e0e0; }
        .header h1 { margin: 0; font-size: 24px; }
        .logo { font-size: 28px; font-weight: bold; color: #3498db; margin-bottom: 10px; }
        .pickup-details { background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #3498db; }
        .pickup-details h3 { margin-top: 0; color: #2c3e50; }
        .detail-item { margin: 10px 0; display: flex; align-items: flex-start; }
        .detail-label { font-weight: bold; min-width: 100px; color: #34495e; }
        .detail-value { flex: 1; }
        .alert { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .alert-icon { font-size: 18px; margin-right: 8px; }
        .action-buttons { text-align: center; margin: 30px 0; }
        .btn { display: inline-block; padding: 12px 24px; margin: 0 10px; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px; transition: background-color 0.3s; }
        .btn-confirm { background-color: #27ae60; }
        .btn-manage { background-color: #3498db; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #7f8c8d; font-size: 14px; }
        .contact-info { margin-top: 20px; padding: 15px; background-color: #ecf0f1; border-radius: 6px; }
        @media only screen and (max-width: 600px) {
            .email-wrapper { padding: 10px; }
            .container { padding: 20px; }
            .btn { display: block; margin: 10px 0; }
            .detail-item { flex-direction: column; }
            .detail-label { min-width: auto; margin-bottom: 5px; }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="container">
            <div class="header">
                <div class="logo">{app_name}</div>
                <h1 style="color: #2c3e50; margin: 0; font-size: 24px;">Pickup Confirmation Required</h1>
        </div>

        <p>Dear {contact_name},</p>

        <p>Your electronics pickup is scheduled for <strong>{pickup_date}</strong>, which is less than 48 hours away.</p>

        <div class="alert">
            <span class="alert-icon">⏰</span>
            <strong>Action Required:</strong> Please confirm your pickup appointment to ensure we\'re prepared for your scheduled service.
        </div>

        <div class="pickup-details">
            <h3>📅 Pickup Details</h3>
            <div class="detail-item">
                <div class="detail-label">Date & Time:</div>
                <div class="detail-value">{pickup_date}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Address:</div>
                <div class="detail-value">{pickup_address}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Request ID:</div>
                <div class="detail-value">#{pickup_id}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Business:</div>
                <div class="detail-value">{business_name}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Load Size:</div>
                <div class="detail-value">{load_size}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Items:</div>
                <div class="detail-value">{item_types}</div>
            </div>
        </div>

        <div class="action-buttons">
            <a href="{confirm_url}" class="btn btn-confirm" style="display: inline-block; padding: 12px 24px; background-color: #27ae60; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px; margin: 0 10px;">✅ Confirm Pickup</a>
            <a href="{management_url}" class="btn btn-manage" style="display: inline-block; padding: 12px 24px; background-color: #3498db; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px; margin: 0 10px;">🔧 Manage Appointment</a>
        </div>

        <p><strong>What happens next?</strong></p>
        <ul>
            <li>✅ <strong>If you confirm:</strong> We\'ll proceed with your scheduled pickup as planned</li>
            <li>❌ <strong>If you need to cancel:</strong> Use the "Manage Appointment" link above</li>
            <li>📞 <strong>If you have questions:</strong> Contact us using the information below</li>
        </ul>

        <div class="contact-info">
            <h4>Need Help?</h4>
            <p>If you have any questions or need to make changes to your pickup, please contact us:</p>
            <p>
                📧 Email: <a href="mailto:{contact_email}">{contact_email}</a><br>
                🌐 Appointment Management: <a href="{management_url}">Click Here</a>
            </p>
        </div>

        <div class="footer">
            <p><strong>{app_name}</strong> - Professional Electronics Recycling Services</p>
            <p>This is an automated reminder. Please do not reply directly to this email.</p>
            <p>&copy; {current_date} {app_name}. All rights reserved.</p>
        </div>
    </div>
    </div>
</body>
</html>';

            case 'pickup_final_reminder':
                return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pickup Reminder - Service Scheduled</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .email-wrapper { max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; }
        .container { background-color: #ffffff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); }
        .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e0e0e0; }
        .header h1 { margin: 0; font-size: 24px; }
        .logo { font-size: 28px; font-weight: bold; color: #27ae60; margin-bottom: 10px; }
        .pickup-details { background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #27ae60; }
        .pickup-details h3 { margin-top: 0; color: #2c3e50; }
        .detail-item { margin: 10px 0; display: flex; align-items: flex-start; }
        .detail-label { font-weight: bold; min-width: 100px; color: #34495e; }
        .detail-value { flex: 1; }
        .status-confirmed { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .status-icon { font-size: 18px; margin-right: 8px; }
        .action-buttons { text-align: center; margin: 30px 0; }
        .btn { display: inline-block; padding: 12px 24px; margin: 0 10px; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px; transition: background-color 0.3s; }
        .btn-manage { background-color: #3498db; }
        .btn-cancel { background-color: #e74c3c; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #7f8c8d; font-size: 14px; }
        .contact-info { margin-top: 20px; padding: 15px; background-color: #ecf0f1; border-radius: 6px; }
        @media only screen and (max-width: 600px) {
            .email-wrapper { padding: 10px; }
            .container { padding: 20px; }
            .btn { display: block; margin: 10px 0; }
            .detail-item { flex-direction: column; }
            .detail-label { min-width: auto; margin-bottom: 5px; }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="container">
            <div class="header">
                <div class="logo">{app_name}</div>
                <h1 style="color: #2c3e50; margin: 0; font-size: 24px;">Pickup Scheduled - Ready to Proceed</h1>
            </div>

            <p>Dear {contact_name},</p>

            <p>This is a friendly reminder that your confirmed electronics pickup is scheduled for <strong>{pickup_date}</strong>, which is less than 48 hours away.</p>

            <div class="status-confirmed">
                <span class="status-icon">✅</span>
                <strong>Status: Confirmed</strong> - Your pickup appointment is confirmed and scheduled to proceed as planned.
            </div>

            <div class="pickup-details">
                <h3>📅 Confirmed Pickup Details</h3>
                <div class="detail-item">
                    <div class="detail-label">Date & Time:</div>
                    <div class="detail-value">{pickup_date}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Address:</div>
                    <div class="detail-value">{pickup_address}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Request ID:</div>
                    <div class="detail-value">#{pickup_id}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Business:</div>
                    <div class="detail-value">{business_name}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Load Size:</div>
                    <div class="detail-value">{load_size}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Items:</div>
                    <div class="detail-value">{item_types}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Assigned Driver:</div>
                    <div class="detail-value">{driver_name}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Special Instructions:</div>
                    <div class="detail-value">{driver_instructions}</div>
                </div>
            </div>

            <p><strong>What to expect:</strong></p>
            <ul>
                <li>🚚 Our team will arrive at the scheduled time</li>
                <li>📋 We\'ll collect the items specified in your request</li>
                <li>🔒 All materials will be handled according to our security protocols</li>
                <li>📄 You\'ll receive documentation for your records</li>
            </ul>

            <div class="action-buttons">
                <a href="{management_url}" class="btn btn-manage" style="display: inline-block; padding: 12px 24px; background-color: #3498db; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px; margin: 0 10px;">🔧 Manage Appointment</a>
            </div>

            <p><strong>Need to make changes?</strong></p>
            <ul>
                <li>📞 <strong>If you need to cancel or reschedule:</strong> Please contact us immediately using the information below or use the management link above</li>
                <li>✉️ <strong>If you have questions:</strong> Reply to this email or call us directly</li>
                <li>🔧 <strong>For minor changes:</strong> Use the "Manage Appointment" link above</li>
            </ul>

            <div class="contact-info">
                <h4>Contact Information</h4>
                <p>If you need to cancel, reschedule, or have any questions, please contact us:</p>
                <p>
                    📧 Email: <a href="mailto:{contact_email}">{contact_email}</a><br>
                    🌐 Appointment Management: <a href="{management_url}">Click Here</a>
                </p>
                <p><strong>Note:</strong> For cancellations or changes less than 24 hours before your appointment, please call us directly.</p>
            </div>

            <div class="footer">
                <p><strong>{app_name}</strong> - Professional Electronics Recycling Services</p>
                <p>We look forward to serving you. Thank you for choosing our services!</p>
                <p>&copy; {current_date} {app_name}. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>';

            case 'form_submission_notification':
                return '<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #4a90e2; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { padding: 20px; background-color: #f9f9f9; border-radius: 0 0 5px 5px; }
        .info-table { width: 100%; margin: 20px 0; border-collapse: collapse; }
        .info-table td { padding: 8px 0; vertical-align: top; }
        .info-table td:first-child { font-weight: bold; width: 35%; color: #555; }
        .btn { display: inline-block; padding: 12px 24px; background-color: #4a90e2; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { margin-top: 20px; color: #666; font-size: 12px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 style="color: #ffffff; margin: 0;">{app_name}</h1>
            <h2 style="color: #ffffff; margin: 10px 0;">New Form Submission</h2>
            <p style="color: #ffffff; margin: 0;">Form: <strong style="color: #ffffff;">{form_name}</strong></p>
        </div>

        <div class="content">
            <p>A new form submission has been received. Here are the details:</p>

            <table class="info-table">
                <tr>
                    <td>Submission ID:</td>
                    <td>#{submission_id}</td>
                </tr>
                <tr>
                    <td>Form:</td>
                    <td>{form_name}</td>
                </tr>
                <tr>
                    <td>Submitted by:</td>
                    <td>{submitter_name}</td>
                </tr>
                <tr>
                    <td>Email:</td>
                    <td><a href="mailto:{submitter_email}">{submitter_email}</a></td>
                </tr>
                <tr>
                    <td>Phone:</td>
                    <td><a href="tel:{submitter_phone}">{submitter_phone}</a></td>
                </tr>
                <tr>
                    <td>Submitted on:</td>
                    <td>{submission_date}</td>
                </tr>
            </table>

            <div style="text-align: center;">
                <a href="{view_url}" class="btn" style="display: inline-block; padding: 12px 24px; background-color: #4a90e2; color: #ffffff; text-decoration: none; border-radius: 5px; margin: 20px 0;">View Full Submission</a>
            </div>

            <div class="footer">
                <p>This notification was automatically generated on {current_date}.</p>
                <p>&copy; {app_name} - Form Management System</p>
            </div>
        </div>
    </div>
</body>
</html>';

            case 'form_submission_notification_full':
                return '<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { background-color: #4a90e2; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { padding: 20px; background-color: #f9f9f9; border-radius: 0 0 5px 5px; }
        .info-table { width: 100%; margin: 20px 0; border-collapse: collapse; }
        .info-table td { padding: 8px 0; vertical-align: top; }
        .info-table td:first-child { font-weight: bold; width: 35%; color: #555; }
        .form-data-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .form-data-table th, .form-data-table td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        .form-data-table th { background-color: #f4f4f4; font-weight: bold; }
        .btn { display: inline-block; padding: 12px 24px; background-color: #4a90e2; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { margin-top: 20px; color: #666; font-size: 12px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 style="color: #ffffff; margin: 0;">{app_name}</h1>
            <h2 style="color: #ffffff; margin: 10px 0;">New Form Submission</h2>
            <p style="color: #ffffff; margin: 0;">Form: <strong style="color: #ffffff;">{form_name}</strong></p>
        </div>

        <div class="content">
            <p>A new form submission has been received. Here are the details:</p>

            <table class="info-table">
                <tr>
                    <td>Submission ID:</td>
                    <td>#{submission_id}</td>
                </tr>
                <tr>
                    <td>Form:</td>
                    <td>{form_name}</td>
                </tr>
                <tr>
                    <td>Submitted by:</td>
                    <td>{submitter_name}</td>
                </tr>
                <tr>
                    <td>Email:</td>
                    <td><a href="mailto:{submitter_email}">{submitter_email}</a></td>
                </tr>
                <tr>
                    <td>Phone:</td>
                    <td><a href="tel:{submitter_phone}">{submitter_phone}</a></td>
                </tr>
                <tr>
                    <td>Submitted on:</td>
                    <td>{submission_date}</td>
                </tr>
            </table>

            <h3>Submitted Data</h3>
            {form_data_table}

            <div style="text-align: center;">
                <a href="{view_url}" class="btn" style="display: inline-block; padding: 12px 24px; background-color: #4a90e2; color: #ffffff; text-decoration: none; border-radius: 5px; margin: 20px 0;">View Full Submission</a>
            </div>

            <div class="footer">
                <p>This notification was automatically generated on {current_date}.</p>
                <p>&copy; {app_name} - Form Management System</p>
            </div>
        </div>
    </div>
</body>
</html>';

            case 'form_submission_pdf_delivery':
                return '<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { padding: 20px; background-color: #f9f9f9; border-radius: 0 0 5px 5px; }
        .info-table { width: 100%; margin: 20px 0; border-collapse: collapse; }
        .info-table td { padding: 8px 0; vertical-align: top; }
        .info-table td:first-child { font-weight: bold; width: 35%; color: #555; }
        .highlight { background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #28a745; }
        .attachment-notice { background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #4a90e2; }
        .footer { margin-top: 20px; color: #666; font-size: 12px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 style="color: #ffffff; margin: 0;">✓ Form Approved</h1>
            <p style="color: #ffffff; margin: 10px 0;">Your submission has been approved and processed</p>
        </div>

        <div class="content">
            <p>Hello {submitter_contact|{submitter_name}},</p>

            <p>Great news! Your <strong>{form_name}</strong> submission has been approved and processed.</p>

            <div class="highlight">
                <strong>📋 Submission Details:</strong><br>
                Submission ID: #{submission_id}<br>
                Approved on: {approval_date}<br>
                Approved by: {approved_by}
            </div>

            <div class="attachment-notice">
                <strong>📎 PDF Document Attached</strong><br>
                Please find your completed form document attached to this email. This PDF contains all the information you submitted along with our processing details.
            </div>

            <table class="info-table">
                <tr>
                    <td>Form:</td>
                    <td>{form_name}</td>
                </tr>
                <tr>
                    <td>Submitted on:</td>
                    <td>{submission_date}</td>
                </tr>
                <tr>
                    <td>Processed on:</td>
                    <td>{approval_date}</td>
                </tr>
                <tr>
                    <td>Your Name:</td>
                    <td>{submitter_contact|{submitter_name}}</td>
                </tr>
                <tr>
                    <td>Organization:</td>
                    <td>{submitter_name}</td>
                </tr>
                <tr>
                    <td>Email:</td>
                    <td>{submitter_email}</td>
                </tr>
            </table>

            <p>Please keep this PDF for your records. If you have any questions about this submission or need additional assistance, please don\'t hesitate to contact us.</p>

            <p>Thank you for using {app_name}!</p>

            <div class="footer">
                <p>This email was sent on {current_date}.</p>
                <p>&copy; {app_name} - Form Processing System</p>
            </div>
        </div>
    </div>
</body>
</html>';

            case 'pickup_cancellation':
                return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pickup Appointment Cancelled</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .email-wrapper { max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; }
        .container { background-color: #ffffff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); }
        .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e0e0e0; }
        .header h1 { margin: 0; font-size: 24px; }
        .logo { font-size: 28px; font-weight: bold; color: #e74c3c; margin-bottom: 10px; }
        .pickup-details { background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #e74c3c; }
        .pickup-details h3 { margin-top: 0; color: #2c3e50; }
        .detail-item { margin: 10px 0; display: flex; align-items: flex-start; }
        .detail-label { font-weight: bold; min-width: 140px; color: #34495e; }
        .detail-value { flex: 1; }
        .cancellation-notice { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .cancellation-icon { font-size: 18px; margin-right: 8px; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #7f8c8d; font-size: 14px; }
        .contact-info { margin-top: 20px; padding: 15px; background-color: #ecf0f1; border-radius: 6px; }
        @media only screen and (max-width: 600px) {
            .email-wrapper { padding: 10px; }
            .container { padding: 20px; }
            .detail-item { flex-direction: column; }
            .detail-label { min-width: auto; margin-bottom: 5px; }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="container">
            <div class="header">
                <div class="logo">{app_name}</div>
                <h1 style="color: #2c3e50; margin: 0; font-size: 24px;">Pickup Appointment Cancelled</h1>
            </div>

            <p>Dear {contact_name},</p>

            <p>We are writing to inform you that your pickup appointment has been cancelled.</p>

            <div class="cancellation-notice">
                <span class="cancellation-icon">❌</span>
                <strong>Cancellation Details:</strong><br>
                <strong>Cancelled by:</strong> {cancelled_by}<br>
                <strong>Cancellation Date:</strong> {cancellation_date}<br>
                <strong>Reason:</strong> {cancellation_reason}
            </div>

            <div class="pickup-details">
                <h3>📅 Original Pickup Details</h3>
                <div class="detail-item">
                    <div class="detail-label">Request ID:</div>
                    <div class="detail-value">#{pickup_id}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Scheduled Date & Time:</div>
                    <div class="detail-value">{pickup_date}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Address:</div>
                    <div class="detail-value">{pickup_address}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Business:</div>
                    <div class="detail-value">{business_name}</div>
                </div>
            </div>

            <p><strong>What happens next?</strong></p>
            <ul>
                <li>🔄 <strong>If you need to reschedule:</strong> Please contact us to arrange a new pickup appointment</li>
                <li>📞 <strong>If you have questions:</strong> Contact us using the information below</li>
                <li>🆕 <strong>For a new pickup:</strong> You can submit a new pickup request through our website</li>
            </ul>

            <div class="contact-info">
                <h4>Contact Information</h4>
                <p>If you have any questions about this cancellation or need to schedule a new pickup, please contact us:</p>
                <p>
                    📧 Email: <a href="mailto:{contact_email}">{contact_email}</a>
                </p>
                <p>We apologize for any inconvenience this cancellation may cause.</p>
            </div>

            <div class="footer">
                <p><strong>{app_name}</strong> - Professional Electronics Recycling Services</p>
                <p>This cancellation notice was sent on {current_date}.</p>
                <p>&copy; {current_date} {app_name}. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>';

            default:
                return '<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f4f4f4; padding: 20px; text-align: center; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{app_name}</h1>
        <p>Email Template</p>
    </div>
    <p>This is a basic email template. Please customize it for your needs.</p>
    <p style="color: #666; font-size: 12px; margin-top: 30px;">
        Sent on {current_date} from {app_name}.
    </p>
</body>
</html>';
        }
    }
}