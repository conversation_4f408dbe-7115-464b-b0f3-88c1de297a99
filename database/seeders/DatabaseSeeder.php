<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user first (required for other seeders)
        $this->call(AdminUserSeeder::class);

        $this->call(TaxPolicySeeder::class);
        $this->call(DepartmentsTableSeeder::class);
        $this->call(InventoryCategoriesTableSeeder::class);
        $this->call(InventoryChecklistItemsSeeder::class);
        $this->call(ChecklistFieldsTableSeeder::class);
        $this->call(UncategorizedInventoryCategory::class);
        $this->call(GlobalConfigSeeder::class);
        $this->call(UserRolesSeeder::class);
        $this->call(EmailTemplateSeeder::class);
    }
}
