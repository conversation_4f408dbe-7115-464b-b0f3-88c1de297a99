<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TestCustomerSeeder extends Seeder
{
    /**
     * Run the database seeds for test customers.
     * This seeder is only run when specifically called, not during regular db:seed
     */
    public function run(): void
    {
        $this->command->info('Creating 20 test customer accounts...');
        
        DB::table('customers')->insert([
            [
                'name' => '<PERSON>',
                'contact' => '<PERSON>',
                'business_name' => null,
                'nickname' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '123 Main Street, Anytown, ST 12345',
                'notes' => 'Regular customer, prefers email communication',
                'type' => 'Website Customer',
                'website' => null,
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => '<PERSON>',
                'contact' => '<PERSON>',
                'business_name' => null,
                'nickname' => null,
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '456 Oak Avenue, Springfield, ST 67890',
                'notes' => 'Prefers phone calls, has specific pickup times',
                'type' => 'Residential Customer',
                'website' => null,
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'TechCorp Solutions',
                'contact' => 'Michael Davis',
                'business_name' => 'TechCorp Solutions LLC',
                'nickname' => 'TechCorp',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '789 Business Park Drive, Suite 200, Metro City, ST 11111',
                'notes' => 'Bulk buyer, monthly orders, NET 30 payment terms',
                'type' => 'Business',
                'website' => 'https://www.techcorp.com',
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Lisa Chen',
                'contact' => 'Lisa Chen',
                'business_name' => null,
                'nickname' => 'LC',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '321 Pine Street, Apartment 4B, Downtown, ST 22222',
                'notes' => 'Student discount applied, prefers text messages',
                'type' => 'eBay Customer',
                'website' => null,
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Robert Wilson',
                'contact' => 'Robert Wilson',
                'business_name' => null,
                'nickname' => 'Bob',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '654 Elm Street, Suburbia, ST 33333',
                'notes' => 'Repeat customer, interested in vintage electronics',
                'type' => 'Facebook Customer',
                'website' => null,
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Global Electronics Inc',
                'contact' => 'Amanda Rodriguez',
                'business_name' => 'Global Electronics Inc',
                'nickname' => 'GEI',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '1000 Corporate Blvd, Industrial Park, ST 44444',
                'notes' => 'Large volume orders, requires certificates of destruction',
                'type' => 'Bulk Buyer',
                'website' => 'https://www.globalelectronics.com',
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'David Thompson',
                'contact' => 'David Thompson',
                'business_name' => null,
                'nickname' => 'Dave',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '987 Maple Drive, Riverside, ST 55555',
                'notes' => 'Cash only customer, pickup preferred',
                'type' => 'Residential Customer',
                'website' => null,
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Jennifer Martinez',
                'contact' => 'Jennifer Martinez',
                'business_name' => null,
                'nickname' => 'Jen',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '246 Cedar Lane, Hillside, ST 66666',
                'notes' => 'Small business owner, buys refurbished laptops',
                'type' => 'Website Customer',
                'website' => null,
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Metro Recycling Co',
                'contact' => 'James Park',
                'business_name' => 'Metro Recycling Company',
                'nickname' => 'Metro',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '500 Industrial Way, Warehouse District, ST 77777',
                'notes' => 'Bulk electronics recycling, weekly pickups',
                'type' => 'Business',
                'website' => 'https://www.metrorecycling.com',
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Karen White',
                'contact' => 'Karen White',
                'business_name' => null,
                'nickname' => null,
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '135 Birch Street, Lakewood, ST 88888',
                'notes' => 'Elderly customer, needs assistance with heavy items',
                'type' => 'eBay Customer',
                'website' => null,
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Tech Startup Hub',
                'contact' => 'Alex Kim',
                'business_name' => 'Tech Startup Hub LLC',
                'nickname' => 'TSH',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '200 Innovation Drive, Tech Valley, ST 99999',
                'notes' => 'Young company, fast-growing, needs scalable solutions',
                'type' => 'Facebook Customer',
                'website' => 'https://www.techstartuphub.com',
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Maria Gonzalez',
                'contact' => 'Maria Gonzalez',
                'business_name' => null,
                'nickname' => 'Mari',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '369 Sunset Boulevard, Coastal City, ST 10101',
                'notes' => 'Bilingual customer, prefers Spanish communication',
                'type' => 'Residential Customer',
                'website' => null,
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Educational Systems Corp',
                'contact' => 'Dr. Patricia Lee',
                'business_name' => 'Educational Systems Corporation',
                'nickname' => 'EduSys',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '1500 University Avenue, Campus Town, ST 20202',
                'notes' => 'Educational institution, requires special pricing and documentation',
                'type' => 'Bulk Buyer',
                'website' => 'https://www.edusystems.edu',
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Thomas Anderson',
                'contact' => 'Thomas Anderson',
                'business_name' => null,
                'nickname' => 'Tom',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '777 Matrix Street, Neo City, ST 30303',
                'notes' => 'Privacy-conscious customer, prefers secure communication',
                'type' => 'Website Customer',
                'website' => null,
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Green Earth Recycling',
                'contact' => 'Emma Thompson',
                'business_name' => 'Green Earth Recycling Solutions',
                'nickname' => 'GreenEarth',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '888 Eco Park Lane, Sustainable City, ST 40404',
                'notes' => 'Environmental focus, requires detailed recycling reports',
                'type' => 'Business',
                'website' => 'https://www.greenearthrecycling.org',
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Ryan O\'Connor',
                'contact' => 'Ryan O\'Connor',
                'business_name' => null,
                'nickname' => 'Ry',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '159 Irish Lane, Cloverfield, ST 50505',
                'notes' => 'Gaming enthusiast, interested in high-end graphics cards',
                'type' => 'eBay Customer',
                'website' => null,
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Healthcare Solutions Inc',
                'contact' => 'Dr. Susan Miller',
                'business_name' => 'Healthcare Solutions Incorporated',
                'nickname' => 'HealthSol',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '2000 Medical Center Drive, Health City, ST 60606',
                'notes' => 'HIPAA compliance required, medical-grade equipment only',
                'type' => 'Business',
                'website' => 'https://www.healthcaresolutions.med',
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Ashley Brown',
                'contact' => 'Ashley Brown',
                'business_name' => null,
                'nickname' => 'Ash',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '753 Autumn Street, Fall River, ST 70707',
                'notes' => 'Art student, needs equipment for digital media projects',
                'type' => 'Facebook Customer',
                'website' => null,
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'International Trade Co',
                'contact' => 'Mohammed Al-Hassan',
                'business_name' => 'International Trade Company Ltd',
                'nickname' => 'ITC',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '3000 Global Plaza, International District, ST 80808',
                'notes' => 'Export business, requires international shipping documentation',
                'type' => 'Bulk Buyer',
                'website' => 'https://www.internationaltrade.com',
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Betty Williams',
                'contact' => 'Betty Williams',
                'business_name' => null,
                'nickname' => 'Bets',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '951 Retirement Lane, Golden Years, ST 90909',
                'notes' => 'Senior citizen, prefers simple transactions and clear explanations',
                'type' => 'Residential Customer',
                'website' => null,
                'total_revenue' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        $this->command->info('All 20 test customers created successfully!');
        $this->command->info('You can now run: php artisan db:seed --class=TestCustomerSeeder');
    }
}
