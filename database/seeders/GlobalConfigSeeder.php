<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class GlobalConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('global_configs')->insert([
            'key' => 'app_name',
            'value' => 'Inventory Management System',
        ]);
        DB::table('global_configs')->insert([
            'key' => 'invoices_popular_inventory_items',
            'value' => '',
        ]);
    }
}
