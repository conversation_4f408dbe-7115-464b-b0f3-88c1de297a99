<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ChecklistFieldsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('checklist_fields')->delete();
        
        \DB::table('checklist_fields')->insert(array (
            0 => 
            array (
                'id' => 12,
                'name' => 'RAM',
                'description' => 'Total amount of system RAM in GB',
                'type' => 'numeric',
                'status' => '1',
                'order' => 4,
                'category_id' => 3,
                'options' => '"{\\"unit\\":\\"Gigabytes\\"}"',
                'created_at' => '2024-12-08 14:40:35',
                'updated_at' => '2024-12-08 16:04:07',
            ),
            1 => 
            array (
                'id' => 13,
                'name' => 'Operating System',
                'description' => NULL,
                'type' => 'select',
                'status' => '1',
                'order' => 6,
                'category_id' => 3,
                'options' => '"{\\"select_list\\":[\\"Windows 11 Pro\\",\\" Windows 11 Home\\",\\" Windows 10 Pro\\",\\" Windows 10 Home\\",\\" MacOS\\",\\" Linux\\",\\" Other\\"]}"',
                'created_at' => '2024-12-08 14:45:08',
                'updated_at' => '2024-12-09 23:07:37',
            ),
            2 => 
            array (
                'id' => 14,
                'name' => 'Screen Res',
                'description' => 'Whats the pixel resolution of this screen?',
                'type' => 'select',
                'status' => '1',
                'order' => 8,
                'category_id' => 3,
                'options' => '"{\\"select_list\\":[\\"1366x768 HD\\",\\"1080x1920 FHD\\",\\"1200x1920 WUXGA\\",\\"4K\\",\\"Other\\"]}"',
                'created_at' => '2024-12-08 14:49:59',
                'updated_at' => '2024-12-09 23:07:37',
            ),
            3 => 
            array (
                'id' => 15,
                'name' => 'Touchscreen',
                'description' => NULL,
                'type' => 'yes/no',
                'status' => '1',
                'order' => 9,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 14:51:20',
                'updated_at' => '2024-12-09 23:07:37',
            ),
            4 => 
            array (
                'id' => 16,
                'name' => 'Screen Size',
                'description' => 'Enter the total screen size in inches',
                'type' => 'text',
                'status' => '1',
                'order' => 7,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:00:37',
                'updated_at' => '2024-12-09 23:07:37',
            ),
            5 => 
            array (
                'id' => 17,
                'name' => 'Serial Number',
                'description' => 'Enter the serial number or service tag shown in BIOS.',
                'type' => 'text',
                'status' => '1',
                'order' => 11,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:01:09',
                'updated_at' => '2024-12-09 23:07:55',
            ),
            6 => 
            array (
                'id' => 18,
                'name' => 'Wi-Fi Working?',
                'description' => NULL,
                'type' => 'yes/no',
                'status' => '1',
                'order' => 20,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:01:39',
                'updated_at' => '2024-12-09 23:07:55',
            ),
            7 => 
            array (
                'id' => 19,
                'name' => 'CPU',
                'description' => 'Enter make and model, eg. Intel i7-12700k',
                'type' => 'text',
                'status' => '1',
                'order' => 2,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:03:21',
                'updated_at' => '2024-12-14 20:08:38',
            ),
            8 => 
            array (
                'id' => 20,
                'name' => 'GPU',
                'description' => NULL,
                'type' => 'text',
                'status' => '1',
                'order' => 3,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:03:40',
                'updated_at' => '2024-12-08 16:03:54',
            ),
            9 => 
            array (
                'id' => 21,
                'name' => 'Primary Storage Type',
                'description' => 'What storage device type was the OS installed on',
                'type' => 'select',
                'status' => '1',
                'order' => 12,
                'category_id' => 3,
                'options' => '"{\\"select_list\\":[\\"NVMe M2\\",\\"SATA M2\\",\\"2.5in SSD\\",\\"eMCC\\",\\"HDD\\",\\"Soldered\\",\\"Other\\"]}"',
                'created_at' => '2024-12-08 16:06:46',
                'updated_at' => '2024-12-09 23:07:55',
            ),
            10 => 
            array (
                'id' => 22,
                'name' => 'Primary Storage Amount',
                'description' => 'Note 1024GB = 1TB',
                'type' => 'numeric',
                'status' => '1',
                'order' => 13,
                'category_id' => 3,
                'options' => '"{\\"unit\\":\\"GB\\"}"',
                'created_at' => '2024-12-08 16:07:51',
                'updated_at' => '2024-12-09 23:07:55',
            ),
            11 => 
            array (
                'id' => 23,
                'name' => 'Secondary Storage',
                'description' => 'Enter secondary storage device type and capacity if any',
                'type' => 'text',
                'status' => '1',
                'order' => 14,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:08:07',
                'updated_at' => '2024-12-09 23:07:55',
            ),
            12 => 
            array (
                'id' => 24,
                'name' => 'Did you check BIOS?',
                'description' => 'Have you reviewed UEFI/BIOS setup? Is the device SATA operation set to AHCI? Is the boot order correct? Is TPM 2.0 enabled?',
                'type' => 'yes/no',
                'status' => '1',
                'order' => 18,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:09:54',
                'updated_at' => '2024-12-09 23:07:55',
            ),
            13 => 
            array (
                'id' => 25,
                'name' => 'Is the new OS installed?',
                'description' => 'Did you install a fresh copy of Windows onto the primary storage device?',
                'type' => 'yes/no',
                'status' => '1',
                'order' => 19,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:10:31',
                'updated_at' => '2024-12-09 23:07:55',
            ),
            14 => 
            array (
                'id' => 26,
                'name' => 'Device Physically Opened',
            'description' => 'Have you physically removed the back cover of the laptop, verified that it has primary storage, and at least 16GB of memory installed (if possible)?',
                'type' => 'yes/no',
                'status' => '1',
                'order' => 17,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:11:13',
                'updated_at' => '2024-12-09 23:07:55',
            ),
            15 => 
            array (
                'id' => 27,
                'name' => 'Screen Condition',
                'description' => 'Clean and then review the screen. What shape is it in?',
                'type' => 'select',
                'status' => '1',
                'order' => 10,
                'category_id' => 3,
                'options' => '"{\\"select_list\\":[\\"Unreviewed\\",\\"Excellent - Almost perfect\\",\\"Good - Difficult to notice scratches or scuffs\\",\\"Fair - Noticeable scratches or scuffs\\",\\"Poor - Significant issues clearly visible\\"]}"',
                'created_at' => '2024-12-08 16:14:36',
                'updated_at' => '2024-12-09 23:07:37',
            ),
            16 => 
            array (
                'id' => 28,
                'name' => 'Physical and Sellable Condition Review',
                'description' => 'Check for major cracks, hinge issues, or other problems.',
                'type' => 'select',
                'status' => '1',
                'order' => 16,
                'category_id' => 3,
                'options' => '"{\\"select_list\\":[\\"Unreviewed\\",\\"Likely Sellable\\",\\"Sellable with slight damage\\",\\"Sellable with major damage\\",\\"Scrap\\"]}"',
                'created_at' => '2024-12-08 16:16:39',
                'updated_at' => '2024-12-09 23:07:55',
            ),
            17 => 
            array (
                'id' => 29,
                'name' => 'Is Windows Activated',
                'description' => 'Connect to internet and verify Windows is activated in system settings. Double check version of Windows installed.',
                'type' => 'yes/no',
                'status' => '1',
                'order' => 22,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:18:29',
                'updated_at' => '2024-12-11 03:18:19',
            ),
            18 => 
            array (
                'id' => 30,
                'name' => 'Update Process',
                'description' => 'Check for system updates. Install all updates. Install all optional updates. Do not check "yes" until 0 more updates are available.',
                'type' => 'yes/no',
                'status' => '1',
                'order' => 23,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:19:03',
                'updated_at' => '2024-12-11 03:18:19',
            ),
            19 => 
            array (
                'id' => 31,
                'name' => 'Device Manager, HWiNFO',
                'description' => 'Have you checked drivers, installed HWiNFO, and uploaded a copy of the screenshot to this page?',
                'type' => 'yes/no',
                'status' => '1',
                'order' => 24,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:20:19',
                'updated_at' => '2024-12-11 03:18:19',
            ),
            20 => 
            array (
                'id' => 32,
                'name' => 'PassMark Score',
                'description' => 'Install and run PassMark. Then post the score here.',
                'type' => 'numeric',
                'status' => '1',
                'order' => 25,
                'category_id' => 3,
                'options' => '"{\\"unit\\":\\"Score\\"}"',
                'created_at' => '2024-12-08 16:20:56',
                'updated_at' => '2024-12-11 03:18:19',
            ),
            21 => 
            array (
                'id' => 33,
                'name' => 'Device Reset to OOBE',
                'description' => 'Has the device been reset to the out-of-box experience?',
                'type' => 'yes/no',
                'status' => '1',
                'order' => 26,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:21:13',
                'updated_at' => '2024-12-11 03:18:19',
            ),
            22 => 
            array (
                'id' => 34,
            'name' => 'Device Ready To Sell (INITIAL)',
                'description' => 'Have you thoroughly cleaned the device according to the process guidelines? Are all accessories and chargers present? Is this device ready to be handed to a customer?',
                'type' => 'text',
                'status' => '1',
                'order' => 27,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:22:31',
                'updated_at' => '2024-12-11 03:18:19',
            ),
            23 => 
            array (
                'id' => 35,
                'name' => 'Battery Health',
                'description' => 'What is the reported battery health in BIOS or the remaining/total capacity under powercfg /batteryreport',
                'type' => 'text',
                'status' => '1',
                'order' => 15,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-08 16:30:59',
                'updated_at' => '2024-12-09 23:07:55',
            ),
            24 => 
            array (
                'id' => 36,
                'name' => 'Ports',
                'description' => 'List all the ports on this device',
                'type' => 'textarea',
                'status' => '1',
                'order' => 5,
                'category_id' => 3,
                'options' => '"[]"',
                'created_at' => '2024-12-09 23:07:08',
                'updated_at' => '2024-12-10 04:00:51',
            ),
            25 => 
            array (
                'id' => 37,
                'name' => 'Describing Features',
                'description' => 'Click all that apply',
                'type' => 'checkboxes',
                'status' => '1',
                'order' => 28,
                'category_id' => 3,
                'options' => '"{\\"checkboxes\\":[\\"Slim Profile\\",\\" Workstation\\",\\" For School\\",\\" For Work\\",\\" For Casual\\",\\" For Gaming\\",\\" Powerful\\",\\" Lightweight\\",\\" Extra Durable\\",\\" Premium\\",\\" Convertible\\",\\" Energy Efficient\\",\\" For Video Editing\\",\\" For Programming\\",\\" Home Office Ready\\",\\" RGB Lighting\\",\\" AMOLED Display\\",\\" Fingerprint Reader\\",\\" Expandable Storage\\"]}"',
                'created_at' => '2024-12-09 23:35:24',
                'updated_at' => '2024-12-11 03:18:19',
            ),
            26 => 
            array (
                'id' => 38,
                'name' => 'Hardware Check',
                'description' => 'Verify the functionality of all the following components. Note any discrepancies. Use keyboard checker tool.',
                'type' => 'checkboxes',
                'status' => '1',
                'order' => 21,
                'category_id' => 3,
                'options' => '"{\\"checkboxes\\":[\\"Fan Noise\\",\\" Ethernet\\",\\" All USB Ports\\",\\" Keyboard\\",\\" Trackpad\\",\\" Speakers\\",\\" Webcam\\"]}"',
                'created_at' => '2024-12-11 03:16:26',
                'updated_at' => '2024-12-11 03:18:19',
            ),
            27 => 
            array (
                'id' => 39,
                'name' => 'Form Factor',
                'description' => NULL,
                'type' => 'select',
                'status' => '1',
                'order' => 1,
                'category_id' => 5,
                'options' => '"{\\"select_list\\":[\\"Full Size Tower\\",\\" Mid Tower\\",\\" Small Form Factor\\",\\" Micro Form Factor\\",\\" All-in-one\\",\\" Other\\"]}"',
                'created_at' => '2024-12-18 00:29:00',
                'updated_at' => '2024-12-18 00:29:31',
            ),
            28 => 
            array (
                'id' => 40,
                'name' => 'CPU',
                'description' => 'Enter make and model, eg. Intel i7-12700k',
                'type' => 'text',
                'status' => '1',
                'order' => 3,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:30:08',
                'updated_at' => '2024-12-18 00:32:24',
            ),
            29 => 
            array (
                'id' => 41,
                'name' => 'GPU',
                'description' => NULL,
                'type' => 'text',
                'status' => '1',
                'order' => 4,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:30:14',
                'updated_at' => '2024-12-18 00:32:24',
            ),
            30 => 
            array (
                'id' => 42,
                'name' => 'RAM',
                'description' => 'Total amount of system RAM in GB',
                'type' => 'numeric',
                'status' => '1',
                'order' => 5,
                'category_id' => 5,
                'options' => '"{\\"unit\\":\\"Gigabytes\\"}"',
                'created_at' => '2024-12-18 00:30:24',
                'updated_at' => '2024-12-18 00:32:24',
            ),
            31 => 
            array (
                'id' => 43,
                'name' => 'Ports',
                'description' => 'List all the ports on this device',
                'type' => 'textarea',
                'status' => '1',
                'order' => 6,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:30:44',
                'updated_at' => '2024-12-18 00:32:24',
            ),
            32 => 
            array (
                'id' => 44,
                'name' => 'Operating System',
                'description' => NULL,
                'type' => 'select',
                'status' => '1',
                'order' => 7,
                'category_id' => 5,
                'options' => '"{\\"select_list\\":[\\"Windows 11 Pro\\",\\" Windows 11 Home\\",\\" Windows 10 Pro\\",\\" Windows 10 Home\\",\\" MacOS\\",\\" Linux\\",\\" Other\\"]}"',
                'created_at' => '2024-12-18 00:30:54',
                'updated_at' => '2024-12-18 00:32:24',
            ),
            33 => 
            array (
                'id' => 45,
                'name' => 'Screen Details',
                'description' => 'If this is an AIO please list screen size, resolution, and condition.',
                'type' => 'textarea',
                'status' => '1',
                'order' => 8,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:31:50',
                'updated_at' => '2024-12-18 00:32:24',
            ),
            34 => 
            array (
                'id' => 46,
                'name' => 'Serial Number',
                'description' => 'Enter the serial number or service tag shown in BIOS / UEFI.',
                'type' => 'text',
                'status' => '1',
                'order' => 2,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:32:12',
                'updated_at' => '2024-12-18 00:32:24',
            ),
            35 => 
            array (
                'id' => 47,
                'name' => 'Primary Storage Type',
                'description' => 'What storage medium does the OS live on?',
                'type' => 'select',
                'status' => '1',
                'order' => 9,
                'category_id' => 5,
                'options' => '"{\\"select_list\\":[\\"NVMe M2\\",\\" SATA M2\\",\\" 2.5in SSD\\",\\" eMCC\\",\\" HDD\\",\\" Soldered\\",\\" Other\\"]}"',
                'created_at' => '2024-12-18 00:32:57',
                'updated_at' => '2024-12-18 00:33:34',
            ),
            36 => 
            array (
                'id' => 48,
                'name' => 'Primary Storage Amount',
                'description' => 'Enter value in gigabytes. Note 1024GB = 1TB.',
                'type' => 'numeric',
                'status' => '1',
                'order' => 10,
                'category_id' => 5,
                'options' => '"{\\"unit\\":\\"GB\\"}"',
                'created_at' => '2024-12-18 00:34:11',
                'updated_at' => '2024-12-18 00:34:11',
            ),
            37 => 
            array (
                'id' => 49,
                'name' => 'Secondary Storage',
                'description' => 'If any, specify capacity and type. Eg, 1TB HDD.',
                'type' => 'text',
                'status' => '1',
                'order' => 11,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:34:38',
                'updated_at' => '2024-12-18 00:34:38',
            ),
            38 => 
            array (
                'id' => 50,
                'name' => 'Physical and Sellable Condition Review',
                'description' => 'How does this device look overall, check for cracks, dents, etc.',
                'type' => 'select',
                'status' => '1',
                'order' => 12,
                'category_id' => 5,
                'options' => '"{\\"select_list\\":[\\"Likely Sellable\\",\\" Sellable with slight damage\\",\\" Sellable with major damage\\",\\" scrap\\"]}"',
                'created_at' => '2024-12-18 00:35:05',
                'updated_at' => '2024-12-18 00:35:28',
            ),
            39 => 
            array (
                'id' => 51,
                'name' => 'Device Physically Opened',
                'description' => 'Have you physically removed the back cover of the desktop, verified that it has primary storage, and at least 16GB of memory installed?',
                'type' => 'yes/no',
                'status' => '1',
                'order' => 13,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:35:50',
                'updated_at' => '2024-12-18 00:35:50',
            ),
            40 => 
            array (
                'id' => 52,
                'name' => 'Did you check BIOS / UEFI?',
                'description' => 'Have you reviewed UEFI/BIOS setup? Is the device SATA operation set to AHCI? Is the boot order correct? Is TPM 2.0 enabled?',
                'type' => 'yes/no',
                'status' => '1',
                'order' => 14,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:36:05',
                'updated_at' => '2024-12-18 00:36:05',
            ),
            41 => 
            array (
                'id' => 53,
                'name' => 'Is the new OS installed?',
                'description' => 'Did you install a fresh copy of Windows onto the primary storage device?',
                'type' => 'yes/no',
                'status' => '1',
                'order' => 15,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:36:16',
                'updated_at' => '2024-12-18 00:36:16',
            ),
            42 => 
            array (
                'id' => 54,
                'name' => 'Wi-Fi Working?',
                'description' => NULL,
                'type' => 'select',
                'status' => '1',
                'order' => 16,
                'category_id' => 5,
                'options' => '"{\\"select_list\\":[\\"No WiFi Card\\",\\" Broken WiFi\\",\\" WiFi Verified Working\\"]}"',
                'created_at' => '2024-12-18 00:36:33',
                'updated_at' => '2024-12-18 00:36:48',
            ),
            43 => 
            array (
                'id' => 55,
                'name' => 'Hardware Check',
                'description' => 'Verify the functionality of all the following components. Note any discrepancies.',
                'type' => 'checkboxes',
                'status' => '1',
                'order' => 17,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:38:17',
                'updated_at' => '2024-12-18 00:38:17',
            ),
            44 => 
            array (
                'id' => 56,
                'name' => 'Is Windows Activated?',
                'description' => 'Connect to internet and verify Windows is activated in system settings. Double check version of Windows installed.',
                'type' => 'yes/no',
                'status' => '1',
                'order' => 18,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:38:31',
                'updated_at' => '2024-12-18 00:38:31',
            ),
            45 => 
            array (
                'id' => 57,
                'name' => 'Update Process',
                'description' => 'Check for system updates. Install all updates. Install all optional updates. Do not check "yes" until 0 more updates are available.',
                'type' => 'yes/no',
                'status' => '1',
                'order' => 19,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:38:46',
                'updated_at' => '2024-12-18 00:38:46',
            ),
            46 => 
            array (
                'id' => 58,
                'name' => 'Device Manager, HWiNFO',
                'description' => 'Have you checked drivers, installed HWiNFO, and uploaded a copy of the screenshot to this page? Ensure no red marks in HWiNFO other than secure boot.',
                'type' => 'yes/no',
                'status' => '1',
                'order' => 20,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:39:26',
                'updated_at' => '2024-12-18 00:39:26',
            ),
            47 => 
            array (
                'id' => 59,
                'name' => 'PassMark Score',
                'description' => 'Install and run PassMark. Then post the score here.',
                'type' => 'numeric',
                'status' => '1',
                'order' => 21,
                'category_id' => 5,
                'options' => '"{\\"unit\\":\\"Score\\"}"',
                'created_at' => '2024-12-18 00:39:46',
                'updated_at' => '2024-12-18 00:39:46',
            ),
            48 => 
            array (
                'id' => 60,
                'name' => 'Device Ready To Sell?',
                'description' => 'Have you thoroughly cleaned the device according to the process guidelines? Are all accessories and chargers present? Is this device ready to be handed to a customer?',
                'type' => 'text',
                'status' => '1',
                'order' => 22,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:40:01',
                'updated_at' => '2024-12-18 00:40:01',
            ),
            49 => 
            array (
                'id' => 61,
                'name' => 'Describing Features',
                'description' => 'Select at least 2 features that describe this device for the description.',
                'type' => 'checkboxes',
                'status' => '1',
                'order' => 23,
                'category_id' => 5,
                'options' => '"[]"',
                'created_at' => '2024-12-18 00:41:32',
                'updated_at' => '2024-12-18 00:41:32',
            ),
        ));
        
        
    }
}