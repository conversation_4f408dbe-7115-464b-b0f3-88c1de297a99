<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\TimeCard;
use App\Models\TimePunch;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class MobileTimeClockTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'enabled' => true,
        ]);
        
        // Give user basic permissions
        $this->user->permissions()->create(['name' => 'view_timecards']);
    }

    /** @test */
    public function mobile_user_can_access_time_clock()
    {
        $response = $this->actingAs($this->user)
            ->withHeaders([
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
            ])
            ->get('/time-clock');

        $response->assertStatus(200);
        $response->assertSee('Time Clock');
    }

    /** @test */
    public function mobile_user_can_clock_in()
    {
        $response = $this->actingAs($this->user)
            ->withHeaders([
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
            ])
            ->post('/time-clock/action', [
                'action' => 'clock_in',
                'notes' => 'Test clock in from mobile'
            ]);

        $response->assertRedirect('/time-clock');
        $response->assertSessionHas('success');

        // Verify punch was created
        $this->assertDatabaseHas('time_punches', [
            'user_id' => $this->user->id,
            'type' => 'clock_in'
        ]);
    }

    /** @test */
    public function mobile_user_can_clock_out()
    {
        // First clock in
        $timeCard = $this->user->getTodayTimeCard();
        TimePunch::createPunch($timeCard->id, 'clock_in', 'Test clock in');

        $response = $this->actingAs($this->user)
            ->withHeaders([
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
            ])
            ->post('/time-clock/action', [
                'action' => 'clock_out',
                'notes' => 'Test clock out from mobile'
            ]);

        $response->assertRedirect('/time-clock');
        $response->assertSessionHas('success');

        // Verify punch was created
        $this->assertDatabaseHas('time_punches', [
            'user_id' => $this->user->id,
            'type' => 'clock_out'
        ]);
    }

    /** @test */
    public function mobile_middleware_logs_requests()
    {
        $this->actingAs($this->user)
            ->withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Android; Mobile) AppleWebKit/537.36'
            ])
            ->get('/time-clock');

        // Check that logs were created (this would need log testing setup)
        $this->assertTrue(true); // Placeholder for log verification
    }

    /** @test */
    public function duplicate_submissions_are_prevented()
    {
        $timeCard = $this->user->getTodayTimeCard();
        
        // Make two rapid requests
        $response1 = $this->actingAs($this->user)
            ->post('/time-clock/action', [
                'action' => 'clock_in',
                'notes' => 'First request'
            ]);

        $response2 = $this->actingAs($this->user)
            ->post('/time-clock/action', [
                'action' => 'clock_in',
                'notes' => 'Second request'
            ]);

        // Both should succeed at the controller level
        // JavaScript prevents duplicates on the frontend
        $response1->assertRedirect('/time-clock');
        $response2->assertRedirect('/time-clock');
    }

    /** @test */
    public function api_endpoint_returns_current_data()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/time-clock/current-data');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'current_status',
            'total_hours',
            'total_break_hours',
            'formatted_total_hours',
            'formatted_total_break_hours',
            'timestamp'
        ]);
    }

    /** @test */
    public function api_endpoint_handles_unauthenticated_requests()
    {
        $response = $this->getJson('/api/time-clock/current-data');

        $response->assertStatus(401);
        $response->assertJson([
            'success' => false,
            'error_code' => 'UNAUTHENTICATED'
        ]);
    }

    /** @test */
    public function csrf_token_mismatch_is_handled_for_mobile()
    {
        // This test would need to simulate CSRF token mismatch
        // which is difficult in testing environment
        $this->assertTrue(true); // Placeholder
    }

    /** @test */
    public function service_worker_is_accessible()
    {
        $response = $this->get('/sw.js');
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/javascript');
    }

    /** @test */
    public function manifest_is_accessible()
    {
        $response = $this->get('/manifest.json');
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/json');
    }

    /** @test */
    public function time_clock_handles_errors_gracefully()
    {
        // Test with invalid action
        $response = $this->actingAs($this->user)
            ->post('/time-clock/action', [
                'action' => 'invalid_action',
                'notes' => 'Test invalid action'
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('error');
    }

    /** @test */
    public function mobile_user_can_start_and_end_break()
    {
        // First clock in
        $timeCard = $this->user->getTodayTimeCard();
        TimePunch::createPunch($timeCard->id, 'clock_in', 'Test clock in');

        // Start break
        $response = $this->actingAs($this->user)
            ->withHeaders([
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
            ])
            ->post('/time-clock/action', [
                'action' => 'break_out',
                'notes' => 'Starting break'
            ]);

        $response->assertRedirect('/time-clock');
        $response->assertSessionHas('success');

        // Verify break punch was created
        $this->assertDatabaseHas('time_punches', [
            'user_id' => $this->user->id,
            'type' => 'break_out'
        ]);

        // End break
        $response = $this->actingAs($this->user)
            ->withHeaders([
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
            ])
            ->post('/time-clock/action', [
                'action' => 'clock_in', // This ends the break
                'notes' => 'Ending break'
            ]);

        $response->assertRedirect('/time-clock');
        $response->assertSessionHas('success');

        // Verify break end punch was created
        $this->assertDatabaseHas('time_punches', [
            'user_id' => $this->user->id,
            'type' => 'break_in'
        ]);
    }

    /** @test */
    public function ajax_requests_return_json_with_status_and_punches()
    {
        // Test clock in via AJAX
        $response = $this->actingAs($this->user)
            ->withHeaders([
                'X-Requested-With' => 'XMLHttpRequest',
                'Accept' => 'application/json'
            ])
            ->postJson('/time-clock/action', [
                'action' => 'clock_in',
                'notes' => 'AJAX clock in test'
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'current_status',
            'punch_id',
            'punches_html'
        ]);

        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals('clock_in', $responseData['current_status']);
        $this->assertNotEmpty($responseData['punches_html']);

        // Test clock out via AJAX
        $response = $this->actingAs($this->user)
            ->withHeaders([
                'X-Requested-With' => 'XMLHttpRequest',
                'Accept' => 'application/json'
            ])
            ->postJson('/time-clock/action', [
                'action' => 'clock_out',
                'notes' => 'AJAX clock out test'
            ]);

        $response->assertStatus(200);
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals('clock_out', $responseData['current_status']);
        $this->assertNotEmpty($responseData['punches_html']);
    }
}
