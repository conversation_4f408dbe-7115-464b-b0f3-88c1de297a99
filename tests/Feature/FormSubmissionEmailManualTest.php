<?php

namespace Tests\Feature;

use App\Models\Form;
use Tests\TestCase;

class FormSubmissionEmailManualTest extends TestCase
{
    /**
     * Test that form email notification methods work correctly
     * This test doesn't use RefreshDatabase so it won't destroy your data
     */
    public function test_form_email_notification_methods(): void
    {
        // Test with a form that has notifications disabled
        $form = new Form([
            'enable_email_notifications' => false,
            'notification_emails' => ['<EMAIL>'],
        ]);

        $this->assertFalse($form->hasEmailNotificationsEnabled());
        $this->assertEmpty($form->getNotificationEmails());

        // Test with a form that has notifications enabled
        $form = new Form([
            'enable_email_notifications' => true,
            'notification_emails' => ['<EMAIL>', 'invalid-email', '', '<EMAIL>'],
        ]);

        $this->assertTrue($form->hasEmailNotificationsEnabled());
        
        $emails = $form->getNotificationEmails();
        $this->assertCount(2, $emails);
        $this->assertContains('<EMAIL>', $emails);
        $this->assertContains('<EMAIL>', $emails);
        $this->assertNotContains('invalid-email', $emails);
        $this->assertNotContains('', $emails);
    }

    /**
     * Test that email template service processes form data correctly
     */
    public function test_email_template_service_form_data_processing(): void
    {
        $emailTemplateService = app(\App\Services\EmailTemplateService::class);
        
        // Test that the service exists and has the method
        $this->assertTrue(method_exists($emailTemplateService, 'templateExists'));
        
        // Test that form submission templates exist
        $this->assertTrue($emailTemplateService->templateExists('form_submission_notification'));
        $this->assertTrue($emailTemplateService->templateExists('form_submission_notification_full'));
    }
}
