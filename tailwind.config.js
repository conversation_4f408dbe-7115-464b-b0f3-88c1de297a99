import defaultTheme from "tailwindcss/defaultTheme";
import forms from "@tailwindcss/forms";
import typography from "@tailwindcss/typography";

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        "./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php",
        "./vendor/laravel/jetstream/**/*.blade.php",
        "./storage/framework/views/*.php",
        "./resources/views/**/*.blade.php",
        "./resources/**/*.js",
        "./resources/**/*.vue",
    ],
    theme: {
        extend: {
            padding: {
                '14': '3.5rem', // Add custom padding value
            },
        },
    },
    daisyui: {
        themes: ["light", "dark", "cupcake", "forest", "synthwave", "retro", "cyberpunk", "valentine", "halloween", "garden", "lofi", "luxury"],
    },
    plugins: [
        forms,
        typography,
        require("daisyui"),
    ],
};
