# ETRFlow Time Tracking System Documentation

## Overview
ETRFlow includes a comprehensive time tracking system for employee time management, including clock in/out, break tracking, PTO (sick/vacation time), overtime calculations, and timesheet generation.

## Core Components

### Models

#### TimeCard (`app/Models/TimeCard.php`)
- **Purpose**: Represents a daily time record for a user
- **Key Fields**:
  - `user_id`: Foreign key to users table
  - `date`: Date of the time card (unique per user per day)
  - `notes`: Optional daily notes
  - `total_hours`: Work hours in HH:MM:SS format (excludes breaks)
  - `total_break_hours`: Break time in HH:MM:SS format
  - `total_sick_time`: Sick time in HH:MM:SS format
  - `total_vacation_time`: Vacation time in HH:MM:SS format
- **Key Methods**:
  - `getCurrentStatus()`: Returns current status (not_started, clock_in, break, clock_out)
  - `getCurrentHours()`: Real-time calculation of hours for today's card
  - `calculateHours()`: Recalculates all time totals from punches
  - `isClockedIn()`, `isOnBreak()`: Status checking methods

#### TimePunch (`app/Models/TimePunch.php`)
- **Purpose**: Individual time punch records (clock in/out, breaks, PTO)
- **Key Fields**:
  - `time_card_id`: Foreign key to time_cards table
  - `user_id`: Foreign key to users table
  - `type`: Enum - 'clock_in', 'clock_out', 'break_in', 'break_out', 'sick_time', 'vacation_time'
  - `punch_time`: Timestamp of the punch
  - `notes`: Optional notes for the punch
  - `original_punch_id`: For edited punches, references original
  - `replaced_by`: ID of punch that replaced this one
  - `edited_by`: User who edited the punch
  - `ip_address`: IP address of punch creation
- **Key Methods**:
  - `createPunch()`: Static method to create punches with validation
  - `getTypeNameAttribute()`, `getTypeIconAttribute()`, `getTypeColorAttribute()`: UI helpers

### Controllers

#### TimeClockController (`app/Http/Controllers/TimeClockController.php`)
- **Purpose**: Main time clock interface
- **Key Methods**:
  - `index()`: Display time clock page with current status
  - `action()`: Handle clock in/out/break actions via POST

#### TimeCardController (`app/Http/Controllers/TimeCardController.php`)
- **Purpose**: Time card management and viewing
- **Key Methods**:
  - `index()`: List time cards with filtering
  - `show()`: View individual time card details
  - `edit()`: Edit time card and punches
  - `createForDate()`: Admin function to create historical time cards

#### TimePunchController (`app/Http/Controllers/TimePunchController.php`)
- **Purpose**: Individual punch management
- **Key Methods**:
  - `addManualPunch()`: Add manual punches
  - `edit()`, `update()`: Edit existing punches
  - `destroy()`: Delete punches
  - `ajaxUpdate()`: AJAX punch editing

#### TimesheetController (`app/Http/Controllers/TimesheetController.php`)
- **Purpose**: Generate and export timesheets
- **Key Methods**:
  - `index()`: Timesheet generation form
  - `generate()`: Generate timesheet reports
  - `print()`: Print individual timesheets
  - `export()`: Export timesheets to Excel

### Services

#### TimeCalculationService (`app/Services/TimeCalculationService.php`)
- **Purpose**: Centralized time calculation logic
- **Key Methods**:
  - `calculateUserTimeSummary()`: Calculate time summary for date range
  - `calculateWeeklyTimeSummary()`: Calculate current week summary
  - `ensureTimeCardsExist()`: Create missing time cards for date range
  - `formatSecondsToTime()`: Convert seconds to HH:MM:SS format
- **Important Logic**:
  - Overtime calculated as hours > 40 per week
  - Billable hours = work hours + PTO
  - Break time excluded from work hours
  - PTO includes sick time + vacation time

## Database Schema

### time_cards Table
```sql
- id (primary key)
- user_id (foreign key to users)
- date (unique with user_id)
- notes (text, nullable)
- total_hours (string, HH:MM:SS format)
- total_break_hours (string, HH:MM:SS format)
- total_sick_time (string, HH:MM:SS format)
- total_vacation_time (string, HH:MM:SS format)
- created_at, updated_at, deleted_at
```

### time_punches Table
```sql
- id (primary key)
- time_card_id (foreign key to time_cards)
- user_id (foreign key to users)
- type (enum: clock_in, clock_out, break_in, break_out, sick_time, vacation_time)
- punch_time (datetime)
- notes (text, nullable)
- original_punch_id (for edited punches)
- replaced_by (punch that replaced this one)
- edited_by (user who edited)
- ip_address (string)
- created_at, updated_at, deleted_at
```

## Key Features

### Time Clock Interface
- **Location**: `/time-clock` route
- **Features**:
  - Real-time clock display with timezone support
  - Current status display (clocked in, on break, clocked out)
  - Action buttons for clock in/out and break start/end
  - Live updating of hours while clocked in
  - Recent punches list with edit capabilities
  - Weekly summary with overtime calculation

### Break Management
- **Minimum Break Duration**: Configurable via global config (`min_break_duration`)
- **Break Types**: break_out (start break), break_in (end break)
- **Validation**: Prevents multiple breaks without ending previous one

### PTO Tracking
- **Sick Time**: Tracked as `sick_time` punch type
- **Vacation Time**: Tracked as `vacation_time` punch type
- **Calculation**: Both count toward billable hours but not overtime
- **Display**: Combined as "PTO Hours" in weekly summary

### Overtime Calculation
- **Rule**: Hours > 40 per work week
- **Exclusions**: Break time and PTO excluded from overtime calculation
- **Weekly Basis**: Calculated per calendar week (Monday-Sunday)

## Routes

### Web Routes
```php
// Main time clock
GET /time-clock - TimeClockController@index
POST /time-clock/action - TimeClockController@action

// Time cards (with permissions)
GET /timecards - TimeCardController@index
GET /timecards/{timecard} - TimeCardController@show
GET /timecards/{timecard}/edit - TimeCardController@edit
PUT /timecards/{timecard} - TimeCardController@update

// Admin functions
GET /timecards/create-for-date - TimeCardController@createForDate
POST /timecards/store-for-date - TimeCardController@storeForDate

// Timesheets (admin only)
GET /timesheets - TimesheetController@index
POST /timesheets/generate - TimesheetController@generate
GET /timesheets/print - TimesheetController@print
POST /timesheets/export - TimesheetController@export

// Punch management
POST /time-cards/{timecard}/add-punch - TimePunchController@addManualPunch
GET /time-punches/{punch}/edit - TimePunchController@edit
PUT /time-punches/{punch} - TimePunchController@update
DELETE /time-punches/{punch}/delete - TimePunchController@destroy
PUT /api/time-punches/{punch}/ajax-update - TimePunchController@ajaxUpdate
```

### API Routes
```php
GET /api/time-clock/current-data - Api\TimeClockController@getCurrentData
```

## Permissions

### Required Permissions
- `view_timecards` or `view_own_timecards`: View time cards
- `edit_timecards` or `edit_own_timecards`: Edit time cards/punches
- `view_timesheets`: View timesheet reports
- `generate_timesheets`: Generate timesheet reports

### Permission Logic
- Users with `view_own_timecards` can only see their own time cards
- Users with `edit_own_timecards` can only edit their own time cards
- Admin permissions allow viewing/editing all users' time data

## Configuration

### Global Config Settings
- `time_zone`: Application timezone for time display
- `min_break_duration`: Minimum break duration in minutes (default: 30)
- `timeclock_daily_report_enabled`: Enable daily time reports (0/1)
- `timeclock_report_email`: Email for daily reports
- `timeclock_report_time`: Time to send daily reports (HH:MM format)

## Views and Templates

### Main Views
- `resources/views/timecards/time-clock.blade.php`: Main time clock interface
- `resources/views/timecards/index.blade.php`: Time cards listing
- `resources/views/timecards/show.blade.php`: Individual time card view
- `resources/views/timecards/edit.blade.php`: Time card editing
- `resources/views/timesheets/index.blade.php`: Timesheet generation
- `resources/views/timesheets/show.blade.php`: Timesheet display
- `resources/views/timesheets/print.blade.php`: Printable timesheet

### Partial Views
- `resources/views/timecards/partials/punches-list.blade.php`: Punch list component

### JavaScript Assets
- `resources/js/timecard-edit.js`: Time card editing functionality with Flatpickr

## Important Implementation Notes

### Time Storage Format
- **Database**: All times stored as strings in HH:MM:SS format
- **Calculation**: Converted to seconds for calculations, back to HH:MM:SS for storage
- **Display**: Formatted for user timezone display

### Time Card Creation
- **Auto-creation**: Time cards automatically created when user first clocks in
- **Historical**: Admins can create time cards for past dates
- **Validation**: Punch dates must match time card dates

### Punch Editing
- **Audit Trail**: Original punches preserved with `original_punch_id` and `replaced_by`
- **Permissions**: Users can edit own punches, admins can edit all
- **Recalculation**: Hours automatically recalculated after punch changes

### Real-time Updates
- **Live Clock**: JavaScript updates current time every second
- **Live Hours**: When clocked in, total hours update every second
- **Status Sync**: UI reflects current clock status

### Timezone Handling
- **Storage**: Times stored in application timezone
- **Display**: Converted to user's configured timezone for display
- **JavaScript**: Browser timezone used for real-time clock display

## Common Workflows

### Daily Time Tracking
1. User navigates to `/time-clock`
2. Clicks "Clock In" to start work
3. Uses "Start Break" / "End Break" for breaks
4. Clicks "Clock Out" to end work day
5. System automatically calculates total hours

### Administrative Time Management
1. Admin views all time cards at `/timecards`
2. Can edit individual time cards and punches
3. Can create historical time cards for past dates
4. Can generate timesheets for payroll

### Timesheet Generation
1. Admin navigates to `/timesheets`
2. Selects date range and users
3. System generates comprehensive timesheet with:
   - Daily hours breakdown
   - Weekly totals
   - Overtime calculations
   - PTO tracking
   - Export to Excel capability

This system provides comprehensive time tracking with audit trails, real-time updates, and flexible reporting capabilities suitable for payroll and employee management needs.

## Technical Implementation Details

### User Model Extensions
The `User` model includes several time tracking methods:
- `getTodayTimeCard()`: Gets or creates today's time card
- `getTimeCardForDate($date)`: Gets or creates time card for specific date
- `ensureTimeCardsExistForWeek()`: Creates missing time cards for a week
- `isClockedIn()`: Checks if user is currently clocked in

### Time Calculation Logic
The system uses a sophisticated time calculation approach:

1. **Punch Pairing**: Clock in/out and break in/out punches are paired chronologically
2. **Work Time**: Calculated as time between clock_in and clock_out, minus break time
3. **Break Time**: Calculated as time between break_out and break_in
4. **PTO Time**: sick_time and vacation_time punches add directly to totals
5. **Validation**: Prevents invalid punch sequences (e.g., clock out without clock in)

### Status Management
Time card status is determined by the most recent punch:
- `not_started`: No punches for the day
- `clock_in`: Last punch was clock_in or break_in
- `break`: Last punch was break_out
- `clock_out`: Last punch was clock_out

### Real-time Features
The time clock interface includes several real-time features:
- Current time display updates every second
- When clocked in, total hours increment every second
- When on break, break hours increment every second
- Weekly totals update in real-time
- Status indicators reflect current state

### Data Integrity
The system maintains data integrity through:
- Soft deletes on both time cards and punches
- Audit trail for punch edits via `original_punch_id` and `replaced_by`
- IP address logging for all punches
- Date validation (punches must match time card date)
- Unique constraints (one time card per user per day)

### Performance Considerations
- Time calculations are cached in time card totals
- Real-time calculations only for today's active time card
- Historical time cards use stored totals for performance
- Weekly summaries calculated on-demand with caching

## Troubleshooting Common Issues

### Time Calculation Discrepancies
1. Check for unpaired punches (clock in without clock out)
2. Verify punch times are in correct timezone
3. Ensure time card date matches punch dates
4. Run `calculateHours()` method to recalculate totals

### Missing Time Cards
- Time cards are auto-created on first punch
- Admins can create historical time cards via `/timecards/create-for-date`
- Use `ensureTimeCardsExistForWeek()` to fill gaps

### Permission Issues
- Verify user has appropriate permissions (view_timecards, edit_timecards, etc.)
- Check if user should have "own" permissions vs full permissions
- Ensure permission middleware is applied to routes

### Timezone Problems
- Verify global config `time_zone` setting
- Check that JavaScript timezone matches server timezone
- Ensure punch times are stored consistently

## API Integration

### Time Clock API
The system provides a REST API for external integrations:

```php
GET /api/time-clock/current-data
```

Returns current time card data including:
- Current status
- Today's hours
- Weekly summary
- Recent punches

Authentication required via Sanctum tokens.

### Extending the API
To add new API endpoints:
1. Create methods in `Api\TimeClockController`
2. Add routes to `routes/api.php`
3. Ensure proper authentication middleware
4. Follow existing response format patterns

## Customization Options

### Adding New Punch Types
1. Update the enum in the time_punches migration
2. Add handling in `TimePunch` model methods
3. Update UI components to support new types
4. Modify calculation logic in `TimeCalculationService`

### Custom Time Calculations
The `TimeCalculationService` can be extended for custom business rules:
- Different overtime thresholds
- Holiday time tracking
- Department-specific rules
- Custom pay periods

### UI Customization
The time clock interface uses DaisyUI components and can be customized:
- Modify `time-clock.blade.php` for layout changes
- Update `timecard-edit.js` for behavior changes
- Customize punch list display in `punches-list.blade.php`

## Security Considerations

### Punch Integrity
- IP addresses logged for all punches
- Audit trail maintained for all edits
- Soft deletes prevent data loss
- Original punch preservation for accountability

### Access Control
- Role-based permissions for viewing/editing
- Users can only access own data unless admin
- Timesheet generation restricted to authorized users
- API endpoints require authentication

### Data Protection
- Sensitive time data protected by Laravel's security features
- CSRF protection on all forms
- SQL injection prevention via Eloquent ORM
- XSS protection in Blade templates

## Migration History

The time tracking system has evolved through several migrations:
1. `2025_04_22_185141_create_time_cards_table.php`: Initial time cards
2. `2025_04_22_185149_create_time_punches_table.php`: Initial time punches
3. `2025_04_27_180414_drop_time_cards_and_time_punches_tables.php`: Schema reset
4. `2025_04_27_180845_create_time_cards_table.php`: Recreated time cards
5. `2025_04_27_180855_create_time_punches_table.php`: Recreated time punches
6. `2025_04_27_190854_change_timecard_hours_to_string.php`: Changed to string format
7. `2025_05_13_082542_add_sick_and_vacation_time_to_time_punches.php`: Added PTO support

## Future Enhancements

### Planned Features
- Mobile app integration
- Geolocation-based clock in/out
- Biometric authentication
- Advanced reporting dashboard
- Integration with payroll systems
- Automated time off requests

### Scalability Considerations
- Database indexing for large datasets
- Caching strategies for frequent calculations
- Background job processing for reports
- API rate limiting for external integrations

This comprehensive time tracking system provides a solid foundation for employee time management with room for future enhancements and customizations.
