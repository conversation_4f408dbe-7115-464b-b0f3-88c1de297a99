# Custom Forms System Documentation

## Overview

The ETRFlow2 Custom Forms System is a comprehensive solution for creating, managing, and processing dynamic forms with flexible customer integration and business workflow automation. The system supports various field types, digital signatures, automatic customer creation, discount application, and configurable approval workflows.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Database Schema](#database-schema)
3. [Form Types](#form-types)
4. [Form Builder](#form-builder)
5. [Public Form Submission](#public-form-submission)
6. [Email Notifications](#email-notifications)
7. [Submission Review Process](#submission-review-process)
8. [Customer Integration](#customer-integration)
9. [Customer Integration Workflow](#customer-integration-workflow)
10. [Permissions](#permissions)
11. [Technical Details](#technical-details)

## System Architecture

The form system consists of several key components:

- **Form Management**: Admin interface for creating and managing forms
- **Form Builder**: Drag-and-drop interface for designing forms
- **Public Interface**: Public-facing form submission pages
- **Review System**: Admin workflow for reviewing and approving submissions
- **Customer Integration**: Automatic customer creation and linking
- **Discount System**: Automatic discount application upon approval

### Key Models

- `Form` - Stores form metadata and configuration
- `FormField` - Defines individual form fields and their properties
- `FormSubmission` - Stores submitted form data and processing status

## Database Schema

### Forms Table
```sql
- id
- name (string)
- slug (string, unique) - URL-friendly identifier
- description (text, nullable)
- is_active (boolean) - Whether form accepts submissions
- requires_signature (boolean) - Enables digital signature capture
- requires_customer_link (boolean) - Form creates/links to customer accounts
- requires_approval (boolean) - Admin approval required before processing
- applies_discount_on_approval (boolean) - Auto-apply discount when approved
- required_contact_fields (json, nullable) - Configurable contact field requirements
- settings (json, nullable) - Additional form configuration
- enable_email_notifications (boolean) - Whether email notifications are enabled
- notification_emails (json, nullable) - Array of email addresses to notify
- include_full_data_in_email (boolean) - Whether to include all form data in emails
- discount_id (bigint, nullable) - Associated discount to apply on approval
- agreement_duration_months (integer, nullable) - Duration for discount application
- created_at, updated_at
```

### Form Fields Table
```sql
- id
- form_id (foreign key)
- type (string) - Field type (text, textarea, select, radio, checkbox, html, signature, email, phone, number, date)
- name (string, nullable) - Field identifier for data storage
- label (string, nullable) - Display label
- content (text, nullable) - For HTML blocks
- placeholder (string, nullable)
- help_text (string, nullable)
- required (boolean)
- options (json, nullable) - For select/radio/checkbox options
- validation_rules (json, nullable)
- order (integer) - Display order
- created_at, updated_at
```

### Form Submissions Table
```sql
- id
- form_id (foreign key)
- customer_id (bigint, nullable) - Linked customer account
- status (string) - 'submitted', 'approved', 'rejected'
- form_data (json) - All submitted field data
- submitter_name (string)
- submitter_email (string)
- submitter_phone (string, nullable)
- signature_data (text, nullable) - Base64 encoded signature
- signed_at (timestamp, nullable)
- ip_address (string, nullable)
- user_agent (text, nullable)
- reviewed_by (bigint, nullable) - User who reviewed
- reviewed_at (timestamp, nullable)
- review_notes (text, nullable)
- approved_at (timestamp, nullable)
- approved_by (bigint, nullable) - User who approved
- metadata (json, nullable)
- created_at, updated_at
```

## Form Configuration Options

### Form Features (Configurable)

1. **Customer Linking** (`requires_customer_link`)
   - Enables automatic customer creation/linking
   - Collects extended contact information
   - Shows customer matching interface during submission

2. **Approval Workflow** (`requires_approval`)
   - Requires admin review before processing
   - Three-stage workflow: submitted → approved/rejected
   - Tracks reviewer and approval timestamps

3. **Discount Application** (`applies_discount_on_approval`)
   - Automatically applies specified discount when approved
   - Duration configurable per form
   - Integrates with customer discount system

4. **Digital Signatures** (`requires_signature`)
   - HTML5 canvas-based signature capture
   - Touch and mouse support
   - Signature validation and storage

5. **Configurable Contact Fields** (`required_contact_fields`)
   - Flexible requirements for name, email, phone, address
   - JSON configuration allows per-form customization
   - Smart business/residential customer detection

## Form Builder

The form builder provides a drag-and-drop interface for creating forms.

### Available Field Types

1. **Input Fields**
   - Text - Single line text input
   - Email - Email address with validation
   - Phone - Phone number input
   - Number - Numeric input
   - Date - Date picker

2. **Text Areas**
   - Textarea - Multi-line text input

3. **Selection Fields**
   - Select - Dropdown menu
   - Radio - Single choice from options
   - Checkbox - Multiple choices

4. **Special Fields**
   - HTML - Rich text content blocks
   - Signature - Digital signature capture

### Form Builder Features

- **Drag & Drop Reordering**: Easily rearrange fields
- **Field Configuration**: Set labels, placeholders, help text
- **Validation Rules**: Mark fields as required
- **Auto-Save**: Changes save automatically after 1 second
- **Live Preview**: See how fields will appear
- **Options Management**: Add options for select/radio/checkbox fields

### Creating a Form

1. Navigate to **Admin → Forms**
2. Click **Create New Form**
3. Configure form features:
   - Form Name and Description
   - Active status
   - Customer linking requirement
   - Approval workflow requirement
   - Signature requirement
   - Contact field requirements (name, email, phone, address)
4. For discount-applying forms:
   - Enable "Apply discount on approval"
   - Select associated discount
   - Set agreement duration (optional)
   - Configure notification settings
5. Save and open Form Builder
6. Add fields using the field palette
7. Configure each field's properties
8. Save changes

## Public Form Submission

Public forms are accessible via: `/forms/{slug}`

### Submission Process

1. **Form Display**
   - Shows form title and description
   - Displays all active fields in order
   - HTML blocks render between fields

2. **Contact Information** (Configurable per form)
   - Full Name (always required for customer-linked forms)
   - Email Address (configurable requirement)
   - Phone Number (configurable requirement)
   - Address (configurable requirement)

3. **Business Information** (Customer-linked forms only)
   - Business Name (optional - affects customer type detection)
   - Customer Type (auto-detected based on business name)
   - Business Address (if address field required)
   - City, State, ZIP (if address field required)

4. **Form Fields**
   - Dynamic fields as configured
   - Validation enforced on required fields
   - Help text displayed below fields

5. **Digital Signature** (If required)
   - Canvas-based signature capture
   - Touch/mouse support
   - Clear and undo functionality

6. **Submission**
   - Data validated on submit
   - Stored with submitter information
   - Success page with reference number

### Data Storage

All form data is stored in the `form_data` JSON field, including:
- User-defined form fields
- Business information (for service agreements)
- System fields excluded: _token, signature_data

## Email Notifications

The form system supports configurable email notifications that are sent immediately when a form is submitted. Email notifications are configured at the form level, allowing different forms to have different notification settings.

### Configuration

Email notifications are configured in the form settings:

1. **Enable Email Notifications** - Toggle to enable/disable notifications for the form
2. **Notification Recipients** - Array of email addresses to receive notifications
3. **Include Full Data** - Option to include all submitted data in the email or just a link

### Email Templates

The system uses two email templates for form submissions:

#### Basic Notification (`form_submission_notification`)
- Contains submitter information and basic form details
- Includes a link to view the full submission
- Used when "Include Full Data" is disabled

#### Full Data Notification (`form_submission_notification_full`)
- Contains all submitted form data in a formatted table
- Includes submitter information and submission details
- Used when "Include Full Data" is enabled

#### PDF Delivery Email (`form_submission_pdf_delivery`)
- Contains customer-friendly approval notification
- Includes PDF attachment of the completed form
- Used when staff manually emails PDF to customer after approval
- Professional green styling to indicate approval status

### Template Variables

The notification templates support the following variables:

- `{form_name}` - Name of the submitted form
- `{form_id}` - Form ID
- `{submission_id}` - Form submission ID
- `{submitter_name}` - Name of the person who submitted the form
- `{submitter_email}` - Email of the person who submitted the form
- `{submitter_phone}` - Phone number of the person who submitted the form
- `{submission_date}` - Date and time when the form was submitted
- `{view_url}` - URL to view the full submission details
- `{app_name}` - Application name
- `{current_date}` - Current date when email is sent
- `{form_data_table}` - Table containing all submitted form data (full template only)

The PDF delivery template supports additional variables:

- `{submitter_contact}` - Contact person name (for business submissions only)
- `{approval_date}` - Date and time when the form was approved
- `{approved_by}` - Name of the person who approved the submission
- `{customer_name}` - Name of the linked customer (if any)

**Important Field Mapping Note:**
- For **Business Submissions**: `{submitter_name}` = business name, `{submitter_contact}` = contact person name
- For **Residential Submissions**: `{submitter_name}` = full name, `{submitter_contact}` = null

**HTML Content Variables:**
When using variables in HTML form fields, the following variables are available:
- `{full_name}` - Contact person name (business) or full name (residential) - **Use this for personal greetings**
- `{business_name}` - Business name (if provided) or submitter name
- `{contact_person}` - Contact person name (business only, empty for residential)
- `{submitter_name}` - Raw submitter_name field (business name for business, full name for residential)
- `{submitter_contact}` - Raw submitter_contact field (contact person for business, null for residential)
- `{submitter_email}` - Email address
- `{submitter_phone}` - Phone number
- `{submission_date}` - Date form was submitted
- `{submission_id}` - Submission ID number
- Date variables: `{date_today_short}`, `{date_today_full}`, `{date_plus_1yr_full}`

### Email Processing

When a form is submitted:

1. System checks if email notifications are enabled for the form
2. Validates notification email addresses
3. Determines which template to use based on "Include Full Data" setting
4. Processes template data including form submission details
5. Sends emails using the templated email system
6. Logs success/failure for each email sent

### Configuration Example

```php
// Form model properties
$form->enable_email_notifications = true;
$form->notification_emails = ['<EMAIL>', '<EMAIL>'];
$form->include_full_data_in_email = true;
```

### Error Handling

- Invalid email addresses are filtered out automatically
- Failed email sends are logged with error details
- System continues processing even if some emails fail
- Fallback logging if email templates are missing

## Submission Review Process

### Workflow States

1. **Submitted** (Pending Review)
   - Initial state after submission
   - Shows yellow badge
   - Available for review

2. **Approved**
   - Submission accepted
   - Green badge
   - Triggers discount application (if service agreement)

3. **Rejected**
   - Submission declined
   - Red badge
   - Requires rejection reason

### Review Interface

Located at: **Admin → Form Submissions**

**Features:**
- Filter by form, status, customer
- Search by submitter details
- Date range filtering
- Bulk actions support

**Individual Submission View:**
- Complete form data display
- Submitter information
- Customer linking interface
- Approval/rejection actions
- Activity history
- PDF generation and download
- Email PDF to customer functionality

### Review Actions

1. **Approve**
   - Option to link existing customer
   - Option to create new customer
   - Automatic discount application for service agreements

2. **Reject**
   - Requires rejection reason
   - Stores reviewer information
   - Timestamp recorded

### PDF Generation and Delivery

The form system provides comprehensive PDF generation and delivery capabilities for approved submissions.

#### PDF Features

1. **Automatic PDF Generation**
   - Professional PDF layout with form branding
   - Includes all submitted form data
   - Digital signature rendering (if captured)
   - Submission metadata and approval details
   - Consistent formatting and styling

2. **PDF Download**
   - Available for all approved submissions
   - Accessible via "Download PDF" button
   - Generates on-demand with current data

3. **Email PDF to Customer**
   - One-click email delivery to customer
   - Only available for approved submissions with email addresses
   - Uses professional email template with PDF attachment
   - Loading state feedback during send process
   - Success/error notifications

#### PDF Content Structure

Generated PDFs include:

- **Header Section**: Form name and submission details
- **Submitter Information**: Contact details and customer type
- **Form Data**: All submitted fields in organized layout
- **HTML Content**: Processed HTML blocks with variable substitution
- **Digital Signature**: Rendered signature image (if captured)
- **Approval Information**: Approval date, approver, and status
- **Footer**: Professional branding and generation timestamp

#### Email Delivery Process

When emailing PDF to customer:

1. **Validation**: Ensures submission is approved and email exists
2. **PDF Generation**: Creates PDF using existing generation logic
3. **Email Template**: Uses `form_submission_pdf_delivery` template
4. **Attachment**: Attaches PDF with descriptive filename
5. **Delivery**: Sends via configured email system
6. **Feedback**: Provides success/error notification to admin

#### Technical Implementation

- **PDF Library**: Uses DomPDF for PDF generation
- **Signature Handling**: Converts base64 signatures to temporary image files
- **Template System**: Integrates with EmailTemplateService
- **Error Handling**: Graceful failure with user feedback
- **File Cleanup**: Automatic cleanup of temporary signature files

## Customer Integration

### Customer Linking Options (Manual Only)

**Important**: All customer linking is now manual and requires staff approval. No automatic linking occurs during form submission.

1. **Link to Existing Customer**
   - Dynamic search interface
   - Search by name, email, phone
   - System shows potential matches for staff review
   - Staff must manually verify and approve the link

2. **Create New Customer**
   - Auto-populated from submission data
   - Smart business/residential detection
   - Preview before creation
   - Staff reviews and approves before creation

### Auto-Population Logic

**For Business Submissions:**
```
IF business_name provided:
  submission.submitter_name = business_name
  submission.submitter_contact = full_name  (contact person)
  customer.name = business_name
  customer.contact = full_name
  customer.type = 'Business'
ELSE:
  submission.submitter_name = full_name
  submission.submitter_contact = null
  customer.name = full_name
  customer.contact = null
  customer.type = 'Residential'
```

**All Submissions:**
```
customer.email = submitter_email
customer.phone = submitter_phone
customer.customer_type = form.customer_type
customer.address = form.address (if provided)
```

### Customer Profile Integration

- Form submissions appear on customer profiles
- Shows submission status and type
- Links to full submission details
- Highlights active discount agreements

## Customer Integration Workflow

### Complete Process

1. **Form Creation**
   - Create form with customer linking enabled
   - Configure required contact fields
   - Associate with specific discount (optional)
   - Enable approval workflow (optional)
   - Set agreement duration for discount (optional)

2. **Public Submission**
   - Customer fills out form
   - Provides contact information based on form requirements
   - Signs digitally (if required)
   - No automatic customer linking occurs

3. **Admin Review** (required for customer-linked forms)
   - Review submission details
   - Manually link to existing customer account OR create new customer
   - System provides potential customer matches based on email/phone
   - Staff must verify and approve customer linking
   - Approve/reject submission with notes

4. **Automatic Actions** (on approval)
   - Customer discount applied (if configured)
   - Duration set based on form configuration
   - Customer account updated
   - Activity and notifications logged

### Discount Application

When a form with discount application is approved:

```php
CustomerDiscount::updateOrCreate([
    'customer_id' => $customer->id,
    'discount_id' => $form->discount_id,
], [
    'start_date' => now(),
    'end_date' => now()->addMonths($duration),
    'maximum_uses' => null, // Unlimited for form-based discounts
    'usage_count' => 0,
]);
```

## Permissions

The form system uses the following permissions:

### Form Management
- `manage_forms` - Create, edit, and delete custom forms
- `view_forms` - View custom forms

### Submission Management
- `view_form_submissions` - View form submissions
- `manage_form_submissions` - Review, approve, and reject submissions
- `export_form_submissions` - Export submission data

### Permission Checks

```php
// Route level
Route::middleware(['permission:manage_forms'])

// View level
@perms('manage_form_submissions')
    // Show admin actions
@endperms
```

## Technical Details

### Routes

```php
// Admin Routes
GET  /admin/forms                    - Form listing
GET  /admin/forms/create             - Create form
POST /admin/forms                    - Store form
GET  /admin/forms/{form}             - Show form
GET  /admin/forms/{form}/edit        - Edit form
PUT  /admin/forms/{form}             - Update form
DELETE /admin/forms/{form}           - Delete form
GET  /admin/forms/{form}/builder     - Form builder
POST /admin/forms/{form}/save-fields - Save form fields
GET  /admin/forms/{form}/preview     - Preview form
POST /admin/forms/{form}/clone       - Clone form

// Submission Routes  
GET  /admin/form-submissions         - Submission listing
GET  /admin/form-submissions/{id}    - Show submission
GET  /admin/form-submissions/{id}/pdf - Generate and download PDF
POST /admin/form-submissions/{id}/email-pdf - Email PDF to customer
GET  /admin/form-submissions/{id}/export - Export submission data
POST /admin/form-submissions/{id}/approve     - Approve
POST /admin/form-submissions/{id}/reject      - Reject
POST /admin/form-submissions/{id}/link-customer - Link customer

// Public Routes
GET  /forms/{slug}                   - Public form
POST /forms/{slug}/submit            - Submit form
```

### Key Features

1. **Auto-Save Form Builder**
   - 1-second debounced auto-save
   - Visual feedback during save
   - Error handling

2. **Responsive Design**
   - Mobile-first approach
   - Touch-friendly interfaces
   - Accessible form controls

3. **Data Validation**
   - Server-side validation
   - Client-side validation
   - Custom validation rules

4. **Security**
   - CSRF protection
   - Permission-based access
   - Input sanitization
   - IP tracking

### JavaScript Components

- **SortableJS** - Drag and drop functionality
- **App Dialog** - Consistent modal dialogs
- **Dynamic Customer Search** - AJAX-powered search
- **Signature Pad** - Canvas-based signatures

### Best Practices

1. **Form Design**
   - Keep forms concise
   - Use clear labels and help text
   - Group related fields
   - Test on mobile devices

2. **Data Management**
   - Regular submission reviews
   - Timely approval/rejection
   - Maintain customer links
   - Monitor service agreements

3. **Performance**
   - Limit form fields (recommended < 50)
   - Optimize HTML content blocks
   - Use pagination for submissions
   - Archive old submissions

## Troubleshooting

### Common Issues

1. **Form Not Showing Publicly**
   - Check form is active
   - Verify slug is correct
   - Ensure no duplicate slugs

2. **Fields Not Saving**
   - Check browser console for errors
   - Verify CSRF token
   - Check field name uniqueness

3. **Customer Creation Fails**
   - Verify required fields present
   - Check email uniqueness
   - Review validation errors

4. **Discount Not Applied**
   - Confirm form has "applies discount on approval" enabled
   - Check discount association
   - Verify customer linkage
   - Ensure submission approved

### Debug Mode

For admins, raw form data is viewable:
- Click "View Raw Data" in submission view
- Shows complete JSON structure
- Helpful for troubleshooting

## Future Enhancements

Potential improvements to consider:

1. **Field Types**
   - File upload fields
   - Conditional logic
   - Calculated fields
   - Address autocomplete

2. **Notifications**
   - Email confirmations
   - Admin notifications
   - Status updates

3. **Reporting**
   - Submission analytics
   - Conversion tracking
   - Export capabilities

4. **Integration**
   - Webhook support
   - API endpoints
   - Third-party services

---

*Last Updated: June 2025*
*Version: 2.1 - Manual Customer Linking (Automatic Linking Disabled)*