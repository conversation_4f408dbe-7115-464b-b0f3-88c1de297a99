# ETRFlow Application Style Guide

Comprehensive design patterns and styling conventions for maintaining consistency across the ETRFlow application.

## Table of Contents

1. [Design Philosophy](#design-philosophy)
2. [Layout Structure](#layout-structure)
3. [Card Components](#card-components)
4. [Form Design](#form-design)
5. [Data Display](#data-display)
6. [Navigation & Actions](#navigation--actions)
7. [Mobile Responsiveness](#mobile-responsiveness)
8. [Permissions System](#permissions-system)
9. [Auto-Save Design Patterns](#auto-save-design-patterns)
10. [Best Practices](#best-practices)

## Design Philosophy

### Core Principles
- **DaisyUI 5 First**: All styling uses DaisyUI 5 classes for consistency
- **Mobile-First Responsive**: Designs work seamlessly from mobile to desktop
- **Clean & Modern**: Minimal, professional appearance with subtle gradients
- **No Nested Cards**: Avoid cards within cards - use clean separation instead

## Layout Structure

### App Layout Foundation
```blade
<x-app-layout
    page-title="Page Title"
    page-icon="fa-sharp fa-icon"
    :primary-buttons="[...]"
    :action-buttons="[...]"
    :breadcrumbs="[...]">

    <div class="py-6 lg:py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
            <!-- Page content cards -->
        </div>
    </div>
</x-app-layout>
```

### Container Patterns
- **Max Width**: `max-w-5xl` for forms/workflows, `max-w-7xl` for data lists
- **Responsive Padding**: `px-4 sm:px-6 lg:px-8` or `sm:px-2 lg:px-4` for lists
- **Content Spacing**: `space-y-6` between main sections, `space-y-8` for index pages
- **Page Spacing**: `py-6 lg:py-8` for consistent vertical rhythm

### Header System
```blade
:primary-buttons="[
    ['type' => 'create', 'route' => route('items.create'), 'text' => 'New Item'],
    ['type' => 'edit', 'route' => route('items.edit', $item), 'text' => 'Edit Item']
]"
:action-buttons="[
    ['name' => 'Custom Action', 'route' => route('action'), 'icon' => 'fa-sharp fa-icon', 'class' => 'btn btn-secondary btn-sm gap-2']
]"
```

## Card Components

### Standard Card Structure
```blade
<div class="card bg-base-100 shadow-md">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
        <div class="flex items-center gap-3">
            <div class="avatar avatar-placeholder">
                <div class="bg-primary text-primary-content w-8 rounded-lg">
                    <i class="fa-sharp fa-icon text-sm"></i>
                </div>
            </div>
            <h4 class="text-lg font-semibold text-base-content">Section Title</h4>
        </div>
    </div>

    <!-- Content Section -->
    <div class="p-6 space-y-6">
        <!-- Content here -->
    </div>
</div>
```

### Introduction Cards (Create Pages)
```blade
<div class="bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 rounded-2xl border border-base-300/50 p-6 lg:p-8 mb-10 relative overflow-hidden shadow-lg">
    <!-- Decorative Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute top-0 left-0 w-32 h-32 bg-primary rounded-full -translate-x-16 -translate-y-16"></div>
        <div class="absolute bottom-0 right-0 w-24 h-24 bg-secondary rounded-full translate-x-12 translate-y-12"></div>
    </div>

    <div class="relative z-10">
        <div class="flex items-start gap-4">
            <div class="avatar avatar-placeholder">
                <div class="bg-gradient-to-br from-primary to-primary-focus text-primary-content w-12 rounded-xl shadow-lg">
                    <i class="fa-sharp fa-plus-circle text-xl"></i>
                </div>
            </div>
            <div class="flex-1">
                <h3 class="text-2xl font-bold text-base-content mb-2">Page Title</h3>
                <p class="text-base-content/70 text-base leading-relaxed">Description text</p>
            </div>
        </div>
    </div>
</div>
```

### Card Header Colors
- **Primary**: `from-primary/10 to-primary/5` with `bg-primary` avatar
- **Secondary**: `from-secondary/10 to-secondary/5` with `bg-secondary` avatar
- **Info**: `from-info/10 to-info/5` with `bg-info` avatar
- **Accent**: `from-accent/10 to-accent/5` with `bg-accent` avatar

## Form Design


### Radio Button Cards (Step1 Style)
```blade
<div class="card bg-base-200/50 border border-base-300 hover:bg-base-200 hover:border-primary/30 transition-all duration-200 cursor-pointer">
    <label class="flex items-start cursor-pointer p-4">
        <input type="radio" name="option" value="value" class="radio radio-primary mt-1 mr-3 flex-shrink-0" required>
        <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2 mb-2">
                <i class="fa-sharp fa-icon text-primary"></i>
                <span class="text-lg font-medium text-base-content">Option Title</span>
            </div>
            <p class="text-sm text-base-content/70">Option description</p>
        </div>
    </label>
</div>
```

### Submit Section (Create Pages)
```blade
<div class="divider my-8">
    <span class="text-base-content/50 font-medium">Ready to create?</span>
</div>

<div class="bg-base-100 rounded-2xl border border-base-300/50 shadow-lg p-6">
    <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
        <div class="flex items-center gap-3">
            <div class="avatar avatar-placeholder">
                <div class="bg-base-300 text-base-content w-10 rounded-lg">
                    <i class="fa-sharp fa-info-circle text-sm"></i>
                </div>
            </div>
            <div class="text-center sm:text-left">
                <p class="font-medium text-base-content">Review your information</p>
                <p class="text-sm text-base-content/60">Make sure all details are correct</p>
            </div>
        </div>
        <div class="flex gap-3 w-full sm:w-auto">
            <a href="{{ route('back') }}" class="btn btn-lg gap-2 flex-1 sm:flex-none">
                <i class="fa-sharp fa-arrow-left"></i>
                Cancel
            </a>
            <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                <i class="fa-sharp fa-plus"></i>
                Create Item
            </button>
        </div>
    </div>
</div>
```

### Navigation Pattern (Step Pages)
```blade
<div class="divider my-8">
    <span class="text-base-content/50 font-medium">Continue to next step</span>
</div>

<div class="flex flex-col sm:flex-row justify-between items-center gap-4">
    <a href="{{ route('back') }}" class="btn btn-lg gap-2 w-full sm:w-auto">
        <i class="fa-sharp fa-arrow-left"></i>
        Back
    </a>
    <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg w-full sm:w-auto">
        Continue to Step 2
        <i class="fa-sharp fa-arrow-right"></i>
    </button>
</div>
```

## Data Display

### Index Page Structure
```blade
<!-- Header Card -->
<div class="card bg-base-100 shadow-sm border border-base-200">
    <div class="card-body">
        <div class="flex items-center justify-between">
            <div class="flex-1">
                <h1 class="text-3xl font-bold text-base-content">Page Title</h1>
                <p class="text-base-content/70 mt-1 text-lg">Description</p>
            </div>
            <div class="hidden lg:flex items-center gap-2 text-base-content/60">
                <div class="stat">
                    <div class="stat-title text-xs">Total Items</div>
                    <div class="stat-value text-2xl">{{ $items->total() }}</div>
                </div>
            </div>
        </div>
    </div>
</div>
```

### Search & Filter Card
```blade
<div class="card bg-base-100 shadow-md">
    <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
        <div class="flex items-center gap-3">
            <div class="avatar avatar-placeholder">
                <div class="bg-primary text-primary-content w-8 rounded-lg">
                    <i class="fa-sharp fa-magnifying-glass text-sm"></i>
                </div>
            </div>
            <h4 class="text-lg font-semibold text-base-content">Search & Filter</h4>
        </div>
    </div>
    <div class="p-6 space-y-6">
        <!-- Search and filter content -->
    </div>
</div>
```

### Data Cards (Mobile/Desktop Responsive)
```blade
<div class="divide-y divide-base-200/50">
    @foreach ($items as $item)
    <div class="p-3 lg:p-4 hover:bg-base-200/50 transition-all duration-200 {{ $loop->even ? 'bg-base-100' : 'bg-base-200/30' }}">
        <!-- Mobile Layout -->
        <div class="lg:hidden">
            <div class="flex items-center gap-2 mb-2">
                <div class="flex-1 min-w-0">
                    <a href="{{ route('items.show', $item) }}" class="link-primary font-medium hover:link-hover text-lg">
                        {{ $item->name }}
                    </a>
                </div>
                <div class="badge badge-sm">{{ $item->status }}</div>
            </div>
            <!-- Two Column Details -->
            <div class="grid grid-cols-2 gap-3 mb-3 text-xs">
                <div class="space-y-1"><!-- Left column --></div>
                <div class="space-y-1"><!-- Right column --></div>
            </div>
            <!-- Mobile Action Bar -->
            <div class="flex gap-2 pt-2 border-t border-base-200/50">
                <a href="#" class="btn btn-primary btn-sm flex-1 gap-1">
                    <i class="fa-sharp fa-eye"></i>
                    View
                </a>
            </div>
        </div>

        <!-- Desktop Layout -->
        <div class="hidden lg:grid lg:grid-cols-12 gap-3 items-center text-sm">
            <!-- Grid columns for desktop -->
        </div>
    </div>
    @endforeach
</div>
```

### Sorting Controls
```blade
<div class="px-6 py-4 border-b border-base-300/50 bg-base-50">
    <form method="GET" class="flex flex-wrap items-center gap-4">
        <div class="flex items-center gap-2">
            <label class="text-sm font-medium text-base-content/70">Sort by:</label>
            <select name="sort" class="select select-bordered select-sm w-auto min-w-32" onchange="this.form.submit()">
                <option value="id">ID</option>
                <option value="name">Name</option>
                <option value="created_at">Created</option>
            </select>
        </div>
    </form>
</div>
```

### Empty States
```blade
<div class="text-center text-base-content/70 py-12">
    <div class="flex flex-col items-center gap-4">
        <div class="avatar avatar-placeholder">
            <div class="bg-base-200 text-base-content/50 rounded-full w-16 h-16">
                <i class="fa-sharp fa-icon text-2xl"></i>
            </div>
        </div>
        <div class="text-center">
            <h3 class="text-lg font-medium text-base-content/80">No items found</h3>
            <p class="text-sm text-base-content/60 mt-1">Description text</p>
        </div>
        <a href="{{ route('items.create') }}" class="btn btn-primary btn-sm gap-2">
            <i class="fa-sharp fa-plus"></i>
            Add First Item
        </a>
    </div>
</div>
```

### Pagination
ETRFlow uses a custom pagination component that provides DaisyUI-styled controls with items per page selection.

#### Standard Pagination Implementation
```blade
<!-- Pagination Section -->
@if($items->hasPages())
    <div class="px-6 py-4 border-t border-base-300/50 bg-base-50">
        <x-pagination :paginator="$items" :pagination="$pagination" />
    </div>
@endif
```

#### Controller Pagination Setup
```php
public function index(Request $request)
{
    // Get pagination preference from session or request
    $pagination = $request->get('pagination', session('pagination', 25));
    session(['pagination' => $pagination]);

    // Apply pagination with query string preservation
    $items = $query->paginate($pagination)->withQueryString();

    return view('items.index', compact('items', 'pagination'));
}
```

#### Pagination Features
- **DaisyUI Join Buttons**: Styled with `join-item btn` classes
- **Items Per Page**: Dropdown with options [5, 10, 25, 50, 100]
- **Smart Page Display**: Shows ellipsis for large page counts
- **Query String Preservation**: Maintains filters when changing pages
- **Results Summary**: "Showing X to Y of Z results" display
- **Session Persistence**: Remembers user's preferred page size

#### Pagination Placement
- **Outside Card Content**: Pagination goes in its own section below the main card
- **Border Styling**: `border-t border-base-300/50 bg-base-50` for visual separation
- **Conditional Display**: Only show when `$paginator->hasPages()` returns true

## Navigation & Actions

### Progress Steps
```blade
<div class="card bg-base-100 shadow-md">
    <div class="p-6">
        <ul class="steps steps-horizontal w-full">
            <li class="step step-primary">Current Step</li>
            <li class="step">Next Step</li>
            <li class="step">Final Step</li>
        </ul>
    </div>
</div>
```

### Information Display
```blade
<div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
    <div class="flex items-center gap-2">
        <i class="fa-sharp fa-icon text-primary"></i>
        <span class="font-medium">Label:</span>
        <span>Value</span>
    </div>
</div>
```

### Action Buttons (Desktop)
```blade
<div class="flex gap-1 justify-center">
    <div class="tooltip" data-tip="View Item">
        <a href="#" class="btn btn-sm btn-circle btn-ghost hover:bg-primary hover:text-primary-content transition-all">
            <i class="fa-sharp fa-eye"></i>
        </a>
    </div>
</div>
```

## Mobile Responsiveness

### Mobile-First Design
- **Condensed Layouts**: `p-3 lg:p-4` for mobile-friendly padding
- **Full Width Buttons**: `w-full sm:w-auto` for mobile-friendly buttons
- **Responsive Flex**: `flex-col sm:flex-row` for stacking on mobile
- **Two-Column Mobile**: `grid grid-cols-2 gap-3` for mobile data display
- **Mobile Action Bars**: `flex gap-2 pt-2 border-t border-base-200/50`

### Responsive Patterns
```blade
<!-- Mobile/Desktop Toggle -->
<div class="lg:hidden"><!-- Mobile content --></div>
<div class="hidden lg:grid lg:grid-cols-12"><!-- Desktop content --></div>

<!-- Responsive Button Sizing -->
<button class="btn btn-sm flex-1 gap-1">Mobile</button>
<button class="btn btn-sm btn-circle btn-ghost">Desktop</button>

<!-- Responsive Grid -->
<div class="grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-6">
```

### Key Mobile Patterns
- **Always use `avatar-placeholder`** instead of just `placeholder`
- **No nested cards** - use clean separation with dividers
- **Radio button cards** for better touch interaction
- **Alternating row backgrounds**: `{{ $loop->even ? 'bg-base-100' : 'bg-base-200/30' }}`

## Permissions System

ETRFlow uses a custom permission system based on user groups and permissions. Understanding how to properly implement permissions is crucial for maintaining security and proper access control.

### Permission Architecture

#### Core Components
- **Users**: Individual user accounts with enabled/disabled status
- **User Groups**: Collections of users (e.g., "Admin", "Staff", "Drivers")
- **Permissions**: Specific capabilities (e.g., "view_inventory_items", "manage_pickup_requests")
- **Permission Scopes**: Logical groupings (e.g., "inventory", "pickups", "timeclock")

#### Permission Naming Convention
```
{action}_{resource}_{modifier?}
```

Examples:
- `view_inventory_items` - View inventory items
- `edit_own_timecards` - Edit only your own timecards
- `manage_pickup_requests` - Full management of pickup requests
- `is_admin` - Special admin permission

### Checking Permissions in Views

#### Using @perms Directive (Recommended)
```blade
@perms('manage_pickup_requests')
    <div class="admin-only-content">
        <!-- Content only visible to users with manage_pickup_requests permission -->
    </div>
@endperms

@perms('view_inventory_items')
    <a href="{{ route('inventory.index') }}" class="btn btn-primary">
        View Inventory
    </a>
@endperms
```

#### Multiple Permissions (OR Logic)
```blade
@perms('edit_timecards|edit_own_timecards')
    <button class="btn btn-secondary">Edit Timecard</button>
@endperms
```

#### Checking Permissions in PHP
```php
// In controllers or models
if (auth()->user()->hasPermission('view_customer_accounts')) {
    // User has permission
}

// Check multiple permissions (OR logic)
$permissions = ['edit_calendars', 'edit_own_calendars'];
$hasAnyPermission = collect($permissions)->some(fn($perm) => auth()->user()->hasPermission($perm));
```

### App Layout Integration

#### Breadcrumbs with Permission Checks
```blade
<x-app-layout
    :breadcrumbs="[
        ['name' => 'Pickup Requests', 'route' => auth()->user()->hasPermission('view_pickup_requests') ? 'pickup-requests.index' : null],
        ['name' => 'Request #' . $request->id]
    ]">
```

#### Action Buttons with Permissions
```blade
:action-buttons="[
    [
        'name' => 'Cancel Request',
        'route' => '#',
        'icon' => 'fa-sharp fa-times',
        'class' => 'btn btn-error btn-sm gap-2',
        'onclick' => 'cancelRequest()',
        'permission' => 'manage_pickup_requests',
        'condition' => in_array($request->status, ['pending', 'contacted'])
    ]
]"
```

#### Primary Buttons with Permissions
```blade
:primary-buttons="[
    [
        'type' => 'create',
        'route' => route('customers.create'),
        'text' => 'New Customer',
        'permission' => 'create_customer_accounts'
    ]
]"
```


#### General Button Rules
- **No `btn-outline` for any buttons**


### Route Protection

#### Middleware Usage
```php
// In routes/web.php
Route::middleware(['auth', 'permission:view_pickup_requests'])->group(function () {
    Route::get('/pickup-requests', [PickupRequestController::class, 'index'])->name('pickup-requests.index');
    Route::get('/pickup-requests/{pickupRequest}', [PickupRequestController::class, 'show'])->name('pickup-requests.show');
});

Route::middleware(['auth', 'permission:manage_pickup_requests'])->group(function () {
    Route::post('/pickup-requests/{pickupRequest}/finalize', [PickupRequestController::class, 'finalize'])->name('pickup-requests.finalize');
});
```

#### Multiple Permissions (OR Logic)
```php
Route::middleware(['auth', 'permission:edit_calendars|edit_own_calendars'])->group(function () {
    Route::get('/calendars/{calendar}/edit', [CalendarController::class, 'edit'])->name('calendars.edit');
});
```

### Controller Permission Checks

#### Basic Permission Check
```php
public function show(PickupRequest $pickupRequest)
{
    // Route middleware already checked view_pickup_requests

    return view('pickup-requests.show', compact('pickupRequest'));
}
```

#### Additional Logic for "Own" Permissions
```php
public function edit(Calendar $calendar)
{
    $user = auth()->user();

    // Check if user has general edit permission OR owns the calendar
    if (!$user->hasPermission('edit_calendars') &&
        !($user->hasPermission('edit_own_calendars') && $calendar->owner_id === $user->id)) {
        abort(403);
    }

    return view('calendars.edit', compact('calendar'));
}
```

### Common Permission Patterns

#### Conditional Content Display
```blade
<div class="card bg-base-100 shadow-md">
    <div class="p-6">
        <h3>Customer Information</h3>

        @perms('view_customer_accounts')
            <p>Email: {{ $customer->email }}</p>
            <p>Phone: {{ $customer->phone }}</p>
        @endperms

        @perms('edit_customer_accounts')
            <a href="{{ route('customers.edit', $customer) }}" class="btn btn-primary">
                Edit Customer
            </a>
        @endperms
    </div>
</div>
```

#### Navigation Menu Items
```blade
@perms('view_inventory_items')
    <li><a href="{{ route('inventory.index') }}">Inventory</a></li>
@endperms

@perms('view_pickup_requests')
    <li><a href="{{ route('pickup-requests.index') }}">Pickup Requests</a></li>
@endperms

@perms('manage_settings')
    <li><a href="{{ route('admin.settings') }}">Settings</a></li>
@endperms
```

#### Form Field Restrictions
```blade
<div>
    <label>Customer Status</label>
    @perms('edit_customer_accounts')
        <select name="status" class="select select-bordered">
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
        </select>
    @else
        <input type="text" value="{{ $customer->status }}" class="input input-bordered" readonly>
    @endperms
</div>
```

### Available Permissions by Scope

#### General
- `view_dashboard` - View the dashboard
- `is_admin` - Administrator access

#### Inventory
- `view_inventory_items` - View inventory items
- `create_inventory_items` - Create new inventory items
- `edit_inventory_items` - Edit existing inventory items
- `delete_inventory_items` - Delete inventory items
- `generate_inventory_descriptions` - Generate AI descriptions

#### Customers
- `view_customer_accounts` - View customer information
- `create_customer_accounts` - Create new customers
- `edit_customer_accounts` - Edit customer information
- `delete_customer_accounts` - Delete customers

#### Pickups
- `view_pickup_requests` - View pickup requests
- `manage_pickup_requests` - Accept, link customers, finalize requests
- `view_pickup_calendar` - View pickup calendar
- `manage_pickup_calendar` - Manage pickup calendar events

#### Timeclock
- `view_timecards` - View all time cards
- `view_own_timecards` - View only your own time cards
- `edit_timecards` - Edit any time cards
- `edit_own_timecards` - Edit only your own time cards
- `manage_paid_time_off` - Manage PTO requests

### Best Practices for Permissions

#### Do's
- ✅ Always use `@perms()` directive instead of `@can()` in Blade templates
- ✅ Check permissions at both route and view levels for security
- ✅ Use descriptive permission names following the naming convention
- ✅ Group related permissions logically in middleware
- ✅ Provide graceful fallbacks when permissions are missing
- ✅ Use "own" permissions for user-specific content (e.g., `edit_own_timecards`)

#### Don'ts
- ❌ Don't rely only on frontend permission checks - always protect routes
- ❌ Don't use `@can()` directive - use `@perms()` instead
- ❌ Don't hardcode permission checks - use the permission system
- ❌ Don't forget to handle permission failures gracefully
- ❌ Don't create overly granular permissions that complicate management

#### Security Considerations
- **Defense in Depth**: Check permissions at route, controller, and view levels
- **Fail Secure**: Default to denying access when permissions are unclear
- **User Experience**: Provide clear feedback when access is denied
- **Audit Trail**: Log permission-related actions for security monitoring

## Best Practices

### Layout & Structure
- ✅ Use `max-w-6xl` for most views. For more detailed views, use `max-w-7xl`. For data tables use style="max-width: 1600px;"
- ✅ Apply consistent spacing: `space-y-6` for forms, `space-y-8` for index pages
- ✅ Use property-based headers with breadcrumbs
- ✅ Follow mobile-first responsive design patterns. Avoid using too much padding on mobile.
- ✅ Break down complex pages into smaller components for better reusability and maintainability. Keep page specific components in a ./components or ./partials directory relative to the view file.

### Cards & Components
- ✅ Use gradient card headers with semantic colors
- ✅ Include avatar placeholders with appropriate icons
- ✅ Apply hover effects: `hover:bg-base-200/50 transition-all duration-200`
- ✅ Use alternating backgrounds for data rows

### Forms & Navigation
- ✅ Use radio button cards for better UX
- ✅ Apply consistent button sizing: `btn-lg` for primary actions
- ✅ Include proper form validation with error states
- ✅ Use dividers for section separation

### Data Display
- ✅ Implement responsive mobile/desktop layouts
- ✅ Use two-column mobile layouts for data
- ✅ Include proper empty states with call-to-action
- ✅ Apply consistent sorting and filtering patterns

### Don'ts
- ❌ Don't use nested card layouts (cards within cards)
- ❌ Don't put navigation buttons in separate cards
- ❌ Don't use fieldsets in forms
- ❌ Don't forget mobile responsiveness
- ❌ Don't use inconsistent spacing patterns
- ❌ Don't use just `placeholder` - always use `avatar-placeholder`
- ❌ Don't forget hover states and transitions
- ❌ Don't use table layouts when card layouts work better

### Icon & Color Guidelines
- **Icons**: Always use `fa-sharp fa-icon` for consistency, other classes like fa-solid WILL NOT WORK
- **Colors**: Use semantic colors (primary, secondary, success, warning, error)
- **Text Opacity**: Use `/70`, `/60`, `/50` for muted text
- **Transitions**: Apply `transition-all duration-200` for smooth interactions

## Auto-Save Design Patterns

**Important**: When using Quill editor:
- Use the `@importQuill` directive which includes all Quill CSS and loads Quill from CDN
- Do not add additional `<script src="...quill.js">` tags as this will cause duplicate loading
- Initialize Quill in a `DOMContentLoaded` event listener to ensure proper loading order

### Auto-Save Status Component
```blade
<!-- Auto-Save Status & Manual Save Section -->
<div class="card bg-base-100 shadow-md">
    <div class="bg-gradient-to-r from-success/10 to-success/5 px-6 py-4 border-b border-base-300/50">
        <div class="flex items-center gap-3">
            <div class="avatar avatar-placeholder">
                <div class="bg-success text-success-content w-8 rounded-lg">
                    <i class="fa-sharp fa-save text-sm"></i>
                </div>
            </div>
            <h4 class="text-lg font-semibold text-base-content">Save Changes</h4>
        </div>
    </div>
    <div class="p-6">
        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div class="flex items-center gap-3">
                <div class="avatar avatar-placeholder">
                    <div class="bg-base-300 text-base-content w-10 rounded-lg">
                        <i class="fa-sharp fa-info-circle text-sm"></i>
                    </div>
                </div>
                <div class="text-center sm:text-left">
                    <p id="saveStatusIndicator" class="font-medium text-base-content">Changes will save automatically</p>
                    <p class="text-sm text-base-content/60">Your changes are saved as you type for accessibility</p>
                </div>
            </div>
            <div class="flex gap-3 w-full sm:w-auto">
                <button type="submit" class="btn btn-primary btn-lg gap-2 shadow-lg flex-1 sm:flex-none">
                    <i class="fa-sharp fa-save"></i>
                    Save All Changes
                </button>
            </div>
        </div>
    </div>
</div>
```

### Field-Level Save Indicators
```blade
<div class="relative">
    <input type="text" name="field_name" class="input input-bordered w-full auto-save" data-field="field_name">
    <span class="saving-indicator absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
        <i class="fa-sharp fa-spinner fa-spin text-primary"></i>
    </span>
</div>
```

### Card-Level Save Indicators
```blade
<div class="card bg-base-100 shadow-md">
    <div class="bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b border-base-300/50">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
                <div class="avatar avatar-placeholder">
                    <div class="bg-primary text-primary-content w-8 rounded-lg">
                        <i class="fa-sharp fa-icon text-sm"></i>
                    </div>
                </div>
                <h4 class="text-lg font-semibold text-base-content">Card Title</h4>
            </div>
            <div class="card-saving-indicator hidden">
                <i class="fa-sharp fa-spinner fa-spin text-primary"></i>
            </div>
        </div>
    </div>
    <div class="p-6">
        <!-- Card content -->
    </div>
</div>
```

### Auto-Save Status Messages
- **Default**: "Changes will save automatically" (text-base-content)
- **Saving**: "Saving Changes..." (text-info)
- **Success**: "All changes saved" (text-success)
- **Error**: "Error saving changes" (text-error)

### Auto-Save Accessibility Features
- **Manual Save Button**: Always provide a manual save button for accessibility
- **Status Announcements**: Use clear status messages that screen readers can announce
- **Visual Indicators**: Provide both text and icon indicators for save states
- **Error Handling**: Show clear error messages with retry options

### Auto-Save Implementation Guidelines
- **Debounce Timing**: 500ms for text inputs, 1000ms for rich text editors
- **Immediate Save**: Radio buttons and checkboxes save immediately
- **Error Recovery**: Show errors prominently and allow manual retry
- **Status Reset**: Reset status messages after 3 seconds (success) or 5 seconds (error)
- **Card Indicators**: Show spinning indicators in card headers when fields in that card are saving
- **Semantic Colors**: Use card's semantic color for the card-level saving indicator


