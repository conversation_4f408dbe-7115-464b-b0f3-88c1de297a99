# Activity Logging System

The Activity Logging System provides comprehensive tracking of events across different components in the application. It can store logs of create, update, delete, view, and custom events with full context including user information, IP addresses, and detailed change tracking.

## Features

- **Generic Design**: Works with any Eloquent model
- **Flexible Data Storage**: Supports both text descriptions and JSON data
- **Automatic Logging**: Automatic tracking of CRUD operations via trait
- **Manual Logging**: Custom event logging for specific business logic
- **User Context**: Tracks which user performed the action
- **Request Context**: Captures IP address and user agent
- **Change Tracking**: Detailed before/after values for updates (only changed fields)
- **Smart View Logging**: View events only logged if user hasn't viewed item in last 15 minutes
- **Smart Auto-Save Consolidation**: Auto-save changes are consolidated to prevent cluttering logs with intermediate typing states
- **Filtering**: Filter logs by component, user, event type, and date range
- **Compact Widget**: Condensed activity display for model detail pages

## Database Structure

The `activity_logs` table stores all activity data:

- `loggable_type` & `loggable_id`: Polymorphic relationship to any model
- `user_id`: User who performed the action (nullable for system actions)
- `event_type`: Type of event (created, updated, deleted, viewed, custom)
- `component`: Component name (customers, inventory, etc.)
- `description`: Human-readable description
- `old_values` & `new_values`: JSON fields storing change data
- `additional_data`: JSON field for custom event data
- `ip_address` & `user_agent`: Request context
- `created_at` & `updated_at`: Timestamps

## Quick Start

### 1. Add Logging to a Model

Add the `LogsActivity` trait to any model you want to track:

```php
use App\Traits\LogsActivity;

class Customer extends Model
{
    use LogsActivity;
    
    // Optional: Customize which events to log
    protected $loggedEvents = ['created', 'updated', 'deleted', 'restored'];
    
    // Optional: Exclude sensitive fields from logs
    protected $excludeFromLogs = ['password', 'remember_token'];
}
```

### 2. Automatic Logging

Once the trait is added, CRUD operations are automatically logged:

```php
// This will automatically create a "created" log entry
$customer = Customer::create([
    'name' => 'John Doe',
    'email' => '<EMAIL>'
]);

// This will automatically create an "updated" log entry with old/new values
$customer->update(['phone' => '************']);

// This will automatically create a "deleted" log entry
$customer->delete();
```

### 3. Manual Logging

For custom events, use the manual logging methods:

```php
// Log a custom event
$customer->logActivity('contract_signed', 'Customer signed service agreement', [
    'contract_id' => 123,
    'contract_value' => 5000
]);

// Or use the service directly
app(ActivityLoggerService::class)->logCustom(
    component: 'customers',
    eventType: 'payment_received',
    description: 'Payment received for invoice #456',
    model: $customer,
    additionalData: ['amount' => 250.00, 'method' => 'credit_card']
);
```

### 4. View Logs

Display logs for a specific model:

```php
// In a controller
$logs = $customer->recentActivityLogs(20);

// Or use the service
$logs = app(ActivityLoggerService::class)->getLogsForModel($customer, 50);
```

Use the activity log widget in Blade templates:

```blade
<!-- Default: shows last 10 events -->
<x-activity-log-widget :model="$customer" />

<!-- Custom limit -->
<x-activity-log-widget :model="$customer" :limit="20" />
```

## Advanced Usage

### Custom Event Types

Define custom event types for your business logic:

```php
// Log when a customer views a product
$customer->logActivity('product_viewed', "Viewed product: {$product->name}", [
    'product_id' => $product->id,
    'category' => $product->category,
    'price' => $product->price
]);

// Log when a certificate is generated
app(ActivityLoggerService::class)->logCustom(
    component: 'certificates',
    eventType: 'pdf_generated',
    description: "PDF generated for certificate {$certificate->number}",
    model: $certificate,
    additionalData: ['file_size' => $fileSize, 'generation_time' => $time]
);
```

### Filtering and Searching

The admin interface provides comprehensive filtering:

- Filter by component (customers, inventory, certificates, etc.)
- Filter by user who performed the action
- Filter by event type (created, updated, deleted, etc.)
- Filter by date range
- Search in descriptions

### Export Functionality

Export filtered logs to CSV for analysis or compliance:

```php
// Export all customer logs from the last 30 days
GET /activity-logs/export?component=customer&date_from=2024-05-01
```

## Customization

### Model-Specific Configuration

Customize logging behavior per model:

```php
class Invoice extends Model
{
    use LogsActivity;
    
    // Only log certain events
    protected $loggedEvents = ['created', 'updated'];
    
    // Exclude sensitive fields
    protected $excludeFromLogs = ['payment_token', 'internal_notes'];
    
    // Custom component name
    protected function getActivityLogComponent(): string
    {
        return 'billing';
    }
    
    // Custom event filtering
    protected function shouldLogActivity(string $event): bool
    {
        // Don't log updates if only timestamps changed
        if ($event === 'updated' && $this->isDirty(['updated_at'])) {
            return false;
        }
        return parent::shouldLogActivity($event);
    }
}
```

### Service Integration

Integrate with other services:

```php
class PaymentService
{
    public function processPayment($invoice, $amount)
    {
        // Process payment...
        
        // Log the payment
        app(ActivityLoggerService::class)->logCustom(
            component: 'payments',
            eventType: 'payment_processed',
            description: "Payment of ${$amount} processed for invoice #{$invoice->id}",
            model: $invoice,
            additionalData: [
                'amount' => $amount,
                'gateway' => 'stripe',
                'transaction_id' => $transactionId
            ]
        );
    }
}
```

## Admin Interface

Access the activity logs admin interface at `/activity-logs` (requires `view_activity_logs` permission).

Features:
- Paginated log listing with filters
- Detailed view of individual log entries
- Export functionality
- Statistics and analytics
- Real-time activity monitoring

## Performance Considerations

- Logs are stored in a separate table to avoid impacting main application performance
- Indexes are provided for common query patterns
- Consider archiving old logs periodically for large applications
- The system is designed to be non-blocking - logging failures won't affect main application flow

### Performance Optimizations

- **Smart View Logging**: View events are only logged if the user hasn't viewed the same item in the last 15 minutes, preventing excessive view logs
- **Efficient Change Tracking**: Only changed fields are stored in update logs, not the entire model state
- **Smart Auto-Save Consolidation**: Auto-save operations within a 5-minute window are consolidated to prevent excessive logging during typing
- **Compact Widget**: Activity widget shows only the last 10 events by default to reduce page load time
- **Selective Field Logging**: Sensitive or irrelevant fields can be excluded from logs

### Smart Auto-Save Consolidation

The system automatically detects auto-save operations and consolidates them intelligently:

**Example Scenario**: User changes phone number from "1234567890" to "5557416518"
- Without consolidation: Multiple logs for "1234567890" → "5557", "5557" → "5557416", "5557416" → "5557416518"
- With consolidation: Single log for "1234567890" → "5557416518"

**How it works**:
1. Auto-save operations are detected via route names containing "auto-save" or the `X-Auto-Save` header
2. When an auto-save update occurs, the system looks for recent update logs (within 5 minutes) by the same user on the same model
3. If a recent log is found and the changes can be consolidated (same fields, progressive values), the existing log is updated instead of creating a new one
4. The original old value is preserved while the new value and timestamp are updated

**Consolidation Rules**:
- Changes must be on the same field(s)
- Changes must be by the same user within 5 minutes
- New values must be a logical continuation (e.g., progressive typing: "555" → "5557416518")
- Manual saves always create new log entries

## Security

- User context is automatically captured when available
- IP addresses and user agents are logged for audit trails
- Sensitive fields can be excluded from logging
- Access to logs requires appropriate permissions
- All log data is stored securely in the database

## Integration with Customer Component

The Customer model is already integrated with the logging system:

- Automatic logging of create, update, delete operations
- Manual logging when customers are viewed
- Activity log widget displayed on customer detail pages
- Filtering by customer in the admin interface

This provides a complete audit trail of all customer-related activities in the system.

## Chain of Custody Logging

The Certificate and Device models are fully integrated with the activity logging system to provide comprehensive tracking of chain of custody documents:

### Certificate Logging
- **Automatic logging** of all certificate changes (status, dates, methods, locations, notes, customer changes)
- **Smart auto-save consolidation** prevents cluttering logs with intermediate typing states
- **Activity log widget** displayed on certificate edit pages
- **Component name**: `chain_of_custody`

### Device Logging
- **Device additions**: Logs when devices are added with full specifications (serial, type, manufacturer, model)
- **Device modifications**: Tracks changes to device specs, destruction status, and notes
- **Device deletions**: Logs complete device details before removal
- **Destruction tracking**: Records when devices are marked as destroyed/not destroyed
- **Notes updates**: Tracks changes to device notes
- **Dual logging**: Device activities are logged to both the device AND the parent certificate
- **Component name**: `chain_of_custody_devices` (for device logs) and `chain_of_custody` (for certificate logs)

### Logged Device Information
Each device log entry includes:
- Serial number
- Device type (with human-readable labels)
- Manufacturer and model
- Capacity and owner label
- Notes and status
- Destruction status and timestamp
- User who performed the action
- Certificate context (certificate number, customer name)

### Example Log Entries
- "Device added to chain of custody: Serial: ABC123, Type: Hard Disk Drive, Manufacturer: Seagate, Model: ST1000DM003"
- "Device marked as destroyed: Serial: ABC123, Type: Hard Disk Drive, Manufacturer: Seagate, Model: ST1000DM003"
- "Device removed from chain of custody: Serial: ABC123, Type: Hard Disk Drive, Manufacturer: Seagate, Model: ST1000DM003"
- "Certificate status changed from pending to completed"

### Unified Certificate View
Device activities are automatically logged to the parent certificate, so when viewing a certificate's activity log, you'll see:
- Certificate changes (status, dates, methods, locations, notes, customer changes)
- Device additions, modifications, and deletions
- Device destruction status changes
- Device notes updates

This unified view provides a complete audit trail of all chain of custody activities in one place, ensuring compliance and traceability without needing separate device views.
