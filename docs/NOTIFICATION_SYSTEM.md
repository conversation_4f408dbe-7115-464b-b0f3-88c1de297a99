# ETRFlow Notification System Documentation

## Table of Contents
1. [Overview](#overview)
2. [Features](#features)
3. [Architecture](#architecture)
4. [Database Schema](#database-schema)
5. [Installation & Setup](#installation--setup)
6. [Usage Examples](#usage-examples)
7. [API Reference](#api-reference)
8. [Frontend Integration](#frontend-integration)
9. [Permissions](#permissions)
10. [Customization](#customization)
11. [Best Practices](#best-practices)
12. [Troubleshooting](#troubleshooting)
13. [Maintenance](#maintenance)

## Overview

The ETRFlow notification system is a comprehensive, real-time notification platform that allows administrators and the system to send targeted notifications to users. Built with Laravel and integrated with the existing permission system, it provides a seamless way to communicate important information to users across desktop and mobile interfaces.

### Key Capabilities
- Send notifications to all users, specific user groups, or individual users
- Multiple urgency levels with distinct visual styling
- Optional auto-expiry and dismissal functionality
- Mobile-optimized interface with swipe gestures
- Real-time updates without page refresh
- Rich notification content with optional action links
- Comprehensive management interface for administrators

## Features

### Core Features
- **Multiple Target Types**: Send to all users, specific user groups, or individual users
- **Urgency Levels**: Low, Normal, High, Critical with different styling and icons
- **Auto-Expiry**: Optional expiration dates for automatic cleanup
- **Dismissal Options**: Users can dismiss notifications or auto-dismiss when read
- **Link Support**: Optional action links within notifications
- **Permission-based Management**: Role-based access to notification features

### User Experience Features
- **Real-time Updates**: Polling-based updates every 30 seconds
- **Toast Notifications**: Pop-up notifications for new items
- **Mobile Support**: Full mobile interface with swipe-to-dismiss
- **Badge Indicators**: Unread count badges on notification buttons
- **Responsive Design**: Optimized for all screen sizes

### Administrative Features
- **Management Interface**: Full CRUD operations for notifications
- **Bulk Operations**: Dismiss all notifications functionality
- **Analytics**: View notification statistics and engagement
- **Cleanup Tools**: Automated removal of expired notifications

## Architecture

The notification system follows a service-oriented architecture with clear separation of concerns:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Controllers   │    │   Services      │
│                 │    │                 │    │                 │
│ • Header Button │◄──►│ Notification    │◄──►│ Notification    │
│ • Mobile Dock   │    │ Controller      │    │ Service         │
│ • Toast System  │    │                 │    │                 │
│ • Widget        │    │ • API Endpoints │    │ • Business      │
└─────────────────┘    │ • CRUD Ops      │    │   Logic         │
                       │ • Validation    │    │ • Targeting     │
                       └─────────────────┘    │ • Cleanup       │
                                              └─────────────────┘
                                                       │
                                              ┌─────────────────┐
                                              │   Models        │
                                              │                 │
                                              │ • Notification  │
                                              │ • Relationships │
                                              │ • Scopes        │
                                              │ • Helpers       │
                                              └─────────────────┘
```

### Component Overview
- **Models**: Data layer with relationships and business logic
- **Services**: Core business logic and notification operations
- **Controllers**: HTTP request handling and API endpoints
- **Helpers**: Static utility methods for common operations
- **Components**: Reusable Blade components for UI
- **Views**: Admin interfaces using x-app-layout structure

## Database Schema

### Notifications Table (`notifications`)
```sql
CREATE TABLE notifications (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    urgency ENUM('low', 'normal', 'high', 'critical') DEFAULT 'normal',
    target_type ENUM('all_users', 'user_group', 'specific_users') DEFAULT 'all_users',
    target_ids JSON NULL,
    link_url VARCHAR(255) NULL,
    link_text VARCHAR(255) NULL,
    expires_at TIMESTAMP NULL,
    is_dismissible BOOLEAN DEFAULT TRUE,
    auto_dismiss BOOLEAN DEFAULT FALSE,
    created_by BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_target_urgency (target_type, urgency),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
);
```

### Notification User Pivot Table (`notification_user`)
```sql
CREATE TABLE notification_user (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    notification_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    read_at TIMESTAMP NULL,
    dismissed_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_notification_user (notification_id, user_id),
    INDEX idx_read_at (read_at),
    INDEX idx_dismissed_at (dismissed_at)
);
```

### Field Descriptions

#### Notifications Table
| Field | Type | Description |
|-------|------|-------------|
| `id` | BIGINT | Primary key |
| `title` | VARCHAR(255) | Notification title (required) |
| `message` | TEXT | Notification content (required) |
| `urgency` | ENUM | Priority level: low, normal, high, critical |
| `target_type` | ENUM | Who receives it: all_users, user_group, specific_users |
| `target_ids` | JSON | Array of user/group IDs (null for all_users) |
| `link_url` | VARCHAR(255) | Optional action URL |
| `link_text` | VARCHAR(255) | Text for action link |
| `expires_at` | TIMESTAMP | Optional expiration date |
| `is_dismissible` | BOOLEAN | Can users dismiss this notification |
| `auto_dismiss` | BOOLEAN | Auto-dismiss when marked as read |
| `created_by` | BIGINT | User who created the notification |

#### Notification User Pivot Table
| Field | Type | Description |
|-------|------|-------------|
| `notification_id` | BIGINT | Reference to notification |
| `user_id` | BIGINT | Reference to user |
| `read_at` | TIMESTAMP | When user read the notification |
| `dismissed_at` | TIMESTAMP | When user dismissed the notification |

## Installation & Setup

### 1. Database Migration
The notification system requires two database tables. Run the migration:

```bash
php artisan migrate
```

### 2. Permissions Setup
Sync the notification permissions with your existing permission system:

```bash
php artisan permissions:sync
```

This adds the following permissions:
- `view_notifications`: View notification management interface
- `create_notifications`: Create and send notifications
- `manage_notifications`: Full management (view, edit, delete)
- `send_to_all_users`: Send notifications to all users
- `send_to_user_groups`: Send notifications to user groups

### 3. Service Registration
The `NotificationService` is automatically registered in `AppServiceProvider`. No additional setup required.

### 4. Frontend Integration
The notification system is automatically included in the main layout via the `x-notification-system` component.

### 5. Cleanup Command (Optional)
Add the cleanup command to your scheduler in `app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule)
{
    $schedule->command('notifications:cleanup')->daily();
}
```

## Core Components

### Models
- **`App\Models\Notification`**: Main notification model
  - Relationships to users and creators
  - Scopes for filtering (active, by urgency, for user)
  - Helper methods for read/dismiss status
  - Urgency styling and icon methods
- **`App\Models\NotificationConfig`**: Centralized notification configuration
  - Polymorphic model that can be attached to any model (forms, tasks, etc.)
  - Stores notification target configurations and preferences
  - Supports multiple event types per model

### Services
- **`App\Services\NotificationService`**: Core business logic
  - Send notifications to different target types
  - Retrieve user notifications with filtering
  - Mark notifications as read/dismissed
  - Cleanup expired notifications
  - Utility methods for forms and counts
- **`App\Services\NotificationConfigService`**: Centralized configuration management
  - Save and manage notification configurations from form data
  - Send notifications based on stored configurations
  - Provide default notification templates and messages

### Controllers
- **`App\Http\Controllers\NotificationController`**: HTTP request handling
  - Management interface (index, create, store, destroy)
  - API endpoints for frontend integration
  - Validation and error handling
  - Permission-based access control

### Helpers
- **`App\Helpers\NotificationHelper`**: Static utility methods
  - Simplified notification sending
  - Common notification patterns (welcome, alerts, etc.)
  - Quick access methods for different scenarios

### Traits
- **`App\Traits\HasNotificationConfig`**: Reusable notification configuration
  - Add to any model to enable centralized notification configuration
  - Provides standardized methods for managing notification settings
  - Eliminates need to add notification fields to individual models

### Views (using x-app-layout)
- **`resources/views/notifications/index.blade.php`**: Management interface
  - List all notifications with filtering
  - Pagination and search capabilities
  - Delete and manage operations
- **`resources/views/notifications/create.blade.php`**: Creation form
  - Target selection (users, groups, all)
  - Urgency and expiry settings
  - Link configuration

### Components
- **`resources/views/components/notification-system.blade.php`**: Global system
  - Real-time polling for new notifications
  - Toast notification display
  - Global notification count updates
- **`resources/views/components/notification-widget.blade.php`**: Dashboard widget
  - Recent notifications display
  - Quick actions (read, dismiss)
  - Configurable display options
- **`resources/views/components/notification-config.blade.php`**: Centralized configuration component
  - Reusable notification target selection interface
  - Supports user groups, specific users, and all users targeting
  - Advanced configuration options (urgency, expiration, custom messages)
  - Can be embedded in any form for consistent notification setup

## Centralized Notification Configuration

### Overview

The ETRFlow notification system now includes a centralized configuration system that eliminates the need to add notification fields to every model. Instead of adding notification-specific fields to forms, tasks, invoices, etc., you can use the reusable `NotificationConfig` model and `HasNotificationConfig` trait.

### Benefits

- **Reusable**: One notification configuration system works across all models
- **Consistent**: Standardized interface for notification setup
- **Maintainable**: Changes to notification logic only need to be made in one place
- **Flexible**: Supports multiple event types per model with different configurations
- **Scalable**: Easy to add new notification types without database changes

### Quick Setup

1. **Add the trait to your model:**
```php
use App\Traits\HasNotificationConfig;

class YourModel extends Model
{
    use HasNotificationConfig;

    // Your existing model code...
}
```

2. **Use the notification configuration component in your forms:**
```blade
<x-notification-config
    event-type="your_event_type"
    event-label="Your Event Label"
    :user-groups="$userGroups"
    :users="$users"
    field-prefix="notification_config"
    :show-advanced="true"
/>
```

3. **Handle form submission in your controller:**
```php
// In your controller's store/update method
if (isset($validated['notification_config'])) {
    $notificationConfigService = app(\App\Services\NotificationConfigService::class);
    $notificationConfigService->saveConfigurationsFromFormData($model, $validated['notification_config']);
}
```

4. **Send notifications when events occur:**
```php
// When your event happens (e.g., form submitted, task created)
$notificationService = app(\App\Services\NotificationService::class);
$notificationService->sendConfiguredNotifications(
    $model,
    'your_event_type',
    ['model' => $model], // Template data
    'Default Title',
    'Default message',
    route('your.route', $model),
    'View Item'
);
```

### Available Event Types

The system comes with predefined event types:
- `form_submitted`: When forms are submitted
- `task_created`: When tasks are created
- `task_assigned`: When tasks are assigned
- `invoice_created`: When invoices are generated
- `pickup_requested`: When pickup requests are made

You can easily add custom event types by defining them in `NotificationConfig::class`.

## Usage Examples

### Centralized Configuration Examples

```php
use App\Models\NotificationConfig;
use App\Services\NotificationConfigService;

// Set up notification configuration for a form
$form->setNotificationConfig(
    NotificationConfig::EVENT_FORM_SUBMITTED,
    NotificationConfig::TARGET_USER_GROUP,
    [$adminGroupId, $managerGroupId],
    [
        'urgency' => NotificationConfig::URGENCY_HIGH,
        'custom_title' => 'Important Form Submitted',
        'custom_message' => 'A high-priority form requires your attention.',
        'expires_after_days' => 7,
    ]
);

// Check if notifications are enabled
if ($form->hasNotificationsEnabledFor(NotificationConfig::EVENT_FORM_SUBMITTED)) {
    // Send notifications when form is submitted
    $notificationService->sendConfiguredNotifications(
        $form,
        NotificationConfig::EVENT_FORM_SUBMITTED,
        ['form' => $form, 'submission' => $submission]
    );
}

// Get notification configuration summary
$summary = $form->getNotificationConfigSummary();
foreach ($summary as $eventType => $config) {
    echo "Event: $eventType, Targets: " . implode(', ', $config['targets']) . "\n";
}
```

### Quick Start with Helper Methods

The `NotificationHelper` class provides the easiest way to send notifications:

```php
use App\Helpers\NotificationHelper;

// Basic notification to all users
NotificationHelper::notifyAllUsers(
    'Welcome to ETRFlow!',
    'Thank you for joining our platform. Explore the features and let us know if you need help.'
);

// High priority notification with link
NotificationHelper::notifyAllUsers(
    'System Maintenance Scheduled',
    'The system will be down for maintenance tonight from 2 AM to 4 AM EST.',
    'high',
    route('maintenance.info'),
    'View Details',
    now()->addDays(1)  // Expires tomorrow
);
```

### Targeting Specific Users

```php
// Send to specific users
$users = User::whereIn('id', [1, 2, 3])->get();
NotificationHelper::notifyUsers(
    $users,
    'Task Assignment',
    'You have been assigned to the Q4 project review.',
    'normal',
    route('projects.show', $project),
    'View Project'
);

// Send to user groups
$adminGroup = UserGroup::where('name', 'Admin')->first();
$managerGroup = UserGroup::where('name', 'Manager')->first();

NotificationHelper::notifyUserGroups(
    [$adminGroup, $managerGroup],
    'Monthly Report Available',
    'The monthly performance report is now available for review.',
    'low',
    route('reports.monthly'),
    'View Report'
);
```

### Common Notification Patterns

```php
// Welcome new user
NotificationHelper::welcomeUser($newUser);

// Admin alerts
NotificationHelper::notifyAdmins(
    'New User Registration',
    "User {$user->name} has registered and requires approval.",
    'normal',
    route('admin.users.show', $user),
    'Review User'
);

// Critical system alerts (non-dismissible)
NotificationHelper::systemAlert(
    'Security Breach Detected',
    'Unauthorized access attempt from IP: *************'
);

// Maintenance notices
NotificationHelper::maintenanceNotice(
    'Scheduled maintenance will occur tonight from 2-4 AM EST.',
    now()->addHours(6),  // Maintenance time
    route('maintenance.info')
);
```

### Advanced Usage with Service

For more control, use the `NotificationService` directly:

```php
use App\Services\NotificationService;

$service = app(NotificationService::class);

// Create notification with all options
$notification = $service->sendToAllUsers(
    title: 'System Update',
    message: 'New features have been added to improve your experience.',
    urgency: 'normal',
    linkUrl: route('features.new'),
    linkText: 'Explore Features',
    expiresAt: now()->addWeeks(2),
    isDismissible: true,
    autoDismiss: false,
    createdBy: auth()->user()
);

// Get user notifications
$notifications = $service->getNotificationsForUser(
    user: $user,
    onlyUnread: true,
    onlyUndismissed: true
);

// Get unread count
$unreadCount = $service->getUnreadCountForUser($user);

// Mark as read/dismissed
$service->markAsRead($notification, $user);
$service->markAsDismissed($notification, $user);

// Dismiss all for user
$dismissedCount = $service->dismissAllForUser($user);
```

### Integration in Controllers

```php
class ProjectController extends Controller
{
    public function store(Request $request)
    {
        $project = Project::create($request->validated());

        // Notify team members
        NotificationHelper::notifyUsers(
            $project->team_members,
            'New Project Created',
            "Project '{$project->name}' has been created and you've been added to the team.",
            'normal',
            route('projects.show', $project),
            'View Project'
        );

        return redirect()->route('projects.index')
            ->with('success', 'Project created and team notified!');
    }
}
```

### Event-Driven Notifications

```php
// In an Event Listener
class SendWelcomeNotification
{
    public function handle(UserRegistered $event)
    {
        NotificationHelper::welcomeUser($event->user);

        // Also notify admins
        NotificationHelper::notifyAdmins(
            'New User Registration',
            "User {$event->user->name} ({$event->user->email}) has registered.",
            'low',
            route('admin.users.show', $event->user),
            'View User'
        );
    }
}
```

## API Reference

### User API Endpoints

All user endpoints require authentication and are available to all authenticated users.

#### Get User Notifications
```http
GET /api/notifications
```

**Parameters:**
- `unread_only` (boolean, optional): Return only unread notifications
- `timestamp` (integer, optional): Return notifications created after this timestamp (for polling)

**Response:**
```json
{
    "notifications": [
        {
            "id": 1,
            "title": "Welcome!",
            "message": "Welcome to our platform.",
            "urgency": "normal",
            "urgency_class": "alert-success",
            "urgency_icon": "fa-check-circle",
            "link_url": "https://app.com/dashboard",
            "link_text": "Get Started",
            "is_dismissible": true,
            "created_at": "2 hours ago",
            "created_at_timestamp": 1640995200000,
            "is_read": false,
            "is_dismissed": false
        }
    ],
    "unread_count": 3
}
```

#### Get Notification Count
```http
GET /api/notifications/count
```

**Response:**
```json
{
    "unread_count": 5
}
```

#### Mark Notification as Read
```http
POST /api/notifications/{id}/read
```

**Response:**
```json
{
    "success": true,
    "message": "Notification marked as read"
}
```

#### Dismiss Notification
```http
POST /api/notifications/{id}/dismiss
```

**Response:**
```json
{
    "success": true,
    "message": "Notification dismissed"
}
```

#### Dismiss All Notifications
```http
POST /api/notifications/dismiss-all
```

**Response:**
```json
{
    "success": true,
    "message": "Dismissed 5 notifications",
    "dismissed_count": 5
}
```

### Management Endpoints

These endpoints require specific permissions.

#### View Management Interface
```http
GET /notifications
```
**Permission Required:** `view_notifications`

#### Create Notification Form
```http
GET /notifications/create
```
**Permission Required:** `create_notifications`

#### Store New Notification
```http
POST /notifications
```
**Permission Required:** `create_notifications`

**Request Body:**
```json
{
    "title": "System Maintenance",
    "message": "The system will be down tonight.",
    "urgency": "high",
    "target_type": "all_users",
    "target_ids": null,
    "link_url": "https://app.com/maintenance",
    "link_text": "Learn More",
    "expires_at": "2024-12-31 23:59:59",
    "is_dismissible": true,
    "auto_dismiss": false
}
```

**Response:**
```json
{
    "success": true,
    "message": "Notification sent successfully",
    "notification": {
        "id": 123,
        "title": "System Maintenance",
        "created_at": "2024-01-01T12:00:00Z"
    }
}
```

#### Delete Notification
```http
DELETE /notifications/{id}
```
**Permission Required:** `manage_notifications`

**Response:**
```json
{
    "success": true,
    "message": "Notification deleted successfully"
}
```

## Frontend Integration

### Desktop Header Integration

The app header includes a notification bell icon with real-time functionality:

**Features:**
- Badge showing unread count
- Dropdown with recent notifications
- Mark as read/dismiss functionality
- Real-time updates via polling
- Click to navigate to linked content

**Implementation:**
The notification button is automatically included in the app header component and connects to the API endpoints for real-time updates.

### Mobile Dock Integration

The mobile dock includes a notification button optimized for touch interfaces:

**Features:**
- Touch-friendly notification button with badge
- Full-screen notification interface
- Swipe-to-dismiss functionality
- Mobile-optimized layout
- Pull-to-refresh capability

**Mobile Gestures:**
- **Tap**: Open notification interface
- **Swipe Left**: Dismiss notification
- **Tap Notification**: Mark as read and navigate to link
- **Tap X**: Dismiss individual notification

### Dashboard Widget

Include the notification widget on any page:

```blade
<x-notification-widget :limit="5" :show-header="true" :show-view-all="true" />
```

**Widget Options:**
- `limit`: Number of notifications to display (default: 5)
- `show-header`: Show widget header with title (default: true)
- `show-view-all`: Show "View All" button (default: true)

### Real-time Updates

The system uses polling every 30 seconds to check for new notifications:

**Features:**
- Automatic badge count updates
- Toast notifications for new items
- Background polling when page is visible
- Efficient timestamp-based filtering
- Graceful error handling

**Toast Notifications:**
New notifications automatically appear as toast messages with:
- Urgency-based styling
- Auto-dismiss after 8 seconds
- Click to navigate to linked content
- Manual dismiss button

### Custom Integration

To integrate notifications in custom components:

```javascript
// Load notifications
fetch('/api/notifications')
    .then(response => response.json())
    .then(data => {
        console.log('Notifications:', data.notifications);
        console.log('Unread count:', data.unread_count);
    });

// Mark as read
fetch(`/api/notifications/${notificationId}/read`, {
    method: 'POST',
    headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        'Accept': 'application/json'
    }
});
```

## Permissions

The notification system integrates with ETRFlow's existing permission system:

| Permission | Description | Access Level |
|------------|-------------|--------------|
| `view_notifications` | View notification management interface | Admin/Manager |
| `create_notifications` | Create and send notifications | Admin/Manager |
| `manage_notifications` | Full management (view, edit, delete) | Admin only |
| `send_to_all_users` | Send notifications to all users | Admin only |
| `send_to_user_groups` | Send notifications to user groups | Admin/Manager |

### Permission Usage

```php
// Check permissions in controllers
if (!auth()->user()->hasPermission('create_notifications')) {
    abort(403, 'Unauthorized');
}

// Check permissions in Blade templates
@perms('view_notifications')
    <a href="{{ route('notifications.index') }}">Manage Notifications</a>
@endperms

// Check permissions in middleware
Route::middleware(['auth', 'permission:create_notifications'])->group(function () {
    Route::post('/notifications', [NotificationController::class, 'store']);
});
```

### Role-based Access

Typical permission assignments:
- **Super Admin**: All notification permissions
- **Admin**: view_notifications, create_notifications, send_to_user_groups
- **Manager**: view_notifications, create_notifications (limited scope)
- **User**: No notification management permissions (can only view their own)

## Frontend Integration

### Desktop Header
The app header includes a notification bell icon with:
- Badge showing unread count
- Dropdown with recent notifications
- Mark as read/dismiss functionality
- Real-time updates

### Mobile Dock
The mobile dock includes:
- Notification button with badge
- Full-screen notification interface
- Swipe-to-dismiss functionality
- Touch-friendly interactions

### Dashboard Widget
Include the notification widget on any page:

```blade
<x-notification-widget :limit="5" />
```

## Real-time Updates

The system uses polling (every 30 seconds) to check for new notifications:
- Shows toast notifications for new items
- Updates badge counts automatically
- Checks when page becomes visible again

## Maintenance

### Cleanup Expired Notifications
Run the cleanup command periodically:

```bash
php artisan notifications:cleanup
```

Consider adding this to your scheduler in `app/Console/Kernel.php`:

```php
$schedule->command('notifications:cleanup')->daily();
```

## Customization

### Urgency Levels and Styling

The notification system supports four urgency levels with distinct visual styling:

| Urgency | DaisyUI Class | Color | FontAwesome Icon | Use Case |
|---------|---------------|-------|------------------|----------|
| `low` | `alert-info` | Blue | `fa-info-circle` | General information, tips |
| `normal` | `alert-success` | Green | `fa-check-circle` | Standard notifications, confirmations |
| `high` | `alert-warning` | Yellow | `fa-exclamation-triangle` | Important updates, warnings |
| `critical` | `alert-error` | Red | `fa-exclamation-circle` | Urgent alerts, system issues |

### Custom Urgency Styling

To customize urgency appearance, modify the `getUrgencyClass()` and `getUrgencyIcon()` methods in the `Notification` model:

```php
// In App\Models\Notification
public function getUrgencyClass(): string
{
    return match ($this->urgency) {
        self::URGENCY_LOW => 'alert-info',
        self::URGENCY_NORMAL => 'alert-success',
        self::URGENCY_HIGH => 'alert-warning',
        self::URGENCY_CRITICAL => 'alert-error',
        default => 'alert-info',
    };
}

public function getUrgencyIcon(): string
{
    return match ($this->urgency) {
        self::URGENCY_LOW => 'fa-info-circle',
        self::URGENCY_NORMAL => 'fa-check-circle',
        self::URGENCY_HIGH => 'fa-exclamation-triangle',
        self::URGENCY_CRITICAL => 'fa-exclamation-circle',
        default => 'fa-info-circle',
    };
}
```

### Custom Notification Templates

Create custom notification templates by extending the base components:

```blade
{{-- resources/views/components/custom-notification-item.blade.php --}}
<div class="notification-item {{ $notification->getUrgencyClass() }} p-4 rounded-lg">
    <div class="flex items-start gap-3">
        <i class="fa-sharp {{ $notification->getUrgencyIcon() }} text-lg"></i>
        <div class="flex-1">
            <h4 class="font-bold">{{ $notification->title }}</h4>
            <p class="text-sm opacity-80">{{ $notification->message }}</p>
            @if($notification->link_url)
                <a href="{{ $notification->link_url }}" class="btn btn-sm mt-2">
                    {{ $notification->link_text ?: 'View' }}
                </a>
            @endif
        </div>
    </div>
</div>
```

### Polling Interval Customization

Modify the polling interval in the notification system component:

```javascript
// In resources/views/components/notification-system.blade.php
// Change from 30 seconds to 60 seconds
setInterval(checkForNewNotifications, 60000);
```

### Custom Notification Sounds

Add sound notifications for different urgency levels:

```javascript
function playNotificationSound(urgency) {
    const sounds = {
        'low': '/sounds/notification-low.mp3',
        'normal': '/sounds/notification-normal.mp3',
        'high': '/sounds/notification-high.mp3',
        'critical': '/sounds/notification-critical.mp3'
    };

    if (sounds[urgency]) {
        const audio = new Audio(sounds[urgency]);
        audio.play().catch(e => console.log('Sound play failed:', e));
    }
}
```

### Custom Target Types

Extend the notification system with custom target types by modifying the enum in the migration and adding corresponding logic:

```php
// Add custom target type
public const TARGET_DEPARTMENT = 'department';

// Update the enum in migration
$table->enum('target_type', [
    'all_users',
    'user_group',
    'specific_users',
    'department'
])->default('all_users');

// Add targeting logic in NotificationService
public function sendToDepartment($departmentId, $title, $message, ...) {
    $users = User::where('department_id', $departmentId)->get();
    return $this->sendToUsers($users, $title, $message, ...);
}
```

## Best Practices

### Using Centralized Notification Configuration

1. **Prefer Centralized Configuration**: Use the `HasNotificationConfig` trait and `notification-config` component instead of adding notification fields to individual models.

   ```php
   // ✅ Good - Use centralized configuration
   use App\Traits\HasNotificationConfig;

   class Task extends Model
   {
       use HasNotificationConfig;
   }

   // ❌ Avoid - Adding notification fields to every model
   class Task extends Model
   {
       protected $fillable = [
           'notification_user_groups',
           'notification_emails',
           'send_notifications',
           // ... other fields
       ];
   }
   ```

2. **Reuse the Notification Component**: Use the same component across all forms for consistency.

   ```blade
   {{-- ✅ Good - Consistent notification interface --}}
   <x-notification-config
       event-type="task_created"
       event-label="Task Creation"
       :user-groups="$userGroups"
       :users="$users"
   />

   {{-- ❌ Avoid - Custom notification fields for each form --}}
   <div class="custom-notification-section">
       <!-- Custom implementation -->
   </div>
   ```

3. **Use Descriptive Event Types**: Choose clear, consistent event type names.

   ```php
   // ✅ Good - Clear event types
   NotificationConfig::EVENT_FORM_SUBMITTED
   NotificationConfig::EVENT_TASK_ASSIGNED
   NotificationConfig::EVENT_INVOICE_CREATED

   // ❌ Avoid - Vague or inconsistent names
   'form_done'
   'task_thing'
   'invoice_made'
   ```

4. **Handle Configuration in Controllers**: Process notification configurations consistently.

   ```php
   // ✅ Good - Consistent handling
   if (isset($validated['notification_config'])) {
       $notificationConfigService = app(\App\Services\NotificationConfigService::class);
       $notificationConfigService->saveConfigurationsFromFormData($model, $validated['notification_config']);
   }
   ```

5. **Provide Default Templates**: Always provide fallback titles and messages.

   ```php
   // ✅ Good - With defaults
   $notificationService->sendConfiguredNotifications(
       $model,
       'event_type',
       $templateData,
       'Default Title',        // Fallback title
       'Default message',      // Fallback message
       $defaultUrl,           // Fallback URL
       'View Item'            // Fallback link text
   );
   ```

### Content Guidelines

1. **Clear and Concise Titles**: Keep titles under 50 characters
   ```php
   // Good
   NotificationHelper::notifyAllUsers('System Maintenance Tonight', '...');

   // Avoid
   NotificationHelper::notifyAllUsers('Important System Maintenance Scheduled for Tonight from 2 AM to 4 AM EST', '...');
   ```

2. **Actionable Messages**: Include clear next steps
   ```php
   // Good
   NotificationHelper::notifyUsers($users,
       'Invoice Requires Approval',
       'Invoice #12345 from Acme Corp needs your approval by Friday.',
       'normal',
       route('invoices.show', $invoice),
       'Review Invoice'
   );
   ```

3. **Appropriate Urgency Levels**:
   - **Low**: Tips, feature announcements, optional updates
   - **Normal**: Standard notifications, task assignments, confirmations
   - **High**: Important deadlines, warnings, required actions
   - **Critical**: System outages, security alerts, urgent issues

### Technical Best Practices

1. **Set Expiration Dates**: Prevent notification buildup
   ```php
   NotificationHelper::notifyAllUsers(
       'Holiday Schedule',
       'Office will be closed December 25-26.',
       'low',
       null,
       null,
       now()->addDays(30) // Expires after holiday
   );
   ```

2. **Use Appropriate Target Types**:
   ```php
   // For system-wide announcements
   NotificationHelper::notifyAllUsers(...);

   // For role-specific information
   NotificationHelper::notifyUserGroups([$adminGroup], ...);

   // For individual actions
   NotificationHelper::notifyUsers([$assignedUser], ...);
   ```

3. **Batch Operations**: For multiple related notifications
   ```php
   // Instead of multiple individual notifications
   $users = $project->team_members;
   NotificationHelper::notifyUsers($users,
       'Project Update',
       "Project {$project->name} has been updated with new requirements."
   );
   ```

4. **Error Handling**: Always handle notification failures gracefully
   ```php
   try {
       NotificationHelper::notifyAllUsers($title, $message);
   } catch (\Exception $e) {
       Log::error('Failed to send notification: ' . $e->getMessage());
       // Continue with application flow
   }
   ```

### Performance Optimization

1. **Regular Cleanup**: Schedule automatic cleanup
   ```bash
   # Add to crontab or Laravel scheduler
   php artisan notifications:cleanup
   ```

2. **Limit Widget Results**: Don't load too many notifications
   ```blade
   <x-notification-widget :limit="5" />
   ```

3. **Efficient Targeting**: Use specific targeting to reduce database load
   ```php
   // More efficient than checking all users
   NotificationHelper::notifyUserGroups([$specificGroup], ...);
   ```

## Security Considerations

### Authentication and Authorization
- **All API endpoints require authentication**
- **Management features require specific permissions**
- **Users can only access their own notifications**
- **Role-based access control for administrative functions**

### Data Protection
- **XSS Prevention**: All user input is properly escaped
- **CSRF Protection**: All POST endpoints include CSRF tokens
- **SQL Injection Prevention**: Using Eloquent ORM and parameterized queries
- **Input Validation**: All form inputs are validated server-side

### Privacy Considerations
- **Targeted Notifications**: Users only see notifications intended for them
- **Audit Trail**: Track who created which notifications
- **Data Retention**: Automatic cleanup of expired notifications
- **Permission Checks**: Verify user permissions before displaying management interfaces

## Migration Guide

### Migrating from Model-Specific Notification Fields

If you have existing models with notification-specific fields (like forms with `notification_emails`, `notification_user_groups`, etc.), you can migrate to the centralized system:

#### Step 1: Add the Trait
```php
use App\Traits\HasNotificationConfig;

class YourModel extends Model
{
    use HasNotificationConfig;

    // Keep existing fields for backward compatibility during migration
}
```

#### Step 2: Create Migration Script
```php
// Create a migration command or script
foreach (YourModel::whereNotNull('notification_user_groups')->get() as $model) {
    if (!empty($model->notification_user_groups)) {
        $model->setNotificationConfig(
            'your_event_type',
            NotificationConfig::TARGET_USER_GROUP,
            $model->notification_user_groups,
            [
                'urgency' => NotificationConfig::URGENCY_NORMAL,
                'is_enabled' => true,
            ]
        );
    }
}
```

#### Step 3: Update Views
Replace custom notification fields with the centralized component:

```blade
{{-- Before --}}
<div class="notification-settings">
    <input type="checkbox" name="enable_notifications">
    <select name="notification_user_groups[]" multiple>
        @foreach($userGroups as $group)
            <option value="{{ $group->id }}">{{ $group->name }}</option>
        @endforeach
    </select>
</div>

{{-- After --}}
<x-notification-config
    event-type="your_event_type"
    event-label="Your Event"
    :config="$model->notificationConfigs->where('event_type', 'your_event_type')->first()"
    :user-groups="$userGroups"
    :users="$users"
/>
```

#### Step 4: Update Controllers
```php
// Before
$validated = $request->validate([
    'notification_user_groups' => 'nullable|array',
    'notification_emails' => 'nullable|array',
    'enable_notifications' => 'boolean',
]);

// After
$validated = $request->validate([
    'notification_config' => 'nullable|array',
    // Keep old fields during transition
    'notification_user_groups' => 'nullable|array',
]);

// Handle both old and new formats
if (isset($validated['notification_config'])) {
    $notificationConfigService->saveConfigurationsFromFormData($model, $validated['notification_config']);
} elseif (isset($validated['notification_user_groups'])) {
    // Handle legacy format
    $model->setNotificationConfig(
        'your_event_type',
        NotificationConfig::TARGET_USER_GROUP,
        $validated['notification_user_groups']
    );
}
```

#### Step 5: Update Notification Sending
```php
// Before
if ($model->enable_notifications && !empty($model->notification_user_groups)) {
    $userGroups = UserGroup::whereIn('id', $model->notification_user_groups)->get();
    NotificationHelper::notifyUserGroups($userGroups, $title, $message);
}

// After
$notificationService->sendConfiguredNotifications(
    $model,
    'your_event_type',
    $templateData,
    $defaultTitle,
    $defaultMessage,
    $defaultUrl,
    $defaultLinkText
);
```

#### Step 6: Remove Legacy Fields
After confirming the migration works, create a migration to remove the old fields:

```php
Schema::table('your_models', function (Blueprint $table) {
    $table->dropColumn([
        'notification_user_groups',
        'notification_emails',
        'enable_notifications'
    ]);
});
```

## Troubleshooting

### Common Issues

#### Notifications Not Appearing
**Symptoms**: Users don't see expected notifications

**Debugging Steps**:
1. Check user permissions and group membership
2. Verify notification targeting (target_type and target_ids)
3. Check if notification has expired
4. Verify notification was created successfully

```php
// Debug notification targeting
$user = User::find(1);
$notifications = app(NotificationService::class)->getNotificationsForUser($user);
dd($notifications); // Check what notifications user should see
```

#### Real-time Updates Not Working
**Symptoms**: New notifications don't appear without page refresh

**Debugging Steps**:
1. Check browser console for JavaScript errors
2. Verify CSRF token is present in page meta tags
3. Check network requests in browser dev tools
4. Ensure polling is not blocked by ad blockers or browser settings

```javascript
// Debug polling in browser console
console.log('CSRF Token:', document.querySelector('meta[name="csrf-token"]'));
```

#### Performance Issues
**Symptoms**: Slow notification loading or high database usage

**Solutions**:
1. Run cleanup command regularly
2. Add database indexes for large datasets
3. Limit notification widget results
4. Monitor notification creation frequency

```sql
-- Check notification table size
SELECT COUNT(*) FROM notifications;
SELECT COUNT(*) FROM notification_user;

-- Check for expired notifications
SELECT COUNT(*) FROM notifications WHERE expires_at < NOW();
```

#### Mobile Gestures Not Working
**Symptoms**: Swipe-to-dismiss not functioning on mobile

**Debugging Steps**:
1. Test on actual mobile devices, not just browser dev tools
2. Check for JavaScript errors in mobile browser
3. Verify touch event handlers are properly attached
4. Ensure CSS doesn't interfere with touch events

### Error Codes and Messages

| Error | Cause | Solution |
|-------|-------|----------|
| 403 Forbidden | Missing permissions | Check user permissions |
| 422 Validation Error | Invalid form data | Verify required fields |
| 500 Server Error | Database or service error | Check logs and database |
| Network Error | API endpoint unreachable | Check routes and middleware |

### Logging and Monitoring

Enable detailed logging for notification operations:

```php
// In config/logging.php
'channels' => [
    'notifications' => [
        'driver' => 'daily',
        'path' => storage_path('logs/notifications.log'),
        'level' => 'info',
    ],
],

// In NotificationService
Log::channel('notifications')->info('Notification sent', [
    'notification_id' => $notification->id,
    'target_type' => $notification->target_type,
    'user_count' => $targetUserCount
]);
```
