# Header and Navigation System Documentation

This document covers the comprehensive navigation system including the new property-based header system and breadcrumbs integration. The system provides consistent styling, predefined actions, and clear navigation hierarchy across all pages.

## System Overview

The navigation system consists of an integrated header system that includes:

1. **Header System**: Property-based header with page titles, icons, and action buttons
2. **Integrated Breadcrumbs**: Hierarchical navigation built directly into the header system

The breadcrumbs are now integrated directly into the header component and display below the action buttons row, providing a seamless navigation experience that is both functional and visually consistent across desktop and mobile devices.

## How the Systems Work Together

### Page Structure
A typical page using the integrated system follows this structure:

```blade
<x-app-layout
    page-title="Edit Inventory Item: {{ $inventory->asset_tag }}"
    page-icon="fa-sharp fa-edit"
    :primary-buttons="[...]"
    :action-buttons="[...]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Inventory', 'route' => 'inventory.index', 'icon' => 'fa-boxes'],
        ['name' => 'Edit Item: ' . $inventory->asset_tag, 'icon' => 'fa-edit']
    ]">

    <div class="py-6 lg:py-8">
        <!-- Page Content -->
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Your page content here -->
        </div>
    </div>
</x-app-layout>
```

### Layout Structure

The application uses a sidebar-based layout with the following structure:

1. **Sidebar**: Fixed left sidebar containing logo and navigation
   - Logo at top (responsive to collapsed/expanded states)
   - Navigation menu items and dropdowns
   - Collapsible on desktop (256px expanded, 64px collapsed)
   - Hidden on mobile with overlay toggle

2. **Header**: Positioned to the right of sidebar
   - Page title and icon for immediate context
   - User account dropdown for profile/logout
   - Responsive positioning based on sidebar state

3. **Action Buttons Row**: Below header, aligned with sidebar
   - Primary buttons (back, edit, view, delete, etc.)
   - Horizontal scrolling on overflow
   - Consistent positioning relative to sidebar

4. **Breadcrumbs Row**: Below action buttons, integrated into header
   - Hierarchical navigation showing current page location
   - Clickable links for navigation
   - Icons for visual context
   - Consistent styling with header system
   - Shows path from root (Dashboard) to current page
   - Each level is clickable except the current page
   - Icons provide visual context for each level

5. **Content Level**: Page-specific content and interactions
   - Forms, data tables, cards, etc.
   - Local navigation within page sections

### Responsive Behavior

**Desktop (lg and above)**:
- Sidebar: Fixed left sidebar (256px expanded, 64px collapsed)
- Header: Positioned to right of sidebar, adjusts width based on sidebar state
- Action buttons: Row below header, aligned with sidebar positioning
- Button size: Small (btn-sm) for clean desktop appearance

**Mobile (below lg)**:
- Sidebar: Hidden by default, slides in from left with overlay
- Header: Full width with mobile toggle button
- Action buttons: Horizontal scrollable row below header
- Button size: Extra small (btn-xs) for optimal touch experience

## Header System - Basic Usage

Instead of using:
```blade
<x-app-layout>
    <x-slot name="header">
        <h2>Page Title</h2>
    </x-slot>
</x-app-layout>
```

Use:
```blade
<x-app-layout page-title="Page Title">
</x-app-layout>
```

## Available Properties

### Page Title
- **Property**: `page-title`
- **Type**: `string`
- **Description**: The main page title displayed in the header

```blade
<x-app-layout page-title="Dashboard">
```

### Page Icon
- **Property**: `page-icon`
- **Type**: `string` (FontAwesome class)
- **Description**: Optional icon that appears to the left of the title

```blade
<x-app-layout 
    page-title="Dashboard"
    page-icon="fa-sharp fa-tachometer-alt">
```

### Primary Buttons
- **Property**: `primary-buttons`
- **Type**: `array`
- **Description**: Predefined action buttons with consistent styling

Available primary button types:
- `back` - Generic back button
- `back-to-index` - Back to index/listing page
- `edit` - Edit button
- `create` - Create new button
- `delete` - Delete button (with confirmation)
- `save` - Save button (submit type)
- `view` - View button
- `duplicate` - Duplicate/clone button
- `pdf` - PDF export button

```blade
<x-app-layout
    page-title="Create New Item"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('items.index'),
            'text' => 'Back to Items'
        ],
        [
            'type' => 'view',
            'route' => 'https://docs.example.com',
            'text' => 'View Documentation',
            'target' => '_blank'
        ]
    ]">
```

### Action Buttons
- **Property**: `action-buttons`
- **Type**: `array`
- **Description**: Custom action buttons

```blade
<x-app-layout 
    page-title="Item Details"
    :action-buttons="[
        [
            'name' => 'Export PDF',
            'route' => route('items.export', $item),
            'icon' => 'fa-sharp fa-file-pdf',
            'class' => 'btn btn-secondary btn-sm gap-2'
        ]
    ]">
```

## Examples

### Simple Page with Icon
```blade
<x-app-layout 
    page-title="Dashboard"
    page-icon="fa-sharp fa-tachometer-alt">
```

### Create Page with Back Button
```blade
<x-app-layout 
    page-title="Create New Inventory Item"
    page-icon="fa-sharp fa-plus"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('inventory.index'),
            'text' => 'Back to Inventory'
        ]
    ]">
```

### Edit Page with Multiple Buttons
```blade
<x-app-layout 
    page-title="Edit Item: {{ $item->name }}"
    page-icon="fa-sharp fa-edit"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('items.index'),
            'text' => 'Back to Items'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'View Item',
            'route' => route('items.show', $item),
            'icon' => 'fa-sharp fa-eye',
            'class' => 'btn btn-info btn-sm gap-2'
        ],
        [
            'name' => 'Delete',
            'route' => route('items.destroy', $item),
            'icon' => 'fa-sharp fa-trash',
            'class' => 'btn btn-error btn-sm gap-2',
            'confirm' => 'Are you sure you want to delete this item?'
        ],
        [
            'name' => 'External Link',
            'route' => 'https://example.com',
            'icon' => 'fa-sharp fa-external-link',
            'class' => 'btn btn-secondary btn-sm gap-2',
            'target' => '_blank'
        ],
        [
            'name' => 'Custom Action',
            'icon' => 'fa-sharp fa-sync',
            'class' => 'btn btn-info btn-sm gap-2',
            'id' => 'custom-action-button',
            'permission' => 'generate_inventory_descriptions',
            'condition' => $item->status === 'active'
        ]
    ]">
```

## Button Configuration Options

### Primary Button Options
- `type` - The predefined button type
- `route` - The URL/route for the button
- `text` - Custom text (overrides default)
- `confirm` - Confirmation message for dangerous actions
- `target` - Link target (e.g., `_blank` for new tab, defaults to same window)
- `permission` - Single permission string or array of permissions (user must have at least one)
- `condition` - Boolean condition that must be true for button to display
- `hidden` - Boolean to explicitly hide the button

### Action Button Options
- `name` - Button text
- `route` - The URL/route for the button (not required if `id` is provided for JavaScript buttons)
- `icon` - FontAwesome icon class
- `class` - CSS classes for styling
- `confirm` - Confirmation message for dangerous actions
- `target` - Link target (e.g., `_blank` for new tab, defaults to same window)
- `id` - Custom ID for the button (creates a `<button>` instead of `<a>` for JavaScript functionality)
- `permission` - Single permission string or array of permissions (user must have at least one)
- `condition` - Boolean condition that must be true for button to display
- `hidden` - Boolean to explicitly hide the button

## Mobile Responsiveness

The header system is fully responsive:

### Desktop (lg and above)
- Page icon: Full size (w-10, text-lg)
- Page title: Full size (text-xl)
- Action buttons: Displayed inline next to the user dropdown
- Button size: Small (btn-sm) for clean desktop appearance

### Mobile (below lg)
- Page icon: Smaller size (w-8, text-sm)
- Page title: Smaller size (text-lg)
- Action buttons: Displayed in a horizontal scrollable row below the main header
- Button size: Extra small (btn-xs) for optimal mobile experience
- Horizontal scrolling: Smooth scrolling with hidden scrollbar when buttons overflow

### Mobile Action Buttons Features
- **Horizontal scrollable**: When buttons exceed viewport width, users can scroll horizontally
- **Hidden scrollbar**: Clean appearance without visible scrollbars
- **Responsive sizing**: Buttons use btn-sm on desktop and btn-xs on mobile regardless of original class
- **Touch-friendly**: Optimized for touch interaction with appropriate button sizing

## Smart Button Class Handling

The system automatically converts button sizes for responsive design:

**Desktop**: Converts to `btn-sm` for clean appearance:
```php
class="{{ str_replace(['btn-xs', 'btn-md', 'btn-lg'], 'btn-sm', $button['class'] ?? 'btn btn-primary btn-sm gap-2') }}"
```

**Mobile**: Converts to `btn-xs` for optimal touch experience:
```php
class="{{ str_replace(['btn-sm', 'btn-md', 'btn-lg'], 'btn-xs', $button['class'] ?? 'btn btn-primary btn-xs gap-1') }} flex-shrink-0"
```

This ensures consistent sizing regardless of the original button class specified in your configuration.

## Permission and Condition System

The header system includes built-in support for permission checks and conditional display logic.

### Permission Checks

Buttons can be automatically hidden based on user permissions:

```blade
<x-app-layout
    page-title="Edit Item"
    :primary-buttons="[
        [
            'type' => 'edit',
            'route' => route('items.edit', $item),
            'permission' => 'edit_inventory_items'  // Single permission
        ],
        [
            'type' => 'delete',
            'route' => route('items.destroy', $item),
            'permission' => ['delete_inventory_items', 'is_admin']  // Multiple permissions (user needs at least one)
        ]
    ]">
```

### Conditional Display

Buttons can be shown/hidden based on dynamic conditions:

```blade
<x-app-layout
    page-title="Item Details"
    :action-buttons="[
        [
            'name' => 'Publish',
            'route' => route('items.publish', $item),
            'condition' => $item->status === 'draft',  // Only show for draft items
            'permission' => 'edit_inventory_items'
        ],
        [
            'name' => 'Generate Report',
            'route' => route('items.report', $item),
            'condition' => $item->data_complete,  // Only show when data is complete
            'hidden' => !$item->is_reportable  // Explicitly hide if not reportable
        ]
    ]">
```

### Using the @perms Directive

You can also use the existing `@perms` directive for more complex permission logic:

```blade
@perms('edit_inventory_items, is_admin')
    <x-app-layout
        page-title="Edit Item"
        :primary-buttons="[
            ['type' => 'edit', 'route' => route('items.edit', $item)]
        ]">
@endperms
```

## Integrated Breadcrumbs System

### Basic Usage

The breadcrumbs are now integrated directly into the header system. Simply pass the breadcrumbs array as a property to the `x-app-layout` component:

```blade
<x-app-layout
    page-title="Your Page Title"
    page-icon="fa-sharp fa-icon"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Inventory', 'route' => 'inventory.index', 'icon' => 'fa-boxes'],
        ['name' => 'Current Page', 'icon' => 'fa-edit']
    ]">
```

### Breadcrumb Item Structure

Each breadcrumb item is an array with the following properties:

- **name** (required): The display text for the breadcrumb
- **route** (optional): The route name or URL to link to. If omitted, the item is not clickable (typically for the current page)
- **icon** (optional): FontAwesome icon class to display before the text

### Common Breadcrumb Patterns

**Dashboard Pages**:
```blade
['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home']
```

**Index/Listing Pages**:
```blade
[
    ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
    ['name' => 'Inventory', 'icon' => 'fa-boxes']
]
```

**Create Pages**:
```blade
[
    ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
    ['name' => 'Inventory', 'route' => 'inventory.index', 'icon' => 'fa-boxes'],
    ['name' => 'Create New Item', 'icon' => 'fa-plus']
]
```

**Edit Pages**:
```blade
[
    ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
    ['name' => 'Inventory', 'route' => 'inventory.index', 'icon' => 'fa-boxes'],
    ['name' => 'Edit Item: ' . $item->name, 'icon' => 'fa-edit']
]
```

**Detail/Show Pages**:
```blade
[
    ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
    ['name' => 'Inventory', 'route' => 'inventory.index', 'icon' => 'fa-boxes'],
    ['name' => $item->name, 'icon' => 'fa-eye']
]
```

### Best Practices

1. **Always start with Dashboard**: Provides consistent root navigation
2. **Use descriptive icons**: Choose icons that clearly represent each level
3. **Keep names concise**: Breadcrumbs should be scannable at a glance
4. **Current page no route**: The final breadcrumb (current page) should not have a route
5. **Dynamic content**: Include relevant identifiers (names, IDs) for specific items

## Integration Examples

### Complete Page Example

Here's a full example showing both header and breadcrumbs working together:

```blade
<x-app-layout
    page-title="Edit Customer: {{ $customer->name }}"
    page-icon="fa-sharp fa-user-edit"
    :primary-buttons="[
        [
            'type' => 'back-to-index',
            'route' => route('customers.index'),
            'text' => 'Back to Customers'
        ]
    ]"
    :action-buttons="[
        [
            'name' => 'View Customer',
            'route' => route('customers.show', $customer),
            'icon' => 'fa-sharp fa-eye',
            'class' => 'btn btn-info btn-sm gap-2'
        ],
        [
            'name' => 'Delete',
            'route' => route('customers.destroy', $customer),
            'icon' => 'fa-sharp fa-trash',
            'class' => 'btn btn-error btn-sm gap-2',
            'confirm' => 'Are you sure you want to delete this customer?'
        ]
    ]"
    :breadcrumbs="[
        ['name' => 'Dashboard', 'route' => 'dashboard', 'icon' => 'fa-home'],
        ['name' => 'Customers', 'route' => 'customers.index', 'icon' => 'fa-users'],
        ['name' => 'Edit: ' . $customer->name, 'icon' => 'fa-user-edit']
    ]">

    <div class="py-6 lg:py-8">
        <!-- Page Content -->
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Your form and content here -->
        </div>
    </div>
</x-app-layout>
```

## Migration from Standalone Breadcrumbs

### Old Pattern (Deprecated)
```blade
<x-app-layout page-title="Page Title">
    <div class="py-6 lg:py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumbs -->
            <x-breadcrumbs :items="[...]" />
        </div>
        <!-- Page Content -->
    </div>
</x-app-layout>
```

### New Pattern (Recommended)
```blade
<x-app-layout
    page-title="Page Title"
    :breadcrumbs="[...]">
    <div class="py-6 lg:py-8">
        <!-- Page Content -->
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Your content here -->
        </div>
    </div>
</x-app-layout>
```

## Backward Compatibility

The old `x-slot name="header"` pattern is still supported for existing pages, but new pages should use the property-based system for consistency. The standalone `<x-breadcrumbs>` component is still available but should be migrated to the integrated system for better visual consistency.
