# ETRFlow2 Timezone Handling Documentation

## Overview

This document outlines the simplified timezone handling patterns used throughout the ETRFlow2 application. The system uses <PERSON><PERSON>'s built-in timezone support to ensure consistency and reduce complexity.

## Core Principles

### 1. **Database Storage**
- **All datetime fields are stored in UTC** in the database
- Use <PERSON><PERSON>'s `datetime` migration type for all datetime columns
- <PERSON><PERSON>'s Eloquent automatically handles UTC conversion during storage

### 2. **Configuration**
- **Application Timezone**: Set via `APP_TIMEZONE` in the `.env` file
- **Accessing Timezone**: Use `config('app.timezone')` throughout the application
- **GlobalConfig Method**: `GlobalConfig::getTimeZone()` now returns `config('app.timezone')` for backward compatibility

### 3. **Display**
- **Backend Views**: <PERSON><PERSON> automatically converts UTC to app timezone for display
- **Frontend**: Display times in user-friendly 12-hour format with AM/PM
- **API Responses**: Return UTC ISO strings with proper timezone indicators

### 4. **Key Benefits**
- **Simplicity**: Let <PERSON><PERSON> handle all timezone conversions automatically
- **Consistency**: Single source of truth for timezone configuration
- **No Manual Conversion**: Remove all manual timezone conversion code

## System-Specific Patterns

### Calendar/Event System

#### **Model Configuration**
```php
// Event.php
protected $casts = [
    'start_date' => 'datetime',
    'end_date' => 'datetime',
];

// Custom serialization for API responses
protected function serializeDate(\DateTimeInterface $date)
{
    return $date->format('Y-m-d\TH:i:s.u\Z'); // UTC with Z suffix
}
```

#### **Controller Processing (EventController.php)**
```php
// Laravel handles timezone conversion automatically
// The dates come from the client in ISO format
$startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
$endDate = $request->end_date ? Carbon::parse($request->end_date) : null;
```

#### **Frontend JavaScript**
```javascript
// Events send datetime in UTC ISO format with Z suffix
// Example: "2025-01-15T17:00:00.000Z"
```

### Pickup Request System

#### **Guest Interface (GuestPickupController.php)**
```php
// Laravel handles timezone conversion automatically
// The date comes from the frontend in UTC ISO format
// No manual conversion needed - Laravel's datetime casting handles it
```

#### **Admin Interface (PickupRequestController.php)**
```php
// Laravel handles timezone conversion automatically
// Admin interface sends datetime in local format
// Laravel's datetime casting handles UTC conversion for storage
```

#### **Frontend JavaScript Patterns**

**Guest Interface (pickup-request.js)**:
```javascript
// Time slots are received from backend in UTC ISO format
// Example: slot.dataset.datetime = "2025-01-15T17:00:00.000Z"
preferredPickupDateInput.value = slot.dataset.datetime; // Submits UTC ISO string
```

**Admin Interface (pickup-time-selector.blade.php)**:
```javascript
// Date and time combined into local datetime string
function updateHiddenInput() {
    const date = dateInput.value;  // YYYY-MM-DD
    const time = timeInput.value;  // HH:MM
    
    if (date && time) {
        const datetime = date + 'T' + time + ':00'; // YYYY-MM-DDTHH:MM:SS
        hiddenInput.value = datetime; // Local timezone format
    }
}
```

### Timesheet/TimeClock System

#### **Model Configuration**
```php
// TimePunch.php
protected $casts = [
    'punch_time' => 'datetime',
];

// TimeCard.php
protected $casts = [
    'date' => 'date',
    'created_at' => 'datetime',
    'updated_at' => 'datetime',
];
```

#### **Controller Patterns**
```php
// Uses Carbon::now() for current timestamp (respects app timezone)
$punchTime = $punchTime ?: Carbon::now();

// Uses GlobalConfig::getTimeZone() for timezone-aware processing
$timezone = GlobalConfig::getTimeZone();
```

#### **Frontend (Flatpickr Configuration)**
```javascript
// Timecard and punch editing
flatpickr(timePicker, {
    enableTime: true,
    noCalendar: true,
    dateFormat: isMobile ? "h:i K" : "h:i:S K", // 12-hour with AM/PM
    time_24hr: false,
    enableSeconds: !isMobile,
});
```

## Display Patterns

### Backend Views

**Simplified Pattern for All Systems**:
```blade
{{-- Laravel automatically converts UTC to app timezone for display --}}
{{ $model->datetime_field ? $model->datetime_field->format('M j, Y g:i A') : 'Not specified' }}
```

**Examples**:
```blade
{{-- Pickup Request Display --}}
{{ $request->preferred_pickup_date ? $request->preferred_pickup_date->format('M j, Y g:i A') : 'Not specified' }}

{{-- Event Display --}}
{{ $event->start_date->format('l, F j, Y \a\t g:i A') }}

{{-- Time Punch Display --}}
{{ $punch->punch_time->format('g:i:s A') }}
```

### Form Value Preparation

**For Edit Forms**:
```blade
{{-- Laravel handles timezone conversion automatically --}}
:value="old('preferred_pickup_date', $pickupRequest->preferred_pickup_date ? $pickupRequest->preferred_pickup_date->format('Y-m-d\TH:i:s') : '')"
```

## Input Processing Flowcharts

### Guest Interface Flow
```
User selects 9:00 AM local time
    ↓
Frontend displays 9:00 AM (converts UTC to local for display)
    ↓
Frontend submits "2025-01-15T17:00:00.000Z" (UTC ISO with Z suffix)
    ↓
Backend removes Z suffix and parses as UTC
    ↓
Backend applies site timezone conversion
    ↓
Laravel stores in UTC in database
    ↓
Backend displays 9:00 AM (converts UTC to site timezone)
```

### Admin Interface Flow
```
User inputs 9:00 AM in date/time fields
    ↓
Frontend combines to "2025-01-15T09:00:00" (local datetime string)
    ↓
Backend parses in site timezone
    ↓
Laravel stores in UTC in database
    ↓
Backend displays 9:00 AM (converts UTC to site timezone)
```

## Migration Patterns

### Database Schema
```php
// Standard datetime column
$table->datetime('preferred_pickup_date')->nullable();
$table->datetime('start_date');
$table->datetime('end_date');
$table->datetime('punch_time');
```

### Model Casting
```php
protected $casts = [
    'preferred_pickup_date' => 'datetime',
    'start_date' => 'datetime',
    'end_date' => 'datetime',
    'punch_time' => 'datetime',
];
```

## API Response Patterns

### Standard API Response Format
```php
// Convert to UTC for API responses
return response()->json([
    'datetime_field' => $model->datetime_field->setTimezone(new \DateTimeZone('UTC'))->format('c'),
    // or using the Event model's custom serialization
    'datetime_field' => $model->datetime_field->format('Y-m-d\TH:i:s.u\Z')
]);
```

## Common Pitfalls and Solutions

### ❌ **Double UTC Conversion**
```php
// WRONG - causes double conversion
$validated['datetime'] = Carbon::parse($request->datetime)->utc();
```

```php
// CORRECT - parse with proper timezone handling
$timezone = \App\Models\GlobalConfig::getTimeZone();
$dateStr = str_replace('Z', '', $request->datetime);
$datetime = new \DateTime($dateStr, new \DateTimeZone('UTC'));
$datetime->setTimezone(new \DateTimeZone($timezone));
$validated['datetime'] = Carbon::instance($datetime);
```

### ❌ **Inconsistent Display Timezone**
```php
// WRONG - displays in UTC
{{ $model->datetime_field->format('M j, Y g:i A') }}
```

```php
// CORRECT - displays in site timezone
{{ $model->datetime_field->setTimezone(\App\Models\GlobalConfig::getTimeZone())->format('M j, Y g:i A') }}
```

### ❌ **Mixed Timezone Sources**
```php
// INCONSISTENT - mixing different timezone sources
$timezone1 = config('app.timezone');
$timezone2 = GlobalConfig::getTimeZone();
```

```php
// CONSISTENT - always use GlobalConfig
$timezone = \App\Models\GlobalConfig::getTimeZone();
```

## Testing Considerations

### Timezone Testing
- Test with different site timezone configurations
- Verify UTC storage in database
- Test frontend display in multiple timezones
- Validate API responses include proper timezone information

### Common Test Cases
1. **Storage Test**: Verify datetime is stored in UTC regardless of input timezone
2. **Display Test**: Verify datetime displays in site timezone
3. **Round-trip Test**: Input → Storage → Display should maintain user's intended time
4. **API Test**: Verify API responses include proper timezone indicators

## Migration Guide (Simplified Timezone Handling)

### Step 1: Update Environment Configuration
```bash
# In .env file, ensure timezone is set correctly
APP_TIMEZONE=America/Denver  # or America/Phoenix for MST without DST
```

### Step 2: Update GlobalConfig Method
The `GlobalConfig::getTimeZone()` method now returns `config('app.timezone')` for consistency with Laravel's built-in timezone handling.

### Step 3: Remove Manual Timezone Conversions
- **Controllers**: Remove all manual timezone conversion code
- **Views**: Remove `->setTimezone()` calls when displaying dates
- **Let Laravel Handle It**: Trust Laravel's automatic timezone conversion

### Impact on Existing Data
- **No Data Migration Required**: All existing UTC data remains unchanged
- **Display Automatically Corrects**: Laravel converts UTC to app timezone for display
- **Form Inputs Work**: Laravel handles conversion from app timezone to UTC for storage

### Before and After Examples

**Before (Complex):**
```php
// Controller
$timezone = GlobalConfig::getTimeZone();
$dateStr = str_replace('Z', '', $request->date);
$date = new \DateTime($dateStr, new \DateTimeZone('UTC'));
$date->setTimezone(new \DateTimeZone($timezone));

// View
{{ $model->date->setTimezone(\App\Models\GlobalConfig::getTimeZone())->format('M j, Y g:i A') }}
```

**After (Simple):**
```php
// Controller
$date = Carbon::parse($request->date);

// View
{{ $model->date->format('M j, Y g:i A') }}
```

## Troubleshooting

### Common Issues
1. **Times off by timezone offset**: Check `APP_TIMEZONE` in `.env` file
2. **Inconsistent display times**: Ensure all datetime fields use Laravel's `datetime` cast
3. **API timezone mismatches**: Use `->toISOString()` for consistent UTC output

### Debug Steps
1. Verify `config('app.timezone')` returns expected timezone
2. Check model `$casts` array includes datetime fields
3. Confirm database stores UTC timestamps (they should look like UTC times)
4. Remove any manual timezone conversion code that might interfere

## Best Practices

1. **Let Laravel handle timezone conversions** - Don't manually convert timezones
2. **Use Carbon for date parsing** - `Carbon::parse()` respects app timezone
3. **Keep datetime casts in models** - Ensures consistent behavior
4. **Use ISO format for APIs** - `->toISOString()` for UTC consistency
5. **Set APP_TIMEZONE correctly** - This is the single source of truth
6. **Test thoroughly** with different timezone configurations
7. **Document any deviations** from these patterns with clear justification

## Summary

The ETRFlow2 application uses a three-layer timezone approach:

1. **Database Layer**: UTC storage (consistent across all systems)
2. **Processing Layer**: Site timezone aware (configurable via GlobalConfig)
3. **Display Layer**: User-friendly formatting in site timezone

This approach ensures data consistency while providing proper timezone handling for users in different locations. All new development should follow these established patterns to maintain system consistency.