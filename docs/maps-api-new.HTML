Autocomplete requests
An autocomplete request takes a query input string and returns a list of place predictions. To make an autocomplete request, call fetchAutocompleteSuggestions() and pass a request with the needed properties. The input property contains the string to search; in a typical application this value would be updated as the user types a query. The request should include a sessionToken, which is used for billing purposes.

The following snippet shows creating a request body and adding a session token, then calling fetchAutocompleteSuggestions() to get a list of PlacePredictions.


// Add an initial request body.
let request = {
  input: "Tadi",
  locationRestriction: {
    west: -122.44,
    north: 37.8,
    east: -122.39,
    south: 37.78,
  },
  origin: { lat: 37.7893, lng: -122.4039 },
  includedPrimaryTypes: ["restaurant"],
  language: "en-US",
  region: "us",
};
// Create a session token.
const token = new AutocompleteSessionToken();

// Add the token to the request.
// @ts-ignore
request.sessionToken = token;
Constrain Autocomplete predictions
By default, Place Autocomplete presents all place types, biased for predictions near the user's location, and fetches all available data fields for the user's selected place. Set Place Autocomplete options to present more relevant predictions, by restricting or biasing results.

Restricting results causes the Autocomplete widget to ignore any results that are outside of the restriction area. A common practice is to restrict results to the map bounds. Biasing results makes the Autocomplete widget show results within the specified area, but some matches may be outside of that area.

Use the origin property to specify the origin point from which to calculate geodesic distance to the destination. If this value is omitted, distance is not returned.

Use the includedPrimaryTypes property to specify up to five place types. If no types are specified, places of all types will be returned.

See the API reference
Get place details
To return a Place object from a place prediction result, first call toPlace(), then call fetchFields() on the resulting Place object (the session ID from the place prediction is automatically included). Calling fetchFields() ends the autocomplete session.


let place = suggestions[0].placePrediction.toPlace(); // Get first predicted place.

await place.fetchFields({
  fields: ["displayName", "formattedAddress"],
});

const placeInfo = document.getElementById("prediction");

placeInfo.textContent =
  "First predicted place: " +
  place.displayName +
  ": " +
  place.formattedAddress;
Session tokens
Session tokens group the query and selection phases of a user autocomplete search into a discrete session for billing purposes. The session begins when the user starts typing. The session is concluded when the user selects a place and a call to Place Details is made.

To create a new session token and add it to a request, create an instance of AutocompleteSessionToken, then set the sessionToken property of the request to use the tokens as shown in the following snippet:


// Create a session token.
const token = new AutocompleteSessionToken();

// Add the token to the request.
// @ts-ignore
request.sessionToken = token;
A session is concluded when fetchFields() is called. After creating the Place instance, you don't need to pass the session token to fetchFields() as this is handled automatically.


await place.fetchFields({
  fields: ["displayName", "formattedAddress"],
});
Make a session token for the next session by creating a new instance of AutocompleteSessionToken.

Session token recommendations:
Use session tokens for all Place Autocomplete calls.
Generate a fresh token for each session.
Pass a unique session token for each new session. Using the same token for more than one session will result in each request being billed individually.
You can optionally omit the autocomplete session token from a request. If the session token is omitted, each request is billed separately, triggering the Autocomplete - Per Request SKU. If you reuse a session token, the session is considered invalid and the requests are charged as if no session token was provided.

Example
As the user types a query, an autocomplete request is called every few keystrokes (not per-character), and a list of possible results is returned. When the user makes a selection from the result list, the selection counts as a request, and all of the requests made during the search are bundled and counted as a single request. If the user selects a place, the search query is available at no charge, and only the Place data request is charged. If the user does not make a selection within a few minutes of the beginning of the session, only the search query is charged.

From the perspective of an app, the flow of events goes like this:

A user begins typing a query to search for "Paris, France".
Upon detecting user input, the app creates a new session token, "Token A".
As the user types, the API makes an autocomplete request every few characters, displaying a new list of potential results for each:
"P"
"Par"
"Paris,"
"Paris, Fr"
When the user makes a selection:
All requests resulting from the query are grouped and added to the session represented by "Token A", as a single request.
The user's selection is counted as a Place Detail request, and added to the session represented by "Token A".
The session is concluded, and the app discards "Token A".
Learn about how sessions are billed
Complete example code
This section contains complete examples showing how to use the Place Autocomplete Data API .
Place autocomplete predictions
The following example demonstrates calling fetchAutocompleteSuggestions() for the input "Tadi", then calling toPlace() on the first prediction result, followed by a call to fetchFields() to get place details.


/**
 * Demonstrates making a single request for Place predictions, then requests Place Details for the first result.
 */
async function init() {
  // @ts-ignore
  const { Place, AutocompleteSessionToken, AutocompleteSuggestion } =
    await google.maps.importLibrary("places");
  // Add an initial request body.
  let request = {
    input: "Tadi",
    locationRestriction: {
      west: -122.44,
      north: 37.8,
      east: -122.39,
      south: 37.78,
    },
    origin: { lat: 37.7893, lng: -122.4039 },
    includedPrimaryTypes: ["restaurant"],
    language: "en-US",
    region: "us",
  };
  // Create a session token.
  const token = new AutocompleteSessionToken();

  // Add the token to the request.
  // @ts-ignore
  request.sessionToken = token;

  // Fetch autocomplete suggestions.
  const { suggestions } =
    await AutocompleteSuggestion.fetchAutocompleteSuggestions(request);
  const title = document.getElementById("title");

  title.appendChild(
    document.createTextNode('Query predictions for "' + request.input + '":'),
  );

  for (let suggestion of suggestions) {
    const placePrediction = suggestion.placePrediction;
    // Create a new list element.
    const listItem = document.createElement("li");
    const resultsElement = document.getElementById("results");

    listItem.appendChild(
      document.createTextNode(placePrediction.text.toString()),
    );
    resultsElement.appendChild(listItem);
  }

  let place = suggestions[0].placePrediction.toPlace(); // Get first predicted place.

  await place.fetchFields({
    fields: ["displayName", "formattedAddress"],
  });

  const placeInfo = document.getElementById("prediction");

  placeInfo.textContent =
    "First predicted place: " +
    place.displayName +
    ": " +
    place.formattedAddress;
}

init();