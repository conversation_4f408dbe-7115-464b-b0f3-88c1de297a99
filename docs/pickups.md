# ETRFlow Pickup System

## Overview

The ETRFlow pickup system provides a comprehensive solution for managing customer pickups through a guided request-based workflow with customer self-service capabilities. The system supports guest (public) submissions, staff management, and customer appointment management through a secure token-based portal system.

The system is built around a request-first architecture where all pickup information is captured in pickup requests, which then get converted to calendar events for scheduling. This ensures data consistency and provides a complete audit trail from initial request to pickup completion.

### Key Features
- **Guest Pickup Requests**: Public multi-step form with real-time availability
- **Customer Self-Service Portal**: Token-based appointment management and confirmation
- **Automated Reminder System**: 48-hour advance notifications with email templates
- **Image Upload System**: Support for pickup documentation across all interfaces
- **Advanced Conflict Prevention**: Multi-layer scheduling validation with buffer time
- **Comprehensive Activity Logging**: Complete audit trail of all pickup activities
- **Email Template Integration**: Customizable notifications through EmailTemplateService
- **Distance Integration**: Google Maps integration for route planning
- **PDF Report Generation**: Daily pickup reports with distance calculations

## System Architecture

### Core Components

1. **Pickup Requests**: Central data structure containing all pickup information
2. **Calendar Events**: Scheduling containers linked to pickup requests
3. **Guest Interface**: Public-facing pickup request form at `/pickup-request`
4. **Customer Portal**: Token-based self-service appointment management
5. **Staff Interface**: Administrative pickup request management
6. **Calendar Integration**: FullCalendar display with pickup request data
7. **Availability Engine**: Real-time scheduling conflict prevention
8. **Automated Reminders**: 48-hour notification system with email templates
9. **Image Upload System**: Multi-source image handling with optimization
10. **Configuration System**: Global settings for scheduling rules and availability
11. **Activity Logging**: Comprehensive audit trail and tracking system
12. **Email Templates**: Customizable communication system

### Data Flow

```
Guest Submission → Pickup Request (incoming) → Customer Assignment → pending
                                                                         ↓
         Customer Confirmation ← Email Reminder ← Automated System
                  ↓                                  ↓
            confirmed → Calendar Event → Pickup Execution → completed
                                                                ↓
                                                          cancelled
```

### Customer Self-Service Integration
```
Staff assigns customer → Email with management link → Customer portal access
                                     ↓
                          Customer confirms/edits → Staff notification
                                     ↓
                          Appointment confirmed → Final reminder sent
```

## Pickup Request Data Structure

### Contact Information
- **Contact Name**: Primary contact person (required)
- **Business Name**: Optional business/organization name
- **Email**: Contact email address (required)
- **Phone**: Contact phone number (required)

### Location Details
- **Pickup Address**: Full pickup location address with Google Maps autocomplete
- **Property Location Details**: Specific location on property (garage, basement, loading dock, etc.)
- **Other Notes**: Security codes, special instructions, access details

### Item Information (Guided Approach)

The system uses a guided approach to capture detailed item information:

#### Load Size Classification
- **Small Load**: 1-2 flatscreen TVs, computers, small appliances (fits in midsize SUV or less)
  - Most residential pickups fall into this category
- **Medium Load**: Pallets, totes, gaylords movable by 1 person with equipment
  - Typical business pickups with organized materials
- **Large/Heavy Load**: Heavy items requiring 2+ people or specialized handling
  - Industrial equipment, large appliances, bulk materials

#### Item Types (Multi-select checkboxes)
- Small Electronics
- Appliances
- Peripheral Devices
- Batteries
- CRT TVs
- Flatscreen TV(s)
- Tote Swap
- Gaylord Swap
- Servers
- Laptops
- Desktops
- Large Appliances
- Other

#### Additional Details
- **Item Specifics**: Detailed description of items and quantities (required)
- **Driver Instructions**: Special instructions for pickup team (optional)
- **Accessibility Level**: Easy, Moderate, or Difficult access (required)
  - Easy: Ground level, easy access
  - Moderate: Stairs, narrow doorways, some obstacles
  - Difficult: Multiple flights, tight spaces, heavy lifting required

### Legacy Field Support

For backward compatibility, the system maintains legacy fields:
- **Pickup Items**: Free-form item description (nullable, legacy)
- **Pickup Quantity**: Estimated quantity (nullable, legacy)

### Metadata and Relationships
- **Preferred Pickup Date**: Customer's preferred pickup time (datetime)
- **Status**: Current request status (incoming, pending, confirmed, completed, cancelled)
- **Customer Link**: Associated customer record (foreign key)
- **Event Link**: Associated calendar event (foreign key)
- **Submission Info**: Timestamp and source tracking
- **Pickup Details**: JSON field for flexible additional data storage
- **Internal Notes**: Staff-only notes for coordination

### Customer Management Integration
- **Management Token**: Unique 20-character secure token for customer portal access
- **Management Token Expires At**: Token expiration timestamp (60-day default)
- **Confirmation Token**: 64-character token for direct email confirmations
- **Confirmed At**: Timestamp when customer or staff confirmed the appointment
- **Cancelled At**: Timestamp when pickup was cancelled
- **Cancellation Reason**: Optional reason for cancellation

### Automated Reminder System
- **Reminder Sent At**: Timestamp when 48-hour confirmation reminder was sent
- **Final Reminder Sent At**: Timestamp when final reminder was sent for confirmed pickups

### Image Documentation
- **Images**: Related pickup request images with automatic optimization
- **Image Limits**: Maximum 10 images per pickup request
- **Supported Sources**: Guest uploads, staff uploads, customer portal uploads

## Pickup Request Workflows

### Guest Workflow (Public Interface)

**Access**: Available at `/pickup-request` - no authentication required

**Multi-Step Process**:
1. **Step 1 - Contact Info**: Enter contact details and business information
   - Contact name, business name (optional), email, phone
   - Real-time validation and error feedback
2. **Step 2 - Time Selection**: Choose from available pickup dates and times
   - Progressive date selection showing only available dates
   - Time slot selection with real-time availability checking
   - Lead time enforcement and buffer time protection
3. **Step 3 - Location Details**: Provide pickup address and property details
   - Google Maps address autocomplete with place validation
   - Property location details and access instructions
   - Security codes and special notes
4. **Step 4 - Item Details**: Use guided form to specify load size, item types, and specifics
   - Load size classification with visual descriptions
   - Multi-select item type checkboxes
   - Detailed item specifications and driver instructions
   - Accessibility level assessment
5. **Image Upload**: Optional image documentation of items to be picked up
   - Drag-and-drop image upload with automatic WebP compression
   - Up to 10 images per request with thumbnail generation
   - Mobile camera integration for direct photo capture
6. **Submission**: Request enters "incoming" status for staff review
7. **Confirmation**: Receive email confirmation with next steps and submission details

### Staff Workflow (Administrative Interface)

#### Creating New Requests
1. **Create Request**: Use `/pickup-requests/create` with full guided form
2. **Customer Selection**: Link to existing customer or create new customer profile
3. **Auto-Population**: Customer data automatically fills contact fields
4. **Item Details**: Use same guided approach as guest form
5. **Direct Scheduling**: Staff-created requests start as "pending" (skip "incoming" status)
6. **Auto-Save**: Form data automatically saves as draft during editing

#### Processing Guest Requests
1. **Review Incoming**: View requests at `/pickup-requests` with "incoming" status filter
2. **Customer Assignment**: Link request to customer profile via step-by-step wizard
   - Step 1: Customer identification with search and suggestions
   - Customer search with autocomplete functionality
   - Option to create new customer if none found
3. **Time Confirmation**: Verify and adjust pickup scheduling
4. **Event Creation**: Convert to calendar event with staff assignments
   - Assign driver and specify staff needed
   - Set pickup duration and scheduling details
5. **Customer Contact**: Send confirmation email with management portal access
6. **Status Management**: Track through pending → confirmed → completed via customer portal or staff action

#### Request Management
- **Edit Requests**: Modify pickup details, contact info, and item specifications
- **Auto-Save**: Real-time saving of form changes during editing with draft indicators
- **Status Updates**: Manage request lifecycle with action buttons
- **Internal Notes**: Add staff-only notes for coordination
- **Calendar Integration**: View requests in calendar context with week view
- **Bulk Operations**: Filter and manage multiple requests efficiently

## Pickup Request Status Workflow

The pickup request system uses a five-stage status workflow to track requests from submission to completion:

### Status Definitions

1. **Incoming** - Pickup requests submitted by clients via the guest pickup request view that need to be assigned a customer and go through the pickup request approval process. Pickup requests created by staff skip this step and go directly to pending.

2. **Pending** (displayed as "Pending Customer Confirmation") - Pickup requests that have been assigned to a customer profile by a staff member and have an assigned pickup time but have not been confirmed by the customer, OR pickup requests created by staff that are not yet confirmed. Automated reminder emails are sent 48 hours before pickup.

3. **Confirmed** - Customer has confirmed the appointment via the customer portal or staff has manually confirmed customer response (email/call). Automated final reminder emails are sent 48 hours before pickup.

4. **Completed** - Pickup has been completed successfully.

5. **Cancelled** - Pickup request has been cancelled by either staff or customer with optional cancellation reason.

### Status Transitions

```
Guest Submission → Incoming
Staff Creation → Pending (skips Incoming)
Incoming + Customer Assignment → Pending
                                    ↓
                          48-Hour Reminder Sent
                                    ↓
        Customer Portal Confirmation OR Staff Manual Confirmation
                                    ↓
Pending + Customer/Staff Confirmation → Confirmed
                                    ↓
                          Final Reminder Sent (48 hours before)
                                    ↓
Confirmed + Pickup Execution → Completed
                                    ↓
Any Status (except Completed) → Cancelled (with optional reason)
```

### Staff Actions by Status

**Incoming Requests:**
- Assign to existing customer or create new customer via step-by-step wizard
- Review pickup details and contact information
- Use customer search functionality to find matching profiles
- Ready to schedule once customer is assigned (status changes to Pending)

**Pending Requests:**
- **Automated Reminders**: System sends 48-hour confirmation reminders automatically
- **Customer Portal Access**: Customers receive management portal links for self-service
- **Multiple Confirmation Paths**: 
  - Customer self-confirmation via portal (preferred)
  - Direct email confirmation via single-click links
  - Staff manual confirmation after phone/email contact
- **Calendar Event Creation**: Create calendar event with staff assignments and driver selection
- **Status Tracking**: Automatic status updates based on customer actions or staff confirmation

**Confirmed Requests:**
- Pickup is scheduled and confirmed with customer
- Appears on pickup calendar with full details
- Ready for pickup team execution
- Can be edited if changes are needed

**Completed/Cancelled:**
- Final statuses for reporting and tracking
- No further action required
- Used for historical reporting and analytics

## Customer Appointment Management Portal

### Overview

The customer appointment management portal provides a secure, token-based system that allows customers to manage their pickup appointments without requiring account registration. Customers receive access via email links with time-limited management tokens.

### Portal Features

#### Access Methods
- **Management Portal**: `/pickup/manage/{token}` - Full appointment management interface
- **Direct Confirmation**: `/pickup/confirm/{token}` - One-click confirmation from emails
- **Edit Interface**: `/pickup/manage/{token}/edit` - Customer editing capabilities

#### Customer Capabilities
- **Appointment Confirmation**: Confirm pickup appointments with one click
- **Appointment Details**: View complete pickup information and scheduling
- **Edit Pickup Details**: Modify item descriptions, accessibility level, and driver instructions
- **Image Upload**: Add up to 10 images documenting items for pickup
- **Cancellation**: Cancel appointments with optional reason
- **Contact Staff**: Direct contact information for questions

#### Security Features
- **Token-Based Access**: 20-character unique tokens for each pickup request
- **Time-Limited**: Tokens expire 60 days from creation (configurable)
- **Single-Use Confirmation**: Direct confirmation tokens are consumed on use
- **No Account Required**: Customers access via email links without registration

### Email Integration

#### Automated Email Sending
- **Confirmation Requests**: Sent when pickup moves to "pending" status
- **48-Hour Reminders**: Automated reminders for unconfirmed pickups
- **Final Reminders**: Sent 48 hours before confirmed pickups
- **Cancellation Confirmations**: Sent when appointments are cancelled

#### Email Template System
- **pickup_confirmation**: Initial appointment details with management link
- **pickup_confirmation_reminder**: 48-hour confirmation reminder
- **pickup_final_reminder**: Final reminder for confirmed appointments
- **pickup_cancellation**: Cancellation confirmation

### Customer Portal Workflow

#### Initial Access
1. **Staff Assignment**: Staff assigns pickup request to customer profile
2. **Email Generation**: System sends confirmation email with management token
3. **Customer Access**: Customer clicks link to access management portal
4. **Portal Display**: Shows appointment details, status, and available actions

#### Confirmation Process
1. **Review Details**: Customer reviews pickup information and scheduling
2. **Confirm Appointment**: Single click confirmation with immediate feedback
3. **Staff Notification**: Automatic notification sent to configured staff groups
4. **Status Update**: Pickup request status updates to "confirmed"
5. **Final Reminder**: System schedules 48-hour final reminder

#### Edit Capabilities
1. **Access Edit Form**: Customer navigates to edit interface
2. **Modify Details**: Update item descriptions, accessibility, driver instructions
3. **Upload Images**: Add visual documentation of items
4. **Save Changes**: Real-time saving with staff notifications
5. **Activity Logging**: All changes tracked with customer attribution

### Staff Integration

#### Management Notifications
- **Customer Confirmations**: Real-time notifications when customers confirm
- **Customer Edits**: Notifications when customers modify pickup details
- **Customer Cancellations**: Immediate alerts for cancelled appointments
- **Customer Questions**: Contact form submissions from portal

#### Staff Controls
- **Manual Email Sending**: Send confirmation emails on-demand
- **Token Management**: View token status and expiration dates
- **Portal Access**: View customer portal as they see it
- **Override Capabilities**: Staff can confirm on behalf of customers

### Technical Implementation

#### Token Generation
- **Management Tokens**: 20-character alphanumeric tokens (non-expiring actions)
- **Confirmation Tokens**: 64-character tokens (single-use confirmations)
- **Expiry Management**: Automatic cleanup of expired tokens
- **Security**: Cryptographically secure random generation

#### Database Fields
```php
'management_token' => string(20) // Portal access token
'management_token_expires_at' => timestamp // Token expiration
'confirmation_token' => string(64) // Direct confirmation token
'confirmed_at' => timestamp // Customer confirmation time
'cancelled_at' => timestamp // Cancellation time
'cancellation_reason' => text // Optional cancellation reason
```

#### Activity Logging
- **Customer Actions**: All customer interactions logged with IP and timestamp
- **Portal Access**: Track when customers access management portal
- **Confirmation Events**: Log confirmation method (portal vs staff)
- **Edit History**: Track all customer modifications with before/after data

### Customer Experience

#### Mobile Optimized
- **Responsive Design**: Optimized for mobile devices and tablets
- **Touch-Friendly**: Large buttons and touch targets
- **Fast Loading**: Minimal JavaScript for quick access
- **Offline Capability**: Key information cached for poor connections

#### User-Friendly Features
- **Clear Status Display**: Visual indicators for appointment status
- **Action Buttons**: Prominent confirmation and edit buttons
- **Help Information**: Contact details and FAQ information
- **Error Handling**: Clear error messages with suggested actions

#### Communication
- **Status Updates**: Real-time status display with latest information
- **Next Steps**: Clear guidance on what happens next
- **Contact Options**: Multiple ways to reach staff with questions
- **Confirmation Feedback**: Immediate confirmation of actions taken

## Image Upload System

### Overview

The pickup request system includes a comprehensive image upload system that allows documentation of items across all interfaces - guest submissions, staff management, and customer portal. The system automatically optimizes images for web delivery while maintaining quality for documentation purposes.

### Image Upload Features

#### Universal Access
- **Guest Interface**: Upload images during pickup request submission
- **Staff Interface**: Add images when creating or editing pickup requests
- **Customer Portal**: Upload images via management portal for additional documentation
- **Mobile Optimized**: Direct camera integration on mobile devices

#### Technical Capabilities
- **Format Support**: JPEG, PNG, WebP, GIF with automatic WebP conversion
- **Automatic Compression**: Client-side compression before upload to reduce file sizes
- **Thumbnail Generation**: Multiple thumbnail sizes (sm, md, lg) for different contexts
- **File Size Limits**: Automatic compression keeps files under reasonable limits
- **Image Optimization**: Progressive JPEG and WebP encoding for fast loading

#### Upload Limits and Management
- **10 Image Maximum**: Per pickup request to prevent excessive storage usage
- **Automatic Cleanup**: Session-based cleanup for abandoned guest uploads
- **Duplicate Prevention**: Hash-based duplicate detection and prevention
- **Error Handling**: Graceful handling of upload failures with user feedback

### Implementation Across Interfaces

#### Guest Pickup Request Form
**Route**: `/pickup-request` (Step 5 - Image Upload)

**Features**:
- **Drag-and-Drop Interface**: Modern file selection with visual feedback
- **Camera Integration**: Direct photo capture on mobile devices
- **Real-Time Preview**: Immediate preview of uploaded images with thumbnails
- **Session Storage**: Images stored temporarily until form submission
- **Progress Indicators**: Upload progress and compression status

**Technical Details**:
- Images uploaded via `/pickup-request/upload-image` endpoint
- Temporary storage with session-based association
- Automatic deletion if pickup request not submitted within 24 hours
- Client-side compression using browser-image-compression library

#### Staff Interface
**Routes**: `/pickup-requests/{id}/upload-image`, `/pickup-requests/{id}/images`

**Features**:
- **Instant Upload**: Images immediately associated with pickup request
- **Gallery View**: Modal gallery for viewing all uploaded images
- **Individual Deletion**: Remove specific images with confirmation
- **Batch Operations**: Select and delete multiple images
- **PhotoSwipe Integration**: Professional image viewing with zoom and navigation

**Management Capabilities**:
- View all images associated with pickup request
- Delete images individually or in batches
- Download original images for documentation
- Image metadata display (upload date, file size, dimensions)

#### Customer Portal
**Route**: `/pickup/manage/{token}/upload-image`

**Features**:
- **Self-Service Upload**: Customers can add documentation images
- **Staff Notifications**: Automatic alerts when customers upload images
- **Activity Logging**: All customer image uploads tracked in activity log
- **Mobile Camera**: Direct camera integration for mobile customers

**Security**:
- Token-based authentication for secure access
- Customer-specific image associations
- Automatic expiry with token expiration

### Image Storage and Organization

#### File Structure
```
storage/app/public/pickup-request-images/
├── originals/          # Full-size uploaded images
├── thumbnails/         # Generated thumbnail images
│   ├── sm/            # Small thumbnails (150px)
│   ├── md/            # Medium thumbnails (300px)
│   └── lg/            # Large thumbnails (600px)
└── temporary/          # Session-based temporary uploads
```

#### Database Schema
**PickupRequestImage Model**:
```php
'pickup_request_id' => foreign key to pickup_requests
'filename' => string // Original filename for display
'file_path' => string // Storage path to original image
'file_size' => integer // File size in bytes
'mime_type' => string // Image MIME type
'uploaded_by_type' => string // 'guest', 'staff', 'customer'
'uploaded_by_id' => nullable foreign key to users (null for guest/customer)
'session_id' => nullable string // For temporary guest uploads
'created_at' => timestamp
'updated_at' => timestamp
```

#### Image Processing Pipeline
1. **Upload Validation**: File type, size, and security validation
2. **Client Compression**: Browser-side compression to reduce file sizes
3. **Server Processing**: Additional server-side optimization if needed
4. **Thumbnail Generation**: Multiple thumbnail sizes created automatically
5. **WebP Conversion**: Modern format conversion for better compression
6. **Storage**: Secure storage with organized file structure
7. **Database Association**: Image metadata stored with pickup request link

### Security and Validation

#### Upload Security
- **File Type Validation**: Whitelist of allowed image MIME types
- **Content Validation**: Actual image content verification, not just extension
- **Size Limits**: Reasonable file size limits to prevent abuse
- **Virus Scanning**: File content scanning for security (if configured)
- **Access Control**: Permission-based access to upload functionality

#### Storage Security
- **Private Storage**: Images stored in private storage, not publicly accessible
- **Authenticated Access**: All image viewing requires proper authentication
- **Token-Based Customer Access**: Secure customer access via management tokens
- **Path Traversal Prevention**: Secure file path handling to prevent directory traversal

#### Privacy Protection
- **Customer Data**: Images treated as sensitive customer data
- **Access Logging**: All image access logged for audit purposes
- **Retention Policies**: Automatic cleanup based on pickup request lifecycle
- **GDPR Compliance**: Customer data handling follows privacy regulations

### Image Viewing and Gallery

#### PhotoSwipe Integration
- **Professional Gallery**: Full-screen image viewing with navigation
- **Zoom Functionality**: Pinch-to-zoom and mouse wheel zoom support
- **Touch Gestures**: Mobile-optimized touch controls
- **Keyboard Navigation**: Arrow keys and escape key support
- **Share Functionality**: Download and share capabilities where appropriate

#### Responsive Thumbnails
- **Adaptive Loading**: Appropriate thumbnail size based on display context
- **Lazy Loading**: Images load only when needed for performance
- **Progressive Enhancement**: Works without JavaScript for accessibility
- **Mobile Optimization**: Touch-friendly thumbnail grids

#### Gallery Features
- **Caption Support**: Optional image captions and descriptions
- **Upload Metadata**: Display upload date, file size, and uploader information
- **Sorting Options**: Chronological and custom sorting capabilities
- **Search Integration**: Find pickup requests by image content (future enhancement)

### Performance Optimization

#### Client-Side Optimization
- **Progressive Loading**: Images load progressively for better perceived performance
- **Compression Pipeline**: Multi-stage compression for optimal file sizes
- **Format Selection**: Automatic best format selection (WebP, JPEG, PNG)
- **Caching Strategy**: Browser caching with appropriate cache headers

#### Server-Side Optimization
- **CDN Integration**: Optional CDN support for faster image delivery
- **Thumbnail Caching**: Pre-generated thumbnails cached for fast access
- **Storage Optimization**: Automatic cleanup of temporary and unused images
- **Database Indexing**: Proper indexing for fast image queries

### Troubleshooting and Maintenance

#### Common Issues
- **Upload Failures**: Network issues, file size limits, format problems
- **Compression Problems**: Browser compatibility with compression library
- **Storage Issues**: Disk space, permissions, file system problems
- **Performance Issues**: Large image files, slow network connections

#### Maintenance Tasks
- **Cleanup Jobs**: Automated cleanup of temporary and orphaned images
- **Storage Monitoring**: Track storage usage and implement limits
- **Performance Monitoring**: Monitor upload times and compression ratios
- **Security Audits**: Regular security scans of uploaded content

#### Configuration Options
- **Upload Limits**: Configurable file size and count limits
- **Compression Settings**: Adjustable compression quality and formats
- **Storage Options**: Multiple storage backend support (local, S3, etc.)
- **Security Policies**: Configurable validation and scanning options

## Availability Windows

### Purpose

Availability windows define the preferred times when pickup scheduling is available. This helps:

- Guide staff to schedule during optimal business hours
- Provide visual feedback about scheduling outside normal times
- Prepare for future customer self-service scheduling (where customers will be restricted to available windows)

### Configuration

Administrators can configure availability windows in **Global Config → Calendar Settings**:

1. **Per Day Setup**: Configure different windows for each day of the week
2. **Multiple Windows**: Add multiple time ranges per day (e.g., morning and afternoon slots)
3. **Flexible Times**: Set start and end times using standard time inputs
4. **Optional Setup**: If no windows are configured, all times are considered available

### Pickup Event Settings

Additional configuration options control pickup event behavior:

#### Default Event Duration
- **Purpose**: Sets how long pickup events run by default
- **Options**: 15 minutes to 4 hours
- **Default**: 1 hour (60 minutes)
- **Usage**: When staff create pickup events, the end time is automatically set based on this duration

#### Scheduling Interval
- **Purpose**: Defines the time intervals at which customers can schedule pickups
- **Options**: Every 5 minutes to every 2 hours
- **Default**: Every 30 minutes (half hour)
- **Usage**: Determines available time slots for customer self-service scheduling

### Lead Time Requirements

Lead time requirements ensure customers provide adequate advance notice for pickup requests.

#### Configuration Options
- **Lead Time Days**: Number of days advance notice required (0-30 days)
- **Lead Time Hours**: Additional hours beyond days (0-23 hours)
- **Business Days Only**: Count only weekdays (Monday-Friday) for lead time calculation
- **Default**: 1 day, 0 hours, business days only

#### Business Days Logic
When "Business Days Only" is enabled:
- **Weekend Exclusion**: Saturdays and Sundays don't count toward lead time
- **Example**: If today is Friday and lead time is 1 business day, earliest pickup is Tuesday
- **Calculation**: System automatically skips weekends when counting required advance notice

#### Lead Time Examples
- **1 day, business days only**: Request on Friday → earliest pickup Tuesday
- **2 days, calendar days**: Request on Friday → earliest pickup Sunday
- **1 day + 4 hours, business days**: Request Monday 9 AM → earliest pickup Wednesday 1 PM

### Buffer Time Management

Buffer time creates mandatory gaps between pickup appointments to allow for travel, setup, or cleanup.

#### Configuration Options
- **Buffer Duration**: 0 to 240 minutes (0 = no buffer, 30 minutes recommended)
- **Buffer Direction**:
  - **After events** (recommended): Gap after each pickup ends
  - **Before events**: Gap before each pickup starts
  - **Both**: Gaps both before and after each pickup

#### Buffer Time Examples

**Scenario**: 30-minute buffer "after events"
- **Existing pickup**: 10:00 AM - 11:00 AM
- **Buffer period**: 11:00 AM - 11:30 AM (blocked)
- **Next available**: 11:30 AM or later

**Scenario**: 30-minute buffer "both directions"
- **Existing pickup**: 10:00 AM - 11:00 AM
- **Buffer periods**: 9:30 AM - 10:00 AM AND 11:00 AM - 11:30 AM (both blocked)
- **Available slots**: Before 9:30 AM or after 11:30 AM

#### Buffer Application
Buffer time is enforced for:
- ✅ **Calendar events** (scheduled pickups)
- ✅ **Pickup requests** (all statuses: incoming, pending, confirmed, etc.)
- ✅ **Guest scheduling** (public pickup request form)
- ✅ **Staff scheduling** (admin pickup creation)

#### Comprehensive Scheduling Logic Example

**Configuration**:
- **Availability Window**: 10:00 AM to 2:00 PM
- **Event Duration**: 1 hour
- **Scheduling Interval**: 30 minutes
- **Buffer Time**: 30 minutes after events
- **Existing Pickup**: 11:00 AM - 12:00 PM

**Available Slots Analysis**:
- **10:00 AM - 11:00 AM** ✓ (fits within window, no conflicts)
- **10:30 AM - 11:30 AM** ✗ (would overlap with existing 11:00 AM pickup)
- **11:00 AM - 12:00 PM** ✗ (conflicts with existing pickup)
- **11:30 AM - 12:30 PM** ✗ (conflicts with existing pickup)
- **12:00 PM - 1:00 PM** ✗ (violates 30-minute buffer after existing pickup)
- **12:30 PM - 1:30 PM** ✓ (respects buffer time, fits within window)
- **1:00 PM - 2:00 PM** ✓ (fits within window, no conflicts)
- **1:30 PM - 2:30 PM** ✗ (would extend past 2:00 PM window end)

This demonstrates how the system considers availability windows, event duration, buffer time, and existing appointments to determine valid scheduling slots.

### Availability Checking Logic

The system performs comprehensive availability checking that considers the entire event duration:

#### Event Validation Rules
1. **Start Time Check**: Event start time must be within an availability window
2. **End Time Check**: Event end time must be within the same availability window
3. **Duration Check**: The entire event must fit within a single availability window
4. **No Spanning**: Events cannot span across multiple windows or unavailable periods

#### Examples of Availability Checking

**Scenario**: Availability window 10:00 AM - 12:00 PM, 1-hour event duration

- **10:00 AM - 11:00 AM**: ✅ Valid (starts and ends within window)
- **11:30 AM - 12:30 PM**: ❌ Invalid (ends outside window at 12:30 PM)
- **12:00 PM - 1:00 PM**: ❌ Invalid (starts at window end, extends beyond)
- **9:30 AM - 10:30 AM**: ❌ Invalid (starts before window, even though it overlaps)

**Multiple Windows**: If there are multiple windows in a day (e.g., 9:00-11:00 AM and 2:00-4:00 PM), events must fit entirely within one window - they cannot span the gap between windows.

## Manual Blockout System

### Purpose

Manual blockouts allow staff to temporarily block specific times or days to prevent pickup scheduling. This is useful for:

- **Staff meetings or training sessions**
- **Equipment maintenance periods**
- **Holiday closures or reduced hours**
- **Emergency situations requiring schedule adjustments**
- **Special events that affect pickup operations**

### Creating Blockouts

Staff can create blockout events through the pickup calendar interface:

1. **Add Blockout Button**: Click the red "Add Blockout" button on pickup calendars
2. **Time Selection**: Choose start and end times for the blockout period
3. **Title and Description**: Provide a clear title and optional description explaining the blockout
4. **Location**: Optionally specify where the blockout applies
5. **All-Day Option**: Mark as all-day event for full day blockouts

### Blockout Behavior

- **Visual Distinction**: Blockout events appear in red on the calendar to distinguish them from regular events
- **Scheduling Prevention**: Customers cannot schedule pickups during blocked-out times (future feature)
- **Staff Override**: Staff can still manually create pickup events during blockouts if necessary
- **Edit/Delete**: Staff can modify or remove blockouts as needed by clicking on them

### Blockout vs Availability Windows

| Feature | Availability Windows | Manual Blockouts |
|---------|---------------------|------------------|
| **Purpose** | Define regular business hours | Block specific times/events |
| **Scope** | Recurring weekly schedule | One-time or temporary blocks |
| **Configuration** | Global config by administrators | Created by staff as needed |
| **Visual** | Background shading on calendar | Red events on calendar |
| **Flexibility** | Fixed weekly pattern | Flexible timing and duration |
| **Use Case** | "We're open 9-5 Monday-Friday" | "Closed Dec 25 for holiday" |

### Best Practices

- **Clear Titles**: Use descriptive titles like "Staff Meeting", "Holiday Closure", "Maintenance"
- **Advance Notice**: Create blockouts as early as possible to prevent conflicts
- **Communication**: Coordinate with team members before blocking significant time periods
- **Regular Review**: Remove expired blockouts to keep the calendar clean

### Staff Experience

- **Request-Based Workflow**: All pickups start as pickup requests, ensuring complete information capture
- **Calendar Integration**: Pickup events display information from linked pickup requests
- **Visual Indicators**: Calendar shows blocked-out times for unavailable periods
- **Pickup Request Management**: Staff can create, edit, and manage pickup requests through dedicated interfaces
- **Event Scheduling**: Convert pickup requests to calendar events with staff assignment and scheduling details
- **Data Consistency**: All pickup information comes from the pickup request, ensuring accuracy
- **Legacy Support**: System hides pickup events that don't have linked pickup requests

## Technical Implementation

### Database Schema

#### PickupRequest Model
```php
// Core contact fields
'contact_name' => string (required)
'business_name' => string (nullable)
'email' => string (required)
'phone' => string (required)

// Location fields
'pickup_address' => text (required)
'property_location_details' => text (required)
'other_notes' => text (nullable)

// Guided pickup fields (modern approach)
'load_size' => enum('small', 'medium', 'large') (required for new requests)
'item_types' => json array (required for new requests)
'item_specifics' => text (required for new requests)
'driver_instructions' => text (nullable)
'accessibility_level' => enum('easy', 'moderate', 'difficult') (required for new requests)

// Legacy fields (nullable for backward compatibility)
'pickup_items' => text (nullable)
'pickup_quantity' => text (nullable)

// Scheduling and workflow
'preferred_pickup_date' => datetime (required)
'status' => enum('incoming', 'pending', 'confirmed', 'completed', 'cancelled')

// Customer management integration
'management_token' => string(20) (unique token for customer portal access)
'management_token_expires_at' => timestamp (token expiration, default 60 days)
'confirmation_token' => string(64) (single-use token for direct confirmations)
'confirmed_at' => timestamp (when customer or staff confirmed appointment)
'cancelled_at' => timestamp (when pickup was cancelled)
'cancellation_reason' => text (optional reason for cancellation)

// Automated reminder system
'reminder_sent_at' => timestamp (when 48-hour confirmation reminder was sent)
'final_reminder_sent_at' => timestamp (when final reminder was sent)

// Relationships
'customer_id' => foreign key to customers (nullable initially, required after assignment)
'event_id' => foreign key to events (nullable until scheduled)

// Metadata and tracking
'pickup_details' => json (flexible additional data storage for legacy support)
'internal_pickup_notes' => text (staff-only notes for coordination)
'submitted_at' => timestamp (auto-set on creation)
'created_at' => timestamp
'updated_at' => timestamp

// Distance and route planning
'distance_miles' => decimal(8,2) (calculated distance from warehouse)
'travel_time_minutes' => integer (estimated travel time)
```

#### PickupRequestImage Model
```php
'pickup_request_id' => foreign key to pickup_requests (cascade delete)
'filename' => string (original filename for display)
'file_path' => string (storage path to original image)
'file_size' => integer (file size in bytes)
'mime_type' => string (image MIME type)
'uploaded_by_type' => enum('guest', 'staff', 'customer')
'uploaded_by_id' => nullable foreign key to users (null for guest/customer)
'session_id' => nullable string (for temporary guest uploads)
'created_at' => timestamp
'updated_at' => timestamp
```

#### Event Model Integration
```php
// Event fields relevant to pickups
'is_pickup_event' => boolean (true for pickup events)
'customer_id' => foreign key to customers
'assigned_driver_id' => foreign key to users
'staff_needed' => integer (number of staff required)
'pickup_address' => text (copied from pickup request)
'contact_name' => string (copied from pickup request)
'contact_phone' => string (copied from pickup request)
'contact_email' => string (copied from pickup request)
'pickup_request_id' => foreign key to pickup_requests (links event to request)
```

#### Status Workflow Constants
```php
public const STATUSES = [
    'incoming' => 'Incoming',
    'pending' => 'Pending Customer Confirmation',
    'confirmed' => 'Confirmed',
    'completed' => 'Completed',
    'cancelled' => 'Cancelled'
];

public const STATUS_DESCRIPTIONS = [
    'incoming' => 'Pickup requests submitted by clients that need customer assignment and approval',
    'pending' => 'Pickup requests assigned to a customer with pickup time but not confirmed by customer',
    'confirmed' => 'Staff has confirmed customer response (email/call), appointment confirmed',
    'completed' => 'Pickup has been completed',
    'cancelled' => 'Pickup request has been cancelled'
];
```

#### Validation Rules

**Guest Pickup Request Validation**:
```php
'contact_name' => 'required|string|max:255'
'business_name' => 'nullable|string|max:255'
'email' => 'required|email|max:255'
'phone' => 'required|string|max:255'
'pickup_address' => 'required|string|max:1000'
'property_location_details' => 'required|string|max:1000'
'other_notes' => 'nullable|string|max:1000'
'preferred_pickup_date' => 'required|date|after_or_equal:minimum_pickup_date'

// Guided pickup fields (required for new approach)
'load_size' => 'required|in:small,medium,large'
'item_types' => 'required|array|min:1'
'item_types.*' => 'string|in:small_electronics,appliances,peripheral_devices,batteries,crt_tvs,flatscreen_tvs,tote_swap,gaylord_swap,servers,laptops,desktops,large_appliances,other'
'item_specifics' => 'required|string|max:1000'
'driver_instructions' => 'nullable|string|max:1000'
'accessibility_level' => 'required|in:easy,moderate,difficult'

// Legacy fields (optional for backward compatibility)
'pickup_items' => 'nullable|string|max:1000'
'pickup_quantity' => 'nullable|string|max:500'
```

**Staff Pickup Request Validation**:
```php
'customer_id' => 'required|exists:customers,id'
'staff_needed' => 'required|integer|min:1|max:10'
'assigned_driver_id' => 'nullable|exists:users,id'
'internal_pickup_notes' => 'nullable|string|max:1000'

// Same validation as guest requests for other fields
// Guided pickup fields are required for staff-created requests
```

**Auto-Save Validation** (more lenient for drafts):
```php
// All fields are nullable for auto-save to allow partial completion
'customer_id' => 'nullable|exists:customers,id'
'contact_name' => 'nullable|string|max:255'
'load_size' => 'nullable|in:small,medium,large'
'item_types' => 'nullable|array'
'accessibility_level' => 'nullable|in:easy,moderate,difficult'
// ... other fields as nullable for draft functionality
```

### Configuration Storage

**Global Config Keys**:
- `pickup_calendar_id`: Target calendar for pickup events (integer, calendar ID)
- `pickup_availability_windows`: JSON day-of-week availability windows
- `pickup_event_duration`: Default event length in minutes (15-480, default: 60)
- `pickup_scheduling_interval`: Time slot granularity in minutes (5-120, default: 30)
- `pickup_lead_time_days`: Minimum advance notice in days (0-30, default: 1)
- `pickup_lead_time_hours`: Additional advance notice in hours (0-23, default: 0)
- `pickup_lead_time_business_days_only`: Weekday-only calculation (boolean, default: true)
- `pickup_buffer_time`: Gap between appointments in minutes (0-240, default: 0)
- `pickup_buffer_direction`: Buffer placement ('after', 'before', 'both', default: 'after')

**Configuration Access**:
- Settings managed through Global Config → Calendar Settings
- Real-time configuration updates without system restart
- Validation rules ensure sensible configuration values
- Default values provided for all settings

### Frontend Architecture

#### Guest Interface Components
- **Multi-Step Form**: Progressive disclosure with step indicators using DaisyUI steps component
- **Time Selector**: Date/time picker with real-time availability integration
- **Item Details Form**: Guided load size and item type collection with visual descriptions
- **Address Autocomplete**: Google Maps integration for location with place validation
- **Validation Engine**: Real-time form validation and error handling with AJAX
- **Mobile Responsive**: Optimized for mobile devices with condensed layouts

#### Staff Interface Components
- **Request Management**: Index, show, edit views for pickup requests with filtering
- **Customer Assignment**: Step-by-step customer linking workflow with search
- **Calendar Integration**: FullCalendar with pickup request data overlay and week view
- **Auto-Save Forms**: Real-time form persistence during editing with draft indicators
- **Status Management**: Action buttons for workflow progression
- **Bulk Operations**: Filter and manage multiple requests efficiently

#### Shared Components
- **Pickup Details Partial**: Reusable display component for request data
- **Item Details Partial**: Guided form component for item collection
- **Google Maps Autocomplete**: Address selection with place validation (both guest and staff versions)
- **Availability Engine**: Shared scheduling logic across interfaces
- **Week View Component**: Calendar week view for pickup request scheduling
- **Status Badge Components**: Visual status indicators across interfaces

## Guest Pickup Request System

### Overview

The guest pickup request system provides a public-facing, multi-step form that allows customers to request pickup services without requiring an account or login. The system features intelligent scheduling, real-time availability checking, and a guided item details collection process.

### Public Interface Features

**Access**: Available at `/pickup-request` - no authentication required

**Multi-Step Process**:
1. **Step 1 - Contact Info**: Contact name, business name (optional), email, phone
   - Real-time validation with error feedback
   - Required field indicators
2. **Step 2 - Time Selection**: Progressive date/time selection with availability checking
   - Date overview showing available dates with slot counts
   - Time slot selection with real-time availability
   - "Show More Dates" functionality for extended planning
3. **Step 3 - Location Details**: Address autocomplete and property location details
   - Google Maps address autocomplete with place validation
   - Property location details (garage, basement, loading dock, etc.)
   - Security codes and access instructions
4. **Step 4 - Item Details**: Guided load size, item types, and specifications
   - Load size classification with visual descriptions
   - Multi-select item type checkboxes
   - Detailed item specifications and driver instructions
   - Accessibility level assessment

**Advanced Scheduling**:
- **Real-Time Availability**: Shows only genuinely available time slots
- **Progressive Disclosure**: Date selection → time slot selection
- **Lead Time Enforcement**: Respects minimum advance notice requirements
- **Buffer Time Protection**: Prevents scheduling conflicts with existing appointments
- **Business Days Logic**: Supports weekday-only lead time calculations
- **Conflict Prevention**: Checks against all events, pickup requests, and blockouts
- **Multi-Layer Validation**: Date level, time slot level, and submission level checking

**Guided Item Collection**:
- **Load Size Classification**: Visual radio buttons with detailed descriptions
  - Small: 1-2 flatscreen TVs, computers (fits in midsize SUV)
  - Medium: Pallets, totes, gaylords (1 person with equipment)
  - Large: Heavy items requiring 2+ people
- **Item Type Selection**: Multi-select checkboxes for 13+ item categories
- **Detailed Specifications**: Free-form text for specific item details (required)
- **Driver Instructions**: Special instructions for pickup team (optional)
- **Accessibility Assessment**: Easy/Moderate/Difficult access classification (required)

**Technical Implementation**:
- **Mobile-Responsive**: DaisyUI framework for consistent styling
- **Google Maps Integration**: Address autocomplete with place validation and country restrictions
- **Real-Time Validation**: AJAX-based availability checking with immediate feedback
- **Progressive Enhancement**: Works without JavaScript for basic functionality
- **Form Persistence**: Maintains form state during multi-step process
- **Error Handling**: Comprehensive validation with user-friendly error messages

### Backend Processing

**Data Storage**:
- Stores complete request in `pickup_requests` table with all guided fields
- Status starts as "incoming" for guest submissions (staff requests start as "pending")
- Captures submission timestamp, IP address, user agent for tracking
- Preserves all guided item details in structured format
- Stores additional metadata in JSON `pickup_details` field

**Staff Integration**:
- Requests appear in staff dashboard at `/pickup-requests` for processing
- Customer assignment workflow for linking to existing profiles via step-by-step wizard
- Step-by-step conversion to calendar events with staff assignments
- Email notifications and status tracking throughout workflow
- Auto-save functionality for staff forms with draft management

**Availability Engine**:
- Uses same logic as staff calendar system for consistency
- Respects global configuration settings (lead time, buffer time, availability windows)
- Prevents double-booking across all interfaces (guest and staff)
- Maintains consistency between guest and staff views
- Real-time conflict checking against events, pickup requests, and blockouts

## Pickup Request to Event Workflow

### Overview

The system now uses a two-step process where pickup requests are the source of truth for all pickup information, and calendar events serve as scheduling containers that display pickup request data.

### Workflow Steps

1. **Create Pickup Request**: Staff or customers create pickup requests with comprehensive details
2. **Review and Process**: Staff review pending pickup requests in the admin interface
3. **Schedule Event**: Convert pickup request to calendar event with scheduling details (staff assignments, driver, etc.)
4. **Calendar Display**: Event appears on calendar showing pickup request information
5. **Manage**: Edit pickup request details or event scheduling as needed

### Data Flow

- **Pickup Request**: Contains all customer and pickup details (address, items, contact info, notes)
- **Calendar Event**: Contains scheduling details (start/end time, assigned staff, driver)
- **Display**: Calendar events pull pickup information from linked pickup requests
- **Legacy Events**: Pickup events without linked requests are hidden from calendar display

### Benefits

- **Complete Information**: Ensures all pickup details are captured in pickup requests
- **Data Consistency**: Single source of truth for pickup information
- **Better Tracking**: Clear workflow from request to scheduled event
- **Flexibility**: Allows editing pickup details without affecting calendar events
- **Integration**: Seamless connection between customer requests and staff scheduling

## Conflict Prevention & Double-Booking Protection

### Overview

The pickup system implements comprehensive conflict prevention to ensure no scheduling conflicts or double-booking occurs.

### Protection Mechanisms

#### Slot Exclusivity
- **Single Booking Rule**: Each time slot can only have one pickup request or event
- **Status Independent**: Applies to ALL pickup requests regardless of status (pending, approved, scheduled, etc.)
- **Real-Time Checking**: Validates availability at multiple points in the scheduling process

#### Multi-Layer Validation

**1. Date Overview Level**
- Shows only dates that have genuinely available time slots
- Counts exclude slots blocked by events, pickup requests, or buffer time
- "Show More Dates" loads additional weeks while maintaining accuracy

**2. Time Slot Level**
- Displays only truly available time slots for selected date
- Applies all restrictions: availability windows, buffer time, existing bookings
- Updates in real-time as slots are selected by other users

**3. Submission Level**
- Final validation before creating pickup request
- Double-checks against all existing events and pickup requests
- Prevents race conditions where multiple users select same slot

#### Conflict Sources

The system checks against:
- ✅ **Calendar Events** (scheduled pickups, blockouts, other events)
- ✅ **Pickup Requests** (pending, approved, completed - all statuses)
- ✅ **Buffer Time** (configured gaps around existing appointments)
- ✅ **Availability Windows** (business hours restrictions)
- ✅ **Lead Time** (minimum advance notice requirements)

### User Experience

**For Guests**:
- Only see genuinely available time slots
- Clear messaging about restrictions and requirements
- Immediate feedback if selected slot becomes unavailable

**For Staff**:
- Same conflict checking applies to admin-created pickup requests
- Visual indicators on calendar for blocked times
- Override capabilities for emergency scheduling (if needed)

### Technical Implementation

**Database Level**:
- Pickup requests stored with preferred_pickup_date timestamp
- Events linked to pickup requests maintain data consistency
- All queries check against complete dataset (not just pending items)

**Application Level**:
- Real-time availability calculation using GlobalConfig settings
- Buffer time applied dynamically based on configuration
- Lead time calculated with business days logic

**Frontend Level**:
- Progressive disclosure prevents invalid selections
- AJAX validation provides immediate feedback
- Error handling guides users to valid alternatives

## System Integration

### Calendar Integration

**Event Creation**:
- Pickup requests convert to calendar events with full data linking via `event_id` foreign key
- Events display pickup request information, not event-specific fields
- Calendar shows pickup events with customer and item details from linked pickup requests
- Staff can view/edit pickup details directly from calendar interface
- Events marked with `is_pickup_event` flag for identification

**Data Synchronization**:
- Pickup request updates automatically reflect in calendar events
- Event scheduling changes update pickup request preferred dates
- Status changes propagate between requests and events
- Internal staff notes sync across both systems
- Legacy pickup events without linked requests are hidden from display

**Visual Indicators**:
- Pickup events display with distinct styling and truck icons
- Load size and item types visible in calendar event details
- Status badges show current request state (incoming, pending, confirmed, etc.)
- Customer information prominently displayed in event titles
- Availability windows shown as background events on pickup calendars

**Calendar Features**:
- Dedicated pickup calendar configured via Global Config
- FullCalendar integration with drag-and-drop functionality
- Week view for detailed pickup scheduling
- Blockout events for preventing scheduling during unavailable times
- Real-time event updates and conflict prevention

### Customer Management Integration

**Profile Linking**:
- Guest requests can be linked to existing customer profiles via step-by-step wizard
- Customer data auto-populates contact fields during staff creation
- Address information imports from customer default addresses
- Business/residential customer type affects form behavior and validation

**Data Consistency**:
- Contact information remains editable even when linked to customers
- Pickup-specific details don't overwrite customer profile data
- Customer notes display as contextual information during request creation
- Profile updates don't affect existing pickup request data
- Customer search functionality with autocomplete for staff assignment

### Notification System

#### Email Template Integration

The pickup system is fully integrated with ETRFlow2's EmailTemplateService, providing customizable, professional email communications throughout the pickup lifecycle.

**Core Email Templates**:
- **pickup_request_notification**: Staff notifications for new guest submissions
- **pickup_confirmation**: Customer appointment details with management portal access
- **pickup_confirmation_reminder**: 48-hour confirmation reminders for pending pickups
- **pickup_final_reminder**: Final reminders sent 48 hours before confirmed pickups
- **pickup_cancellation**: Cancellation confirmations for customers and staff

**Template Features**:
- **Variable Substitution**: Dynamic content based on pickup request and customer data
- **Professional Formatting**: HTML emails with consistent branding
- **Mobile Optimization**: Responsive email design for mobile devices
- **Customizable Content**: Administrators can modify templates without code changes
- **Multi-Language Support**: Template system supports multiple languages (future)

**Template Variables Available**:
```php
// Common variables (available in all pickup emails)
{app_name}                    // Application name
{current_date}                // Current date in site timezone
{current_datetime}            // Current date and time

// Pickup-specific variables
{pickup_request_id}           // Pickup request ID number
{contact_name}                // Customer contact name
{business_name}               // Business name (if provided)
{email}                       // Customer email address
{phone}                       // Customer phone number
{pickup_address}              // Full pickup address
{property_location_details}   // Property location specifics
{preferred_pickup_date}       // Formatted pickup date and time
{load_size}                   // Load size (Small/Medium/Large)
{item_types_list}             // Formatted list of item types
{item_specifics}              // Detailed item description
{accessibility_level}         // Access difficulty level
{driver_instructions}         // Special instructions for driver
{status}                      // Current pickup status
{management_url}              // Customer portal management URL
{confirmation_url}            // Direct confirmation URL
{customer_name}               // Linked customer name (if assigned)
{internal_pickup_notes}       // Staff notes (staff emails only)
```

**Automated Email Sending**:
- **Status-Based Triggers**: Emails automatically sent on status changes
- **Scheduled Reminders**: Cron job sends 48-hour advance notifications
- **Staff Notifications**: Real-time alerts for customer actions
- **Error Handling**: Failed email delivery tracked and retried

#### Email Delivery Workflow

**Guest Submission Flow**:
1. **Immediate Confirmation**: Guest receives pickup_confirmation template
2. **Staff Notification**: pickup_request_notification sent to configured staff groups
3. **Customer Assignment**: pickup_confirmation sent with management portal access
4. **Reminder System**: pickup_confirmation_reminder sent 48 hours before (if unconfirmed)
5. **Final Reminder**: pickup_final_reminder sent 48 hours before (if confirmed)

**Customer Actions**:
- **Confirmation**: pickup_final_reminder template sent immediately
- **Cancellation**: pickup_cancellation template sent to customer and staff
- **Modifications**: Staff notifications sent when customers edit pickup details

**Staff Controls**:
- **Manual Sending**: Staff can trigger email templates on-demand
- **Template Preview**: Preview emails before sending with actual data
- **Delivery Tracking**: Monitor email delivery status and failures
- **Custom Messages**: Add custom content to template-based emails

#### Integration with NotificationService

**Unified Notifications**:
- Email templates integrate with ETRFlow2's comprehensive notification system
- Browser push notifications sent alongside email notifications
- In-app notifications for staff when customers take actions
- User group targeting for staff notifications

**Notification Preferences**:
- Staff can configure notification preferences by type
- Email frequency controls (immediate, daily digest, weekly summary)
- Notification routing based on pickup complexity and customer type
- Emergency notification escalation for urgent pickups

#### Configuration and Management

**Email Template Administration**:
- **Template Editor**: Web-based editor for modifying email content
- **Variable Reference**: Built-in documentation of available variables
- **Preview System**: Test emails with sample data before deployment
- **Version Control**: Track template changes and rollback capabilities
- **A/B Testing**: Test different template versions for effectiveness

**SMTP Configuration**:
- **Multiple Providers**: Support for various email service providers
- **Delivery Tracking**: Monitor email delivery rates and bounce handling
- **Queue Management**: Email sending through Laravel's queue system
- **Rate Limiting**: Prevent email flooding with configurable limits

**Compliance Features**:
- **Unsubscribe Handling**: Automatic unsubscribe link generation
- **Privacy Protection**: Customer email preferences respected
- **Audit Trail**: Complete log of all email communications
- **GDPR Compliance**: Customer data handling in email communications

#### Troubleshooting Email Issues

**Common Problems**:
- **Delivery Failures**: SMTP configuration, recipient blocking, content filtering
- **Template Errors**: Variable substitution failures, formatting issues
- **Timing Issues**: Reminder scheduling, timezone handling, queue delays
- **Customer Access**: Token expiration, email client security, spam filtering

**Diagnostic Tools**:
- **Email Queue Monitoring**: Track queued, sent, and failed emails
- **Template Testing**: Send test emails with sample data
- **Delivery Reports**: Comprehensive email delivery analytics
- **Error Logging**: Detailed logs of email processing errors

**Email Confirmations**:
- Guest submissions receive immediate confirmation emails with request details
- Status changes trigger notification emails to customers
- Staff receive alerts for new incoming requests requiring attention
- Pickup reminders sent before scheduled appointments via automated system

**Internal Alerts**:
- Dashboard notifications for pending requests requiring action
- Calendar alerts for upcoming confirmed pickups
- Status change notifications for team coordination
- Overdue request warnings for follow-up
- Auto-save notifications for staff forms
- Real-time browser push notifications for customer actions

## Distance Integration and Route Planning

### Overview

The pickup system includes comprehensive distance integration with Google Maps to provide accurate route planning, travel time estimates, and daily pickup optimization. This helps staff plan efficient pickup routes and provides customers with realistic scheduling expectations.

### Distance Calculation Features

#### Automatic Distance Calculation
- **Real-Time Calculation**: Distances calculated automatically when pickup addresses are entered
- **Google Maps Integration**: Uses Google Maps Distance Matrix API for accurate results
- **Warehouse Integration**: Calculates distance from configured warehouse location to pickup address
- **Multiple Route Options**: Considers traffic conditions and route alternatives
- **Database Storage**: Calculated distances stored for reporting and route optimization

#### Travel Time Estimation
- **Traffic-Aware**: Accounts for current and predicted traffic conditions
- **Time-of-Day Factors**: Considers pickup time for accurate travel estimates
- **Route Optimization**: Suggests optimal routes for multiple pickups
- **Buffer Time Integration**: Incorporates travel time into scheduling availability

### Implementation Details

#### Database Integration
```php
// PickupRequest model fields for distance
'distance_miles' => decimal(8,2) // Distance from warehouse in miles
'travel_time_minutes' => integer // Estimated travel time in minutes
'distance_calculated_at' => timestamp // When distance was last calculated
'route_notes' => text // Special routing instructions or considerations
```

#### API Endpoints
- `GET /pickup-requests/{id}/distance` - Calculate distance for specific pickup
- `POST /pickup-requests/batch-distance` - Calculate distances for multiple pickups
- `GET /pickup-requests/route-optimization` - Optimize route for daily pickups

#### Configuration
- **Warehouse Address**: Configured in Global Config for consistent calculations
- **Google Maps API**: Requires Google Maps API key with Distance Matrix access
- **Calculation Triggers**: Automatic calculation on address changes
- **Caching**: Results cached to minimize API usage and costs

## Daily PDF Report System

### Overview

The daily PDF report system generates comprehensive pickup reports with route planning, distance calculations, and pickup details. These reports help drivers and office staff coordinate daily pickup operations efficiently.

### Report Features

#### Comprehensive Pickup Information
- **Daily Schedule**: All pickups scheduled for selected date
- **Customer Details**: Contact information, business names, and customer notes
- **Pickup Specifics**: Load sizes, item types, accessibility levels, and driver instructions
- **Distance and Travel**: Calculated distances and estimated travel times
- **Route Optimization**: Suggested pickup order for efficient routing
- **Status Tracking**: Current status of each pickup with confirmation details

#### Professional PDF Generation
- **Company Branding**: Consistent branding with company logo and styling
- **Mobile-Friendly**: Optimized for viewing on mobile devices and tablets
- **Print-Ready**: High-quality formatting for physical printing
- **Digital Delivery**: Email delivery to drivers and office staff
- **Archive Capability**: Historical report storage and retrieval

### Report Sections

#### Header Information
- **Report Date**: Date range covered by the report
- **Generation Time**: When the report was created and by whom
- **Summary Statistics**: Total pickups, estimated mileage, and time requirements
- **Emergency Contacts**: Key contact information for day-of coordination

#### Pickup Details
For each scheduled pickup:
- **Pickup Time**: Scheduled start time with buffer considerations
- **Customer Information**: Name, business, phone, email
- **Location Details**: Full address, property location specifics, access notes
- **Item Information**: Load size, item types, detailed specifications
- **Distance Data**: Miles from warehouse, estimated travel time
- **Driver Instructions**: Special requirements, security codes, contact preferences
- **Status Indicators**: Confirmation status, reminder history

#### Route Planning Section
- **Optimal Routing**: Suggested pickup order to minimize travel time
- **Total Distance**: Combined mileage for all pickups
- **Time Estimates**: Total time including travel and pickup duration
- **Map Integration**: Optional route maps for complex pickup days
- **Alternative Routes**: Backup routing suggestions for traffic issues

#### Unconfirmed Pickups Section
- **Pending Confirmations**: Pickups awaiting customer confirmation
- **Contact Instructions**: How to reach customers for last-minute confirmations
- **Backup Plans**: Alternative arrangements if pickups are cancelled
- **Priority Indicators**: High-priority or time-sensitive pickups highlighted

### Report Generation

#### Access and Permissions
- **Route**: `/pickup-requests/daily-pdf` with date parameter
- **Permissions**: Requires `view_pickup_requests` permission
- **Date Flexibility**: Generate reports for any date (past, present, future)
- **Filtering Options**: Filter by status, customer type, or driver assignment

#### Automated Generation
- **Scheduled Reports**: Automatic daily report generation and email delivery
- **Staff Distribution**: Configurable email lists for report distribution
- **Format Options**: PDF download, email attachment, or browser viewing
- **Archive Integration**: Automatic storage of generated reports for reference

#### Customization Options
- **Report Layouts**: Multiple report formats for different use cases
- **Data Selection**: Choose which pickup details to include
- **Branding Options**: Customize company branding and styling
- **Export Formats**: PDF, Excel, or CSV export options

### Integration with Route Optimization

#### Smart Routing Features
- **Geographic Clustering**: Group nearby pickups for efficient routing
- **Time Window Optimization**: Consider pickup time preferences and availability
- **Vehicle Capacity**: Factor in load sizes for vehicle assignment
- **Driver Preferences**: Account for driver familiarity with areas
- **Traffic Patterns**: Incorporate historical traffic data for better timing

#### Real-Time Adjustments
- **Dynamic Reordering**: Adjust route order based on cancellations or additions
- **Traffic Updates**: Real-time traffic conditions affecting route timing
- **Customer Changes**: Accommodate last-minute customer requests
- **Emergency Prioritization**: Handle urgent pickups with route modification

### Performance and Optimization

#### API Usage Optimization
- **Batch Processing**: Calculate multiple distances in single API calls
- **Caching Strategy**: Cache distance calculations to reduce API costs
- **Smart Updates**: Recalculate only when addresses change significantly
- **Fallback Options**: Provide manual distance entry if API is unavailable

#### Report Performance
- **Efficient Queries**: Optimized database queries for large datasets
- **Parallel Processing**: Generate report sections concurrently
- **Caching**: Cache generated reports for repeated access
- **Progressive Loading**: Stream report generation for large reports

## Future Enhancements

### Advanced Scheduling Features

**Capacity Management**:
- Multiple pickup slots per time period
- Load size-based capacity calculations
- Geographic route optimization
- Resource allocation (trucks, staff) integration

**Smart Scheduling**:
- AI-powered optimal time suggestions
- Customer preference learning
- Seasonal demand prediction
- Dynamic pricing based on demand

### Enhanced Item Tracking

**Inventory Integration**:
- Pre-populate item types from customer history
- Estimated value calculations
- Special handling requirements
- Certification tracking for data destruction

**Photo Documentation**:
- Item photos during pickup request creation
- Before/after pickup documentation
- Condition assessment integration
- Visual inventory verification

## Administration

### Initial Setup

#### Prerequisites
1. **Calendar Creation**: Create a public calendar dedicated to pickup scheduling
2. **User Permissions**: Ensure staff have appropriate pickup request permissions:
   - `view_pickup_requests`: View pickup requests and calendar
   - `manage_pickup_requests`: Create, edit, and process pickup requests
3. **Google Maps API**: Configure API key for address autocomplete functionality
4. **Email Configuration**: Set up email templates for pickup confirmations and notifications

#### Global Configuration

**Required Settings** (Global Config → Calendar Settings):
- ✅ **Pickup Calendar**: Select the calendar for pickup events (required)
- ✅ **Event Duration**: Default pickup duration (15-480 minutes, recommended: 60 minutes)
- ✅ **Scheduling Interval**: Time slot granularity (5-120 minutes, recommended: 30 minutes)

**Scheduling Settings**:
- ✅ **Availability Windows**: Configure business hours per day of week (JSON format)
- ✅ **Lead Time Days**: Minimum advance notice in days (0-30, recommended: 1)
- ✅ **Lead Time Hours**: Additional hours beyond days (0-23, recommended: 0)
- ✅ **Business Days Only**: Count only weekdays for lead time (recommended: enabled)
- ✅ **Buffer Time**: Gap between appointments (0-240 minutes, recommended: 30)
- ✅ **Buffer Direction**: Buffer placement (after/before/both, recommended: after)

**Advanced Options**:
- ⚪ **Multiple Windows**: Morning/afternoon availability periods per day
- ⚪ **Extended Lead Time**: Longer notice for complex pickups (2+ days)
- ⚪ **Bidirectional Buffer**: Buffer time before and after events
- ⚪ **Custom Availability**: Different windows for different days of week

### Operational Management

#### Daily Operations
- **Monitor Incoming**: Check `/pickup-requests` for new guest requests requiring customer assignment
- **Process Pending**: Contact customers to confirm pending pickup appointments
- **Update Status**: Mark requests as confirmed after customer contact using action buttons
- **Calendar Review**: Monitor pickup calendar for scheduling conflicts or changes
- **Draft Management**: Review and clear auto-saved drafts for staff forms

#### Request Processing Workflow
1. **Incoming Requests**: Review guest submissions for completeness and data quality
2. **Customer Assignment**: Use step-by-step wizard to link to existing customers or create new profiles
3. **Time Confirmation**: Verify pickup scheduling meets customer needs and availability
4. **Event Creation**: Convert to calendar events with staff assignments and driver selection
5. **Customer Contact**: Confirm details and provide pickup information via email/phone
6. **Status Tracking**: Monitor through confirmed → completed lifecycle with status updates

#### Quality Assurance
- **Data Completeness**: Ensure all guided pickup fields are properly filled (load size, item types, specifics)
- **Customer Accuracy**: Verify customer linking and contact information matches
- **Scheduling Validation**: Confirm pickup times respect availability windows and lead time requirements
- **Item Details**: Review load size and item type selections for accuracy and completeness
- **Address Validation**: Verify pickup addresses are complete and accessible
- **Staff Assignments**: Ensure appropriate driver and staff assignments for pickup complexity

### Staff Training

#### Core Concepts
- **Request-First Approach**: All pickups start as pickup requests, not calendar events
- **Guided Item Collection**: Use structured load size and item type fields instead of free-form text
- **Status Workflow**: Understand incoming → pending → confirmed → completed progression
- **Calendar Integration**: Pickup events display request data, not event fields
- **Auto-Save Functionality**: Forms automatically save drafts during editing

#### Key Skills
- **Customer Assignment**: Use step-by-step wizard to link guest requests to customer profiles
- **Customer Search**: Use autocomplete search functionality to find existing customers
- **Form Navigation**: Navigate multi-step forms with auto-save and guided item details
- **Status Management**: Progress requests through workflow stages using action buttons
- **Calendar Usage**: View and manage pickup events in calendar context with week view
- **Draft Management**: Understand auto-save drafts and clear functionality

#### Best Practices
- **Complete Information**: Fill all guided pickup fields (load size, item types, specifics, accessibility)
- **Customer Communication**: Confirm details via email/phone before marking requests as confirmed
- **Timely Processing**: Process incoming requests within 24 hours of submission
- **Data Consistency**: Use pickup requests as single source of truth for all pickup information
- **Accurate Customer Linking**: Verify customer information matches before assignment
- **Clear Communication**: Use driver instructions field for special pickup requirements

## Troubleshooting

### Common Issues

#### Calendar and Display Issues
- **Calendar Not Showing**: Verify pickup calendar is selected in Global Config → Calendar Settings
- **Events Not Displaying**: Check that pickup events have linked pickup requests (legacy events are hidden)
- **Availability Windows Not Working**: Verify JSON format in global config and check browser console
- **Time Slots Not Appearing**: Check lead time and buffer time configurations, verify availability windows
- **Calendar Loading Slowly**: Check for large numbers of events, consider archiving old pickup requests

#### Form and Data Issues
- **Auto-Save Not Working**: Verify CSRF token and check browser console for JavaScript errors
- **Customer Data Not Populating**: Check customer profile completeness and user permissions
- **Address Autocomplete Failing**: Verify Google Maps API key configuration and country restrictions
- **Item Details Not Saving**: Check guided pickup field validation rules and required field completion
- **Draft Not Loading**: Check auto-save functionality and clear draft if corrupted

#### Scheduling Conflicts
- **Double-Booking**: Verify buffer time settings and availability window configuration
- **Past Time Slots Showing**: Check lead time requirements and business days settings
- **Guest Requests Not Appearing**: Verify pickup request permissions and status workflow
- **Availability Checking Errors**: Check GlobalConfig settings and database connectivity
- **Time Zone Issues**: Verify timezone configuration in Global Config

#### Permission Issues
- **Access Denied**: Ensure users have `view_pickup_requests` or `manage_pickup_requests` permissions
- **Cannot Edit Requests**: Check `manage_pickup_requests` permission assignment
- **Calendar Access Issues**: Verify calendar sharing settings and user permissions
- **Customer Assignment Failing**: Check customer management permissions and database constraints

### Diagnostic Steps

#### For Scheduling Issues
1. Check Global Config → Calendar Settings for all pickup-related configurations
2. Verify availability windows are properly formatted JSON (use JSON validator)
3. Test lead time calculation with business days setting using different dates
4. Review buffer time settings and direction (after/before/both)
5. Check pickup calendar ID is set and calendar exists
6. Verify timezone settings match server and user expectations

#### For Data Issues
1. Check pickup request database records for completeness and data integrity
2. Verify customer linking and profile data accuracy
3. Review guided pickup field validation in browser console for JavaScript errors
4. Test auto-save functionality with network monitoring tools
5. Check CSRF token validity and session management
6. Verify Google Maps API key and quota limits

#### For Permission Issues
1. Verify user has appropriate pickup request permissions (`view_pickup_requests`, `manage_pickup_requests`)
2. Check calendar sharing settings for pickup calendar
3. Review role assignments and permission inheritance
4. Test with different user accounts to isolate permission vs. system issues
5. Check database constraints and foreign key relationships

### Performance Optimization

#### Database Optimization
- **Index Usage**: Ensure proper indexing on pickup_requests table (customer_id, status, preferred_pickup_date)
- **Query Optimization**: Monitor slow queries during availability checking and calendar loading
- **Data Cleanup**: Archive completed pickup requests older than 1 year to improve performance
- **Connection Pooling**: Optimize database connections for high-traffic periods

#### Frontend Performance
- **JavaScript Loading**: Optimize pickup request form JavaScript loading and minimize bundle size
- **AJAX Requests**: Monitor availability checking API response times and implement caching
- **Calendar Rendering**: Optimize FullCalendar event loading for large datasets with pagination
- **Mobile Optimization**: Ensure responsive design performs well on mobile devices

## Routes and API Endpoints

### Guest Routes (Public Access)
- `GET /pickup-request` - Display guest pickup request form
- `POST /pickup-request` - Submit guest pickup request
- `GET /pickup-request/available-dates` - Get available pickup dates (AJAX)
- `GET /pickup-request/time-slots` - Get available time slots for specific date (AJAX)
- `GET /pickup-request/lead-time-info` - Get lead time information for display (AJAX)
- `POST /pickup-request/upload-image` - Upload images during guest form submission
- `DELETE /pickup-request/delete-image/{id}` - Delete uploaded images during guest form

### Customer Portal Routes (Token-Based Access)
- `GET /pickup/manage/{token}` - Customer appointment management portal
- `GET /pickup/confirm/{token}` - Direct appointment confirmation from emails
- `GET /pickup/confirm-success/{token}` - Confirmation success page with details
- `GET /pickup/manage/{token}/edit` - Customer edit interface for pickup details
- `POST /pickup/manage/{token}/update` - Update pickup details via customer portal
- `POST /pickup/manage/{token}/upload-image` - Customer image uploads
- `POST /pickup/manage/{token}/cancel` - Customer cancellation with optional reason

### Staff Routes (Authenticated)

#### Pickup Request Management
- `GET /pickup-requests` - List pickup requests with filtering and search
- `GET /pickup-requests/create` - Create new pickup request form
- `POST /pickup-requests` - Store new pickup request
- `GET /pickup-requests/{id}` - Show pickup request details with activity log
- `GET /pickup-requests/{id}/edit` - Edit pickup request form
- `PUT /pickup-requests/{id}` - Update pickup request
- `DELETE /pickup-requests/{id}` - Delete pickup request (with confirmation)

#### Auto-Save and Drafts
- `POST /pickup-requests/auto-save` - Auto-save form data as draft (real-time)
- `POST /pickup-requests/clear-draft` - Clear saved draft data
- `GET /pickup-requests/draft-status` - Check draft status for forms

#### Customer Assignment Workflow
- `GET /pickup-requests/{id}/step1` - Customer identification step with search
- `GET /pickup-requests/{id}/search-customers` - AJAX customer search with autocomplete
- `POST /pickup-requests/{id}/assign-customer` - Assign customer to request
- `GET /pickup-requests/{id}/step2` - Scheduling step with calendar integration
- `POST /pickup-requests/{id}/schedule` - Create calendar event from request

#### Status Management
- `POST /pickup-requests/{id}/confirm` - Mark request as confirmed (staff override)
- `POST /pickup-requests/{id}/complete` - Mark request as completed
- `POST /pickup-requests/{id}/cancel` - Cancel pickup request with reason

#### Email and Communication
- `POST /pickup-requests/{id}/send-confirmation-email` - Send confirmation email manually
- `GET /pickup-requests/{id}/preview-email/{template}` - Preview email templates
- `POST /pickup-requests/{id}/resend-reminder` - Resend reminder emails

#### Image Management
- `POST /pickup-requests/{id}/upload-image` - Staff image uploads
- `DELETE /pickup-requests/{id}/delete-image/{image}` - Delete specific images
- `GET /pickup-requests/{id}/images` - Get all images for modal gallery
- `GET /pickup-requests/{id}/image-thumbnail/{image}/{size}` - Get thumbnail images

#### Reporting and Analytics
- `GET /pickup-requests/daily-pdf` - Generate daily pickup PDF report
- `GET /pickup-requests/export` - Export pickup requests to CSV/Excel
- `GET /pickup-requests/analytics` - Pickup request analytics dashboard

#### Distance and Route Planning
- `GET /pickup-requests/{id}/distance` - Calculate distance from warehouse
- `POST /pickup-requests/batch-distance` - Calculate distances for multiple requests
- `GET /pickup-requests/route-optimization` - Route optimization suggestions

#### Calendar Integration
- `GET /pickup-requests/{id}/week-view` - Get week view data for specific pickup
- `GET /pickup-requests/general-week-view` - General week view for scheduling
- `GET /pickup-calendar` - Display pickup calendar interface
- `GET /pickup-calendar/events` - AJAX endpoint for calendar events

### Console Commands (Artisan)

#### Automated Reminder System
- `php artisan pickups:send-reminders` - Send 48-hour confirmation and final reminders
- `php artisan pickups:send-confirmation-email {pickup_request_id}` - Send individual confirmation email

#### Maintenance and Cleanup
- `php artisan pickups:cleanup-expired-tokens` - Remove expired management tokens
- `php artisan pickups:cleanup-temporary-images` - Clean up abandoned guest image uploads
- `php artisan pickups:generate-missing-thumbnails` - Regenerate missing image thumbnails

#### Reporting and Analytics
- `php artisan pickups:daily-report` - Generate and email daily pickup reports
- `php artisan pickups:analytics-summary` - Generate pickup analytics summaries

#### Data Migration and Maintenance
- `php artisan pickups:migrate-legacy-data` - Convert legacy pickup data to guided fields
- `php artisan pickups:recalculate-distances` - Recalculate distances for all pickup requests

### API Response Formats

#### Availability Checking
```json
{
  "available_dates": [
    {
      "date": "2024-01-15",
      "slots_available": 8,
      "day_name": "Monday"
    }
  ],
  "time_slots": [
    {
      "time": "09:00",
      "display": "9:00 AM",
      "available": true
    }
  ]
}
```

#### Pickup Request Data
```json
{
  "id": 123,
  "status": "pending",
  "contact_name": "John Doe",
  "business_name": "Acme Corp",
  "email": "<EMAIL>",
  "phone": "(*************",
  "pickup_address": "123 Main St, City, State 12345",
  "property_location_details": "Loading dock on south side",
  "other_notes": "Security code: 1234",
  "load_size": "small",
  "item_types": ["laptops", "desktops", "small_electronics"],
  "item_specifics": "5 laptops, 3 desktop computers",
  "driver_instructions": "Call when arriving",
  "accessibility_level": "easy",
  "preferred_pickup_date": "2024-01-15T09:00:00Z",
  "customer_id": 456,
  "event_id": 789,
  "management_token": "AbC123XyZ789DeF456GhI",
  "management_token_expires_at": "2024-03-15T09:00:00Z",
  "confirmed_at": null,
  "reminder_sent_at": "2024-01-13T09:00:00Z",
  "final_reminder_sent_at": null,
  "distance_miles": 12.5,
  "travel_time_minutes": 18,
  "images": [
    {
      "id": 1,
      "filename": "equipment.jpg",
      "file_size": 245760,
      "uploaded_by_type": "guest",
      "created_at": "2024-01-10T14:30:00Z"
    }
  ],
  "activity_log": [
    {
      "action": "created",
      "user_name": "Guest Submission",
      "timestamp": "2024-01-10T14:30:00Z",
      "details": "Pickup request submitted via guest form"
    }
  ]
}
```

#### Image Upload Response
```json
{
  "success": true,
  "image": {
    "id": 15,
    "filename": "equipment_photo.jpg",
    "file_size": 245760,
    "mime_type": "image/jpeg",
    "uploaded_by_type": "staff",
    "thumbnails": {
      "sm": "/pickup-request-images/thumbnails/sm/15_equipment_photo.webp",
      "md": "/pickup-request-images/thumbnails/md/15_equipment_photo.webp",
      "lg": "/pickup-request-images/thumbnails/lg/15_equipment_photo.webp"
    },
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

#### Distance Calculation Response
```json
{
  "pickup_request_id": 123,
  "distance_miles": 12.5,
  "travel_time_minutes": 18,
  "warehouse_address": "456 Industrial Blvd, City, State",
  "pickup_address": "123 Main St, City, State 12345",
  "calculated_at": "2024-01-15T09:00:00Z"
}
```

#### Email Template Preview Response
```json
{
  "template_name": "pickup_confirmation",
  "subject": "Your pickup appointment is scheduled - ETRFlow",
  "html_content": "<html>...</html>",
  "text_content": "Your pickup appointment...",
  "variables_used": [
    "contact_name",
    "preferred_pickup_date",
    "management_url"
  ]
}
```

### Support Resources

- **Development Team**: Contact ETRFlow developers for technical issues and system modifications
- **User Documentation**: Refer to main application documentation for general system usage
- **Training Materials**: Access staff training guides for workflow questions and best practices
- **Configuration Help**: Review Global Config documentation for setup assistance and troubleshooting
- **API Documentation**: Reference Google Maps API documentation for address autocomplete issues
