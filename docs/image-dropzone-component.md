# Image Dropzone Component

## Overview

The Image Dropzone Component is a reusable, feature-rich file upload system built for ETRFlow2. It provides drag-and-drop functionality, client-side image compression, mobile camera integration, and thumbnail generation with proper permission-based security.

## Architecture

### Component Structure

```
resources/views/components/image-dropzone.blade.php  # Blade component
public/js/image-dropzone.js                         # JavaScript logic
routes/web.php                                       # File access routes
app/Http/Controllers/FileController.php             # File handling
app/Models/File.php                                  # File model with permissions
```

### Key Features

- **Drag & Drop Interface**: Modern dropzone UI with visual feedback
- **Client-Side Compression**: Automatic WebP conversion with size optimization
- **Mobile Camera Integration**: Direct camera access on mobile devices
- **Permission-Based Security**: Secure file access with user permission checks
- **Thumbnail Generation**: Cached thumbnail generation with proper headers
- **Multiple Upload Modes**: Session-based or form-based file handling
- **Filename Preservation**: Maintains original filenames with proper extensions

## Usage

### Basic Usage

```blade
<x-image-dropzone
    id="my-dropzone"
    name="images[]"
    :max-files="5"
    :max-filesize="10"
    label="Upload Images"
    help-text="Drag and drop your images here"
/>
```

### Advanced Configuration

```blade
<x-image-dropzone
    id="mobile-photos"
    name="item_photos[]"
    :max-files="10"
    :max-filesize="5"
    :max-width="1250"
    :max-height="1250"
    :client-resize="true"
    accepted-formats="image/jpeg,image/jpg,image/png,image/webp"
    :multiple="true"
    capture="environment"
    :trigger-camera-upload="true"
    :session-based="false"
    label="Item Photos"
    help-text="Take photos of items being processed"
    sub-text="Images will be automatically compressed"
/>
```

## Component Parameters

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Unique identifier for the dropzone instance |
| `name` | string | Form field name for file uploads |

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `maxFiles` | integer | 5 | Maximum number of files allowed |
| `maxFilesize` | integer | 10 | Maximum file size in MB |
| `maxWidth` | integer | null | Maximum image width for compression |
| `maxHeight` | integer | null | Maximum image height for compression |
| `clientResize` | boolean | true | Enable client-side image compression |
| `acceptedFormats` | string | 'image/jpeg,image/jpg,image/png,image/webp' | Allowed file MIME types |
| `existingImages` | array | [] | Pre-loaded images to display |
| `sessionBased` | boolean | false | Use session-based temporary uploads |
| `sessionId` | string | null | Session ID for temporary uploads |
| `uploadUrl` | string | null | Custom upload endpoint URL |
| `deleteUrl` | string | null | Custom delete endpoint URL |
| `multiple` | boolean | true | Allow multiple file selection |
| `capture` | string | 'environment' | Camera mode (environment/user) |
| `triggerCameraUpload` | boolean | false | Auto-trigger camera on mobile |
| `label` | string | 'Upload Photos' | Display label for the component |
| `helpText` | string | 'Drop images here or click to upload' | Help text shown in dropzone |
| `subText` | string | null | Additional informational text |

## Client-Side Processing

### Image Compression

The component uses the `browser-image-compression` library to:

1. **Resize Images**: Automatically resize to specified max dimensions
2. **Convert Format**: Convert all images to WebP for optimal file size
3. **Preserve Quality**: Maintain 85% quality for good visual fidelity
4. **Maintain Filenames**: Create proper File objects with original names

```javascript
const options = {
    maxSizeMB: Math.min(this.config.maxFilesize, 2),
    maxWidthOrHeight: Math.max(this.config.maxWidth || 0, this.config.maxHeight || 0),
    useWebWorker: true,
    fileType: file.type,
    initialQuality: 0.85
};

const compressedBlob = await imageCompression(file, options);

// Create proper File object with preserved filename
const originalExtension = file.name.split('.').pop();
const nameWithoutExtension = file.name.replace('.' + originalExtension, '');
const newFileName = nameWithoutExtension + '.webp';

processedFile = new File([compressedBlob], newFileName, {
    type: 'image/webp',
    lastModified: Date.now()
});
```

### Mobile Camera Integration

For mobile devices, the component can automatically trigger the camera:

```blade
:trigger-camera-upload="true"
capture="environment"
```

This enables:
- Automatic camera activation on page load (mobile only)
- Environment camera preference (back camera)
- Single-file mode optimization for mobile
- Touch-friendly interface adjustments

## File Handling & Security

### Permission System

File access is controlled through the `File` model's `userCanAccess()` method:

```php
public function userCanAccess()
{
    // Public files are accessible to everyone
    if ($this->is_public) {
        return true;
    }

    // Admin users can access all files
    if (Auth::user() && Auth::user()->isAdmin()) {
        return true;
    }

    // File uploaders can access their own files
    if (Auth::id() === $this->uploaded_by) {
        return true;
    }

    // Check parent model permissions (e.g., Invoice model)
    $parent = $this->fileable;
    if ($parent && method_exists($parent, 'userCanAccessFiles')) {
        return $parent->userCanAccessFiles(Auth::user());
    }

    return false;
}
```

### File Routes

```php
// File download and viewing
Route::get('/files/{file}/download', [FileController::class, 'download'])->name('files.download');
Route::get('/files/{file}/view', [FileController::class, 'view'])->name('files.view');

// Optimized thumbnail generation
Route::get('/files/{file}/thumbnail/{width?}/{height?}', [FileController::class, 'thumbnail'])->name('files.thumbnail');
```

### Thumbnail Generation

The `thumbnail` route provides optimized image serving:

```php
public function thumbnail(Request $request, File $file, $width = 150, $height = 150)
{
    // Permission check
    if (!$file->userCanAccess()) {
        abort(403, 'You do not have permission to access this file');
    }

    // Image validation
    if (!$file->is_image) {
        abort(404, 'File is not an image');
    }

    // Generate and cache thumbnail
    $response = $this->fileService->getResizedImage($file, $width, $height);
    
    // Add cache headers (1 hour)
    $response->headers->set('Cache-Control', 'public, max-age=3600');
    $response->headers->set('Expires', gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
    
    return $response;
}
```

## Implementation Examples

### Mobile Payout System

```blade
<!-- resources/views/mobile/payouts/step3.blade.php -->
<x-image-dropzone
    id="mobile-payout-photos"
    name="item_photos[]"
    :max-files="10"
    :max-filesize="10"
    :max-width="1250"
    :max-height="1250"
    :client-resize="true"
    accepted-formats="image/jpeg,image/jpg,image/png,image/webp"
    :multiple="true"
    capture="environment"
    :trigger-camera-upload="true"
    label="Item Photos (Optional)"
    help-text="Take photos of the items being paid for"
    sub-text="Images will be automatically compressed"
/>
```

### Pickup Request System

```blade
<!-- resources/views/guest/pickup-request-components/step-item-details.blade.php -->
<x-image-dropzone
    id="pickup-item-photos"
    name="photos[]"
    :max-files="5"
    :max-filesize="5"
    :max-width="1250"
    :max-height="1250"
    :client-resize="true"
    :session-based="true"
    session-id="{{ session()->getId() }}"
    upload-url="{{ route('guest.pickup-request.upload-image') }}"
    delete-url="{{ route('guest.pickup-request.delete-image', ['id' => '{id}']) }}"
    label="Item Photos"
    help-text="Upload photos of items for pickup"
/>
```

### Invoice Attachments Display

```blade
<!-- resources/views/invoices/show.blade.php -->
@foreach($invoice->files as $file)
    @if(in_array($file->extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']))
        <div class="w-full h-40 overflow-hidden rounded-md border border-base-300 mb-3">
            <img src="{{ $file->getThumbnailUrl(300, 160) }}" 
                 alt="{{ $file->description }}" 
                 class="h-full w-full object-cover object-center">
        </div>
    @endif
@endforeach
```

## Upload Modes

### Form-Based Upload (Default)

Files are processed client-side and submitted with the parent form:

- Files are compressed and added to form data
- FormData intercepts form submission when files are present
- All files submitted together with form
- Ideal for: Mobile payouts, customer forms

### Session-Based Upload

Files are uploaded immediately to temporary storage:

- Each file is uploaded individually via AJAX
- Session ID tracks temporary files
- Files are associated with final record on form submission
- Ideal for: Multi-step forms, guest submissions

## Error Handling

### Client-Side Validation

- File type validation against accepted formats
- File size validation against maximum limits
- File count validation against maximum files
- Compression error handling with fallback to original files

### Server-Side Security

- File extension validation
- MIME type verification
- File size limits enforcement
- Permission-based access control
- Virus scanning integration points

## Performance Optimizations

### Client-Side

- **Web Workers**: Image compression uses web workers to prevent UI blocking
- **Lazy Loading**: Only load compression library when needed
- **File Batching**: Process multiple files efficiently
- **Progress Feedback**: Visual indicators for compression progress

### Server-Side

- **Thumbnail Caching**: Generated thumbnails are cached with proper headers
- **Response Headers**: Cache-Control and Expires headers reduce server load
- **Image Optimization**: Server-side WebP conversion with quality optimization
- **Storage Efficiency**: Private storage for security, public URLs for performance

## Browser Compatibility

### Desktop Support
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Mobile Support
- iOS Safari 12+
- Chrome Mobile 60+
- Samsung Internet 8+
- UC Browser 12+

### Required APIs
- File API
- Blob API
- FormData API
- Fetch API
- Web Workers (for compression)

## Troubleshooting

### Common Issues

1. **Files not uploading**
   - Check form `enctype="multipart/form-data"`
   - Verify server file size limits
   - Check browser console for JavaScript errors

2. **Thumbnails not displaying**
   - Verify user permissions on files
   - Check thumbnail route accessibility
   - Confirm file is recognized as image type

3. **Compression failures**
   - Check browser support for Web Workers
   - Verify browser-image-compression library loading
   - Fall back to original files if compression fails

4. **Mobile camera not triggering**
   - Requires HTTPS in production
   - Check mobile browser permissions
   - Verify `capture` attribute support

### Debug Information

Enable debug logging by temporarily adding console.log statements:

```javascript
// In image-dropzone.js
console.log('Files being processed:', this.files);
console.log('Compression options:', options);
console.log('Form submission data:', formData);
```

## Future Enhancements

### Planned Features
- **Drag Reordering**: Allow users to reorder uploaded images
- **Image Cropping**: Built-in image cropping interface
- **EXIF Data Handling**: Preserve or strip EXIF data as needed
- **Progressive Upload**: Resume interrupted uploads
- **Batch Operations**: Select and delete multiple images at once

### Integration Opportunities
- **AI Image Analysis**: Automatic image categorization and tagging
- **Cloud Storage**: Direct upload to cloud storage providers
- **Image Filters**: Apply filters and effects before upload
- **Metadata Extraction**: Extract and store image metadata

## Related Documentation

- [File System Architecture](file-system.md)
- [Mobile Payout System](mobile-payouts.md)
- [Pickup Request System](pickups.md)
- [Permission System](permissions.md)
- [Style Guide](STYLE_GUIDE.md)