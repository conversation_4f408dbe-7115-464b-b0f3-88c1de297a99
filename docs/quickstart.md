# ETRFlow2 Quickstart Guide

## Initial Setup

### 1. Environment Configuration

Copy the example environment file and configure your database settings:

```bash
cp .env.example .env
```

Edit `.env` file with your database credentials and other settings:
- Database connection (MariaDB recommended)
- Python executable path for PDF operations
- Google Maps API configuration
- OpenAI API settings for AI features
- Mail configuration for notifications

### 2. Install Dependencies

```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install

# Install Python dependencies
pip3 install -r scripts/requirements.txt

```



### 3. Application Setup

```bash
# Generate application key
php artisan key:generate

# Create symbolic link for storage
php artisan storage:link

# Run database migrations
php artisan migrate

# Seed initial data
php artisan db:seed
```

### 4. Build Assets

```bash
# Development build
npm run dev

# Production build
npm run build
```

## Laravel Task Scheduler Setup

ETRFlow2 includes several automated tasks that need to be scheduled to run automatically:

- **Auto Clock-Out**: Automatically clocks out users who forget to clock out
- **Daily Timeclock Reports**: Sends daily time tracking reports via email
- **System Maintenance**: Various background maintenance tasks

### Setting Up the Scheduler

#### Step 1: Add Laravel Scheduler to Crontab

The Laravel scheduler needs to run every minute to check for scheduled tasks. Add this entry to your system's crontab:

```bash
# Open crontab editor
crontab -e

# Add this line (replace /path/to/etrflow2 with your actual project path):
* * * * * cd /path/to/etrflow2 && php artisan schedule:run >> /dev/null 2>&1
```

**Alternative: One-line setup**
```bash
# From your project directory, run:
echo "* * * * * cd $(pwd) && php artisan schedule:run >> /dev/null 2>&1" | crontab -
```

#### Step 2: Verify Scheduler Setup

Check that the crontab entry was added correctly:
```bash
crontab -l
```

List all scheduled Laravel commands:
```bash
php artisan schedule:list
```

Expected output should show commands like:
- `php artisan inspire` (runs hourly)
- `php artisan timeclock:send-daily-report` (runs daily at configured time)
- `php artisan timeclock:auto-clock-out` (runs daily at configured time + 1 minute)

## Queue Worker Setup (Supervisor)

For the application to process background jobs like sending emails without making users wait, a queue worker must be running continuously. While `php artisan queue:listen` or `php artisan queue:work` is suitable for local development, a more robust solution is needed for production.

**Supervisor** is a process manager for Linux that automatically starts, stops, and restarts the queue worker if it fails.

### Setting Up Supervisor

#### Step 1: Install Supervisor

If you don't have Supervisor installed, you can install it on Ubuntu/Debian with:

```bash
sudo apt-get install supervisor
```

#### Step 2: Create a Configuration File

Create a new configuration file for the ETRFlow worker.

```bash
sudo nano /etc/supervisor/conf.d/etrflow-worker.conf
```

Add the following configuration to the file. **Remember to replace `/path/to/etrflow2` with the actual absolute path to your project and `www-data` with the user your web server runs as.**

```ini
[program:etrflow-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/etrflow2/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=8
redirect_stderr=true
stdout_logfile=/path/to/etrflow2/storage/logs/worker.log
stopwaitsecs=3600
```

-   `command`: The command to run. We use `queue:work` which is more efficient for production.
-   `numprocs`: The number of worker processes to run. `8` is a good starting point, but you may need to adjust this based on your server's CPU cores.
-   `user`: The user that runs the worker. This should be the same user that owns your application files.
-   `stdout_logfile`: Where to log the worker's output.

#### Step 3: Start the Worker

Tell Supervisor to read the new configuration and start the worker processes:

```bash
# Tell Supervisor to read the new config file
sudo supervisorctl reread

# Apply the new configuration
sudo supervisorctl update

# Start the worker processes
sudo supervisorctl start etrflow-worker:*
```

#### Step 4: Verify the Worker is Running

You can check the status of the worker at any time:

```bash
sudo supervisorctl status etrflow-worker:*
```

You should see the status as `RUNNING`. If it's not, check the log file at `/path/to/etrflow2/storage/logs/worker.log` for errors.

## Scheduled Commands Configuration

### Auto Clock-Out System

The auto clock-out feature automatically clocks out users who forget to punch out.

**Configuration:**
1. Navigate to Global Configuration (`/globalconfig/edit`)
2. Go to the "Time Clock" tab
3. Configure:
   - **Enable Auto Clock-Out**: Toggle on/off
   - **Auto Clock-Out Time**: Set the time (e.g., "22:00" for 10:00 PM)

**How it works:**
- The system checks daily at the configured time + 1 minute
- Any users still clocked in or on break are automatically clocked out
- Auto clock-out events are logged and displayed to users as warnings
- Users see notifications when they were auto-clocked out

**Manual execution (for testing):**
```bash
# Test the auto clock-out command
php artisan timeclock:auto-clock-out

# Process auto clock-out for a specific date
php artisan timeclock:auto-clock-out --date=2024-12-20
```

### Daily Timeclock Reports

Automatically sends daily time tracking summaries via email.

**Configuration:**
1. Navigate to Global Configuration (`/globalconfig/edit`)
2. Go to the "Time Clock" tab
3. Configure:
   - **Enable Daily Reports**: Toggle on/off
   - **Report Email**: Email address to receive reports
   - **Report Time**: Time to send daily reports (e.g., "07:00" for 7:00 AM)

**Manual execution:**
```bash
# Send daily report for today
php artisan timeclock:send-daily-report

# Send report for a specific date
php artisan timeclock:send-daily-report --date=2024-12-20
```

## Development Workflow

### Running Development Server

For development, you can run multiple services concurrently:

```bash
# Start Laravel server, queue worker, and Vite dev server
composer run dev
```

This runs:
- `php artisan serve` (Laravel development server)
- `php artisan queue:listen` (Background job processing for development)
- `npm run dev` (Vite asset compilation with hot reload)

**Note:** The `queue:listen` command is for local development. For production, you should use Supervisor to run the queue worker (see the "Queue Worker Setup" section).

### Individual Commands

```bash
# Laravel development server
php artisan serve

# Queue worker (for background jobs)
php artisan queue:listen

# Vite development server (asset compilation)
npm run dev

# Build production assets
npm run build
```

## User Management

### Adding Users to Groups

```bash
# Add user to a permission group
php artisan users:add-user-to-group {email} {group}

# Enable a user account
php artisan users:enable-user

# Sync permissions (run after permission changes)
php artisan permissions:sync
```

## Troubleshooting

### Scheduler Issues

**Check if scheduler is running:**
```bash
# View scheduled commands and their next run times
php artisan schedule:list

# Test a specific command manually
php artisan timeclock:auto-clock-out
```

**Common issues:**
- Crontab not set up correctly
- Wrong file paths in crontab
- PHP path issues (use full path: `/usr/bin/php` instead of `php`)
- Permission issues (ensure web user can execute commands)

**Debug crontab:**
```bash
# Check crontab entries
crontab -l

# Check cron logs (Ubuntu/Debian)
sudo grep CRON /var/log/syslog

# Test cron manually
cd /path/to/etrflow2 && php artisan schedule:run
```

### Configuration Issues

**Clear application cache:**
```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
```

**Check configuration:**
```bash
# View current configuration
php artisan config:show

# Check database connection
php artisan migrate:status
```

### Asset Compilation Issues

```bash
# Clear Node modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Rebuild assets
npm run build

# Clear browser cache and hard refresh
```

## Production Deployment

### Optimization Commands

```bash
# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Install production dependencies only
composer install --no-dev --optimize-autoloader

# Build production assets
npm run build
```

### Scheduler in Production

For production environments, consider:

1. **Monitoring**: Set up monitoring for scheduled tasks
2. **Logging**: Configure proper log rotation for scheduler output
3. **Error Handling**: Set up alerts for failed scheduled tasks
4. **Backup**: Include scheduler configuration in backup procedures

**Production crontab with logging:**
```bash
* * * * * cd /path/to/etrflow2 && php artisan schedule:run >> /var/log/laravel-scheduler.log 2>&1
```

## Security Notes

- Ensure proper file permissions for the application directory
- Keep `.env` file secure and not web-accessible
- Regularly update dependencies for security patches
- Use HTTPS in production
- Configure proper database user permissions
- Set up firewall rules for database access

## Support

For additional help:
- Check the Laravel documentation for scheduler details
- Review application logs in `storage/logs/`
- Use `php artisan tinker` for debugging
- Check the main project documentation in `/docs/`