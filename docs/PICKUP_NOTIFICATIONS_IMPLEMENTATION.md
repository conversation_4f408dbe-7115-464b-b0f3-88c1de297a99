# Pickup Request Notifications Implementation

## Overview
This document describes the implementation of configurable pickup request notifications that are sent when guests submit pickup requests through the guest form.

## Features Implemented

### 1. Global Configuration Settings
- Added a new section in the Calendar settings tab of Global Configuration
- Allows administrators to select which user groups receive pickup request notifications
- User groups are displayed with descriptions and user counts
- Settings are saved as JSON in the global_configs table

### 2. Backend Implementation

#### GlobalConfig Model Updates
- Added `getPickupNotificationUserGroups()` method to retrieve configured user groups
- Added `setPickupNotificationUserGroups()` method to save user group selections
- Uses existing `decodeValue()` and `setValue()` methods for JSON handling

#### GlobalConfigController Updates
- Added pickup notification user groups to the edit method
- Added validation for the new setting in the update method
- Includes user group validation to ensure only valid groups are saved
- Loads all user groups with user counts for the configuration interface

#### GuestPickupController Updates
- Added notification logic to the store method after successful pickup request creation
- Sends notifications to all configured user groups
- Includes comprehensive error handling that doesn't fail the pickup request if notifications fail
- Logs notification success/failure for debugging

### 3. Frontend Implementation

#### Configuration Interface
- Added checkbox interface for selecting user groups
- Shows user group names, descriptions, and user counts
- JavaScript handles checkbox state management and JSON serialization
- Responsive design that works on mobile and desktop

#### Notification Content
- **Title**: "New Pickup Request Submitted"
- **Message**: Includes contact name, business name (if provided), pickup date/time, and address
- **Link**: Direct link to view the pickup request details
- **Urgency**: Normal level
- **Expiration**: 7 days
- **Dismissible**: Yes

## Technical Details

### Database Storage
- Configuration stored in `global_configs` table with key `pickup_notification_user_groups`
- Value is JSON array of user group IDs
- Uses existing GlobalConfig infrastructure

### Error Handling
- Notification failures are logged but don't prevent pickup request submission
- Invalid user group IDs are filtered out during saving
- Graceful handling when no user groups are configured

### Security
- Only valid user group IDs are accepted
- Uses existing permission system for global config access
- Notifications respect user group membership

## Usage Instructions

### For Administrators
1. Navigate to Global Configuration → Calendar tab
2. Scroll to "Pickup Request Notifications" section
3. Select the user groups that should receive notifications
4. Save the configuration

### For Users
- Users in selected groups will automatically receive notifications when guests submit pickup requests
- Notifications appear in the notification system (header bell icon, mobile dock)
- Users can click the notification to view the pickup request details

## Integration Points

### Existing Systems Used
- **Notification System**: Uses `NotificationHelper::notifyUserGroups()`
- **User Groups**: Leverages existing user group system
- **Global Config**: Uses established configuration pattern
- **Pickup Requests**: Integrates with existing pickup request workflow

### Routes Used
- Configuration: `/global-config` (existing)
- Pickup Request View: `/pickup-requests/{id}` (existing)

## Testing Recommendations

1. **Configuration Testing**
   - Verify user groups display correctly in global config
   - Test saving and loading of selected groups
   - Confirm invalid group IDs are filtered out

2. **Notification Testing**
   - Submit a guest pickup request
   - Verify notifications are sent to configured groups
   - Test with no groups configured (should not send notifications)
   - Test with invalid group configurations

3. **Error Handling Testing**
   - Test notification failures don't break pickup request submission
   - Verify proper logging of notification events

## Files Modified

### Backend
- `app/Models/GlobalConfig.php` - Added pickup notification methods
- `app/Http/Controllers/GlobalConfigController.php` - Added configuration handling
- `app/Http/Controllers/GuestPickupController.php` - Added notification logic

### Frontend
- `resources/views/global_config/edit.blade.php` - Added configuration interface

### Dependencies
- Uses existing NotificationHelper class
- Uses existing UserGroup model
- Uses existing notification system infrastructure

## Future Enhancements

Potential improvements that could be added:
- Email notifications in addition to in-app notifications
- Customizable notification templates
- Different notification settings for different pickup types
- Notification scheduling/throttling options
