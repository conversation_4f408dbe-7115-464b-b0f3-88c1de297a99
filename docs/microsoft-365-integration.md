# Microsoft 365 Outlook Calendar Integration

This guide covers the setup and configuration of Microsoft 365 Outlook calendar integration for ETRFlow2's pickup calendar system.

## Overview

The Microsoft 365 integration allows all staff to view pickup events in a **shared Microsoft 365 Outlook calendar**. This is a **read-only, one-way integration** where ETRFlow2 remains the authoritative source for all pickup data. All pickup events are synchronized to a single selected calendar that all staff members can access.

### Key Features

- **Calendar Selection**: Choose any existing Microsoft 365 calendar for syncing
- **Shared Access**: Single calendar that all staff can access and view  
- **One-Way Sync**: ETRFlow2 → Microsoft 365 (read-only integration)
- **Non-Destructive**: Only manages ETRFlow2-created events, leaves other events untouched
- **Automatic Sync**: Pickup events sync to the selected calendar in real-time
- **Rich Event Details**: Customer information, item details, locations, and driver assignments
- **Customizable Settings**: Admin control over what data syncs and when
- **Token Management**: Automatic token refresh for seamless authentication
- **Calendar Management**: Easy calendar switching without losing existing events
- **Comprehensive Logging**: Detailed sync history and error tracking

### How It Works

1. **ETRFlow2 Events Only**: The integration only creates, updates, and deletes events that originated from ETRFlow2
2. **Event Identification**: Each synced event has a unique Microsoft event ID stored in ETRFlow2
3. **Existing Events Safe**: All existing calendar events and manually created events remain completely untouched
4. **Staff Friendly**: Users can continue to add their own personal events, meetings, and appointments to the same calendar
5. **Clear Separation**: ETRFlow2 pickup events are clearly marked with categories and distinctive titles

## Prerequisites

Before setting up the integration, you need:

1. **Microsoft 365 Business/Enterprise Account** with admin access
2. **Azure Active Directory** access to register applications
3. **ETRFlow2 Admin Access** to configure environment variables
4. **Calendar Permissions**: Admin account used for setup must have access to the target calendar

## Setup Instructions

### Step 1: Create Microsoft Azure App Registration

1. **Sign in to Azure Portal**
   - Go to [Azure Portal](https://portal.azure.com)
   - Sign in with your Microsoft 365 admin account

2. **Register a New Application**
   - Navigate to **Azure Active Directory** → **App registrations**
   - Click **New registration**
   - Fill out the registration form:
     - **Name**: `ETRFlow2 Calendar Integration`
     - **Supported account types**: `Accounts in this organizational directory only`
     - **Redirect URI**: `Web` → `https://yourdomain.com/microsoft/callback`
   - Click **Register**

3. **Configure API Permissions**
   - In your app registration, go to **API permissions**
   - Click **Add a permission**
   - Select **Microsoft Graph**
   - Choose **Delegated permissions**
   - Add these permissions:
     - `Calendars.ReadWrite` - Read and write user calendars
     - `User.Read` - Read user profile
   - Click **Grant admin consent** for your organization

4. **Create Client Secret**
   - Go to **Certificates & secrets**
   - Click **New client secret**
   - Add description: `ETRFlow2 Integration Secret`
   - Choose expiration (recommend 24 months)
   - Click **Add**
   - **⚠️ Important**: Copy the secret value immediately - you won't see it again

5. **Note Application Details**
   - Go to **Overview** tab
   - Copy these values for later:
     - **Application (client) ID**
     - **Directory (tenant) ID**
     - **Client secret** (from step 4)

### Step 2: Configure ETRFlow2 Environment

1. **Update Environment Variables**
   - Open your `.env` file
   - Add the following configuration:

```env
# Microsoft 365 Integration
MICROSOFT_CLIENT_ID="your-application-client-id"
MICROSOFT_CLIENT_SECRET="your-client-secret"
MICROSOFT_TENANT_ID="your-directory-tenant-id"
MICROSOFT_REDIRECT_URI="https://yourdomain.com/microsoft/callback"
MICROSOFT_GRAPH_API_VERSION="v1.0"
```

2. **Replace the Values**
   - `MICROSOFT_CLIENT_ID`: Application (client) ID from Azure
   - `MICROSOFT_CLIENT_SECRET`: Client secret you created
   - `MICROSOFT_TENANT_ID`: Directory (tenant) ID from Azure
   - `MICROSOFT_REDIRECT_URI`: Your callback URL (must match Azure registration)

### Step 3: Install Dependencies and Run Migrations

1. **Install Dependencies**
```bash
composer install
```

2. **Run Database Migrations**
```bash
php artisan migrate
```

3. **Clear Configuration Cache**
```bash
php artisan config:clear
php artisan config:cache
```

### Step 4: Configure Queue System

The Microsoft integration requires a queue system for background sync jobs.

1. **Configure Queue Driver**
   - In `.env`, ensure you have a queue driver configured:
```env
QUEUE_CONNECTION=database
```

2. **Run Queue Worker**
   - In production, set up a queue worker:
```bash
php artisan queue:work
```
   - Or use a process manager like Supervisor

## Admin Setup Instructions

Once the system is configured, administrators can set up the shared calendar integration:

### Step 1: Initial Setup

1. **Access Admin Settings**
   - Log into ETRFlow2 as an administrator
   - Go to **Administrative** → **APIs and Integrations**
   - Click **Set Up** on the Microsoft 365 card

2. **Microsoft Sign-In**
   - Click **Connect to Microsoft 365**
   - Sign in with a Microsoft 365 admin account or dedicated service account
   - Grant permissions when prompted
   - You'll be redirected to the calendar selection page

### Step 2: Calendar Selection

1. **Choose Your Calendar**
   - Review all available calendars in your Microsoft 365 account
   - Select the calendar you want to use for ETRFlow2 pickup events
   - Consider using:
     - A dedicated "ETRFlow Pickups" calendar
     - A shared team calendar that all staff can access
     - Your default calendar if preferred

2. **Calendar Information**
   - Each calendar shows:
     - **Name**: Calendar display name
     - **Owner**: Who owns/manages the calendar
     - **Permissions**: Whether you can edit the calendar
     - **Default Status**: If it's the account's default calendar
     - **Color**: Visual calendar color (if set)

3. **Complete Setup**
   - Click **Complete Setup** with your selected calendar
   - The integration will be activated immediately

### Step 3: Configure Sync Settings

1. **Basic Settings**
   - **Calendar Name**: Descriptive name for the integration (for ETRFlow2 display)
   - **Enable automatic synchronization**: Turn on/off sync
   - **Event Category**: Outlook category for pickup events (default: "ETRFlow Pickup")

2. **Event Types**
   - **Sync pickup events**: Include active pickup events in sync (recommended: enabled)
   - **Sync completed events**: Include completed pickups (recommended: disabled)
   - **Sync cancelled events**: Include cancelled events (recommended: disabled)

3. **Event Details**
   - **Include customer details**: Add customer info to event descriptions (recommended: enabled)
   - **Include item details**: Add pickup item details to events (recommended: enabled)

### Step 4: Test and Manage

1. **Initial Testing**
   - Click **Test Connection** to verify setup
   - Use **Sync Now** for immediate manual sync of existing events
   - Check the selected Microsoft calendar for pickup events

2. **Ongoing Management**
   - Monitor sync status and history
   - Use **Change Calendar** to switch to a different calendar
   - Adjust sync settings as needed

## Calendar Management

### Changing Calendars

You can switch to a different Microsoft 365 calendar at any time:

1. **Access Calendar Management**
   - Go to **Administrative** → **APIs and Integrations**
   - Click **Manage** on the Microsoft 365 card
   - Click **Change Calendar**

2. **Select New Calendar**
   - Choose from available calendars
   - Click **Update Calendar**

3. **Migration Behavior**
   - **Existing events** remain in the old calendar
   - **New events** will be created in the new calendar
   - **No data loss** occurs during the switch

### Calendar Permissions

For optimal functionality, ensure:

- **Can Edit**: The selected calendar allows editing permissions
- **Shared Access**: Staff members have at least view access to the selected calendar
- **Owner Rights**: The setup account should own or have full access to the selected calendar

## Event Synchronization Details

### What Gets Synced

**Pickup Events Include:**
- Event title with customer name and load size
- Date and time of pickup
- Customer contact information
- Pickup location/address
- Item types and details (if enabled)
- Driver assignments and staff requirements
- Special instructions and notes

**Event Format Example:**
```
Subject: John Smith (ABC Company) - Large Load Pickup
Location: 123 Main St, Anytown, ST 12345
Body: [Rich HTML with customer details, items, and instructions]
      🔗 Edit this pickup in ETRFlow2: [Direct Link]
      ⚠️ Note: Changes must be made in ETRFlow2, not Outlook
Category: ETRFlow Pickup
```

### What Doesn't Get Synced

- **Non-pickup events** from ETRFlow2
- **Manual calendar events** created directly in Microsoft 365
- **Personal appointments** added by staff
- **Meeting invitations** and responses
- **Other calendar items** unrelated to ETRFlow2

### Sync Behavior

1. **Create**: New pickup events are created in Microsoft 365
2. **Update**: Changes to pickup events update the Microsoft event
3. **Delete**: Deleted pickup events are removed from Microsoft 365
4. **Preserve**: All non-ETRFlow2 events remain completely untouched

### ⚠️ Important: Read-Only Events

**Changes made to pickup events in Microsoft 365/Outlook will NOT be synchronized back to ETRFlow2.** This is a one-way integration where ETRFlow2 is the authoritative source.

- **Editing**: All pickup event changes must be made in ETRFlow2
- **Overwrite**: Any manual changes in Outlook will be overwritten on the next sync
- **Link Provided**: Each synced event includes a direct link to edit the pickup in ETRFlow2
- **Recommended**: Use the "Edit in ETRFlow2" link in the event description to make changes

## Staff Access

### For Staff Members

Staff members benefit from the integration without any setup:

1. **Automatic Access**
   - The selected calendar appears in their Outlook calendar list
   - All pickup events automatically appear with full details
   - No individual setup or configuration required

2. **Using the Shared Calendar**
   - View pickup schedules alongside personal calendars
   - See customer details, locations, and assignments
   - Access events from Outlook desktop, web, or mobile apps
   - Print schedules and export data as needed

3. **Adding Personal Events**
   - Staff can freely add personal appointments to the same calendar
   - Personal events will never be affected by ETRFlow2 sync
   - Complete separation between ETRFlow2 and personal events

4. **Editing Pickup Events**
   - **Do NOT edit pickup events in Outlook** - changes will be overwritten
   - Each pickup event includes an **"Edit in ETRFlow2"** link in the description
   - Click the link to open the pickup request directly in ETRFlow2
   - All changes must be made through the ETRFlow2 system
   - Changes are automatically synced back to Outlook within minutes

### Best Practices for Staff

- **✅ Do**: Use the calendar to view pickup schedules and details
- **✅ Do**: Add your own personal appointments to the same calendar
- **✅ Do**: Set reminders and notifications for pickup events
- **✅ Do**: Use the "Edit in ETRFlow2" link for changes
- **❌ Don't**: Edit pickup event details directly in Outlook
- **❌ Don't**: Move pickup events to different times in Outlook
- **❌ Don't**: Delete pickup events from Outlook

## Configuration Options

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `MICROSOFT_CLIENT_ID` | Azure application client ID | Yes | - |
| `MICROSOFT_CLIENT_SECRET` | Azure application secret | Yes | - |
| `MICROSOFT_TENANT_ID` | Azure directory tenant ID | Yes | `common` |
| `MICROSOFT_REDIRECT_URI` | OAuth callback URL | Yes | - |
| `MICROSOFT_GRAPH_API_VERSION` | Graph API version | No | `v1.0` |

### Sync Settings

Each shared integration can be configured with these preferences:

- **sync_pickup_events**: Whether to sync pickup events (default: true)
- **sync_completed_events**: Include completed pickup events (default: false)
- **sync_cancelled_events**: Include cancelled events (default: false)
- **include_customer_details**: Add customer information to events (default: true)
- **include_item_details**: Include pickup item details (default: true)
- **event_category**: Outlook category for synced events (default: "ETRFlow Pickup")

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI" Error**
   - Ensure `MICROSOFT_REDIRECT_URI` in `.env` exactly matches Azure registration
   - Check that URL is accessible and HTTPS (required for production)

2. **"Insufficient privileges" Error**
   - Verify API permissions are granted in Azure
   - Ensure admin consent was provided
   - Check user has appropriate calendar permissions

3. **"No calendars found" Error**
   - Verify the setup account has calendar access
   - Check that the account has appropriate Microsoft 365 licenses
   - Ensure calendars exist and are accessible

4. **Token Expired Errors**
   - Tokens refresh automatically - check logs for refresh failures
   - Admin may need to reconnect if refresh tokens expire
   - Verify system clock is accurate

5. **Sync Not Working**
   - Check queue system is running: `php artisan queue:work`
   - Verify sync is enabled in admin settings
   - Check Laravel logs for detailed error messages

6. **Calendar Selection Issues**
   - Ensure the setup account has access to target calendar
   - Verify calendar permissions allow editing
   - Check for calendar sharing restrictions

7. **"My changes in Outlook were lost" Issue**
   - This is expected behavior - the integration is read-only from Microsoft 365
   - Use the "Edit in ETRFlow2" link in the event description
   - All pickup event changes must be made in ETRFlow2
   - Changes made in Outlook are overwritten on the next sync

### Debugging Commands

```bash
# Check shared integration status
php artisan tinker
>>> App\Models\MicrosoftCalendarIntegration::where('is_shared_calendar', true)->first()

# View sync logs
>>> App\Models\CalendarSyncLog::latest()->limit(10)->get()

# Test sync for shared calendar
>>> $integration = App\Models\MicrosoftCalendarIntegration::getSharedCalendarIntegration()
>>> App\Jobs\SyncPickupEventsToMicrosoft::forIntegration($integration)->dispatch()

# Manual sync specific event
>>> $event = App\Models\Event::find(1)
>>> app(App\Services\PickupCalendarSyncService::class)->syncEvent($event)
```

### Log Locations

- **Laravel Logs**: `storage/logs/laravel.log`
- **Queue Jobs**: Check queue_jobs table in database
- **Sync Logs**: calendar_sync_logs table

## Security Considerations

1. **Token Storage**: Access tokens are encrypted in the database
2. **HTTPS Required**: Microsoft requires HTTPS for production OAuth callbacks
3. **Scope Limitation**: Integration only requests necessary calendar permissions
4. **Read-Only Design**: ETRFlow2 remains authoritative source for all data
5. **Token Rotation**: Tokens automatically refresh to maintain security
6. **Event Isolation**: Only ETRFlow2-created events are managed by the integration

## API Reference

### Admin Routes

- `GET /admin/microsoft-integration` - Integration management dashboard
- `GET /admin/microsoft-integration/setup` - Setup instructions page
- `GET /admin/microsoft-integration/connect` - Initiate OAuth flow
- `GET /admin/microsoft-integration/calendar-selection` - Select calendar after OAuth
- `POST /admin/microsoft-integration/calendar-selection` - Complete setup with selected calendar
- `GET /admin/microsoft-integration/change-calendar` - Change selected calendar
- `POST /admin/microsoft-integration/change-calendar` - Update selected calendar
- `PUT /admin/microsoft-integration` - Update sync settings
- `POST /admin/microsoft-integration/sync` - Trigger manual sync
- `POST /admin/microsoft-integration/test` - Test connection
- `DELETE /admin/microsoft-integration` - Disconnect integration

### Authentication Routes

- `GET /microsoft/connect` - Initiate OAuth flow
- `GET /microsoft/callback` - OAuth callback handler
- `POST /microsoft/disconnect` - Remove integration
- `GET /microsoft/test-connection` - Test connection

## Support

For technical support:

1. Check the troubleshooting section above
2. Review Laravel logs for detailed error messages
3. Verify Azure app registration configuration
4. Ensure all environment variables are correctly set
5. Test calendar permissions and access

## Version History

- **v2.0.0** - Calendar Selection Update
  - Added calendar selection during setup
  - Calendar switching capability
  - Enhanced calendar information display
  - Improved admin interface
  - Better error handling and user guidance

- **v1.0.0** - Initial Microsoft 365 integration release
  - OAuth 2.0 authentication
  - Real-time pickup event sync
  - Shared calendar architecture
  - Background job processing
  - Comprehensive error handling and logging