{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "barryvdh/laravel-dompdf": "^3.0", "intervention/image": "*", "jurosh/pdf-merge": "^2.1", "laravel/framework": "^11.31", "laravel/jetstream": "^5.3", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "league/oauth2-client": "^2.8", "livewire/livewire": "^3.5", "microsoft/kiota-authentication-phpleague": "^1.5", "microsoft/microsoft-graph": "^2.41", "minishlink/web-push": "^9.0", "myclabs/deep-copy": "*", "openai-php/client": "^0.10.3", "orangehill/iseed": "^3.0", "phpoffice/phpspreadsheet": "^4.3", "predis/predis": "^3.1", "setasign/fpdi": "^2.6", "spatie/pdf-to-image": "^1.2", "tecnickcom/tcpdf": "^6.9"}, "require-dev": {"fakerphp/faker": "^1.23", "kitloong/laravel-migrations-generator": "^7.0", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "pestphp/pest": "^3.6", "pestphp/pest-plugin-laravel": "^3.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": "npx concurrently \"php artisan serve --host=0.0.0.0 --port=8000\" \"php artisan queue:listen --tries=1\" \"npm run dev\""}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "tbachert/spi": true}, "process-timeout": 0}, "minimum-stability": "stable", "prefer-stable": true}