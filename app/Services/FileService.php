<?php

namespace App\Services;

use App\Models\File;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileService
{
    /**
     * Upload a file and create a File record
     *
     * @param UploadedFile $uploadedFile
     * @param string $directory
     * @param Model $model
     * @param array $options
     * @return File
     */
    public function uploadFile(UploadedFile $uploadedFile, string $directory, $model, array $options = [])
    {
        // Generate a unique filename
        $filename = $this->generateUniqueFilename($uploadedFile);

        // Determine the disk to use
        $disk = $options['disk'] ?? 'private';

        // Make sure the directory exists
        Storage::disk($disk)->makeDirectory($directory);

        // Store the file
        $path = $uploadedFile->storeAs(
            $directory,
            $filename,
            $disk
        );

        // Create the file record
        $file = new File([
            'filename' => $filename,
            'original_filename' => $uploadedFile->getClientOriginalName(),
            'filepath' => $path,
            'mime_type' => $uploadedFile->getMimeType(),
            'size' => $uploadedFile->getSize(),
            'extension' => $uploadedFile->getClientOriginalExtension(),
            'description' => $options['description'] ?? null,
            'uploaded_by' => Auth::id(),
            'is_public' => $options['is_public'] ?? false,
            'metadata' => $options['metadata'] ?? null,
        ]);

        // Associate with the model
        $model->files()->save($file);

        return $file;
    }

    /**
     * Generate a unique filename for the uploaded file
     *
     * @param UploadedFile $file
     * @return string
     */
    protected function generateUniqueFilename(UploadedFile $file)
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('YmdHis');
        $random = Str::random(10);

        return "{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Download a file
     *
     * @param File $file
     * @param bool $forceDownload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function downloadFile(File $file, $forceDownload = true)
    {
        // Validate file path to prevent path traversal attacks
        $this->validateFilePath($file->filepath);
        
        $disk = $file->is_public ? 'public' : 'private';
        $headers = [
            'Content-Type' => $file->mime_type,
        ];

        if ($forceDownload) {
            $headers['Content-Disposition'] = 'attachment; filename="' . $file->original_filename . '"';
        }

        try {
            // Use Laravel's Storage facade instead of constructing raw paths
            if (!Storage::disk($disk)->exists($file->filepath)) {
                abort(404, 'File not found');
            }

            // Get the full path using Storage facade for additional security
            $fullPath = Storage::disk($disk)->path($file->filepath);
            
            // Additional security check: ensure the resolved path is within the storage directory
            $storagePath = Storage::disk($disk)->path('');
            $realFullPath = realpath($fullPath);
            $realStoragePath = realpath($storagePath);
            
            if ($realFullPath === false || !str_starts_with($realFullPath, $realStoragePath)) {
                abort(404, 'File not found');
            }

            return response()->file($realFullPath, $headers);
        } catch (\Exception $e) {
            abort(404, 'File not found');
        }
    }

    /**
     * Get a resized image
     *
     * @param File $file
     * @param int|null $width
     * @param int|null $height
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function getResizedImage(File $file, $width = null, $height = null)
    {
        // Validate that this is an image file
        if (!$file->is_image) {
            return $this->downloadFile($file, false);
        }

        // Get the file path
        $disk = $file->is_public ? 'public' : 'private';
        $path = $file->is_public ? 'public/' . $file->filepath : 'private/' . $file->filepath;
        $fullPath = storage_path('app/' . $path);

        if (!file_exists($fullPath)) {
            abort(404, 'File not found');
        }

        // Create image resource
        $imageInfo = getimagesize($fullPath);
        $mimeType = $imageInfo['mime'];
        $originalWidth = $imageInfo[0];
        $originalHeight = $imageInfo[1];

        // If no resize is needed, just return the file
        if ((!$width || $width >= $originalWidth) && (!$height || $height >= $originalHeight)) {
            return $this->downloadFile($file, false);
        }

        // Calculate new dimensions while maintaining aspect ratio
        if ($width && $height) {
            // Both dimensions specified, use them directly
            $newWidth = (int) $width;
            $newHeight = (int) $height;
        } elseif ($width) {
            // Only width specified, calculate height to maintain aspect ratio
            $newWidth = (int) $width;
            $newHeight = (int) ($originalHeight * ($newWidth / $originalWidth));
        } else {
            // Only height specified, calculate width to maintain aspect ratio
            $newHeight = (int) $height;
            $newWidth = (int) ($originalWidth * ($newHeight / $originalHeight));
        }

        // Create the image resource based on mime type
        $sourceImage = match ($mimeType) {
            'image/jpeg' => imagecreatefromjpeg($fullPath),
            'image/png' => imagecreatefrompng($fullPath),
            'image/gif' => imagecreatefromgif($fullPath),
            'image/webp' => imagecreatefromwebp($fullPath),
            default => null,
        };

        if (!$sourceImage) {
            return $this->downloadFile($file, false);
        }

        // Create a new true color image
        $newImage = imagecreatetruecolor($newWidth, $newHeight);

        // Preserve transparency for PNG and WebP
        if ($mimeType === 'image/png' || $mimeType === 'image/webp') {
            imagealphablending($newImage, false);
            imagesavealpha($newImage, true);
            $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
            imagefilledrectangle($newImage, 0, 0, $newWidth, $newHeight, $transparent);
        }

        // Resize the image
        imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);

        // Start output buffering
        ob_start();

        // Output the image
        switch ($mimeType) {
            case 'image/jpeg':
                imagejpeg($newImage, null, 85);
                break;
            case 'image/png':
                imagepng($newImage, null, 6);
                break;
            case 'image/gif':
                imagegif($newImage);
                break;
            case 'image/webp':
                imagewebp($newImage, null, 85);
                break;
        }

        // Get the image data
        $imageData = ob_get_clean();

        // Free up memory
        imagedestroy($sourceImage);
        imagedestroy($newImage);

        // Return the image
        return response($imageData)
            ->header('Content-Type', $mimeType)
            ->header('Content-Length', strlen($imageData));
    }

    /**
     * Delete a file
     *
     * @param File $file
     * @param bool $forceDelete
     * @return bool
     */
    public function deleteFile(File $file, $forceDelete = false)
    {

        // Delete the physical file
        $disk = $file->is_public ? 'public' : 'private';
        $physicalFileDeleted = false;
        
        try {
            Storage::disk($disk)->delete($file->filepath);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::warning('PHYSICAL FILE DELETE FAILED, TRYING FALLBACK', [
                'file_id' => $file->id,
                'error' => $e->getMessage(),
                'disk' => $disk,
                'path' => $file->filepath,
            ]);
            
            // If the file doesn't exist in the specified disk, try the local disk
            try {
                $path = $file->is_public ? 'public/' . $file->filepath : 'private/' . $file->filepath;
                Storage::disk('local')->delete($path);
                $physicalFileDeleted = true;
                \Illuminate\Support\Facades\Log::info('PHYSICAL FILE DELETED VIA FALLBACK', [
                    'file_id' => $file->id,
                    'fallback_path' => $path,
                ]);
            } catch (\Exception $e2) {
                \Illuminate\Support\Facades\Log::warning('PHYSICAL FILE DELETE FALLBACK FAILED', [
                    'file_id' => $file->id,
                    'fallback_error' => $e2->getMessage(),
                    'fallback_path' => $path ?? 'unknown',
                ]);
                // File might not exist, continue with deletion of the record
            }
        }



        // Delete the database record WITHOUT modifying the polymorphic relationship
        $result = false;
        try {

            if ($forceDelete) {
                $result = $file->forceDelete();
            } else {
                $result = $file->delete();
            }
            

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('FILE DATABASE DELETE FAILED', [
                'file_id' => $file->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
        




        return $result;
    }

    /**
     * Validate file path to prevent path traversal attacks
     *
     * @param string $filepath
     * @throws \InvalidArgumentException
     */
    protected function validateFilePath(string $filepath)
    {
        // Check for path traversal sequences
        if (str_contains($filepath, '..') || 
            str_contains($filepath, './') || 
            str_contains($filepath, '.\\') ||
            str_starts_with($filepath, '/') ||
            str_starts_with($filepath, '\\') ||
            preg_match('/[<>:"|?*]/', $filepath)) {
            throw new \InvalidArgumentException('Invalid file path detected');
        }
        
        // Normalize path separators and check again
        $normalizedPath = str_replace(['\\', '/'], DIRECTORY_SEPARATOR, $filepath);
        if (str_contains($normalizedPath, '..' . DIRECTORY_SEPARATOR)) {
            throw new \InvalidArgumentException('Invalid file path detected');
        }
    }
}
