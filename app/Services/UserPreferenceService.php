<?php

namespace App\Services;

use App\Models\UserPreference;
use Illuminate\Support\Facades\Auth;

class UserPreferenceService
{
    /**
     * Get a preference for the current user.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function get(string $key, $default = null)
    {
        $userId = Auth::id();
        if (!$userId) {
            return $default; // Return default for guests
        }

        $preference = UserPreference::where('user_id', $userId)->where('key', $key)->first();

        return $preference ? json_decode($preference->value, true) : $default;
    }

    /**
     * Set a preference for the current user.
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public function set(string $key, $value)
    {
        $userId = Auth::id();
        if (!$userId) {
            return;
        }

        UserPreference::updateOrCreate(
            ['user_id' => $userId, 'key' => $key],
            ['value' => json_encode($value)]
        );
    }
}
