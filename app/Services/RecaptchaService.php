<?php

namespace App\Services;

use App\Models\GlobalConfig;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class RecaptchaService
{
    /**
     * Verify a reCAPTCHA token.
     *
     * @param string $token
     * @param string|null $action
     * @param float $minScore
     * @return array
     */
    public function verify(string $token, ?string $action = null, float $minScore = 0.5): array
    {
        $secretKey = GlobalConfig::getRecaptchaSecretKey();
        
        if (!$secretKey) {
            Log::warning('reCAPTCHA verification attempted but secret key not configured');
            return [
                'success' => false,
                'error' => 'reCAPTCHA not configured'
            ];
        }

        try {
            $response = Http::asForm()->post('https://www.google.com/recaptcha/api/siteverify', [
                'secret' => $secretKey,
                'response' => $token,
                'remoteip' => request()->ip()
            ]);

            if (!$response->successful()) {
                Log::error('reCAPTCHA API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return [
                    'success' => false,
                    'error' => 'reCAPTCHA verification failed'
                ];
            }

            $data = $response->json();

            // Check if the verification was successful
            if (!$data['success']) {
                Log::warning('reCAPTCHA verification failed', [
                    'error_codes' => $data['error-codes'] ?? [],
                    'token' => substr($token, 0, 20) . '...'
                ]);
                return [
                    'success' => false,
                    'error' => 'reCAPTCHA verification failed',
                    'error_codes' => $data['error-codes'] ?? []
                ];
            }

            // Check the score (for v3)
            $score = $data['score'] ?? 1.0;
            if ($score < $minScore) {
                Log::warning('reCAPTCHA score too low', [
                    'score' => $score,
                    'min_score' => $minScore,
                    'action' => $action
                ]);
                return [
                    'success' => false,
                    'error' => 'reCAPTCHA score too low',
                    'score' => $score
                ];
            }

            // Check the action (for v3)
            if ($action && isset($data['action']) && $data['action'] !== $action) {
                Log::warning('reCAPTCHA action mismatch', [
                    'expected' => $action,
                    'received' => $data['action']
                ]);
                return [
                    'success' => false,
                    'error' => 'reCAPTCHA action mismatch'
                ];
            }

            Log::info('reCAPTCHA verification successful', [
                'score' => $score,
                'action' => $data['action'] ?? null,
                'hostname' => $data['hostname'] ?? null
            ]);

            return [
                'success' => true,
                'score' => $score,
                'action' => $data['action'] ?? null,
                'hostname' => $data['hostname'] ?? null,
                'challenge_ts' => $data['challenge_ts'] ?? null
            ];

        } catch (\Exception $e) {
            Log::error('reCAPTCHA verification exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [
                'success' => false,
                'error' => 'reCAPTCHA verification error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check if reCAPTCHA is enabled and configured.
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return GlobalConfig::isRecaptchaConfigured();
    }

    /**
     * Get the reCAPTCHA site key for frontend use.
     *
     * @return string|null
     */
    public function getSiteKey(): ?string
    {
        return GlobalConfig::getRecaptchaSiteKey();
    }
}
