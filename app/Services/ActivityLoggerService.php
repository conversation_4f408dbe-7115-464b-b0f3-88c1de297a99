<?php

namespace App\Services;

use App\Models\ActivityLog;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ActivityLoggerService
{
    protected $request;

    public function __construct(?Request $request = null)
    {
        $this->request = $request ?: request();
    }

    /**
     * Log a model creation event
     */
    public function logCreated(Model $model, ?string $description = null, array $additionalData = []): ActivityLog
    {
        return $this->log(
            model: $model,
            eventType: 'created',
            description: $description ?: $this->getModelName($model) . ' was created',
            newValues: $model->getAttributes(),
            additionalData: $additionalData
        );
    }

    /**
     * Log a model update event
     */
    public function logUpdated(Model $model, array $oldValues, ?string $description = null, array $additionalData = [], array $newValues = []): ActivityLog
    {
        // If no new values provided, only include the changed fields
        if (empty($newValues)) {
            $newValues = [];
            foreach (array_keys($oldValues) as $field) {
                $newValues[$field] = $model->getAttribute($field);
            }
        }

        return $this->log(
            model: $model,
            eventType: 'updated',
            description: $description ?: $this->getModelName($model) . ' was updated',
            oldValues: $oldValues,
            newValues: $newValues,
            additionalData: $additionalData
        );
    }

    /**
     * Smart log update that consolidates auto-save changes
     * This method will either update an existing recent log or create a new one
     */
    public function logUpdatedSmart(Model $model, array $oldValues, ?string $description = null, array $additionalData = [], array $newValues = [], bool $isAutoSave = false): ActivityLog
    {
        // If no new values provided, only include the changed fields
        if (empty($newValues)) {
            $newValues = [];
            foreach (array_keys($oldValues) as $field) {
                $newValues[$field] = $model->getAttribute($field);
            }
        }

        // If this is an auto-save, try to consolidate with recent logs
        if ($isAutoSave) {
            $consolidatedLog = $this->tryConsolidateWithRecentLog($model, $oldValues, $newValues);
            if ($consolidatedLog) {
                return $consolidatedLog;
            }
        }

        return $this->log(
            model: $model,
            eventType: 'updated',
            description: $description ?: $this->getModelName($model) . ' was updated',
            oldValues: $oldValues,
            newValues: $newValues,
            additionalData: $additionalData
        );
    }

    /**
     * Log a model deletion event
     */
    public function logDeleted(Model $model, ?string $description = null, array $additionalData = []): ActivityLog
    {
        return $this->log(
            model: $model,
            eventType: 'deleted',
            description: $description ?: $this->getModelName($model) . ' was deleted',
            oldValues: $model->getAttributes(),
            additionalData: $additionalData
        );
    }

    /**
     * Log a model restoration event (for soft deletes)
     */
    public function logRestored(Model $model, ?string $description = null, array $additionalData = []): ActivityLog
    {
        return $this->log(
            model: $model,
            eventType: 'restored',
            description: $description ?: $this->getModelName($model) . ' was restored',
            newValues: $model->getAttributes(),
            additionalData: $additionalData
        );
    }

    /**
     * Log a custom event
     */
    public function logCustom(
        string $component,
        string $eventType,
        string $description,
        ?Model $model = null,
        array $oldValues = [],
        array $newValues = [],
        array $additionalData = []
    ): ActivityLog {
        return $this->log(
            model: $model,
            eventType: $eventType,
            description: $description,
            component: $component,
            oldValues: $oldValues,
            newValues: $newValues,
            additionalData: $additionalData
        );
    }

    /**
     * Core logging method
     */
    protected function log(
        ?Model $model = null,
        string $eventType,
        string $description,
        ?string $component = null,
        array $oldValues = [],
        array $newValues = [],
        array $additionalData = []
    ): ActivityLog {
        $user = Auth::user();
        
        return ActivityLog::create([
            'loggable_type' => $model ? get_class($model) : null,
            'loggable_id' => $model?->getKey(),
            'user_id' => $user?->id,
            'event_type' => $eventType,
            'component' => $component ?: $this->getComponentName($model),
            'description' => $description,
            'old_values' => empty($oldValues) ? null : $oldValues,
            'new_values' => empty($newValues) ? null : $newValues,
            'additional_data' => empty($additionalData) ? null : $additionalData,
            'ip_address' => $this->request?->ip(),
            'user_agent' => $this->request?->userAgent(),
        ]);
    }

    /**
     * Get human-readable model name
     */
    protected function getModelName(Model $model): string
    {
        $className = class_basename($model);
        return ucfirst(strtolower(preg_replace('/([A-Z])/', ' $1', $className)));
    }

    /**
     * Get component name from model
     */
    protected function getComponentName(?Model $model = null): string
    {
        if (!$model) {
            return 'system';
        }

        $className = class_basename($model);
        return strtolower(preg_replace('/(?<!^)([A-Z])/', '_$1', $className));
    }

    /**
     * Log a view event (when someone views a record)
     */
    public function logViewed(Model $model, ?string $description = null, array $additionalData = []): ActivityLog
    {
        return $this->log(
            model: $model,
            eventType: 'viewed',
            description: $description ?: $this->getModelName($model) . ' was viewed',
            additionalData: $additionalData
        );
    }

    /**
     * Try to consolidate the current update with a recent log entry
     * Returns the updated log if consolidation was successful, null otherwise
     */
    protected function tryConsolidateWithRecentLog(Model $model, array $oldValues, array $newValues): ?ActivityLog
    {
        $user = Auth::user();
        if (!$user) {
            return null;
        }

        // Define consolidation window (5 minutes)
        $consolidationWindow = now()->subMinutes(5);

        // Find the most recent update log for this model by this user within the consolidation window
        $recentLog = ActivityLog::where('loggable_type', get_class($model))
            ->where('loggable_id', $model->getKey())
            ->where('user_id', $user->id)
            ->where('event_type', 'updated')
            ->where('created_at', '>=', $consolidationWindow)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$recentLog || !$recentLog->old_values || !$recentLog->new_values) {
            return null;
        }

        // Check if we can consolidate these changes
        if ($this->canConsolidateChanges($recentLog->old_values, $recentLog->new_values, $oldValues, $newValues)) {
            // Update the existing log with new values and timestamp
            $recentLog->update([
                'new_values' => $newValues,
                'updated_at' => now(),
                'description' => $this->getModelName($model) . ' was updated'
            ]);

            return $recentLog;
        }

        return null;
    }

    /**
     * Determine if changes can be consolidated
     * This checks if the new changes are a continuation of the previous changes
     */
    protected function canConsolidateChanges(array $oldLogOldValues, array $oldLogNewValues, array $newOldValues, array $newNewValues): bool
    {
        // Get the fields that changed in both logs
        $oldFields = array_keys($oldLogNewValues);
        $newFields = array_keys($newNewValues);

        // If different fields were changed, don't consolidate
        if (count(array_intersect($oldFields, $newFields)) === 0) {
            return false;
        }

        // Check each field that appears in both changes
        foreach ($oldFields as $field) {
            if (!isset($newNewValues[$field])) {
                continue;
            }

            $oldLogOldValue = $oldLogOldValues[$field] ?? null;
            $oldLogNewValue = $oldLogNewValues[$field] ?? null;
            $newOldValue = $newOldValues[$field] ?? null;
            $newNewValue = $newNewValues[$field] ?? null;

            // The new old value should match the old new value (continuation)
            // OR the new old value should match the original old value (user went back and changed again)
            if ($newOldValue === $oldLogNewValue || $newOldValue === $oldLogOldValue) {
                // This looks like a continuation or revision of the same change
                continue;
            }

            // Check if this looks like progressive typing (new value starts with old value)
            if (is_string($oldLogNewValue) && is_string($newNewValue) &&
                strlen($newNewValue) > strlen($oldLogNewValue) &&
                str_starts_with($newNewValue, $oldLogNewValue)) {
                // This looks like progressive typing (e.g., "555" -> "5557416518")
                continue;
            }

            // If we can't identify this as a continuation, don't consolidate
            return false;
        }

        return true;
    }

    /**
     * Log an export event
     */
    public function logExported(string $component, string $description, array $additionalData = []): ActivityLog
    {
        return $this->logCustom(
            component: $component,
            eventType: 'exported',
            description: $description,
            additionalData: $additionalData
        );
    }

    /**
     * Log an import event
     */
    public function logImported(string $component, string $description, array $additionalData = []): ActivityLog
    {
        return $this->logCustom(
            component: $component,
            eventType: 'imported',
            description: $description,
            additionalData: $additionalData
        );
    }

    /**
     * Get logs for a specific model
     */
    public function getLogsForModel(Model $model, int $limit = 50)
    {
        return ActivityLog::where('loggable_type', get_class($model))
            ->where('loggable_id', $model->getKey())
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent logs for a component
     */
    public function getRecentLogsForComponent(string $component, int $limit = 100)
    {
        return ActivityLog::forComponent($component)
            ->with(['user', 'loggable'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}
