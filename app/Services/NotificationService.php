<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use App\Models\UserGroup;
use App\Models\NotificationSubscription;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Minishlink\WebPush\WebPush;
use Minishlink\WebPush\Subscription;

class NotificationService
{
    /**
     * Send notification to all users
     */
    public function sendToAllUsers(
        string $title,
        string $message,
        string $urgency = Notification::URGENCY_NORMAL,
        ?string $linkUrl = null,
        ?string $linkText = null,
        ?Carbon $expiresAt = null,
        bool $isDismissible = true,
        bool $autoDismiss = false,
        ?User $createdBy = null
    ): Notification {
        return $this->createNotification([
            'title' => $title,
            'message' => $message,
            'urgency' => $urgency,
            'target_type' => Notification::TARGET_ALL_USERS,
            'target_ids' => null,
            'link_url' => $linkUrl,
            'link_text' => $linkText,
            'expires_at' => $expiresAt,
            'is_dismissible' => $isDismissible,
            'auto_dismiss' => $autoDismiss,
            'created_by' => $createdBy ? $createdBy->id : auth()->id(),
        ]);
    }

    /**
     * Send notification to specific users
     */
    public function sendToUsers(
        Collection|array $users,
        string $title,
        string $message,
        string $urgency = Notification::URGENCY_NORMAL,
        ?string $linkUrl = null,
        ?string $linkText = null,
        ?Carbon $expiresAt = null,
        bool $isDismissible = true,
        bool $autoDismiss = false,
        ?User $createdBy = null
    ): Notification {
        $userIds = collect($users)->map(function ($user) {
            return $user instanceof User ? $user->id : $user;
        })->toArray();

        return $this->createNotification([
            'title' => $title,
            'message' => $message,
            'urgency' => $urgency,
            'target_type' => Notification::TARGET_SPECIFIC_USERS,
            'target_ids' => $userIds,
            'link_url' => $linkUrl,
            'link_text' => $linkText,
            'expires_at' => $expiresAt,
            'is_dismissible' => $isDismissible,
            'auto_dismiss' => $autoDismiss,
            'created_by' => $createdBy ? $createdBy->id : auth()->id(),
        ]);
    }

    /**
     * Send notification to user groups
     */
    public function sendToUserGroups(
        Collection|array $userGroups,
        string $title,
        string $message,
        string $urgency = Notification::URGENCY_NORMAL,
        ?string $linkUrl = null,
        ?string $linkText = null,
        ?Carbon $expiresAt = null,
        bool $isDismissible = true,
        bool $autoDismiss = false,
        ?User $createdBy = null
    ): Notification {
        $groupIds = collect($userGroups)->map(function ($group) {
            return $group instanceof UserGroup ? $group->id : $group;
        })->toArray();

        return $this->createNotification([
            'title' => $title,
            'message' => $message,
            'urgency' => $urgency,
            'target_type' => Notification::TARGET_USER_GROUP,
            'target_ids' => $groupIds,
            'link_url' => $linkUrl,
            'link_text' => $linkText,
            'expires_at' => $expiresAt,
            'is_dismissible' => $isDismissible,
            'auto_dismiss' => $autoDismiss,
            'created_by' => $createdBy ? $createdBy->id : auth()->id(),
        ]);
    }

    /**
     * Get notifications for a specific user
     */
    public function getNotificationsForUser(User $user, bool $onlyUnread = false, bool $onlyUndismissed = true): Collection
    {
        try {
            $query = Notification::active()
                ->forUser($user)
                ->orderBy('urgency', 'desc')
                ->orderBy('created_at', 'desc');

            $notifications = $query->get();

            return $notifications->filter(function ($notification) use ($user, $onlyUnread, $onlyUndismissed) {
                try {
                    // Filter out dismissed notifications if requested
                    if ($onlyUndismissed && $notification->isDismissedByUser($user)) {
                        return false;
                    }

                    // Filter out read notifications if requested
                    if ($onlyUnread && $notification->isReadByUser($user)) {
                        return false;
                    }

                    return true;
                } catch (\Exception $e) {
                    // Log the error but don't break the entire collection
                    \Log::warning('Error filtering notification', [
                        'notification_id' => $notification->id ?? 'unknown',
                        'user_id' => $user->id,
                        'error' => $e->getMessage()
                    ]);
                    return false;
                }
            });
        } catch (\Exception $e) {
            \Log::error('Error fetching notifications for user', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return collect(); // Return empty collection on error
        }
    }

    /**
     * Get unread notification count for user
     */
    public function getUnreadCountForUser(User $user): int
    {
        try {
            return $this->getNotificationsForUser($user, true, true)->count();
        } catch (\Exception $e) {
            \Log::error('Error getting unread notification count', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * Mark notification as read for user
     */
    public function markAsRead(Notification $notification, User $user): void
    {
        $notification->markAsReadByUser($user);
    }

    /**
     * Mark notification as dismissed for user
     */
    public function markAsDismissed(Notification $notification, User $user): void
    {
        $notification->markAsDismissedByUser($user);
    }

    /**
     * Dismiss all notifications for user
     */
    public function dismissAllForUser(User $user): int
    {
        $notifications = $this->getNotificationsForUser($user, false, true);
        $count = 0;

        foreach ($notifications as $notification) {
            if ($notification->is_dismissible) {
                $this->markAsDismissed($notification, $user);
                $count++;
            }
        }

        return $count;
    }

    /**
     * Clean up expired notifications
     */
    public function cleanupExpiredNotifications(): int
    {
        return Notification::where('expires_at', '<', now())->delete();
    }

    /**
     * Create a notification
     */
    protected function createNotification(array $data): Notification
    {
        // Ensure created_by is set, default to first admin user if no auth user
        if (!isset($data['created_by']) || !$data['created_by']) {
            $data['created_by'] = auth()->id() ?: User::whereHas('groups', function($query) {
                $query->where('name', 'Admin');
            })->first()?->id ?: 1; // Fallback to user ID 1
        }

        $notification = Notification::create($data);

        // Send browser push notifications if enabled
        $this->sendBrowserPushNotifications($notification);

        return $notification;
    }

    /**
     * Get urgency levels for forms
     */
    public function getUrgencyLevels(): array
    {
        return [
            Notification::URGENCY_LOW => 'Low',
            Notification::URGENCY_NORMAL => 'Normal',
            Notification::URGENCY_HIGH => 'High',
            Notification::URGENCY_CRITICAL => 'Critical',
        ];
    }

    /**
     * Get target types for forms
     */
    public function getTargetTypes(): array
    {
        return [
            Notification::TARGET_ALL_USERS => 'All Users',
            Notification::TARGET_USER_GROUP => 'User Groups',
            Notification::TARGET_SPECIFIC_USERS => 'Specific Users',
        ];
    }

    /**
     * Send notifications based on model's notification configurations
     */
    public function sendConfiguredNotifications(
        $model,
        string $eventType,
        array $templateData = [],
        ?string $defaultTitle = null,
        ?string $defaultMessage = null,
        ?string $defaultLinkUrl = null,
        ?string $defaultLinkText = null
    ): Collection {
        $notificationConfigService = app(\App\Services\NotificationConfigService::class);

        return $notificationConfigService->sendNotificationsForEvent(
            $model,
            $eventType,
            $templateData,
            $defaultTitle,
            $defaultMessage,
            $defaultLinkUrl,
            $defaultLinkText
        );
    }

    /**
     * Send browser push notifications to subscribed users
     */
    protected function sendBrowserPushNotifications(Notification $notification): void
    {
        try {
            // Get target users based on notification type
            $users = $this->getTargetUsers($notification);
            
            // Get active subscriptions for these users
            $subscriptions = NotificationSubscription::whereIn('user_id', $users->pluck('id'))
                ->where('is_active', true)
                ->get();

            if ($subscriptions->isEmpty()) {
                return;
            }

            // Initialize WebPush with VAPID credentials
            $auth = [
                'VAPID' => [
                    'subject' => config('app.url'),
                    'publicKey' => config('services.webpush.public_key'),
                    'privateKey' => config('services.webpush.private_key'),
                ],
            ];

            $webPush = new WebPush($auth);

            // Queue notifications for each subscription
            foreach ($subscriptions as $subscription) {
                try {
                    $payload = json_encode([
                        'id' => $notification->id,
                        'title' => $notification->title,
                        'message' => $notification->message,
                        'urgency' => $notification->urgency,
                        'link_url' => $notification->link_url,
                        'created_at' => $notification->created_at->toIso8601String(),
                    ]);

                    $webPush->queueNotification(
                        Subscription::create([
                            'endpoint' => $subscription->endpoint,
                            'publicKey' => $subscription->public_key,
                            'authToken' => $subscription->auth_token,
                        ]),
                        $payload
                    );
                } catch (\Exception $e) {
                    Log::error('Failed to queue push notification', [
                        'subscription_id' => $subscription->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Send all queued notifications
            foreach ($webPush->flush() as $report) {
                $endpoint = $report->getRequest()->getUri()->__toString();
                
                if ($report->isSuccess()) {
                    Log::info('Push notification sent successfully', ['endpoint' => $endpoint]);
                } else {
                    Log::error('Push notification failed', [
                        'endpoint' => $endpoint,
                        'reason' => $report->getReason()
                    ]);

                    // Handle expired subscriptions
                    if ($report->isSubscriptionExpired()) {
                        NotificationSubscription::where('endpoint', $endpoint)
                            ->update(['is_active' => false]);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to send browser push notifications', [
                'notification_id' => $notification->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get target users for a notification
     */
    protected function getTargetUsers(Notification $notification): Collection
    {
        switch ($notification->target_type) {
            case Notification::TARGET_ALL_USERS:
                return User::all();
                
            case Notification::TARGET_SPECIFIC_USERS:
                return User::whereIn('id', $notification->target_ids ?? [])->get();
                
            case Notification::TARGET_USER_GROUP:
                return User::whereHas('groups', function ($query) use ($notification) {
                    $query->whereIn('user_groups.id', $notification->target_ids ?? []);
                })->get();
                
            default:
                return collect();
        }
    }

    /**
     * Send test push notification to a user
     */
    public function sendTestPushNotification(User $user): bool
    {
        try {
            $testNotification = new Notification([
                'id' => 0,
                'title' => 'Test Notification',
                'message' => 'This is a test push notification from ETRFlow.',
                'urgency' => Notification::URGENCY_NORMAL,
                'link_url' => route('dashboard'),
                'created_at' => now(),
            ]);

            // Get user's active subscriptions
            $subscriptions = NotificationSubscription::where('user_id', $user->id)
                ->where('is_active', true)
                ->get();

            if ($subscriptions->isEmpty()) {
                return false;
            }

            // Send test notification to all user's subscriptions
            $auth = [
                'VAPID' => [
                    'subject' => config('app.url'),
                    'publicKey' => config('services.webpush.public_key'),
                    'privateKey' => config('services.webpush.private_key'),
                ],
            ];

            $webPush = new WebPush($auth);

            foreach ($subscriptions as $subscription) {
                $payload = json_encode([
                    'id' => 0,
                    'title' => $testNotification->title,
                    'message' => $testNotification->message,
                    'urgency' => $testNotification->urgency,
                    'link_url' => $testNotification->link_url,
                ]);

                $webPush->queueNotification(
                    Subscription::create([
                        'endpoint' => $subscription->endpoint,
                        'publicKey' => $subscription->public_key,
                        'authToken' => $subscription->auth_token,
                    ]),
                    $payload
                );
            }

            // Send all queued notifications
            $reports = $webPush->flush();
            $success = false;

            foreach ($reports as $report) {
                if ($report->isSuccess()) {
                    $success = true;
                }
            }

            return $success;
        } catch (\Exception $e) {
            Log::error('Failed to send test push notification', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
