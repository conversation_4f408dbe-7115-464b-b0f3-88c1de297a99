<?php

namespace App\Services;

use App\Models\Event;
use App\Models\MicrosoftCalendarIntegration;
use App\Http\Controllers\MicrosoftOAuthController;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class MicrosoftGraphService
{
    private MicrosoftCalendarIntegration $integration;
    private string $accessToken;

    public function __construct(MicrosoftCalendarIntegration $integration, MicrosoftOAuthController $oauthController)
    {
        $this->integration = $integration;

        if ($this->integration->isTokenExpired()) {
            Log::info('Microsoft access token expired, attempting refresh.', [
                'user_id' => $this->integration->user_id,
                'is_shared_calendar' => $this->integration->is_shared_calendar,
            ]);
            
            try {
                $oauthController->refreshAccessToken($this->integration);
                $this->integration = $this->integration->fresh();
                Log::info('Microsoft access token successfully refreshed.', [
                    'user_id' => $this->integration->user_id
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to refresh Microsoft access token.', [
                    'user_id' => $this->integration->user_id,
                    'error' => $e->getMessage(),
                ]);
                throw new \Exception('Microsoft token refresh failed: ' . $e->getMessage(), 0, $e);
            }
        }
        
        $this->accessToken = decrypt($this->integration->access_token);
    }

    /**
     * Get user's calendars from Microsoft Graph.
     */
    public function getCalendars(): array
    {
        try {
            $response = Http::withToken($this->accessToken)
                ->get('https://graph.microsoft.com/v1.0/me/calendars', [
                    '$select' => 'id,name,color,isDefaultCalendar,canEdit,owner'
                ]);

            if ($response->successful()) {
                return $response->json('value', []);
            }

            throw new \Exception('Failed to fetch calendars: ' . $response->body());
        } catch (\Exception $e) {
            Log::error('Failed to fetch Microsoft calendars', [
                'user_id' => $this->integration->user_id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get a specific calendar by ID.
     */
    public function getCalendar(string $calendarId): ?array
    {
        try {
            $response = Http::withToken($this->accessToken)
                ->get("https://graph.microsoft.com/v1.0/me/calendars/{$calendarId}");

            if ($response->successful()) {
                return $response->json();
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Failed to fetch specific calendar', [
                'calendar_id' => $calendarId,
                'user_id' => $this->integration->user_id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Create a calendar event in Microsoft 365.
     */
    public function createEvent(Event $etrFlowEvent): ?string
    {
        try {
            $eventData = $this->buildEventData($etrFlowEvent);
            
            $calendarId = $this->integration->outlook_calendar_id ?: null;
            $endpoint = $calendarId 
                ? "https://graph.microsoft.com/v1.0/me/calendars/{$calendarId}/events"
                : "https://graph.microsoft.com/v1.0/me/events";
            
            $response = Http::withToken($this->accessToken)
                ->post($endpoint, $eventData);

            if ($response->successful()) {
                $eventId = $response->json('id');
                
                Log::info('Microsoft event created', [
                    'etr_event_id' => $etrFlowEvent->id,
                    'microsoft_event_id' => $eventId,
                    'user_id' => $this->integration->user_id
                ]);

                return $eventId;
            }

            throw new \Exception('Failed to create event: ' . $response->body());

        } catch (\Exception $e) {
            Log::error('Failed to create Microsoft event', [
                'etr_event_id' => $etrFlowEvent->id,
                'user_id' => $this->integration->user_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Update a calendar event in Microsoft 365.
     */
    public function updateEvent(Event $etrFlowEvent, string $microsoftEventId): bool
    {
        try {
            $eventData = $this->buildEventData($etrFlowEvent);
            
            $calendarId = $this->integration->outlook_calendar_id ?: null;
            $endpoint = $calendarId 
                ? "https://graph.microsoft.com/v1.0/me/calendars/{$calendarId}/events/{$microsoftEventId}"
                : "https://graph.microsoft.com/v1.0/me/events/{$microsoftEventId}";
            
            $response = Http::withToken($this->accessToken)
                ->patch($endpoint, $eventData);

            if ($response->successful()) {
                Log::info('Microsoft event updated', [
                    'etr_event_id' => $etrFlowEvent->id,
                    'microsoft_event_id' => $microsoftEventId,
                    'user_id' => $this->integration->user_id
                ]);

                return true;
            }

            throw new \Exception('Failed to update event: ' . $response->body());

        } catch (\Exception $e) {
            Log::error('Failed to update Microsoft event', [
                'etr_event_id' => $etrFlowEvent->id,
                'microsoft_event_id' => $microsoftEventId,
                'user_id' => $this->integration->user_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Delete a calendar event from Microsoft 365.
     */
    public function deleteEvent(string $microsoftEventId): bool
    {
        try {
            $calendarId = $this->integration->outlook_calendar_id ?: null;
            $endpoint = $calendarId 
                ? "https://graph.microsoft.com/v1.0/me/calendars/{$calendarId}/events/{$microsoftEventId}"
                : "https://graph.microsoft.com/v1.0/me/events/{$microsoftEventId}";
            
            $response = Http::withToken($this->accessToken)
                ->delete($endpoint);

            if ($response->successful()) {
                Log::info('Microsoft event deleted', [
                    'microsoft_event_id' => $microsoftEventId,
                    'user_id' => $this->integration->user_id
                ]);

                return true;
            }

            throw new \Exception('Failed to delete event: ' . $response->body());

        } catch (\Exception $e) {
            Log::error('Failed to delete Microsoft event', [
                'microsoft_event_id' => $microsoftEventId,
                'user_id' => $this->integration->user_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get an event from Microsoft 365.
     */
    public function getEvent(string $microsoftEventId): ?array
    {
        try {
            $calendarId = $this->integration->outlook_calendar_id ?: null;
            $endpoint = $calendarId 
                ? "https://graph.microsoft.com/v1.0/me/calendars/{$calendarId}/events/{$microsoftEventId}"
                : "https://graph.microsoft.com/v1.0/me/events/{$microsoftEventId}";
            
            $response = Http::withToken($this->accessToken)
                ->get($endpoint);

            if ($response->successful()) {
                return $response->json();
            }

            return null;

        } catch (\Exception $e) {
            Log::error('Failed to fetch Microsoft event', [
                'microsoft_event_id' => $microsoftEventId,
                'user_id' => $this->integration->user_id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Test the Microsoft Graph connection.
     */
    public function testConnection(): array
    {
        try {
            // Test by fetching user profile
            $userResponse = Http::withToken($this->accessToken)
                ->get('https://graph.microsoft.com/v1.0/me');
                
            if (!$userResponse->successful()) {
                throw new \Exception('Failed to fetch user profile: ' . $userResponse->body());
            }
            
            $user = $userResponse->json();
            
            // Test by fetching calendars
            $calendars = $this->getCalendars();
            
            return [
                'success' => true,
                'user' => [
                    'name' => $user['displayName'] ?? 'Unknown',
                    'email' => $user['mail'] ?? $user['userPrincipalName'] ?? 'Unknown',
                ],
                'calendars_count' => count($calendars),
                'calendars' => array_map(function ($calendar) {
                    return [
                        'id' => $calendar['id'] ?? null,
                        'name' => $calendar['name'] ?? 'Unknown',
                        'canEdit' => $calendar['canEdit'] ?? false,
                    ];
                }, $calendars)
            ];
            
        } catch (\Exception $e) {
            Log::error('Microsoft Graph connection test failed', [
                'user_id' => $this->integration->user_id,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Build event data for Microsoft Graph API.
     */
    private function buildEventData(Event $etrFlowEvent): array
    {
        $syncSettings = $this->integration->getSyncSettingsWithDefaults();
        
        // Basic event information
        $subject = $this->buildEventSubject($etrFlowEvent);
        $body = $this->buildEventBody($etrFlowEvent, $syncSettings);
        
        $eventData = [
            'subject' => $subject,
            'body' => [
                'contentType' => 'html',
                'content' => $body
            ],
            'start' => [
                'dateTime' => $etrFlowEvent->start_date->format('Y-m-d\TH:i:s.v'),
                'timeZone' => config('app.timezone')
            ],
            'end' => [
                'dateTime' => $etrFlowEvent->end_date->format('Y-m-d\TH:i:s.v'),
                'timeZone' => config('app.timezone')
            ],
            'isAllDay' => $etrFlowEvent->all_day ?? false
        ];
        
        // Location
        if ($etrFlowEvent->pickup_address || $etrFlowEvent->location) {
            $eventData['location'] = [
                'displayName' => $etrFlowEvent->pickup_address ?: $etrFlowEvent->location
            ];
        }
        
        // Categories
        if (!empty($syncSettings['event_category'])) {
            $eventData['categories'] = [$syncSettings['event_category']];
        }
        
        // Attendees
        $attendees = $this->buildEventAttendees($etrFlowEvent);
        if (!empty($attendees)) {
            $eventData['attendees'] = $attendees;
        }
        
        return $eventData;
    }

    /**
     * Build event subject line.
     */
    private function buildEventSubject(Event $etrFlowEvent): string
    {
        if ($etrFlowEvent->is_pickup_event && $etrFlowEvent->pickupRequest) {
            $pickupRequest = $etrFlowEvent->pickupRequest;
            $loadSize = $pickupRequest->load_size ? ucfirst($pickupRequest->load_size) . ' Load' : 'Pickup';
            $customerName = $pickupRequest->contact_name;
            
            if ($pickupRequest->business_name) {
                return "{$customerName} ({$pickupRequest->business_name}) - {$loadSize} Pickup";
            } else {
                return "{$customerName} - {$loadSize} Pickup";
            }
        }
        
        return $etrFlowEvent->title ?: 'ETRFlow Event';
    }

    /**
     * Build event body with pickup details.
     */
    private function buildEventBody(Event $etrFlowEvent, array $syncSettings): string
    {
        $body = "<h3>ETRFlow Pickup Details</h3>";
        
        if ($etrFlowEvent->is_pickup_event && $etrFlowEvent->pickupRequest) {
            $pickupRequest = $etrFlowEvent->pickupRequest;
            
            // Contact Information
            $body .= "<h4>Contact Information</h4>";
            $body .= "<p><strong>Contact:</strong> {$pickupRequest->contact_name}</p>";
            if ($pickupRequest->business_name) {
                $body .= "<p><strong>Business:</strong> {$pickupRequest->business_name}</p>";
            }
            $body .= "<p><strong>Email:</strong> {$pickupRequest->email}</p>";
            $body .= "<p><strong>Phone:</strong> {$pickupRequest->phone}</p>";
            
            // Pickup Details
            if ($syncSettings['include_item_details']) {
                $body .= "<h4>Pickup Details</h4>";
                if ($pickupRequest->load_size) {
                    $body .= "<p><strong>Load Size:</strong> " . ucfirst($pickupRequest->load_size) . "</p>";
                }
                if ($pickupRequest->accessibility_level) {
                    $body .= "<p><strong>Accessibility:</strong> " . ucfirst($pickupRequest->accessibility_level) . "</p>";
                }
                if ($pickupRequest->item_types) {
                    $itemTypes = is_array($pickupRequest->item_types) 
                        ? implode(', ', array_map('ucwords', str_replace('_', ' ', $pickupRequest->item_types)))
                        : $pickupRequest->item_types;
                    $body .= "<p><strong>Item Types:</strong> {$itemTypes}</p>";
                }
                if ($pickupRequest->item_specifics) {
                    $body .= "<p><strong>Item Details:</strong> {$pickupRequest->item_specifics}</p>";
                }
            }
            
            // Location Details
            $body .= "<h4>Location Details</h4>";
            $body .= "<p><strong>Address:</strong> {$pickupRequest->pickup_address}</p>";
            if ($pickupRequest->property_location_details) {
                $body .= "<p><strong>Property Location:</strong> {$pickupRequest->property_location_details}</p>";
            }
            if ($pickupRequest->driver_instructions) {
                $body .= "<p><strong>Driver Instructions:</strong> {$pickupRequest->driver_instructions}</p>";
            }
            if ($pickupRequest->other_notes) {
                $body .= "<p><strong>Additional Notes:</strong> {$pickupRequest->other_notes}</p>";
            }
            
            // Staff Assignment
            if ($etrFlowEvent->assignedDriver) {
                $body .= "<h4>Assignment</h4>";
                $body .= "<p><strong>Assigned Driver:</strong> {$etrFlowEvent->assignedDriver->name}</p>";
                if ($etrFlowEvent->staff_needed) {
                    $body .= "<p><strong>Staff Needed:</strong> {$etrFlowEvent->staff_needed}</p>";
                }
            }
        } else {
            // Non-pickup event
            if ($etrFlowEvent->description) {
                $body .= "<p>{$etrFlowEvent->description}</p>";
            }
        }
        
        // Add edit link and read-only warning
        $body .= "<hr>";
        
        if ($etrFlowEvent->is_pickup_event && $etrFlowEvent->pickupRequest) {
            $editUrl = route('pickup-requests.edit', $etrFlowEvent->pickupRequest->id);
            $body .= "<p><strong>📝 <a href='{$editUrl}' target='_blank' style='color: #0066cc; text-decoration: underline;'>Edit this pickup in ETRFlow2</a></strong></p>";
        } elseif ($etrFlowEvent->id) {
            $editUrl = route('events.edit', $etrFlowEvent->id);
            $body .= "<p><strong>📝 <a href='{$editUrl}' target='_blank' style='color: #0066cc; text-decoration: underline;'>Edit this event in ETRFlow2</a></strong></p>";
        }
        
        $body .= "<p><strong>⚠️ Important:</strong> Changes must be made in ETRFlow2, not in Outlook. Any changes made in Outlook will be overwritten.</p>";
        $body .= "<p><small>Synced from ETRFlow2 - " . now()->format('Y-m-d H:i:s') . "</small></p>";
        
        return $body;
    }

    /**
     * Build event attendees.
     */
    private function buildEventAttendees(Event $etrFlowEvent): array
    {
        $attendees = [];
        
        // Add assigned driver
        if ($etrFlowEvent->assignedDriver && $etrFlowEvent->assignedDriver->email) {
            $attendees[] = [
                'emailAddress' => [
                    'address' => $etrFlowEvent->assignedDriver->email,
                    'name' => $etrFlowEvent->assignedDriver->name
                ],
                'type' => 'required'
            ];
        }
        
        // Note: Customer emails are included in event details but not as attendees
        // to avoid sending unwanted calendar invitations to customers
        
        return $attendees;
    }
}