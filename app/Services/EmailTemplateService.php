<?php

namespace App\Services;

use App\Models\EmailTemplate;
use App\Models\GlobalConfig;
use Illuminate\Support\Facades\Log;

class EmailTemplateService
{
    /**
     * Render an email template with the provided data
     */
    public function renderTemplate(string $templateName, array $data = []): ?array
    {
        $template = EmailTemplate::findByName($templateName);
        
        if (!$template) {
            Log::warning("Email template not found: {$templateName}");
            return null;
        }

        // Prepare the data with common variables
        $templateData = $this->prepareTemplateData($data);
        
        // Handle special template-specific processing
        $templateData = $this->processTemplateSpecificData($templateName, $templateData);

        return $template->render($templateData);
    }

    /**
     * Prepare common template data
     */
    private function prepareTemplateData(array $data): array
    {
        // Get contact email from GlobalConfig site_email (business email), fallback to mail from address
        $contactEmail = GlobalConfig::getValue('site_email') ?: config('mail.from.address');
        
        // Add common variables available to all templates
        $commonData = [
            'app_name' => config('app.name'),
            'current_date' => now()->format('F j, Y'),
            'current_datetime' => now()->format('F j, Y g:i A'),
            'contact_email' => $contactEmail,
        ];

        return array_merge($commonData, $data);
    }

    /**
     * Process template-specific data transformations
     */
    private function processTemplateSpecificData(string $templateName, array $data): array
    {
        switch ($templateName) {
            case 'daily_timeclock_report':
                return $this->processDailyTimeclockData($data);

            case 'pickup_request_notification':
                return $this->processPickupRequestData($data);

            case 'pickup_confirmation':
                return $this->processPickupConfirmationData($data);

            case 'pickup_confirmation_reminder':
                return $this->processPickupConfirmationReminderData($data);

            case 'pickup_final_reminder':
                return $this->processPickupFinalReminderData($data);

            case 'pickup_cancellation':
                return $this->processPickupCancellationData($data);

            case 'form_submission_notification':
            case 'form_submission_notification_full':
                return $this->processFormSubmissionData($data);

            case 'form_submission_pdf_delivery':
                return $this->processFormSubmissionPdfDeliveryData($data);

            default:
                return $data;
        }
    }

    /**
     * Process data for daily timeclock report template
     */
    private function processDailyTimeclockData(array $data): array
    {
        // Format the report date if it's a Carbon instance
        if (isset($data['report_date']) && is_object($data['report_date'])) {
            $data['report_date'] = $data['report_date']->format('F j, Y');
        }

        // Format numbers
        if (isset($data['total_hours_worked'])) {
            $data['total_hours_worked'] = number_format($data['total_hours_worked'], 1);
        }
        
        if (isset($data['total_break_hours'])) {
            $data['total_break_hours'] = number_format($data['total_break_hours'], 1);
        }

        // Count employees if we have report data
        if (isset($data['report_data']) && is_array($data['report_data'])) {
            $data['employee_count'] = count($data['report_data']);
            
            // Generate employee details table
            $data['employee_details'] = $this->generateEmployeeDetailsTable($data['report_data']);
        }

        return $data;
    }

    /**
     * Process data for pickup request notification template
     */
    private function processPickupRequestData(array $data): array
    {
        // Handle pickup request object
        if (isset($data['pickup_request']) && is_object($data['pickup_request'])) {
            $pickup = $data['pickup_request'];
            
            $data['pickup_id'] = $pickup->id;
            $data['status'] = $pickup->status;
            $data['contact_name'] = $pickup->contact_name;
            $data['business_name'] = $pickup->business_name ?? 'N/A';
            $data['email'] = $pickup->email;
            $data['phone'] = $pickup->phone;
            $data['pickup_address'] = $pickup->pickup_address;
            $data['load_size'] = $pickup->load_size ?? 'Not specified';
            $data['driver_instructions'] = $pickup->driver_instructions ?? 'None';
            
            // Format pickup date
            if ($pickup->preferred_pickup_date) {
                $data['pickup_date'] = $pickup->preferred_pickup_date->format('F j, Y');
            }
            
            // Handle item types array
            if ($pickup->item_types && is_array($pickup->item_types)) {
                $data['item_types'] = implode(', ', $pickup->item_types);
            } else {
                $data['item_types'] = 'Not specified';
            }
            
            // Generate view URL
            $data['view_url'] = route('pickup-requests.show', $pickup->id);
        }

        // Set notification type
        if (isset($data['type'])) {
            $data['notification_type'] = $data['type'] === 'new_request' ? 'New' : 'Updated';
        }

        return $data;
    }

    /**
     * Process data for pickup confirmation template
     */
    private function processPickupConfirmationData(array $data): array
    {
        // Handle pickup request object
        if (isset($data['pickup_request']) && is_object($data['pickup_request'])) {
            $pickup = $data['pickup_request'];

            $data['pickup_id'] = $pickup->id;
            $data['contact_name'] = $pickup->contact_name;
            $data['business_name'] = $pickup->business_name ?? '';
            $data['pickup_address'] = $pickup->pickup_address;
            $data['driver_instructions'] = $pickup->driver_instructions ?? 'None';
            $data['load_size'] = $pickup->load_size ?? 'Not specified';

            // Handle item types array
            if ($pickup->item_types && is_array($pickup->item_types)) {
                $data['item_types'] = implode(', ', $pickup->item_types);
            } else {
                $data['item_types'] = 'Not specified';
            }

            // Handle event and pickup date
            if ($pickup->event && $pickup->event->start_date) {
                $timezone = \App\Models\GlobalConfig::getTimeZone();
                $data['pickup_date'] = $pickup->event->start_date->setTimezone($timezone)->format('l, F j, Y \a\t g:i A');
            } else {
                $data['pickup_date'] = 'To be scheduled';
            }

            // Handle driver information
            if ($pickup->event && $pickup->event->assigned_driver_id && $pickup->event->assignedDriver) {
                $data['driver_name'] = $pickup->event->assignedDriver->name;
            } else {
                $data['driver_name'] = 'To be assigned';
            }

            // Generate unique management link using the pickup request's token
            if ($pickup->management_token) {
                $data['management_url'] = route('pickup.manage', ['token' => $pickup->management_token]);
            } else {
                $data['management_url'] = '#';
            }
        }

        return $data;
    }

    /**
     * Process data for pickup confirmation reminder template
     */
    private function processPickupConfirmationReminderData(array $data): array
    {
        // Handle pickup request object
        if (isset($data['pickup_request']) && is_object($data['pickup_request'])) {
            $pickup = $data['pickup_request'];

            $data['pickup_id'] = $pickup->id;
            $data['contact_name'] = $pickup->contact_name;
            $data['business_name'] = $pickup->business_name ?? '';
            $data['pickup_address'] = $pickup->pickup_address;
            $data['driver_instructions'] = $pickup->driver_instructions ?? 'None';
            $data['load_size'] = $pickup->load_size ?? 'Not specified';

            // Handle item types array
            if ($pickup->item_types && is_array($pickup->item_types)) {
                $data['item_types'] = implode(', ', $pickup->item_types);
            } else {
                $data['item_types'] = 'Not specified';
            }

            // Handle event and pickup date
            if ($pickup->event && $pickup->event->start_date) {
                $timezone = \App\Models\GlobalConfig::getTimeZone();
                $data['pickup_date'] = $pickup->event->start_date->setTimezone($timezone)->format('l, F j, Y \\a\\t g:i A');
                
                // Calculate hours until pickup
                $data['hours_until_pickup'] = now()->diffInHours($pickup->event->start_date, false);
            } else {
                $data['pickup_date'] = 'To be scheduled';
                $data['hours_until_pickup'] = 0;
            }

            // Handle driver information
            if ($pickup->event && $pickup->event->assigned_driver_id && $pickup->event->assignedDriver) {
                $data['driver_name'] = $pickup->event->assignedDriver->name;
            } else {
                $data['driver_name'] = 'To be assigned';
            }

            // Generate management URLs using the pickup request's token
            if ($pickup->management_token) {
                $data['management_url'] = route('pickup.manage', ['token' => $pickup->management_token]);
                $data['confirm_url'] = route('pickup.confirm', ['token' => $pickup->management_token]);
                $data['cancel_url'] = route('pickup.manage', ['token' => $pickup->management_token]);
            } else {
                $data['management_url'] = '#';
                $data['confirm_url'] = '#';
                $data['cancel_url'] = '#';
            }
        }

        return $data;
    }

    /**
     * Process data for pickup final reminder template
     */
    private function processPickupFinalReminderData(array $data): array
    {
        // Handle pickup request object
        if (isset($data['pickup_request']) && is_object($data['pickup_request'])) {
            $pickup = $data['pickup_request'];

            $data['pickup_id'] = $pickup->id;
            $data['contact_name'] = $pickup->contact_name;
            $data['business_name'] = $pickup->business_name ?? '';
            $data['pickup_address'] = $pickup->pickup_address;
            $data['driver_instructions'] = $pickup->driver_instructions ?? 'None';
            $data['load_size'] = $pickup->load_size ?? 'Not specified';

            // Handle item types array
            if ($pickup->item_types && is_array($pickup->item_types)) {
                $data['item_types'] = implode(', ', $pickup->item_types);
            } else {
                $data['item_types'] = 'Not specified';
            }

            // Handle event and pickup date
            if ($pickup->event && $pickup->event->start_date) {
                $timezone = \App\Models\GlobalConfig::getTimeZone();
                $data['pickup_date'] = $pickup->event->start_date->setTimezone($timezone)->format('l, F j, Y \\a\\t g:i A');
                
                // Calculate hours until pickup
                $data['hours_until_pickup'] = now()->diffInHours($pickup->event->start_date, false);
            } else {
                $data['pickup_date'] = 'To be scheduled';
                $data['hours_until_pickup'] = 0;
            }

            // Handle driver information
            if ($pickup->event && $pickup->event->assigned_driver_id && $pickup->event->assignedDriver) {
                $data['driver_name'] = $pickup->event->assignedDriver->name;
            } else {
                $data['driver_name'] = 'To be assigned';
            }

            // Generate management URLs using the pickup request's token
            // Note: No confirm_url for final reminders since pickup is already confirmed
            if ($pickup->management_token) {
                $data['management_url'] = route('pickup.manage', ['token' => $pickup->management_token]);
                $data['cancel_url'] = route('pickup.manage', ['token' => $pickup->management_token]);
            } else {
                $data['management_url'] = '#';
                $data['cancel_url'] = '#';
            }
        }

        return $data;
    }

    /**
     * Process data for pickup cancellation template
     */
    private function processPickupCancellationData(array $data): array
    {
        // Handle pickup request object
        if (isset($data['pickup_request']) && is_object($data['pickup_request'])) {
            $pickup = $data['pickup_request'];

            $data['pickup_id'] = $pickup->id;
            $data['contact_name'] = $pickup->contact_name;
            $data['business_name'] = $pickup->business_name ?? '';
            $data['pickup_address'] = $pickup->pickup_address;

            // Handle event and pickup date
            if ($pickup->event && $pickup->event->start_date) {
                $timezone = \App\Models\GlobalConfig::getTimeZone();
                $data['pickup_date'] = $pickup->event->start_date->setTimezone($timezone)->format('l, F j, Y \\a\\t g:i A');
            } else {
                $data['pickup_date'] = 'To be scheduled';
            }

            // Format cancellation date
            if ($pickup->cancelled_at) {
                $timezone = \App\Models\GlobalConfig::getTimeZone();
                $data['cancellation_date'] = $pickup->cancelled_at->setTimezone($timezone)->format('l, F j, Y \\a\\t g:i A');
            } else {
                $data['cancellation_date'] = now()->format('l, F j, Y \\a\\t g:i A');
            }
        }

        // Set default values if not provided
        if (!isset($data['cancellation_reason'])) {
            $data['cancellation_reason'] = 'Appointment cancelled';
        }

        if (!isset($data['cancelled_by'])) {
            $data['cancelled_by'] = 'Staff';
        }

        return $data;
    }

    /**
     * Generate HTML table for employee details
     */
    private function generateEmployeeDetailsTable(array $reportData): string
    {
        if (empty($reportData)) {
            return '<p>No employee data available.</p>';
        }

        $html = '<table style="width: 100%; border-collapse: collapse; margin-top: 20px;">';
        $html .= '<thead>';
        $html .= '<tr style="background-color: #f4f4f4;">';
        $html .= '<th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Employee</th>';
        $html .= '<th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Hours Worked</th>';
        $html .= '<th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Break Hours</th>';
        $html .= '<th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">First In</th>';
        $html .= '<th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Last Out</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody>';

        foreach ($reportData as $employee) {
            $html .= '<tr>';
            $html .= '<td style="padding: 10px; border-bottom: 1px solid #ddd;">' . e($employee['user']->name) . '</td>';
            $html .= '<td style="padding: 10px; border-bottom: 1px solid #ddd;">' . number_format($employee['hours_worked'], 1) . '</td>';
            $html .= '<td style="padding: 10px; border-bottom: 1px solid #ddd;">' . number_format($employee['break_hours'], 1) . '</td>';
            $html .= '<td style="padding: 10px; border-bottom: 1px solid #ddd;">' . 
                     ($employee['first_clock_in'] ? $employee['first_clock_in']->format('g:i A') : 'N/A') . '</td>';
            $html .= '<td style="padding: 10px; border-bottom: 1px solid #ddd;">' . 
                     ($employee['last_clock_out'] ? $employee['last_clock_out']->format('g:i A') : 'N/A') . '</td>';
            $html .= '</tr>';
        }

        $html .= '</tbody>';
        $html .= '</table>';

        return $html;
    }

    /**
     * Get available templates
     */
    public function getAvailableTemplates(): array
    {
        return EmailTemplate::where('is_active', true)->get()->toArray();
    }

    /**
     * Check if a template exists and is active
     */
    public function templateExists(string $templateName): bool
    {
        return EmailTemplate::where('name', $templateName)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * Process form submission notification data
     */
    private function processFormSubmissionData(array $data): array
    {
        $form = $data['form'] ?? null;
        $submission = $data['submission'] ?? null;

        if (!$form || !$submission) {
            return $data;
        }

        $processedData = [
            'form_name' => $form->name,
            'form_id' => $form->id,
            'submission_id' => $submission->id,
            'submitter_name' => $submission->submitter_name,
            'submitter_email' => $submission->submitter_email,
            'submitter_phone' => $submission->submitter_phone ?? 'Not provided',
            'submitter_contact' => $submission->submitter_contact ?? $submission->submitter_name,
            'submission_date' => $submission->created_at->format('M d, Y \a\t g:i A'),
            'view_url' => route('form-submissions.show', $submission->id),
            'app_name' => config('app.name', 'ETRFlow'),
            'current_date' => now()->format('M d, Y \a\t g:i A'),
        ];

        // Add form data table for full notification
        if (isset($data['include_full_data']) && $data['include_full_data']) {
            $processedData['form_data_table'] = $this->buildFormDataTable($form, $submission);
        }

        return array_merge($data, $processedData);
    }

    /**
     * Build HTML table for form submission data
     */
    private function buildFormDataTable($form, $submission): string
    {
        $html = '<table class="form-data-table">';
        $html .= '<thead>';
        $html .= '<tr>';
        $html .= '<th>Field</th>';
        $html .= '<th>Value</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody>';

        // Add business information for forms requiring customer link
        if ($form->requires_customer_link) {
            $businessName = $submission->getFormDataValue('business_name');
            $address = $submission->getFormDataValue('address');
            $customerType = $submission->getFormDataValue('customer_type');

            if ($businessName) {
                $html .= '<tr>';
                $html .= '<td><strong>Business Name</strong></td>';
                $html .= '<td>' . htmlspecialchars($businessName) . '</td>';
                $html .= '</tr>';
            }

            if ($customerType) {
                $html .= '<tr>';
                $html .= '<td><strong>Customer Type</strong></td>';
                $html .= '<td>' . htmlspecialchars($customerType) . '</td>';
                $html .= '</tr>';
            }

            if ($address) {
                $html .= '<tr>';
                $html .= '<td><strong>Address</strong></td>';
                $html .= '<td>' . htmlspecialchars($address) . '</td>';
                $html .= '</tr>';
            }
        }

        // Add form fields
        foreach ($form->fields as $field) {
            if ($field->isInput() && $field->name) {
                $value = $submission->getFormDataValue($field->name);

                if ($value !== null && $value !== '') {
                    $html .= '<tr>';
                    $html .= '<td><strong>' . htmlspecialchars($field->label ?: $field->name) . '</strong></td>';

                    // Handle different field types
                    if (is_array($value)) {
                        $html .= '<td>' . htmlspecialchars(implode(', ', $value)) . '</td>';
                    } else {
                        $html .= '<td>' . htmlspecialchars($value) . '</td>';
                    }

                    $html .= '</tr>';
                }
            }
        }

        // Add signature information if present
        if ($submission->signature_data && $submission->signed_at) {
            $html .= '<tr>';
            $html .= '<td><strong>Digital Signature</strong></td>';
            $html .= '<td>Signed on ' . $submission->signed_at->format('M d, Y \a\t g:i A') . '</td>';
            $html .= '</tr>';
        }

        $html .= '</tbody>';
        $html .= '</table>';

        return $html;
    }

    /**
     * Process form submission PDF delivery data
     */
    private function processFormSubmissionPdfDeliveryData(array $data): array
    {
        $form = $data['form'] ?? null;
        $submission = $data['submission'] ?? null;

        if (!$form || !$submission) {
            return $data;
        }

        $processedData = [
            'form_name' => $form->name,
            'form_id' => $form->id,
            'submission_id' => $submission->id,
            'submitter_name' => $submission->submitter_name,
            'submitter_email' => $submission->submitter_email,
            'submitter_contact' => $submission->submitter_contact ?? $submission->submitter_name,
            'submission_date' => $submission->created_at->format('M d, Y \\a\\t g:i A'),
            'approval_date' => $submission->approved_at ? $submission->approved_at->format('M d, Y \\a\\t g:i A') : 'Not approved',
            'approved_by' => $submission->approver ? $submission->approver->name : 'Unknown',
            'customer_name' => $submission->customer ? $submission->customer->name : $submission->submitter_name,
            'app_name' => config('app.name', 'ETRFlow'),
            'current_date' => now()->format('M d, Y \\a\\t g:i A'),
        ];

        return array_merge($data, $processedData);
    }

    /**
     * Render an email template with attachments support
     */
    public function renderTemplateWithAttachments(string $templateName, array $data = [], array $attachments = []): ?array
    {
        $rendered = $this->renderTemplate($templateName, $data);
        
        if ($rendered && !empty($attachments)) {
            $rendered['attachments'] = $attachments;
        }
        
        return $rendered;
    }
}