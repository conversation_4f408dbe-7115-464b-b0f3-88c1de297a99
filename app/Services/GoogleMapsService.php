<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class GoogleMapsService
{
    private $apiKey;
    private $baseUrl = 'https://places.googleapis.com/v1';

    public function __construct()
    {
        $this->apiKey = config('services.google_maps.api_key');
        
        if (!$this->apiKey) {
            throw new \Exception('Google Maps API key not configured');
        }
    }

    /**
     * Fetch autocomplete suggestions using the new Places Data API
     *
     * @param string $input The search input
     * @param string|null $sessionToken Session token for billing optimization
     * @param array $options Additional options (restrictions, location bias, etc.)
     * @return array
     */
    public function fetchAutocompleteSuggestions(string $input, ?string $sessionToken = null, array $options = []): array
    {
        try {
            // Prepare the request body
            $requestBody = [
                'input' => $input,
                'languageCode' => $options['language'] ?? 'en-US',
                'regionCode' => $options['region'] ?? 'us',
            ];

            // Add session token if provided
            if ($sessionToken) {
                $requestBody['sessionToken'] = $sessionToken;
            }

            // Add location restrictions if provided
            if (!empty($options['restrictions'])) {
                $requestBody['includedRegionCodes'] = $options['restrictions'];
            }

            // Add location bias if provided
            if (!empty($options['origin'])) {
                $requestBody['origin'] = [
                    'latitude' => $options['origin']['lat'],
                    'longitude' => $options['origin']['lng']
                ];
            }

            // Add location restriction (bounds) if provided
            if (!empty($options['locationRestriction'])) {
                $requestBody['locationRestriction'] = [
                    'rectangle' => [
                        'low' => [
                            'latitude' => $options['locationRestriction']['south'],
                            'longitude' => $options['locationRestriction']['west']
                        ],
                        'high' => [
                            'latitude' => $options['locationRestriction']['north'],
                            'longitude' => $options['locationRestriction']['east']
                        ]
                    ]
                ];
            }

            // Add place types if provided
            if (!empty($options['includedPrimaryTypes'])) {
                $requestBody['includedPrimaryTypes'] = $options['includedPrimaryTypes'];
            }

            Log::info('Making Google Maps autocomplete request', [
                'input' => $input,
                'sessionToken' => $sessionToken ? 'present' : 'none',
                'options' => $options
            ]);

            // Make the API request
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'X-Goog-Api-Key' => $this->apiKey,
                'X-Goog-FieldMask' => 'suggestions.placePrediction.place,suggestions.placePrediction.placeId,suggestions.placePrediction.text,suggestions.placePrediction.structuredFormat,suggestions.placePrediction.types'
            ])->post($this->baseUrl . '/places:autocomplete', $requestBody);

            if (!$response->successful()) {
                Log::error('Google Maps autocomplete API error', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'input' => $input
                ]);

                return [
                    'success' => false,
                    'error' => 'Failed to fetch suggestions from Google Maps API',
                    'suggestions' => []
                ];
            }

            $data = $response->json();
            
            Log::info('Google Maps autocomplete response received', [
                'suggestions_count' => count($data['suggestions'] ?? [])
            ]);

            return [
                'success' => true,
                'suggestions' => $data['suggestions'] ?? [],
                'sessionToken' => $sessionToken
            ];

        } catch (\Exception $e) {
            Log::error('Google Maps autocomplete service error', [
                'message' => $e->getMessage(),
                'input' => $input,
                'sessionToken' => $sessionToken
            ]);

            return [
                'success' => false,
                'error' => 'An error occurred while fetching suggestions',
                'suggestions' => []
            ];
        }
    }

    /**
     * Fetch place details using place ID
     *
     * @param string $placeId The place ID
     * @param string|null $sessionToken Session token for billing optimization
     * @param array $fields Fields to retrieve
     * @return array
     */
    public function fetchPlaceDetails(string $placeId, ?string $sessionToken = null, array $fields = []): array
    {
        try {
            // Default fields if none provided - using new Places API fields
            if (empty($fields)) {
                $fields = [
                    'id',
                    'displayName',
                    'formattedAddress',
                    'shortFormattedAddress',  // New API field for concise addresses
                    'location',
                    'addressComponents',
                    'types',
                    'primaryType',           // New API field
                    'primaryTypeDisplayName' // New API field
                ];
            }

            // Prepare query parameters
            $queryParams = [
                'languageCode' => 'en-US'
            ];

            // Add session token if provided
            if ($sessionToken) {
                $queryParams['sessionToken'] = $sessionToken;
            }

            // Ensure place ID is in correct format: places/{placeId}
            $resourceName = $placeId;
            if (!str_starts_with($placeId, 'places/')) {
                $resourceName = 'places/' . $placeId;
            }

            Log::info('Making Google Maps place details request', [
                'originalPlaceId' => $placeId,
                'resourceName' => $resourceName,
                'sessionToken' => $sessionToken ? 'present' : 'none',
                'fields' => $fields
            ]);

            // Create field mask from fields array
            $fieldMask = implode(',', $fields);

            // Construct the full URL using the resource name format
            // GET https://places.googleapis.com/v1/{name=places/*}
            $url = $this->baseUrl . '/' . $resourceName;

            Log::info('Place details API URL', ['url' => $url, 'fieldMask' => $fieldMask]);

            // Make the API request using GET method
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'X-Goog-Api-Key' => $this->apiKey,
                'X-Goog-FieldMask' => $fieldMask
            ])->get($url, $queryParams);

            if (!$response->successful()) {
                Log::error('Google Maps place details API error', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'headers' => $response->headers(),
                    'originalPlaceId' => $placeId,
                    'resourceName' => $resourceName,
                    'url' => $url,
                    'queryParams' => $queryParams,
                    'fieldMask' => $fieldMask,
                    'method' => 'GET'
                ]);

                return [
                    'success' => false,
                    'error' => 'Failed to fetch place details from Google Maps API',
                    'place' => null
                ];
            }

            $data = $response->json();

            Log::info('Google Maps place details response received', [
                'placeId' => $placeId,
                'hasPlace' => isset($data['place']),
                'responseKeys' => array_keys($data ?? []),
                'fullResponse' => $data
            ]);

            // The new Places API returns the place data directly, not wrapped in a 'place' key
            $place = $data;

            // Check if we have valid place data
            if (isset($data['id']) || isset($data['displayName']) || isset($data['formattedAddress'])) {
                return [
                    'success' => true,
                    'place' => $place,
                    'sessionToken' => $sessionToken
                ];
            } else {
                Log::warning('Place details response missing expected fields', [
                    'placeId' => $placeId,
                    'response' => $data
                ]);

                return [
                    'success' => false,
                    'error' => 'Place details response missing expected fields',
                    'place' => null
                ];
            }

        } catch (\Exception $e) {
            Log::error('Google Maps place details service error', [
                'message' => $e->getMessage(),
                'placeId' => $placeId,
                'sessionToken' => $sessionToken
            ]);

            return [
                'success' => false,
                'error' => 'An error occurred while fetching place details',
                'place' => null
            ];
        }
    }

    /**
     * Calculate distance and travel time between two addresses using Distance Matrix API
     *
     * @param string $origin Origin address
     * @param string $destination Destination address
     * @param array $options Additional options (units, mode, avoid, etc.)
     * @return array
     */
    public function getDistanceMatrix(string $origin, string $destination, array $options = []): array
    {
        try {
            // Validate API key
            if (empty($this->apiKey)) {
                Log::error('Google Maps API key not configured for distance matrix');
                return [
                    'success' => false,
                    'error' => 'Google Maps API key not configured',
                    'distance' => null,
                    'duration' => null,
                    'distance_text' => 'Unavailable',
                    'duration_text' => 'Unavailable'
                ];
            }

            // Build request parameters
            $params = [
                'origins' => $origin,
                'destinations' => $destination,
                'units' => $options['units'] ?? 'imperial', // imperial for miles, metric for kilometers
                'mode' => $options['mode'] ?? 'driving', // driving, walking, bicycling, transit
                'avoid' => $options['avoid'] ?? null, // tolls, highways, ferries, indoor
                'key' => $this->apiKey
            ];

            // Remove null values
            $params = array_filter($params, function($value) {
                return $value !== null;
            });

            Log::info('Making Google Maps Distance Matrix request', [
                'origin' => $origin,
                'destination' => $destination,
                'options' => $options
            ]);

            // Make the API request
            $response = Http::get('https://maps.googleapis.com/maps/api/distancematrix/json', $params);

            if (!$response->successful()) {
                Log::error('Google Maps Distance Matrix API error', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'origin' => $origin,
                    'destination' => $destination
                ]);

                return [
                    'success' => false,
                    'error' => 'Failed to fetch distance data from Google Maps API',
                    'distance' => null,
                    'duration' => null,
                    'distance_text' => 'Could not calculate',
                    'duration_text' => 'Could not calculate'
                ];
            }

            $data = $response->json();

            Log::info('Google Maps Distance Matrix response received', [
                'status' => $data['status'] ?? 'unknown'
            ]);

            // Check API response status
            if (($data['status'] ?? '') !== 'OK') {
                Log::error('Google Maps Distance Matrix API returned error status', [
                    'status' => $data['status'] ?? 'unknown',
                    'error_message' => $data['error_message'] ?? 'No error message provided'
                ]);

                return [
                    'success' => false,
                    'error' => 'Google Maps API error: ' . ($data['error_message'] ?? $data['status'] ?? 'Unknown error'),
                    'distance' => null,
                    'duration' => null,
                    'distance_text' => 'Could not calculate',
                    'duration_text' => 'Could not calculate'
                ];
            }

            // Extract the first (and only) result
            $rows = $data['rows'] ?? [];
            if (empty($rows) || empty($rows[0]['elements'])) {
                Log::error('Google Maps Distance Matrix API returned no results', [
                    'response' => $data
                ]);

                return [
                    'success' => false,
                    'error' => 'No route found between the addresses',
                    'distance' => null,
                    'duration' => null,
                    'distance_text' => 'No route found',
                    'duration_text' => 'No route found'
                ];
            }

            $element = $rows[0]['elements'][0];

            // Check element status
            if (($element['status'] ?? '') !== 'OK') {
                Log::error('Google Maps Distance Matrix element error', [
                    'element_status' => $element['status'] ?? 'unknown',
                    'origin' => $origin,
                    'destination' => $destination
                ]);

                $errorMessages = [
                    'NOT_FOUND' => 'One or both addresses could not be found',
                    'ZERO_RESULTS' => 'No route could be found between the addresses',
                    'MAX_ROUTE_LENGTH_EXCEEDED' => 'Route is too long to calculate'
                ];

                $errorMessage = $errorMessages[$element['status']] ?? 'Could not calculate route';

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'distance' => null,
                    'duration' => null,
                    'distance_text' => 'Unavailable',
                    'duration_text' => 'Unavailable'
                ];
            }

            // Extract distance and duration
            $distance = $element['distance'] ?? null;
            $duration = $element['duration'] ?? null;

            return [
                'success' => true,
                'distance' => $distance['value'] ?? null, // Distance in meters
                'duration' => $duration['value'] ?? null, // Duration in seconds
                'distance_text' => $distance['text'] ?? 'Unavailable', // Human readable distance
                'duration_text' => $duration['text'] ?? 'Unavailable', // Human readable duration
                'origin_addresses' => $data['origin_addresses'] ?? [],
                'destination_addresses' => $data['destination_addresses'] ?? []
            ];

        } catch (\Exception $e) {
            Log::error('Google Maps Distance Matrix service error', [
                'message' => $e->getMessage(),
                'origin' => $origin,
                'destination' => $destination
            ]);

            return [
                'success' => false,
                'error' => 'An error occurred while calculating distance and travel time',
                'distance' => null,
                'duration' => null,
                'distance_text' => 'Could not calculate',
                'duration_text' => 'Could not calculate'
            ];
        }
    }

    /**
     * Generate a new session token
     *
     * @return string
     */
    public function generateSessionToken(): string
    {
        // Generate a unique session token
        return 'session_' . uniqid() . '_' . time();
    }
}
