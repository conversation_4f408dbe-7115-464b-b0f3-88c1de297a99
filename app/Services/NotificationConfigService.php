<?php

namespace App\Services;

use App\Models\NotificationConfig;
use App\Models\User;
use App\Models\UserGroup;
use App\Services\NotificationService;
use App\Helpers\NotificationHelper;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class NotificationConfigService
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Save notification configurations from form data
     */
    public function saveConfigurationsFromFormData(Model $model, array $configData): Collection
    {
        $savedConfigs = collect();

        foreach ($configData as $eventType => $data) {
            if (!isset($data['is_enabled']) || !$data['is_enabled']) {
                // Remove existing configuration if disabled
                $model->removeNotificationConfig($eventType);
                continue;
            }

            $config = $this->saveConfiguration($model, $eventType, $data);
            $savedConfigs->push($config);
        }

        return $savedConfigs;
    }

    /**
     * Save a single notification configuration
     */
    public function saveConfiguration(Model $model, string $eventType, array $data): NotificationConfig
    {
        $configData = [
            'target_type' => $data['target_type'] ?? NotificationConfig::TARGET_USER_GROUP,
            'target_ids' => $data['target_ids'] ?? [],
            'urgency' => $data['urgency'] ?? NotificationConfig::URGENCY_NORMAL,
            'is_enabled' => (bool)($data['is_enabled'] ?? true),
            'custom_title' => $data['custom_title'] ?? null,
            'custom_message' => $data['custom_message'] ?? null,
            'include_link' => (bool)($data['include_link'] ?? true),
            'link_text' => $data['link_text'] ?? null,
            'expires_after_days' => $data['expires_after_days'] ?? null,
            'is_dismissible' => (bool)($data['is_dismissible'] ?? true),
            'auto_dismiss' => (bool)($data['auto_dismiss'] ?? false),
        ];

        return $model->setNotificationConfig($eventType, $configData['target_type'], $configData['target_ids'], $configData);
    }

    /**
     * Send notifications based on model's configuration for a specific event
     */
    public function sendNotificationsForEvent(
        Model $model, 
        string $eventType, 
        array $templateData = [],
        ?string $defaultTitle = null,
        ?string $defaultMessage = null,
        ?string $defaultLinkUrl = null,
        ?string $defaultLinkText = null
    ): Collection {
        $configs = $model->getNotificationConfigsForEvent($eventType);
        $sentNotifications = collect();

        foreach ($configs as $config) {
            $notification = $this->sendNotificationFromConfig(
                $config, 
                $templateData, 
                $defaultTitle, 
                $defaultMessage, 
                $defaultLinkUrl, 
                $defaultLinkText
            );

            if ($notification) {
                $sentNotifications->push($notification);
            }
        }

        return $sentNotifications;
    }

    /**
     * Send a notification based on a configuration
     */
    public function sendNotificationFromConfig(
        NotificationConfig $config,
        array $templateData = [],
        ?string $defaultTitle = null,
        ?string $defaultMessage = null,
        ?string $defaultLinkUrl = null,
        ?string $defaultLinkText = null
    ) {
        // Determine title and message
        $title = $config->custom_title ?: $defaultTitle ?: $this->getDefaultTitle($config->event_type, $templateData);
        $message = $config->custom_message ?: $defaultMessage ?: $this->getDefaultMessage($config->event_type, $templateData);

        // Determine link
        $linkUrl = $config->include_link ? $defaultLinkUrl : null;
        $linkText = $config->link_text ?: $defaultLinkText;

        // Get expiration date
        $expiresAt = $config->getExpirationDate();

        // Send notification based on target type
        switch ($config->target_type) {
            case NotificationConfig::TARGET_ALL_USERS:
                return $this->notificationService->sendToAllUsers(
                    $title,
                    $message,
                    $config->urgency,
                    $linkUrl,
                    $linkText,
                    $expiresAt,
                    $config->is_dismissible,
                    $config->auto_dismiss
                );

            case NotificationConfig::TARGET_USER_GROUP:
                $userGroups = $config->getTargetUserGroups();
                if ($userGroups->isEmpty()) {
                    return null;
                }

                return $this->notificationService->sendToUserGroups(
                    $userGroups,
                    $title,
                    $message,
                    $config->urgency,
                    $linkUrl,
                    $linkText,
                    $expiresAt,
                    $config->is_dismissible,
                    $config->auto_dismiss
                );

            case NotificationConfig::TARGET_SPECIFIC_USERS:
                $users = $config->getTargetUsers();
                if ($users->isEmpty()) {
                    return null;
                }

                return $this->notificationService->sendToUsers(
                    $users,
                    $title,
                    $message,
                    $config->urgency,
                    $linkUrl,
                    $linkText,
                    $expiresAt,
                    $config->is_dismissible,
                    $config->auto_dismiss
                );

            default:
                return null;
        }
    }

    /**
     * Get default notification title for an event type
     */
    protected function getDefaultTitle(string $eventType, array $templateData = []): string
    {
        return match ($eventType) {
            NotificationConfig::EVENT_FORM_SUBMITTED => 'New Form Submission',
            NotificationConfig::EVENT_TASK_CREATED => 'New Task Created',
            NotificationConfig::EVENT_TASK_ASSIGNED => 'Task Assigned',
            NotificationConfig::EVENT_INVOICE_CREATED => 'New Invoice Created',
            NotificationConfig::EVENT_PICKUP_REQUESTED => 'New Pickup Request',
            default => 'New Notification',
        };
    }

    /**
     * Get default notification message for an event type
     */
    protected function getDefaultMessage(string $eventType, array $templateData = []): string
    {
        return match ($eventType) {
            NotificationConfig::EVENT_FORM_SUBMITTED => $this->getFormSubmissionMessage($templateData),
            NotificationConfig::EVENT_TASK_CREATED => $this->getTaskCreatedMessage($templateData),
            NotificationConfig::EVENT_TASK_ASSIGNED => $this->getTaskAssignedMessage($templateData),
            NotificationConfig::EVENT_INVOICE_CREATED => $this->getInvoiceCreatedMessage($templateData),
            NotificationConfig::EVENT_PICKUP_REQUESTED => $this->getPickupRequestMessage($templateData),
            default => 'A new notification has been created.',
        };
    }

    /**
     * Get form submission notification message
     */
    protected function getFormSubmissionMessage(array $data): string
    {
        $form = $data['form'] ?? null;
        $submission = $data['submission'] ?? null;

        if (!$form || !$submission) {
            return 'A new form submission has been received.';
        }

        $submitterName = $submission->submitter_name ?? 'Unknown';
        return "A new submission for '{$form->name}' has been received from {$submitterName}.";
    }

    /**
     * Get task created notification message
     */
    protected function getTaskCreatedMessage(array $data): string
    {
        $task = $data['task'] ?? null;
        
        if (!$task) {
            return 'A new task has been created.';
        }

        return "A new task '{$task->title}' has been created.";
    }

    /**
     * Get task assigned notification message
     */
    protected function getTaskAssignedMessage(array $data): string
    {
        $task = $data['task'] ?? null;
        $assignee = $data['assignee'] ?? null;
        
        if (!$task) {
            return 'A task has been assigned.';
        }

        $assigneeName = $assignee ? $assignee->name : 'you';
        return "Task '{$task->title}' has been assigned to {$assigneeName}.";
    }

    /**
     * Get invoice created notification message
     */
    protected function getInvoiceCreatedMessage(array $data): string
    {
        $invoice = $data['invoice'] ?? null;
        
        if (!$invoice) {
            return 'A new invoice has been created.';
        }

        $customerName = $invoice->customer ? $invoice->customer->name : 'Unknown Customer';
        return "Invoice #{$invoice->id} has been created for {$customerName}.";
    }

    /**
     * Get pickup request notification message
     */
    protected function getPickupRequestMessage(array $data): string
    {
        $pickupRequest = $data['pickup_request'] ?? null;
        
        if (!$pickupRequest) {
            return 'A new pickup request has been submitted.';
        }

        $contactName = $pickupRequest->contact_name ?? 'Unknown';
        $businessName = $pickupRequest->business_name ? " from {$pickupRequest->business_name}" : '';
        
        return "A new pickup request has been submitted by {$contactName}{$businessName}.";
    }

    /**
     * Get all available event types with labels
     */
    public function getAvailableEventTypes(): array
    {
        return [
            NotificationConfig::EVENT_FORM_SUBMITTED => 'Form Submission',
            NotificationConfig::EVENT_TASK_CREATED => 'Task Created',
            NotificationConfig::EVENT_TASK_ASSIGNED => 'Task Assigned',
            NotificationConfig::EVENT_INVOICE_CREATED => 'Invoice Created',
            NotificationConfig::EVENT_PICKUP_REQUESTED => 'Pickup Request',
        ];
    }

    /**
     * Create default notification configurations for a model
     */
    public function createDefaultConfigurations(Model $model, array $eventTypes = []): Collection
    {
        $configs = collect();
        
        if (empty($eventTypes)) {
            $eventTypes = [NotificationConfig::EVENT_FORM_SUBMITTED]; // Default for forms
        }

        foreach ($eventTypes as $eventType) {
            $config = $model->setNotificationConfig(
                $eventType,
                NotificationConfig::TARGET_USER_GROUP,
                [], // No default targets
                [
                    'urgency' => NotificationConfig::URGENCY_NORMAL,
                    'is_enabled' => false, // Disabled by default
                ]
            );
            
            $configs->push($config);
        }

        return $configs;
    }
}
