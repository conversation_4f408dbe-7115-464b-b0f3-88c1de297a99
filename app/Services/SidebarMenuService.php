<?php

namespace App\Services;

use Illuminate\Support\Facades\Auth;
use App\Models\GlobalConfig;
use Illuminate\Support\Facades\Schema;

class SidebarMenuService
{
    protected $menuConfig;

    public function __construct()
    {
        $this->loadMenuConfig();
    }

    protected function loadMenuConfig()
    {
        $configPath = config_path('sidebar-menu.json');
        if (file_exists($configPath)) {
            $this->menuConfig = json_decode(file_get_contents($configPath), true);
        } else {
            $this->menuConfig = ['menu_items' => []];
        }
    }

    public function getMenuItems()
    {
        $user = Auth::user();
        if (!$user) {
            return [];
        }

        $filteredItems = [];

        foreach ($this->menuConfig['menu_items'] as $item) {
            if ($this->shouldShowItem($item, $user)) {
                if ($item['type'] === 'dropdown') {
                    $item['items'] = $this->filterDropdownItems($item['items'] ?? [], $user);
                    // Only show dropdown if it has visible items
                    if (!empty($item['items'])) {
                        $filteredItems[] = $item;
                    }
                } else {
                    $filteredItems[] = $item;
                }
            }
        }

        return $filteredItems;
    }

protected function shouldShowItem($item, $user)
    {
        // Check condition (like tasks_enabled)
        if (isset($item['condition'])) {
            if ($item['condition'] === 'tasks_enabled') {
                // Safely check if global_configs table exists and tasks are enabled
                if (!Schema::hasTable('global_configs')) {
                    return true; // Default to showing during migration
                }
                
                try {
                    return GlobalConfig::isTasksEnabled();
                } catch (\Exception $e) {
                    // If there's any error accessing the config, default to showing the item
                    return true;
                }
            }
        }

        // Check permissions
        if (!empty($item['permissions'])) {
            return $this->hasAnyPermission($item['permissions'], $user);
        }

        return true;
    }

    protected function filterDropdownItems($items, $user)
    {
        $filteredItems = [];

        foreach ($items as $item) {
            if ($this->shouldShowItem($item, $user)) {
                $filteredItems[] = $item;
            }
        }

        return $filteredItems;
    }

    protected function hasAnyPermission($permissions, $user)
    {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission, $user)) {
                return true;
            }
        }
        return false;
    }

    protected function hasPermission($permission, $user)
    {
        // Handle special admin permission
        if ($permission === 'is_admin') {
            return $user->isAdmin();
        }

        // Handle multiple permissions separated by comma (OR logic)
        if (strpos($permission, ',') !== false) {
            $perms = array_map('trim', explode(',', $permission));
            foreach ($perms as $perm) {
                if (trim($perm) === 'is_admin') {
                    if ($user->isAdmin()) {
                        return true;
                    }
                } else {
                    if ($user->hasPermission(trim($perm))) {
                        return true;
                    }
                }
            }
            return false;
        }

        // Single permission check
        return $user->hasPermission($permission);
    }
}
