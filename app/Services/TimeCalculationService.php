<?php

namespace App\Services;

use App\Models\User;
use App\Models\TimeCard;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class TimeCalculationService
{
    /**
     * Calculate time summary for a user or multiple users within a date range
     *
     * @param int|array $userIds User ID or array of user IDs
     * @param Carbon|string $startDate Start date
     * @param Carbon|string $endDate End date
     * @param bool $includeDetails Whether to include detailed time card data
     * @return array Time summary data
     */
    public function calculateTimeSummary($userIds, $startDate, $endDate, $includeDetails = false)
    {
        // Convert dates to Carbon instances if they're not already
        $startDate = $startDate instanceof Carbon ? $startDate : Carbon::parse($startDate);
        $endDate = $endDate instanceof Carbon ? $endDate : Carbon::parse($endDate);

        // Ensure we're working with an array of user IDs
        $userIds = is_array($userIds) ? $userIds : [$userIds];

        // Get users
        $users = User::whereIn('id', $userIds)->get();

        $result = [
            'users' => [],
            'total_hours' => 0,
            'total_break_hours' => 0,
            'total_sick_time' => 0,
            'total_vacation_time' => 0,
            'total_pto' => 0,
            'total_billable_hours' => 0,
            'total_overtime_hours' => 0,
            'total_regular_hours' => 0,
        ];

        foreach ($users as $user) {
            $userSummary = $this->calculateUserTimeSummary($user, $startDate, $endDate, $includeDetails);
            $result['users'][$user->id] = $userSummary;

            // Add to overall totals
            $result['total_hours'] += $userSummary['total_hours'];
            $result['total_break_hours'] += $userSummary['total_break_hours'];
            $result['total_sick_time'] += $userSummary['total_sick_time'];
            $result['total_vacation_time'] += $userSummary['total_vacation_time'];
            $result['total_pto'] += $userSummary['total_pto'];
            $result['total_billable_hours'] += $userSummary['total_billable_hours'];
            $result['total_overtime_hours'] += $userSummary['total_overtime_hours'];
            $result['total_regular_hours'] += $userSummary['total_regular_hours'];
        }

        // Add formatted values for the totals
        $result['formatted_total_hours'] = $this->formatSecondsToTime($result['total_hours'] * 3600);
        $result['formatted_total_break_hours'] = $this->formatSecondsToTime($result['total_break_hours'] * 3600);
        $result['formatted_total_sick_time'] = $this->formatSecondsToTime($result['total_sick_time'] * 3600);
        $result['formatted_total_vacation_time'] = $this->formatSecondsToTime($result['total_vacation_time'] * 3600);
        $result['formatted_total_pto'] = $this->formatSecondsToTime($result['total_pto'] * 3600);
        $result['formatted_total_billable_hours'] = $this->formatSecondsToTime($result['total_billable_hours'] * 3600);

        return $result;
    }

    /**
     * Calculate time summary for a single user within a date range
     *
     * @param User $user User model
     * @param Carbon $startDate Start date
     * @param Carbon $endDate End date
     * @param bool $includeDetails Whether to include detailed time card data
     * @return array User time summary data
     */
    public function calculateUserTimeSummary(User $user, Carbon $startDate, Carbon $endDate, $includeDetails = false)
    {
        // Ensure time cards exist for the date range
        $this->ensureTimeCardsExist($user, $startDate, $endDate);

        // Get all time cards for the date range
        $timeCards = $user->timeCards()
            ->whereBetween('date', [$startDate, $endDate])
            ->orderBy('date')
            ->get();

        Log::info("Calculating time summary for user {$user->id}", [
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'time_card_count' => $timeCards->count()
        ]);

        // Calculate totals
        $totalSeconds = 0;
        $totalBreakSeconds = 0;
        $totalSickTimeSeconds = 0;
        $totalVacationTimeSeconds = 0;

        // Group time cards by week for overtime calculation
        $weeklyTimeCards = [];

        foreach ($timeCards as $timeCard) {
            // For today's time card, get the real-time hours
            if ($timeCard->date->isToday()) {
                $currentHours = $timeCard->getCurrentHours();

                $totalSeconds += $this->timeStringToSeconds($currentHours['formatted_total_hours']);
                $totalBreakSeconds += $this->timeStringToSeconds($currentHours['formatted_total_break_hours']);
                $totalSickTimeSeconds += $this->timeStringToSeconds($currentHours['formatted_total_sick_time']);
                $totalVacationTimeSeconds += $this->timeStringToSeconds($currentHours['formatted_total_vacation_time']);
            } else {
                $totalSeconds += $this->timeStringToSeconds($timeCard->total_hours);
                $totalBreakSeconds += $this->timeStringToSeconds($timeCard->total_break_hours);
                $totalSickTimeSeconds += $this->timeStringToSeconds($timeCard->total_sick_time);
                $totalVacationTimeSeconds += $this->timeStringToSeconds($timeCard->total_vacation_time);
            }

            // Group by week for overtime calculation
            $weekStartDate = $timeCard->date->copy()->startOfWeek(Carbon::SUNDAY)->format('Y-m-d');
            if (!isset($weeklyTimeCards[$weekStartDate])) {
                $weeklyTimeCards[$weekStartDate] = [
                    'start_date' => $timeCard->date->copy()->startOfWeek(Carbon::SUNDAY),
                    'end_date' => $timeCard->date->copy()->endOfWeek(Carbon::SUNDAY),
                    'time_cards' => [],
                    'total_seconds' => 0,
                    'total_break_seconds' => 0,
                    'total_sick_time_seconds' => 0,
                    'total_vacation_time_seconds' => 0
                ];
            }

            $weeklyTimeCards[$weekStartDate]['time_cards'][] = $timeCard;

            // Add to weekly totals
            if ($timeCard->date->isToday()) {
                $weeklyTimeCards[$weekStartDate]['total_seconds'] += $this->timeStringToSeconds($currentHours['formatted_total_hours']);
                $weeklyTimeCards[$weekStartDate]['total_break_seconds'] += $this->timeStringToSeconds($currentHours['formatted_total_break_hours']);
                $weeklyTimeCards[$weekStartDate]['total_sick_time_seconds'] += $this->timeStringToSeconds($currentHours['formatted_total_sick_time']);
                $weeklyTimeCards[$weekStartDate]['total_vacation_time_seconds'] += $this->timeStringToSeconds($currentHours['formatted_total_vacation_time']);
            } else {
                $weeklyTimeCards[$weekStartDate]['total_seconds'] += $this->timeStringToSeconds($timeCard->total_hours);
                $weeklyTimeCards[$weekStartDate]['total_break_seconds'] += $this->timeStringToSeconds($timeCard->total_break_hours);
                $weeklyTimeCards[$weekStartDate]['total_sick_time_seconds'] += $this->timeStringToSeconds($timeCard->total_sick_time);
                $weeklyTimeCards[$weekStartDate]['total_vacation_time_seconds'] += $this->timeStringToSeconds($timeCard->total_vacation_time);
            }
        }

        // Calculate decimal hours
        $totalHours = round($totalSeconds / 3600, 2);
        $totalBreakHours = round($totalBreakSeconds / 3600, 2);
        $totalSickTime = round($totalSickTimeSeconds / 3600, 2);
        $totalVacationTime = round($totalVacationTimeSeconds / 3600, 2);
        $totalPtoTime = $totalSickTime + $totalVacationTime;

        // IMPORTANT: The total_hours from the time card already excludes breaks
        // So we should NOT subtract breaks again when calculating billable hours

        // Calculate billable hours (work hours + PTO)
        $totalBillableHours = $totalHours + $totalPtoTime;

        // Calculate overtime (hours over 40 per week)
        $totalOvertimeHours = 0;
        $totalRegularHours = 0;

        // Process each week for overtime calculation
        foreach ($weeklyTimeCards as $weekData) {
            $weekTotalSeconds = $weekData['total_seconds'];

            // The total_hours from the time card already excludes breaks
            // So we should NOT subtract breaks again when calculating work hours
            $weekWorkHours = $weekTotalSeconds / 3600;

            // Calculate overtime for this week
            if ($weekWorkHours > 40) {
                $weekOvertimeHours = $weekWorkHours - 40;
                $weekRegularHours = 40;
                $totalOvertimeHours += $weekOvertimeHours;
            } else {
                $weekRegularHours = $weekWorkHours;
            }

            $totalRegularHours += $weekRegularHours;
        }

        // Prepare the result
        $result = [
            'user' => $user,
            'total_hours' => $totalHours,
            'total_break_hours' => $totalBreakHours,
            'total_sick_time' => $totalSickTime,
            'total_vacation_time' => $totalVacationTime,
            'total_pto' => $totalPtoTime,
            'total_billable_hours' => $totalBillableHours,
            'total_overtime_hours' => $totalOvertimeHours,
            'total_regular_hours' => $totalRegularHours,
            'formatted_total_hours' => $this->formatSecondsToTime($totalSeconds),
            'formatted_total_break_hours' => $this->formatSecondsToTime($totalBreakSeconds),
            'formatted_total_sick_time' => $this->formatSecondsToTime($totalSickTimeSeconds),
            'formatted_total_vacation_time' => $this->formatSecondsToTime($totalVacationTimeSeconds),
            'formatted_total_pto' => $this->formatSecondsToTime($totalSickTimeSeconds + $totalVacationTimeSeconds),
            'formatted_total_billable_hours' => $this->formatSecondsToTime(($totalHours * 3600) + $totalSickTimeSeconds + $totalVacationTimeSeconds),
        ];

        // Include detailed data if requested
        if ($includeDetails) {
            $result['time_cards'] = $timeCards;
            $result['weekly_data'] = $weeklyTimeCards;
        }

        return $result;
    }

    /**
     * Calculate weekly time summary for a user (Sunday to Saturday)
     *
     * @param User $user User model
     * @return array Weekly time summary data
     */
    public function calculateWeeklyTimeSummary(User $user)
    {
        $startDate = now()->startOfWeek(Carbon::SUNDAY);
        $endDate = now()->endOfWeek(Carbon::SUNDAY);

        return $this->calculateUserTimeSummary($user, $startDate, $endDate);
    }

    /**
     * Ensure time cards exist for a user within a date range
     *
     * @param User $user User model
     * @param Carbon $startDate Start date
     * @param Carbon $endDate End date
     */
    public function ensureTimeCardsExist(User $user, Carbon $startDate, Carbon $endDate)
    {
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $this->getOrCreateTimeCard($user, $date);
        }
    }

    /**
     * Get or create a time card for a user on a specific date
     *
     * @param User $user User model
     * @param Carbon $date Date
     * @return TimeCard Time card model
     */
    public function getOrCreateTimeCard(User $user, Carbon $date)
    {
        $formattedDate = $date->format('Y-m-d');

        return TimeCard::firstOrCreate(
            [
                'user_id' => $user->id,
                'date' => $formattedDate
            ],
            [
                'total_hours' => '00:00:00',
                'total_break_hours' => '00:00:00',
                'total_sick_time' => '00:00:00',
                'total_vacation_time' => '00:00:00'
            ]
        );
    }

    /**
     * Convert time string (HH:MM:SS) to seconds
     *
     * @param string|null $timeString Time string in HH:MM:SS format
     * @return int Total seconds
     */
    public function timeStringToSeconds($timeString)
    {
        if (empty($timeString)) {
            return 0;
        }

        if (!is_string($timeString)) {
            return floatval($timeString);
        }

        $parts = explode(':', $timeString);

        if (count($parts) < 2) {
            return 0;
        }

        $hours = (int)$parts[0];
        $minutes = (int)$parts[1];
        $seconds = isset($parts[2]) ? (int)$parts[2] : 0;

        return $hours * 3600 + $minutes * 60 + $seconds;
    }

    /**
     * Format seconds to HH:MM:SS time string
     *
     * @param int|null $seconds Total seconds
     * @return string Formatted time string
     */
    public function formatSecondsToTime($seconds)
    {
        if (empty($seconds) || !is_numeric($seconds)) {
            $seconds = 0;
        }

        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $secs);
    }

    /**
     * Format seconds to HH:MM (no seconds)
     *
     * @param int|null $seconds Total seconds
     * @return string Formatted time string
     */
    public function formatSecondsToHoursMinutes($seconds)
    {
        if (empty($seconds) || !is_numeric($seconds)) {
            $seconds = 0;
        }

        $hours = floor($seconds / 3600);
        $minutes = round(($seconds % 3600) / 60);

        // Handle case where minutes round to 60
        if ($minutes == 60) {
            $hours++;
            $minutes = 0;
        }

        return sprintf('%02d:%02d', $hours, $minutes);
    }
}
