<?php

namespace App\Services;

use App\Models\Event;
use App\Models\MicrosoftCalendarIntegration;
use App\Models\CalendarSyncLog;
use App\Http\Controllers\MicrosoftOAuthController;
use Illuminate\Support\Facades\Log;

class PickupCalendarSyncService
{
    private MicrosoftOAuthController $oauthController;

    public function __construct(MicrosoftOAuthController $oauthController)
    {
        $this->oauthController = $oauthController;
    }
    /**
     * Sync a single pickup event to the shared calendar.
     */
    public function syncEvent(Event $event): array
    {
        if (!$event->is_pickup_event) {
            return ['success' => false, 'message' => 'Event is not a pickup event'];
        }

        $sharedIntegration = MicrosoftCalendarIntegration::getSharedCalendarIntegration();
        
        if (!$sharedIntegration) {
            return ['success' => false, 'message' => 'No active shared calendar integration found'];
        }

        try {
            $result = $this->syncEventForIntegration($event, $sharedIntegration);
            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to sync event to shared calendar', [
                'event_id' => $event->id,
                'integration_id' => $sharedIntegration->id,
                'error' => $e->getMessage()
            ]);
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Sync a pickup event for a specific integration.
     */
    public function syncEventForIntegration(Event $event, MicrosoftCalendarIntegration $integration): array
    {
        // Check if integration is active
        if (!$integration->isActive()) {
            return ['success' => false, 'message' => 'Integration is not active'];
        }

        // Check sync settings
        $syncSettings = $integration->getSyncSettingsWithDefaults();
        if (!$this->shouldSyncEvent($event, $syncSettings)) {
            return ['success' => true, 'message' => 'Event skipped based on sync settings', 'skipped' => true];
        }

        try {
            $graphService = new MicrosoftGraphService($integration, $this->oauthController);
            
            if ($event->microsoft_event_id) {
                // Update existing event
                $result = $this->updateEventInMicrosoft($event, $integration, $graphService, $event->microsoft_event_id);
            } else {
                // Create new event
                $result = $this->createEventInMicrosoft($event, $integration, $graphService);
            }

            // Update last sync time
            $integration->update(['last_sync_at' => now()]);

            return $result;

        } catch (\Exception $e) {
            $this->logSyncError($event, $integration, 'sync_event', $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a pickup event from the shared Microsoft calendar.
     */
    public function deleteEvent(Event $event): array
    {
        $sharedIntegration = MicrosoftCalendarIntegration::getSharedCalendarIntegration();
        
        if (!$sharedIntegration || !$event->microsoft_event_id) {
            return [
                'success' => false,
                'message' => 'No shared calendar integration or Microsoft event ID found'
            ];
        }

        try {
            $graphService = new MicrosoftGraphService($sharedIntegration, $this->oauthController);
            $graphService->deleteEvent($event->microsoft_event_id);

            $this->logSyncSuccess($event, $sharedIntegration, 'delete', null, [
                'microsoft_event_id' => $event->microsoft_event_id
            ]);

            // Clear Microsoft event ID from the ETRFlow event
            $event->update(['microsoft_event_id' => null]);

            return [
                'success' => true,
                'action' => 'deleted'
            ];

        } catch (\Exception $e) {
            $this->logSyncError($event, $sharedIntegration, 'delete', $e->getMessage(), [
                'microsoft_event_id' => $event->microsoft_event_id
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Sync all pickup events for the shared calendar integration.
     */
    public function syncAllEventsForIntegration(MicrosoftCalendarIntegration $integration, ?\DateTime $since = null): array
    {
        $query = Event::where('is_pickup_event', true)
            ->where('is_active', true);

        if ($since) {
            $query->where('updated_at', '>=', $since);
        }

        $events = $query->get();
        $results = [
            'total' => $events->count(),
            'synced' => 0,
            'skipped' => 0,
            'failed' => 0,
            'errors' => []
        ];

        $this->logSyncStart($integration, 'sync_all', count($events));

        foreach ($events as $event) {
            try {
                $result = $this->syncEventForIntegration($event, $integration);
                
                if ($result['success']) {
                    if ($result['skipped'] ?? false) {
                        $results['skipped']++;
                    } else {
                        $results['synced']++;
                    }
                } else {
                    $results['failed']++;
                    $results['errors'][] = "Event {$event->id}: " . $result['message'];
                }

            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Event {$event->id}: " . $e->getMessage();
            }
        }

        $integration->update([
            'last_sync_at' => now(),
            'sync_status' => $results['failed'] > 0 ? 'failed' : 'completed'
        ]);

        $this->logSyncComplete($integration, 'sync_all', $results);

        return $results;
    }

    /**
     * Get sync status for the shared calendar integration.
     */
    public function getSyncStatus(): ?array
    {
        $sharedIntegration = MicrosoftCalendarIntegration::where('is_shared_calendar', true)->with('user')->first();
        
        if (!$sharedIntegration) {
            return null;
        }

        return [
            'user_id' => $sharedIntegration->user_id,
            'user_name' => $sharedIntegration->user->name,
            'user_email' => $sharedIntegration->user->email,
            'microsoft_email' => $sharedIntegration->microsoft_user_email,
            'calendar_name' => $sharedIntegration->calendar_name,
            'sync_enabled' => $sharedIntegration->sync_enabled,
            'sync_status' => $sharedIntegration->sync_status,
            'last_sync_at' => $sharedIntegration->last_sync_at,
            'token_expires_at' => $sharedIntegration->token_expires_at,
            'is_active' => $sharedIntegration->isActive(),
            'is_token_expired' => $sharedIntegration->isTokenExpired(),
            'error_message' => $sharedIntegration->error_message,
            'is_shared_calendar' => true,
        ];
    }

    /**
     * Create event in Microsoft calendar.
     */
    private function createEventInMicrosoft(Event $event, MicrosoftCalendarIntegration $integration, MicrosoftGraphService $graphService): array
    {
        $this->logSyncStart($integration, 'create', null, $event);

        try {
            $microsoftEventId = $graphService->createEvent($event);

            // Store Microsoft event ID in ETRFlow event
            $event->update(['microsoft_event_id' => $microsoftEventId]);

            $this->logSyncSuccess($event, $integration, 'create', $microsoftEventId);

            return [
                'success' => true,
                'action' => 'created',
                'microsoft_event_id' => $microsoftEventId
            ];

        } catch (\Exception $e) {
            $this->logSyncError($event, $integration, 'create', $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update event in Microsoft calendar.
     */
    private function updateEventInMicrosoft(Event $event, MicrosoftCalendarIntegration $integration, MicrosoftGraphService $graphService, string $microsoftEventId): array
    {
        try {
            $success = $graphService->updateEvent($event, $microsoftEventId);

            if ($success) {
                $this->logSyncSuccess($event, $integration, 'update', $microsoftEventId);
                return [
                    'success' => true,
                    'action' => 'updated',
                    'microsoft_event_id' => $microsoftEventId
                ];
            } else {
                throw new \Exception('Update operation returned false');
            }

        } catch (\Exception $e) {
            // If event doesn't exist in Microsoft, try to create it
            if (strpos($e->getMessage(), 'does not exist') !== false || 
                strpos($e->getMessage(), 'ErrorItemNotFound') !== false) {
                
                Log::info('Microsoft event not found, creating new one', [
                    'event_id' => $event->id,
                    'microsoft_event_id' => $microsoftEventId,
                    'user_id' => $integration->user_id
                ]);

                return $this->createEventInMicrosoft($event, $integration, $graphService);
            }

            $this->logSyncError($event, $integration, 'update', $e->getMessage(), [
                'microsoft_event_id' => $microsoftEventId
            ]);
            throw $e;
        }
    }

    /**
     * Check if event should be synced based on settings.
     */
    private function shouldSyncEvent(Event $event, array $syncSettings): bool
    {
        if (!$syncSettings['sync_pickup_events']) {
            return false;
        }

        // Check if we should sync completed events
        if ($event->pickupRequest && $event->pickupRequest->status === 'completed' && !$syncSettings['sync_completed_events']) {
            return false;
        }

        // Check if we should sync cancelled events
        if ($event->pickupRequest && $event->pickupRequest->status === 'cancelled' && !$syncSettings['sync_cancelled_events']) {
            return false;
        }

        return true;
    }

    

    /**
     * Log sync operation start.
     */
    private function logSyncStart(MicrosoftCalendarIntegration $integration, string $action, ?int $eventCount = null, ?Event $event = null): CalendarSyncLog
    {
        return CalendarSyncLog::create([
            'microsoft_calendar_integration_id' => $integration->id,
            'event_id' => $event?->id,
            'action' => $action,
            'status' => 'pending',
            'started_at' => now(),
            'request_data' => [
                'event_count' => $eventCount,
                'sync_settings' => $integration->getSyncSettingsWithDefaults()
            ]
        ]);
    }

    /**
     * Log successful sync operation.
     */
    private function logSyncSuccess(Event $event, MicrosoftCalendarIntegration $integration, string $action, ?string $microsoftEventId = null, array $additionalData = []): void
    {
        CalendarSyncLog::create([
            'microsoft_calendar_integration_id' => $integration->id,
            'event_id' => $event->id,
            'microsoft_event_id' => $microsoftEventId,
            'action' => $action,
            'status' => 'success',
            'started_at' => now(),
            'completed_at' => now(),
            'response_data' => array_merge([
                'microsoft_event_id' => $microsoftEventId,
                'event_title' => $event->title
            ], $additionalData)
        ]);
    }

    /**
     * Log sync operation error.
     */
    private function logSyncError(Event $event, MicrosoftCalendarIntegration $integration, string $action, string $errorMessage, array $additionalData = []): void
    {
        CalendarSyncLog::create([
            'microsoft_calendar_integration_id' => $integration->id,
            'event_id' => $event->id,
            'action' => $action,
            'status' => 'failed',
            'error_message' => $errorMessage,
            'started_at' => now(),
            'completed_at' => now(),
            'request_data' => array_merge([
                'event_title' => $event->title
            ], $additionalData)
        ]);

        // Update integration status if there are repeated failures
        $recentFailures = CalendarSyncLog::where('microsoft_calendar_integration_id', $integration->id)
            ->where('status', 'failed')
            ->where('created_at', '>=', now()->subHours(1))
            ->count();

        if ($recentFailures >= 5) {
            $integration->update([
                'sync_status' => 'failed',
                'error_message' => "Multiple sync failures. Latest: {$errorMessage}"
            ]);
        }
    }

    /**
     * Log sync completion.
     */
    private function logSyncComplete(MicrosoftCalendarIntegration $integration, string $action, array $results): void
    {
        CalendarSyncLog::create([
            'microsoft_calendar_integration_id' => $integration->id,
            'action' => $action,
            'status' => $results['failed'] > 0 ? 'failed' : 'success',
            'started_at' => now(),
            'completed_at' => now(),
            'response_data' => $results
        ]);
    }
}