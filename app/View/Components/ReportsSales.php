<?php

namespace App\View\Components;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\Customer;
use App\Models\LineItem;
use Illuminate\View\Component;

class ReportsSales extends Component
{
    public $filterType;

    public function __construct($filterType = 'category')
    {
        $this->filterType = $filterType;
    }
    private function getSalesByCategory()
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();
    
        return LineItem::with('inventory.category')
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->get()
            ->groupBy(fn($item) => $item->inventory->category->name ?? 'Uncategorized')
            ->map(function ($items) {
                return [
                    'netRevenue' => $items->sum('net_revenue'),
                ];
            })
            ->sortByDesc('netRevenue');
    }
    
    private function getSalesByCustomerType()
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();
    
        return Customer::query()
            ->select(
                'type',
                DB::raw('SUM(invoices.total_price - invoices.total_discount) as netRevenue')
            )
            ->join('invoices', 'customers.id', '=', 'invoices.customer_id')
            ->whereBetween('invoices.created_at', [$startOfMonth, $endOfMonth])
            ->groupBy('type')
            ->orderByDesc('netRevenue')
            ->get()
            ->mapWithKeys(fn($row) => [
                $row->type ?? 'Unknown' => [
                    'netRevenue' => $row->netRevenue,
                ]
            ]);
    }
    
    
    public function getSalesData()
    {
        return match ($this->filterType) {
            'category' => $this->getSalesByCategory(),
            'customerType' => $this->getSalesByCustomerType(),
            default => collect(),
        };
    }

    public function render(): View
    {
        $salesData = $this->getSalesData();
        $filterType = $this->filterType;
        return view('components.reports-sales', compact('salesData', 'filterType'));
    }
}
