<?php

namespace App\View\Components;

use App\Models\LineItem;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class LatestSales extends Component
{
    public $latestSales;

    /**
     * Create a new component instance.
     */
    public function __construct()
    {
        $this->latestSales = LineItem::with(['invoice', 'inventory'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.latest-sales', ['latestSales' => $this->latestSales]);
    }
}
