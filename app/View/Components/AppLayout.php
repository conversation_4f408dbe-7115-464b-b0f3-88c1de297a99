<?php

namespace App\View\Components;

use Illuminate\View\Component;
use Illuminate\View\View;

class AppLayout extends Component
{
    /**
     * The title of the page.
     */
    public string|null $title;

    /**
     * The page title for the header.
     */
    public string|null $pageTitle;

    /**
     * The page icon for the header.
     */
    public string|null $pageIcon;

    /**
     * Custom action buttons for the header.
     */
    public array $actionButtons;

    /**
     * Predefined primary buttons for the header.
     */
    public array $primaryButtons;

    /**
     * Breadcrumb items for navigation.
     */
    public array $breadcrumbs;

    /**
     * Create a new component instance.
     *
     * @param string|null $title
     * @param string|null $pageTitle
     * @param string|null $pageIcon
     * @param array $actionButtons
     * @param array $primaryButtons
     * @param array $breadcrumbs
     */
    public function __construct(
        string $title = null,
        string $pageTitle = null,
        string $pageIcon = null,
        array $actionButtons = [],
        array $primaryButtons = [],
        array $breadcrumbs = []
    ) {
        $this->title = $title;
        $this->pageTitle = $pageTitle ?? $title;
        $this->pageIcon = $pageIcon;
        $this->actionButtons = $actionButtons;
        $this->primaryButtons = $primaryButtons;
        $this->breadcrumbs = $breadcrumbs;
    }

    /**
     * Get the view / contents that represents the component.
     */
    public function render(): View
    {
        return view('layouts.app', [
            'title' => $this->title,
            'pageTitle' => $this->pageTitle,
            'pageIcon' => $this->pageIcon,
            'actionButtons' => $this->actionButtons,
            'primaryButtons' => $this->primaryButtons,
            'breadcrumbs' => $this->breadcrumbs,
        ]);
    }
}
