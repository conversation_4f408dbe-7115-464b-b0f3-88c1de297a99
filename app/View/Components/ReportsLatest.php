<?php

namespace App\View\Components;

use App\Models\Report;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class ReportsLatest extends Component
{
    public $reports;

    /**
     * Create a new component instance.
     */
    public function __construct()
    {
        // Fetch the latest 10 reports
        $this->reports = Report::latest()->take(10)->get();
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.reports-latest');
    }
}
