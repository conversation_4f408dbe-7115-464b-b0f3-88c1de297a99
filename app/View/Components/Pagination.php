<?php

namespace App\View\Components;

use Illuminate\View\Component;
use Illuminate\Contracts\Pagination\Paginator;

class Pagination extends Component
{
    public Paginator $paginator;
    public ?int $pagination;

    /**
     * Create a new component instance.
     */
    public function __construct(Paginator $paginator, ?int $pagination = null)
    {
        $this->paginator = $paginator;
        $this->pagination = $pagination;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render()
    {
        return view('components.pagination');
    }
}
