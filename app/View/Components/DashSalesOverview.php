<?php

namespace App\View\Components;

use Illuminate\View\Component;
use App\Models\Department;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class DashSalesOverview extends Component
{
    public $salesData;

    public function __construct()
    {

        $currentDate = Carbon::now();

        // Explicitly calculate the start and end of the current month
        $currentMonthStart = Carbon::create($currentDate->year, $currentDate->month, 1, 0, 0, 0);
        
        // Calculate the start and end of the last month
        $lastMonthStart = (clone $currentMonthStart)->subMonth();
        $lastMonthEnd = (clone $lastMonthStart)->endOfMonth();
        
        // Calculate the start of the year
        $yearStart = Carbon::create($currentDate->year, 1, 1, 0, 0, 0);

        $this->salesData = Department::with(['lineItems.invoice' => function ($query) {
            $query->select('id', 'invoice_date');
        }])->get()->map(function ($department) use ($currentMonthStart, $lastMonthStart, $lastMonthEnd, $yearStart) {
            $lineItems = $department->lineItems;

            // Debugging: Print all relevant data
            foreach ($lineItems as $lineItem) {
                Log::info("LineItem ID: {$lineItem->id}, Invoice Date: " . ($lineItem->invoice->invoice_date ?? 'NULL'));
            }

            // Metrics for MTD
            $mtd = $this->calculateMetrics($lineItems, $currentMonthStart);

            // Metrics for Last Month
            $lastMonth = $this->calculateMetrics($lineItems, $lastMonthStart, $lastMonthEnd);

            // Metrics for YTD
            $ytd = $this->calculateMetrics($lineItems, $yearStart);

            return [
                'name' => $department->name,
                'mtd' => $mtd,
                'lastMonth' => $lastMonth,
                'ytd' => $ytd,
            ];
        });
    }

    /**
     * Calculate metrics for a given date range.
     */
    private function calculateMetrics($lineItems, $startDate, $endDate = null)
    {
        $filtered = $lineItems->filter(function ($item) use ($startDate, $endDate) {
            $invoiceDate = $item->invoice->invoice_date ? Carbon::parse($item->invoice->invoice_date) : null;
            return $invoiceDate && $invoiceDate >= $startDate && (!$endDate || $invoiceDate <= $endDate);
        });
    
        // Debugging: Log the filtered data
        Log::info("Filtered Data for Range [$startDate to " . ($endDate ?? 'NOW') . "]:", $filtered->toArray());
    
        $grossRevenue = $filtered->sum('subtotal');
        $totalTaxes = $filtered->sum('tax');
        $totalDiscounts = $filtered->sum('discount');
        $netRevenue = $grossRevenue - $totalTaxes - $totalDiscounts;
    
        return [
            'grossRevenue' => $grossRevenue,
            'totalTaxes' => $totalTaxes,
            'totalDiscounts' => $totalDiscounts,
            'netRevenue' => $netRevenue,
            'rawData' => $filtered->toArray(), // Attach raw filtered data
            'dateRange' => [
                'start' => $startDate->toDateString(),
                'end' => $endDate ? $endDate->toDateString() : 'NOW',
            ], // Attach the range for debugging
        ];
    }
    
    
    
    

    public function render()
    {
        return view('components.dash-sales-overview', ['salesData' => $this->salesData]);
    }
}
