<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class PickupCustomerSearch extends Component
{
    /**
     * The input ID.
     */
    public string $id;

    /**
     * The name input field attribute.
     */
    public string $name;

    /**
     * The placeholder text.
     */
    public string $placeholder;

    /**
     * The selected customer ID.
     */
    public ?int $selectedId;

    /**
     * The selected customer name.
     */
    public ?string $selectedName;

    /**
     * The pickup request instance for context.
     */
    public $pickupRequest;

    /**
     * Create a new component instance.
     */
    public function __construct(
        string $id = 'pickupCustomerSearch',
        string $name = 'customer_id',
        string $placeholder = 'Search by business name, contact name, email, or phone...',
        ?int $selectedId = null,
        ?string $selectedName = null,
        $pickupRequest = null
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->placeholder = $placeholder;
        $this->selectedId = $selectedId;
        $this->selectedName = $selectedName;
        $this->pickupRequest = $pickupRequest;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.pickup-customer-search');
    }
}
