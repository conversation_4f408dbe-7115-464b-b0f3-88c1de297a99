<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class DynamicCustomerSearch extends Component
{
    /**
     * The unique ID for the search input and related elements.
     */
    public string $id;

    /**
     * The name attribute for the hidden input storing the selected customer ID.
     */
    public string $name;

    /**
     * The placeholder text for the search input.
     */
    public string $placeholder;

    /**
     * The action to perform when the search is submitted.
     */
    public string $action;

    /**
     * The ID of the quick create button to click instead of redirecting.
     */
    public ?string $quickCreateButtonId;

    /**
     * The selected customer ID.
     */
    public ?int $selectedId;

    /**
     * The selected customer name.
     */
    public ?string $selectedName;

    /**
     * Create a new component instance.
     *
     * @param string $id
     * @param string $name
     * @param string $placeholder
     * @param string $action
     * @param string|null $quickCreateButtonId ID of the quick create button to click instead of redirecting
     * @param int|null $selectedId
     * @param string|null $selectedName
     */
    public function __construct(string $id, string $name, string $placeholder = 'Search...', string $action = 'form', ?string $quickCreateButtonId = null, ?int $selectedId = null, ?string $selectedName = null)
    {
        $this->id = $id;
        $this->name = $name;
        $this->placeholder = $placeholder;
        $this->action = $action;
        $this->quickCreateButtonId = $quickCreateButtonId;
        $this->selectedId = $selectedId;
        $this->selectedName = $selectedName;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.dynamic-customer-search');
    }
}
