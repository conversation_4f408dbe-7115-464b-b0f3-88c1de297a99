<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class QuickCreateCustomer extends Component
{
    /**
     * The name input field attribute, where the customer name will be populated.
     */
    public string $name;

    /**
     * The input ID to populate with the customer ID.
     */
    public string $id;

    /**
     * Create a new component instance.
     */
    public function __construct(string $name, string $id)
    {
        $this->name = $name;
        $this->id = $id;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.quick-create-customer');
    }
}
