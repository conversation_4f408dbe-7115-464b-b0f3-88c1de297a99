<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class PickupQuickCreateCustomer extends Component
{
    /**
     * The input ID to populate with the customer ID.
     */
    public string $id;

    /**
     * The name input field attribute.
     */
    public string $name;

    /**
     * The pickup request instance for pre-populating data.
     */
    public $pickupRequest;

    /**
     * Create a new component instance.
     */
    public function __construct(
        string $id = 'pickupCustomerSearch',
        string $name = 'customer_id',
        $pickupRequest = null
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->pickupRequest = $pickupRequest;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.pickup-quick-create-customer');
    }
}
