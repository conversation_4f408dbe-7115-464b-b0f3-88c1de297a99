<?php

namespace App\Console\Commands;

use App\Models\PickupRequest;
use App\Services\EmailTemplateService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class SendPickupConfirmationEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pickups:send-confirmation-email {pickup_request_id : The ID of the pickup request}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manually send pickup confirmation email for a specific pickup request ID (ignores status)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $pickupRequestId = $this->argument('pickup_request_id');
        
        // Find the pickup request
        $pickupRequest = PickupRequest::find($pickupRequestId);
        
        if (!$pickupRequest) {
            $this->error("Pickup request with ID {$pickupRequestId} not found.");
            return Command::FAILURE;
        }
        
        $this->info("Found pickup request #{$pickupRequest->id} for {$pickupRequest->contact_name} ({$pickupRequest->email})");
        
        // Confirm with user before sending
        if (!$this->confirm('Do you want to send the pickup confirmation email to this customer?')) {
            $this->info('Email sending cancelled.');
            return Command::SUCCESS;
        }
        
        try {
            // Generate a unique confirmation token if not already set
            if (!$pickupRequest->confirmation_token) {
                $pickupRequest->confirmation_token = Str::random(64);
            }
            
            // Generate management token if not already set
            if (!$pickupRequest->management_token) {
                $pickupRequest->generateManagementToken();
            }
            
            // Update reminder sent timestamp
            $pickupRequest->reminder_sent_at = now();
            $pickupRequest->save();
            
            // Log the reminder sent event
            $pickupRequest->logReminderSent();
            
            // Send the reminder email using the existing template system
            $emailTemplateService = new EmailTemplateService();
            
            // Prepare data for the email template
            $templateData = [
                'pickup_request' => $pickupRequest,
            ];

            // Render the email template
            $renderedEmail = $emailTemplateService->renderTemplate('pickup_confirmation_reminder', $templateData);
            
            if (!$renderedEmail) {
                $this->error("Failed to render pickup confirmation reminder email template for pickup #{$pickupRequest->id}");
                return Command::FAILURE;
            }

            // Send the email using the rendered template
            Mail::html($renderedEmail['body'], function ($message) use ($pickupRequest, $renderedEmail) {
                $message->to($pickupRequest->email, $pickupRequest->contact_name)
                        ->subject($renderedEmail['subject']);
                        
                // Use template's from settings if available, otherwise use config defaults
                if ($renderedEmail['from_email'] && $renderedEmail['from_name']) {
                    $message->from($renderedEmail['from_email'], $renderedEmail['from_name']);
                } else {
                    $message->from(config('mail.from.address'), config('app.name'));
                }
            });
            
            $this->info("✓ Successfully sent pickup confirmation email for pickup #{$pickupRequest->id} to {$pickupRequest->email}");
            
        } catch (\Exception $e) {
            $this->error("Failed to send pickup confirmation email for pickup #{$pickupRequest->id}: {$e->getMessage()}");
            return Command::FAILURE;
        }
        
        return Command::SUCCESS;
    }
}