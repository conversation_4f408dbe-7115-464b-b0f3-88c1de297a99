<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UserGroup;

class AddUserToGroup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:add-user-to-group 
                            {email : The email of the user} 
                            {group : The name of the group}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Adds a user to a group. The user and group are specified by their email and group name.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $groupName = $this->argument('group');

        // Find the user by email
        $user = User::where('email', $email)->first();
        if (!$user) {
            $this->error("User with email '{$email}' not found.");
            return 1;
        }

        // Find the group by name
        $group = UserGroup::where('name', $groupName)->first();
        if (!$group) {
            $this->error("Group with name '{$groupName}' not found.");
            return 1;
        }

        // Check if the user is already in the group
        if ($user->groups()->where('user_group_id', $group->id)->exists()) {
            $this->info("User '{$email}' is already a member of the '{$groupName}' group.");
            return 0;
        }

        // Add the user to the group
        $user->groups()->attach($group->id);

        $this->info("User '{$email}' has been added to the '{$groupName}' group.");
        return 0;
    }
}
