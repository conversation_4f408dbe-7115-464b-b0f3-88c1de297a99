<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\InventoryImage;
use App\Models\Image;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class TransferImagesFromInventoryImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:transferfrominventoryimages';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Transfer InventoryImage data to the Image table, convert to WebP, generate thumbnails, and update image_id field.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $inventoryImages = InventoryImage::all();

        foreach ($inventoryImages as $inventoryImage) {
            $this->info("Processing InventoryImage ID: {$inventoryImage->id}");

            // Define the new image path with .webp extension
            $targetPath = str_replace('inventory_photos/', 'img/', $inventoryImage->image_path);
            $targetPath = preg_replace('/\.\w+$/', '.webp', $targetPath);
            $newImagePath = public_path("storage/{$targetPath}");

            // Process the source image
            $sourcePath = public_path("storage/{$inventoryImage->image_path}");

            if (file_exists($sourcePath)) {
                if (!file_exists(dirname($newImagePath))) {
                    mkdir(dirname($newImagePath), 0755, true);
                }

                // Convert the image to WebP and save
                if ($this->convertToWebp($sourcePath, $newImagePath)) {
                    $this->info("Image converted to WebP successfully: {$newImagePath}");

                    // Check if an image already exists in the `images` table for this path
                    $existingImage = Image::where('image_path', $targetPath)->first();

                    if (!$existingImage) {
                        // Create a new entry in the `images` table
                        $newImage = Image::create([
                            'image_path' => $targetPath,
                            'og_filename' => $inventoryImage->og_filename,
                            'uploaded_by' => null, // Set uploaded_by if applicable
                            'title' => null,
                            'description' => null,
                            'alt_text' => null,
                        ]);
                        $this->info("New Image created with ID: {$newImage->id}");
                    } else {
                        $this->info("Image already exists in the database with ID: {$existingImage->id}");
                        $newImage = $existingImage;
                    }

                    // Update InventoryImage to reference the new Image ID
                    $inventoryImage->update(['image_id' => $newImage->id]);

                    // Generate thumbnails for the new WebP image
                    Image::generateThumbnails($targetPath);

                    $this->info("Thumbnails generated successfully for: {$targetPath}");
                } else {
                    Log::error("Failed to convert image to WebP: {$sourcePath}");
                    $this->error("Failed to convert image to WebP: {$sourcePath}");
                }
            } else {
                Log::error("Source image not found: {$sourcePath}");
                $this->error("Source image not found: {$sourcePath}");
            }
        }

        $this->info("Data transfer, image conversion, and updates completed.");
    }

    /**
     * Convert an image to WebP format.
     */
    private function convertToWebp($sourcePath, $targetPath)
    {
        $source = match (mime_content_type($sourcePath)) {
            'image/jpeg' => imagecreatefromjpeg($sourcePath),
            'image/png' => imagecreatefrompng($sourcePath),
            'image/webp' => imagecreatefromwebp($sourcePath),
            default => null,
        };

        if (!$source) {
            Log::error("Unsupported image type for WebP conversion: {$sourcePath}");
            return false;
        }

        // Ensure the directory exists
        if (!file_exists(dirname($targetPath))) {
            mkdir(dirname($targetPath), 0755, true);
        }

        // Save as WebP with quality 85
        $success = imagewebp($source, $targetPath, 85);
        imagedestroy($source);

        return $success;
    }
}
