<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Inventory;
use App\Models\InventoryCategory;

class FixAssetTags extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:fix-asset-tags';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix asset tags for all inventory items to ensure they match the ID';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to fix asset tags...');

        // Fetch all inventory items
        $inventories = Inventory::withTrashed()->get();

        foreach ($inventories as $inventory) {
            // Get the category code
            $categoryCode = InventoryCategory::find($inventory->inv_category_id)?->code;

            if (!$categoryCode) {
                $this->error("Category code not found for inventory ID {$inventory->id}. Skipping...");
                continue;
            }

            // Generate the correct asset tag
            $correctAssetTag = $categoryCode . str_pad($inventory->id, 4, '0', STR_PAD_LEFT);

            if ($inventory->asset_tag !== $correctAssetTag) {
                // Update the asset tag if it doesn't match
                $inventory->update(['asset_tag' => $correctAssetTag]);
                $this->info("Fixed asset tag for inventory ID {$inventory->id}: {$correctAssetTag}");
            } else {
                $this->info("Asset tag for inventory ID {$inventory->id} is already correct.");
            }
        }

        $this->info('Asset tags fixed successfully!');
    }
}
