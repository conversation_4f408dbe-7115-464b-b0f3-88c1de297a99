<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\NotificationService;

class CleanupExpiredNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired notifications from the database';

    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Cleaning up expired notifications...');

        $deletedCount = $this->notificationService->cleanupExpiredNotifications();

        if ($deletedCount > 0) {
            $this->info("Successfully deleted {$deletedCount} expired notification(s).");
        } else {
            $this->info('No expired notifications found.');
        }

        return Command::SUCCESS;
    }
}
