<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Invoice;

class RecalculateTotals extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recalculate:totals';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate totals for all line items and invoices in the system';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting total recalculation...');

        // Process invoices in chunks
        Invoice::with('lineItems')->chunk(10, function ($invoices) {
            foreach ($invoices as $invoice) {


                $this->info("Processing Invoice ID: {$invoice->id}");

                // Call the existing calculateTotals method
                $invoice->calculateTotals();

                // Save the updated invoice
                $invoice->save();

                $this->info("Recalculated totals for Invoice ID: {$invoice->id}");
            }
        });

        $this->info('Recalculation completed successfully.');
        return 0;
    }
}
