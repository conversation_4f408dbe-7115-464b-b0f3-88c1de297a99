<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Image;
use Illuminate\Support\Facades\Log;

class GenerateThumbnails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-thumbnails {--force : Regenerate thumbnails even if they already exist}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate or regenerate thumbnails for all images in the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting thumbnail generation...');
        
        $force = $this->option('force');
        $images = Image::all();

        if ($images->isEmpty()) {
            $this->warn('No images found in the database.');
            return 0;
        }

        foreach ($images as $image) {
            try {
                $this->info("Processing image ID: {$image->id} ({$image->image_path})");

                if (!$force) {
                    $thumbnailsExist = file_exists(public_path("storage/img/thumbs/" . str_replace('img/', '', $image->image_path)));
                    if ($thumbnailsExist) {
                        $this->info("Thumbnails already exist for image ID: {$image->id}. Skipping...");
                        continue;
                    }
                }

                $thumbnails = $image->generateThumbnails($image->image_path);
                $this->info("Thumbnails generated: " . implode(', ', $thumbnails));
            } catch (\Exception $e) {
                $this->error("Error processing image ID: {$image->id}. Error: " . $e->getMessage());
                Log::error("Error generating thumbnails for image ID {$image->id}: {$e->getMessage()}");
            }
        }

        $this->info('Thumbnail generation completed.');
        return 0;
    }
}
