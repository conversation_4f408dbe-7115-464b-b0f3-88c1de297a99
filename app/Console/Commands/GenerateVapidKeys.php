<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Minishlink\WebPush\VAPID;

class GenerateVapidKeys extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webpush:generate-keys';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate VAPID keys for Web Push notifications';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Generating VAPID keys for Web Push notifications...');
            
            $keys = VAPID::createVapidKeys();
            
            $this->newLine();
            $this->info('✅ VAPID keys generated successfully!');
            $this->newLine();
            
            $this->line('<fg=yellow>Add these keys to your .env file:</>');
            $this->newLine();
            
            $this->line("WEBPUSH_PUBLIC_KEY=\"{$keys['publicKey']}\"");
            $this->line("WEBPUSH_PRIVATE_KEY=\"{$keys['privateKey']}\"");
            
            $this->newLine();
            $this->warn('⚠️  Keep the private key secure and never share it publicly!');
            $this->newLine();
            
            $this->info('After adding these keys to your .env file, run:');
            $this->line('php artisan config:clear');
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to generate VAPID keys: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
