<?php

namespace App\Console\Commands;

use App\Models\PickupRequest;
use App\Services\EmailTemplateService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Carbon\Carbon;

class SendPickupReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pickups:send-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send reminder emails to customers 48 hours before their pickup appointment';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking for pickups that need reminders...');
        
        // Get the date range for pickups happening within the next 48 hours
        $reminderStart = Carbon::now(); // Right now
        $reminderEnd = Carbon::now()->addHours(48);   // 48 hours from now
        
        // FIRST: Send confirmation reminders to unconfirmed pickups
        // Find pickup requests that:
        // 1. Are pending status
        // 2. Have not been sent a reminder
        // 3. Have not been confirmed
        // 4. Have not been cancelled
        // 5. Preferred pickup date is within the next 48 hours
        $pickupsNeedingConfirmation = PickupRequest::where('status', 'pending')
            ->whereNull('reminder_sent_at')
            ->whereNull('confirmed_at')
            ->whereNull('cancelled_at')
            ->whereBetween('preferred_pickup_date', [$reminderStart, $reminderEnd])
            ->get();
            
        $this->info("Found {$pickupsNeedingConfirmation->count()} pickups needing confirmation reminders.");
        
        foreach ($pickupsNeedingConfirmation as $pickupRequest) {
            try {
                // Generate a unique confirmation token if not already set
                if (!$pickupRequest->confirmation_token) {
                    $pickupRequest->confirmation_token = Str::random(64);
                }
                
                // Generate management token if not already set
                if (!$pickupRequest->management_token) {
                    $pickupRequest->generateManagementToken();
                }
                
                $pickupRequest->reminder_sent_at = now();
                $pickupRequest->save();
                
                // Log the reminder sent event
                $pickupRequest->logReminderSent();
                
                // Send the reminder email using the existing template system
                $emailTemplateService = new EmailTemplateService();
                
                // Prepare data for the email template
                $templateData = [
                    'pickup_request' => $pickupRequest,
                ];

                // Render the email template
                $renderedEmail = $emailTemplateService->renderTemplate('pickup_confirmation_reminder', $templateData);
                
                if (!$renderedEmail) {
                    $this->error("Failed to render pickup confirmation reminder email template for pickup #{$pickupRequest->id}");
                    continue;
                }

                // Send the email using the rendered template
                Mail::html($renderedEmail['body'], function ($message) use ($pickupRequest, $renderedEmail) {
                    $message->to($pickupRequest->email, $pickupRequest->contact_name)
                            ->subject($renderedEmail['subject']);
                            
                    // Use template's from settings if available, otherwise use config defaults
                    if ($renderedEmail['from_email'] && $renderedEmail['from_name']) {
                        $message->from($renderedEmail['from_email'], $renderedEmail['from_name']);
                    } else {
                        $message->from(config('mail.from.address'), config('app.name'));
                    }
                });
                
                $this->info("Sent confirmation reminder for pickup #{$pickupRequest->id} to {$pickupRequest->email}");
            } catch (\Exception $e) {
                $this->error("Failed to send confirmation reminder for pickup #{$pickupRequest->id}: {$e->getMessage()}");
            }
        }
        
        // SECOND: Send final reminders to already confirmed pickups
        // Find pickup requests that:
        // 1. Are confirmed status (customer has already confirmed)
        // 2. Have been confirmed (confirmed_at is not null)
        // 3. Have not been sent a final reminder
        // 4. Have not been cancelled
        // 5. Preferred pickup date is within the next 48 hours
        $pickupsNeedingFinalReminder = PickupRequest::where('status', 'confirmed')
            ->whereNotNull('confirmed_at')
            ->whereNull('final_reminder_sent_at')
            ->whereNull('cancelled_at')
            ->whereBetween('preferred_pickup_date', [$reminderStart, $reminderEnd])
            ->get();
            
        $this->info("Found {$pickupsNeedingFinalReminder->count()} confirmed pickups needing final reminders.");
        
        foreach ($pickupsNeedingFinalReminder as $pickupRequest) {
            try {
                $pickupRequest->final_reminder_sent_at = now();
                $pickupRequest->save();
                
                // Log the final reminder sent event
                $pickupRequest->logFinalReminderSent();
                
                // Send the final reminder email
                $emailTemplateService = new EmailTemplateService();
                
                // Prepare data for the email template
                $templateData = [
                    'pickup_request' => $pickupRequest,
                ];

                // Render the final reminder email template
                $renderedEmail = $emailTemplateService->renderTemplate('pickup_final_reminder', $templateData);
                
                if (!$renderedEmail) {
                    $this->error("Failed to render pickup final reminder email template for pickup #{$pickupRequest->id}");
                    continue;
                }

                // Send the email using the rendered template
                Mail::html($renderedEmail['body'], function ($message) use ($pickupRequest, $renderedEmail) {
                    $message->to($pickupRequest->email, $pickupRequest->contact_name)
                            ->subject($renderedEmail['subject']);
                            
                    // Use template's from settings if available, otherwise use config defaults
                    if ($renderedEmail['from_email'] && $renderedEmail['from_name']) {
                        $message->from($renderedEmail['from_email'], $renderedEmail['from_name']);
                    } else {
                        $message->from(config('mail.from.address'), config('app.name'));
                    }
                });
                
                $this->info("Sent final reminder for confirmed pickup #{$pickupRequest->id} to {$pickupRequest->email}");
            } catch (\Exception $e) {
                $this->error("Failed to send final reminder for pickup #{$pickupRequest->id}: {$e->getMessage()}");
            }
        }
        
        $this->info('All reminder sending complete.');
        
        return Command::SUCCESS;
    }
}
