<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\LineItem;
use App\Models\Inventory;

class FixLineItemDepartments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lineitems:fix-departments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix line items with null department_id by assigning the default department_id of their inventory category';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("Starting to fix line items with null department_id...");

        // Fetch all line items with a null department_id
        $lineItems = LineItem::whereNull('department_id')->get();

        if ($lineItems->isEmpty()) {
            $this->info("No line items found with null department_id.");
            return 0;
        }

        $fixedCount = 0;

        foreach ($lineItems as $lineItem) {
            // Get the inventory item associated with the line item
            $inventory = Inventory::with('category')->find($lineItem->item_id);

            if ($inventory && $inventory->category && $inventory->category->department_id) {
                // Update the department_id of the line item
                $lineItem->update(['department_id' => $inventory->category->department_id]);
                $fixedCount++;
            }
        }

        $this->info("Successfully updated {$fixedCount} line items with the default department_id of their categories.");
        return 0;
    }
}
