<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Inventory;
use App\Models\InventoryCategory;

class RegenerateAssetTags extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:regenerate-asset-tags';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerate asset tags for all inventory items based on their ID and category code';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Regenerating asset tags for all inventory items...');
        
        $items = Inventory::all();

        foreach ($items as $item) {
            $category = InventoryCategory::find($item->inv_category_id);

            if (!$category) {
                $this->error("Category not found for item ID {$item->id}. Skipping...");
                continue;
            }

            // Generate the asset tag
            $assetTag = $category->code . str_pad($item->id, 4, '0', STR_PAD_LEFT);

            // Update the asset tag
            $item->update(['asset_tag' => $assetTag]);

            $this->info("Asset tag for item ID {$item->id} updated to {$assetTag}");
        }

        $this->info('Asset tag regeneration complete!');
        return 0;
    }
}
