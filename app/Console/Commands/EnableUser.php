<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class EnableUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:enable-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sets enabled flag for a user given their email address';

    /**
     * Execute the console command.
     */
    public function handle(): void  
    {
        $email = $this->ask('Enter the email address of the user to enable');
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error('User not found');
            return;
        }

        $user->enabled = true;
        $user->save();

        $this->info('User enabled');
    }
   
}
