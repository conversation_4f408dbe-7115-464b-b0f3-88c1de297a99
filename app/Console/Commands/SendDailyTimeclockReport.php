<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GlobalConfig;
use App\Models\TimeCard;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Mail\DailyTimeclockReport;
use App\Mail\TemplatedMail;
use App\Services\EmailTemplateService;
use Illuminate\Support\Facades\Log;

class SendDailyTimeclockReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'timeclock:send-daily-report
                            {--today : Use today\'s date instead of yesterday for testing}
                            {--email= : Override the recipient email address for testing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate and send the daily timeclock report to the configured email address';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Log::info('Daily timeclock report command started.');
        // Check if the feature is enabled
        if (!GlobalConfig::isFeatureEnabled('timeclock_daily_report')) {
            $this->info('Daily timeclock report is disabled in global settings.');
            Log::info('Daily timeclock report command ran but feature is disabled.');
            return 0;
        }

        // Get the email recipient (or use the one specified with --email option)
        $recipientEmail = $this->option('email') ?: GlobalConfig::getValue('timeclock_report_email');
        if (!$recipientEmail) {
            $this->error('No recipient email configured for timeclock reports.');
            Log::error('Daily timeclock report command ran but no recipient email is configured.');
            return 1;
        }

        if ($this->option('email')) {
            $this->info('Using override email address: ' . $recipientEmail);
        }

        // Get yesterday's date (or use today if specified with --today option)
        $reportDate = $this->option('today') ? Carbon::today() : Carbon::yesterday();
        $this->info('Generating timeclock report for ' . $reportDate->format('Y-m-d') . ($this->option('today') ? ' (TODAY)' : ''));

        // Get all time cards for the report date
        $timeCards = TimeCard::with(['user', 'allValidPunches' => function ($query) {
            $query->orderBy('punch_time', 'asc');
        }])
        ->whereDate('date', $reportDate)
        ->get();

        if ($timeCards->isEmpty()) {
            $this->info('No time cards found for ' . $reportDate->format('Y-m-d'));
            Log::info('No time cards found for daily report on ' . $reportDate->format('Y-m-d'));
            return 0;
        }

        // Process time cards to create report data
        $reportData = [];
        $totalHoursWorked = 0;
        $totalBreakHours = 0;

        foreach ($timeCards as $timeCard) {
            // Make sure we have the latest calculated hours
            // Force a recalculation to ensure accuracy
            $timeCard->calculateHours(true);
            $timeCard->refresh();

            // Convert HH:MM:SS to decimal hours for calculations
            $hoursWorked = $this->timeStringToDecimal($timeCard->total_hours);
            $breakHours = $this->timeStringToDecimal($timeCard->total_break_hours);

            // Log the values for debugging
            Log::info("TimeCard ID {$timeCard->id} for user {$timeCard->user->name}: Hours worked: {$timeCard->total_hours} ({$hoursWorked}), Break hours: {$timeCard->total_break_hours} ({$breakHours})");

            // Only add to totals if there are actual hours worked
            if ($hoursWorked > 0) {
                $totalHoursWorked += $hoursWorked;
            }

            if ($breakHours > 0) {
                $totalBreakHours += $breakHours;
            }

            // Get first clock in and last clock out
            $firstClockIn = null;
            $lastClockOut = null;

            // Make sure we have all valid punches loaded
            $validPunches = $timeCard->allValidPunches()->orderBy('punch_time', 'asc')->get();

            // Log the number of punches found
            Log::info("Found " . $validPunches->count() . " valid punches for TimeCard ID {$timeCard->id}");

            foreach ($validPunches as $punch) {
                if ($punch->type === 'clock_in' && (!$firstClockIn || $punch->punch_time->lt($firstClockIn))) {
                    $firstClockIn = $punch->punch_time;
                }
                if ($punch->type === 'clock_out' && (!$lastClockOut || $punch->punch_time->gt($lastClockOut))) {
                    $lastClockOut = $punch->punch_time;
                }
            }

            $reportData[] = [
                'user' => $timeCard->user,
                'time_card' => $timeCard,
                'hours_worked' => $hoursWorked,
                'break_hours' => $breakHours,
                'first_clock_in' => $firstClockIn,
                'last_clock_out' => $lastClockOut,
                'punches' => $validPunches
            ];
        }

        // Sort by user name
        usort($reportData, function ($a, $b) {
            return $a['user']->name <=> $b['user']->name;
        });

        // Send the email
        try {
            $emailTemplateService = app(EmailTemplateService::class);
            
            // Check if we have a custom template
            if ($emailTemplateService->templateExists('daily_timeclock_report')) {
                // Use the custom template
                $templateData = [
                    'report_date' => $reportDate,
                    'report_data' => $reportData,
                    'total_hours_worked' => $totalHoursWorked,
                    'total_break_hours' => $totalBreakHours,
                ];
                
                Mail::to($recipientEmail)->send(new TemplatedMail('daily_timeclock_report', $templateData));
                $this->info('Daily timeclock report sent using custom template to ' . $recipientEmail);
            } else {
                // Fall back to the original mail class
                Mail::to($recipientEmail)->send(new DailyTimeclockReport(
                    $reportDate,
                    $reportData,
                    $totalHoursWorked,
                    $totalBreakHours
                ));
                $this->info('Daily timeclock report sent using default template to ' . $recipientEmail);
            }

            Log::info('Daily timeclock report sent successfully to ' . $recipientEmail);
            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to send daily timeclock report: ' . $e->getMessage());
            Log::error('Failed to send daily timeclock report: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Convert time string (HH:MM:SS) to decimal hours
     */
    private function timeStringToDecimal($timeString)
    {
        if (!$timeString) {
            return 0;
        }

        $parts = explode(':', $timeString);
        if (count($parts) !== 3) {
            return 0;
        }

        $hours = (int)$parts[0];
        $minutes = (int)$parts[1];
        $seconds = (int)$parts[2];

        return $hours + ($minutes / 60) + ($seconds / 3600);
    }
}
