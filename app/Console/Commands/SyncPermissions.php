<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Permission;
use App\Models\UserGroup;

class SyncPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync permissions from the configuration file into the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $permissions = config('permissions');

        // Ensure the Admin group exists
        $adminGroup = UserGroup::firstOrCreate(['name' => 'Admin'], ['description' => 'Administrative Group']);
        $this->info('Ensured Admin group exists.');

        foreach ($permissions as $scope => $actions) {
            foreach ($actions as $action => $description) {
                $permission = Permission::updateOrCreate(
                    ['name' => $action],
                    ['description' => $description, 'scope' => $scope]
                );
                $this->info("Synced permission: {$action}");

                // // Attach the permission to the Admin group if not already attached
                // if (!$adminGroup->permissions->contains($permission->id)) {
                //     $adminGroup->permissions()->attach($permission->id);
                //     $this->info("Granted Admin group access to permission: {$action}");
                // }
            }
        }

        $this->info('Permissions sync and Admin group check completed.');
    }
}
