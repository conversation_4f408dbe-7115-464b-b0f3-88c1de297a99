<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class SendTestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-test-email {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test email to the specified email address';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('Invalid email address provided.');
            return 1;
        }

        try {
            Mail::raw('This is a test email from ETRFlow.', function ($message) use ($email) {
                $message->to($email)
                        ->subject('Test Email from ETRFlow');
            });
            $this->info("Test email sent successfully to {$email}.");
            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to send test email: {$e->getMessage()}");
            return 1;
        }
    }
}
