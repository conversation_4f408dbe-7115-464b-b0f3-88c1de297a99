<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use OpenAI;

class TestOpenAIConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:openai';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the OpenAI API connection';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing OpenAI connection...');

        try {
            // Initialize the OpenAI client
            $openai = OpenAI::client(config('services.openai.api_key'));

            // Make a test request to the OpenAI chat completion endpoint
            $response = $openai->chat()->create([
                'model' => 'gpt-4',
                'messages' => [
                    ['role' => 'user', 'content' => 'Hello!'],
                ],
            ]);

            // Display the result
            $message = $response->choices[0]->message->content ?? 'No response received.';
            $this->info("OpenAI response: $message");
        } catch (\Exception $e) {
            $this->error('Failed to connect to OpenAI: ' . $e->getMessage());
        }
    }
}
