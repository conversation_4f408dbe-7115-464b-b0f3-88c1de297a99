<?php

namespace App\Console\Commands;

use App\Models\GlobalConfig;
use App\Models\TimeCard;
use App\Models\TimePunch;
use App\Models\User;
use App\Helpers\NotificationHelper;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessAutoClockOut extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'timeclock:auto-clock-out {--date= : Process for a specific date (YYYY-MM-DD)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically clock out users who forgot to clock out';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!GlobalConfig::isAutoClockOutEnabled()) {
            $this->info('Auto clock-out is disabled. Enable it in global settings.');
            return Command::SUCCESS;
        }

        $autoClockOutTime = GlobalConfig::getAutoClockOutTime();
        $this->info("Processing auto clock-out at {$autoClockOutTime}...");

        // Allow processing for a specific date (useful for testing or fixing past issues)
        $processDate = $this->option('date') ? Carbon::parse($this->option('date')) : Carbon::today();
        
        // Get all users who might need auto clock-out
        $users = User::all();
        $processedCount = 0;

        foreach ($users as $user) {
            // Get time card for the process date
            $timeCard = TimeCard::where('user_id', $user->id)
                ->where('date', $processDate->format('Y-m-d'))
                ->first();

            if (!$timeCard) {
                continue;
            }

            // Check if user is still clocked in or on break
            $currentStatus = $timeCard->getCurrentStatus();
            
            if ($currentStatus === 'clock_in' || $currentStatus === 'break') {
                // Get the last clock-in or break-out punch
                $lastActivePunch = $timeCard->allValidPunches()
                    ->whereIn('type', ['clock_in', 'break_out', 'break_in'])
                    ->orderBy('punch_time', 'desc')
                    ->first();

                if (!$lastActivePunch) {
                    continue;
                }

                // Check if we're past the auto clock-out time
                $autoClockOutDateTime = Carbon::parse($processDate->format('Y-m-d') . ' ' . $autoClockOutTime);
                $now = Carbon::now();

                // Only process if we're past the auto clock-out time
                if ($now->greaterThan($autoClockOutDateTime)) {
                    try {
                        // Create auto clock-out punch manually since createPunch() expects Auth::id()
                        $punch = new TimePunch();
                        $punch->time_card_id = $timeCard->id;
                        $punch->user_id = $user->id;
                        $punch->type = 'clock_out';
                        $punch->punch_time = $autoClockOutDateTime;
                        $punch->notes = 'Auto clock-out at ' . $autoClockOutTime;
                        $punch->ip_address = '127.0.0.1'; // Console/system IP
                        $punch->save();
                        
                        // Recalculate hours after auto clock-out
                        $timeCard->calculateHours();

                        $processedCount++;
                        
                        $this->info("Auto clocked-out user: {$user->name} (ID: {$user->id}) at {$autoClockOutTime}");
                        
                        Log::info("Auto clock-out processed", [
                            'user_id' => $user->id,
                            'user_name' => $user->name,
                            'time_card_id' => $timeCard->id,
                            'punch_id' => $punch->id,
                            'clock_out_time' => $autoClockOutDateTime->toDateTimeString(),
                            'previous_status' => $currentStatus
                        ]);

                        // Send notification to admins
                        NotificationHelper::notifyAdmins(
                            'Auto Clock-Out Processed',
                            "User {$user->name} was automatically clocked out at {$autoClockOutTime} on {$processDate->format('M j, Y')}. Please review their timecard to ensure accuracy.",
                            'normal',
                            route('timecards.edit', $timeCard->id),
                            'Edit Timecard'
                        );

                    } catch (\Exception $e) {
                        $this->error("Failed to auto clock-out user {$user->name}: " . $e->getMessage());
                        
                        Log::error("Auto clock-out failed", [
                            'user_id' => $user->id,
                            'user_name' => $user->name,
                            'time_card_id' => $timeCard->id,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                }
            }
        }

        $this->info("Auto clock-out complete. Processed {$processedCount} users.");
        
        return Command::SUCCESS;
    }
}