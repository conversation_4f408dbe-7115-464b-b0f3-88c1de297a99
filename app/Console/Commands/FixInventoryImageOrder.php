<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\InventoryImage;

class FixInventoryImageOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:inventory-image-order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix duplicate order numbers for inventory images';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to fix inventory image orders...');
        
        // Fetch all inventory IDs
        $inventoryIds = InventoryImage::distinct()->pluck('inventory_id');

        foreach ($inventoryIds as $inventoryId) {
            $this->info("Processing inventory ID: $inventoryId");

            // Fetch images for the current inventory, ordered by current `order`
            $images = InventoryImage::where('inventory_id', $inventoryId)
                ->orderBy('order')
                ->get();

            $order = 1; // Start the order from 1
            foreach ($images as $image) {
                $image->update(['order' => $order]);
                $this->info("Updated image ID {$image->id} to order $order");
                $order++;
            }
        }

        $this->info('All inventory image orders have been fixed successfully!');
    }
}
