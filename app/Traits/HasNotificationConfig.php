<?php

namespace App\Traits;

use App\Models\NotificationConfig;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait HasNotificationConfig
{
    /**
     * Get all notification configurations for this model
     */
    public function notificationConfigs(): MorphMany
    {
        return $this->morphMany(NotificationConfig::class, 'configurable');
    }

    /**
     * Get notification configurations for a specific event type
     */
    public function getNotificationConfigsForEvent(string $eventType)
    {
        return $this->notificationConfigs()
            ->enabled()
            ->forEvent($eventType)
            ->get();
    }

    /**
     * Create or update a notification configuration
     */
    public function setNotificationConfig(
        string $eventType,
        string $targetType,
        array $targetIds = null,
        array $options = []
    ): NotificationConfig {
        $config = $this->notificationConfigs()
            ->where('event_type', $eventType)
            ->first();

        $data = array_merge([
            'event_type' => $eventType,
            'target_type' => $targetType,
            'target_ids' => $targetIds,
            'urgency' => NotificationConfig::URGENCY_NORMAL,
            'is_enabled' => true,
            'include_link' => true,
            'is_dismissible' => true,
            'auto_dismiss' => false,
        ], $options);

        if ($config) {
            $config->update($data);
            return $config;
        }

        return $this->notificationConfigs()->create($data);
    }

    /**
     * Remove notification configuration for an event type
     */
    public function removeNotificationConfig(string $eventType): bool
    {
        return $this->notificationConfigs()
            ->where('event_type', $eventType)
            ->delete() > 0;
    }

    /**
     * Check if notifications are enabled for an event type
     */
    public function hasNotificationsEnabledFor(string $eventType): bool
    {
        return $this->notificationConfigs()
            ->enabled()
            ->forEvent($eventType)
            ->exists();
    }

    /**
     * Get all enabled notification configurations
     */
    public function getEnabledNotificationConfigs()
    {
        return $this->notificationConfigs()
            ->enabled()
            ->get();
    }

    /**
     * Disable all notification configurations
     */
    public function disableAllNotifications(): int
    {
        return $this->notificationConfigs()
            ->update(['is_enabled' => false]);
    }

    /**
     * Enable all notification configurations
     */
    public function enableAllNotifications(): int
    {
        return $this->notificationConfigs()
            ->update(['is_enabled' => true]);
    }

    /**
     * Get notification configuration summary for display
     */
    public function getNotificationConfigSummary(): array
    {
        $configs = $this->getEnabledNotificationConfigs();
        $summary = [];

        foreach ($configs as $config) {
            $targets = [];
            
            if ($config->targetsAllUsers()) {
                $targets[] = 'All Users';
            } elseif ($config->target_type === NotificationConfig::TARGET_USER_GROUP) {
                $groups = $config->getTargetUserGroups();
                $targets = $groups->pluck('name')->toArray();
            } elseif ($config->target_type === NotificationConfig::TARGET_SPECIFIC_USERS) {
                $users = $config->getTargetUsers();
                $targets = $users->pluck('name')->toArray();
            }

            $summary[$config->event_type] = [
                'enabled' => $config->is_enabled,
                'targets' => $targets,
                'urgency' => $config->urgency,
                'target_type' => $config->target_type,
            ];
        }

        return $summary;
    }
}
