<?php

namespace App\Traits;

use App\Models\ActivityLog;
use App\Services\ActivityLoggerService;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait LogsActivity
{
    /**
     * Boot the trait
     */
    protected static function bootLogsActivity()
    {
        // Log when model is created
        static::created(function ($model) {
            if ($model->shouldLogActivity('created')) {
                app(ActivityLoggerService::class)->logCreated($model);
            }
        });

        // Log when model is updated
        static::updated(function ($model) {
            if ($model->shouldLogActivity('updated')) {
                $oldValues = [];
                $newValues = [];

                // Get only the fields that actually changed
                foreach ($model->getDirty() as $field => $newValue) {
                    if ($model->shouldLogField($field)) {
                        $oldValues[$field] = $model->getOriginal($field);
                        $newValues[$field] = $newValue;
                    }
                }

                // Only log if there are actual changes to log
                if (!empty($oldValues)) {
                    // Check if this is an auto-save operation
                    $isAutoSave = $model->isAutoSaveOperation();

                    if ($isAutoSave) {
                        // Use smart logging for auto-save operations
                        app(ActivityLoggerService::class)->logUpdatedSmart($model, $oldValues, null, [], $newValues, true);
                    } else {
                        // Use regular logging for manual saves
                        app(ActivityLoggerService::class)->logUpdated($model, $oldValues, null, [], $newValues);
                    }
                }
            }
        });

        // Log when model is deleted
        static::deleted(function ($model) {
            if ($model->shouldLogActivity('deleted')) {
                app(ActivityLoggerService::class)->logDeleted($model);
            }
        });

        // Log when model is restored (for soft deletes)
        if (method_exists(static::class, 'restored')) {
            static::restored(function ($model) {
                if ($model->shouldLogActivity('restored')) {
                    app(ActivityLoggerService::class)->logRestored($model);
                }
            });
        }
    }

    /**
     * Get all activity logs for this model
     */
    public function activityLogs(): MorphMany
    {
        return $this->morphMany(ActivityLog::class, 'loggable')->orderBy('created_at', 'desc');
    }

    /**
     * Get recent activity logs for this model
     */
    public function recentActivityLogs(int $limit = 20)
    {
        return $this->activityLogs()->with('user')->limit($limit)->get();
    }

    /**
     * Determine if activity should be logged for the given event
     * Override this method in your model to customize logging behavior
     */
    protected function shouldLogActivity(string $event): bool
    {
        // Check if the model has a property to define which events to log
        if (property_exists($this, 'loggedEvents')) {
            return in_array($event, $this->loggedEvents);
        }

        // By default, log all events except 'updated' if no changes were made
        return true;
    }

    /**
     * Determine if a specific field should be logged
     * Override this method in your model to exclude sensitive fields
     */
    protected function shouldLogField(string $field): bool
    {
        // Check if the model has a property to define which fields to exclude
        if (property_exists($this, 'excludeFromLogs')) {
            return !in_array($field, $this->excludeFromLogs);
        }

        // Common fields to exclude by default
        $defaultExcluded = [
            'password',
            'remember_token',
            'two_factor_secret',
            'two_factor_recovery_codes',
            'updated_at', // We already track when the log was created
        ];

        return !in_array($field, $defaultExcluded);
    }

    /**
     * Manually log a custom activity
     */
    public function logActivity(string $eventType, string $description, array $additionalData = []): ActivityLog
    {
        return app(ActivityLoggerService::class)->logCustom(
            component: $this->getActivityLogComponent(),
            eventType: $eventType,
            description: $description,
            model: $this,
            additionalData: $additionalData
        );
    }

    /**
     * Get the component name for activity logs
     * Override this method in your model to customize the component name
     */
    protected function getActivityLogComponent(): string
    {
        $className = class_basename($this);
        return strtolower(preg_replace('/([A-Z])/', '_$1', $className));
    }

    /**
     * Get a summary of recent activity for this model
     */
    public function getActivitySummary(int $days = 30): array
    {
        $logs = $this->activityLogs()
            ->where('created_at', '>=', now()->subDays($days))
            ->get();

        return [
            'total_activities' => $logs->count(),
            'created_count' => $logs->where('event_type', 'created')->count(),
            'updated_count' => $logs->where('event_type', 'updated')->count(),
            'deleted_count' => $logs->where('event_type', 'deleted')->count(),
            'viewed_count' => $logs->where('event_type', 'viewed')->count(),
            'last_activity' => $logs->first()?->created_at,
            'most_active_user' => $logs->groupBy('user_id')
                ->map(fn($userLogs) => $userLogs->count())
                ->sortDesc()
                ->keys()
                ->first(),
        ];
    }

    /**
     * Determine if the current operation is an auto-save
     * This checks various indicators to detect auto-save vs manual save
     */
    protected function isAutoSaveOperation(): bool
    {
        // Check if we're in a request context
        if (!app()->bound('request')) {
            return false;
        }

        $request = request();

        // Check for auto-save indicators in the request
        if ($request->has('auto_save') || $request->input('auto_save') === true) {
            return true;
        }

        // Check if the request is an AJAX request to auto-save endpoints
        if ($request->ajax()) {
            $route = $request->route();
            if ($route) {
                $routeName = $route->getName();
                // Check for common auto-save route patterns
                if ($routeName && (
                    str_contains($routeName, 'auto-save') ||
                    str_contains($routeName, 'autoSave') ||
                    str_contains($routeName, 'auto_save')
                )) {
                    return true;
                }
            }
        }

        // Check for auto-save in the request headers or user agent
        if ($request->header('X-Auto-Save') || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            // Additional check: if it's an AJAX request and has specific auto-save patterns
            $uri = $request->getRequestUri();
            if (str_contains($uri, 'auto-save') || str_contains($uri, 'autoSave')) {
                return true;
            }
        }

        return false;
    }
}
