<?php

namespace App\Helpers;

class HeaderButtons
{
    /**
     * Get predefined primary button configurations
     */
    public static function getPrimaryButton(string $type, array $options = []): array
    {
        return match ($type) {
            'back' => [
                'name' => 'Back',
                'route' => $options['route'] ?? url()->previous(),
                'icon' => 'fa-sharp fa-arrow-left',
                'class' => 'btn btn-outline btn-sm gap-2 hover:btn-primary transition-all duration-200',
                'text' => $options['text'] ?? 'Back',
                'target' => $options['target'] ?? null
            ],
            'back-to-index' => [
                'name' => 'Back to Index',
                'route' => $options['route'] ?? '#',
                'icon' => 'fa-sharp fa-arrow-left',
                'class' => 'btn btn-primary btn-sm gap-2 hover:btn-primary transition-all duration-200',
                'text' => $options['text'] ?? 'Back to Index',
                'target' => $options['target'] ?? null
            ],
            'edit' => [
                'name' => 'Edit',
                'route' => $options['route'] ?? '#',
                'icon' => 'fa-sharp fa-edit',
                'class' => 'btn btn-primary btn-sm gap-2',
                'text' => $options['text'] ?? 'Edit',
                'target' => $options['target'] ?? null
            ],
            'create' => [
                'name' => 'Create',
                'route' => $options['route'] ?? '#',
                'icon' => 'fa-sharp fa-plus',
                'class' => 'btn btn-primary btn-sm gap-2',
                'text' => $options['text'] ?? 'Create New',
                'target' => $options['target'] ?? null
            ],
            'delete' => [
                'name' => 'Delete',
                'route' => $options['route'] ?? '#',
                'icon' => 'fa-sharp fa-trash',
                'class' => 'btn btn-error btn-sm gap-2',
                'text' => $options['text'] ?? 'Delete',
                'confirm' => $options['confirm'] ?? 'Are you sure you want to delete this item?',
                'target' => $options['target'] ?? null
            ],
            'save' => [
                'name' => 'Save',
                'route' => $options['route'] ?? '#',
                'icon' => 'fa-sharp fa-save',
                'class' => 'btn btn-success btn-sm gap-2',
                'text' => $options['text'] ?? 'Save',
                'type' => 'submit',
                'target' => $options['target'] ?? null
            ],
            'view' => [
                'name' => 'View',
                'route' => $options['route'] ?? '#',
                'icon' => $options['icon'] ?? 'fa-sharp fa-eye',
                'class' => $options['class'] ?? 'btn btn-info btn-sm gap-2',
                'text' => $options['text'] ?? 'View',
                'target' => $options['target'] ?? null
            ],
            'duplicate' => [
                'name' => 'Duplicate',
                'route' => $options['route'] ?? '#',
                'icon' => $options['icon'] ?? 'fa-sharp fa-copy',
                'class' => $options['class'] ?? 'btn btn-secondary btn-sm gap-2',
                'text' => $options['text'] ?? 'Duplicate',
                'target' => $options['target'] ?? null
            ],
            'pdf' => [
                'name' => 'PDF',
                'route' => $options['route'] ?? '#',
                'icon' => $options['icon'] ?? 'fa-sharp fa-file-pdf',
                'class' => $options['class'] ?? 'btn btn-accent btn-sm gap-2',
                'text' => $options['text'] ?? 'PDF',
                'target' => $options['target'] ?? null
            ],
            default => [
                'name' => ucfirst($type),
                'route' => $options['route'] ?? '#',
                'icon' => $options['icon'] ?? 'fa-sharp fa-circle',
                'class' => $options['class'] ?? 'btn btn-primary btn-sm gap-2',
                'text' => $options['text'] ?? ucfirst($type),
                'target' => $options['target'] ?? null
            ]
        };
    }

    /**
     * Get multiple primary buttons
     */
    public static function getPrimaryButtons(array $buttons): array
    {
        $result = [];
        
        foreach ($buttons as $button) {
            if (is_string($button)) {
                $result[] = self::getPrimaryButton($button);
            } elseif (is_array($button) && isset($button['type'])) {
                $type = $button['type'];
                $options = $button;
                unset($options['type']);
                $processedButton = self::getPrimaryButton($type, $options);

                // Preserve permission, condition, and hidden properties
                if (isset($button['permission'])) {
                    $processedButton['permission'] = $button['permission'];
                }
                if (isset($button['condition'])) {
                    $processedButton['condition'] = $button['condition'];
                }
                if (isset($button['hidden'])) {
                    $processedButton['hidden'] = $button['hidden'];
                }

                $result[] = $processedButton;
            }
        }
        
        return $result;
    }
}
