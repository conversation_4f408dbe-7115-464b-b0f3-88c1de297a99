<?php

namespace App\Helpers;

use App\Models\Notification;
use App\Models\User;
use App\Models\UserGroup;
use App\Services\NotificationService;
use Carbon\Carbon;

class NotificationHelper
{
    /**
     * Send a notification to all users
     */
    public static function notifyAllUsers(
        string $title,
        string $message,
        string $urgency = Notification::URGENCY_NORMAL,
        ?string $linkUrl = null,
        ?string $linkText = null,
        ?Carbon $expiresAt = null,
        bool $isDismissible = true,
        bool $autoDismiss = false
    ): Notification {
        $service = app(NotificationService::class);
        
        return $service->sendToAllUsers(
            $title,
            $message,
            $urgency,
            $linkUrl,
            $linkText,
            $expiresAt,
            $isDismissible,
            $autoDismiss
        );
    }

    /**
     * Send a notification to specific users
     */
    public static function notifyUsers(
        array $users,
        string $title,
        string $message,
        string $urgency = Notification::URGENCY_NORMAL,
        ?string $linkUrl = null,
        ?string $linkText = null,
        ?Carbon $expiresAt = null,
        bool $isDismissible = true,
        bool $autoDismiss = false
    ): Notification {
        $service = app(NotificationService::class);
        
        return $service->sendToUsers(
            $users,
            $title,
            $message,
            $urgency,
            $linkUrl,
            $linkText,
            $expiresAt,
            $isDismissible,
            $autoDismiss
        );
    }

    /**
     * Send a notification to user groups
     */
    public static function notifyUserGroups(
        array $userGroups,
        string $title,
        string $message,
        string $urgency = Notification::URGENCY_NORMAL,
        ?string $linkUrl = null,
        ?string $linkText = null,
        ?Carbon $expiresAt = null,
        bool $isDismissible = true,
        bool $autoDismiss = false
    ): Notification {
        $service = app(NotificationService::class);
        
        return $service->sendToUserGroups(
            $userGroups,
            $title,
            $message,
            $urgency,
            $linkUrl,
            $linkText,
            $expiresAt,
            $isDismissible,
            $autoDismiss
        );
    }

    /**
     * Send a notification to administrators
     */
    public static function notifyAdmins(
        string $title,
        string $message,
        string $urgency = Notification::URGENCY_HIGH,
        ?string $linkUrl = null,
        ?string $linkText = null,
        ?Carbon $expiresAt = null,
        bool $isDismissible = true,
        bool $autoDismiss = false
    ): ?Notification {
        $adminGroup = UserGroup::where('name', 'Admin')->first();
        
        if (!$adminGroup) {
            return null;
        }
        
        return self::notifyUserGroups(
            [$adminGroup],
            $title,
            $message,
            $urgency,
            $linkUrl,
            $linkText,
            $expiresAt,
            $isDismissible,
            $autoDismiss
        );
    }

    /**
     * Send a critical system notification
     */
    public static function systemAlert(
        string $title,
        string $message,
        ?string $linkUrl = null,
        ?string $linkText = null
    ): ?Notification {
        return self::notifyAdmins(
            $title,
            $message,
            Notification::URGENCY_CRITICAL,
            $linkUrl,
            $linkText,
            null, // Never expires
            false, // Not dismissible
            false
        );
    }

    /**
     * Send a welcome notification to a new user
     */
    public static function welcomeUser(User $user): Notification
    {
        return self::notifyUsers(
            [$user],
            'Welcome to ' . config('app.name', 'ETRFlow') . '!',
            'Welcome to the system! We\'re excited to have you on board. Take a moment to explore the features and let us know if you need any help.',
            Notification::URGENCY_NORMAL,
            route('dashboard'),
            'Get Started',
            now()->addDays(30), // Expires in 30 days
            true,
            false
        );
    }

    /**
     * Send a task assignment notification
     */
    public static function taskAssigned(User $user, $task): Notification
    {
        return self::notifyUsers(
            [$user],
            'New Task Assigned',
            "You have been assigned a new task: {$task->title}",
            Notification::URGENCY_NORMAL,
            route('my-tasks'),
            'View Tasks',
            null,
            true,
            false
        );
    }

    /**
     * Send an invoice notification
     */
    public static function invoiceCreated($invoice, User $user = null): Notification
    {
        $users = $user ? [$user] : User::where('enabled', true)->get();
        
        return self::notifyUsers(
            $users,
            'New Invoice Created',
            "Invoice #{$invoice->id} has been created for {$invoice->customer->name}",
            Notification::URGENCY_LOW,
            route('invoices.show', $invoice),
            'View Invoice',
            now()->addDays(7),
            true,
            false
        );
    }

    /**
     * Send a system maintenance notification
     */
    public static function maintenanceNotice(
        string $message,
        Carbon $maintenanceTime,
        ?string $linkUrl = null
    ): Notification {
        return self::notifyAllUsers(
            'Scheduled Maintenance',
            $message,
            Notification::URGENCY_HIGH,
            $linkUrl,
            'Learn More',
            $maintenanceTime->addHours(2), // Expires 2 hours after maintenance
            false, // Not dismissible
            false
        );
    }

    /**
     * Get urgency levels for forms
     */
    public static function getUrgencyLevels(): array
    {
        return [
            Notification::URGENCY_LOW => 'Low',
            Notification::URGENCY_NORMAL => 'Normal',
            Notification::URGENCY_HIGH => 'High',
            Notification::URGENCY_CRITICAL => 'Critical',
        ];
    }

    /**
     * Get urgency color class
     */
    public static function getUrgencyColorClass(string $urgency): string
    {
        return match ($urgency) {
            Notification::URGENCY_LOW => 'text-info',
            Notification::URGENCY_NORMAL => 'text-success',
            Notification::URGENCY_HIGH => 'text-warning',
            Notification::URGENCY_CRITICAL => 'text-error',
            default => 'text-info',
        };
    }
}
