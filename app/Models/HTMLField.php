<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Schema;

class HTMLField extends Model
{
    use HasFactory;

    protected $table = 'html_fields';
    protected $fillable = ['key', 'content', 'description'];

    /**
     * Get the content of an HTML field by key.
     *
     * @param string $key
     * @return string|null
     */
    public static function getContent(string $key): ?string
    {
        // Check if table exists first
        if (!Schema::hasTable('html_fields')) {
            return null; // Return null during migration
        }

        try {
            $field = self::where('key', $key)->first();
            return $field ? $field->content : null;
        } catch (\Exception $e) {
            return null; // Return null if there's an error
        }
    }

    /**
     * Set the content of an HTML field and save it to the database.
     *
     * @param string $key
     * @param string|null $content
     * @param string|null $description
     * @return bool
     */
    public static function setContent(string $key, ?string $content, ?string $description = null): bool
    {
        $field = self::firstOrNew(['key' => $key]);
        $field->content = $content;
        if ($description !== null) {
            $field->description = $description;
        }
        return $field->save();
    }

    /**
     * Get all HTML fields as key-value pairs
     *
     * @return array
     */
    public static function getAllFields(): array
    {
        if (!Schema::hasTable('html_fields')) {
            return [];
        }

        try {
            return self::pluck('content', 'key')->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get pickup request welcome message
     *
     * @return string|null
     */
    public static function getPickupWelcomeMessage(): ?string
    {
        return self::getContent('pickup_request_welcome_message');
    }

    /**
     * Get pickup request item details message
     *
     * @return string|null
     */
    public static function getPickupItemDetailsMessage(): ?string
    {
        return self::getContent('pickup_request_item_details_message');
    }

    /**
     * Get pickup request first-time customer message
     *
     * @return string|null
     */
    public static function getPickupFirstTimeMessage(): ?string
    {
        return self::getContent('pickup_request_first_time_message');
    }

    /**
     * Get pickup request fee acknowledgment message
     *
     * @return string|null
     */
    public static function getPickupFeeAcknowledgmentMessage(): ?string
    {
        return self::getContent('pickup_request_fee_acknowledgment_message');
    }

    /**
     * Get pickup request agreement message
     *
     * @return string|null
     */
    public static function getPickupAgreementMessage(): ?string
    {
        return self::getContent('pickup_request_agreement_message');
    }
}
