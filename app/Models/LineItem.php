<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LineItem extends Model
{
    protected $fillable = [
        'invoice_id',
        'item_id',
        'description',
        'tax_policy_id',
        'quantity',
        'price',
        'tax',
        'discount',
        'subtotal',
        'item_name',
        'department_id',
        'gross_revenue', //  Gross revenue before discounts/taxes are applied
        'net_revenue',   // Net revenue after discounts, before taxes
    ];

    // Ensure price can be negative for payouts
    protected $casts = [
        'price' => 'float',
    ];

    // Relationship: A line item belongs to an invoice
    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    // Relationship: A line item belongs to an inventory item
    public function inventory()
    {
        return $this->belongsTo(Inventory::class, 'item_id');
    }

    // Relationship: A line item belongs to a tax policy
    public function taxPolicy()
    {
        return $this->belongsTo(TaxPolicy::class, 'tax_policy_id');
    }

    // Relationship: A line item belongs to a department
    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }
}
