<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FormSubmissionHtmlContent extends Model
{
    protected $table = 'form_submission_html_content';
    
    protected $fillable = [
        'form_submission_id',
        'form_field_id',
        'field_name',
        'field_label',
        'original_content',
        'processed_content',
        'processed_at',
    ];

    protected $casts = [
        'processed_at' => 'datetime',
    ];

    public function formSubmission(): BelongsTo
    {
        return $this->belongsTo(FormSubmission::class);
    }

    public function formField(): BelongsTo
    {
        return $this->belongsTo(FormField::class);
    }
}
