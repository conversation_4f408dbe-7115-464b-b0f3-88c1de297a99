<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class TimePunch extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'time_card_id',
        'user_id',
        'type',
        'punch_time',
        'notes',
        'original_punch_id',
        'replaced_by',
        'edited_by',
        'ip_address',
    ];

    protected $casts = [
        'punch_time' => 'datetime',
    ];

    /**
     * Get the time card that owns the punch.
     */
    public function timeCard()
    {
        return $this->belongsTo(TimeCard::class);
    }

    /**
     * Get the user that owns the punch.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the editor of the punch.
     */
    public function editor()
    {
        return $this->belongsTo(User::class, 'edited_by');
    }

    /**
     * Get the original punch that this punch replaces.
     */
    public function originalPunch()
    {
        return $this->belongsTo(TimePunch::class, 'original_punch_id');
    }

    /**
     * Get the punch that replaced this punch.
     */
    public function replacementPunch()
    {
        return $this->hasOne(TimePunch::class, 'original_punch_id');
    }

    /**
     * Get the type name for display.
     */
    public function getTypeNameAttribute()
    {
        switch ($this->type) {
            case 'clock_in':
                return 'Clock In';
            case 'clock_out':
                return 'Clock Out';
            case 'break_out':
                return 'Break Start';
            case 'break_in':
                return 'Break End';
            case 'sick_time':
                return 'Sick Time';
            case 'vacation_time':
                return 'Vacation Time';
            default:
                return 'Unknown';
        }
    }

    /**
     * Get the icon for the punch type.
     */
    public function getTypeIconAttribute()
    {
        switch ($this->type) {
            case 'clock_in':
                return 'fa-sharp fa-solid fa-arrow-right-to-bracket text-success';
            case 'clock_out':
                return 'fa-sharp fa-solid fa-arrow-right-from-bracket text-error';
            case 'break_out':
                return 'fa-sharp fa-solid fa-mug-hot text-warning';
            case 'break_in':
                return 'fa-sharp fa-solid fa-person-walking text-info';
            case 'sick_time':
                return 'fa-sharp fa-solid fa-head-side-mask text-accent';
            case 'vacation_time':
                return 'fa-sharp fa-solid fa-umbrella-beach text-primary';
            default:
                return 'fa-sharp fa-solid fa-question text-gray-500';
        }
    }

    /**
     * Get the color for the punch type.
     */
    public function getTypeColorAttribute()
    {
        switch ($this->type) {
            case 'clock_in':
                return 'text-success';
            case 'clock_out':
                return 'text-error';
            case 'break_out':
                return 'text-warning';
            case 'break_in':
                return 'text-info';
            case 'sick_time':
                return 'text-accent';
            case 'vacation_time':
                return 'text-primary';
            default:
                return 'text-gray-500';
        }
    }

    /**
     * Create a new punch and mark any previous punch as replaced.
     *
     * @throws \Exception If the punch date doesn't match the time card date
     */
    public static function createPunch($timeCardId, $type, $notes = null, $punchTime = null, $originalPunchId = null, $editedBy = null)
    {
        $punchTime = $punchTime ?: Carbon::now();
        $userId = Auth::id();
        $ipAddress = request()->ip();

        // Get the time card
        $timeCard = TimeCard::find($timeCardId);
        if ($timeCard) {
            // Check if the punch date matches the time card date
            $punchDate = $punchTime->format('Y-m-d');
            $timecardDate = Carbon::parse($timeCard->date)->format('Y-m-d');

            if ($punchDate !== $timecardDate) {
                throw new \Exception("The punch date ({$punchDate}) must match the time card date ({$timecardDate}).");
            }
        }

        $punch = self::create([
            'time_card_id' => $timeCardId,
            'user_id' => $userId,
            'type' => $type,
            'punch_time' => $punchTime,
            'notes' => $notes,
            'original_punch_id' => $originalPunchId,
            'edited_by' => $editedBy,
            'ip_address' => $ipAddress,
        ]);

        // If this is replacing an original punch, mark the original as replaced
        if ($originalPunchId) {
            $originalPunch = self::find($originalPunchId);
            if ($originalPunch) {
                $originalPunch->replaced_by = $punch->id;
                $originalPunch->save();
            }
        }

        // Recalculate hours for the time card
        $timeCard = TimeCard::find($timeCardId);
        if ($timeCard) {
            $timeCard->calculateHours();
        }

        return $punch;
    }
}
