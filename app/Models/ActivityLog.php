<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ActivityLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'loggable_type',
        'loggable_id',
        'user_id',
        'event_type',
        'component',
        'description',
        'old_values',
        'new_values',
        'additional_data',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'additional_data' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the model that this log entry belongs to
     */
    public function loggable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who performed the action
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to filter by component
     */
    public function scopeForComponent($query, string $component)
    {
        return $query->where('component', $component);
    }

    /**
     * Scope to filter by user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by event type
     */
    public function scopeForEventType($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope to get recent logs
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Get a human-readable summary of changes
     */
    public function getChangesSummaryAttribute(): string
    {
        if (!$this->old_values || !$this->new_values) {
            return $this->description;
        }

        $changes = [];
        foreach ($this->new_values as $field => $newValue) {
            $oldValue = $this->old_values[$field] ?? null;
            if ($oldValue !== $newValue) {
                // Handle array values by converting to JSON
                $oldValueStr = is_array($oldValue) ? json_encode($oldValue) : $oldValue;
                $newValueStr = is_array($newValue) ? json_encode($newValue) : $newValue;
                $changes[] = "{$field}: '{$oldValueStr}' → '{$newValueStr}'";
            }
        }

        return empty($changes) ? $this->description : implode(', ', $changes);
    }

    /**
     * Get the model name in a human-readable format
     */
    public function getModelNameAttribute(): string
    {
        if (!$this->loggable_type) {
            return 'System';
        }

        $className = class_basename($this->loggable_type);
        return ucfirst(strtolower(preg_replace('/([A-Z])/', ' $1', $className)));
    }

    /**
     * Common event types
     */
    public const EVENT_TYPES = [
        'created' => 'Created',
        'updated' => 'Updated',
        'deleted' => 'Deleted',
        'restored' => 'Restored',
        'viewed' => 'Viewed',
        'exported' => 'Exported',
        'imported' => 'Imported',
        'custom' => 'Custom Action',
    ];

    /**
     * Get formatted event type
     */
    public function getFormattedEventTypeAttribute(): string
    {
        return self::EVENT_TYPES[$this->event_type] ?? ucfirst($this->event_type);
    }
}
