<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class EventCategory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'color',
        'creator_id',
        'is_global',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_global' => 'boolean',
    ];

    /**
     * Get the creator of the category.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * Get the events for the category.
     */
    public function events(): BelongsToMany
    {
        return $this->belongsToMany(Event::class, 'event_event_category');
    }

    /**
     * Scope a query to only include global categories.
     */
    public function scopeGlobal($query)
    {
        return $query->where('is_global', true);
    }

    /**
     * Scope a query to only include categories created by a specific user.
     */
    public function scopeCreatedBy($query, $userId)
    {
        return $query->where('creator_id', $userId);
    }

    /**
     * Scope a query to include categories available to a user (global or created by the user).
     */
    public function scopeAvailableTo($query, $userId)
    {
        return $query->where('is_global', true)
                     ->orWhere('creator_id', $userId);
    }
}
