<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'message',
        'urgency',
        'target_type',
        'target_ids',
        'link_url',
        'link_text',
        'expires_at',
        'is_dismissible',
        'auto_dismiss',
        'created_by',
    ];

    protected $casts = [
        'target_ids' => 'array',
        'expires_at' => 'datetime',
        'is_dismissible' => 'boolean',
        'auto_dismiss' => 'boolean',
    ];

    protected static function booted()
    {
        static::created(function ($notification) {
            $notification->createUserPivotRecords();
        });
    }

    // Urgency levels
    public const URGENCY_LOW = 'low';
    public const URGENCY_NORMAL = 'normal';
    public const URGENCY_HIGH = 'high';
    public const URGENCY_CRITICAL = 'critical';

    // Target types
    public const TARGET_ALL_USERS = 'all_users';
    public const TARGET_USER_GROUP = 'user_group';
    public const TARGET_SPECIFIC_USERS = 'specific_users';

    /**
     * Get the user who created this notification
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Users who have interacted with this notification
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'notification_user')
            ->withPivot(['read_at', 'dismissed_at'])
            ->withTimestamps();
    }

    /**
     * Scope to get active notifications (not expired)
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * Scope to get notifications by urgency
     */
    public function scopeByUrgency(Builder $query, string $urgency): Builder
    {
        return $query->where('urgency', $urgency);
    }

    /**
     * Scope to get notifications for a specific user
     */
    public function scopeForUser(Builder $query, User $user): Builder
    {
        return $query->whereHas('users', function ($q) use ($user) {
            $q->where('user_id', $user->id);
        })->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        })->orderBy('created_at', 'desc');
    }

    /**
     * Check if notification is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if user has read this notification
     */
    public function isReadByUser(User $user): bool
    {
        try {
            return $this->users()
                ->where('user_id', $user->id)
                ->whereNotNull('notification_user.read_at')
                ->exists();
        } catch (\Exception $e) {
            \Log::warning('Error checking if notification is read by user', [
                'notification_id' => $this->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if user has dismissed this notification
     */
    public function isDismissedByUser(User $user): bool
    {
        try {
            return $this->users()
                ->where('user_id', $user->id)
                ->whereNotNull('notification_user.dismissed_at')
                ->exists();
        } catch (\Exception $e) {
            \Log::warning('Error checking if notification is dismissed by user', [
                'notification_id' => $this->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Mark notification as read by user
     */
    public function markAsReadByUser(User $user): void
    {
        $this->users()->syncWithoutDetaching([
            $user->id => [
                'read_at' => now(),
                'updated_at' => now(),
            ]
        ]);

        // Auto-dismiss if configured
        if ($this->auto_dismiss) {
            $this->markAsDismissedByUser($user);
        }
    }

    /**
     * Mark notification as dismissed by user
     */
    public function markAsDismissedByUser(User $user): void
    {
        try {
            $this->users()->syncWithoutDetaching([
                $user->id => [
                    'dismissed_at' => now(),
                    'updated_at' => now(),
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error marking notification as dismissed by user', [
                'notification_id' => $this->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get CSS class for urgency styling
     */
    public function getUrgencyClass(): string
    {
        return match ($this->urgency) {
            self::URGENCY_LOW => 'alert-info',
            self::URGENCY_NORMAL => 'alert-success',
            self::URGENCY_HIGH => 'alert-warning',
            self::URGENCY_CRITICAL => 'alert-error',
            default => 'alert-info',
        };
    }

    /**
     * Get icon for urgency level
     */
    public function getUrgencyIcon(): string
    {
        return match ($this->urgency) {
            self::URGENCY_LOW => 'fa-info-circle',
            self::URGENCY_NORMAL => 'fa-check-circle',
            self::URGENCY_HIGH => 'fa-exclamation-triangle',
            self::URGENCY_CRITICAL => 'fa-exclamation-circle',
            default => 'fa-info-circle',
        };
    }

    /**
     * Create pivot records for users based on target type
     */
    public function createUserPivotRecords(): void
    {
        $userIds = [];

        switch ($this->target_type) {
            case self::TARGET_ALL_USERS:
                $userIds = User::where('enabled', true)->pluck('id')->toArray();
                break;

            case self::TARGET_SPECIFIC_USERS:
                $userIds = $this->target_ids ?? [];
                break;

            case self::TARGET_USER_GROUP:
                if ($this->target_ids) {
                    // Extract user group IDs from target_ids
                    $groupIds = collect($this->target_ids)->map(function ($item) {
                        // Handle both ID arrays and full group objects
                        if (is_array($item) && isset($item['id'])) {
                            return $item['id']; // Full object with id key
                        } elseif (is_numeric($item)) {
                            return $item; // Just the ID
                        }
                        return null;
                    })->filter()->toArray();

                    if (!empty($groupIds)) {
                        $userIds = \DB::table('user_group_user')
                            ->whereIn('user_group_id', $groupIds)
                            ->pluck('user_id')
                            ->unique()
                            ->toArray();
                    }
                }
                break;
        }

        // Create pivot records for all target users
        if (!empty($userIds)) {
            $pivotData = [];
            $now = now();

            foreach ($userIds as $userId) {
                $pivotData[] = [
                    'notification_id' => $this->id,
                    'user_id' => $userId,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            // Insert all pivot records at once
            \DB::table('notification_user')->insert($pivotData);
        }
    }
}
