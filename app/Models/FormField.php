<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FormField extends Model
{
    protected $fillable = [
        'form_id',
        'type',
        'name',
        'label',
        'content',
        'options',
        'validation_rules',
        'required',
        'placeholder',
        'help_text',
        'order',
        'settings',
    ];

    protected $casts = [
        'options' => 'json',
        'validation_rules' => 'json',
        'required' => 'boolean',
        'settings' => 'json',
    ];

    const TYPE_TEXT = 'text';
    const TYPE_TEXTAREA = 'textarea';
    const TYPE_SELECT = 'select';
    const TYPE_RADIO = 'radio';
    const TYPE_CHECKBOX = 'checkbox';
    const TYPE_HTML = 'html';
    const TYPE_SIGNATURE = 'signature';
    const TYPE_EMAIL = 'email';
    const TYPE_PHONE = 'phone';
    const TYPE_NUMBER = 'number';
    const TYPE_DATE = 'date';

    const TYPES = [
        self::TYPE_TEXT => 'Text Input',
        self::TYPE_EMAIL => 'Email Input',
        self::TYPE_PHONE => 'Phone Input',
        self::TYPE_NUMBER => 'Number Input',
        self::TYPE_DATE => 'Date Input',
        self::TYPE_TEXTAREA => 'Text Area',
        self::TYPE_SELECT => 'Select Dropdown',
        self::TYPE_RADIO => 'Radio Buttons',
        self::TYPE_CHECKBOX => 'Checkboxes',
        self::TYPE_HTML => 'HTML Content',
        self::TYPE_SIGNATURE => 'Signature Pad',
    ];

    public function form(): BelongsTo
    {
        return $this->belongsTo(Form::class);
    }

    public function isInput(): bool
    {
        return !in_array($this->type, [self::TYPE_HTML, self::TYPE_SIGNATURE]);
    }

    public function hasOptions(): bool
    {
        return in_array($this->type, [self::TYPE_SELECT, self::TYPE_RADIO, self::TYPE_CHECKBOX]);
    }

    /**
     * Clean up encoding issues in HTML content
     */
    public function getContentAttribute($value)
    {
        if ($this->type === self::TYPE_HTML && $value) {
            // Fix common encoding issues with quotes and apostrophes
            return str_replace([
                'â€™', 'â€œ', 'â€', 'â€"', 'â€"', 'â€¦', 'â', 'â', 'Â'
            ], [
                "'", '"', '"', '–', '—', '…', "'", '"', ''
            ], $value);
        }
        
        return $value;
    }

    public function getValidationRulesString(): string
    {
        $rules = $this->validation_rules ?? [];
        
        if ($this->required && !in_array('required', $rules)) {
            array_unshift($rules, 'required');
        }

        // Add type-specific validation
        switch ($this->type) {
            case self::TYPE_EMAIL:
                if (!in_array('email', $rules)) {
                    $rules[] = 'email';
                }
                break;
            case self::TYPE_NUMBER:
                if (!in_array('numeric', $rules)) {
                    $rules[] = 'numeric';
                }
                break;
            case self::TYPE_DATE:
                if (!in_array('date', $rules)) {
                    $rules[] = 'date';
                }
                break;
        }

        return implode('|', $rules);
    }

    public function getOptionsArray(): array
    {
        if (!$this->hasOptions()) {
            return [];
        }

        $options = $this->options ?? [];
        
        // Handle both simple array and key-value pairs
        if (is_array($options)) {
            return $options;
        }

        return [];
    }
}
