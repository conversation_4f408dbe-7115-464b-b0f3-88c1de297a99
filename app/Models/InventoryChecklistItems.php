<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryChecklistItems extends Model
{
    use HasFactory;

    protected $fillable = [
        'inventory_id',
        'checklist_field_id',
        'value',
        'long_value',
    ];

    public function inventory()
    {
        return $this->belongsTo(Inventory::class);
    }

    public function checklistField()
    {
        return $this->belongsTo(ChecklistFields::class, 'checklist_field_id');
    }
    protected $casts = [
        'long_value' => 'array', // For checkboxes stored as JSON
    ];
    
    
}
