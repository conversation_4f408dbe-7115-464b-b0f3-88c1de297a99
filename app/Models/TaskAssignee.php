<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TaskAssignee extends Model
{
    use HasFactory;

    protected $fillable = [
        'task_id',
        'assignee_type', // 'user' or 'group'
        'assignee_id', // user_id or group_id based on assignee_type
    ];

    // Relationships
    public function task()
    {
        return $this->belongsTo(Task::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'assignee_id')->where('assignee_type', 'user');
    }

    public function userGroup()
    {
        return $this->belongsTo(UserGroup::class, 'assignee_id')->where('assignee_type', 'group');
    }

    
}
