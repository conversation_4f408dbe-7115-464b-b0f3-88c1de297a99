<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerDiscount extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'discount_id',
        'start_date',
        'end_date',
        'usage_count',
        'maximum_uses',
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class)->withTrashed();
    }

    public function discount()
    {
        return $this->belongsTo(Discount::class);
    }

    public function isActive()
    {
        $now = now();
        return (!$this->start_date || $this->start_date <= $now) &&
               (!$this->end_date || $this->end_date >= $now) &&
               ($this->maximum_uses === null || $this->usage_count < $this->maximum_uses);
    }


    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    protected static function booted()
{
    static::creating(function ($customerDiscount) {
        // Set maximum_uses based on related discount if not explicitly set
        if (!$customerDiscount->maximum_uses) {
            $discount = $customerDiscount->discount;
            $customerDiscount->maximum_uses = $discount->reusable ? null : 1;
        }
    });
}

    
}
