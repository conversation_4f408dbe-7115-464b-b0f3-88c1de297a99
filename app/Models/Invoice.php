<?php

namespace App\Models;

use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'invoice_date',
        'total_price',
        'total_tax',
        'total_discount',
        'final_price',
        'status',
        'invoice_number',
        'payment_method',
        'notes',
    ];

    protected static function booted()
    {
        static::creating(function ($invoice) {
            if (empty($invoice->invoice_number)) {
                $lastInvoice = self::latest('id')->first();
                $lastNumber = $lastInvoice ? intval(substr($lastInvoice->invoice_number, 4)) : 0;
                $invoice->invoice_number = 'INV-' . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
            }
        });

        static::saving(function ($invoice) {
            $invoice->calculateTotals();
        });
    }

    public function discounts()
    {
        return $this->belongsToMany(Discount::class, 'invoice_discounts');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id')->withTrashed();
    }


    public function lineItems()
    {
        return $this->hasMany(LineItem::class, 'invoice_id');
    }


    protected $casts = [
        'invoice_date' => 'datetime',
    ];

    public function invoiceDiscounts()
    {
        return $this->hasMany(InvoiceDiscount::class, 'invoice_id');
    }

    /**
     * Polymorphic relationship with files
     */
    public function files()
    {
        return $this->morphMany(File::class, 'fileable');
    }

    /**
     * Check if user can access files attached to this invoice
     * Used by the File model to determine access permissions
     */
    public function userCanAccessFiles($user)
    {
        // Users with permission to view invoices can access the files
        return $user && $user->hasPermission('view_invoices');
    }



    public function calculateTotals()
    {
        $totalPrice = 0;       // Total Gross Revenue
        $totalTax = 0;         // Total Tax
        $totalDiscounts = 0;   // Total Discounts
        $totalNetRevenue = 0;  // Total Net Revenue

        // Adjust totals to account for negative prices (payouts)
        $lineItems = $this->lineItems()->with('taxPolicy')->get(); // Eager load tax policy

        // Precompute total gross revenue
        $totalGrossRevenue = $lineItems->sum(fn($lineItem) => $lineItem->quantity * $lineItem->price);

        // Calculate the total discount from invoice discounts
        $totalDiscount = $this->invoiceDiscounts()->sum('discount_amount');
        $remainingDiscount = $totalDiscount;

        foreach ($lineItems as $lineItem) {
            // Calculate line item total (price * quantity)
            // Note: For payouts (negative prices), the total will be negative
            // This is correct as it represents money flowing out of the business
            $lineItemTotal = $lineItem->quantity * $lineItem->price;

            // For contribution calculation, we need to handle negative prices specially
            // We'll use absolute values for the contribution calculation
            $absLineItemTotal = abs($lineItemTotal);
            $absTotalGrossRevenue = abs($totalGrossRevenue);

            // Contribution of this line item to the total gross revenue
            $lineItemContribution = $absTotalGrossRevenue > 0 ? ($absLineItemTotal / $absTotalGrossRevenue) : 0;

            // Assign gross revenue
            $lineItem->gross_revenue = $lineItemTotal;

            // Calculate the discount for the line item
            $lineItemDiscount = round($totalDiscount * $lineItemContribution, 2);

            // Ensure we do not over-allocate the discount
            if ($remainingDiscount < $lineItemDiscount) {
                $lineItemDiscount = $remainingDiscount;
            }

            $lineItem->discount = $lineItemDiscount;
            $remainingDiscount -= $lineItemDiscount;
            $totalDiscounts += $lineItemDiscount;

            // Calculate net revenue (after discount, before tax)
            $lineItemNetRevenue = $lineItemTotal - $lineItemDiscount;
            $lineItem->net_revenue = $lineItemNetRevenue;

            // Calculate tax for the line item
            $taxRate = $lineItem->taxPolicy ? $lineItem->taxPolicy->rate : 0;
            $lineItemTax = round($lineItemNetRevenue * ($taxRate / 100), 2);
            $lineItem->tax = $lineItemTax;

            // Calculate subtotal (Net Revenue + Tax)
            $lineItem->subtotal = $lineItemNetRevenue + $lineItemTax;

            // Save the line item without triggering lifecycle events
            $lineItem->saveQuietly();

            // Accumulate totals
            $totalPrice += $lineItemTotal;
            $totalTax += $lineItemTax;
            $totalNetRevenue += $lineItemNetRevenue;
        }

        // Assign invoice totals
        $this->total_price = $totalPrice;                 // Total Gross Revenue
        $this->total_discount = $totalDiscounts;          // Total Discounts
        $this->total_tax = $totalTax;                     // Total Tax
        $this->final_price = $totalNetRevenue + $totalTax; // Final Price (Net Revenue + Tax)

        // Save the invoice without triggering lifecycle events
        $this->saveQuietly();
    }


}
