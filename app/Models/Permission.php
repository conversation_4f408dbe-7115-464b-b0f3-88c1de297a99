<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Permission extends Model
{
    protected $fillable = ['name', 'description', 'scope'];

    protected static function booted()
    {
        static::created(function ($permission) {
            $adminGroup = UserGroup::where('name', 'Admin')->first();
            if ($adminGroup) {
                $permission->userGroups()->attach($adminGroup->id);
            }
        });
    }

    
    public function userGroups()
    {
        return $this->belongsToMany(UserGroup::class, 'user_group_permission');
    }
}
