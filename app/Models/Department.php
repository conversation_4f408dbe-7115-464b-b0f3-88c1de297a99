<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Department extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'user_id', 'description'];

    public function leader()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function lineItems()
    {
        return $this->hasMany(LineItem::class, 'department_id');
    }
}
