<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class NotificationConfig extends Model
{
    use HasFactory;

    protected $fillable = [
        'configurable_type',
        'configurable_id',
        'event_type',
        'target_type',
        'target_ids',
        'urgency',
        'is_enabled',
        'custom_title',
        'custom_message',
        'include_link',
        'link_text',
        'expires_after_days',
        'is_dismissible',
        'auto_dismiss',
    ];

    protected $casts = [
        'target_ids' => 'array',
        'is_enabled' => 'boolean',
        'include_link' => 'boolean',
        'is_dismissible' => 'boolean',
        'auto_dismiss' => 'boolean',
    ];

    // Target types
    public const TARGET_ALL_USERS = 'all_users';
    public const TARGET_USER_GROUP = 'user_group';
    public const TARGET_SPECIFIC_USERS = 'specific_users';

    // Urgency levels
    public const URGENCY_LOW = 'low';
    public const URGENCY_NORMAL = 'normal';
    public const URGENCY_HIGH = 'high';
    public const URGENCY_CRITICAL = 'critical';

    // Common event types
    public const EVENT_FORM_SUBMITTED = 'form_submitted';
    public const EVENT_TASK_CREATED = 'task_created';
    public const EVENT_TASK_ASSIGNED = 'task_assigned';
    public const EVENT_INVOICE_CREATED = 'invoice_created';
    public const EVENT_PICKUP_REQUESTED = 'pickup_requested';

    /**
     * Get the configurable model (Form, Task, etc.)
     */
    public function configurable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user groups for this configuration
     */
    public function userGroups(): BelongsToMany
    {
        return $this->belongsToMany(UserGroup::class, 'notification_config_user_group');
    }

    /**
     * Get the specific users for this configuration
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'notification_config_user');
    }

    /**
     * Scope to get enabled configurations
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * Scope to get configurations for a specific event type
     */
    public function scopeForEvent($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Get target user groups
     */
    public function getTargetUserGroups()
    {
        if ($this->target_type !== self::TARGET_USER_GROUP) {
            return collect();
        }

        return UserGroup::whereIn('id', $this->target_ids ?? [])->get();
    }

    /**
     * Get target users
     */
    public function getTargetUsers()
    {
        if ($this->target_type !== self::TARGET_SPECIFIC_USERS) {
            return collect();
        }

        return User::whereIn('id', $this->target_ids ?? [])->get();
    }

    /**
     * Check if this configuration targets all users
     */
    public function targetsAllUsers(): bool
    {
        return $this->target_type === self::TARGET_ALL_USERS;
    }

    /**
     * Get expiration date based on expires_after_days
     */
    public function getExpirationDate()
    {
        if (!$this->expires_after_days) {
            return null;
        }

        return now()->addDays($this->expires_after_days);
    }
}
