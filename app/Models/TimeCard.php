<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;


class TimeCard extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'date',
        'notes',
        'total_hours',
        'total_break_hours',
        'total_sick_time',
        'total_vacation_time',
    ];

    protected $casts = [
        'date' => 'date',
        // Hours are now stored as strings in HH:MM:SS format
    ];

    protected $attributes = [
        'total_hours' => '00:00:00',
        'total_break_hours' => '00:00:00',
        'total_sick_time' => '00:00:00',
        'total_vacation_time' => '00:00:00',
    ];

    /**
     * Get the user that owns the time card.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the punches for the time card.
     */
    public function punches()
    {
        return $this->hasMany(TimePunch::class);
    }

    /**
     * Get all valid (not replaced) punches for the time card.
     */
    public function allValidPunches()
    {
        return $this->punches()->whereNull('replaced_by');
    }

    /**
     * Check if the user is currently clocked in.
     */
    public function isClockedIn()
    {
        $lastPunch = $this->allValidPunches()->orderBy('punch_time', 'desc')->first();
        return $lastPunch && ($lastPunch->type === 'clock_in' || $lastPunch->type === 'break_in');
    }

    /**
     * Check if the user is currently on break.
     */
    public function isOnBreak()
    {
        $lastPunch = $this->allValidPunches()->orderBy('punch_time', 'desc')->first();
        return $lastPunch && $lastPunch->type === 'break_out';
    }

    /**
     * Get the current status of the time card.
     */
    public function getCurrentStatus()
    {
        if ($this->isOnBreak()) {
            return 'break';
        } elseif ($this->isClockedIn()) {
            return 'clock_in';
        } else {
            return 'clock_out';
        }
    }

    /**
     * Get the last clock in punch.
     */
    public function getLastClockInPunch()
    {
        return $this->allValidPunches()
            ->whereIn('type', ['clock_in', 'break_in'])
            ->orderBy('punch_time', 'desc')
            ->first();
    }

    /**
     * Get the last break start punch.
     */
    public function getLastBreakStartPunch()
    {
        return $this->allValidPunches()
            ->where('type', 'break_out')
            ->orderBy('punch_time', 'desc')
            ->first();
    }

    /**
     * Get the most recent break start punch (for break duration warnings).
     */
    public function getMostRecentBreakStartPunch()
    {
        return $this->allValidPunches()
            ->where('type', 'break_out')
            ->orderBy('punch_time', 'desc')
            ->first();
    }

    /**
     * Calculate and update the total hours worked.
     */
    public function calculateHours($save = true)
    {
        $punches = $this->allValidPunches()->orderBy('punch_time')->get();
        $totalSeconds = 0;
        $breakSeconds = 0;
        $sickTimeSeconds = 0;
        $vacationTimeSeconds = 0;
        $clockInTime = null;
        $breakStartTime = null;

        // If there are no punches and we're just calculating current values, return zeros
        if ($punches->isEmpty() && !$save) {
            return [
                'total_hours' => 0, // Decimal hours for backward compatibility
                'total_break_hours' => 0, // Decimal hours for backward compatibility
                'total_sick_time' => 0,
                'total_vacation_time' => 0,
                'formatted_total_hours' => '00:00:00',
                'formatted_total_break_hours' => '00:00:00',
                'formatted_total_sick_time' => '00:00:00',
                'formatted_total_vacation_time' => '00:00:00',
            ];
        }

        foreach ($punches as $punch) {
            $punchTime = Carbon::parse($punch->punch_time);

            if ($punch->type === 'clock_in') {
                // Regular clock in
                $clockInTime = $punchTime;
            } elseif ($punch->type === 'clock_out') {
                // Regular clock out
                if ($clockInTime) {
                    $totalSeconds += abs($punchTime->timestamp - $clockInTime->timestamp);
                    $clockInTime = null;
                }
            } elseif ($punch->type === 'break_out') {
                // Break start
                $breakStartTime = $punchTime;
                if ($clockInTime) {
                    $totalSeconds += abs($punchTime->timestamp - $clockInTime->timestamp);
                    $clockInTime = null;
                }
            } elseif ($punch->type === 'break_in') {
                // Break end
                if ($breakStartTime) {
                    $breakSeconds += abs($punchTime->timestamp - $breakStartTime->timestamp);
                    $breakStartTime = null;
                }
                $clockInTime = $punchTime;
            } elseif ($punch->type === 'sick_time') {
                // Sick time - add the hours from the notes field (expected format: "8:00:00" or similar)
                if (!empty($punch->notes) && preg_match('/^\d{1,2}:\d{2}(:\d{2})?$/', $punch->notes)) {
                    // Convert HH:MM:SS to seconds
                    $parts = explode(':', $punch->notes);
                    $hours = (int)$parts[0];
                    $minutes = (int)$parts[1];
                    $seconds = isset($parts[2]) ? (int)$parts[2] : 0;
                    $sickTimeSeconds += ($hours * 3600) + ($minutes * 60) + $seconds;
                }
            } elseif ($punch->type === 'vacation_time') {
                // Vacation time - add the hours from the notes field (expected format: "8:00:00" or similar)
                if (!empty($punch->notes) && preg_match('/^\d{1,2}:\d{2}(:\d{2})?$/', $punch->notes)) {
                    // Convert HH:MM:SS to seconds
                    $parts = explode(':', $punch->notes);
                    $hours = (int)$parts[0];
                    $minutes = (int)$parts[1];
                    $seconds = isset($parts[2]) ? (int)$parts[2] : 0;
                    $vacationTimeSeconds += ($hours * 3600) + ($minutes * 60) + $seconds;
                }
            }
        }

        // If still clocked in or on break, calculate up to now
        $now = Carbon::now();

        // Get the current status
        $status = $this->getCurrentStatus();

        // If user is currently clocked in (and not on break), add time since last clock in
        if ($status === 'clock_in') {
            $lastClockInPunch = $this->getLastClockInPunch();
            if ($lastClockInPunch) {
                $lastClockInTime = Carbon::parse($lastClockInPunch->punch_time);
                $elapsedSeconds = abs($now->timestamp - $lastClockInTime->timestamp);
                $totalSeconds += $elapsedSeconds;

                // Log for debugging
                Log::info("Adding {$elapsedSeconds} seconds to total time. Current total: {$totalSeconds}");
            }
        }

        // If user is currently on break, add time since last break start
        if ($status === 'break') {
            $lastBreakStartPunch = $this->getLastBreakStartPunch();
            if ($lastBreakStartPunch) {
                $lastBreakStartTime = Carbon::parse($lastBreakStartPunch->punch_time);
                $breakSeconds += abs($now->timestamp - $lastBreakStartTime->timestamp);
            }
        }

        // Format seconds as HH:MM:SS
        $formattedTotalHours = $this->formatSecondsToTime($totalSeconds);
        $formattedTotalBreakHours = $this->formatSecondsToTime($breakSeconds);
        $formattedTotalSickTime = $this->formatSecondsToTime($sickTimeSeconds);
        $formattedTotalVacationTime = $this->formatSecondsToTime($vacationTimeSeconds);

        // Calculate decimal hours for backward compatibility
        $totalHours = round($totalSeconds / 3600, 2);
        $totalBreakHours = round($breakSeconds / 3600, 2);
        $totalSickTime = round($sickTimeSeconds / 3600, 2);
        $totalVacationTime = round($vacationTimeSeconds / 3600, 2);

        // Only save to database if save parameter is true
        if ($save) {
            $this->total_hours = $formattedTotalHours;
            $this->total_break_hours = $formattedTotalBreakHours;
            $this->total_sick_time = $formattedTotalSickTime;
            $this->total_vacation_time = $formattedTotalVacationTime;
            $this->save();
        }

        // Log the final values for debugging
        Log::info("Final calculation - totalSeconds: {$totalSeconds}, breakSeconds: {$breakSeconds}, sickTimeSeconds: {$sickTimeSeconds}, vacationTimeSeconds: {$vacationTimeSeconds}");
        Log::info("Final formatted values - totalHours: {$formattedTotalHours}, breakHours: {$formattedTotalBreakHours}, sickTime: {$formattedTotalSickTime}, vacationTime: {$formattedTotalVacationTime}");

        return [
            'total_hours' => $totalHours, // Decimal hours for backward compatibility
            'total_break_hours' => $totalBreakHours, // Decimal hours for backward compatibility
            'total_sick_time' => $totalSickTime,
            'total_vacation_time' => $totalVacationTime,
            'formatted_total_hours' => $formattedTotalHours,
            'formatted_total_break_hours' => $formattedTotalBreakHours,
            'formatted_total_sick_time' => $formattedTotalSickTime,
            'formatted_total_vacation_time' => $formattedTotalVacationTime,
        ];
    }



    /**
     * Format hours as HH:MM:SS (for backward compatibility)
     */
    public function formatHours($hours)
    {
        // Ensure hours is a positive number
        $hours = abs($hours);

        // Calculate hours, minutes, and seconds
        $wholeHours = floor($hours);
        $decimalMinutes = ($hours - $wholeHours) * 60;
        $minutes = floor($decimalMinutes);
        $seconds = round(($decimalMinutes - $minutes) * 60);

        // Handle case where seconds round to 60
        if ($seconds == 60) {
            $minutes++;
            $seconds = 0;
        }

        // Handle case where minutes round to 60
        if ($minutes == 60) {
            $wholeHours++;
            $minutes = 0;
        }

        // Format with leading zeros
        return sprintf('%02d:%02d:%02d', $wholeHours, $minutes, $seconds);
    }

    /**
     * Get formatted total hours
     */
    public function getFormattedTotalHoursAttribute()
    {
        // Hours are already stored in HH:MM:SS format
        return $this->total_hours;
    }

    /**
     * Get formatted total break hours
     */
    public function getFormattedTotalBreakHoursAttribute()
    {
        // Hours are already stored in HH:MM:SS format
        return $this->total_break_hours;
    }

    /**
     * Get total hours in decimal format
     */
    public function getTotalHoursDecimalAttribute()
    {
        return $this->timeToDecimalHours($this->total_hours);
    }

    /**
     * Get total break hours in decimal format
     */
    public function getTotalBreakHoursDecimalAttribute()
    {
        return $this->timeToDecimalHours($this->total_break_hours);
    }

    /**
     * Get formatted total sick time
     */
    public function getFormattedTotalSickTimeAttribute()
    {
        // Hours are already stored in HH:MM:SS format
        return $this->total_sick_time;
    }

    /**
     * Get formatted total vacation time
     */
    public function getFormattedTotalVacationTimeAttribute()
    {
        // Hours are already stored in HH:MM:SS format
        return $this->total_vacation_time;
    }

    /**
     * Get total sick time in decimal format
     */
    public function getTotalSickTimeDecimalAttribute()
    {
        return $this->timeToDecimalHours($this->total_sick_time);
    }

    /**
     * Get total vacation time in decimal format
     */
    public function getTotalVacationTimeDecimalAttribute()
    {
        return $this->timeToDecimalHours($this->total_vacation_time);
    }

    /**
     * Get total PTO (sick + vacation) in decimal format
     */
    public function getTotalPtoDecimalAttribute()
    {
        return $this->getTotalSickTimeDecimalAttribute() + $this->getTotalVacationTimeDecimalAttribute();
    }

    /**
     * Get total billable hours (regular hours + PTO) in decimal format
     *
     * IMPORTANT: The total_hours already excludes breaks, so we should NOT subtract breaks again
     */
    public function getTotalBillableHoursDecimalAttribute()
    {
        // Work hours + PTO hours
        return $this->getTotalHoursDecimalAttribute() + $this->getTotalPtoDecimalAttribute();
    }

    /**
     * Convert HH:MM:SS to decimal hours
     */
    public function timeToDecimalHours($timeString)
    {
        if (empty($timeString)) {
            return 0;
        }

        $parts = explode(':', $timeString);

        if (count($parts) !== 3) {
            return 0;
        }

        $hours = (int)$parts[0];
        $minutes = (int)$parts[1];
        $seconds = (int)$parts[2];

        return $hours + ($minutes / 60) + ($seconds / 3600);
    }

    /**
     * Get current hours in real-time
     */
    public function getCurrentHours()
    {
        // Always get fresh data from the database
        $this->refresh();

        // Get the latest punches
        $this->load('punches');

        // Log the current status
        $status = $this->getCurrentStatus();
        Log::info("getCurrentHours - Current status: {$status}");

        // Calculate hours without saving to database
        $hours = $this->calculateHours(false);

        // Log the calculated hours
        Log::info("getCurrentHours - Calculated hours: " . json_encode($hours));

        return $hours;
    }

    /**
     * Format seconds to HH:MM:SS time string
     */
    public function formatSecondsToTime($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $secs);
    }

    /**
     * Check if the user was auto-clocked out on this time card
     */
    public function wasAutoClockedOut()
    {
        $lastClockOut = $this->allValidPunches()
            ->where('type', 'clock_out')
            ->orderBy('punch_time', 'desc')
            ->first();

        if (!$lastClockOut) {
            return false;
        }

        // Check if the notes indicate auto clock-out
        return $lastClockOut->notes && str_contains($lastClockOut->notes, 'Auto clock-out');
    }

    /**
     * Get the auto clock-out punch if exists
     */
    public function getAutoClockOutPunch()
    {
        return $this->allValidPunches()
            ->where('type', 'clock_out')
            ->where('notes', 'like', 'Auto clock-out%')
            ->orderBy('punch_time', 'desc')
            ->first();
    }

    /**
     * Validate if a punch action is allowed based on current state
     * 
     * @param string $action The action to validate (clock_in, clock_out, break_out, break_in)
     * @return array ['allowed' => bool, 'message' => string]
     */
    public function validatePunchAction($action)
    {
        $currentStatus = $this->getCurrentStatus();
        $lastPunch = $this->allValidPunches()->orderBy('punch_time', 'desc')->first();
        
        switch ($action) {
            case 'clock_in':
                if ($currentStatus === 'clock_in') {
                    return [
                        'allowed' => false,
                        'message' => 'You are already clocked in. You cannot clock in twice without clocking out first.'
                    ];
                }
                if ($currentStatus === 'break') {
                    // Allow clock_in from break state (it acts as break_in)
                    return ['allowed' => true, 'message' => ''];
                }
                return ['allowed' => true, 'message' => ''];
                
            case 'clock_out':
                if ($currentStatus === 'clock_out') {
                    return [
                        'allowed' => false,
                        'message' => 'You are already clocked out. You must clock in first.'
                    ];
                }
                return ['allowed' => true, 'message' => ''];
                
            case 'break_out':
                if ($currentStatus === 'clock_out') {
                    return [
                        'allowed' => false,
                        'message' => 'You must clock in before taking a break.'
                    ];
                }
                if ($currentStatus === 'break') {
                    return [
                        'allowed' => false,
                        'message' => 'You are already on break. End your current break before starting another.'
                    ];
                }
                
                return ['allowed' => true, 'message' => ''];
                
            case 'break_in':
                if ($currentStatus !== 'break') {
                    return [
                        'allowed' => false,
                        'message' => 'You are not on break. You cannot end a break that hasn\'t started.'
                    ];
                }
                return ['allowed' => true, 'message' => ''];
                
            default:
                return [
                    'allowed' => false,
                    'message' => 'Invalid action.'
                ];
        }
    }

    /**
     * Get time since last work session started (clock in or break end)
     * 
     * @return int Minutes since last work session started
     */
    public function getMinutesSinceLastWorkStart()
    {
        $lastWorkStart = $this->allValidPunches()
            ->whereIn('type', ['clock_in', 'break_in'])
            ->orderBy('punch_time', 'desc')
            ->first();
            
        if (!$lastWorkStart) {
            return 0;
        }
        
        return Carbon::now()->diffInMinutes(Carbon::parse($lastWorkStart->punch_time));
    }
}
