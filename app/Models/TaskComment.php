<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TaskComment extends Model
{
    use HasFactory;

    protected $fillable = [
        'task_completion_id',
        'user_id',
        'comment',
    ];

    // Relationships
    public function taskCompletion()
    {
        return $this->belongsTo(TaskCompletion::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
