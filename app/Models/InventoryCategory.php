<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InventoryCategory extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'code',
        'status',
        'quantity_type',
        'tax_policy_id',
        'department_id',
        'ai_promptable',
    ];

    public function fields()
    {
        return $this->hasMany(ChecklistFields::class, 'category_id');
    }

    public function checklistFields()
    {
        return $this->hasMany(ChecklistFields::class, 'category_id');
    }

    public function taxPolicy()
    {
        return $this->belongsTo(TaxPolicy::class);
    }

    public function defaultTaxPolicy()
    {
        return $this->belongsTo(TaxPolicy::class, 'tax_policy_id');
    }

    public function listedItems()
    {
        return $this->hasMany(Inventory::class, 'inv_category_id')
            ->where(function ($query) {
                $query->where('status', 'forsale')
                    ->orWhere('status', 'commodity');
            });
    }

    public function department()
    {
        return $this->belongsTo(Department::class);
    }
}
