<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class MobilePayout extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'payout_number',
        'status',
        'step1_completed',
        'step2_completed',
        'step3_completed',
        'step_data',
        'invoice_id',
        'created_by',
        'completed_at',
    ];

    protected $casts = [
        'step_data' => 'array',
        'step1_completed' => 'boolean',
        'step2_completed' => 'boolean',
        'step3_completed' => 'boolean',
        'completed_at' => 'datetime',
    ];

    /**
     * Boot the model and auto-generate payout number
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($mobilePayout) {
            if (empty($mobilePayout->payout_number)) {
                $mobilePayout->payout_number = 'EZP-' . strtoupper(Str::random(8));
            }
        });
    }

    /**
     * Get the customer that owns the mobile payout
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the user who created the mobile payout
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the final invoice (if completed)
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get files associated with this mobile payout
     */
    public function files()
    {
        return $this->morphMany(\App\Models\File::class, 'fileable');
    }

    /**
     * Get step data for a specific step
     */
    public function getStepData($step)
    {
        return $this->step_data[$step] ?? [];
    }

    /**
     * Set step data for a specific step
     */
    public function setStepData($step, $data)
    {
        $stepData = $this->step_data ?? [];
        $stepData[$step] = $data;
        $this->step_data = $stepData;
    }

    /**
     * Mark a step as completed
     */
    public function markStepCompleted($step)
    {
        $this->{"step{$step}_completed"} = true;
        $this->save();
    }

    /**
     * Get the current step number (next incomplete step)
     */
    public function getCurrentStep()
    {
        if (!$this->step1_completed) return 1;
        if (!$this->step2_completed) return 2;
        if (!$this->step3_completed) return 3;
        return 3; // All steps completed
    }

    /**
     * Get progress percentage
     */
    public function getProgressPercentage()
    {
        $completedSteps = 0;
        if ($this->step1_completed) $completedSteps++;
        if ($this->step2_completed) $completedSteps++;
        if ($this->step3_completed) $completedSteps++;

        return ($completedSteps / 3) * 100;
    }

    /**
     * Check if all steps are completed
     */
    public function isCompleted()
    {
        return $this->step1_completed &&
               $this->step2_completed &&
               $this->step3_completed;
    }

    /**
     * Get formatted status
     */
    public function getFormattedStatus()
    {
        return match($this->status) {
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            default => ucfirst($this->status)
        };
    }

    /**
     * Get incomplete payouts for a specific user
     */
    public static function getIncompleteForUser($userId)
    {
        return static::where('created_by', $userId)
            ->where('status', 'in_progress')
            ->with('customer')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Check if this payout is older than the specified hours
     */
    public function isOlderThan($hours = 1)
    {
        return $this->created_at->lt(now()->subHours($hours));
    }

    /**
     * Delete old incomplete payouts for a user
     */
    public static function deleteOldIncompleteForUser($userId, $hours = 1)
    {
        $oldPayouts = static::where('created_by', $userId)
            ->where('status', 'in_progress')
            ->where('created_at', '<', now()->subHours($hours))
            ->get();

        $count = $oldPayouts->count();

        foreach ($oldPayouts as $payout) {
            // Delete associated files first (soft delete)
            $payout->files()->delete();
            // Delete the payout
            $payout->delete();
        }

        return $count;
    }

    /**
     * Get the next step URL for resuming
     */
    public function getResumeUrl()
    {
        $currentStep = $this->getCurrentStep();
        return route("mobile.payouts.step{$currentStep}", $this);
    }

    /**
     * Get a human-readable time since creation
     */
    public function getTimeSinceCreation()
    {
        return $this->created_at->diffForHumans();
    }
}
