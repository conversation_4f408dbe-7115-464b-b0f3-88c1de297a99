<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Signature extends Model
{
    use HasFactory;

    protected $fillable = [
        'certificate_id',
        'custody_event_id',
        'signatory_id',
        'signatory_name',
        'signatory_title',
        'signatory_organization',
        'signature_data',
        'signature_date',
        'ip_address',
        'role',
        'verification_code',
        'signature_image_path',
        'contract_text',
        'signature_type', // Add signature_type to track if it's drawn or typed
    ];

    protected $casts = [
        'signature_date' => 'datetime',
    ];

    // Signatory roles
    public const ROLES = [
        'client' => 'Client Representative',
        'driver' => 'Transport Driver',
        'warehouse' => 'Warehouse Receiver',
        'technician' => 'Certifying Technician',
        'witness' => 'Destruction Witness',
        'supervisor' => 'Supervisor',
    ];

    // Relationship with Certificate
    public function certificate()
    {
        return $this->belongsTo(Certificate::class);
    }

    // Relationship with CustodyEvent
    public function custodyEvent()
    {
        return $this->belongsTo(CustodyEvent::class);
    }

    // Polymorphic relationship with signatory
    public function signatory()
    {
        return $this->morphTo();
    }

    // Get role label
    public function getRoleLabel()
    {
        return self::ROLES[$this->role] ?? $this->role;
    }

    /**
     * Get the full URL for the signature image
     *
     * @return string|null
     */
    public function getSignatureImageUrl()
    {
        return $this->signature_image_path ? asset('storage/' . $this->signature_image_path) : null;
    }

    /**
     * Check if this signature has an image
     *
     * @return bool
     */
    public function hasSignatureImage()
    {
        return !empty($this->signature_image_path);
    }
}
