<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChecklistFields extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'name',
        'description',
        'type',
        'status',
        'options',
        'order',
    ];

    protected $casts = [
        'options' => 'array',
    ];
    

    public function category()
    {
        return $this->belongsTo(InventoryCategory::class, 'category_id');
    }

    


}
