<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'subject',
        'from_name',
        'from_email',
        'body_html',
        'available_variables',
        'is_active',
    ];

    protected $casts = [
        'available_variables' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Template definitions with their available variables
     */
    public static function templateDefinitions()
    {
        return [
            'daily_timeclock_report' => [
                'display_name' => 'Daily Timeclock Report',
                'description' => 'Daily report of employee time tracking sent to administrators',
                'variables' => [
                    '{report_date}' => 'The date of the report (formatted)',
                    '{total_hours_worked}' => 'Total hours worked by all employees',
                    '{total_break_hours}' => 'Total break hours taken by all employees',
                    '{employee_count}' => 'Number of employees who worked',
                    '{app_name}' => 'Application name',
                    '{current_date}' => 'Current date when email is sent',
                    '{contact_email}' => 'Contact/support email address',
                    '{employee_details}' => 'Table of employee time details (special block)',
                ],
            ],
            'pickup_request_notification' => [
                'display_name' => 'Pickup Request Notification',
                'description' => 'Notification sent when a new pickup request is submitted',
                'variables' => [
                    '{pickup_id}' => 'Pickup request ID',
                    '{status}' => 'Current status of the pickup request',
                    '{contact_name}' => 'Contact person name',
                    '{business_name}' => 'Business name (if applicable)',
                    '{email}' => 'Contact email address',
                    '{phone}' => 'Contact phone number',
                    '{pickup_date}' => 'Preferred pickup date',
                    '{pickup_address}' => 'Pickup location address',
                    '{load_size}' => 'Size of the load',
                    '{item_types}' => 'Types of items for pickup',
                    '{driver_instructions}' => 'Special instructions for driver',
                    '{view_url}' => 'URL to view the pickup request',
                    '{app_name}' => 'Application name',
                    '{notification_type}' => 'Type of notification (new/updated)',
                    '{contact_email}' => 'Contact/support email address',
                ],
            ],
            'pickup_confirmation' => [
                'display_name' => 'Pickup Confirmation',
                'description' => 'Confirmation email sent to customers when pickup is scheduled',
                'variables' => [
                    '{pickup_id}' => 'Pickup request ID',
                    '{contact_name}' => 'Contact person name',
                    '{business_name}' => 'Business name (if applicable)',
                    '{pickup_date}' => 'Scheduled pickup date and time',
                    '{pickup_address}' => 'Pickup location address',
                    '{driver_name}' => 'Name of assigned driver (if provided)',
                    '{driver_instructions}' => 'Special instructions for driver',
                    '{item_types}' => 'Types of items for pickup',
                    '{load_size}' => 'Size of the load',
                    '{management_url}' => 'URL for customer to manage their appointment',
                    '{app_name}' => 'Application name',
                    '{current_date}' => 'Current date when email is sent',
                    '{contact_email}' => 'Contact/support email address',
                ],
            ],
            'pickup_confirmation_reminder' => [
                'display_name' => 'Pickup Confirmation Reminder (48 Hours)',
                'description' => 'Reminder email sent 48 hours before pickup to prompt customer confirmation',
                'variables' => [
                    '{pickup_id}' => 'Pickup request ID',
                    '{contact_name}' => 'Contact person name',
                    '{business_name}' => 'Business name (if applicable)',
                    '{pickup_date}' => 'Scheduled pickup date and time',
                    '{pickup_address}' => 'Pickup location address',
                    '{driver_name}' => 'Name of assigned driver (if provided)',
                    '{driver_instructions}' => 'Special instructions for driver',
                    '{item_types}' => 'Types of items for pickup',
                    '{load_size}' => 'Size of the load',
                    '{management_url}' => 'URL for customer to manage their appointment',
                    '{confirm_url}' => 'Direct URL to confirm the appointment',
                    '{cancel_url}' => 'Direct URL to cancel the appointment',
                    '{app_name}' => 'Application name',
                    '{current_date}' => 'Current date when email is sent',
                    '{contact_email}' => 'Contact/support email address',
                    '{hours_until_pickup}' => 'Number of hours until scheduled pickup',
                ],
            ],
            'pickup_final_reminder' => [
                'display_name' => 'Pickup Final Reminder (48 Hours - Confirmed)',
                'description' => 'Final reminder email sent 48 hours before pickup to confirmed customers',
                'variables' => [
                    '{pickup_id}' => 'Pickup request ID',
                    '{contact_name}' => 'Contact person name',
                    '{business_name}' => 'Business name (if applicable)',
                    '{pickup_date}' => 'Scheduled pickup date and time',
                    '{pickup_address}' => 'Pickup location address',
                    '{driver_name}' => 'Name of assigned driver (if provided)',
                    '{driver_instructions}' => 'Special instructions for driver',
                    '{item_types}' => 'Types of items for pickup',
                    '{load_size}' => 'Size of the load',
                    '{management_url}' => 'URL for customer to manage their appointment',
                    '{cancel_url}' => 'Direct URL to cancel the appointment',
                    '{app_name}' => 'Application name',
                    '{current_date}' => 'Current date when email is sent',
                    '{contact_email}' => 'Contact/support email address',
                    '{hours_until_pickup}' => 'Number of hours until scheduled pickup',
                ],
            ],
            'form_submission_notification' => [
                'display_name' => 'Form Submission Notification',
                'description' => 'Notification sent when a form is submitted (basic version with link only)',
                'variables' => [
                    '{form_name}' => 'Name of the submitted form',
                    '{form_id}' => 'Form ID',
                    '{submission_id}' => 'Form submission ID',
                    '{submitter_name}' => 'Business name (for business) or full name (for residential)',
                    '{submitter_contact}' => 'Contact person name (business) or submitter name (residential)',
                    '{submitter_email}' => 'Email of the person who submitted the form',
                    '{submitter_phone}' => 'Phone number of the person who submitted the form',
                    '{submission_date}' => 'Date and time when the form was submitted',
                    '{view_url}' => 'URL to view the full submission details',
                    '{app_name}' => 'Application name',
                    '{current_date}' => 'Current date when email is sent',
                    '{contact_email}' => 'Contact/support email address',
                ],
            ],
            'form_submission_notification_full' => [
                'display_name' => 'Form Submission Notification (Full Data)',
                'description' => 'Notification sent when a form is submitted (includes all submitted data)',
                'variables' => [
                    '{form_name}' => 'Name of the submitted form',
                    '{form_id}' => 'Form ID',
                    '{submission_id}' => 'Form submission ID',
                    '{submitter_name}' => 'Business name (for business) or full name (for residential)',
                    '{submitter_contact}' => 'Contact person name (business) or submitter name (residential)',
                    '{submitter_email}' => 'Email of the person who submitted the form',
                    '{submitter_phone}' => 'Phone number of the person who submitted the form',
                    '{submission_date}' => 'Date and time when the form was submitted',
                    '{form_data_table}' => 'Table containing all submitted form data (special block)',
                    '{view_url}' => 'URL to view the full submission details',
                    '{app_name}' => 'Application name',
                    '{current_date}' => 'Current date when email is sent',
                    '{contact_email}' => 'Contact/support email address',
                ],
            ],
            'form_submission_pdf_delivery' => [
                'display_name' => 'Form Submission PDF Delivery',
                'description' => 'Email sent to customer with form submission PDF attachment',
                'variables' => [
                    '{form_name}' => 'Name of the submitted form',
                    '{form_id}' => 'Form ID',
                    '{submission_id}' => 'Form submission ID',
                    '{submitter_name}' => 'Business name (for business) or full name (for residential)',
                    '{submitter_contact}' => 'Contact person name (business) or submitter name (residential)',
                    '{submitter_email}' => 'Email of the person who submitted the form',
                    '{submission_date}' => 'Date and time when the form was submitted',
                    '{approval_date}' => 'Date and time when the form was approved',
                    '{approved_by}' => 'Name of the person who approved the submission',
                    '{customer_name}' => 'Name of the linked customer (if any)',
                    '{app_name}' => 'Application name',
                    '{current_date}' => 'Current date when email is sent',
                    '{contact_email}' => 'Contact/support email address',
                ],
            ],
            'pickup_cancellation' => [
                'display_name' => 'Pickup Cancellation',
                'description' => 'Email sent to customer when their pickup appointment is cancelled',
                'variables' => [
                    '{pickup_id}' => 'Pickup request ID',
                    '{contact_name}' => 'Contact person name',
                    '{business_name}' => 'Business name (if applicable)',
                    '{pickup_date}' => 'Original scheduled pickup date and time',
                    '{pickup_address}' => 'Pickup location address',
                    '{cancellation_reason}' => 'Reason for cancellation',
                    '{cancelled_by}' => 'Who cancelled the appointment (customer/staff)',
                    '{cancellation_date}' => 'Date and time when cancelled',
                    '{app_name}' => 'Application name',
                    '{current_date}' => 'Current date when email is sent',
                    '{contact_email}' => 'Contact/support email address',
                ],
            ],
        ];
    }

    /**
     * Get the template definition for this template
     */
    public function getDefinition()
    {
        $definitions = self::templateDefinitions();
        return $definitions[$this->name] ?? null;
    }

    /**
     * Get available variables for this template
     */
    public function getAvailableVariablesAttribute($value)
    {
        // If we have stored variables, use them
        if ($value) {
            return json_decode($value, true);
        }

        // Otherwise, get from template definition
        $definition = $this->getDefinition();
        return $definition['variables'] ?? [];
    }

    /**
     * Find a template by name
     */
    public static function findByName($name)
    {
        return self::where('name', $name)->where('is_active', true)->first();
    }

    /**
     * Render the template with given data
     */
    public function render($data = [])
    {
        $subject = $this->subject;
        $body = $this->body_html;

        // Replace variables in subject and body
        foreach ($data as $key => $value) {
            $placeholder = '{' . $key . '}';
            
            // Handle special cases for arrays/objects
            if (is_array($value) || is_object($value)) {
                continue; // Skip complex data types for simple replacement
            }
            
            $subject = str_replace($placeholder, $value, $subject);
            $body = str_replace($placeholder, $value, $body);
        }

        return [
            'subject' => $subject,
            'body' => $body,
            'from_name' => $this->from_name,
            'from_email' => $this->from_email,
        ];
    }
}