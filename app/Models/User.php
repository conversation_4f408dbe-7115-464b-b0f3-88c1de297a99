<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Carbon\Carbon;

use Illuminate\Support\Facades\Log;

class User extends Authenticatable
{
    use HasApiTokens;

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;
    use HasProfilePhoto;
    use Notifiable;
    use TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'enabled',
        'profile_photo_id',
        // Emergency contact information
        'emergency_contact_name',
        'emergency_contact_relationship',
        'emergency_contact_phone',
        'additional_emergency_contacts',
        // Health information (optional)
        'allergies',
        'medications',
        'medical_conditions',
        'blood_type',
        'additional_health_info',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    public function isUser()
    {
        return $this->role === 'user';
    }

    public function groups()
    {
        return $this->belongsToMany(UserGroup::class, 'user_group_user', 'user_id', 'user_group_id')
            ->withTimestamps();
    }


    public function hasPermission($permission)
    {
        return $this->groups()->whereHas('permissions', function ($query) use ($permission) {
            $query->where('name', $permission);
        })->exists();
    }

    public function tasks()
    /**
     * Get the tasks created by the user.
     */
    {
        return $this->hasMany(Task::class, 'created_by');
    }

    public function assignedTasks()
    /**
     * Get the tasks assigned to the user.
     */
    {
        return $this->hasManyThrough(Task::class, TaskAssignee::class, 'assignee_id', 'id', 'id', 'task_id')
            ->where('assignee_type', 'user');
    }


    public function preferences()
    {
        return $this->hasMany(UserPreference::class);
    }


    public function getTaskOccurrences($startDate = null, $endDate = null)
    {
        $startDate = $startDate ? Carbon::parse($startDate) : now();
        $endDate = $endDate ? Carbon::parse($endDate) : now()->addDays(7);

        $tasks = Task::whereHas('assignees', function ($query) {
            $query->where('assignee_type', 'user')
                ->where('assignee_id', $this->id);
        })
            ->where(function ($query) use ($startDate, $endDate) {
                $query->whereNull('start_date')->orWhere('start_date', '<=', $endDate);
            })
            ->where(function ($query) use ($startDate) {
                $query->whereNull('end_date')->orWhere('end_date', '>=', $startDate);
            })
            ->with(['recurrences', 'completions' => function ($query) {
                $query->where('user_id', $this->id);
            }])
            ->get();

        $taskOccurrences = [];

        foreach ($tasks as $task) {
            $recurrence = $task->recurrences;
            $dates = [];

            // Determine task dates based on recurrence
            if ($recurrence) {
                $taskStartDate = $task->start_date ? $task->start_date->copy() : $startDate->copy();
                if ($recurrence->repeat_interval === 'daily') {
                    for ($date = $taskStartDate; $date <= $endDate; $date->addDay()) {
                        if ($date >= $startDate) {
                            $dates[] = $date->toDateString();
                        }
                    }
                } elseif ($recurrence->repeat_interval === 'weekly') {
                    $repeatDays = $recurrence->repeat_days ?? [];
                    for ($date = $taskStartDate; $date <= $endDate; $date->addDay()) {
                        if (in_array($date->format('l'), $repeatDays) && $date >= $startDate) {
                            $dates[] = $date->toDateString();
                        }
                    }
                } elseif ($recurrence->repeat_interval === 'custom') {
                    $repeatDays = $recurrence->repeat_days ?? [];
                    for ($date = $taskStartDate; $date <= $endDate; $date->addDay()) {
                        if (in_array($date->format('l'), $repeatDays) && $date >= $startDate) {
                            $dates[] = $date->toDateString();
                        }
                    }
                } elseif ($recurrence->repeat_interval === 'once') {
                    if ($task->end_date && $task->end_date >= $startDate && $task->end_date <= $endDate) {
                        $dates[] = $task->end_date->toDateString();
                    }
                }
            } else {
                $dateKey = $task->end_date ? $task->end_date->toDateString() : $startDate->toDateString();
                if ($dateKey >= $startDate->toDateString() && $dateKey <= $endDate->toDateString()) {
                    $dates[] = $dateKey;
                }
            }

            // Process each date and add tasks to occurrences
            foreach ($dates as $date) {
                $completedCount = $task->completions->where('status', 'complete')->where('due_date', $date)->count();
                $taskOccurrences[$date][] = [
                    'task_id' => $task->id,
                    'title' => $task->title,
                    'description' => $task->description,
                    'due_time' => $recurrence->due_time ?? 'Any time',
                    'completed_count' => $completedCount,
                    'total_count' => 1,
                ];
            }
        }

        // Format JSON output with totals
        $formattedOccurrences = [];
        foreach ($taskOccurrences as $date => $tasks) {
            $totalComplete = array_sum(array_column($tasks, 'completed_count'));
            $totalTasks = array_sum(array_column($tasks, 'total_count'));
            $formattedOccurrences[$date] = [
                'total_complete' => $totalComplete,
                'total_tasks' => $totalTasks,
                'tasks' => $tasks,
            ];
        }

        // Sort by date
        ksort($formattedOccurrences);

        return $formattedOccurrences;
    }




    public function getDailyTaskSummary($date = null)
    {
        $date = $date ? \Carbon\Carbon::parse($date) : now();
        $currentTime = now()->format('H:i:s');

        // Get the task occurrences for the specified date
        $taskOccurrences = $this->getTaskOccurrences($date->toDateString(), $date->toDateString());

        $totalTasks = 0;
        $completedTasks = 0;
        $overdueTasks = 0;

        // Check if data exists for the specific date
        if (isset($taskOccurrences[$date->toDateString()])) {
            $dayOccurrences = $taskOccurrences[$date->toDateString()];

            // Accumulate totals and completed counts
            $totalTasks += $dayOccurrences['total_tasks'] ?? 0;
            $completedTasks += $dayOccurrences['total_complete'] ?? 0;

            // Calculate overdue tasks
            foreach ($dayOccurrences['tasks'] as $task) {
                if ($task['due_time'] !== 'Any time' && $task['due_time'] < $currentTime && $task['completed_count'] == 0) {
                    $overdueTasks++;
                }
            }
        }

        return [
            'completed' => $completedTasks,
            'total' => $totalTasks,
            'overdue' => $overdueTasks,
        ];
    }

    public function certifiedDestructions()
    {
        return $this->hasMany(Certificate::class, 'certifying_user_id');
    }

    public function custodyEvents()
    {
        return $this->hasMany(CustodyEvent::class);
    }

    public function signatures()
    {
        return $this->morphMany(Signature::class, 'signatory');
    }

    /**
     * Get the user's profile photo.
     */
    public function profilePhoto()
    {
        return $this->belongsTo(Image::class, 'profile_photo_id');
    }

    /**
     * Get the URL of the user's profile photo.
     *
     * @return string
     */
    public function getProfilePhotoUrlAttribute()
    {
        if ($this->profilePhoto) {
            return $this->profilePhoto->getImageSrc('sm');
        }

        return $this->defaultProfilePhotoUrl();
    }

    /**
     * Get the default profile photo URL if no profile photo has been uploaded.
     *
     * @return string
     */
    protected function defaultProfilePhotoUrl()
    {
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->name) . '&color=7F9CF5&background=EBF4FF';
    }

    public function hasCertificationPermission()
    {
        return $this->hasPermission('certify_destruction');
    }

    /**
     * Get the calendars owned by the user.
     */
    public function ownedCalendars()
    {
        return $this->hasMany(Calendar::class, 'owner_id');
    }

    /**
     * Get the calendars created by the user.
     */
    public function createdCalendars()
    {
        return $this->hasMany(Calendar::class, 'creator_id');
    }

    /**
     * Get the calendars shared with the user.
     */
    public function sharedCalendars()
    {
        return $this->belongsToMany(Calendar::class, 'calendar_shares')
            ->withPivot('permission', 'shared_by')
            ->withTimestamps();
    }

    /**
     * Get all calendars accessible to the user (owned, created, or shared).
     */
    public function accessibleCalendars()
    {
        return Calendar::where(function ($query) {
            $query->where('owner_id', $this->id)
                  ->orWhere('creator_id', $this->id)
                  ->orWhereHas('shares', function ($q) {
                      $q->where('user_id', $this->id);
                  });
        });
    }

    /**
     * Get the events created by the user.
     */
    public function createdEvents()
    {
        return $this->hasMany(Event::class, 'creator_id');
    }

    /**
     * Get the event categories created by the user.
     */
    public function eventCategories()
    {
        return $this->hasMany(EventCategory::class, 'creator_id');
    }

    /**
     * Get the time cards for the user.
     */
    public function timeCards()
    {
        return $this->hasMany(TimeCard::class);
    }

    /**
     * Get the time punches for the user.
     */
    public function timePunches()
    {
        return $this->hasMany(TimePunch::class);
    }

    /**
     * Get the Microsoft calendar integration for the user.
     */
    public function microsoftCalendarIntegration()
    {
        return $this->hasOne(MicrosoftCalendarIntegration::class);
    }

    /**
     * Get the current time card for today.
     */
    public function getTodayTimeCard()
    {
        $today = now()->format('Y-m-d');
        return $this->timeCards()->firstOrCreate(
            ['date' => $today],
            [
                'total_hours' => '00:00:00',
                'total_break_hours' => '00:00:00',
                'total_sick_time' => '00:00:00',
                'total_vacation_time' => '00:00:00'
            ]
        );
    }

    /**
     * Get or create a time card for a specific date.
     */
    public function getTimeCardForDate($date)
    {
        $formattedDate = Carbon::parse($date)->format('Y-m-d');
        return $this->timeCards()->firstOrCreate(
            ['date' => $formattedDate],
            [
                'total_hours' => '00:00:00',
                'total_break_hours' => '00:00:00',
                'total_sick_time' => '00:00:00',
                'total_vacation_time' => '00:00:00'
            ]
        );
    }

    /**
     * Ensure time cards exist for the entire week.
     * This helps make sure we have time cards for all days of the week,
     * even if the user hasn't clocked in on those days.
     */
    public function ensureWeekTimeCards()
    {
        // Explicitly set Sunday as start of week (0 = Sunday, 1 = Monday, etc.)
        $startDate = now()->startOfWeek(Carbon::SUNDAY);
        $endDate = now()->endOfWeek(Carbon::SUNDAY);

        Log::info("Ensuring time cards exist for week", [
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'user_id' => $this->id
        ]);

        // Create a time card for each day of the week if it doesn't exist
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $timeCard = $this->getTimeCardForDate($date);

            // If this is not today's card, make sure the hours are calculated correctly
            if (!$date->isToday()) {
                // Only recalculate if the card has punches
                if ($timeCard->punches()->count() > 0) {
                    Log::info("Recalculating hours for time card", [
                        'date' => $date->format('Y-m-d'),
                        'id' => $timeCard->id,
                        'user_id' => $this->id
                    ]);

                    // Force a recalculation of hours
                    $timeCard->calculateHours(true);
                    $timeCard->refresh();
                }
            }
        }
    }

    /**
     * Check if the user is currently clocked in.
     */
    public function isClockedIn()
    {
        $timeCard = $this->getTodayTimeCard();
        return $timeCard->isClockedIn();
    }

    /**
     * Check if the user is currently on break.
     */
    public function isOnBreak()
    {
        $timeCard = $this->getTodayTimeCard();
        return $timeCard->isOnBreak();
    }

    /**
     * Get the weekly time card summary.
     */
    public function getWeeklyTimeCardSummary()
    {
        $timeCalculationService = app(\App\Services\TimeCalculationService::class);
        return $timeCalculationService->calculateWeeklyTimeSummary($this);
    }

    /**
     * Convert HH:MM:SS to seconds
     */
    private function timeStringToSeconds($timeString)
    {
        if (empty($timeString)) {
            return 0;
        }

        $parts = explode(':', $timeString);

        if (count($parts) !== 3) {
            return 0;
        }

        $hours = (int)$parts[0];
        $minutes = (int)$parts[1];
        $seconds = (int)$parts[2];

        return $hours * 3600 + $minutes * 60 + $seconds;
    }

    /**
     * Format seconds to HH:MM
     */
    private function formatSecondsToHoursMinutes($seconds)
    {
        // Ensure seconds is a positive number
        $seconds = abs($seconds);

        // Calculate hours and minutes
        $hours = floor($seconds / 3600);
        $minutes = round(($seconds % 3600) / 60);

        // Handle case where minutes round to 60
        if ($minutes == 60) {
            $hours++;
            $minutes = 0;
        }

        // Format with leading zeros
        return sprintf('%02d:%02d', $hours, $minutes);
    }

    /**
     * Format hours as HH:MM:SS
     */
    public function formatHours($hours)
    {
        // Ensure hours is a positive number
        $hours = abs($hours);

        // Calculate hours, minutes, and seconds
        $wholeHours = floor($hours);
        $decimalMinutes = ($hours - $wholeHours) * 60;
        $minutes = floor($decimalMinutes);
        $seconds = round(($decimalMinutes - $minutes) * 60);

        // Handle case where seconds round to 60
        if ($seconds == 60) {
            $minutes++;
            $seconds = 0;
        }

        // Handle case where minutes round to 60
        if ($minutes == 60) {
            $wholeHours++;
            $minutes = 0;
        }

        // Format with leading zeros
        return sprintf('%02d:%02d:%02d', $wholeHours, $minutes, $seconds);
    }

    /**
     * Format hours as HH:MM (no seconds)
     */
    public function formatHoursMinutes($hours)
    {
        // Ensure hours is a positive number
        $hours = abs($hours);

        // Calculate hours and minutes
        $wholeHours = floor($hours);
        $decimalMinutes = ($hours - $wholeHours) * 60;
        $minutes = round($decimalMinutes);

        // Handle case where minutes round to 60
        if ($minutes == 60) {
            $wholeHours++;
            $minutes = 0;
        }

        // Format with leading zeros
        return sprintf('%02d:%02d', $wholeHours, $minutes);
    }

    /**
     * Format seconds to HH:MM:SS time string
     */
    public function formatSecondsToTime($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $secs);
    }

    /**
     * Get the notification subscriptions for the user.
     */
    public function notificationSubscriptions()
    {
        return $this->hasMany(NotificationSubscription::class);
    }

    /**
     * Get the active notification subscriptions for the user.
     */
    public function activeNotificationSubscriptions()
    {
        return $this->hasMany(NotificationSubscription::class)->active();
    }
}
