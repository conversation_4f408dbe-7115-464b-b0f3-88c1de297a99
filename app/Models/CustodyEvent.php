<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustodyEvent extends Model
{
    use HasFactory;

    protected $fillable = [
        'certificate_id',
        'event_type',
        'event_date',
        'user_id',
        'location',
        'notes',
        'previous_event_id',
    ];

    protected $casts = [
        'event_date' => 'datetime',
    ];

    // Event types
    public const EVENT_TYPES = [
        'created' => 'Certificate Created',
        'client_handover' => 'Client Handover',
        'transport_pickup' => 'Transport Pickup',
        'warehouse_receipt' => 'Warehouse Receipt',
        'secure_storage' => 'Secure Storage',
        'destruction_prep' => 'Destruction Preparation',
        'destruction' => 'Destruction Completed',
        'certificate_issued' => 'Certificate Issued',
    ];

    // Relationship with Certificate
    public function certificate()
    {
        return $this->belongsTo(Certificate::class);
    }

    // Relationship with User
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Relationship with Signature
    public function signature()
    {
        return $this->hasOne(Signature::class);
    }

    // Self-referential relationship to track event sequence
    public function previousEvent()
    {
        return $this->belongsTo(CustodyEvent::class, 'previous_event_id');
    }

    public function nextEvent()
    {
        return $this->hasOne(CustodyEvent::class, 'previous_event_id');
    }

    // Get event type label
    public function getEventTypeLabel()
    {
        return self::EVENT_TYPES[$this->event_type] ?? $this->event_type;
    }
}
