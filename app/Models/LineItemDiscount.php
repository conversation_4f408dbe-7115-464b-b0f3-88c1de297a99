<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LineItemDiscount extends Model
{
    use HasFactory;

    protected $fillable = [
        'line_item_id',
        'discount_id',
        'discount_amount',
    ];

    public function lineItem()
    {
        return $this->belongsTo(LineItem::class);
    }

    public function discount()
    {
        return $this->belongsTo(Discount::class);
    }
}
