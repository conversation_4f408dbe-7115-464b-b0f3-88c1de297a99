<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Task extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'due_type',
        'type',
        'status', // Default status if needed
        'created_by',
        'start_date',
        'end_date',
    ];

    protected $casts = [
        'start_date' => 'date', 
        'end_date' => 'date', 
    ];

    

    // Relationships
    public function recurrences()
    {
        return $this->hasOne(TaskRecurrence::class);
    }

    public function completions()
    {
        return $this->hasMany(TaskCompletion::class);
    }

    public function assignees()
    {
        return $this->hasMany(TaskAssignee::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Dynamic check for recurrence
    public function isRecurring()
    {
        return $this->recurrences()->exists();
    }


    
}
