<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\LogsActivity;

class Certificate extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'certificate_number',
        'customer_id',
        'certifying_user_id',
        'status',
        'scheduled_destruction_date',
        'actual_destruction_date',
        'destruction_method',
        'destruction_location',
        'notes',
        'manifest_path',
        'pickup_dropoff_date',
        'stats', // Add stats to the fillable array
        'client_manifest_drive_count',
        'client_manifest_device_count',
        'etech_verified_drive_count',
        'etech_verified_device_count',
        'service_selection',
    ];

    protected $casts = [
        'scheduled_destruction_date' => 'datetime',
        'actual_destruction_date' => 'datetime',
        'pickup_dropoff_date' => 'datetime',
        'stats' => 'array', // Cast stats as JSON
    ];

    /**
     * Events to log for this model
     */
    protected $loggedEvents = ['created', 'updated', 'deleted', 'restored'];

    /**
     * Fields to exclude from activity logs
     */
    protected $excludeFromLogs = [
        'updated_at', // Already tracked by log timestamp
        'stats', // Complex array data not meaningful in change logs
    ];

    /**
     * Boot the model and set up event listeners
     */
    protected static function boot()
    {
        parent::boot();

        // Automatically generate certificate number and set default stats after creation
        static::created(function ($certificate) {
            $needsSave = false;
            
            if (empty($certificate->certificate_number)) {
                $certificate->certificate_number = self::generateCertificateNumber($certificate->id);
                $needsSave = true;
            }
            
            // Initialize default warehouse stats if not provided
            $stats = $certificate->stats ?? [];
            if (!isset($stats['warehouse_manifest_provided'])) {
                $stats['warehouse_manifest_provided'] = 'No manifest provided';
                $needsSave = true;
            }
            if (!isset($stats['warehouse_device_count'])) {
                $stats['warehouse_device_count'] = 'Devices not counted on receipt';
                $needsSave = true;
            }
            if (!isset($stats['warehouse_drive_count'])) {
                $stats['warehouse_drive_count'] = 'Drives not counted on receipt';
                $needsSave = true;
            }
            
            if ($needsSave) {
                $certificate->stats = $stats;
                $certificate->saveQuietly(); // Use saveQuietly to avoid triggering events again
            }
        });
    }

    /**
     * Get the component name for activity logs
     */
    protected function getActivityLogComponent(): string
    {
        return 'chain_of_custody';
    }

    /**
     * Override the activity logging to include certificate context
     */
    public function logActivity(string $eventType, string $description, array $additionalData = []): \App\Models\ActivityLog
    {
        // Add certificate context to additional data
        $additionalData = array_merge($additionalData, [
            'certificate_number' => $this->certificate_number,
            'customer_name' => $this->customer?->name,
            'device_count' => $this->devices()->count(),
        ]);

        return app(\App\Services\ActivityLoggerService::class)->logCustom(
            component: $this->getActivityLogComponent(),
            eventType: $eventType,
            description: $description,
            model: $this,
            additionalData: $additionalData
        );
    }

    // Relationship with Customer
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    // Relationship with User (certifying technician)
    public function certifyingTechnician()
    {
        return $this->belongsTo(User::class, 'certifying_user_id');
    }

    // Relationship with Devices
    public function devices()
    {
        return $this->hasMany(Device::class);
    }

    // Relationship with CustodyEvents
    public function custodyEvents()
    {
        return $this->hasMany(CustodyEvent::class);
    }

    // Relationship with Signatures
    public function signatures()
    {
        return $this->hasMany(Signature::class);
    }

    // Polymorphic relationship with files
    public function files()
    {
        return $this->morphMany(File::class, 'fileable');
    }

    // Helper method to get the current custody status
    public function getCurrentStatus()
    {
        return $this->custodyEvents()->latest()->first()?->event_type ?? 'created';
    }

    /**
     * Generate certificate number based on the record ID
     *
     * @param int $id The certificate ID
     * @return string
     */
    public static function generateCertificateNumber($id)
    {
        return 'COC-' . str_pad($id, 5, '0', STR_PAD_LEFT);
    }

    // Helper method to check if certificate is complete
    public function isComplete()
    {
        return $this->status === 'completed' && $this->actual_destruction_date !== null;
    }

    // Status constants
    public const STATUSES = [
        'pending' => 'Pending',
        'verifying' => 'Verifying',
        'completed' => 'Complete'
    ];

    // Get formatted status
    public function getFormattedStatus()
    {
        return self::STATUSES[$this->status] ?? ucfirst($this->status);
    }
}
