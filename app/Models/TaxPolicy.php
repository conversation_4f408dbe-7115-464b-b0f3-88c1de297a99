<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class TaxPolicy extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['name', 'rate', 'description'];

    // Prevent 'rate' from being updated
    public function setRateAttribute($value)
    {
        if ($this->exists) {
            throw new \Exception('The tax rate cannot be changed after creation.');
        }

        $this->attributes['rate'] = $value;
    }
}
