<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Jobs\SyncPickupEventsToMicrosoft;
use App\Models\MicrosoftCalendarIntegration;
use Illuminate\Support\Facades\Log;

class Event extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * Boot the model and set up event listeners for Microsoft sync.
     */
    protected static function boot()
    {
        parent::boot();

        // Sync to Microsoft when pickup events are created
        static::created(function (Event $event) {
            if ($event->is_pickup_event) {
                static::queueMicrosoftSync($event, 'created');
            }
        });

        // Sync to Microsoft when pickup events are updated
        static::updated(function (Event $event) {
            if ($event->is_pickup_event) {
                // Check if any relevant fields were changed
                $relevantFields = [
                    'title', 'description', 'start_date', 'end_date', 'all_day',
                    'location', 'pickup_address', 'pickup_items', 'staff_needed',
                    'assigned_driver_id', 'contact_name', 'contact_phone', 'contact_email',
                    'pickup_details', 'is_active'
                ];

                $hasRelevantChanges = false;
                foreach ($relevantFields as $field) {
                    if ($event->isDirty($field)) {
                        $hasRelevantChanges = true;
                        break;
                    }
                }

                if ($hasRelevantChanges) {
                    static::queueMicrosoftSync($event, 'updated');
                }
            }
        });

        // Sync to Microsoft when pickup events are deleted
        static::deleted(function (Event $event) {
            if ($event->is_pickup_event) {
                static::queueMicrosoftSync($event, 'deleted');
            }
        });
    }

    /**
     * Queue Microsoft sync for the event.
     */
    protected static function queueMicrosoftSync(Event $event, string $action)
    {
        try {
            // Only sync if there is an active shared calendar integration
            if (MicrosoftCalendarIntegration::hasActiveSharedCalendar()) {
                Log::info('Queueing Microsoft sync for pickup event to shared calendar', [
                    'event_id' => $event->id,
                    'action' => $action,
                    'title' => $event->title
                ]);

                // Queue the sync job for the shared calendar
                SyncPickupEventsToMicrosoft::forEvent($event)->dispatch();
            }
        } catch (\Exception $e) {
            Log::error('Failed to queue Microsoft sync for pickup event', [
                'event_id' => $event->id,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'calendar_id',
        'title',
        'description',
        'start_date',
        'end_date',
        'all_day',
        'creator_id',
        'recurrence_pattern_id',
        'location',
        'color',
        'is_active',
        // Event type flags
        'is_pickup_event',
        'is_blockout_event',
        'customer_id',
        // Pickup-specific fields
        'pickup_address',
        'pickup_items',
        'staff_needed',
        'assigned_driver_id',
        'contact_name',
        'contact_phone',
        'contact_email',

        'pickup_details',
        'microsoft_event_ids',
        'microsoft_event_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'all_day' => 'boolean',
        'is_active' => 'boolean',
        'is_pickup_event' => 'boolean',
        'is_blockout_event' => 'boolean',
        'pickup_details' => 'array',
        'microsoft_event_ids' => 'array',
    ];

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(\DateTimeInterface $date)
    {
        // Format with timezone information to ensure proper UTC handling
        return $date->format('Y-m-d\TH:i:s.u\Z');
    }

    /**
     * Get the calendar that owns the event.
     */
    public function calendar(): BelongsTo
    {
        return $this->belongsTo(Calendar::class);
    }

    /**
     * Get the creator of the event.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * Get the recurrence pattern for the event.
     */
    public function recurrencePattern(): BelongsTo
    {
        return $this->belongsTo(RecurrencePattern::class);
    }

    /**
     * Get the categories for the event.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(EventCategory::class, 'event_event_category');
    }

    /**
     * Get the customer for pickup events.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the pickup request associated with this pickup event.
     */
    public function pickupRequest(): HasOne
    {
        return $this->hasOne(PickupRequest::class, 'event_id', 'id');
    }

    /**
     * Get the assigned driver for pickup events.
     */
    public function assignedDriver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_driver_id');
    }

    /**
     * Scope a query to only include pickup events.
     */
    public function scopePickupEvents($query)
    {
        return $query->where('is_pickup_event', true);
    }

    /**
     * Scope a query to only include blockout events.
     */
    public function scopeBlockoutEvents($query)
    {
        return $query->where('is_blockout_event', true);
    }

    /**
     * Scope a query to only include events within a date range.
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->where(function ($q) use ($startDate, $endDate) {
            // Events that start within the range
            $q->whereBetween('start_date', [$startDate, $endDate])
              // Or events that end within the range
              ->orWhereBetween('end_date', [$startDate, $endDate])
              // Or events that span the entire range
              ->orWhere(function ($q2) use ($startDate, $endDate) {
                  $q2->where('start_date', '<=', $startDate)
                     ->where('end_date', '>=', $endDate);
              });
        });
    }

    /**
     * Get pickup details from the linked pickup request.
     */
    public function getPickupDetailsArray(): array
    {
        // If this is a pickup event with a linked pickup request, use that data
        if ($this->is_pickup_event && $this->pickupRequest) {
            return $this->pickupRequest->getPickupDetailsArray();
        }

        // Return empty array for non-pickup events or pickup events without linked requests
        return [];
    }

    /**
     * Get a formatted summary of pickup details for display.
     */
    public function getPickupSummary(): string
    {
        // If this is a pickup event with a linked pickup request, use that data
        if ($this->is_pickup_event && $this->pickupRequest) {
            return $this->pickupRequest->getPickupSummary();
        }

        // Return empty string for non-pickup events or pickup events without linked requests
        return '';
    }

    /**
     * Set pickup details from a pickup request with additional data.
     */
    public function setPickupDetailsFromRequest(PickupRequest $pickupRequest, array $additionalDetails = []): void
    {
        // Get the pickup request details
        $pickupDetails = $pickupRequest->getPickupDetailsArray();

        // Merge with additional details
        $combinedDetails = array_merge_recursive($pickupDetails, $additionalDetails);

        // Store in the pickup_details JSON field
        $this->pickup_details = $combinedDetails;
        $this->save();
    }
}
