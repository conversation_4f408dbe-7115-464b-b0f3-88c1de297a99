<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CertificatePdf extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'certificate_id',
        'file_path',
        'file_name',
        'generated_by',
        'version',
        'device_count',
        'is_current',
    ];

    protected $casts = [
        'is_current' => 'boolean',
        'device_count' => 'integer',
    ];

    /**
     * Get the certificate that owns the PDF.
     */
    public function certificate()
    {
        return $this->belongsTo(Certificate::class);
    }

    /**
     * Get the user who generated the PDF.
     */
    public function generatedBy()
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    /**
     * Get the full URL for the PDF file
     *
     * @return string|null
     */
    public function getFileUrl()
    {
        return $this->file_path ? asset('storage/' . $this->file_path) : null;
    }

    /**
     * Mark this PDF as the current version for the certificate
     */
    public function markAsCurrent()
    {
        // First, mark all other PDFs for this certificate as not current
        self::where('certificate_id', $this->certificate_id)
            ->where('id', '!=', $this->id)
            ->update(['is_current' => false]);

        // Then mark this one as current
        $this->is_current = true;
        $this->save();

        return $this;
    }

    /**
     * Get the current PDF for a certificate
     *
     * @param int $certificateId
     * @return CertificatePdf|null
     */
    public static function getCurrentForCertificate($certificateId)
    {
        return self::where('certificate_id', $certificateId)
            ->where('is_current', true)
            ->first();
    }

    /**
     * Get all PDFs for a certificate
     *
     * @param int $certificateId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getAllForCertificate($certificateId)
    {
        return self::where('certificate_id', $certificateId)
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
