<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationSubscription extends Model
{
    protected $fillable = [
        'user_id',
        'endpoint',
        'public_key',
        'auth_token',
        'device_info',
        'is_active',
    ];

    protected $casts = [
        'device_info' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user that owns this subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get only active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get subscriptions for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Mark the subscription as inactive.
     */
    public function deactivate()
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Mark the subscription as active.
     */
    public function activate()
    {
        $this->update(['is_active' => true]);
    }
}
