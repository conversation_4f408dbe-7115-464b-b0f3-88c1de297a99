<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PickupRequestImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'pickup_request_id',
        'image_id',
        'sort_order',
    ];

    public function pickupRequest()
    {
        return $this->belongsTo(PickupRequest::class);
    }

    public function image()
    {
        return $this->belongsTo(Image::class);
    }
}
