<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Inventory extends Model
{
    use HasFactory, SoftDeletes;

    // Statuses for the inventory
    public const STATUS = [
        'intake' => 'Intake',
        'commodity' => 'Commodity',
        'refurbishing' => 'Refurbishing',
        'cleaning' => 'Cleaning',
        'ready-to-list' => 'Ready to List',
        'forsale' => 'For Sale',
        'sold' => 'Sold',
        'scrap' => 'Scrap',
    ];

    public const UNITS = [
        'each' => 'Each',
        'box' => 'Box',
        'pallet' => 'Pallet',
        'lb' => 'Pound',
        'oz' => 'Ounce',
        'g' => 'Gram',
        'kg' => 'Kilogram',
        'ton' => 'Ton',
        'ft' => 'Foot',
        'in' => 'Inch',
        'cm' => 'Centimeter',
        'm' => 'Meter',
        'sqft' => 'Square Foot',
        'sqm' => 'Square Meter',
        'cuft' => 'Cubic Foot',
        'cuyd' => 'Cubic Yard',
        'gal' => 'Gallon',
        'l' => 'Liter',
        'ml' => 'Milliliter',
        'bbl' => 'Barrel',
    ];

    // Fillable fields for mass assignment
    protected $fillable = [
        'name',
        'description',
        'status',
        'technician_id',
        'inv_category_id',
        'location',
        'suggested_price',
        'quantity',
        'unit_price',
        'unit',
        'weight',
        'condition',
        'asset_tag',
        'generated_description',
        'generated_title',
    ];

    /**
     * Relationship with the User (technician).
     */
    public function technician()
    {
        return $this->belongsTo(User::class, 'technician_id');
    }

    public function assetTag()
    {
        return $this->asset_tag;
    }

    /** Some Accessors  */
    public function getCustomerAttribute()
    {
        return $this->lineItems->first()?->invoice?->customer ?? null;
    }

    /**
     * Get the sold date (from the first line item).
     */
    public function getSoldDateAttribute()
    {
        return $this->lineItems->first()?->created_at ?? null;
    }

    /**
     * Get the sold price (from the first line item).
     */
    public function getSoldPriceAttribute()
    {
        return $this->lineItems->first()?->price;
    }

    /**
     * Get the sold quantity (from the first line item).
     */
    public function getSoldQuantityAttribute()
    {
        return $this->lineItems->first()?->quantity;
    }

    public function getIndividualSoldItemInvoice(){
        return $this->lineItems->first()?->invoice;
    }

    /**
     * Relationship with the InventoryCategory.
     */
    public function category()
    {
        return $this->belongsTo(InventoryCategory::class, 'inv_category_id');
    }

    /**
     * Relationship with checklist items.
     */
    public function checklistItems()
    {
        return $this->hasMany(InventoryChecklistItems::class);
    }

    /**
     * Relationship with checklist fields via category.
     */
    public function checklistFields()
    {
        return $this->category->checklistFields;
    }

    /**
     * Relationship with inventory images.
     */
    public function images()
    {
        return $this->hasMany(InventoryImage::class)->orderBy('order');
    }

    /**
     * Relationship with line items.
     */
    public function lineItems()
    {
        return $this->hasMany(LineItem::class, 'item_id');
    }

    public function latestLineItem()
    {
        return $this->hasOne(LineItem::class, 'item_id')->latestOfMany();
    }

    /**
     * Accessor to get the customer who bought the inventory item.
     */
    public function getSoldToCustomerAttribute()
    {
        return $this->lineItem?->invoice?->customer;
    }

    /**
     * Checks if the inventory has a generated description.
     */
    public function hasGeneratedDescription(): bool
    {
        return $this->category->name === 'Laptops';
    }

    public function getFormattedChecklistValues(): array
    {
        // Ensure relationships are loaded
        $this->loadMissing('category.checklistFields', 'checklistItems');

        // Check if the category has checklist fields
        if (!$this->category || $this->category->checklistFields->isEmpty()) {
            return [];
        }

        // Format and return checklist values
        return $this->category->checklistFields->sortBy('order')->mapWithKeys(function ($field) {
            $item = $this->checklistItems->firstWhere('checklist_field_id', $field->id);
            $value = $item->value ?? 'N/A';
            $longValue = $item->long_value ?? 'N/A';

            switch ($field->type) {
                case 'yes/no':
                    $formattedValue = $value === '1' ? 'Yes' : ($value === '0' ? 'No' : 'N/A');
                    break;
                case 'numeric':
                    $options = json_decode($field->options, true);
                    $unit = $options['unit'] ?? '';
                    $formattedValue = $value !== 'N/A' ? $value . ' ' . $unit : 'N/A';
                    break;
                case 'textarea':
                    $formattedValue = preg_replace('/\s+/', ' ', str_replace(["\r\n", "\n", "\r"], ', ', $longValue));
                    break;
                case 'checkboxes':
                    $decodedValue = json_decode($longValue, true);
                    $formattedValue = is_array($decodedValue) ? implode(', ', $decodedValue) : 'N/A';
                    break;
                default:
                    $formattedValue = $value ?? 'N/A';
                    break;
            }

            return [$field->name => $formattedValue];
        })->toArray();
    }

    /**
     * Check if a user can access images for this inventory item
     */
    public function userCanAccessImages($user = null)
    {
        $user = $user ?? auth()->user();
        
        // No user logged in
        if (!$user) {
            return false;
        }

        // Admin users can access all inventory images
        if ($user->isAdmin()) {
            return true;
        }

        // Users with view inventory permission can access images
        if ($user->hasPermission('view_inventory_items')) {
            return true;
        }

        return false;
    }

    // Date fields
    protected $dates = [
        'updated_at',
        'deleted_at', // Add this line
    ];
}
