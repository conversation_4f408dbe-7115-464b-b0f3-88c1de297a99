<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class Image extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'image_path',
        'og_filename',
        'title',
        'description',
        'alt_text',
        'uploaded_by',
        'context_type',
        'context_id',
        'order_number',
        'is_public',
        'session_id',
    ];

    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the model that this image belongs to (polymorphic relationship)
     */
    public function contextable()
    {
        return $this->morphTo('context');
    }

    /**
     * Scope to get images for a specific context
     */
    public function scopeForContext($query, $contextType, $contextId)
    {
        return $query->where('context_type', $contextType)
                    ->where('context_id', $contextId)
                    ->orderBy('order_number');
    }

    /**
     * Scope to get images by session ID
     */
    public function scopeForSession($query, $sessionId)
    {
        return $query->where('session_id', $sessionId)
                    ->orderBy('order_number');
    }

    /**
     * Check if the current user can access this image
     */
    public function userCanAccess($user = null)
    {
        $user = $user ?? Auth::user();
        
        // Public images are accessible to everyone
        if ($this->is_public) {
            return true;
        }

        // No user logged in and image is not public
        if (!$user) {
            return false;
        }

        // Admin users can access all images
        if ($user->isAdmin()) {
            return true;
        }

        // Image uploaders can access their own images
        if ($user->id === $this->uploaded_by) {
            return true;
        }

        // Check context-specific permissions
        if ($this->context_type && $this->context_id) {
            $contextModel = $this->getContextModel();
            if ($contextModel && method_exists($contextModel, 'userCanAccessImages')) {
                return $contextModel->userCanAccessImages($user);
            }
        }

        return false;
    }

    /**
     * Get the actual context model instance
     */
    public function getContextModel()
    {
        if (!$this->context_type || !$this->context_id) {
            return null;
        }

        $modelClass = 'App\\Models\\' . Str::studly($this->context_type);
        
        if (class_exists($modelClass)) {
            return $modelClass::find($this->context_id);
        }

        return null;
    }

    /**
     * Get the next order number for a context
     */
    public static function getNextOrderNumber($contextType, $contextId)
    {
        return static::forContext($contextType, $contextId)->max('order_number') + 1;
    }

    /**
     * Associate this image with a context
     */
    public function associateWithContext($contextType, $contextId, $orderNumber = null)
    {
        $this->context_type = $contextType;
        $this->context_id = $contextId;
        $this->order_number = $orderNumber ?? static::getNextOrderNumber($contextType, $contextId);
        $this->session_id = null; // Clear session ID when associating with context
        $this->save();
        
        return $this;
    }

    /**
     * Resize an image, convert to WebP, and save it.
     */
    public static function resize($sourcePath, $targetPath, $maxWidth)
    {
        if (!file_exists($sourcePath)) {
            Log::error("Source image not found: {$sourcePath}");
            return false;
        }

        try {
            // Get image dimensions and type
            $imageInfo = getimagesize($sourcePath);
            if (!$imageInfo) {
                Log::error("Failed to get image size: {$sourcePath}");
                return false;
            }

            [$width, $height, $type] = $imageInfo;

            // Log image information for debugging
            Log::info("Resizing image", [
                'source' => $sourcePath,
                'target' => $targetPath,
                'width' => $width,
                'height' => $height,
                'type' => $type,
                'maxWidth' => $maxWidth
            ]);

            if ($width <= $maxWidth) {
                // No resizing needed, but convert to WebP
                return self::convertToWebp($sourcePath, $targetPath);
            }

            $newWidth = $maxWidth;
            $newHeight = intval($height * ($newWidth / $width));

            // Create a new GD image
            $source = null;

            // Handle different image types
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $source = imagecreatefromjpeg($sourcePath);
                    break;
                case IMAGETYPE_PNG:
                    $source = imagecreatefrompng($sourcePath);
                    break;
                case IMAGETYPE_WEBP:
                    $source = imagecreatefromwebp($sourcePath);
                    break;
                case IMAGETYPE_GIF:
                    $source = imagecreatefromgif($sourcePath);
                    break;
            }

            if (!$source) {
                Log::error("Failed to create image resource for: {$sourcePath}");
                return false;
            }

            // Create a new true color image
            $resized = imagecreatetruecolor($newWidth, $newHeight);

            // Handle transparency for PNG and GIF
            if ($type === IMAGETYPE_PNG || $type === IMAGETYPE_GIF) {
                // Set alpha blending mode
                imagealphablending($resized, false);
                imagesavealpha($resized, true);

                // Fill with transparent background
                $transparent = imagecolorallocatealpha($resized, 0, 0, 0, 127);
                imagefilledrectangle($resized, 0, 0, $newWidth, $newHeight, $transparent);
            } else {
                // For JPEG, fill with white background
                $white = imagecolorallocate($resized, 255, 255, 255);
                imagefilledrectangle($resized, 0, 0, $newWidth, $newHeight, $white);
            }

            // Copy and resize the original image onto the new image
            imagecopyresampled($resized, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

            // Free memory
            imagedestroy($source);

            // Save as WebP
            $result = self::saveAsWebp($resized, $targetPath);

            // Log the result
            Log::info("Resize result: " . ($result ? "success" : "failure"));

            return $result;
        } catch (\Exception $e) {
            Log::error("Error resizing image: " . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return false;
        }
    }

    /**
     * Generate thumbnails for an image as WebP.
     */
    public static function generateThumbnails($imagePath)
    {
        $sizes = ['sm' => 300, 'md' => 600, 'lg' => 1200];
        $thumbnails = [];

        foreach ($sizes as $size => $maxWidth) {
            $thumbnailPath = str_replace('img/', 'img/thumbs/', $imagePath);
            $thumbnailPath = str_replace('.', "-{$size}.", $thumbnailPath);
            $thumbnailPath = preg_replace('/\.\w+$/', '.webp', $thumbnailPath);

            $sourcePath = public_path("storage/{$imagePath}");
            $targetPath = public_path("storage/{$thumbnailPath}");

            if (self::resize($sourcePath, $targetPath, $maxWidth)) {
                $thumbnails[$size] = $thumbnailPath;
            }
        }

        return $thumbnails;
    }

    /**
     * Convert an image to WebP format.
     */
    public static function convertToWebp($sourcePath, $targetPath, $quality = 100, $maxWidth = null)
    {
        try {
            // Log conversion attempt
            Log::info("Converting to WebP", [
                'source' => $sourcePath,
                'target' => $targetPath,
                'quality' => $quality,
                'maxWidth' => $maxWidth
            ]);

            if ($maxWidth !== null) {
                $imageInfo = getimagesize($sourcePath);
                if (!$imageInfo) {
                    Log::error("Failed to get image size: {$sourcePath}");
                    return false;
                }

                $width = $imageInfo[0];
                if ($width > $maxWidth) {
                    $resizedPath = tempnam(sys_get_temp_dir(), 'resized_') . '.webp';
                    if (!self::resize($sourcePath, $resizedPath, $maxWidth)) {
                        return false;
                    }
                    $sourcePath = $resizedPath;
                }
            }

            // Get image type
            $imageInfo = getimagesize($sourcePath);
            if (!$imageInfo) {
                Log::error("Failed to get image info: {$sourcePath}");
                return false;
            }

            $type = $imageInfo[2]; // Image type constant
            $source = null;

            // Create image resource based on type
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $source = imagecreatefromjpeg($sourcePath);
                    break;
                case IMAGETYPE_PNG:
                    $source = imagecreatefrompng($sourcePath);
                    break;
                case IMAGETYPE_WEBP:
                    $source = imagecreatefromwebp($sourcePath);
                    break;
                case IMAGETYPE_GIF:
                    $source = imagecreatefromgif($sourcePath);
                    break;
                default:
                    // Try using mime_content_type as fallback
                    $mimeType = mime_content_type($sourcePath);
                    switch ($mimeType) {
                        case 'image/jpeg':
                            $source = imagecreatefromjpeg($sourcePath);
                            break;
                        case 'image/png':
                            $source = imagecreatefrompng($sourcePath);
                            break;
                        case 'image/webp':
                            $source = imagecreatefromwebp($sourcePath);
                            break;
                        case 'image/gif':
                            $source = imagecreatefromgif($sourcePath);
                            break;
                    }
            }

            if (!$source) {
                Log::error("Failed to create image resource: {$sourcePath}");
                return false;
            }

            // Get image dimensions
            $width = imagesx($source);
            $height = imagesy($source);

            // Create a new true color image
            $trueColorImage = imagecreatetruecolor($width, $height);

            // Handle transparency for PNG and GIF
            if ($type === IMAGETYPE_PNG || $type === IMAGETYPE_GIF) {
                // Set alpha blending mode
                imagealphablending($trueColorImage, false);
                imagesavealpha($trueColorImage, true);

                // Fill with transparent background
                $transparent = imagecolorallocatealpha($trueColorImage, 0, 0, 0, 127);
                imagefilledrectangle($trueColorImage, 0, 0, $width, $height, $transparent);
            } else {
                // For JPEG, fill with white background
                $white = imagecolorallocate($trueColorImage, 255, 255, 255);
                imagefilledrectangle($trueColorImage, 0, 0, $width, $height, $white);
            }

            // Copy the original image onto the true color image
            imagecopy($trueColorImage, $source, 0, 0, 0, 0, $width, $height);

            // Free the original image resource
            imagedestroy($source);

            // Ensure target directory exists
            if (!file_exists(dirname($targetPath))) {
                mkdir(dirname($targetPath), 0755, true);
            }

            // Save as WebP with specified quality
            if (imagewebp($trueColorImage, $targetPath, $quality)) {
                imagedestroy($trueColorImage);

                // Clean up temporary file if it was created
                if (isset($resizedPath) && file_exists($resizedPath)) {
                    @unlink($resizedPath);
                }

                // Log success
                Log::info("Successfully converted to WebP: {$targetPath}");
                return true;
            }

            Log::error("Failed to save WebP image to: {$targetPath}");
            imagedestroy($trueColorImage);
            return false;
        } catch (\Exception $e) {
            Log::error("Error converting to WebP: " . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return false;
        }
    }

    /**
     * Save a GD image resource as WebP.
     */
    private static function saveAsWebp($imageResource, $targetPath)
    {
        try {
            // Ensure the path has .webp extension
            $webpPath = preg_replace('/\.\w+$/', '.webp', $targetPath);

            // Log the save attempt
            Log::info("Saving image as WebP", [
                'path' => $webpPath,
                'width' => imagesx($imageResource),
                'height' => imagesy($imageResource)
            ]);

            // Ensure target directory exists
            if (!file_exists(dirname($webpPath))) {
                mkdir(dirname($webpPath), 0755, true);
            }

            // Save the image as WebP with quality 85
            $result = imagewebp($imageResource, $webpPath, 85);

            // Free the image resource
            imagedestroy($imageResource);

            if ($result) {
                // Verify the file was created and has content
                if (file_exists($webpPath) && filesize($webpPath) > 0) {
                    Log::info("Successfully saved WebP image: {$webpPath}");
                    return true;
                } else {
                    Log::error("WebP file was created but is empty or missing: {$webpPath}");
                    return false;
                }
            }

            Log::error("Failed to save WebP image to: {$webpPath}");
            return false;
        } catch (\Exception $e) {
            Log::error("Error saving WebP image: " . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // Make sure to free the resource even if there's an error
            if ($imageResource && (is_resource($imageResource) || $imageResource instanceof \GdImage)) {
                imagedestroy($imageResource);
            }

            return false;
        }
    }

    /**
     * Get the image src for a specific size or full image.
     */
    public function getImageSrc($size = 'full')
    {
        if ($size === 'full') {
            $webpPath = preg_replace('/\.\w+$/', '.webp', $this->image_path);
            return asset("storage/{$webpPath}");
        }

        $thumbnailPath = str_replace('img/', 'img/thumbs/', $this->image_path);
        $thumbnailPath = str_replace('.', "-{$size}.", $thumbnailPath);
        $thumbnailPath = preg_replace('/\.\w+$/', '.webp', $thumbnailPath);

        $thumbnailFullPath = public_path("storage/{$thumbnailPath}");

        if (file_exists($thumbnailFullPath)) {
            return asset("storage/{$thumbnailPath}");
        }

        // Return full image if the thumbnail doesn't exist
        $webpPath = preg_replace('/\.\w+$/', '.webp', $this->image_path);
        return asset("storage/{$webpPath}");
    }
}
