<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RecurrencePattern extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'frequency',
        'interval',
        'days_of_week',
        'days_of_month',
        'months_of_year',
        'week_of_month',
        'until_date',
        'count',
        'exception_dates',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'days_of_week' => 'array',
        'days_of_month' => 'array',
        'months_of_year' => 'array',
        'until_date' => 'date',
        'exception_dates' => 'array',
    ];

    /**
     * Get the events that use this recurrence pattern.
     */
    public function events(): HasMany
    {
        return $this->hasMany(Event::class);
    }

    /**
     * Generate occurrence dates based on the recurrence pattern.
     *
     * @param \DateTime $startDate
     * @param \DateTime $endDate
     * @return array
     */
    public function generateOccurrences(\DateTime $startDate, \DateTime $endDate): array
    {
        // This is a placeholder for the actual implementation
        // In a real application, this would generate all occurrences of the event
        // based on the recurrence pattern, between startDate and endDate

        // Suppress unused variable warnings
        $_ = [$startDate, $endDate];

        return [];
    }
}
