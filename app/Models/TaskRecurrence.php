<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TaskRecurrence extends Model
{
    use HasFactory;

    protected $fillable = [
        'task_id',
        'repeat_interval',
        'repeat_days',
        'excluded_dates',
        'custom_interval',
        'due_time',
    ];

    protected $casts = [
        'repeat_days' => 'array',
        'excluded_dates' => 'array',
    ];

    // Relationships
    public function task()
    {
        return $this->belongsTo(Task::class);
    }
}
