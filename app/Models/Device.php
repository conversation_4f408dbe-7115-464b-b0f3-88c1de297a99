<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\LogsActivity;

class Device extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'certificate_id',
        'serial_number',
        'device_type',
        'manufacturer',
        'model',
        'is_destroyed',
        'destroyed_at',
        'destroyed_by',
        'capacity',
        'owner_label',
        'notes',
        'status',
        'data_sanitization_method',
        'verification_method',
        'pc_manufacturer',
        'pc_serial_number',
        'pc_form_factor',
    ];

    protected $casts = [
        'is_destroyed' => 'boolean',
        'destroyed_at' => 'datetime',
    ];

    /**
     * Events to log for this model
     */
    protected $loggedEvents = ['created', 'updated', 'deleted', 'restored'];

    /**
     * Fields to exclude from activity logs
     */
    protected $excludeFromLogs = [
        'updated_at', // Already tracked by log timestamp
    ];

    // Device types
    public const DEVICE_TYPES = [
        'hdd' => 'Hard Disk Drive',
        'ssd' => 'Solid State Drive',
        'm2-ssd' => 'M.2 SSD',
        'nvme-ssd' => 'NVMe SSD',
        'sd-card' => 'SD Card',
        'eMMC' => 'eMMC',
        'soldered' => 'Soldered Storage',
        'entire-device' => 'Entire Device',
        'usb' => 'USB Drive',
        'tape' => 'Tape Drive',
        'optical' => 'Optical Media',
        'other' => 'Other Storage Media',
    ];

    // Sanitization methods
    public const SANITIZATION_METHODS = [
        'physical_destruction' => 'Physical Destruction',
        'degaussing' => 'Degaussing',
        'shredding' => 'Shredding',
        'secure_erase' => 'Secure Erase',
        'crypto_erase' => 'Cryptographic Erase',
    ];

    // Relationship with Certificate
    public function certificate()
    {
        return $this->belongsTo(Certificate::class);
    }

    // Relationship with User who destroyed the device
    public function destroyedByUser()
    {
        return $this->belongsTo(User::class, 'destroyed_by');
    }

    /**
     * Get a human-readable description of this device for activity logs
     */
    public function getDeviceDescription(): string
    {
        $parts = [];

        if ($this->serial_number) {
            $parts[] = "Serial: {$this->serial_number}";
        }

        if ($this->device_type && isset(self::DEVICE_TYPES[$this->device_type])) {
            $parts[] = "Type: " . self::DEVICE_TYPES[$this->device_type];
        }

        if ($this->manufacturer) {
            $parts[] = "Manufacturer: {$this->manufacturer}";
        }

        if ($this->model) {
            $parts[] = "Model: {$this->model}";
        }

        return implode(', ', $parts) ?: 'Device';
    }

    /**
     * Get the component name for activity logs
     */
    protected function getActivityLogComponent(): string
    {
        return 'chain_of_custody_devices';
    }

    /**
     * Override the activity logging to include device description and certificate context
     * Log to both the device and the parent certificate for visibility
     */
    public function logActivity(string $eventType, string $description, array $additionalData = []): \App\Models\ActivityLog
    {
        // Add device description and certificate context to additional data
        $additionalData = array_merge($additionalData, [
            'device_description' => $this->getDeviceDescription(),
            'certificate_id' => $this->certificate_id,
            'certificate_number' => $this->certificate?->certificate_number,
        ]);

        // Log to the device itself
        $deviceLog = app(\App\Services\ActivityLoggerService::class)->logCustom(
            component: $this->getActivityLogComponent(),
            eventType: $eventType,
            description: $description,
            model: $this,
            additionalData: $additionalData
        );

        // Also log to the parent certificate so device activities show up in certificate logs
        if ($this->certificate) {
            app(\App\Services\ActivityLoggerService::class)->logCustom(
                component: 'chain_of_custody',
                eventType: $eventType,
                description: $description,
                model: $this->certificate,
                additionalData: $additionalData
            );
        }

        return $deviceLog;
    }

    /**
     * Boot method to add additional model event listeners for certificate logging
     */
    protected static function boot()
    {
        parent::boot();

        // Add additional logging to certificate for device events
        static::created(function ($device) {
            if ($device->certificate) {
                try {
                    $device->certificate->logActivity(
                        'device_added',
                        "Device added: ID #{$device->id}, {$device->getDeviceDescription()}",
                        [
                            'device_id' => $device->id,
                            'device_specs' => [
                                'serial_number' => $device->serial_number,
                                'device_type' => $device->device_type,
                                'device_type_label' => self::DEVICE_TYPES[$device->device_type] ?? $device->device_type,
                                'manufacturer' => $device->manufacturer,
                                'model' => $device->model,
                            ]
                        ]
                    );
                } catch (\Exception $e) {
                    // Log error but don't break the device creation
                    \Log::error('Error logging device creation to certificate: ' . $e->getMessage());
                }
            }
        });

        static::updated(function ($device) {
            if ($device->certificate && $device->isDirty()) {
                try {
                    $changes = [];
                    $skipGenericUpdate = false;

                    foreach ($device->getDirty() as $field => $newValue) {
                        if (!in_array($field, ['updated_at'])) {
                            $changes[$field] = [
                                'old' => $device->getOriginal($field),
                                'new' => $newValue
                            ];
                        }
                    }

                    // Skip generic "device updated" log if this is a destruction status change or notes-only update
                    // These are handled by specific controller logging
                    if (isset($changes['is_destroyed']) || isset($changes['destroyed_at']) || isset($changes['destroyed_by'])) {
                        $skipGenericUpdate = true;
                    }

                    // If only notes changed, also skip generic update (handled by controller)
                    if (count($changes) === 1 && isset($changes['notes'])) {
                        $skipGenericUpdate = true;
                    }

                    // Only log generic update for meaningful spec changes (serial, type, manufacturer, model, etc.)
                    if (!empty($changes) && !$skipGenericUpdate) {
                        $device->certificate->logActivity(
                            'device_updated',
                            "Device updated: ID #{$device->id}, {$device->getDeviceDescription()}",
                            [
                                'device_id' => $device->id,
                                'changes' => $changes,
                                'device_specs' => [
                                    'serial_number' => $device->serial_number,
                                    'device_type' => $device->device_type,
                                    'device_type_label' => self::DEVICE_TYPES[$device->device_type] ?? $device->device_type,
                                    'manufacturer' => $device->manufacturer,
                                    'model' => $device->model,
                                ]
                            ]
                        );
                    }
                } catch (\Exception $e) {
                    // Log error but don't break the device update
                    \Log::error('Error logging device update to certificate: ' . $e->getMessage());
                }
            }
        });

        static::deleted(function ($device) {
            if ($device->certificate) {
                try {
                    $device->certificate->logActivity(
                        'device_removed',
                        "Device removed: ID #{$device->id}, {$device->getDeviceDescription()}",
                        [
                            'device_id' => $device->id,
                            'device_specs' => [
                                'serial_number' => $device->serial_number,
                                'device_type' => $device->device_type,
                                'device_type_label' => self::DEVICE_TYPES[$device->device_type] ?? $device->device_type,
                                'manufacturer' => $device->manufacturer,
                                'model' => $device->model,
                                'capacity' => $device->capacity,
                                'owner_label' => $device->owner_label,
                                'notes' => $device->notes,
                                'status' => $device->status,
                                'is_destroyed' => $device->is_destroyed,
                                'destroyed_at' => $device->destroyed_at?->format('Y-m-d H:i:s'),
                                'destroyed_by' => $device->destroyedByUser?->name,
                            ]
                        ]
                    );
                } catch (\Exception $e) {
                    // Log error but don't break the device deletion
                    \Log::error('Error logging device deletion to certificate: ' . $e->getMessage());
                }
            }
        });
    }

    // Get device type label
    public function getDeviceTypeLabel()
    {
        return self::DEVICE_TYPES[$this->device_type] ?? $this->device_type;
    }

    // Get sanitization method label
    public function getSanitizationMethodLabel()
    {
        return self::SANITIZATION_METHODS[$this->data_sanitization_method] ?? $this->data_sanitization_method;
    }
}
