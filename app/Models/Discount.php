<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


class Discount extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'amount',
        'scope',
        'start_date',
        'end_date',
        'once_per_customer',
        'minimum_purchase',
        'maximum_discount',
        'category_id',
        'reusable',
        'is_customer_specific',
    ];

    // Relationships
    public function category()
    {
        return $this->belongsTo(InventoryCategory::class, 'category_id');
    }

    public function customerDiscounts()
    {
        return $this->hasMany(CustomerDiscount::class);
    }

    public function invoiceDiscounts()
    {
        return $this->hasMany(InvoiceDiscount::class);
    }

    public function lineItemDiscounts()
    {
        return $this->hasMany(LineItemDiscount::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('end_date')->orWhere('end_date', '>=', now());
        })->where('start_date', '<=', now());
    }

    public function isValid()
    {
        return $this->start_date <= now() && (!$this->end_date || $this->end_date >= now());
    }

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    public function isGeneralDiscount()
{
    return !$this->is_customer_specific;
}

public function isCustomerSpecific()
{
    return $this->is_customer_specific;
}

public function customer()
{
    return $this->belongsTo(Customer::class)->withTrashed();
}


    

}
