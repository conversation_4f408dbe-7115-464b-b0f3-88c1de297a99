<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CalendarSyncLog extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'microsoft_calendar_integration_id',
        'event_id',
        'microsoft_event_id',
        'action',
        'status',
        'error_message',
        'request_data',
        'response_data',
        'started_at',
        'completed_at',
        'retry_count',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'request_data' => 'array',
        'response_data' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'retry_count' => 'integer',
    ];

    /**
     * Get the Microsoft calendar integration.
     */
    public function microsoftCalendarIntegration(): BelongsTo
    {
        return $this->belongsTo(MicrosoftCalendarIntegration::class);
    }

    /**
     * Get the ETRFlow event.
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Mark the sync as successful.
     */
    public function markAsSuccessful(array $responseData = null): void
    {
        $this->update([
            'status' => 'success',
            'completed_at' => now(),
            'response_data' => $responseData,
            'error_message' => null,
        ]);
    }

    /**
     * Mark the sync as failed.
     */
    public function markAsFailed(string $errorMessage, array $responseData = null): void
    {
        $this->update([
            'status' => 'failed',
            'completed_at' => now(),
            'error_message' => $errorMessage,
            'response_data' => $responseData,
        ]);
    }

    /**
     * Get the duration of the sync operation.
     */
    public function getDurationInSeconds(): ?int
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        return $this->completed_at->diffInSeconds($this->started_at);
    }
}
