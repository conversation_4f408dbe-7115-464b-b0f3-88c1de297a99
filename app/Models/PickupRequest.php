<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\LogsActivity;
use App\Jobs\SyncPickupEventsToMicrosoft;
use App\Models\MicrosoftCalendarIntegration;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class PickupRequest extends Model
{
    use HasFactory, LogsActivity;

    // Configure activity logging
    protected $loggedEvents = ['created', 'updated', 'deleted'];
    protected $excludeFromLogs = ['pickup_details', 'management_token'];

    /**
     * Boot the model and set up event listeners for Microsoft sync.
     */
    protected static function boot()
    {
        parent::boot();

        // Sync to Microsoft when pickup requests are updated with relevant changes
        static::updated(function (PickupRequest $pickupRequest) {
            // Only sync if this pickup request has an associated event
            if ($pickupRequest->event && $pickupRequest->event->is_pickup_event) {
                // Check if any relevant pickup details were changed
                $relevantFields = [
                    'contact_name', 'business_name', 'email', 'phone',
                    'pickup_address', 'pickup_items', 'pickup_quantity',
                    'property_location_details', 'other_notes', 'pickup_details',
                    'accessibility_level', 'driver_instructions', 'load_size',
                    'item_types', 'item_specifics', 'status'
                ];

                $hasRelevantChanges = false;
                foreach ($relevantFields as $field) {
                    if ($pickupRequest->isDirty($field)) {
                        $hasRelevantChanges = true;
                        break;
                    }
                }

                if ($hasRelevantChanges) {
                    static::queueMicrosoftSyncForEvent($pickupRequest->event, 'pickup_request_updated');
                }
            }
        });
    }

    /**
     * Queue Microsoft sync for the associated event.
     */
    protected static function queueMicrosoftSyncForEvent(Event $event, string $action)
    {
        try {
            // Only sync if there is an active shared calendar integration
            if (MicrosoftCalendarIntegration::hasActiveSharedCalendar()) {
                Log::info('Queueing Microsoft sync for pickup request update to shared calendar', [
                    'event_id' => $event->id,
                    'action' => $action,
                    'title' => $event->title
                ]);

                // Queue the sync job for the shared calendar
                SyncPickupEventsToMicrosoft::forEvent($event)->dispatch();
            }
        } catch (\Exception $e) {
            Log::error('Failed to queue Microsoft sync for pickup request update', [
                'event_id' => $event->id,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
        }
    }

    // Status constants for the pickup request workflow
    public const STATUSES = [
        'incoming' => 'Incoming',
        'pending' => 'Pending Customer Confirmation',
        'confirmed' => 'Confirmed',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled'
    ];

    // Status descriptions for better understanding
    public const STATUS_DESCRIPTIONS = [
        'incoming' => 'Pickup requests submitted by clients that need customer assignment and approval',
        'pending' => 'Pickup requests assigned to a customer with pickup time but not confirmed by customer',
        'confirmed' => 'Staff has confirmed customer response (email/call), appointment confirmed',
        'completed' => 'Pickup has been completed',
        'cancelled' => 'Pickup request has been cancelled'
    ];

    protected $fillable = [
        'contact_name',
        'business_name',
        'email',
        'phone',
        'pickup_address',
        'pickup_items',
        'pickup_quantity',
        'property_location_details',
        'other_notes',
        'pickup_details',
        'preferred_pickup_date',
        'status',
        'customer_id',
        'event_id',
        'submitted_at',
        // New guided pickup fields
        'accessibility_level',
        'driver_instructions',
        'load_size',
        'item_types',
        'item_specifics',
        // Management token fields
        'management_token',
        'management_token_expires_at',
        // Reminder and confirmation fields
        'reminder_sent_at',
        'final_reminder_sent_at',
        'confirmation_token',
        'confirmed_at',
        'cancelled_at',
        'cancellation_reason',
    ];

    protected $casts = [
        'submitted_at' => 'datetime',
        'preferred_pickup_date' => 'datetime:Y-m-d H:i:s',
        'pickup_details' => 'array',
        'item_types' => 'array',
        'management_token_expires_at' => 'datetime',
        'reminder_sent_at' => 'datetime',
        'final_reminder_sent_at' => 'datetime',
        'confirmed_at' => 'datetime',
        'cancelled_at' => 'datetime',
    ];

    /**
     * Get the customer associated with this pickup request.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the event associated with this pickup request.
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get the images associated with this pickup request.
     */
    public function images()
    {
        return $this->belongsToMany(Image::class, 'pickup_request_images')
            ->withPivot('sort_order')
            ->orderBy('pickup_request_images.sort_order');
    }

    /**
     * Get all pickup details as a structured array.
     * Combines individual fields with JSON data for comprehensive details.
     */
    public function getPickupDetailsArray(): array
    {
        $details = [
            // Contact Information
            'contact' => [
                'name' => $this->contact_name,
                'business_name' => $this->business_name,
                'email' => $this->email,
                'phone' => $this->phone,
            ],

            // Pickup Information
            'pickup' => [
                'address' => $this->pickup_address,
                'items' => $this->pickup_items,
                'quantity' => $this->pickup_quantity,
                'property_location_details' => $this->property_location_details,
                'other_notes' => $this->other_notes,
                'preferred_date' => $this->preferred_pickup_date?->toISOString(),
            ],

            // Guided Pickup Details
            'guided_details' => [
                'accessibility_level' => $this->accessibility_level,
                'driver_instructions' => $this->driver_instructions,
                'load_size' => $this->load_size,
                'item_types' => $this->item_types ?? [],
                'item_specifics' => $this->item_specifics,
            ],

            // Metadata
            'meta' => [
                'submitted_at' => $this->submitted_at?->toISOString(),
                'status' => $this->status,
                'request_id' => $this->id,
            ]
        ];

        // Merge with any additional JSON details
        if ($this->pickup_details) {
            $details = array_merge_recursive($details, $this->pickup_details);
        }

        return $details;
    }

    /**
     * Update the JSON pickup details while maintaining individual fields.
     */
    public function updatePickupDetails(array $additionalDetails = []): void
    {
        $currentDetails = $this->pickup_details ?? [];
        $this->pickup_details = array_merge($currentDetails, $additionalDetails);
        $this->save();
    }

    /**
     * Get internal staff notes from pickup_details JSON.
     */
    public function getInternalStaffNotes(): ?string
    {
        return $this->pickup_details['internal']['staff_notes'] ?? null;
    }

    /**
     * Update internal staff notes in pickup_details JSON.
     */
    public function updateInternalStaffNotes(string $notes): void
    {
        $currentDetails = $this->pickup_details ?? [];
        $currentDetails['internal']['staff_notes'] = $notes;
        $currentDetails['internal']['updated_at'] = now()->toISOString();
        $currentDetails['internal']['updated_by'] = auth()->user()?->id;

        $this->pickup_details = $currentDetails;
        $this->save();
    }

    /**
     * Record that a pickup details email was sent.
     */
    public function recordConfirmationEmailSent(string $emailAddress): void
    {
        $currentDetails = $this->pickup_details ?? [];

        if (!isset($currentDetails['email_communications'])) {
            $currentDetails['email_communications'] = [];
        }

        $currentDetails['email_communications']['confirmation_email'] = [
            'sent_at' => now()->toISOString(),
            'sent_to' => $emailAddress,
            'sent_by' => auth()->user()?->id,
            'status' => 'sent'
        ];

        $this->pickup_details = $currentDetails;
        $this->save();
    }

    /**
     * Check if a pickup details email has been sent.
     */
    public function hasConfirmationEmailBeenSent(): bool
    {
        return isset($this->pickup_details['email_communications']['confirmation_email']);
    }

    /**
     * Get pickup details email information.
     */
    public function getConfirmationEmailDetails(): ?array
    {
        return $this->pickup_details['email_communications']['confirmation_email'] ?? null;
    }

    /**
     * Generate a unique management token for customer appointment management.
     */
    public function generateManagementToken(): string
    {
        // Generate a secure random token with 20 characters
        $token = \Illuminate\Support\Str::random(20);

        // Set expiration to 60 days from now
        $expiresAt = now()->addDays(60);

        // Store the token and expiration in database fields
        $this->update([
            'management_token' => $token,
            'management_token_expires_at' => $expiresAt
        ]);

        return $token;
    }

    /**
     * Ensure a valid management token exists, generating one if needed.
     */
    public function ensureManagementToken(): string
    {
        // If we already have a valid token, return it
        if ($this->hasValidManagementToken()) {
            return $this->management_token;
        }

        // Otherwise, generate a new one
        return $this->generateManagementToken();
    }

    /**
     * Get the management token for this pickup request.
     */
    public function getManagementToken(): ?string
    {
        return $this->management_token;
    }

    /**
     * Check if the management token is valid (exists and not expired).
     */
    public function hasValidManagementToken(): bool
    {
        return $this->management_token &&
               $this->management_token_expires_at &&
               $this->management_token_expires_at->isFuture();
    }

    /**
     * Check if the management token is expired.
     */
    public function isManagementTokenExpired(): bool
    {
        return $this->management_token_expires_at &&
               $this->management_token_expires_at->isPast();
    }

    /**
     * Find a pickup request by management token (only if valid).
     */
    public static function findByValidToken(string $token): ?self
    {
        return self::where('management_token', $token)
                   ->where('management_token_expires_at', '>', now())
                   ->first();
    }

    /**
     * Get a formatted summary of pickup details for display.
     */
    public function getPickupSummary(): string
    {
        $summary = "Pickup for: {$this->contact_name}\n";

        if ($this->business_name) {
            $summary .= "Business: {$this->business_name}\n";
        }

        $summary .= "Email: {$this->email}\n";
        $summary .= "Phone: {$this->phone}\n\n";

        // Legacy fields (for backward compatibility)
        if ($this->pickup_items) {
            $summary .= "Items: {$this->pickup_items}\n";
        }
        if ($this->pickup_quantity) {
            $summary .= "Quantity: {$this->pickup_quantity}\n";
        }

        // New guided pickup details
        if ($this->load_size) {
            $loadSizeLabels = [
                'small' => 'Small Load',
                'medium' => 'Medium Load',
                'large' => 'Large or Heavy Load'
            ];
            $summary .= "Load Size: " . ($loadSizeLabels[$this->load_size] ?? $this->load_size) . "\n";
        }

        if ($this->item_types && is_array($this->item_types)) {
            $itemTypeLabels = [
                'small_electronics' => 'Small Electronics',
                'appliances' => 'Appliances',
                'peripheral_devices' => 'Peripheral Devices',
                'batteries' => 'Batteries',
                'crt_tvs' => 'CRT TVs',
                'flatscreen_tvs' => 'Flatscreen TV(s)',
                'tote_swap' => 'Tote Swap',
                'gaylord_swap' => 'Gaylord Swap',
                'servers' => 'Servers',
                'laptops' => 'Laptops',
                'desktops' => 'Desktops',
                'large_appliances' => 'Large Appliances',
                'other' => 'Other',
            ];
            $typeLabels = array_map(fn($type) => $itemTypeLabels[$type] ?? $type, $this->item_types);
            $summary .= "Item Types: " . implode(', ', $typeLabels) . "\n";
        }

        if ($this->item_specifics) {
            $summary .= "Item Specifics: {$this->item_specifics}\n";
        }

        $summary .= "Location on Property: {$this->property_location_details}\n";

        if ($this->accessibility_level) {
            $accessibilityLabels = [
                'easy' => 'Easy Access',
                'moderate' => 'Moderate Access',
                'difficult' => 'Difficult Access'
            ];
            $summary .= "Accessibility: " . ($accessibilityLabels[$this->accessibility_level] ?? $this->accessibility_level) . "\n";
        }

        if ($this->driver_instructions) {
            $summary .= "Driver Instructions: {$this->driver_instructions}\n";
        }

        if ($this->other_notes) {
            $summary .= "Customer Notes: {$this->other_notes}\n";
        }

        return trim($summary);
    }

    /**
     * Get the formatted status label.
     */
    public function getFormattedStatus(): string
    {
        // Special case: if status is pending but pickup is not yet scheduled
        if ($this->status === 'pending' && $this->customer_id && !$this->event_id) {
            return 'Schedule Finalization Required';
        }
        
        return self::STATUSES[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Get the status description.
     */
    public function getStatusDescription(): string
    {
        return self::STATUS_DESCRIPTIONS[$this->status] ?? '';
    }

    /**
     * Check if the pickup request is in incoming status.
     */
    public function isIncoming(): bool
    {
        return $this->status === 'incoming';
    }

    /**
     * Check if the pickup request is pending customer confirmation.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the pickup request is confirmed.
     */
    public function isConfirmed(): bool
    {
        return $this->status === 'confirmed';
    }

    /**
     * Check if the pickup request is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the pickup request is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if the pickup request can be cancelled.
     */
    public function canBeCancelled(): bool
    {
        return !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * Check if the pickup request needs customer assignment.
     */
    public function needsCustomerAssignment(): bool
    {
        return $this->status === 'incoming' && !$this->customer_id;
    }

    /**
     * Check if the pickup request is ready to be scheduled.
     */
    public function isReadyToSchedule(): bool
    {
        return $this->status === 'incoming' && $this->customer_id;
    }

    /**
     * Check if the pickup request needs customer confirmation.
     */
    public function needsCustomerConfirmation(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if pickup is within 72 hours and customer can confirm.
     */
    public function canCustomerConfirm(): bool
    {
        if ($this->status !== 'pending' || !$this->event) {
            return false;
        }

        $pickupTime = $this->event->start_date;
        $hoursUntilPickup = now()->diffInHours($pickupTime, false);
        
        return $hoursUntilPickup <= 72 && $hoursUntilPickup > 0;
    }

    /**
     * Confirm the pickup request (customer confirmation).
     */
    public function confirmByCustomer(): void
    {
        if (!$this->canCustomerConfirm()) {
            throw new \Exception('Pickup request cannot be confirmed by customer at this time.');
        }

        $this->status = 'confirmed';
        $this->recordCustomerConfirmation();
        $this->save();
    }

    /**
     * Record that the customer confirmed the appointment.
     */
    public function recordCustomerConfirmation(): void
    {
        $currentDetails = $this->pickup_details ?? [];

        if (!isset($currentDetails['customer_confirmations'])) {
            $currentDetails['customer_confirmations'] = [];
        }

        $currentDetails['customer_confirmations']['self_confirmation'] = [
            'confirmed_at' => now()->toISOString(),
            'confirmation_method' => 'customer_portal',
            'status' => 'confirmed'
        ];

        $this->pickup_details = $currentDetails;
    }

    /**
     * Check if customer has self-confirmed.
     */
    public function hasCustomerSelfConfirmed(): bool
    {
        return isset($this->pickup_details['customer_confirmations']['self_confirmation']);
    }

    /**
     * Check if 48-hour confirmation reminder has been sent.
     */
    public function hasConfirmationReminderBeenSent(): bool
    {
        return isset($this->pickup_details['email_communications']['48_hour_reminder']);
    }

    /**
     * Get 48-hour confirmation reminder details.
     */
    public function getConfirmationReminderDetails(): ?array
    {
        return $this->pickup_details['email_communications']['48_hour_reminder'] ?? null;
    }

    /**
     * Log pickup request creation with detailed information.
     */
    public function logCreation(string $sourceType, ?string $createdByUser = null): void
    {
        // Laravel automatically handles timezone - no manual conversion needed
        $pickupDateLocal = $this->preferred_pickup_date->format('M j, Y \a\t g:i A');
        
        $description = $sourceType === 'guest' 
            ? "Pickup request created by guest: {$this->contact_name}"
            : "Pickup request created by staff: {$this->contact_name}";
            
        $additionalData = [
            'source_type' => $sourceType,
            'created_by_user' => $createdByUser,
            'contact_details' => [
                'name' => $this->contact_name,
                'business_name' => $this->business_name,
                'email' => $this->email,
                'phone' => $this->phone,
            ],
            'pickup_details' => [
                'preferred_date_local' => $pickupDateLocal,
                'preferred_date_utc' => $this->preferred_pickup_date->toISOString(),
                'load_size' => $this->load_size,
                'item_types' => $this->item_types,
                'accessibility_level' => $this->accessibility_level,
            ]
        ];

        $this->logActivity('created', $description, $additionalData);
    }

    /**
     * Log when pickup request is linked to a customer.
     */
    public function logCustomerLinked(Customer $customer, string $linkedByUser): void
    {
        $description = "Pickup request linked to customer: {$customer->name} (ID: {$customer->id})";
        
        $additionalData = [
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'customer_type' => $customer->type,
            'linked_by_user' => $linkedByUser,
            'previous_status' => 'incoming',
            'new_status' => 'pending'
        ];

        $this->logActivity('customer_linked', $description, $additionalData);
    }

    /**
     * Log when pickup request is unlinked from a customer.
     */
    public function logCustomerUnlinked(?string $previousCustomerName, string $unlinkedByUser): void
    {
        $description = $previousCustomerName 
            ? "Pickup request unlinked from customer: {$previousCustomerName}"
            : "Pickup request customer unlinked";
            
        $additionalData = [
            'previous_customer_name' => $previousCustomerName,
            'unlinked_by_user' => $unlinkedByUser,
            'previous_status' => 'pending',
            'new_status' => 'incoming'
        ];

        $this->logActivity('customer_unlinked', $description, $additionalData);
    }

    /**
     * Log when appointment is scheduled (event created).
     */
    public function logAppointmentScheduled(Event $event, string $scheduledByUser): void
    {
        // Laravel automatically handles timezone - no manual conversion needed
        $scheduledTime = $event->start_date->format('M j, Y \a\t g:i A');
        $originalTime = $this->preferred_pickup_date->format('M j, Y \a\t g:i A');
        
        $timeChanged = !$event->start_date->equalTo($this->preferred_pickup_date);
        
        $description = $timeChanged 
            ? "Appointment scheduled for {$scheduledTime} (changed from original request time: {$originalTime})"
            : "Appointment scheduled for {$scheduledTime} (same as requested time)";
            
        $additionalData = [
            'event_id' => $event->id,
            'scheduled_time_local' => $scheduledTime,
            'scheduled_time_utc' => $event->start_date->toISOString(),
            'original_requested_time_local' => $originalTime,
            'original_requested_time_utc' => $this->preferred_pickup_date->toISOString(),
            'time_changed' => $timeChanged,
            'scheduled_by_user' => $scheduledByUser,
            'assigned_driver' => $event->assignedDriver?->name,
            'staff_needed' => $event->staff_needed
        ];

        $this->logActivity('appointment_scheduled', $description, $additionalData);
    }

    /**
     * Log when pickup details email is sent to customer.
     */
    public function logConfirmationEmailSent(string $sentToEmail, string $sentByUser, bool $isResend = false): void
    {
        $description = $isResend 
            ? "Pickup details email resent to customer: {$sentToEmail}"
            : "Pickup details email sent to customer: {$sentToEmail}";
            
        $additionalData = [
            'sent_to_email' => $sentToEmail,
            'sent_by_user' => $sentByUser,
            'is_resend' => $isResend,
            'management_token_included' => !empty($this->management_token),
            'email_type' => 'pickup_confirmation'
        ];

        $this->logActivity('confirmation_email_sent', $description, $additionalData);
    }

    /**
     * Log when pickup is confirmed by customer through portal.
     */
    public function logCustomerConfirmation(): void
    {
        // Laravel automatically handles timezone - no manual conversion needed
        $confirmationTime = now()->format('M j, Y \a\t g:i A');
        
        $description = "Pickup confirmed by customer {$this->contact_name} via appointment portal";
        
        $additionalData = [
            'confirmed_by' => 'customer',
            'confirmation_method' => 'appointment_portal',
            'customer_name' => $this->contact_name,
            'customer_email' => $this->email,
            'confirmation_time_local' => $confirmationTime,
            'confirmation_time_utc' => now()->toISOString(),
            'previous_status' => 'pending',
            'new_status' => 'confirmed'
        ];

        $this->logActivity('customer_confirmed', $description, $additionalData);
    }

    /**
     * Log when pickup is confirmed by staff manually.
     */
    public function logStaffConfirmation(string $confirmedByUser): void
    {
        // Laravel automatically handles timezone - no manual conversion needed
        $confirmationTime = now()->format('M j, Y \a\t g:i A');
        
        $description = "Pickup manually confirmed by staff: {$confirmedByUser}";
        
        $additionalData = [
            'confirmed_by' => 'staff',
            'confirmed_by_user' => $confirmedByUser,
            'confirmation_method' => 'staff_backend',
            'confirmation_time_local' => $confirmationTime,
            'confirmation_time_utc' => now()->toISOString(),
            'previous_status' => 'pending',
            'new_status' => 'confirmed'
        ];

        $this->logActivity('staff_confirmed', $description, $additionalData);
    }

    /**
     * Log when pickup is unconfirmed.
     */
    public function logUnconfirmed(string $unconfirmedByUser): void
    {
        $description = "Pickup marked as not confirmed by staff: {$unconfirmedByUser}";
        
        $additionalData = [
            'unconfirmed_by_user' => $unconfirmedByUser,
            'previous_status' => 'confirmed',
            'new_status' => 'pending'
        ];

        $this->logActivity('unconfirmed', $description, $additionalData);
    }

    /**
     * Log when pickup is cancelled.
     */
    public function logCancellation(string $cancelledBy, string $cancellationMethod, ?string $cancellationReason = null): void
    {
        $description = $cancelledBy === 'customer' 
            ? "Pickup cancelled by customer via {$cancellationMethod}"
            : "Pickup cancelled by staff: {$cancelledBy}";
            
        $additionalData = [
            'cancelled_by' => $cancelledBy,
            'cancellation_method' => $cancellationMethod,
            'cancellation_reason' => $cancellationReason,
            'previous_status' => $this->getOriginal('status'),
            'new_status' => 'cancelled'
        ];

        $this->logActivity('cancelled', $description, $additionalData);
    }

    /**
     * Log when pickup is marked as completed.
     */
    public function logCompletion(string $completedByUser): void
    {
        // Laravel automatically handles timezone - no manual conversion needed
        $completionTime = now()->format('M j, Y \a\t g:i A');
        
        $description = "Pickup marked as completed by staff: {$completedByUser}";
        
        $additionalData = [
            'completed_by_user' => $completedByUser,
            'completion_time_local' => $completionTime,
            'completion_time_utc' => now()->toISOString(),
            'previous_status' => 'confirmed',
            'new_status' => 'completed'
        ];

        $this->logActivity('completed', $description, $additionalData);
    }

    /**
     * Log when reminder email is sent.
     */
    public function logReminderSent(): void
    {
        // Laravel automatically handles timezone - no manual conversion needed
        $reminderTime = now()->format('M j, Y \a\t g:i A');
        
        $description = "Sent 48-hour reminder asking for customer final confirmation to: {$this->email}";
        
        $additionalData = [
            'sent_to_email' => $this->email,
            'sent_by' => 'system',
            'reminder_type' => '48_hour_confirmation',
            'reminder_time_local' => $reminderTime,
            'reminder_time_utc' => now()->toISOString(),
            'pickup_status' => $this->status
        ];

        $this->logActivity('reminder_sent', $description, $additionalData);
    }

    /**
     * Log when final reminder email is sent.
     */
    public function logFinalReminderSent(): void
    {
        // Laravel automatically handles timezone - no manual conversion needed
        $reminderTime = now()->format('M j, Y \a\t g:i A');
        
        $description = "Sent 48-hour reminder that appointment is fully scheduled to: {$this->email}";
        
        $additionalData = [
            'sent_to_email' => $this->email,
            'sent_by' => 'system',
            'reminder_type' => '48_hour_final_reminder',
            'reminder_time_local' => $reminderTime,
            'reminder_time_utc' => now()->toISOString(),
            'pickup_status' => $this->status
        ];

        $this->logActivity('final_reminder_sent', $description, $additionalData);
    }

    /**
     * Send pickup details email to customer.
     * Uses existing management token if valid, otherwise generates a new one.
     */
    public function sendConfirmationEmail(bool $forceResend = false): void
    {
        // Validate that the pickup request is in the correct status
        if (!in_array($this->status, ['pending', 'confirmed'])) {
            throw new \Exception('Confirmation emails can only be sent for pending or confirmed pickup requests.');
        }

        // Validate that an event has been created
        if (!$this->event) {
            throw new \Exception('Cannot send pickup details - pickup has not been scheduled yet.');
        }

        // Check if pickup details email has already been sent (unless force resend is requested)
        if ($this->hasConfirmationEmailBeenSent() && !$forceResend) {
            $emailDetails = $this->getConfirmationEmailDetails();
            throw new \Exception('Pickup details have already been sent to ' . $emailDetails['sent_to'] . ' on ' .
                       \Carbon\Carbon::parse($emailDetails['sent_at'])->format('M j, Y \a\t g:i A') . '.');
        }

        // Load the event with assigned driver
        $this->load('event.assignedDriver');

        // Ensure management token exists (reuse existing valid token or generate new one)
        $this->ensureManagementToken();

        // Prepare template data
        $templateData = [
            'pickup_request' => $this,
        ];

        // Send email using EmailTemplateService
        $emailTemplateService = app(\App\Services\EmailTemplateService::class);

        if ($emailTemplateService->templateExists('pickup_confirmation')) {
            \Mail::to($this->email)->send(new \App\Mail\TemplatedMail('pickup_confirmation', $templateData));
        } else {
            throw new \Exception('Pickup details email template not found. Please contact administrator.');
        }

        // Record that the email was sent
        $this->recordConfirmationEmailSent($this->email);

        // Log the confirmation email sent
        $this->logConfirmationEmailSent($this->email, auth()->user()->name ?? 'system', $forceResend);

        \Log::info('Pickup details email sent', [
            'pickup_request_id' => $this->id,
            'email' => $this->email,
            'sent_by' => auth()->user()->email ?? 'system',
            'is_resend' => $forceResend
        ]);
    }

    /**
     * Override the activity log component name.
     */
    protected function getActivityLogComponent(): string
    {
        return 'pickup_requests';
    }
}
