<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Jobs\SyncPickupEventsToMicrosoft;
use Illuminate\Support\Facades\Log;

class MicrosoftCalendarIntegration extends Model
{
    /**
     * Boot the model and set up event listeners.
     */
    protected static function boot()
    {
        parent::boot();

        // Trigger sync when sync is enabled
        static::updated(function (MicrosoftCalendarIntegration $integration) {
            // Check if sync was just enabled
            if ($integration->isDirty('sync_enabled') && $integration->sync_enabled) {
                Log::info('Microsoft sync enabled, queueing full sync', [
                    'user_id' => $integration->user_id,
                    'integration_id' => $integration->id
                ]);

                // Queue a full sync for this integration
                SyncPickupEventsToMicrosoft::forIntegration($integration)->dispatch();
            }
        });
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'access_token',
        'refresh_token',
        'token_expires_at',
        'outlook_calendar_id',
        'sync_enabled',
        'last_sync_at',
        'sync_status',
        'error_message',
        'sync_settings',
        'microsoft_user_id',
        'microsoft_user_email',
        'is_shared_calendar',
        'calendar_name',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'token_expires_at' => 'datetime',
        'last_sync_at' => 'datetime',
        'sync_enabled' => 'boolean',
        'sync_settings' => 'array',
        'is_shared_calendar' => 'boolean',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'access_token',
        'refresh_token',
    ];

    /**
     * Get the user that owns the integration.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the sync logs for this integration.
     */
    public function syncLogs(): HasMany
    {
        return $this->hasMany(CalendarSyncLog::class);
    }

    /**
     * Check if the access token is expired.
     */
    public function isTokenExpired(): bool
    {
        return $this->token_expires_at && $this->token_expires_at->isPast();
    }

    /**
     * Check if the integration is active and properly configured.
     */
    public function isActive(): bool
    {
        return $this->sync_enabled && 
               !empty($this->access_token) && 
               !$this->isTokenExpired() &&
               $this->sync_status !== 'failed';
    }

    /**
     * Get the shared calendar integration (single integration for all staff).
     */
    public static function getSharedCalendarIntegration(): ?self
    {
        return static::where('is_shared_calendar', true)
            ->where('sync_enabled', true)
            ->first();
    }

    /**
     * Check if there is an active shared calendar integration.
     */
    public static function hasActiveSharedCalendar(): bool
    {
        return static::where('is_shared_calendar', true)
            ->where('sync_enabled', true)
            ->where('sync_status', '!=', 'failed')
            ->exists();
    }

    /**
     * Get the sync settings with defaults.
     */
    public function getSyncSettingsWithDefaults(): array
    {
        return array_merge([
            'sync_pickup_events' => true,
            'sync_completed_events' => false,
            'sync_cancelled_events' => false,
            'include_customer_details' => true,
            'include_item_details' => true,
            'event_category' => 'ETRFlow Pickup',
        ], $this->sync_settings ?? []);
    }
}
