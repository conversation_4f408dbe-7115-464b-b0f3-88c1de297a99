<?php

namespace App\Models;

use App\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;
    
    protected static function boot()
    {
        parent::boot();
        
        // DEBUGGING: Log when customer is being deleted
        static::deleting(function ($customer) {
            \Illuminate\Support\Facades\Log::warning('CUSTOMER DELETING EVENT TRIGGERED', [
                'customer_id' => $customer->id,
                'customer_name' => $customer->name,
                'is_soft_delete' => !$customer->isForceDeleting(),
                'backtrace' => collect(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 15))
                    ->map(function ($trace) {
                        return [
                            'file' => $trace['file'] ?? 'unknown',
                            'line' => $trace['line'] ?? 0,
                            'function' => $trace['function'] ?? 'unknown',
                            'class' => $trace['class'] ?? null,
                        ];
                    })
                    ->toArray(),
            ]);
        });
    }

    protected $fillable = [
        'name',
        'contact',
        'nickname',
        'email',
        'phone',
        'address',
        'notes',
        'type',
        'website',
    ];

    /**
     * Events to log for this model
     */
    protected $loggedEvents = ['created', 'updated', 'deleted', 'restored'];

    /**
     * Fields to exclude from activity logs
     */
    protected $excludeFromLogs = [
        'updated_at', // Already tracked by log timestamp
    ];

    public function getTotalPurchasesAttribute(): int
    {
        // Count inventory items purchased by this customer through invoices
        $count = \App\Models\Inventory::whereHas('lineItems.invoice', function($query) {
            $query->where('customer_id', $this->id);
        })->count();

        return $count;
    }

    /**
     * Get inventory items purchased by this customer through invoices
     */
    public function purchasedInventory()
    {
        return \App\Models\Inventory::whereHas('lineItems.invoice', function($query) {
            $query->where('customer_id', $this->id);
        });
    }

    public function updateTotalRevenue()
    {
        $this->total_revenue = $this->invoices()->sum('final_price');
        $this->save();
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function getTotalPurchasesValueAttribute(): float
    {
        return $this->invoices()->sum('final_price');
    }

    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }

    /**
     * Get customer discounts relationship
     */
    public function customerDiscounts()
    {
        return $this->hasMany(CustomerDiscount::class);
    }

    /**
     * Get the files associated with the customer.
     */
    public function files()
    {
        return $this->morphMany(File::class, 'fileable');
    }

    /**
     * Get all form submissions for this customer
     */
    public function formSubmissions()
    {
        return $this->hasMany(FormSubmission::class);
    }

    /**
     * Check if a user can access files for this customer
     * Used by the File model to determine access permissions
     */
    public function userCanAccessFiles($user)
    {
        if (!$user) return false;

        // Admin users can access all files
        if ($user->isAdmin()) return true;

        // Users with permission to view customer accounts can access files
        return $user->hasPermission('view_customer_accounts');
    }

    /**
     * Check if customer has a photo ID on file
     *
     * @return bool
     */
    public function hasPhotoIdOnFile()
    {
        return $this->files()
            ->where(function($query) {
                $query->where('description', 'LIKE', '%photo_id%')
                      ->orWhere('description', 'LIKE', '%Customer ID%')
                      ->orWhere('description', 'LIKE', '%ID%')
                      ->orWhere('metadata->file_type', 'photo_id');
            })
            ->exists();
    }

    /**
     * Get the active customer service agreement (contract discount)
     */
    public function getActiveServiceAgreement()
    {
        $activeContractDiscountId = \App\Models\GlobalConfig::getValue('active_customer_contract_id');

        if (!$activeContractDiscountId) {
            return null;
        }

        $now = now();
        return $this->customerDiscounts()
            ->where('discount_id', $activeContractDiscountId)
            ->where(function($query) use ($now) {
                $query->where(function($q) use ($now) {
                    $q->whereNull('start_date')
                      ->orWhere('start_date', '<=', $now);
                })->where(function($q) use ($now) {
                    $q->whereNull('end_date')
                      ->orWhere('end_date', '>=', $now);
                })->where(function($q) {
                    $q->whereNull('maximum_uses')
                      ->orWhereRaw('usage_count < maximum_uses');
                });
            })
            ->first();
    }

    /**
     * Check if customer has a signed service agreement
     */
    public function hasSignedServiceAgreement(): bool
    {
        return $this->getActiveServiceAgreement() !== null;
    }

    /**
     * Get service agreement start date
     */
    public function getServiceAgreementStartDate()
    {
        $agreement = $this->getActiveServiceAgreement();
        return $agreement ? $agreement->start_date : null;
    }

    /**
     * Get service agreement end date
     */
    public function getServiceAgreementEndDate()
    {
        $agreement = $this->getActiveServiceAgreement();
        return $agreement ? $agreement->end_date : null;
    }
}
