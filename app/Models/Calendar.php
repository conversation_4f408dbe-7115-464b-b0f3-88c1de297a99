<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Calendar extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'owner_id',
        'creator_id',
        'color',
        'is_public',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_public' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the owner of the calendar.
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Get the creator of the calendar.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * Get the events for the calendar.
     */
    public function events(): HasMany
    {
        return $this->hasMany(Event::class);
    }

    /**
     * Get the shares for the calendar.
     */
    public function shares(): HasMany
    {
        return $this->hasMany(CalendarShare::class);
    }

    /**
     * Get the users that the calendar is shared with.
     */
    public function sharedUsers()
    {
        return $this->belongsToMany(User::class, 'calendar_shares')
            ->withPivot('permission', 'shared_by')
            ->withTimestamps();
    }
}
