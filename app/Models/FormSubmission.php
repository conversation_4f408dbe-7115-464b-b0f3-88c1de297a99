<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use App\Models\Customer;
use App\Models\CustomerDiscount;
use App\Models\Activity;
use App\Models\FormSubmissionHtmlContent;
use Carbon\Carbon;

class FormSubmission extends Model
{
    protected $fillable = [
        'form_id',
        'customer_id',
        'status',
        'form_data',
        'submitter_name',
        'submitter_email',
        'submitter_phone',
        'submitter_contact',
        'signature_data',
        'signed_at',
        'ip_address',
        'user_agent',
        'reviewed_by',
        'reviewed_at',
        'review_notes',
        'approved_at',
        'approved_by',
        'metadata',
    ];

    protected $casts = [
        'form_data' => 'json',
        'metadata' => 'json',
        'signed_at' => 'datetime',
        'reviewed_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    const STATUS_SUBMITTED = 'submitted';
    const STATUS_REVIEWING = 'reviewing';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';

    const STATUSES = [
        self::STATUS_SUBMITTED => 'Pending Review',
        self::STATUS_REVIEWING => 'Under Review',
        self::STATUS_APPROVED => 'Approved',
        self::STATUS_REJECTED => 'Rejected',
    ];

    public function form(): BelongsTo
    {
        return $this->belongsTo(Form::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function htmlContent()
    {
        return $this->hasMany(FormSubmissionHtmlContent::class);
    }

    public function isApproved(): bool
    {
        return $this->status === self::STATUS_APPROVED;
    }

    public function isRejected(): bool
    {
        return $this->status === self::STATUS_REJECTED;
    }

    public function isPending(): bool
    {
        return $this->status === self::STATUS_SUBMITTED;
    }

    public function markAsReviewing(User $reviewer): void
    {
        $this->update([
            'status' => self::STATUS_REVIEWING,
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
        ]);
    }

    public function approve(User $approver, ?Customer $customer = null): void
    {
        $data = [
            'status' => self::STATUS_APPROVED,
            'approved_by' => $approver->id,
            'approved_at' => now(),
        ];

        if ($customer) {
            $data['customer_id'] = $customer->id;
        }

        $this->update($data);

        // If this form applies discount on approval, add the discount to the customer
        if ($this->form->applies_discount_on_approval && $this->customer_id && $this->form->discount_id) {
            $this->applyFormApprovalDiscount();
        }
    }

    public function reject(User $reviewer, ?string $notes = null): void
    {
        $this->update([
            'status' => self::STATUS_REJECTED,
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
            'review_notes' => $notes,
        ]);
    }

    public function applyFormApprovalDiscount(): void
    {
        $durationMonths = $this->form->getDiscountDurationMonths();
        $endDate = Carbon::now()->addMonths($durationMonths);

        // Create or update the customer discount
        CustomerDiscount::updateOrCreate(
            [
                'customer_id' => $this->customer_id,
                'discount_id' => $this->form->discount_id,
            ],
            [
                'start_date' => now(),
                'end_date' => $endDate,
                'maximum_uses' => null, // Unlimited for forms with approval discounts
                'usage_count' => 0,
            ]
        );

        // Log the activity
        if (class_exists('App\\Models\\Activity')) {
            Activity::create([
                'user_id' => $this->approved_by,
                'action' => 'form_approval_discount_applied',
                'description' => "Form approval discount applied to customer {$this->customer->name} for {$durationMonths} months",
                'model_type' => 'Customer',
                'model_id' => $this->customer_id,
            ]);
        }
    }

    public function getFormDataValue(string $fieldName, $default = null)
    {
        return data_get($this->form_data, $fieldName, $default);
    }

    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_SUBMITTED);
    }

    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    public function scopeForForm($query, $formId)
    {
        return $query->where('form_id', $formId);
    }

    public function getStatusBadgeClass(): string
    {
        return match ($this->status) {
            self::STATUS_SUBMITTED => 'badge-warning',
            self::STATUS_APPROVED => 'badge-success',
            self::STATUS_REJECTED => 'badge-error',
            default => 'badge-ghost',
        };
    }

    /**
     * Process HTML content with template variable replacements
     * Replaces template variables like {date_today_short}, {business_name}, etc.
     * with actual values from the submission data
     */
    public function processHtmlContent(string $content): string
    {
        // Get current date values
        $today = Carbon::now();
        $dateShort = $today->format('m/d/Y'); // USA format
        $dateFull = $today->format('F j, Y');
        $datePlusOneYear = $today->copy()->addYear()->format('F j, Y');
        
        // Get business name from form data or use submitter name
        $businessName = $this->getFormDataValue('business_name', $this->submitter_name);
        
        // Get full name - for business submissions, use submitter_contact (contact person name)
        // For residential submissions, use submitter_name (full name)
        $fullName = $this->submitter_contact ?: $this->submitter_name;
        
        // Replace date variables
        $content = str_replace('{date_today_short}', $dateShort, $content);
        $content = str_replace('{date_today_full}', $dateFull, $content);
        $content = str_replace('{date_plus_1yr_full}', $datePlusOneYear, $content);
        
        // Replace name variables
        $content = str_replace('{business_name}', $businessName, $content);
        $content = str_replace('{full_name}', $fullName, $content);
        $content = str_replace('{contact_person}', $this->submitter_contact ?: '', $content);
        
        // Replace submission metadata
        $content = str_replace('{submitter_name}', $this->submitter_name, $content);
        $content = str_replace('{submitter_contact}', $this->submitter_contact ?: '', $content);
        $content = str_replace('{submitter_email}', $this->submitter_email, $content);
        $content = str_replace('{submitter_phone}', $this->submitter_phone ?: '', $content);
        $content = str_replace('{submission_date}', $this->created_at->format('F j, Y'), $content);
        $content = str_replace('{submission_id}', $this->id, $content);
        
        // Replace any form field values using the field name
        if ($this->form_data && is_array($this->form_data)) {
            foreach ($this->form_data as $fieldName => $value) {
                if (!is_array($value) && !is_object($value)) {
                    $content = str_replace('{' . $fieldName . '}', (string) $value, $content);
                }
            }
        }
        
        return $content;
    }

    /**
     * Check if HTML content was saved with this submission
     */
    public function hasSavedHtmlContent(): bool
    {
        return $this->htmlContent()->exists();
    }

    /**
     * Get all saved HTML content from this submission
     */
    public function getSavedHtmlContent(): array
    {
        return $this->htmlContent()
            ->with('formField')
            ->get()
            ->mapWithKeys(function ($content) {
                return [$content->form_field_id => [
                    'field_name' => $content->field_name,
                    'field_label' => $content->field_label,
                    'original_content' => $content->original_content,
                    'processed_content' => $content->processed_content,
                    'processed_at' => $content->processed_at->toIso8601String(),
                ]];
            })
            ->toArray();
    }

    /**
     * Get saved HTML content for a specific field
     */
    public function getSavedHtmlContentForField(int $fieldId): ?array
    {
        $content = $this->htmlContent()
            ->where('form_field_id', $fieldId)
            ->first();
            
        if (!$content) {
            return null;
        }
        
        return [
            'field_name' => $content->field_name,
            'field_label' => $content->field_label,
            'original_content' => $content->original_content,
            'processed_content' => $content->processed_content,
            'processed_at' => $content->processed_at->toIso8601String(),
        ];
    }
}
