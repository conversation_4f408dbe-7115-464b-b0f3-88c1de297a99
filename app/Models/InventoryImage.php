<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryImage extends Model
{
    use HasFactory;

    protected $fillable = ['inventory_id', 'image_path', 'order', 'og_filename', 'image_id'];

    public function inventory()
    {
        return $this->belongsTo(Inventory::class);
    }

    public function image()
    {
        return $this->belongsTo(\App\Models\Image::class);
    }
}
