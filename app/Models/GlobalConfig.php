<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Schema;

class GlobalConfig extends Model
{
    protected $fillable = ['key', 'value'];

    /**
     * Get the value of a configuration item by key.
     *
     * @param string $key
     * @return mixed
     */
    public static function getValue(string $key)
    {
        // Check if table exists first
        if (!Schema::hasTable('global_configs')) {
            return null; // Return null during migration
        }
        
        try {
            $config = self::where('key', $key)->first();
            return $config ? $config->value : null;
        } catch (\Exception $e) {
            return null; // Return null if there's an error
        }
    }

    /**
     * Get the 'General Customer' ID.
     *
     * @return int|null
     */
    public static function getGeneralCustomerId(): ?int
    {
        $value = self::getValue('general_customer_id');
        return $value ? (int) $value : null;
    }

    /**
     * Decode a JSON configuration value into an array.
     *
     * @param string $key
     * @return array
     */
    public static function decodeValue(string $key): array
    {
        $value = self::getValue($key);
        return $value ? json_decode($value, true) : [];
    }

    /**
     * Set a configuration value and save it to the database.
     *
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public static function setValue(string $key, $value): bool
    {
        $config = self::firstOrNew(['key' => $key]);
        $config->value = is_array($value) ? json_encode($value) : $value;
        return $config->save();
    }

    /**
     * Check if a feature is enabled.
     *
     * @param string $feature
     * @return bool
     */
    public static function isFeatureEnabled(string $feature): bool
    {
        // Check if table exists first
        if (!Schema::hasTable('global_configs')) {
            return true; // Default to enabled during migration
        }
        
        try {
            return self::getValue($feature . '_enabled') === '1';
        } catch (\Exception $e) {
            return true; // Default to enabled if there's an error
        }
    }

    /**
     * Check if the tasks feature is enabled.
     *
     * @return bool
     */
public static function isTasksEnabled()
{
    // Check if table exists first
    if (!Schema::hasTable('global_configs')) {
        return true; // Default to enabled during migration
    }
    
    try {
        return self::getValue('tasks_enabled') === '1';
    } catch (\Exception $e) {
        return true; // Default to enabled if there's an error
    }
}

    /**
     * Get the application time zone.
     * Now uses Laravel's app.timezone config for consistency.
     *
     * @return string
     */
    public static function getTimeZone(): string
    {
        // Simplified to use Laravel's timezone config exclusively
        // This ensures consistency with Laravel's automatic timezone handling
        return config('app.timezone');
    }

    /**
     * Get the reCAPTCHA site key.
     *
     * @return string|null
     */
    public static function getRecaptchaSiteKey(): ?string
    {
        return config('services.recaptcha.site_key');
    }

    /**
     * Get the reCAPTCHA secret key.
     *
     * @return string|null
     */
    public static function getRecaptchaSecretKey(): ?string
    {
        return config('services.recaptcha.secret_key');
    }

    /**
     * Check if reCAPTCHA is configured.
     *
     * @return bool
     */
    public static function isRecaptchaConfigured(): bool
    {
        return !empty(self::getRecaptchaSiteKey()) && !empty(self::getRecaptchaSecretKey());
    }

    /**
     * Get the warehouse location address.
     *
     * @return string|null
     */
    public static function getWarehouseLocation(): ?string
    {
        return self::getValue('warehouse_location');
    }

    /**
     * Set the warehouse location address.
     *
     * @param string|null $location
     * @return bool
     */
    public static function setWarehouseLocation(?string $location): bool
    {
        return self::setValue('warehouse_location', $location);
    }

    /**
     * Get the pickup notification user groups.
     *
     * @return array
     */
    public static function getPickupNotificationUserGroups(): array
    {
        return self::decodeValue('pickup_notification_user_groups');
    }

    /**
     * Set the pickup notification user groups.
     *
     * @param array $userGroupIds
     * @return bool
     */
    public static function setPickupNotificationUserGroups(array $userGroupIds): bool
    {
        return self::setValue('pickup_notification_user_groups', $userGroupIds);
    }

    /**
     * Get the pickup scheduling notification email addresses.
     *
     * @return array
     */
    public static function getPickupSchedulingNotificationEmails(): array
    {
        $value = self::getValue('pickup_scheduling_notification_emails');
        if (empty($value)) {
            return [];
        }

        // Split by commas and/or newlines, trim whitespace, and filter out empty values
        $emails = preg_split('/[,\n]+/', $value);
        $emails = array_map('trim', $emails);
        $emails = array_filter($emails, function($email) {
            return !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
        });

        return array_values($emails); // Re-index array
    }

    /**
     * Get the minimum break duration in minutes.
     *
     * @return int
     */
    public static function getMinBreakDuration(): int
    {
        $value = self::getValue('min_break_duration');
        // Default to 30 minutes if not set or invalid
        return $value && is_numeric($value) ? (int) $value : 30;
    }

    /**
     * Get the timeclock report email address.
     *
     * @return string|null
     */
    public static function getTimeclockReportEmail(): ?string
    {
        return self::getValue('timeclock_report_email');
    }

    /**
     * Get the timeclock report time.
     *
     * @return string
     */
    public static function getTimeclockReportTime(): string
    {
        $value = self::getValue('timeclock_report_time');
        // Default to 7:00 AM if not set
        return $value ?: '07:00';
    }

    /**
     * Get the pickup calendar ID.
     *
     * @return int|null
     */
    public static function getPickupCalendarId(): ?int
    {
        $value = self::getValue('pickup_calendar_id');
        return $value ? (int) $value : null;
    }

    /**
     * Get the pickup availability windows.
     *
     * @return array
     */
    public static function getPickupAvailabilityWindows(): array
    {
        return self::decodeValue('pickup_availability_windows');
    }

    /**
     * Set the pickup availability windows.
     *
     * @param array $windows
     * @return bool
     */
    public static function setPickupAvailabilityWindows(array $windows): bool
    {
        return self::setValue('pickup_availability_windows', $windows);
    }

    /**
     * Check if a given day and time is within pickup availability windows.
     *
     * @param string $dayOfWeek (0=Sunday, 1=Monday, ..., 6=Saturday)
     * @param string $time (HH:MM format)
     * @return bool
     */
    public static function isPickupTimeAvailable(string $dayOfWeek, string $time): bool
    {
        $windows = self::getPickupAvailabilityWindows();

        if (empty($windows) || !isset($windows[$dayOfWeek])) {
            return true; // If no windows configured, allow all times
        }

        $dayWindows = $windows[$dayOfWeek];
        if (empty($dayWindows)) {
            return false; // No windows for this day means no availability
        }

        foreach ($dayWindows as $window) {
            if (isset($window['start']) && isset($window['end'])) {
                if ($time >= $window['start'] && $time <= $window['end']) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get the default pickup event duration in minutes.
     *
     * @return int
     */
    public static function getPickupEventDuration(): int
    {
        $value = self::getValue('pickup_event_duration');
        return $value && is_numeric($value) ? (int) $value : 60; // Default to 60 minutes
    }

    /**
     * Get the pickup scheduling interval in minutes.
     *
     * @return int
     */
    public static function getPickupSchedulingInterval(): int
    {
        $value = self::getValue('pickup_scheduling_interval');
        return $value && is_numeric($value) ? (int) $value : 30; // Default to 30 minutes
    }

    /**
     * Get the pickup lead time in days.
     *
     * @return int
     */
    public static function getPickupLeadTimeDays(): int
    {
        $value = self::getValue('pickup_lead_time_days');
        return $value && is_numeric($value) ? (int) $value : 1; // Default to 1 day
    }

    /**
     * Get the pickup lead time in hours.
     *
     * @return int
     */
    public static function getPickupLeadTimeHours(): int
    {
        $value = self::getValue('pickup_lead_time_hours');
        return $value && is_numeric($value) ? (int) $value : 0; // Default to 0 hours
    }

    /**
     * Get whether pickup lead time should count business days only.
     *
     * @return bool
     */
    public static function getPickupLeadTimeBusinessDaysOnly(): bool
    {
        $value = self::getValue('pickup_lead_time_business_days_only');
        return $value === '1' || $value === 1 || $value === true; // Default to true (business days only)
    }

    /**
     * Get the pickup buffer time in minutes.
     *
     * @return int
     */
    public static function getPickupBufferTime(): int
    {
        $value = self::getValue('pickup_buffer_time');
        return $value && is_numeric($value) ? (int) $value : 0; // Default to 0 minutes (no buffer)
    }

    /**
     * Get the pickup buffer direction.
     *
     * @return string
     */
    public static function getPickupBufferDirection(): string
    {
        $value = self::getValue('pickup_buffer_direction');
        return in_array($value, ['after', 'before', 'both']) ? $value : 'after'; // Default to 'after'
    }

    /**
     * Calculate the minimum allowed pickup date based on lead time settings.
     *
     * @return \Carbon\Carbon
     */
    public static function getMinimumPickupDate(): \Carbon\Carbon
    {
        $leadTimeDays = self::getPickupLeadTimeDays();
        $leadTimeHours = self::getPickupLeadTimeHours();
        $businessDaysOnly = self::getPickupLeadTimeBusinessDaysOnly();
        $timezone = self::getTimeZone();

        $now = \Carbon\Carbon::now($timezone);
        $minimumDate = $now->copy()->addHours($leadTimeHours);

        if ($businessDaysOnly && $leadTimeDays > 0) {
            // Add business days only (Monday = 1, Friday = 5)
            $daysAdded = 0;
            $currentDate = $minimumDate->copy();

            while ($daysAdded < $leadTimeDays) {
                $currentDate->addDay();
                // If it's a weekday (Monday-Friday), count it
                if ($currentDate->isWeekday()) {
                    $daysAdded++;
                }
            }

            $minimumDate = $currentDate;
        } else {
            // Add calendar days
            $minimumDate->addDays($leadTimeDays);
        }

        // Convert to UTC for consistent comparisons with stored datetimes
        return $minimumDate->utc();
    }

    /**
     * Get available pickup time slots for a given day and availability windows.
     *
     * @param string $dayOfWeek (0=Sunday, 1=Monday, ..., 6=Saturday)
     * @param array $windows Optional windows array, will fetch from config if not provided
     * @return array Array of time slots in HH:MM format
     */
    public static function getPickupTimeSlots(string $dayOfWeek, array $windows = null): array
    {
        if ($windows === null) {
            $windows = self::getPickupAvailabilityWindows();
        }

        $slots = [];
        $interval = self::getPickupSchedulingInterval();
        $duration = self::getPickupEventDuration();

        if (empty($windows) || !isset($windows[$dayOfWeek])) {
            return $slots; // No windows configured for this day
        }

        $dayWindows = $windows[$dayOfWeek];
        if (empty($dayWindows)) {
            return $slots; // No windows for this day
        }

        foreach ($dayWindows as $window) {
            if (!isset($window['start']) || !isset($window['end'])) {
                continue;
            }

            $startTime = strtotime($window['start']);
            $endTime = strtotime($window['end']);

            // Generate slots at the specified interval
            $currentTime = $startTime;
            while ($currentTime <= $endTime) {
                // Check if the event would fit within the window
                $eventEndTime = $currentTime + ($duration * 60); // Convert duration to seconds

                if ($eventEndTime <= $endTime) {
                    $slots[] = date('H:i', $currentTime);
                }

                $currentTime += ($interval * 60); // Convert interval to seconds
            }
        }

        return array_unique($slots);
    }

    /**
     * Check if auto clock-out is enabled.
     *
     * @return bool
     */
    public static function isAutoClockOutEnabled(): bool
    {
        $value = self::getValue('timeclock_auto_clock_out_enabled');
        return $value === '1' || $value === 1 || $value === true;
    }

    /**
     * Get the auto clock-out time.
     *
     * @return string
     */
    public static function getAutoClockOutTime(): string
    {
        $value = self::getValue('timeclock_auto_clock_out_time');
        // Default to 11:59 PM if not set
        return $value ?: '23:59';
    }

    /**
     * Set auto clock-out enabled status.
     *
     * @param bool $enabled
     * @return bool
     */
    public static function setAutoClockOutEnabled(bool $enabled): bool
    {
        return self::setValue('timeclock_auto_clock_out_enabled', $enabled ? '1' : '0');
    }

    /**
     * Set the auto clock-out time.
     *
     * @param string $time HH:MM format
     * @return bool
     */
    public static function setAutoClockOutTime(string $time): bool
    {
        // Validate time format
        if (!preg_match('/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/', $time)) {
            throw new \InvalidArgumentException('Time must be in HH:MM format');
        }
        return self::setValue('timeclock_auto_clock_out_time', $time);
    }

    /**
     * Get the pickup request contact email.
     *
     * @return string|null
     */
    public static function getPickupContactEmail(): ?string
    {
        return self::getValue('pickup_contact_email');
    }

    /**
     * Set the pickup request contact email.
     *
     * @param string|null $email
     * @return bool
     */
    public static function setPickupContactEmail(?string $email): bool
    {
        return self::setValue('pickup_contact_email', $email);
    }

    /**
     * Get the pickup request contact phone.
     *
     * @return string|null
     */
    public static function getPickupContactPhone(): ?string
    {
        return self::getValue('pickup_contact_phone');
    }

    /**
     * Set the pickup request contact phone.
     *
     * @param string|null $phone
     * @return bool
     */
    public static function setPickupContactPhone(?string $phone): bool
    {
        return self::setValue('pickup_contact_phone', $phone);
    }

    /**
     * Get the pickup request website link.
     *
     * @return string|null
     */
    public static function getPickupWebsiteLink(): ?string
    {
        return self::getValue('pickup_website_link');
    }

    /**
     * Set the pickup request website link.
     *
     * @param string|null $link
     * @return bool
     */
    public static function setPickupWebsiteLink(?string $link): bool
    {
        return self::setValue('pickup_website_link', $link);
    }

    /**
     * Get the pickup item types configuration.
     *
     * @return array
     */
    public static function getPickupItemTypes(): array
    {
        return self::decodeValue('pickup_item_types');
    }

    /**
     * Set the pickup item types configuration.
     *
     * @param array $itemTypes
     * @return bool
     */
    public static function setPickupItemTypes(array $itemTypes): bool
    {
        return self::setValue('pickup_item_types', $itemTypes);
    }

    /**
     * Get the default pickup item types configuration.
     *
     * @return array
     */
    public static function getDefaultPickupItemTypes(): array
    {
        return [
            [
                'name' => 'Small Electronics',
                'value' => 'small_electronics',
                'icon' => 'fa-sharp fa-microchip',
                'notes' => 'Phones, tablets, small devices, cables, and accessories'
            ],
            [
                'name' => 'Appliances',
                'value' => 'appliances',
                'icon' => 'fa-sharp fa-blender',
                'notes' => 'Small household appliances like microwaves, toasters, coffee makers'
            ],
            [
                'name' => 'Peripheral Devices',
                'value' => 'peripheral_devices',
                'icon' => 'fa-sharp fa-keyboard',
                'notes' => 'Keyboards, mice, printers, scanners, and other computer peripherals'
            ],
            [
                'name' => 'Batteries',
                'value' => 'batteries',
                'icon' => 'fa-sharp fa-battery-full',
                'notes' => 'All types of batteries - automotive, UPS, rechargeable, etc.'
            ],
            [
                'name' => 'CRT TVs',
                'value' => 'crt_tvs',
                'icon' => 'fa-sharp fa-tv-retro',
                'notes' => 'CRT TVs must not be cracked! We cannot accept damaged CRT screens due to lead content.'
            ],
            [
                'name' => 'Flatscreen TV(s)',
                'value' => 'flatscreen_tvs',
                'icon' => 'fa-sharp fa-tv',
                'notes' => 'LCD, LED, OLED, and plasma flatscreen televisions and monitors'
            ],
            [
                'name' => 'Tote Swap',
                'value' => 'tote_swap',
                'icon' => 'fa-sharp fa-box',
                'notes' => 'Exchange full totes for empty ones - please have items ready for pickup'
            ],
            [
                'name' => 'Gaylord Swap',
                'value' => 'gaylord_swap',
                'icon' => 'fa-sharp fa-pallet',
                'notes' => 'Large cardboard box exchange - requires forklift or pallet jack access'
            ],
            [
                'name' => 'Servers',
                'value' => 'servers',
                'icon' => 'fa-sharp fa-server',
                'notes' => 'Rack-mounted servers, tower servers, and networking equipment'
            ],
            [
                'name' => 'Laptops',
                'value' => 'laptops',
                'icon' => 'fa-sharp fa-laptop',
                'notes' => 'Portable computers, notebooks, and mobile workstations'
            ],
            [
                'name' => 'Desktops',
                'value' => 'desktops',
                'icon' => 'fa-sharp fa-desktop',
                'notes' => 'Desktop computers, towers, and all-in-one PCs'
            ],
            [
                'name' => 'Large Appliances',
                'value' => 'large_appliances',
                'icon' => 'fa-sharp fa-washer',
                'notes' => 'Refrigerators, washers, dryers, and other large household appliances'
            ],
            [
                'name' => 'Other',
                'value' => 'other',
                'icon' => 'fa-sharp fa-ellipsis',
                'notes' => 'Items not covered by other categories - please specify in item details'
            ]
        ];
    }
}
