<?php

namespace App\Models;

use App\Traits\HasNotificationConfig;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
use Carbon\Carbon;

class Form extends Model
{
    use HasNotificationConfig;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'is_active',
        'requires_signature',
        'requires_customer_link',
        'requires_approval',
        'applies_discount_on_approval',
        'generate_pdf_on_approval',
        'save_html_content',
        'recaptcha_enabled',
        'thankyou_show_submission_details',
        'thankyou_show_html_message',
        'thankyou_html_message',
        'thankyou_show_need_help',
        'thankyou_enable_print',
        'thankyou_enable_close',
        'required_contact_fields',
        'settings',
        'enable_email_notifications',
        'notification_emails',
        'include_full_data_in_email',
        'discount_id',
        'agreement_duration_months',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'requires_signature' => 'boolean',
        'requires_customer_link' => 'boolean',
        'requires_approval' => 'boolean',
        'applies_discount_on_approval' => 'boolean',
        'generate_pdf_on_approval' => 'boolean',
        'save_html_content' => 'boolean',
        'recaptcha_enabled' => 'boolean',
        'thankyou_show_submission_details' => 'boolean',
        'thankyou_show_html_message' => 'boolean',
        'thankyou_show_need_help' => 'boolean',
        'thankyou_enable_print' => 'boolean',
        'thankyou_enable_close' => 'boolean',
        'required_contact_fields' => 'json',
        'settings' => 'json',
        'enable_email_notifications' => 'boolean',
        'notification_emails' => 'json',
        'include_full_data_in_email' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($form) {
            if (empty($form->slug)) {
                $form->slug = Str::slug($form->name);
                
                // Ensure unique slug
                $count = 1;
                $originalSlug = $form->slug;
                while (static::where('slug', $form->slug)->exists()) {
                    $form->slug = $originalSlug . '-' . $count++;
                }
            }
        });
    }

    public function fields(): HasMany
    {
        return $this->hasMany(FormField::class)->orderBy('order');
    }

    public function submissions(): HasMany
    {
        return $this->hasMany(FormSubmission::class);
    }

    public function discount(): BelongsTo
    {
        return $this->belongsTo(Discount::class);
    }

    public function getPublicUrl(): string
    {
        return route('forms.public.show', $this->slug);
    }

    public function getDiscountDurationMonths(): int
    {
        if ($this->agreement_duration_months) {
            return $this->agreement_duration_months;
        }

        // Get from global config if not set on form
        $configValue = \App\Models\GlobalConfig::getValue('service_agreement_duration_months');
        return $configValue ? (int) $configValue : 12; // Default to 12 months
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeRequiresApproval($query)
    {
        return $query->where('requires_approval', true);
    }

    public function scopeRequiresCustomerLink($query)
    {
        return $query->where('requires_customer_link', true);
    }

    /**
     * Get the required contact fields for this form
     */
    public function getRequiredContactFields(): array
    {
        $defaults = [
            'name' => true,           // Full name for the submitter
            'business_name' => true,  // Business name (optional, but shown by default)
            'email' => true,          // Email address
            'phone' => false,         // Phone number
            'address' => false,       // Physical address
        ];

        if (!$this->required_contact_fields) {
            return $defaults;
        }

        return array_merge($defaults, $this->required_contact_fields);
    }

    /**
     * Check if a specific contact field is required
     */
    public function isContactFieldRequired(string $field): bool
    {
        $fields = $this->getRequiredContactFields();
        return $fields[$field] ?? false;
    }

    /**
     * Get notification emails for this form
     */
    public function getNotificationEmails(): array
    {
        if (!$this->enable_email_notifications) {
            return [];
        }

        $emails = $this->notification_emails ?? [];

        // Filter out empty emails and validate
        return array_filter($emails, function($email) {
            return !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
        });
    }

    /**
     * Check if form has email notifications enabled
     */
    public function hasEmailNotificationsEnabled(): bool
    {
        return $this->enable_email_notifications && !empty($this->getNotificationEmails());
    }

    /**
     * Process HTML fields with template variables for preview
     * Used in form builder and preview modes
     */
    public function processHtmlFieldsForPreview(): array
    {
        $processedFields = [];
        $today = now();
        
        foreach ($this->fields as $field) {
            if ($field->type === 'html') {
                $content = $field->content;
                
                // Replace date variables
                $content = str_replace('{date_today_short}', $today->format('m/d/Y'), $content);
                $content = str_replace('{date_today_full}', $today->format('F j, Y'), $content);
                $content = str_replace('{date_plus_1yr_full}', $today->addYear()->format('F j, Y'), $content);
                
                // Replace name variables with placeholders
                $content = str_replace('{business_name}', '<span class="text-info">BUSINESS_NAME</span>', $content);
                $content = str_replace('{full_name}', '<span class="text-info">YOUR_NAME</span>', $content);
                
                $processedFields[$field->id] = $content;
            }
        }
        
        return $processedFields;
    }
}
