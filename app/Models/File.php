<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class File extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'filename',
        'original_filename',
        'filepath',
        'mime_type',
        'size',
        'extension',
        'description',
        'uploaded_by',
        'fileable_id',
        'fileable_type',
        'metadata',
        'is_public',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_public' => 'boolean',
        'size' => 'integer',
    ];

    protected $appends = [
        'human_readable_size',
        'icon',
        'is_image',
    ];

    /**
     * Get the parent fileable model (polymorphic).
     */
    public function fileable()
    {
        return $this->morphTo();
    }

    /**
     * Get the user who uploaded the file.
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the full URL for the file
     *
     * @return string|null
     */
    public function getUrl()
    {
        if ($this->is_public) {
            return Storage::disk('public')->url($this->filepath);
        }

        // For non-public files, generate a temporary URL
        return $this->getTemporaryUrl();
    }

    /**
     * Get a temporary URL for the file (valid for 5 minutes by default)
     *
     * @param int $minutes
     * @return string|null
     */
    public function getTemporaryUrl($minutes = 5)
    {
        // For local storage, we can't generate a temporary URL, so we'll use a route
        // that checks permissions and serves the file
        return route('files.view', $this);
    }

    /**
     * Check if the current user has permission to access this file
     *
     * @return bool
     */
    public function userCanAccess()
    {
        // Public files are accessible to everyone
        if ($this->is_public) {
            return true;
        }

        // Admin users can access all files
        if (Auth::user() && Auth::user()->isAdmin()) {
            return true;
        }

        // File uploaders can access their own files
        if (Auth::id() === $this->uploaded_by) {
            return true;
        }

        // Check if the parent model has a specific permission method
        $parent = $this->fileable;
        if ($parent && method_exists($parent, 'userCanAccessFiles')) {
            return $parent->userCanAccessFiles(Auth::user());
        }

        // Default to false for security
        return false;
    }

    /**
     * Get the file's icon based on its extension with color
     *
     * @return string
     */
    public function getIconAttribute()
    {
        return match($this->extension) {
            'pdf' => 'fa-file-pdf text-red-500',
            'jpg', 'jpeg', 'png', 'gif', 'webp' => 'fa-file-image text-blue-500',
            'doc', 'docx' => 'fa-file-word text-blue-700',
            'xls', 'xlsx' => 'fa-file-excel text-green-700',
            'ppt', 'pptx' => 'fa-file-powerpoint text-orange-600',
            'zip', 'rar', '7z' => 'fa-file-archive text-yellow-700',
            'txt' => 'fa-file-lines text-gray-700',
            default => 'fa-file text-gray-500',
        };
    }

    /**
     * Get human-readable file size
     *
     * @return string
     */
    public function getHumanReadableSizeAttribute()
    {
        $bytes = $this->size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $factor = floor((strlen($bytes) - 1) / 3);
        return sprintf("%.2f", $bytes / pow(1024, $factor)) . ' ' . $units[$factor];
    }

    /**
     * Check if the file is an image
     */
    public function getIsImageAttribute()
    {
        return in_array($this->extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
    }

    /**
     * Get thumbnail URL for image files
     *
     * @param int $width Width of the thumbnail
     * @param int $height Height of the thumbnail
     * @return string|null URL of the thumbnail or null if not an image
     */
    public function getThumbnailUrl($width = 100, $height = 100)
    {
        if (!$this->is_image) {
            return null;
        }

        // Use the dedicated thumbnail route for better caching and performance
        return route('files.thumbnail', ['file' => $this, 'width' => $width, 'height' => $height]);
    }

    /**
     * Delete the file from storage when the model is deleted
     */
    protected static function booted()
    {
        static::deleting(function ($file) {
            // Only delete the actual file on force delete
            if ($file->isForceDeleting()) {
                Storage::delete($file->filepath);
            }
        });
    }
}
