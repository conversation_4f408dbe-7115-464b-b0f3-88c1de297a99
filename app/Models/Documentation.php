<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Documentation extends Model
{
    use HasFactory, SoftDeletes;

    // Specify the table name
    protected $table = 'documentation';
    
    // Allow mass assignment for the following attributes
    protected $fillable = ['title', 'content', 'short_desc'];
}
