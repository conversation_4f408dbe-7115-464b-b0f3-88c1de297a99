<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;

class DailyTimeclockReport extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The report date.
     *
     * @var Carbon
     */
    public $reportDate;

    /**
     * The report data.
     *
     * @var array
     */
    public $reportData;

    /**
     * The total hours worked.
     *
     * @var float
     */
    public $totalHoursWorked;

    /**
     * The total break hours.
     *
     * @var float
     */
    public $totalBreakHours;

    /**
     * Create a new message instance.
     */
    public function __construct(Carbon $reportDate, array $reportData, float $totalHoursWorked, float $totalBreakHours)
    {
        $this->reportDate = $reportDate;
        $this->reportData = $reportData;
        $this->totalHoursWorked = $totalHoursWorked;
        $this->totalBreakHours = $totalBreakHours;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Daily Timeclock Report - ' . $this->reportDate->format('M d, Y'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.timeclock.daily-report',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
