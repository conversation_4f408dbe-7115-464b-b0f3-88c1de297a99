<?php

namespace App\Mail;

use App\Services\EmailTemplateService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class TemplatedMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    protected string $templateName;
    protected array $templateData;
    protected ?array $renderedTemplate;

    /**
     * Create a new message instance.
     */
    public function __construct(string $templateName, array $data = [])
    {
        $this->templateName = $templateName;
        $this->templateData = $data;
        
        // Render the template
        $emailTemplateService = app(EmailTemplateService::class);
        $this->renderedTemplate = $emailTemplateService->renderTemplate($templateName, $data);
        
        if (!$this->renderedTemplate) {
            Log::error("Failed to render email template: {$templateName}");
        }
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->renderedTemplate['subject'] ?? 'Email from ' . config('app.name');

        $envelope = new Envelope(subject: $subject);

        // Set custom from if specified in template
        if (!empty($this->renderedTemplate['from_email'])) {
            $envelope->from(
                $this->renderedTemplate['from_email'],
                $this->renderedTemplate['from_name'] ?? null
            );
        } elseif (!empty($this->renderedTemplate['from_name'])) {
            $envelope->from(
                config('mail.from.address'),
                $this->renderedTemplate['from_name']
            );
        } else {
            // Always ensure we have a from address as fallback
            $envelope->from(
                config('mail.from.address', '<EMAIL>'),
                config('mail.from.name', config('app.name'))
            );
        }

        return $envelope;
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        if (!$this->renderedTemplate) {
            // Fallback to a basic error template
            return new Content(
                view: 'emails.template-error',
                with: [
                    'templateName' => $this->templateName,
                    'error' => 'Template not found or failed to render'
                ]
            );
        }

        return new Content(
            view: 'emails.templated-mail',
            with: [
                'htmlContent' => $this->renderedTemplate['body']
            ]
        );
    }

    /**
     * Get the attachments for the message.
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Check if template was successfully rendered
     */
    public function isRendered(): bool
    {
        return $this->renderedTemplate !== null;
    }

    /**
     * Get the rendered template data (for debugging)
     */
    public function getRenderedTemplate(): ?array
    {
        return $this->renderedTemplate;
    }
}