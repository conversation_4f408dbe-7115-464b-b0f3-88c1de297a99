<?php

namespace App\Mail;

use App\Models\PickupRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PickupRequestNotification extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $pickupRequest;
    public $title;
    public $type;

    /**
     * Create a new message instance.
     */
    public function __construct(array $data)
    {
        $this->pickupRequest = $data['pickupRequest'];
        $this->title = $data['title'];
        $this->type = $data['type'];
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->title,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.pickup-request-notification',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}