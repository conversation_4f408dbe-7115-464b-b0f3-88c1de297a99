<?php

namespace App\Jobs;

use App\Models\PickupRequest;
use App\Services\EmailTemplateService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendPickupConfirmationReminder implements ShouldQueue
{
    use Queueable;

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Process pending pickups (need confirmation)
        $this->processPendingPickups();
        
        // Process confirmed pickups (final reminder)
        $this->processConfirmedPickups();
    }

    /**
     * Process pending pickup requests that need customer confirmation.
     */
    private function processPendingPickups(): void
    {
        // Get pickup requests that:
        // 1. Are in 'pending' status (need customer confirmation)
        // 2. Have an associated event scheduled within the next 48 hours
        // 3. Haven't already had a reminder sent
        $pickupRequests = PickupRequest::with(['event', 'customer'])
            ->where('status', 'pending')
            ->whereNull('reminder_sent_at')
            ->whereNull('confirmed_at')
            ->whereNull('cancelled_at')
            ->whereHas('event', function ($query) {
                $query->whereBetween('start_date', [
                    now()->addHours(47)->startOfHour(),
                    now()->addHours(49)->endOfHour()
                ]);
            })
            ->get();

        foreach ($pickupRequests as $pickupRequest) {
            try {
                // Send the confirmation reminder email
                $this->sendReminderEmail($pickupRequest, 'pickup_confirmation_reminder');
                
                // Record that we sent the reminder
                $this->recordReminderSent($pickupRequest);
                
                // Log the reminder sent event
                $pickupRequest->logReminderSent();
                
                Log::info("48-hour pickup confirmation reminder sent", [
                    'pickup_request_id' => $pickupRequest->id,
                    'customer_email' => $pickupRequest->email,
                    'pickup_date' => $pickupRequest->event->start_date,
                    'type' => 'confirmation_reminder'
                ]);
                
            } catch (\Exception $e) {
                Log::error("Failed to send pickup confirmation reminder", [
                    'pickup_request_id' => $pickupRequest->id,
                    'error' => $e->getMessage(),
                    'type' => 'confirmation_reminder'
                ]);
            }
        }
    }

    /**
     * Process confirmed pickup requests that need final reminder.
     */
    private function processConfirmedPickups(): void
    {
        // Get pickup requests that:
        // 1. Are in 'confirmed' status (customer already confirmed)
        // 2. Have an associated event scheduled within the next 48 hours
        // 3. Haven't already had a final reminder sent
        $pickupRequests = PickupRequest::with(['event', 'customer'])
            ->where('status', 'confirmed')
            ->whereNotNull('confirmed_at')
            ->whereNull('final_reminder_sent_at')
            ->whereNull('cancelled_at')
            ->whereHas('event', function ($query) {
                $query->whereBetween('start_date', [
                    now()->addHours(47)->startOfHour(),
                    now()->addHours(49)->endOfHour()
                ]);
            })
            ->get();

        foreach ($pickupRequests as $pickupRequest) {
            try {
                // Send the final reminder email
                $this->sendReminderEmail($pickupRequest, 'pickup_final_reminder');
                
                // Record that we sent the final reminder
                $this->recordFinalReminderSent($pickupRequest);
                
                // Log the final reminder sent event
                $pickupRequest->logFinalReminderSent();
                
                Log::info("48-hour pickup final reminder sent", [
                    'pickup_request_id' => $pickupRequest->id,
                    'customer_email' => $pickupRequest->email,
                    'pickup_date' => $pickupRequest->event->start_date,
                    'type' => 'final_reminder'
                ]);
                
            } catch (\Exception $e) {
                Log::error("Failed to send pickup final reminder", [
                    'pickup_request_id' => $pickupRequest->id,
                    'error' => $e->getMessage(),
                    'type' => 'final_reminder'
                ]);
            }
        }
    }

    /**
     * Check if a 48-hour reminder has already been sent.
     */
    private function hasReminderBeenSent(PickupRequest $pickupRequest): bool
    {
        // Check the new reminder_sent_at field first, fall back to JSON field
        return $pickupRequest->reminder_sent_at !== null || 
               isset($pickupRequest->pickup_details['email_communications']['48_hour_reminder']);
    }

    /**
     * Send the reminder email to the customer.
     */
    private function sendReminderEmail(PickupRequest $pickupRequest, string $templateName): void
    {
        $emailTemplateService = new EmailTemplateService();
        
        // Prepare data for the email template
        $templateData = [
            'pickup_request' => $pickupRequest,
        ];

        // Render the email template
        $renderedEmail = $emailTemplateService->renderTemplate($templateName, $templateData);
        
        if (!$renderedEmail) {
            Log::error('Failed to render pickup reminder email template', [
                'pickup_request_id' => $pickupRequest->id,
                'template_name' => $templateName
            ]);
            return;
        }

        // Send the email using the rendered template
        Mail::html($renderedEmail['body'], function ($message) use ($pickupRequest, $renderedEmail) {
            $message->to($pickupRequest->email, $pickupRequest->contact_name)
                    ->subject($renderedEmail['subject']);
                    
            // Use template's from settings if available, otherwise use config defaults
            if ($renderedEmail['from_email'] && $renderedEmail['from_name']) {
                $message->from($renderedEmail['from_email'], $renderedEmail['from_name']);
            } else {
                $message->from(config('mail.from.address'), config('app.name'));
            }
        });
    }

    /**
     * Record that a 48-hour reminder was sent.
     */
    private function recordReminderSent(PickupRequest $pickupRequest): void
    {
        // Update both new fields and maintain JSON for backward compatibility
        $pickupRequest->reminder_sent_at = now();
        
        // Generate confirmation token if not already set
        if (!$pickupRequest->confirmation_token) {
            $pickupRequest->confirmation_token = \Illuminate\Support\Str::random(64);
        }
        
        $currentDetails = $pickupRequest->pickup_details ?? [];

        if (!isset($currentDetails['email_communications'])) {
            $currentDetails['email_communications'] = [];
        }

        $currentDetails['email_communications']['48_hour_reminder'] = [
            'sent_at' => now()->toISOString(),
            'sent_to' => $pickupRequest->email,
            'pickup_date' => $pickupRequest->event->start_date->toISOString(),
            'status' => 'sent'
        ];

        $pickupRequest->pickup_details = $currentDetails;
        $pickupRequest->save();
    }

    /**
     * Record that a final reminder was sent.
     */
    private function recordFinalReminderSent(PickupRequest $pickupRequest): void
    {
        // Update the final reminder field
        $pickupRequest->final_reminder_sent_at = now();
        
        $currentDetails = $pickupRequest->pickup_details ?? [];

        if (!isset($currentDetails['email_communications'])) {
            $currentDetails['email_communications'] = [];
        }

        $currentDetails['email_communications']['48_hour_final_reminder'] = [
            'sent_at' => now()->toISOString(),
            'sent_to' => $pickupRequest->email,
            'pickup_date' => $pickupRequest->event->start_date->toISOString(),
            'status' => 'sent'
        ];

        $pickupRequest->pickup_details = $currentDetails;
        $pickupRequest->save();
    }
}
