<?php

namespace App\Jobs;

use App\Models\Event;
use App\Models\MicrosoftCalendarIntegration;
use App\Services\PickupCalendarSyncService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncPickupEventsToMicrosoft implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    private ?MicrosoftCalendarIntegration $integration;
    private ?Event $event;
    private string $syncType;
    private ?\DateTime $since;

    /**
     * Create a new job instance for full sync.
     */
    public function __construct(?MicrosoftCalendarIntegration $integration = null, ?Event $event = null, string $syncType = 'full', ?\DateTime $since = null)
    {
        $this->integration = $integration;
        $this->event = $event;
        $this->syncType = $syncType;
        $this->since = $since;
    }

    /**
     * Execute the job.
     */
    public function handle(PickupCalendarSyncService $syncService): void
    {
        try {
            Log::info('Starting Microsoft calendar sync job', [
                'sync_type' => $this->syncType,
                'integration_id' => $this->integration?->id,
                'event_id' => $this->event?->id,
                'since' => $this->since?->format('Y-m-d H:i:s')
            ]);

            switch ($this->syncType) {
                case 'single_event':
                    $this->handleSingleEventSync($syncService);
                    break;
                
                case 'single_integration':
                    $this->handleSingleIntegrationSync($syncService);
                    break;
                
                case 'full':
                default:
                    $this->handleFullSync($syncService);
                    break;
            }

        } catch (\Exception $e) {
            Log::error('Microsoft calendar sync job failed', [
                'sync_type' => $this->syncType,
                'integration_id' => $this->integration?->id,
                'event_id' => $this->event?->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Update integration status if specific integration failed
            if ($this->integration) {
                $this->integration->update([
                    'sync_status' => 'failed',
                    'error_message' => 'Sync job failed: ' . $e->getMessage()
                ]);
            }

            throw $e;
        }
    }

    /**
     * Handle syncing a single event across all integrations.
     */
    private function handleSingleEventSync(PickupCalendarSyncService $syncService): void
    {
        if (!$this->event) {
            throw new \InvalidArgumentException('Event is required for single event sync');
        }

        Log::info('Syncing single event to Microsoft calendars', [
            'event_id' => $this->event->id,
            'event_title' => $this->event->title
        ]);

        $results = $syncService->syncEvent($this->event);
        
        $successCount = count(array_filter($results, fn($r) => $r['success']));
        $totalCount = count($results);

        Log::info('Single event sync completed', [
            'event_id' => $this->event->id,
            'success_count' => $successCount,
            'total_count' => $totalCount,
            'results' => $results
        ]);
    }

    /**
     * Handle syncing all events for a single integration.
     */
    private function handleSingleIntegrationSync(PickupCalendarSyncService $syncService): void
    {
        if (!$this->integration) {
            throw new \InvalidArgumentException('Integration is required for single integration sync');
        }

        Log::info('Syncing all events for single integration', [
            'integration_id' => $this->integration->id,
            'user_id' => $this->integration->user_id,
            'since' => $this->since?->format('Y-m-d H:i:s')
        ]);

        $this->integration->update(['sync_status' => 'syncing']);

        $results = $syncService->syncAllEventsForIntegration($this->integration, $this->since);

        Log::info('Single integration sync completed', [
            'integration_id' => $this->integration->id,
            'results' => $results
        ]);
    }

    /**
     * Handle full sync for the shared calendar.
     */
    private function handleFullSync(PickupCalendarSyncService $syncService): void
    {
        Log::info('Starting full Microsoft calendar sync for shared calendar');

        $sharedIntegration = MicrosoftCalendarIntegration::getSharedCalendarIntegration();

        if (!$sharedIntegration) {
            Log::info('No active shared Microsoft calendar integration found');
            return;
        }

        try {
            Log::info('Processing shared calendar integration', [
                'integration_id' => $sharedIntegration->id,
                'user_id' => $sharedIntegration->user_id,
                'calendar_name' => $sharedIntegration->calendar_name
            ]);

            $sharedIntegration->update(['sync_status' => 'syncing']);

            $results = $syncService->syncAllEventsForIntegration($sharedIntegration, $this->since);

            $overallResults = [
                'integration_processed' => true,
                'integration_succeeded' => true,
                'total_events_synced' => $results['synced'],
                'total_events_failed' => $results['failed'],
                'total_events_skipped' => $results['skipped'],
                'errors' => $results['errors']
            ];

        } catch (\Exception $e) {
            $overallResults = [
                'integration_processed' => true,
                'integration_succeeded' => false,
                'total_events_synced' => 0,
                'error' => $e->getMessage()
            ];

            Log::error('Shared calendar sync failed', [
                'integration_id' => $sharedIntegration->id,
                'error' => $e->getMessage()
            ]);

            $sharedIntegration->update([
                'sync_status' => 'failed',
                'error_message' => $e->getMessage()
            ]);
        }

        Log::info('Full Microsoft calendar sync completed', $overallResults);
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Microsoft calendar sync job permanently failed', [
            'sync_type' => $this->syncType,
            'integration_id' => $this->integration?->id,
            'event_id' => $this->event?->id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // Update integration status if specific integration failed
        if ($this->integration) {
            $this->integration->update([
                'sync_status' => 'failed',
                'error_message' => 'Job permanently failed after ' . $this->tries . ' attempts: ' . $exception->getMessage()
            ]);
        }
    }

    /**
     * Create job for syncing a single event.
     */
    public static function forEvent(Event $event): self
    {
        return new self(null, $event, 'single_event');
    }

    /**
     * Create job for syncing all events for a single integration.
     */
    public static function forIntegration(MicrosoftCalendarIntegration $integration, ?\DateTime $since = null): self
    {
        return new self($integration, null, 'single_integration', $since);
    }

    /**
     * Create job for full sync across all integrations.
     */
    public static function forFullSync(?\DateTime $since = null): self
    {
        return new self(null, null, 'full', $since);
    }
}
