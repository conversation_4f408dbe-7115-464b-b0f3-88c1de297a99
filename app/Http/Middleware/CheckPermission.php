<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $permission): Response
    {
        // Ensure the user account is enabled
        if (!$request->user()->enabled) {
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();
            
            // Handle AJAX requests differently
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Your account has been disabled. Please contact an administrator.',
                    'redirect' => route('login')
                ], 401);
            }
            
            return redirect()->route('login')->with('error', 'Your account has been disabled. Please contact an administrator.');
        }

        // Ensure the user is authenticated
        if (!$request->user()) {
            return response('Forbidden', 403);
        }

        // Check for multiple permissions
        $permissions = explode('|', $permission);
        foreach ($permissions as $perm) {
            if ($request->user()->hasPermission($perm)) {
                return $next($request);
            }
        }

        return response('Forbidden', 403);
    }
}
