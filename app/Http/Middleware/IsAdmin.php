<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class IsAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

        // check if they are logged in first
        if (!auth()->check()) {
            return response('Unauthorized', 401);
        }
        

        if (auth()->user()->role !== 'admin') {
            return response('Unauthorized', 401);
        }
        return $next($request);
    }
}
