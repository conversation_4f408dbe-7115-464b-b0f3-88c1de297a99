<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class HandleMobileTimeClockRequests
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Detect mobile devices
        $userAgent = $request->userAgent();
        $isMobile = preg_match('/Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i', $userAgent);
        $isPWA = $request->hasHeader('X-Requested-With') && 
                 $request->header('X-Requested-With') === 'XMLHttpRequest' &&
                 preg_match('/standalone/i', $userAgent);

        // Log mobile time clock requests for debugging
        if ($isMobile && $request->is('time-clock*')) {
            Log::info('Mobile time clock request', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'user_agent' => $userAgent,
                'is_pwa' => $isPWA,
                'ip_address' => $request->ip(),
                'user_id' => auth()->id(),
                'session_id' => $request->session()->getId(),
                'csrf_token' => $request->session()->token(),
                'has_csrf_header' => $request->hasHeader('X-CSRF-TOKEN'),
                'referer' => $request->header('Referer'),
                'timestamp' => now()->toISOString()
            ]);
        }

        // Handle CSRF token issues for mobile devices
        if ($isMobile && $request->isMethod('POST') && $request->is('time-clock*')) {
            // Check if CSRF token is missing or invalid
            $sessionToken = $request->session()->token();
            $requestToken = $request->input('_token') ?: $request->header('X-CSRF-TOKEN');
            
            if (!$requestToken || !hash_equals($sessionToken, $requestToken)) {
                Log::warning('CSRF token mismatch on mobile time clock request', [
                    'user_id' => auth()->id(),
                    'session_token' => $sessionToken,
                    'request_token' => $requestToken,
                    'user_agent' => $userAgent,
                    'ip_address' => $request->ip()
                ]);
                
                // For mobile devices, regenerate the token and redirect back with error
                $request->session()->regenerateToken();
                
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Security token expired. Please refresh the page and try again.',
                        'error_code' => 'CSRF_TOKEN_MISMATCH',
                        'new_token' => $request->session()->token()
                    ], 419);
                }
                
                return redirect()->back()
                    ->with('error', 'Security token expired. Please try again.')
                    ->with('mobile_csrf_error', true);
            }
        }

        $response = $next($request);

        // Add mobile-specific headers for better caching and PWA support
        if ($isMobile && $request->is('time-clock*')) {
            $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate');
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', '0');
            
            // Add PWA-specific headers
            if ($isPWA) {
                $response->headers->set('X-PWA-Request', 'true');
            }
        }

        return $response;
    }
}
