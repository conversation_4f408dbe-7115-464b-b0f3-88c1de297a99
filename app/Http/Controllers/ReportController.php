<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Report;
use App\Models\Invoice;
use App\Models\Department;
use App\Models\LineItem;
use App\View\Components\ReportsSales;
use App\Services\UserPreferenceService;


class ReportController extends Controller
{
    public function index(Request $request)
    {
        $pagination = $request->query('pagination', session('pagination', 10));
        session(['pagination' => $pagination]); // Save to session
        $reports = Report::with('generatedBy')->paginate($pagination);
        return view('reports.index', compact('reports'));
    }
    
    public function list(Request $request, UserPreferenceService $userPreferenceService)
    {
        // Default preferences
        $defaultPagination = $userPreferenceService->get('pagination_reports', 10);
        $defaultSort = $userPreferenceService->get('report_sort', 'id');
        $defaultOrder = $userPreferenceService->get('report_order', 'asc');
    
        // Retrieve preferences from request or defaults
        $pagination = $request->query('pagination', $defaultPagination);
        $sort = $request->query('sort', $defaultSort);
        $order = $request->query('order', $defaultOrder);
    
        // Save updated preferences if they differ from defaults
        if ($pagination !== $defaultPagination) {
            $userPreferenceService->set('pagination_reports', $pagination);
        }
        if ($sort !== $defaultSort) {
            $userPreferenceService->set('report_sort', $sort);
        }
        if ($order !== $defaultOrder) {
            $userPreferenceService->set('report_order', $order);
        }
    
        // Define valid columns for sorting
        $validColumns = ['id', 'name', 'type', 'generated_by', 'created_at'];
    
        // Apply sorting
        $query = Report::with('generatedBy');
        if (in_array($sort, $validColumns)) {
            if ($sort === 'generated_by') {
                // Sort by generated_by relationship
                $query->join('users', 'reports.generated_by', '=', 'users.id')
                      ->select('reports.*', 'users.name as generated_by_name')
                      ->orderBy('users.name', $order);
            } else {
                $query->orderBy($sort, $order);
            }
        }
    
        // Paginate results
        $reports = $query->paginate($pagination);
    
        return view('reports.list', compact('reports', 'pagination', 'sort', 'order'));
    }
    

    public function show(Report $report)
    {
        return view('reports.show', compact('report'));
    }

    public function create()
    {   
        $departments = Department::all();
        
        return view('reports.create', compact('departments'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            
            'report_type' => 'required|string',
            'name' => 'required|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'department' => 'nullable|exists:departments,id',

        ]);

        $data = [];

        if ($validated['report_type'] === 'invoices_summary') {
            $data = $this->generateReportInvoicesSummary($validated['start_date'], $validated['end_date']);
        }
        if ($validated['report_type'] === 'transactions_summary') {
            $data = $this->generateTransactionsSummary($validated['start_date'], $validated['end_date'], $validated['department'] ?? null);
        }

        // Store the report
        $report = Report::create([
            'name' => $validated['name'],
            'generated_by' => auth()->id(),
            'description' => 'Invoices by date range',
            'notes' => 'Generated for invoices between ' . $validated['start_date'] . ' and ' . $validated['end_date'],
            'type' => $validated['report_type'],
            'data' => $data,
        ]);

        return redirect()->route('reports.show', $report->id)->with('success', 'Report generated successfully!');
    }

    private function generateReportInvoicesSummary($start_date, $end_date)
    {
        // Fetch invoices, including their customer details
        $invoices = Invoice::whereBetween('created_at', [$start_date, $end_date])
            ->with('customer:id,name') // Eager load customer with only 'id' and 'name'
            ->get(['id', 'total_price', 'total_tax', 'total_discount', 'invoice_number', 'customer_id', 'invoice_date']);
    
        $totals = [
            'total_price' => $invoices->sum('total_price'),
            'total_tax' => $invoices->sum('total_tax'),
            'total_discount' => $invoices->sum('total_discount'),
            'count' => $invoices->count(),
        ];
    
        // Map each invoice to include customer name and other details
        $invoices = $invoices->map(function ($invoice) {
            return [
                'invoice_number' => $invoice->invoice_number,
                'customer_name' => $invoice->customer->name ?? 'Unknown',
                'customer_id' => $invoice->customer_id,
                'total_price' => $invoice->total_price,
                'total_tax' => $invoice->total_tax,
                'total_discount' => $invoice->total_discount,
                'invoice_date' => $invoice->invoice_date->format('Y-m-d'),
            ];
        });
    
        $data = [
            'type' => 'invoices_summary',
            'totals' => $totals,
            'invoices' => $invoices,
            'start_date' => $start_date,
            'end_date' => $end_date,
        ];
    
        return $data;
    }

    /**
     * Summarize all transactions (line items) on invoices within a date range, optionally filtered by department.
     */
    private function generateTransactionsSummary($start_date, $end_date, $department_id = null)
    {
        // Query line items within the date range
        $query = LineItem::whereBetween('created_at', [$start_date, $end_date])
            ->with(['department:id,name']); // Eager load department details
    
        // Filter by department if provided
        if ($department_id) {
            $query->where('department_id', $department_id);
        }
    
        $lineItems = $query->get(['id', 'item_name', 'quantity', 'price', 'tax', 'discount', 'subtotal', 'department_id']);
    
        // Calculate totals
        $totals = [
            'total_revenue' => $lineItems->sum('subtotal'),
            'gross_profit' => $lineItems->sum(function ($item) {
                return ($item->price * $item->quantity) - $item->discount;
            }),
            'total_tax' => $lineItems->sum('tax'),
            'total_discount' => $lineItems->sum('discount'),
            'count' => $lineItems->count(),
        ];
    
        // Map each line item to include additional details
        $lineItems = $lineItems->map(function ($item) {
            return [
                'item_name' => $item->item_name ?? 'Unknown',
                'quantity' => $item->quantity,
                'price' => $item->price,
                'tax' => $item->tax,
                'discount' => $item->discount,
                'subtotal' => $item->subtotal,
                'department_name' => $item->department->name ?? 'Unknown',
            ];
        });
    
        $data = [
            'type' => 'transactions_summary',
            'totals' => $totals,
            'line_items' => $lineItems,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'department_filter' => $department_id ? Department::find($department_id)->name : 'All Departments',
        ];
    
        return $data;
    }
    


    public function destroy(Report $report)
    {
        $report->delete();
        return redirect()->route('reports.index')->with('success', 'Report deleted successfully!');
    }

    public function salesData(Request $request)
    {
        $filterType = $request->get('filterType', 'category');
        $component = new ReportsSales($filterType);
        $salesData = $component->getSalesData();
    
        return response()->json([
            'salesData' => $salesData,
            'filterType' => $filterType,
        ]);
    }
    
    

}
