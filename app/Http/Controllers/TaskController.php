<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Models\TaskRecurrence;
use App\Models\TaskAssignee;
use App\Models\User;
use App\Models\UserGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\TaskCompletion;
use App\Services\UserPreferenceService;
use Carbon\Carbon;

class TaskController extends Controller
{
    /**
     * Display a listing of the tasks.
     */
    public function index(Request $request, UserPreferenceService $userPreferenceService)
    {
        // Retrieve saved preferences or set defaults
        $defaultPagination = $userPreferenceService->get('pagination_tasks', 10);
        $defaultSort = $userPreferenceService->get('task_sort', 'id');
        $defaultOrder = $userPreferenceService->get('task_order', 'asc');
    
        // Get current request parameters or fall back to defaults
        $pagination = $request->query('pagination', $defaultPagination);
        $sort = $request->query('sort', $defaultSort);
        $order = $request->query('order', $defaultOrder);
    
        // Save updated preferences if they differ from defaults
        if ($pagination !== $defaultPagination) {
            $userPreferenceService->set('pagination_tasks', $pagination);
        }
        if ($sort !== $defaultSort) {
            $userPreferenceService->set('task_sort', $sort);
        }
        if ($order !== $defaultOrder) {
            $userPreferenceService->set('task_order', $order);
        }
    
        // Define valid columns for sorting
        $validColumns = ['id', 'title', 'due_type', 'type', 'end_date'];
    
        if (in_array($sort, $validColumns)) {
            $tasks = Task::with(['creator', 'recurrences'])
                ->orderBy($sort, $order)
                ->paginate($pagination);
        } else {
            $tasks = Task::with(['creator', 'recurrences'])->paginate($pagination);
        }
    
        return view('tasks.index', compact('tasks', 'pagination', 'sort', 'order'));
    }
    

    /**
     * Show the form for creating a new task.
     */
    public function create()
    {
        $users = User::all(); // Fetch all users for assigning tasks
        return view('tasks.create', compact('users'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'due_type' => 'required|in:specific_time,any_time',
            //'type' => 'required|in:simple,checklist',
        ]);

        // only type for now is simple
        $validated['type'] = 'simple';

        // created_by is the authenticated user
        $validated['created_by'] = auth()->id();

        // Create the task
        $task = Task::create($validated);
        // Redirect to the edit page for the new task
        return redirect()->route('tasks.edit', ['task' => $task->id])->with('success', 'Task created successfully!');
    }


    public function edit(Task $task)
    {
        $users = User::all(); // Fetch all users for assigning tasks
        $userGroups = UserGroup::all(); // Fetch all user groups for assigning tasks
        $task->load(['recurrences', 'assignees']);
        return view('tasks.edit', compact('task', 'users', 'userGroups'));
    }


    public function update(Request $request, Task $task)
    {
        Log::info('Starting Task Update', ['task_id' => $task->id, 'request' => $request->all()]);

        try {
            // Validate the input
            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string',
                'due_type' => 'required|in:specific_time,any_time',
               // 'type' => 'required|in:simple,checklist',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:today',
            ]);

            Log::info('Validation Passed', ['validated' => $validated]);

            // Update the task
            $task->update($validated);
            Log::info('Task Updated', ['task' => $task]);

            // Handle recurrence updates
            if ($request->has('repeat_interval')) {

                if ($request->filled('due_time')) {
                    $request->merge(['due_time' => substr($request->due_time, 0, 5)]);
                }

                Log::info('Recurrence Data Provided', ['recurrence_data' => $request->all()]);

                $recurrenceData = $request->validate([
                    'repeat_interval' => 'required|in:once,daily,weekly,monthly,custom',
                    'repeat_days' => 'nullable|array',
                    'repeat_days.*' => 'string|in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',
                    'excluded_dates' => 'nullable|array',
                    'excluded_dates.*' => 'date',
                    'custom_interval' => 'nullable|integer|min:1',
                    'due_time' => 'nullable|date_format:H:i',
                ]);

                // Ensure repeat_days is an array for custom only
                $recurrenceData['repeat_days'] = $recurrenceData['repeat_days'] ?? [];

                // Always save recurrence data for "Once"
                $recurrence = $task->recurrences ?? new TaskRecurrence(['task_id' => $task->id]);
                $recurrence->fill($recurrenceData)->save();
                Log::info('Recurrence Updated', ['recurrence' => $recurrence]);
            }

            // Handle assignees
            if ($request->user()->hasPermission('create_edit_own_tasks') && !$request->user()->hasPermission('create_tasks')) {
                // Assign the task to the logged-in user only
                $task->assignees()->delete();
                TaskAssignee::create([
                    'task_id' => $task->id,
                    'assignee_type' => 'user',
                    'assignee_id' => $request->user()->id,
                ]);
                Log::info('Task Assigned to Logged-in User', ['task_id' => $task->id, 'assignee_id' => $request->user()->id]);
            } else {
                if ($request->filled('assignees')) {
                    Log::info('Assignees Provided', ['assignees' => $request->assignees]);

                    // Clear existing assignees
                    $task->assignees()->delete();
                    Log::info('Existing Assignees Cleared', ['task_id' => $task->id]);

                    foreach ($request->assignees as $assignee) {
                        [$type, $id] = explode(':', $assignee);

                        if ($type === 'user') { // Ensure we're only handling users
                            TaskAssignee::create([
                                'task_id' => $task->id,
                                'assignee_type' => $type,
                                'assignee_id' => (int)$id,
                            ]);
                            Log::info('Assignee Added', ['task_id' => $task->id, 'assignee_id' => $id]);
                        } else {
                            Log::warning('Unexpected Assignee Type Skipped', ['assignee' => $assignee]);
                        }
                    }
                } else {
                    // No assignees provided, clear all
                    $task->assignees()->delete();
                    Log::info('All Assignees Cleared', ['task_id' => $task->id]);
                }
            }

            // Redirect based on permissions
            if ($request->user()->hasPermission('create_tasks')) {
                return redirect()->route('tasks.index')->with('success', 'Task updated successfully!');
            } else {
                return redirect()->route('my-tasks')->with('success', 'Task updated successfully!');
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Log validation errors
            Log::error('Validation Failed', ['errors' => $e->errors()]);
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            // Log unexpected errors
            Log::error('Unexpected Error During Task Update', ['error' => $e->getMessage()]);
            return redirect()->back()->with('error', 'An unexpected error occurred.');
        }
    }


    /**
     * Remove the specified task from storage.
     */
    public function destroy(Task $task)
    {
        $task->delete();
        return redirect()->route('tasks.index')->with('success', 'Task deleted successfully!');
    }


    public function myTasks(Request $request)
    {
        $user = auth()->user();
        $currentWeek = $request->query('week', now()->format('Y-\WW'));
        $startDate = $request->query('start', Carbon::parse($currentWeek)->startOfWeek()->toDateString());
        $endDate = $request->query('end', Carbon::parse($currentWeek)->endOfWeek()->toDateString());

        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);

        $taskOccurrences = $user->getTaskOccurrences($start, $end);

        // Include the created_by field in the task data
        foreach ($taskOccurrences as $date => &$tasks) {
            foreach ($tasks['tasks'] as &$task) {
                $taskModel = Task::find($task['task_id']);
                $task['created_by'] = $taskModel->created_by;
            }
        }

        $previousWeek = $start->copy()->subWeek()->format('Y-\WW');
        $nextWeek = $start->copy()->addWeek()->format('Y-\WW');

        return view('tasks.my-tasks', compact('taskOccurrences', 'currentWeek', 'previousWeek', 'nextWeek', 'startDate', 'endDate', 'start'));
    }

    public function getTask($id)
    {
        $task = Task::with('recurrences')
            ->where('id', $id)
            ->firstOrFail();

        return response()->json([
            'title' => $task->title,
            'description' => $task->description,
            'due_time' => $task->recurrences->due_time ?? 'Any time',
        ]);
    }

    public function updateTaskStatus(Request $request, $id)
{
    try {
        $validated = $request->validate([
            'status' => 'required|in:incomplete,complete',
            'due_date' => 'required|date',
        ]);

        $completion = TaskCompletion::updateOrCreate(
            [
                'task_id' => $id,
                'user_id' => auth()->id(),
                'due_date' => $validated['due_date'],
            ],
            ['status' => $validated['status']]
        );
        
        return response()->json(['success' => true, 'status' => $completion->status]);
    } catch (\Illuminate\Validation\ValidationException $e) {
        return response()->json(['success' => false, 'message' => $e->getMessage()], 422);
    } catch (\Exception $e) {
        return response()->json(['success' => false, 'message' => 'An unexpected error occurred.'], 500);
    }
}

public function userTasks($userId)
{
    $user = User::findOrFail($userId);
    $start = now()->startOfWeek();
    $end = now()->endOfWeek();
    $taskOccurrences = $user->getTaskOccurrences($start, $end);

    return view('tasks.user-tasks', compact('user', 'taskOccurrences'));
}
    
}
