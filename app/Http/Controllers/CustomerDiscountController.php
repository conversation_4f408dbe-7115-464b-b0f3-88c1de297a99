<?php

namespace App\Http\Controllers;

use App\Models\CustomerDiscount;
use App\Models\Customer;
use App\Models\Discount;
use Illuminate\Http\Request;

class CustomerDiscountController extends Controller
{
    public function index(Request $request)
    {
        // Default values
        $pagination = $request->query('pagination', session('pagination', 10));
        $sort = $request->query('sort', 'id');
        $order = $request->query('order', 'desc');
        session(['pagination' => $pagination]); // Save to session

        $query = CustomerDiscount::with('customer', 'discount');

        // Handle search
        if ($search = $request->input('search')) {
            $query->where(function($q) use ($search) {
                $q->whereHas('customer', function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                })
                ->orWhereHas('discount', function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%");
                });
            });
        }

        // Filter by customer type
        if ($customerId = $request->input('customer_id')) {
            $query->where('customer_id', $customerId);
        }

        // Filter by discount
        if ($discountId = $request->input('discount_id')) {
            $query->where('discount_id', $discountId);
        }

        // Filter by active status
        if ($request->has('status')) {
            $status = $request->input('status');
            $now = now();

            if ($status === 'active') {
                $query->where(function($q) use ($now) {
                    $q->where(function($q) use ($now) {
                        $q->whereNull('start_date')
                          ->orWhere('start_date', '<=', $now);
                    })->where(function($q) use ($now) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', $now);
                    })->where(function($q) {
                        $q->whereNull('maximum_uses')
                          ->orWhereRaw('usage_count < maximum_uses');
                    });
                });
            } elseif ($status === 'expired') {
                $query->where(function($q) use ($now) {
                    $q->where('end_date', '<', $now)
                      ->orWhereRaw('maximum_uses IS NOT NULL AND usage_count >= maximum_uses');
                });
            } elseif ($status === 'future') {
                $query->where('start_date', '>', $now);
            }
        }

        // Define valid columns for sorting
        $validColumns = ['id', 'customer_id', 'discount_id', 'start_date', 'end_date', 'usage_count', 'maximum_uses', 'created_at'];

        // Apply sorting
        if (in_array($sort, $validColumns)) {
            $query->orderBy($sort, $order);
        } else {
            // Handle special sorting cases
            if ($sort === 'customer_name') {
                $query->join('customers', 'customer_discounts.customer_id', '=', 'customers.id')
                      ->orderBy('customers.name', $order)
                      ->select('customer_discounts.*');
            } elseif ($sort === 'discount_name') {
                $query->join('discounts', 'customer_discounts.discount_id', '=', 'discounts.id')
                      ->orderBy('discounts.name', $order)
                      ->select('customer_discounts.*');
            }
        }

        $customerDiscounts = $query->paginate($pagination);
        $customerDiscounts->appends($request->except('page')); // Maintain query parameters in pagination links

        // Get unique customers and discounts for filters
        $customers = Customer::whereIn('id', CustomerDiscount::select('customer_id')->distinct())->get();
        $discounts = Discount::whereIn('id', CustomerDiscount::select('discount_id')->distinct())->get();

        return view('customer_discounts.index', compact('customerDiscounts', 'pagination', 'sort', 'order', 'customers', 'discounts'));
    }

    public function create(Request $request)
    {
        $discounts = Discount::all();
        $selectedCustomer = null;
        $selectedCustomerId = null;

        // Check if customer_id is provided in the request
        if ($request->has('customer_id')) {
            $selectedCustomerId = $request->input('customer_id');
            $selectedCustomer = Customer::find($selectedCustomerId);
        }

        return view('customer_discounts.create', compact('discounts', 'selectedCustomer', 'selectedCustomerId'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'discount_id' => 'required|exists:discounts,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'maximum_uses' => 'nullable|integer|min:1',
        ]);

        CustomerDiscount::create($validated);

        // Check if we should redirect back to the customer's page
        if ($request->has('redirect_to_customer') && $request->input('redirect_to_customer') === 'true') {
            $customerId = $validated['customer_id'];
            return redirect()->route('customers.show', $customerId)->with('success', 'Customer Discount created successfully.');
        }

        return redirect()->route('customer_discounts.index')->with('success', 'Customer Discount created successfully.');
    }

    public function edit($id)
    {
        $customerDiscount = CustomerDiscount::with('customer')->findOrFail($id);
        $discounts = Discount::all(); // Fetch all discounts

        return view('customer_discounts.edit', compact('customerDiscount', 'discounts'));
    }


    public function update(Request $request, CustomerDiscount $customerDiscount)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'discount_id' => 'required|exists:discounts,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'maximum_uses' => 'nullable|integer|min:1',
        ]);

        $customerDiscount->update($validated);

        return redirect()->route('customer_discounts.index')->with('success', 'Customer Discount updated successfully.');
    }

    public function destroy(CustomerDiscount $customerDiscount)
    {
        $customerDiscount->delete();
        return redirect()->route('customer_discounts.index')->with('success', 'Customer Discount deleted successfully.');
    }



    public function markAsUsed($customerId, $discountId)
    {
        // Retrieve the discount
        $discount = Discount::findOrFail($discountId);

        // Determine `maximum_uses` based on `reusable`
        $maximumUses = $discount->reusable ? null : 1; // `null` for reusable, `1` for non-reusable

        // Find or create the CustomerDiscount record
        $customerDiscount = CustomerDiscount::firstOrCreate(
            [
                'customer_id' => $customerId,
                'discount_id' => $discountId,
            ],
            [
                'usage_count' => 0,                  // Default usage count
                'maximum_uses' => $maximumUses,      // Set maximum uses
                'start_date' => $discount->start_date ?? now(), // Default start date
                'end_date' => $discount->end_date,  // End date from discount
            ]
        );

        // Check if the discount is still valid
        if (!$customerDiscount->isActive()) {
            return response()->json(['success' => false, 'message' => 'Discount is not valid or already used up.']);
        }

        // Increment the usage count
        $customerDiscount->increment('usage_count');

        return response()->json(['success' => true, 'message' => 'Discount marked as used.']);
    }

    public function unmarkAsUsed($customerId, $discountId)
    {
        // Find the CustomerDiscount record
        $customerDiscount = CustomerDiscount::where([
            'customer_id' => $customerId,
            'discount_id' => $discountId,
        ])->first();

        if (!$customerDiscount) {
            return response()->json(['success' => false, 'message' => 'Discount not found for this customer.']);
        }

        // Ensure usage count isn't already 0
        if ($customerDiscount->usage_count <= 0) {
            return response()->json(['success' => false, 'message' => 'Discount has not been used yet.']);
        }

        // Decrement the usage count
        $customerDiscount->decrement('usage_count');

        return response()->json(['success' => true, 'message' => 'Discount usage unmarked.']);
    }

    public function search(Request $request)
    {
        $query = $request->input('query');
        $pagination = $request->query('pagination', session('pagination', 10));
        $sort = $request->query('sort', 'id');
        $order = $request->query('order', 'desc');

        $customerDiscounts = CustomerDiscount::with('customer', 'discount')
            ->whereHas('customer', function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%")
                  ->orWhere('phone', 'like', "%{$query}%");
            })
            ->orWhereHas('discount', function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%");
            })
            ->orderBy($sort, $order)
            ->paginate($pagination);

        $customerDiscounts->appends($request->except('page'));

        // Get unique customers and discounts for filters
        $customers = Customer::whereIn('id', CustomerDiscount::select('customer_id')->distinct())->get();
        $discounts = Discount::whereIn('id', CustomerDiscount::select('discount_id')->distinct())->get();

        return view('customer_discounts.index', compact('customerDiscounts', 'pagination', 'sort', 'order', 'customers', 'discounts'));
    }

}
