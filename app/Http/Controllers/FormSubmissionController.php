<?php

namespace App\Http\Controllers;

use App\Models\FormSubmission;
use App\Models\Form;
use App\Models\Customer;
use App\Models\CustomerDiscount;
use App\Models\File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;

class FormSubmissionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = FormSubmission::with(['form', 'customer', 'reviewer', 'approver']);

        // Filter by form
        if ($formId = $request->input('form_id')) {
            $query->where('form_id', $formId);
        }

        // Filter by customer
        if ($customerId = $request->input('customer_id')) {
            $query->where('customer_id', $customerId);
        }

        // Filter by status
        if ($status = $request->input('status')) {
            $query->where('status', $status);
        }

        // Search
        if ($search = $request->input('search')) {
            $query->where(function($q) use ($search) {
                if (strlen($search) < 5) {
                    // For short search terms (< 5 chars), only search ID and submitter name
                    $q->where('id', $search) // Exact match for submission ID
                      ->orWhere('submitter_name', 'like', "%{$search}%");
                } else {
                    // For longer search terms (5+ chars), search all fields
                    $q->where('submitter_name', 'like', "%{$search}%")
                      ->orWhere('submitter_email', 'like', "%{$search}%")
                      ->orWhere('submitter_phone', 'like', "%{$search}%")
                      ->orWhere('id', $search); // Search by submission ID
                }
            });
        }

        // Date range filter
        if ($startDate = $request->input('start_date')) {
            $query->whereDate('created_at', '>=', $startDate);
        }
        if ($endDate = $request->input('end_date')) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        $submissions = $query->latest()->paginate(20);
        $forms = Form::active()->get();

        return view('form-submissions.index', compact('submissions', 'forms'));
    }

    /**
     * Display the specified resource.
     */
    public function show(FormSubmission $formSubmission)
    {
        $formSubmission->load(['form.fields', 'customer', 'reviewer', 'approver']);
        
        // Find potential customer matches
        $potentialCustomers = Customer::where('email', $formSubmission->submitter_email)
            ->orWhere('phone', $formSubmission->submitter_phone)
            ->get();

        return view('form-submissions.show', compact('formSubmission', 'potentialCustomers'));
    }

    /**
     * Review a submission
     */
    public function review(FormSubmission $formSubmission)
    {
        if (!$formSubmission->isPending()) {
            return redirect()->route('form-submissions.show', $formSubmission)
                ->with('error', 'This submission has already been reviewed.');
        }

        $formSubmission->markAsReviewing(Auth::user());

        return redirect()->route('form-submissions.show', $formSubmission)
            ->with('success', 'Submission marked as under review.');
    }

    /**
     * Approve a submission
     */
    public function approve(Request $request, FormSubmission $formSubmission)
    {
        if (!$formSubmission->isPending()) {
            return redirect()->route('form-submissions.show', $formSubmission)
                ->with('error', 'This submission has already been processed.');
        }

        $validated = $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'create_customer' => 'boolean',
        ]);

        DB::transaction(function () use ($formSubmission, $validated) {
            $customer = null;

            if ($validated['create_customer'] ?? false) {
                // Create new customer from submission data
                $businessName = $formSubmission->getFormDataValue('business_name');
                
                $customerData = [
                    'name' => $businessName ?: $formSubmission->submitter_name,
                    'contact' => $businessName ? $formSubmission->submitter_name : null,
                    'email' => $formSubmission->submitter_email,
                    'phone' => $formSubmission->submitter_phone,
                    'customer_type' => $formSubmission->getFormDataValue('customer_type', 'Residential Customer'),
                    'address' => $formSubmission->getFormDataValue('address'),
                ];

                // Set the type field based on whether business name was provided
                if ($businessName) {
                    $customerData['type'] = 'Business';
                } else {
                    $customerData['type'] = 'Residential';
                }

                $customer = Customer::create($customerData);
            } elseif ($validated['customer_id'] ?? null) {
                $customer = Customer::find($validated['customer_id']);
            }

            $formSubmission->approve(Auth::user(), $customer);
            
            // Reload the form and customer relationships to ensure we have fresh data
            $formSubmission->load(['form', 'customer']);
            
            // Use the customer from the submission if one exists (could be from linkCustomer or from approval)
            $pdfCustomer = $formSubmission->customer ?: $customer;

            // Generate PDF if form is configured to do so
            \Log::info('Checking PDF generation conditions', [
                'submission_id' => $formSubmission->id,
                'generate_pdf_on_approval' => $formSubmission->form->generate_pdf_on_approval,
                'has_customer' => !is_null($pdfCustomer),
                'customer_id' => $pdfCustomer ? $pdfCustomer->id : null,
                'form_id' => $formSubmission->form->id,
                'form_name' => $formSubmission->form->name
            ]);
            
            if ($formSubmission->form->generate_pdf_on_approval && $pdfCustomer) {
                try {
                    \Log::info('Starting PDF generation for form submission', [
                        'submission_id' => $formSubmission->id,
                        'customer_id' => $pdfCustomer->id
                    ]);
                    
                    $file = $this->generateAndSavePdfForCustomer($formSubmission, $pdfCustomer);
                    
                    \Log::info('PDF generated and saved for form submission', [
                        'submission_id' => $formSubmission->id,
                        'customer_id' => $pdfCustomer->id,
                        'file_id' => $file->id,
                        'filepath' => $file->filepath
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Failed to generate PDF for form submission', [
                        'submission_id' => $formSubmission->id,
                        'customer_id' => $pdfCustomer->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }
        });

        return redirect()->route('form-submissions.show', $formSubmission)
            ->with('success', 'Submission approved successfully.');
    }

    /**
     * Reject a submission
     */
    public function reject(Request $request, FormSubmission $formSubmission)
    {
        if (!$formSubmission->isPending()) {
            return redirect()->route('form-submissions.show', $formSubmission)
                ->with('error', 'This submission has already been processed.');
        }

        $validated = $request->validate([
            'review_notes' => 'required|string|max:1000',
        ]);

        $formSubmission->reject(Auth::user(), $validated['review_notes']);

        return redirect()->route('form-submissions.show', $formSubmission)
            ->with('success', 'Submission rejected.');
    }

    /**
     * Link submission to existing customer
     */
    public function linkCustomer(Request $request, FormSubmission $formSubmission)
    {
        $validated = $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
        ]);

        $customerId = $validated['customer_id'] ?: null;
        $formSubmission->update(['customer_id' => $customerId]);

        // If already approved and form applies discount on approval, apply discount
        if ($customerId && $formSubmission->isApproved() && 
            $formSubmission->form->applies_discount_on_approval && 
            $formSubmission->form->discount_id) {
            $formSubmission->applyFormApprovalDiscount();
        }

        $message = $customerId ? 'Submission linked to customer.' : 'Submission unlinked from customer.';
        
        return redirect()->route('form-submissions.show', $formSubmission)
            ->with('success', $message);
    }

    /**
     * Export submission data
     */
    public function export(FormSubmission $formSubmission)
    {
        $data = [
            'form' => $formSubmission->form->name,
            'submitted_at' => $formSubmission->created_at->format('Y-m-d H:i:s'),
            'status' => $formSubmission->status,
            'submitter' => [
                'name' => $formSubmission->submitter_name,
                'email' => $formSubmission->submitter_email,
                'phone' => $formSubmission->submitter_phone,
            ],
            'form_data' => $formSubmission->form_data,
            'signed' => $formSubmission->signed_at ? $formSubmission->signed_at->format('Y-m-d H:i:s') : null,
        ];

        return response()->json($data, 200, [
            'Content-Disposition' => 'attachment; filename="submission-' . $formSubmission->id . '.json"',
        ]);
    }

    /**
     * Generate PDF for form submission
     */
    public function generatePdf(FormSubmission $formSubmission, $returnContent = false)
    {
        // Load all necessary relationships
        $formSubmission->load(['form.fields', 'customer', 'reviewer', 'approver']);

        // Process signature data for PDF
        $signatureImagePath = null;
        if ($formSubmission->signature_data) {
            // Check if it's a data URL
            if (strpos($formSubmission->signature_data, 'data:image') === 0) {
                // Extract the base64 data
                $data = explode(',', $formSubmission->signature_data);
                if (count($data) > 1) {
                    $imageData = base64_decode($data[1]);
                    
                    // Create a temporary file for the signature
                    $tempPath = storage_path('app/temp/signatures');
                    if (!file_exists($tempPath)) {
                        mkdir($tempPath, 0755, true);
                    }
                    
                    $tempFile = $tempPath . '/sig_' . $formSubmission->id . '_' . time() . '.png';
                    file_put_contents($tempFile, $imageData);
                    
                    // Use the file path for the PDF
                    $signatureImagePath = $tempFile;
                }
            }
        }

        // Prepare field data for display
        $fieldData = [];
        $formData = $formSubmission->form_data;
        
        foreach ($formSubmission->form->fields()->orderBy('order')->get() as $field) {
            // For HTML and section fields, always include them
            if ($field->type === 'html' || $field->type === 'section') {
                $fieldData[] = [
                    'id' => $field->id,
                    'type' => $field->type,
                    'label' => $field->label,
                    'content' => $field->content,
                    'value' => null
                ];
                continue;
            }
            
            // For input fields, check if value exists
            $value = null;
            if (isset($formData['fields'][$field->name])) {
                $value = $formData['fields'][$field->name];
            } elseif (isset($formData[$field->name])) {
                // Sometimes data might be stored directly without 'fields' wrapper
                $value = $formData[$field->name];
            }
            
            // Only include fields that have values or are required
            if ($value !== null && $value !== '') {
                $fieldData[] = [
                    'type' => $field->type,
                    'label' => $field->label,
                    'name' => $field->name,
                    'value' => $value
                ];
            }
        }

        // Create filename
        $filename = 'form-submission-' . $formSubmission->id . '-' . now()->format('Y-m-d') . '.pdf';

        // Generate PDF with proper options for image rendering
        $pdf = Pdf::loadView('form-submissions.pdf', [
            'submission' => $formSubmission,
            'fieldData' => $fieldData,
            'signatureImagePath' => $signatureImagePath
        ]);

        // Set paper size and orientation
        $pdf->setPaper('letter', 'portrait');
        
        // Set options to enable proper rendering
        $pdf->getDomPDF()->set_option('isHtml5ParserEnabled', true);
        $pdf->getDomPDF()->set_option('isPhpEnabled', false);

        if ($returnContent) {
            // Return PDF content for emailing
            $content = $pdf->output();
            
            // Clean up temporary signature file
            if ($signatureImagePath && file_exists($signatureImagePath)) {
                unlink($signatureImagePath);
            }
            
            return $content;
        } else {
            // Stream the PDF to the browser
            $response = $pdf->stream($filename);
            
            // Clean up temporary signature file
            if ($signatureImagePath && file_exists($signatureImagePath)) {
                unlink($signatureImagePath);
            }
            
            return $response;
        }
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:approve,reject,delete',
            'submission_ids' => 'required|array',
            'submission_ids.*' => 'exists:form_submissions,id',
        ]);

        $count = 0;

        foreach ($validated['submission_ids'] as $id) {
            $submission = FormSubmission::find($id);
            
            switch ($validated['action']) {
                case 'approve':
                    if ($submission->isPending()) {
                        $submission->approve(Auth::user());
                        $count++;
                    }
                    break;
                    
                case 'reject':
                    if ($submission->isPending()) {
                        $submission->reject(Auth::user(), 'Bulk rejected');
                        $count++;
                    }
                    break;
                    
                case 'delete':
                    $submission->delete();
                    $count++;
                    break;
            }
        }

        return redirect()->route('form-submissions.index')
            ->with('success', "{$count} submissions {$validated['action']}d successfully.");
    }

    /**
     * Generate and save PDF for approved submission
     */
    protected function generateAndSavePdfForCustomer(FormSubmission $formSubmission, Customer $customer)
    {
        \Log::info('generateAndSavePdfForCustomer called', [
            'submission_id' => $formSubmission->id,
            'customer_id' => $customer->id
        ]);
        
        // Load all necessary relationships
        $formSubmission->load(['form.fields', 'reviewer', 'approver']);

        // Get signature data - it's already stored as a complete data URL
        $signatureImagePath = null;
        if ($formSubmission->signature_data) {
            // Check if it's a data URL
            if (strpos($formSubmission->signature_data, 'data:image') === 0) {
                // Extract the base64 data
                $data = explode(',', $formSubmission->signature_data);
                if (count($data) > 1) {
                    $imageData = base64_decode($data[1]);
                    
                    // Create a temporary file for the signature
                    $tempPath = storage_path('app/temp/signatures');
                    if (!file_exists($tempPath)) {
                        mkdir($tempPath, 0755, true);
                    }
                    
                    $tempFile = $tempPath . '/sig_' . $formSubmission->id . '_' . time() . '.png';
                    file_put_contents($tempFile, $imageData);
                    
                    // Use the file path for the PDF
                    $signatureImagePath = $tempFile;
                }
            }
        }

        // Prepare field data for display
        $fieldData = [];
        $formData = $formSubmission->form_data;
        
        foreach ($formSubmission->form->fields()->orderBy('order')->get() as $field) {
            // For HTML and section fields, always include them
            if ($field->type === 'html' || $field->type === 'section') {
                $fieldData[] = [
                    'id' => $field->id,
                    'type' => $field->type,
                    'label' => $field->label,
                    'content' => $field->content,
                    'value' => null
                ];
                continue;
            }
            
            // For input fields, check if value exists
            $value = null;
            if (isset($formData['fields'][$field->name])) {
                $value = $formData['fields'][$field->name];
            } elseif (isset($formData[$field->name])) {
                // Sometimes data might be stored directly without 'fields' wrapper
                $value = $formData[$field->name];
            }
            
            // Only include fields that have values or are required
            if ($value !== null && $value !== '') {
                $fieldData[] = [
                    'type' => $field->type,
                    'label' => $field->label,
                    'name' => $field->name,
                    'value' => $value
                ];
            }
        }

        // Generate PDF
        $pdf = Pdf::loadView('form-submissions.pdf', [
            'submission' => $formSubmission,
            'fieldData' => $fieldData,
            'signatureImagePath' => $signatureImagePath
        ]);

        // Set paper size and orientation
        $pdf->setPaper('letter', 'portrait');
        
        // Set options to enable proper rendering
        $pdf->getDomPDF()->set_option('isHtml5ParserEnabled', true);
        $pdf->getDomPDF()->set_option('isPhpEnabled', false);

        // Generate the PDF content
        $pdfContent = $pdf->output();
        
        // Clean up temporary signature file
        if ($signatureImagePath && file_exists($signatureImagePath)) {
            unlink($signatureImagePath);
        }

        // Create filename
        $filename = 'form-' . $formSubmission->form->slug . '-submission-' . $formSubmission->id . '-' . now()->format('Y-m-d-His') . '.pdf';
        
        // Save to storage - using the same path structure as FileService
        $path = 'customers/' . $customer->id . '/' . $filename;
        Storage::disk('private')->put($path, $pdfContent);

        \Log::info('PDF saved to storage', [
            'path' => $path,
            'size' => strlen($pdfContent),
            'customer_id' => $customer->id
        ]);

        // Create file record
        $file = File::create([
            'filename' => $filename,
            'original_filename' => $formSubmission->form->name . ' - Submission #' . $formSubmission->id,
            'filepath' => $path,
            'mime_type' => 'application/pdf',
            'size' => strlen($pdfContent),
            'extension' => 'pdf',
            'description' => 'Automatically generated PDF for ' . $formSubmission->form->name . ' submission #' . $formSubmission->id . ' approved on ' . now()->format('F j, Y'),
            'uploaded_by' => Auth::id(),
            'fileable_id' => $customer->id,
            'fileable_type' => Customer::class,
            'metadata' => [
                'form_id' => $formSubmission->form->id,
                'form_submission_id' => $formSubmission->id,
                'auto_generated' => true,
                'generated_at' => now()->toIso8601String(),
            ],
            'is_public' => false,
        ]);

        // Log activity
        if (class_exists('App\\Models\\Activity')) {
            \App\Models\Activity::create([
                'action' => 'pdf_generated',
                'description' => "PDF generated for form submission #{$formSubmission->id} and saved to customer files",
                'model_type' => 'FormSubmission',
                'model_id' => $formSubmission->id,
                'user_id' => Auth::id(),
            ]);
        }

        return $file;
    }

    /**
     * Email the PDF to the customer
     */
    public function emailPdf(FormSubmission $formSubmission)
    {
        // Check if submission is approved
        if (!$formSubmission->isApproved()) {
            return response()->json([
                'success' => false,
                'message' => 'Only approved submissions can be emailed to customers.'
            ], 400);
        }

        // Check if we have a customer email
        $customerEmail = $formSubmission->submitter_email;
        if (!$customerEmail) {
            return response()->json([
                'success' => false,
                'message' => 'No customer email address found for this submission.'
            ], 400);
        }

        try {
            // Generate the PDF content using existing logic
            $pdfContent = $this->generatePdf($formSubmission, true);
            
            // Create filename
            $filename = 'form-submission-' . $formSubmission->id . '-' . now()->format('Y-m-d') . '.pdf';
            
            // Prepare template data
            $templateData = [
                'form' => $formSubmission->form,
                'submission' => $formSubmission,
            ];

            // Prepare attachment
            $attachment = [
                'content' => $pdfContent,
                'filename' => $filename,
                'mime_type' => 'application/pdf'
            ];

            // Use EmailTemplateService to send the email
            $emailTemplateService = app(\App\Services\EmailTemplateService::class);
            $renderedTemplate = $emailTemplateService->renderTemplateWithAttachments(
                'form_submission_pdf_delivery',
                $templateData,
                [$attachment]
            );

            if (!$renderedTemplate) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email template "form_submission_pdf_delivery" not found. Please contact an administrator.'
                ], 500);
            }

            // Send email using Laravel Mail
            \Mail::send([], [], function ($message) use ($renderedTemplate, $customerEmail, $formSubmission) {
                $message->to($customerEmail)
                        ->subject($renderedTemplate['subject'])
                        ->html($renderedTemplate['body']);

                if (isset($renderedTemplate['from_email']) && $renderedTemplate['from_email']) {
                    $message->from($renderedTemplate['from_email'], $renderedTemplate['from_name'] ?? config('mail.from.name'));
                }

                // Add attachment
                if (isset($renderedTemplate['attachments'])) {
                    foreach ($renderedTemplate['attachments'] as $attachment) {
                        $message->attachData(
                            $attachment['content'],
                            $attachment['filename'],
                            ['mime' => $attachment['mime_type']]
                        );
                    }
                }
            });

            // Log activity
            if (class_exists('App\\Models\\Activity')) {
                \App\Models\Activity::create([
                    'action' => 'pdf_emailed',
                    'description' => "Form submission PDF emailed to {$customerEmail} for submission #{$formSubmission->id}",
                    'model_type' => 'FormSubmission',
                    'model_id' => $formSubmission->id,
                    'user_id' => Auth::id(),
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => "PDF successfully emailed to {$customerEmail}"
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to email form submission PDF', [
                'submission_id' => $formSubmission->id,
                'customer_email' => $customerEmail,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send email. Please try again or contact support.'
            ], 500);
        }
    }

}
