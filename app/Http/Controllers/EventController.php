<?php

namespace App\Http\Controllers;

use App\Models\Calendar;
use App\Models\Event;
use App\Models\EventCategory;
use App\Models\RecurrencePattern;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class EventController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $calendarId = $request->input('calendar_id');
        $startDate = $request->input('start');
        $endDate = $request->input('end');

        $calendar = Calendar::findOrFail($calendarId);

        // Check if user has access to this calendar
        $user = Auth::user();
        if (! $calendar->is_public && $calendar->owner_id !== $user->id && ! $calendar->shares()->where('user_id', $user->id)->exists()) {
            return response()->json(['error' => 'You do not have permission to view this calendar.'], 403);
        }

        // Get events within the date range
        $events = $calendar->events()
            ->where('is_active', true)
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                return $query->inDateRange($startDate, $endDate);
            })
            ->with(['recurrencePattern', 'categories', 'customer', 'pickupRequest'])
            ->get()
            ->map(function ($event) {
                // Determine event color based on type
                $backgroundColor = $event->color ?: $event->calendar->color;
                $borderColor = $event->color ?: $event->calendar->color;

                // Override color for blockout events
                if ($event->is_blockout_event) {
                    $backgroundColor = '#dc2626'; // Red color for blockouts
                    $borderColor = '#dc2626';
                }

                $extendedProps = [
                    'description' => $event->description,
                    'location' => $event->location,
                    'categories' => $event->categories->pluck('name'),
                    'is_pickup_event' => $event->is_pickup_event,
                    'is_blockout_event' => $event->is_blockout_event,
                    'recurrence' => $event->recurrencePattern ? [
                        'frequency' => $event->recurrencePattern->frequency,
                        'interval' => $event->recurrencePattern->interval,
                    ] : null,
                ];

                // Add pickup-specific data for pickup events
                if ($event->is_pickup_event) {
                    $pickupData = [
                        'customer_id' => $event->customer_id,
                        'customer' => $event->customer ? [
                            'id' => $event->customer->id,
                            'name' => $event->customer->name,
                        ] : null,
                    ];

                    // If there's a linked pickup request, use that data
                    if ($event->pickupRequest) {
                        $pickupDetails = $event->pickupRequest->getPickupDetailsArray();
                        $pickupData = array_merge($pickupData, [
                            'pickup_request_id' => $event->pickupRequest->id,
                            'pickup_details' => $pickupDetails,
                            // Extract commonly used fields for backward compatibility
                            'pickup_address' => $pickupDetails['pickup']['address'] ?? null,
                            'pickup_items' => $pickupDetails['pickup']['items'] ?? null,
                            'contact_name' => $pickupDetails['contact']['name'] ?? null,
                            'contact_phone' => $pickupDetails['contact']['phone'] ?? null,
                            'contact_email' => $pickupDetails['contact']['email'] ?? null,
                        ]);
                    } else {
                        // For legacy pickup events without linked requests, show basic info
                        $pickupData = array_merge($pickupData, [
                            'pickup_request_id' => null,
                            'pickup_details' => [],
                            'pickup_address' => 'Legacy pickup event - no linked request',
                            'pickup_items' => 'Legacy pickup event',
                            'contact_name' => null,
                            'contact_phone' => null,
                            'contact_email' => null,
                        ]);
                    }

                    $extendedProps = array_merge($extendedProps, $pickupData);
                }

                return [
                    'id' => $event->id,
                    'title' => $event->title,
                    'start' => $event->start_date->toISOString(),
                    'end' => $event->end_date ? $event->end_date->toISOString() : null,
                    'allDay' => $event->all_day,
                    'backgroundColor' => $backgroundColor,
                    'borderColor' => $borderColor,
                    'extendedProps' => $extendedProps,
                ];
            });

        return response()->json($events);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'calendar_id' => 'required|exists:calendars,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'all_day' => 'boolean',
            'location' => 'nullable|string|max:255',
            'color' => 'nullable|string|max:7',
            'categories' => 'nullable|array',
            'categories.*' => 'exists:event_categories,id',
            'recurrence' => 'nullable|array',
            'recurrence.frequency' => 'required_with:recurrence|in:daily,weekly,monthly,yearly,custom',
            'recurrence.interval' => 'required_with:recurrence|integer|min:1',
            'recurrence.days_of_week' => 'nullable|array',
            'recurrence.days_of_month' => 'nullable|array',
            'recurrence.months_of_year' => 'nullable|array',
            'recurrence.until_date' => 'nullable|date|after:start_date',
            'recurrence.count' => 'nullable|integer|min:1',
            // Event type flags
            'is_pickup_event' => 'boolean',
            'is_blockout_event' => 'boolean',
            'customer_id' => 'nullable|exists:customers,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $calendar = Calendar::findOrFail($request->calendar_id);

        // Check if user has permission to add events to this calendar
        $user = Auth::user();
        if ($calendar->owner_id !== $user->id &&
            ! $calendar->shares()->where('user_id', $user->id)->whereIn('permission', ['edit', 'manage'])->exists()) {
            return response()->json(['error' => 'You do not have permission to add events to this calendar.'], 403);
        }

        // Create recurrence pattern if provided
        $recurrencePatternId = null;
        if ($request->has('recurrence')) {
            $recurrencePattern = RecurrencePattern::create([
                'frequency' => $request->input('recurrence.frequency'),
                'interval' => $request->input('recurrence.interval'),
                'days_of_week' => $request->input('recurrence.days_of_week'),
                'days_of_month' => $request->input('recurrence.days_of_month'),
                'months_of_year' => $request->input('recurrence.months_of_year'),
                'until_date' => $request->input('recurrence.until_date'),
                'count' => $request->input('recurrence.count'),
            ]);

            $recurrencePatternId = $recurrencePattern->id;
        }

        // Laravel will handle timezone conversion automatically
        // The dates come from the client in ISO format
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        // Log the parsed dates for debugging
        Log::debug('Parsed dates from request:', [
            'original_start' => $request->start_date,
            'original_end' => $request->end_date,
            'parsed_start' => $startDate ? $startDate->format('Y-m-d H:i:s') : null,
            'parsed_end' => $endDate ? $endDate->format('Y-m-d H:i:s') : null,
        ]);

        // Create the event
        $event = Event::create([
            'calendar_id' => $request->calendar_id,
            'title' => $request->title,
            'description' => $request->description,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'all_day' => $request->all_day ?? false,
            'creator_id' => $user->id,
            'recurrence_pattern_id' => $recurrencePatternId,
            'location' => $request->location,
            'color' => $request->color,
            // Event type flags
            'is_pickup_event' => $request->is_pickup_event ?? false,
            'is_blockout_event' => $request->is_blockout_event ?? false,
            'customer_id' => $request->customer_id,
        ]);

        // Attach categories if provided
        if ($request->has('categories')) {
            $event->categories()->attach($request->categories);
        }

        // Log the event dates for debugging
        Log::debug('Event dates after creation:', [
            'id' => $event->id,
            'start_date' => $event->start_date,
            'end_date' => $event->end_date,
            'start_date_iso' => $event->start_date->toISOString(),
            'end_date_iso' => $event->end_date ? $event->end_date->toISOString() : null,
            'app_timezone' => config('app.timezone'),
        ]);

        return response()->json([
            'message' => 'Event created successfully',
            'event' => [
                'id' => $event->id,
                'title' => $event->title,
                'start' => $event->start_date->toISOString(),
                'end' => $event->end_date ? $event->end_date->toISOString() : null,
                'allDay' => $event->all_day,
                'backgroundColor' => $event->color ?: $calendar->color,
                'borderColor' => $event->color ?: $calendar->color,
                'extendedProps' => [
                    'description' => $event->description,
                    'location' => $event->location,
                ],
            ],
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $event = Event::with(['calendar', 'categories', 'recurrencePattern', 'customer', 'pickupRequest'])->findOrFail($id);

        // Check if user has access to this event's calendar
        $user = Auth::user();
        $calendar = $event->calendar;

        if (! $calendar->is_public && $calendar->owner_id !== $user->id && ! $calendar->shares()->where('user_id', $user->id)->exists()) {
            return response()->json(['error' => 'You do not have permission to view this event.'], 403);
        }

        // Log the event dates for debugging
        Log::debug('Event dates from database:', [
            'id' => $event->id,
            'start_date' => $event->start_date,
            'end_date' => $event->end_date,
            'timezone' => config('app.timezone'),
            'app_timezone' => config('app.timezone'),
        ]);

        // Prepare pickup data if this is a pickup event with linked request
        $pickupData = [];
        if ($event->is_pickup_event && $event->pickupRequest) {
            $pickupDetails = $event->pickupRequest->getPickupDetailsArray();
            $pickupData = [
                'pickup_request_id' => $event->pickupRequest->id,
                'pickup_request_status' => $event->pickupRequest->status,
                'pickup_address' => $pickupDetails['pickup']['address'] ?? null,
                'pickup_items' => $pickupDetails['pickup']['items'] ?? null,
                'pickup_quantity' => $pickupDetails['pickup']['quantity'] ?? null,
                'property_location_details' => $pickupDetails['pickup']['property_location_details'] ?? null,
                'contact_name' => $pickupDetails['contact']['name'] ?? null,
                'contact_phone' => $pickupDetails['contact']['phone'] ?? null,
                'contact_email' => $pickupDetails['contact']['email'] ?? null,
                'pickup_notes' => $pickupDetails['pickup']['other_notes'] ?? null,
                'pickup_details' => $pickupDetails,
            ];

            // Calculate distance and travel time from warehouse to pickup location
            $warehouseLocation = \App\Models\GlobalConfig::getWarehouseLocation();
            if ($warehouseLocation && ! empty($pickupData['pickup_address'])) {
                try {
                    $googleMapsService = app(\App\Services\GoogleMapsService::class);
                    $distanceData = $googleMapsService->getDistanceMatrix(
                        $warehouseLocation,
                        $pickupData['pickup_address'],
                        ['units' => 'imperial', 'mode' => 'driving']
                    );

                    // Add distance data to pickup data
                    $pickupData['distance_data'] = $distanceData;
                    $pickupData['warehouse_location'] = $warehouseLocation;
                } catch (\Exception $e) {
                    \Log::error('Error calculating distance for pickup event', [
                        'event_id' => $event->id,
                        'pickup_request_id' => $event->pickupRequest->id,
                        'warehouse_location' => $warehouseLocation,
                        'pickup_address' => $pickupData['pickup_address'],
                        'error' => $e->getMessage(),
                    ]);

                    $pickupData['distance_data'] = [
                        'success' => false,
                        'error' => 'Could not calculate distance',
                        'distance_text' => 'Unavailable',
                        'duration_text' => 'Unavailable',
                    ];
                    $pickupData['warehouse_location'] = $warehouseLocation;
                }
            }
        }

        return response()->json([
            'id' => $event->id,
            'title' => $event->title,
            'description' => $event->description,
            'start_date' => $event->start_date->toISOString(),
            'end_date' => $event->end_date ? $event->end_date->toISOString() : null,
            'all_day' => $event->all_day,
            'location' => $event->location,
            'color' => $event->color,
            // Event type flags
            'is_pickup_event' => $event->is_pickup_event,
            'is_blockout_event' => $event->is_blockout_event,
            'customer_id' => $event->customer_id,
            // Pickup-specific fields (from pickup request if available)
            ...$pickupData,
            'customer' => $event->customer ? [
                'id' => $event->customer->id,
                'name' => $event->customer->name,
                'email' => $event->customer->email,
                'phone' => $event->customer->phone,
                'address' => $event->customer->address,
            ] : null,
            'assigned_driver' => $event->assignedDriver ? [
                'id' => $event->assignedDriver->id,
                'name' => $event->assignedDriver->name,
                'email' => $event->assignedDriver->email,
            ] : null,
            'calendar' => [
                'id' => $calendar->id,
                'title' => $calendar->title,
                'color' => $calendar->color,
            ],
            'categories' => $event->categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'color' => $category->color,
                ];
            }),
            'recurrence' => $event->recurrencePattern ? [
                'frequency' => $event->recurrencePattern->frequency,
                'interval' => $event->recurrencePattern->interval,
                'days_of_week' => $event->recurrencePattern->days_of_week,
                'days_of_month' => $event->recurrencePattern->days_of_month,
                'months_of_year' => $event->recurrencePattern->months_of_year,
                'until_date' => $event->recurrencePattern->until_date,
                'count' => $event->recurrencePattern->count,
            ] : null,
            'created_at' => $event->created_at,
            'updated_at' => $event->updated_at,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'all_day' => 'boolean',
            'location' => 'nullable|string|max:255',
            'color' => 'nullable|string|max:7',
            'categories' => 'nullable|array',
            'categories.*' => 'exists:event_categories,id',
            'recurrence' => 'nullable|array',
            'recurrence.frequency' => 'required_with:recurrence|in:daily,weekly,monthly,yearly,custom',
            'recurrence.interval' => 'required_with:recurrence|integer|min:1',
            'recurrence.days_of_week' => 'nullable|array',
            'recurrence.days_of_month' => 'nullable|array',
            'recurrence.months_of_year' => 'nullable|array',
            'recurrence.until_date' => 'nullable|date|after:start_date',
            'recurrence.count' => 'nullable|integer|min:1',
            // Event type flags
            'is_pickup_event' => 'boolean',
            'is_blockout_event' => 'boolean',
            'customer_id' => 'nullable|exists:customers,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $event = Event::findOrFail($id);
        $calendar = $event->calendar;

        // Check if user has permission to update this event
        $user = Auth::user();
        $hasEditPermission = $user->hasPermission('edit_events');
        $hasEditOwnPermission = $user->hasPermission('edit_own_events');

        // If user has edit_own_events permission but not edit_events, check if they created the event
        if (! $hasEditPermission && $hasEditOwnPermission && $event->creator_id !== $user->id) {
            return response()->json(['error' => 'You only have permission to edit events you created.'], 403);
        }

        // If user doesn't have either permission, check if they have other ways to edit (calendar owner or shared access)
        if (! $hasEditPermission && ! $hasEditOwnPermission &&
            ($calendar->owner_id !== $user->id && $event->creator_id !== $user->id &&
            ! $calendar->shares()->where('user_id', $user->id)->whereIn('permission', ['edit', 'manage'])->exists())) {
            return response()->json(['error' => 'You do not have permission to update this event.'], 403);
        }

        // Update recurrence pattern if provided
        if ($request->has('recurrence')) {
            if ($event->recurrencePattern) {
                $event->recurrencePattern->update([
                    'frequency' => $request->input('recurrence.frequency'),
                    'interval' => $request->input('recurrence.interval'),
                    'days_of_week' => $request->input('recurrence.days_of_week'),
                    'days_of_month' => $request->input('recurrence.days_of_month'),
                    'months_of_year' => $request->input('recurrence.months_of_year'),
                    'until_date' => $request->input('recurrence.until_date'),
                    'count' => $request->input('recurrence.count'),
                ]);
            } else {
                $recurrencePattern = RecurrencePattern::create([
                    'frequency' => $request->input('recurrence.frequency'),
                    'interval' => $request->input('recurrence.interval'),
                    'days_of_week' => $request->input('recurrence.days_of_week'),
                    'days_of_month' => $request->input('recurrence.days_of_month'),
                    'months_of_year' => $request->input('recurrence.months_of_year'),
                    'until_date' => $request->input('recurrence.until_date'),
                    'count' => $request->input('recurrence.count'),
                ]);

                $event->recurrence_pattern_id = $recurrencePattern->id;
            }
        } elseif ($event->recurrence_pattern_id) {
            // Remove recurrence pattern if it was previously set but not included in the update
            $event->recurrence_pattern_id = null;
        }

        // Laravel will handle timezone conversion automatically
        // The dates come from the client in ISO format
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        // Log the parsed dates for debugging
        Log::debug('Parsed dates from update request:', [
            'original_start' => $request->start_date,
            'original_end' => $request->end_date,
            'parsed_start' => $startDate ? $startDate->format('Y-m-d H:i:s') : null,
            'parsed_end' => $endDate ? $endDate->format('Y-m-d H:i:s') : null,
        ]);

        // Update the event
        $event->update([
            'title' => $request->title,
            'description' => $request->description,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'all_day' => $request->all_day ?? false,
            'location' => $request->location,
            'color' => $request->color,
            // Event type flags
            'is_pickup_event' => $request->is_pickup_event ?? false,
            'is_blockout_event' => $request->is_blockout_event ?? false,
            'customer_id' => $request->customer_id,
        ]);

        // Update categories if provided
        if ($request->has('categories')) {
            $event->categories()->sync($request->categories);
        }

        // Log the event dates for debugging
        Log::debug('Event dates after update:', [
            'id' => $event->id,
            'start_date' => $event->start_date,
            'end_date' => $event->end_date,
            'start_date_iso' => $event->start_date->toISOString(),
            'end_date_iso' => $event->end_date ? $event->end_date->toISOString() : null,
            'app_timezone' => config('app.timezone'),
        ]);

        return response()->json([
            'message' => 'Event updated successfully',
            'event' => [
                'id' => $event->id,
                'title' => $event->title,
                'start' => $event->start_date->toISOString(),
                'end' => $event->end_date ? $event->end_date->toISOString() : null,
                'allDay' => $event->all_day,
                'backgroundColor' => $event->color ?: $calendar->color,
                'borderColor' => $event->color ?: $calendar->color,
                'extendedProps' => [
                    'description' => $event->description,
                    'location' => $event->location,
                ],
            ],
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $event = Event::findOrFail($id);
        $calendar = $event->calendar;

        // Check if user has permission to delete this event
        $user = Auth::user();
        $hasDeletePermission = $user->hasPermission('delete_events');
        $hasDeleteOwnPermission = $user->hasPermission('delete_own_events');

        // If user has delete_own_events permission but not delete_events, check if they created the event
        if (! $hasDeletePermission && $hasDeleteOwnPermission && $event->creator_id !== $user->id) {
            return response()->json(['error' => 'You only have permission to delete events you created.'], 403);
        }

        // If user doesn't have either permission, check if they have other ways to delete (calendar owner or shared access)
        if (! $hasDeletePermission && ! $hasDeleteOwnPermission &&
            ($calendar->owner_id !== $user->id && $event->creator_id !== $user->id &&
            ! $calendar->shares()->where('user_id', $user->id)->whereIn('permission', ['edit', 'manage'])->exists())) {
            return response()->json(['error' => 'You do not have permission to delete this event.'], 403);
        }

        // Soft delete the event
        $event->delete();

        return response()->json(['message' => 'Event deleted successfully']);
    }

    /**
     * Get categories for events.
     */
    public function getCategories()
    {
        $user = Auth::user();

        // Get global categories and user's own categories
        $categories = EventCategory::where('is_global', true)
            ->orWhere('creator_id', $user->id)
            ->get()
            ->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'color' => $category->color,
                    'is_global' => $category->is_global,
                ];
            });

        return response()->json($categories);
    }

    /**
     * Update event dates (for drag and drop).
     */
    public function updateEventDates(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'all_day' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $event = Event::findOrFail($id);
        $calendar = $event->calendar;

        // Check if user has permission to update this event
        $user = Auth::user();
        $hasEditPermission = $user->hasPermission('edit_events');
        $hasEditOwnPermission = $user->hasPermission('edit_own_events');

        // If user has edit_own_events permission but not edit_events, check if they created the event
        if (! $hasEditPermission && $hasEditOwnPermission && $event->creator_id !== $user->id) {
            return response()->json(['error' => 'You only have permission to edit events you created.'], 403);
        }

        // If user doesn't have either permission, check if they have other ways to edit (calendar owner or shared access)
        if (! $hasEditPermission && ! $hasEditOwnPermission &&
            ($calendar->owner_id !== $user->id && $event->creator_id !== $user->id &&
            ! $calendar->shares()->where('user_id', $user->id)->whereIn('permission', ['edit', 'manage'])->exists())) {
            return response()->json(['error' => 'You do not have permission to update this event.'], 403);
        }

        // Parse dates from ISO format
        // No manual timezone conversion needed - Laravel handles it automatically
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        // Log the parsed dates for debugging
        Log::debug('Parsed dates from updateEventDates request:', [
            'original_start' => $request->start_date,
            'original_end' => $request->end_date,
            'parsed_start' => $startDate ? $startDate->format('Y-m-d H:i:s') : null,
            'parsed_end' => $endDate ? $endDate->format('Y-m-d H:i:s') : null,
        ]);

        // Update the event
        $event->update([
            'start_date' => $startDate,
            'end_date' => $endDate,
            'all_day' => $request->all_day ?? $event->all_day,
        ]);

        // Log the event dates for debugging
        Log::debug('Event dates after updateEventDates:', [
            'id' => $event->id,
            'start_date' => $event->start_date,
            'end_date' => $event->end_date,
            'start_date_iso' => $event->start_date->toISOString(),
            'end_date_iso' => $event->end_date ? $event->end_date->toISOString() : null,
            'app_timezone' => config('app.timezone'),
        ]);

        return response()->json([
            'message' => 'Event dates updated successfully',
            'event' => [
                'id' => $event->id,
                'title' => $event->title,
                'start' => $event->start_date->toISOString(),
                'end' => $event->end_date ? $event->end_date->toISOString() : null,
                'allDay' => $event->all_day,
                'backgroundColor' => $event->color ?: $calendar->color,
                'borderColor' => $event->color ?: $calendar->color,
                'extendedProps' => [
                    'description' => $event->description,
                    'location' => $event->location,
                ],
            ],
        ]);
    }

    /**
     * Create a new category.
     */
    public function storeCategory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'color' => 'required|string|max:7',
            'is_global' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = Auth::user();

        // Only admins can create global categories
        if ($request->is_global && ! $user->hasPermission('manage_settings')) {
            return response()->json(['error' => 'You do not have permission to create global categories.'], 403);
        }

        $category = EventCategory::create([
            'name' => $request->name,
            'color' => $request->color,
            'creator_id' => $user->id,
            'is_global' => $request->is_global ?? false,
        ]);

        return response()->json([
            'message' => 'Category created successfully',
            'category' => [
                'id' => $category->id,
                'name' => $category->name,
                'color' => $category->color,
                'is_global' => $category->is_global,
            ],
        ]);
    }
}
