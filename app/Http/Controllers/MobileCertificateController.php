<?php

namespace App\Http\Controllers;

use App\Models\Certificate;
use App\Models\Signature;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class MobileCertificateController extends Controller
{
    /**
     * Display the mobile certificate creation entry point
     */
    public function index()
    {
        return view('mobile.certificates.index');
    }

    /**
     * Start the certificate creation process
     */
    public function start(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'pickup_dropoff_date' => 'required|date',
        ]);

        // Create the certificate (certificate_number will be auto-generated)
        $certificate = Certificate::create([
            'customer_id' => $request->customer_id,
            'scheduled_destruction_date' => now()->addDays(2),
            'pickup_dropoff_date' => $request->pickup_dropoff_date,
            'status' => 'pending',
        ]);

        // Redirect to the first step
        return redirect()->route('mobile.certificates.step1', $certificate);
    }

    /**
     * Step 1: Pickup or Dropoff
     */
    public function step1(Certificate $certificate)
    {
        return view('mobile.certificates.step1', compact('certificate'));
    }

    /**
     * Save Step 1 data
     */
    public function saveStep1(Request $request, Certificate $certificate)
    {
        $request->validate([
            'pickup_dropoff' => 'required|in:Yes,No',
        ]);

        // Save to certificate stats
        $stats = $certificate->stats ?? [];
        $stats['driver_pickup_dropoff'] = $request->pickup_dropoff;
        $certificate->stats = $stats;
        $certificate->save();

        return redirect()->route('mobile.certificates.step2', $certificate);
    }

    /**
     * Step 2: Destruction Method
     */
    public function step2(Certificate $certificate)
    {
        return view('mobile.certificates.step2', compact('certificate'));
    }

    /**
     * Save Step 2 data
     */
    public function saveStep2(Request $request, Certificate $certificate)
    {
        $request->validate([
            'certification_requirement' => 'required|in:certified_destruction,uncertified_destruction,decline_destruction',
        ]);

        // Map certification_requirement to service_selection
        $serviceSelection = $request->certification_requirement;

        // Save to certificate stats
        $stats = $certificate->stats ?? [];
        $stats['client_certification_requirement'] = $request->certification_requirement;
        $certificate->stats = $stats;

        // Save service_selection and destruction_method to the certificate model
        $certificate->service_selection = $serviceSelection;
        $certificate->destruction_method = $request->certification_requirement;
        $certificate->save();

        return redirect()->route('mobile.certificates.step3', $certificate);
    }

    /**
     * Step 3: Manifest Information
     */
    public function step3(Certificate $certificate)
    {
        return view('mobile.certificates.step3', compact('certificate'));
    }

    /**
     * Save Step 3 data
     */
    public function saveStep3(Request $request, Certificate $certificate)
    {
        // Validation rules depend on whether a manifest was provided
        if ($request->has('manifest_provided') && $request->manifest_provided === 'Yes') {
            // Validation rules for the simplified system
            $validationRules = [
                'manifest_provided' => 'required|in:Yes,No',
                'client_total_assets' => 'required|integer|min:0',
                'etech_verified_assets' => 'required|integer|min:0',
            ];

            $request->validate($validationRules);

            // Save to certificate stats (matching desktop version)
            $stats = $certificate->stats ?? [];
            $stats['client_provided_manifest'] = 'yes';

            $certificate->stats = $stats;

            // Save directly to certificate model (using the existing drive count fields for compatibility)
            $certificate->client_manifest_drive_count = $request->client_total_assets;
            $certificate->etech_verified_drive_count = $request->etech_verified_assets;

            // Set device counts to 0 since we're using unified counting
            $certificate->client_manifest_device_count = 0;
            $certificate->etech_verified_device_count = 0;

            $certificate->save();
        } else {
            $request->validate([
                'manifest_provided' => 'required|in:Yes,No',
            ]);

            // Save to certificate stats (matching desktop version)
            $stats = $certificate->stats ?? [];
            $stats['client_provided_manifest'] = 'no';
            $certificate->stats = $stats;

            // Set counts to 0 if no manifest provided
            $certificate->client_manifest_drive_count = 0;
            $certificate->etech_verified_drive_count = 0;
            $certificate->client_manifest_device_count = 0;
            $certificate->etech_verified_device_count = 0;
            $certificate->save();
        }

        return redirect()->route('mobile.certificates.step4', $certificate);
    }

    /**
     * Step 4: Client Signature
     */
    public function step4(Certificate $certificate)
    {
        // Get the contract text based on the certification requirement
        $requirement = $certificate->stats['client_certification_requirement'] ?? '';
        $contractText = $this->getContractText('client', $requirement, $certificate);

        return view('mobile.certificates.step4', compact('certificate', 'contractText'));
    }

    /**
     * Save Step 4 data (Client Signature)
     */
    public function saveStep4(Request $request, Certificate $certificate)
    {
        $request->validate([
            'signatory_name' => 'required|string|max:255',
            'signature_data' => 'required|string',
            'contract_text' => 'required|string',
        ]);

        // Save the signature
        $this->saveSignature($certificate, 'client', $request->signatory_name, $request->signature_data, $request->contract_text);

        return redirect()->route('mobile.certificates.step5', $certificate);
    }

    /**
     * Step 5: Employee Signature
     */
    public function step5(Certificate $certificate)
    {
        // Determine if this is a driver or dropoff signature
        $isDropOff = ($certificate->stats['driver_pickup_dropoff'] ?? '') === 'No';
        $role = $isDropOff ? 'warehouse' : 'driver';

        // Get the contract text
        $contractText = $this->getContractText($role, null, $certificate);

        return view('mobile.certificates.step5', compact('certificate', 'contractText', 'isDropOff', 'role'));
    }

    /**
     * Save Step 5 data (Employee Signature)
     */
    public function saveStep5(Request $request, Certificate $certificate)
    {
        $request->validate([
            'signatory_name' => 'required|string|max:255',
            'signature_data' => 'required|string',
            'contract_text' => 'required|string',
            'role' => 'required|in:driver,warehouse',
        ]);

        // Save the signature
        $this->saveSignature($certificate, $request->role, $request->signatory_name, $request->signature_data, $request->contract_text);

        return redirect()->route('mobile.certificates.success', $certificate);
    }

    /**
     * Success page
     */
    public function success(Certificate $certificate)
    {
        return view('mobile.certificates.success', compact('certificate'));
    }

    /**
     * Helper method to get contract text
     */
    private function getContractText(string $role, ?string $requirement = null, ?Certificate $certificate = null)
    {
        // Determine the contract file based on the role and requirement
        $fileSuffix = null;
        if ($role === 'client') {
            $serviceSelection = $certificate->service_selection ?? '';
            $fileSuffix = match ($serviceSelection) {
                'certified_drive_serialization' => 'client-certification',
                'certified_drive_origin_serialization' => 'client-certification-origin',
                'uncertified_destruction' => 'client-waiver',
                'decline_destruction' => 'client-no-destruction',
                default => null,
            };
        } else if ($role === 'driver' || $role === 'warehouse') {
            // For driver/warehouse, use the existing logic based on pickup/dropoff
            $isDropOff = ($certificate->stats['driver_pickup_dropoff'] ?? '') === 'No';
            if ($role === 'driver' && $isDropOff) {
                // No driver signature needed for drop-off, so no contract text
                return "";
            } else if ($role === 'driver') {
                $fileSuffix = 'driver';
            } else if ($role === 'warehouse') {
                $fileSuffix = 'warehouse';
            }
        }

        // Build the file path
        $filePath = $fileSuffix
            ? resource_path("data/cert-contract-{$role}-{$fileSuffix}-new.json")
            : resource_path("data/cert-contract-{$role}-new.json");

        // If the new file doesn't exist, fall back to the old file
        if (!file_exists($filePath)) {
            $filePath = $fileSuffix
                ? resource_path("data/cert-contract-{$role}-{$fileSuffix}.json")
                : resource_path("data/cert-contract-{$role}.json");
        }

        if (!file_exists($filePath)) {
            Log::error("Contract file not found: {$filePath}");
            return "Contract text not available.";
        }

        $content = json_decode(file_get_contents($filePath), true);

        // Check if this is the new format (has 'text' field instead of 'paragraphs')
        $isNewFormat = isset($content['text']);

        // Prepare the contract text
        $contractText = '';

        if ($isNewFormat) {
            $contractText = $content['text'];
        } else {
            // For old format, join paragraphs with HTML
            $contractText = implode("<br><br>", $content['paragraphs'] ?? []);
        }

        // Replace placeholders
        $contractText = $this->replacePlaceholders($contractText, $certificate);

        return $contractText;
    }

    /**
     * Helper method to replace placeholders in contract text
     */
    private function replacePlaceholders($text, Certificate $certificate)
    {
        if (!$certificate) {
            return $text;
        }

        $stats = $certificate->stats ?? [];
        $currentDate = now()->format('F j, Y');

        // Get service selection text for display
        $serviceSelectionText = match ($certificate->service_selection) {
            'certified_drive_serialization' => 'Certify & Destroy by drive serial number',
            'certified_drive_origin_serialization' => 'Certify & Destroy by both drive serial and serial/tag of the device it came from',
            'uncertified_destruction' => 'Data Device Shredding WITHOUT CERTIFICATION',
            'decline_destruction' => 'Data Destruction Services DECLINED',
            default => $certificate->service_selection ?? ''
        };

        $replacements = [
            '[SIGNATORY_NAME]' => '[NAME]',
            '[NAME]' => '[NAME]',
            '[SERVICE_SELECTION]' => $serviceSelectionText,
            '[CLIENT_CERTIFICATION_REQUIREMENT]' => $stats['client_certification_requirement'] ?? '',
            '[CLIENT_PROVIDED_COUNT]' => $stats['client_provided_count'] ?? '',
            '[CLIENT_MANIFEST_DRIVE_COUNT]' => $certificate->client_manifest_drive_count ?? '',
            '[CLIENT_MANIFEST_DEVICE_COUNT]' => $certificate->client_manifest_device_count ?? '',
            '[ETECH_VERIFIED_DRIVE_COUNT]' => $certificate->etech_verified_drive_count ?? '',
            '[ETECH_VERIFIED_DEVICE_COUNT]' => $certificate->etech_verified_device_count ?? '',
            '[CURRENT_DATE]' => $currentDate,
            '[TECHNICIAN_TOTAL_DRIVES_FOUND]' => $stats['technician_total_drives_found'] ?? 'N/A',
            '[WAREHOUSE_MANIFEST_PROVIDED]' => $stats['warehouse_manifest_provided'] ?? 'No manifest provided',
            '[WAREHOUSE_DEVICE_COUNT]' => $stats['warehouse_device_count'] ?? 'Devices not counted on receipt',
            '[WAREHOUSE_DRIVE_COUNT]' => $stats['warehouse_drive_count'] ?? 'Drives not counted on receipt',
        ];

        foreach ($replacements as $placeholder => $value) {
            $text = str_replace($placeholder, $value, $text);
        }

        return $text;
    }

    /**
     * Helper method to save a signature
     */
    private function saveSignature(Certificate $certificate, $role, $signatoryName, $signatureData, $contractText)
    {
        // Create a unique filename for the signature image
        $filename = Str::uuid() . '.png';
        $path = 'signatures/' . $filename;

        // Convert base64 image data to binary
        $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $signatureData));

        // Store the image
        Storage::disk('public')->put($path, $imageData);

        // Create the signature record
        Signature::create([
            'certificate_id' => $certificate->id,
            'signatory_name' => $signatoryName,
            'signature_data' => $signatureData,
            'signature_date' => now(),
            'ip_address' => request()->ip(),
            'role' => $role,
            'signature_image_path' => $path,
            'contract_text' => $contractText,
            'signature_type' => 'draw',
        ]);
    }
}
