<?php

namespace App\Http\Controllers;

use App\Models\Image;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use ZipArchive;
use Illuminate\Support\Facades\Storage;

class ImageController extends Controller
{
    public function index(Request $request)
    {
        // Get pagination preference from session or request
        $pagination = $request->get('pagination', session('pagination', 25));
        session(['pagination' => $pagination]);

        // Build query with optional search
        $query = Image::query();
        
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('og_filename', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Add sorting
        $sort = $request->get('sort', 'created_at');
        $direction = $request->get('direction', 'desc');
        
        if ($sort === 'size') {
            // Sort by file size (would need to add size column to images table)
            $query->orderBy('created_at', $direction);
        } else {
            $query->orderBy($sort, $direction);
        }

        // Apply pagination with query string preservation
        $images = $query->paginate($pagination)->withQueryString();

        return view('images.index', compact('images', 'pagination'));
    }

    public function upload()
    {
        return view('images.upload');
    }

    public function store(Request $request)
    {
        $request->validate([
            'file' => 'required|image|max:10240',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'alt_text' => 'nullable|string|max:255',
            'context_type' => 'nullable|string|max:255',
            'context_id' => 'nullable|integer',
            'session_id' => 'nullable|string|max:255',
            'is_public' => 'nullable|boolean',
        ]);

        $file = $request->file('file');
        $originalName = $file->getClientOriginalName();

        // Temporary path where the uploaded file is stored
        $tempPath = $file->store('temp', 'public');

        // Define the final path for the WebP file
        $webpPath = str_replace('temp/', 'img/', $tempPath);
        $webpPath = preg_replace('/\.\w+$/', '.webp', $webpPath);

        // Convert the uploaded file to WebP format
        $sourcePath = public_path("storage/{$tempPath}");
        $targetPath = public_path("storage/{$webpPath}");
        if (!Image::convertToWebp($sourcePath, $targetPath, 100, 2000)) {
            return response()->json(['success' => false, 'message' => 'Failed to save the image in WebP format.'], 500);
        }

        // Delete the temporary file
        \Illuminate\Support\Facades\Storage::disk('public')->delete($tempPath);

        // Generate thumbnails
        Image::generateThumbnails($webpPath);

        // If no title, then the title will be the original filename
        $title = $request->input('title', $originalName);

        // Determine order number
        $orderNumber = null;
        if ($request->has('context_type') && $request->has('context_id')) {
            $orderNumber = Image::getNextOrderNumber($request->input('context_type'), $request->input('context_id'));
        } elseif ($request->has('session_id')) {
            $orderNumber = Image::forSession($request->input('session_id'))->max('order_number') + 1;
        }

        // Save the image information to the database
        $image = Image::create([
            'image_path' => $webpPath,
            'og_filename' => $originalName,
            'title' => $title,
            'description' => $request->input('description', null),
            'alt_text' => $request->input('alt_text', null),
            'uploaded_by' => auth()->id(),
            'context_type' => $request->input('context_type'),
            'context_id' => $request->input('context_id'),
            'session_id' => $request->input('session_id'),
            'order_number' => $orderNumber,
            'is_public' => $request->input('is_public', false),
        ]);

        return response()->json([
            'success' => true,
            'image' => $image,
            'thumbnail_url' => $image->getImageSrc('sm'),
            'full_url' => $image->getImageSrc('full'),
        ]);
    }

    /**
     * Store image with immediate context association
     */
    public function storeForContext(Request $request, $contextType, $contextId)
    {
        $request->merge([
            'context_type' => $contextType,
            'context_id' => $contextId,
        ]);

        return $this->store($request);
    }

    /**
     * Store image for session (temporary upload)
     */
    public function storeForSession(Request $request, $sessionId)
    {
        $request->merge([
            'session_id' => $sessionId,
        ]);

        return $this->store($request);
    }

    /**
     * Associate session images with a context
     */
    public function associateSessionImages(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string',
            'context_type' => 'required|string',
            'context_id' => 'required|integer',
        ]);

        $images = Image::forSession($request->input('session_id'))->get();
        
        foreach ($images as $image) {
            $image->associateWithContext(
                $request->input('context_type'),
                $request->input('context_id')
            );
        }

        return response()->json([
            'success' => true,
            'message' => count($images) . ' images associated with context',
            'images' => $images,
        ]);
    }

    /**
     * Reorder images within a context
     */
    public function reorderImages(Request $request)
    {
        $request->validate([
            'image_ids' => 'required|array',
            'image_ids.*' => 'integer|exists:images,id',
        ]);

        $imageIds = $request->input('image_ids');
        
        foreach ($imageIds as $index => $imageId) {
            Image::where('id', $imageId)->update(['order_number' => $index + 1]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Images reordered successfully',
        ]);
    }


    public function destroy(Image $image)
    {
        $image->delete();
        return response()->json([
            'success' => true,
        ]);
    }

    public function fetchImages(Request $request)
    {
        $images = \App\Models\Image::latest()->paginate(50);

        $formattedImages = $images->map(function ($image) {
            return [
                'id' => $image->id,
                'image_path' => $image->getImageSrc('sm'), // Small image path
                'alt_text' => $image->alt_text ?? '',
                'title' => $image->title ?? '',
                'description' => $image->description ?? '',
                'webp_path' => $image->getImageSrc('full'), // Full-size image path
            ];
        });

        return response()->json([
            'success' => true,
            'images' => $formattedImages->values()->toArray(),
            'pagination' => [
                'total' => $images->total(),
                'per_page' => $images->perPage(),
                'current_page' => $images->currentPage(),
                'last_page' => $images->lastPage(),
                'next_page_url' => $images->nextPageUrl(),
                'prev_page_url' => $images->previousPageUrl(),
            ],
        ]);
    }



    public function downloadMultiple(array $imageIds)
    {
        $images = Image::whereIn('id', $imageIds)->get();

        if ($images->isEmpty()) {
            return response()->json(['error' => 'No images found.'], 404);
        }

        if ($images->count() === 1) {
            $image = $images->first();
            $filePath = public_path("storage/{$image->image_path}");

            if (!file_exists($filePath)) {
                return response()->json(['error' => 'Image file not found.'], 404);
            }

            return response()->download($filePath, $image->og_filename);
        }

        $zipFileName = 'images_' . time() . '.zip';
        $zipFilePath = storage_path("app/public/{$zipFileName}");

        $zip = new ZipArchive();

        if ($zip->open($zipFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE)) {
            foreach ($images as $image) {
                $filePath = public_path("storage/{$image->image_path}");
                if (file_exists($filePath)) {
                    $zip->addFile($filePath, basename($filePath));
                }
            }
            $zip->close();
        } else {
            Log::error('Failed to create ZIP file.');
            return response()->json(['error' => 'Failed to create ZIP file.'], 500);
        }

        return response()->download($zipFilePath)->deleteFileAfterSend(true);
    }


    public function show($id)
    {
        // Retrieve the image by its ID
        $image = \App\Models\Image::findOrFail($id);
        
        // Load the uploader relationship
        $image->load('uploader');
        
        // Find where this image is used
        $usages = [];
        
        // Check if used in inventory
        if ($image->context_type === 'inventory' && $image->context_id) {
            $inventory = \App\Models\Inventory::find($image->context_id);
            if ($inventory) {
                $usages[] = [
                    'type' => 'inventory',
                    'model' => $inventory,
                    'description' => "Inventory Item: {$inventory->name}",
                    'link' => route('inventory.show', $inventory),
                    'icon' => 'fa-box'
                ];
            }
        }
        
        // You can add more context types here in the future
        // For example: pickup requests, customer profiles, etc.

        // Pass the image to the view
        return view('images.show', compact('image', 'usages'));
    }

    public function edit(Image $image)
    {
        $previousUrl = url()->previous();
        return view('images.edit', compact('image', 'previousUrl'));
    }


    public function update(Request $request)
{
    $request->validate([
        'id' => 'required|exists:images,id',
        'title' => 'nullable|string|max:255',
        'description' => 'nullable|string|max:1000',
        'image' => 'nullable|string', // Base64 image string
    ]);

    $image = Image::findOrFail($request->id);

    // Update image if provided
    if ($request->has('image')) {
        $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $request->image));

        // Define the new path under `/img`
        $imagePath = 'img/' . uniqid() . '.webp';

        // Store the new image as a WebP file
        Storage::disk('public')->put($imagePath, $imageData);

        // Regenerate thumbnails for the new image
        Image::generateThumbnails($imagePath);

        // Update the image path in the database
        $image->image_path = $imagePath;
    }

    // Update the title and description if provided
    $image->update([
        'title' => $request->title,
        'description' => $request->description,
    ]);

    return redirect()->back()->with('success', 'Image updated successfully.');
}

    public function download(Image $image)
    {
        // Get the full path to the image
        $filePath = public_path("storage/{$image->image_path}");
        
        if (!file_exists($filePath)) {
            abort(404, 'Image file not found.');
        }
        
        // Use original filename if available, otherwise use a generic name
        $filename = $image->og_filename ?: 'image_' . $image->id . '.webp';
        
        return response()->download($filePath, $filename);
    }

}
