<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PickupRequest;
use App\Models\GlobalConfig;
use App\Models\Event;
use App\Models\UserGroup;
use App\Services\RecaptchaService;
use App\Helpers\NotificationHelper;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use App\Models\Image;

class GuestPickupController extends Controller
{
    /**
     * Show the guest pickup request form.
     */
    public function show()
    {
        // Get pickup availability windows for display
        $availabilityWindows = GlobalConfig::getPickupAvailabilityWindows();
        $pickupEventDuration = GlobalConfig::getPickupEventDuration();
        $pickupSchedulingInterval = GlobalConfig::getPickupSchedulingInterval();

        // Get pickup item types
        $pickupItemTypes = GlobalConfig::getPickupItemTypes();
        if (empty($pickupItemTypes)) {
            $pickupItemTypes = GlobalConfig::getDefaultPickupItemTypes();
        }

        return view('guest.pickup-request', compact(
            'availabilityWindows',
            'pickupEventDuration',
            'pickupSchedulingInterval',
            'pickupItemTypes'
        ));
    }

    /**
     * Get available dates with slot counts.
     */
    public function getAvailableSlots(Request $request)
    {
        try {
            $request->validate([
                'offset' => 'nullable|integer|min:0|max:21', // Max 3 weeks
            ]);

            $offset = (int) $request->input('offset', 0);

            // Get pickup calendar
            $pickupCalendarId = GlobalConfig::getPickupCalendarId();
            if (!$pickupCalendarId) {
                return response()->json([
                    'error' => 'Pickup scheduling is currently unavailable. Please contact us directly.'
                ]);
            }

            // Get availability windows and settings
            $availabilityWindows = GlobalConfig::getPickupAvailabilityWindows();
            $eventDuration = GlobalConfig::getPickupEventDuration();
            $bufferTime = GlobalConfig::getPickupBufferTime();
            $bufferDirection = GlobalConfig::getPickupBufferDirection();

        // Generate dates starting from minimum pickup date + offset, show next 5 dates
        $minimumPickupDate = GlobalConfig::getMinimumPickupDate();
        $startDate = $minimumPickupDate->copy()->addDays($offset);
        $maxDate = Carbon::today()->addDays(21); // 3 weeks max

        // Get existing events for the period
        $existingEvents = Event::where('calendar_id', $pickupCalendarId)
            ->where('is_active', true)
            ->where(function ($query) use ($startDate, $maxDate) {
                $query->whereBetween('start_date', [$startDate->startOfDay(), $maxDate->endOfDay()])
                      ->orWhereBetween('end_date', [$startDate->startOfDay(), $maxDate->endOfDay()])
                      ->orWhere(function ($q) use ($startDate, $maxDate) {
                          $q->where('start_date', '<=', $startDate->startOfDay())
                            ->where('end_date', '>=', $maxDate->endOfDay());
                      });
            })
            ->get();

        // Get ALL pickup requests for the period that could conflict with time slots
        // This includes pending, approved, and any other status to prevent double-booking
        $existingPickupRequests = PickupRequest::whereBetween('preferred_pickup_date', [$startDate->startOfDay(), $maxDate->endOfDay()])
            ->get();

        $availableDates = [];
        $datesFound = 0;
        $daysChecked = 0;
        $currentDate = $startDate->copy();

        // Find next 5 dates with available slots
        while ($datesFound < 5 && $currentDate->lte($maxDate)) {
            $dayOfWeek = $currentDate->dayOfWeek;

            // Get available time slots for this day
            $timeSlots = GlobalConfig::getPickupTimeSlots((string)$dayOfWeek, $availabilityWindows);

            $availableSlots = 0;
            foreach ($timeSlots as $timeSlot) {
                // Create slot datetime
                $slotStart = Carbon::parse($currentDate->format('Y-m-d') . ' ' . $timeSlot);
                $slotEnd = $slotStart->copy()->addMinutes($eventDuration);

                // Skip past time slots for today
                if ($currentDate->isToday() && $slotStart->isPast()) {
                    continue;
                }

                $hasConflict = false;

                // Check for conflicts with existing events (including buffer time)
                foreach ($existingEvents as $event) {
                    $eventStart = Carbon::parse($event->start_date);
                    $eventEnd = Carbon::parse($event->end_date);

                    // For all-day events, block the entire day
                    if ($event->all_day) {
                        // Check if the slot date matches the all-day event date
                        if ($slotStart->toDateString() === $eventStart->toDateString()) {
                            $hasConflict = true;
                            break;
                        }
                    } else {
                        // Apply buffer time based on direction for timed events
                        $bufferedStart = $eventStart->copy();
                        $bufferedEnd = $eventEnd->copy();

                        if ($bufferDirection === 'before' || $bufferDirection === 'both') {
                            $bufferedStart->subMinutes($bufferTime);
                        }
                        if ($bufferDirection === 'after' || $bufferDirection === 'both') {
                            $bufferedEnd->addMinutes($bufferTime);
                        }

                        // Check for overlap with buffered time
                        if ($slotStart < $bufferedEnd && $slotEnd > $bufferedStart) {
                            $hasConflict = true;
                            break;
                        }
                    }
                }

                // Check for conflicts with ALL pickup requests (including buffer time)
                // This prevents multiple customers from requesting the same slot
                if (!$hasConflict) {
                    foreach ($existingPickupRequests as $pickupRequest) {
                        $requestStart = Carbon::parse($pickupRequest->preferred_pickup_date);
                        $requestEnd = $requestStart->copy()->addMinutes($eventDuration);

                        // Apply buffer time based on direction
                        $bufferedStart = $requestStart->copy();
                        $bufferedEnd = $requestEnd->copy();

                        if ($bufferDirection === 'before' || $bufferDirection === 'both') {
                            $bufferedStart->subMinutes($bufferTime);
                        }
                        if ($bufferDirection === 'after' || $bufferDirection === 'both') {
                            $bufferedEnd->addMinutes($bufferTime);
                        }

                        // Check for overlap with buffered time
                        if ($slotStart < $bufferedEnd && $slotEnd > $bufferedStart) {
                            $hasConflict = true;
                            break;
                        }
                    }
                }

                if (!$hasConflict) {
                    $availableSlots++;
                }
            }

            // Only include dates with available slots
            if ($availableSlots > 0) {
                $availableDates[] = [
                    'date' => $currentDate->format('Y-m-d'),
                    'display_date' => $currentDate->format('M j'),
                    'day_name' => $currentDate->format('l'),
                    'day_short' => $currentDate->format('D'),
                    'is_today' => $currentDate->isToday(),
                    'is_tomorrow' => $currentDate->isTomorrow(),
                    'available_slots' => $availableSlots
                ];
                $datesFound++;
            }

            $currentDate->addDay();
            $daysChecked++;
        }

        $hasMoreDates = $currentDate->lte($maxDate);

        return response()->json([
            'dates' => $availableDates,
            'has_more' => $hasMoreDates,
            'next_offset' => $offset + $daysChecked
        ]);

        } catch (\Exception $e) {
            Log::error('Error in getAvailableSlots: ' . $e->getMessage());
            return response()->json([
                'error' => 'An error occurred while loading available dates. Please try again.'
            ], 500);
        }
    }

    /**
     * Get available time slots for a specific date.
     */
    public function getTimeSlotsForDate(Request $request)
    {
        // Get minimum pickup date for validation
        $minimumPickupDate = GlobalConfig::getMinimumPickupDate();

        $request->validate([
            'date' => 'required|date|after_or_equal:' . $minimumPickupDate->format('Y-m-d') . '|before_or_equal:' . Carbon::today()->addDays(21)->format('Y-m-d'),
        ]);

        // Parse the date (comes as YYYY-MM-DD string)
        $date = Carbon::parse($request->date);
        $dayOfWeek = $date->dayOfWeek;

        // Double-check that the requested date meets lead time requirements
        if ($date->startOfDay()->lt($minimumPickupDate->startOfDay())) {
            return response()->json([
                'error' => 'This date does not meet the minimum lead time requirements. Please select a later date.'
            ]);
        }

        // Get pickup calendar
        $pickupCalendarId = GlobalConfig::getPickupCalendarId();
        if (!$pickupCalendarId) {
            return response()->json([
                'error' => 'Pickup scheduling is currently unavailable. Please contact us directly.'
            ]);
        }

        // Get availability windows and settings
        $availabilityWindows = GlobalConfig::getPickupAvailabilityWindows();
        $eventDuration = GlobalConfig::getPickupEventDuration();
        $bufferTime = GlobalConfig::getPickupBufferTime();
        $bufferDirection = GlobalConfig::getPickupBufferDirection();

        // Get available time slots for this day
        $timeSlots = GlobalConfig::getPickupTimeSlots((string)$dayOfWeek, $availabilityWindows);

        if (empty($timeSlots)) {
            return response()->json(['slots' => []]);
        }

        // Get existing events for this date
        $startOfDay = $date->copy()->startOfDay();
        $endOfDay = $date->copy()->endOfDay();

        $existingEvents = Event::where('calendar_id', $pickupCalendarId)
            ->where('is_active', true)
            ->where(function ($query) use ($startOfDay, $endOfDay) {
                $query->whereBetween('start_date', [$startOfDay, $endOfDay])
                      ->orWhereBetween('end_date', [$startOfDay, $endOfDay])
                      ->orWhere(function ($q) use ($startOfDay, $endOfDay) {
                          $q->where('start_date', '<=', $startOfDay)
                            ->where('end_date', '>=', $endOfDay);
                      });
            })
            ->get();

        // Get ALL pickup requests for this date to prevent double-booking
        $existingPickupRequests = PickupRequest::whereBetween('preferred_pickup_date', [$startOfDay, $endOfDay])
            ->get();

        // Filter out conflicting time slots
        $availableSlots = [];
        foreach ($timeSlots as $timeSlot) {
            // Create slot datetime
            $slotStart = Carbon::parse($date->format('Y-m-d') . ' ' . $timeSlot);
            $slotEnd = $slotStart->copy()->addMinutes($eventDuration);

            // Skip past time slots for today
            if ($date->isToday() && $slotStart->isPast()) {
                continue;
            }

            $hasConflict = false;

            // Check for conflicts with existing events (including buffer time)
            foreach ($existingEvents as $event) {
                $eventStart = Carbon::parse($event->start_date);
                $eventEnd = Carbon::parse($event->end_date);

                // For all-day events, block the entire day
                if ($event->all_day) {
                    // Check if the slot date matches the all-day event date
                    if ($slotStart->toDateString() === $eventStart->toDateString()) {
                        $hasConflict = true;
                        break;
                    }
                } else {
                    // Apply buffer time based on direction for timed events
                    $bufferedStart = $eventStart->copy();
                    $bufferedEnd = $eventEnd->copy();

                    if ($bufferDirection === 'before' || $bufferDirection === 'both') {
                        $bufferedStart->subMinutes($bufferTime);
                    }
                    if ($bufferDirection === 'after' || $bufferDirection === 'both') {
                        $bufferedEnd->addMinutes($bufferTime);
                    }

                    // Check for overlap with buffered time
                    if ($slotStart < $bufferedEnd && $slotEnd > $bufferedStart) {
                        $hasConflict = true;
                        break;
                    }
                }
            }

            // Check for conflicts with ALL pickup requests (including buffer time)
            if (!$hasConflict) {
                foreach ($existingPickupRequests as $pickupRequest) {
                    $requestStart = Carbon::parse($pickupRequest->preferred_pickup_date);
                    $requestEnd = $requestStart->copy()->addMinutes($eventDuration);

                    // Apply buffer time based on direction
                    $bufferedStart = $requestStart->copy();
                    $bufferedEnd = $requestEnd->copy();

                    if ($bufferDirection === 'before' || $bufferDirection === 'both') {
                        $bufferedStart->subMinutes($bufferTime);
                    }
                    if ($bufferDirection === 'after' || $bufferDirection === 'both') {
                        $bufferedEnd->addMinutes($bufferTime);
                    }

                    // Check for overlap with buffered time
                    if ($slotStart < $bufferedEnd && $slotEnd > $bufferedStart) {
                        $hasConflict = true;
                        break;
                    }
                }
            }

            if (!$hasConflict) {
                $availableSlots[] = [
                    'time' => $timeSlot,
                    'display' => Carbon::parse($timeSlot)->format('g:i A'),
                    'datetime' => $slotStart->format('Y-m-d\TH:i:s')
                ];
            }
        }

        return response()->json([
            'date' => $date->format('Y-m-d'),
            'display_date' => $date->format('l, M j'),
            'slots' => $availableSlots
        ]);
    }

    /**
     * Store a new pickup request.
     */
    public function store(Request $request, RecaptchaService $recaptchaService)
    {
        Log::info('Guest pickup request store method called', [
            'request_data' => $request->except(['recaptcha_token']), // Don't log the token
            'ip' => $request->ip()
        ]);

        // Validate reCAPTCHA if configured
        if ($recaptchaService->isEnabled()) {
            $recaptchaToken = $request->input('recaptcha_token');

            if (!$recaptchaToken) {
                Log::warning('reCAPTCHA token missing from pickup request', [
                    'ip' => $request->ip()
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Security verification failed. Please refresh the page and try again.'
                ], 422);
            }

            $recaptchaResult = $recaptchaService->verify($recaptchaToken, 'pickup_request', 0.5);

            if (!$recaptchaResult['success']) {
                Log::warning('reCAPTCHA verification failed for pickup request', [
                    'ip' => $request->ip(),
                    'error' => $recaptchaResult['error'],
                    'score' => $recaptchaResult['score'] ?? null
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Security verification failed. Please try again.'
                ], 422);
            }

            Log::info('reCAPTCHA verification successful for pickup request', [
                'ip' => $request->ip(),
                'score' => $recaptchaResult['score']
            ]);
        }

        // Get minimum pickup date for validation (will do detailed validation after timezone conversion)
        $minimumPickupDate = GlobalConfig::getMinimumPickupDate();

        $validated = $request->validate([
            'contact_name' => 'required|string|max:255',
            'business_name' => 'nullable|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:255',
            'pickup_address' => 'required|string|max:1000',
            'pickup_items' => 'nullable|string|max:1000', // Made optional since we have guided fields now
            'pickup_quantity' => 'nullable|string|max:500', // Made optional since we have guided fields now
            'property_location_details' => 'required|string|max:1000',
            'other_notes' => 'nullable|string|max:1000',
            'preferred_pickup_date' => 'required|date',
            // New guided pickup fields
            'accessibility_level' => 'required|in:easy,moderate,difficult',
            'driver_instructions' => 'required|string|max:1000',
            'load_size' => 'required|in:small,medium,large',
            'item_types' => 'required|array|min:1',
            'item_types.*' => 'string|in:small_electronics,appliances,peripheral_devices,batteries,crt_tvs,flatscreen_tvs,tote_swap,gaylord_swap,servers,laptops,desktops,large_appliances,other',
            'item_specifics' => 'required|string|max:2000',
        ]);

        // No timezone conversion needed - Laravel handles everything automatically
        // Just ensure we have a valid datetime string
        if ($validated['preferred_pickup_date']) {
            // Parse the datetime and let Laravel handle timezone conversion automatically
            $validated['preferred_pickup_date'] = Carbon::parse($validated['preferred_pickup_date']);
        }

        // Double-check that the requested date meets lead time requirements
        $requestedDate = Carbon::parse($validated['preferred_pickup_date']);
        if ($requestedDate->lt($minimumPickupDate)) {
            return response()->json([
                'success' => false,
                'message' => 'The selected pickup time does not meet the minimum lead time requirements. Please select a later time.',
                'errors' => ['preferred_pickup_date' => ['This pickup time does not meet the minimum lead time requirements.']]
            ], 422);
        }

        // Validate that the selected time slot is still available
        $pickupCalendarId = GlobalConfig::getPickupCalendarId();
        $eventDuration = GlobalConfig::getPickupEventDuration();
        $bufferTime = GlobalConfig::getPickupBufferTime();
        $bufferDirection = GlobalConfig::getPickupBufferDirection();
        $requestedStart = Carbon::parse($validated['preferred_pickup_date']);
        $requestedEnd = $requestedStart->copy()->addMinutes($eventDuration);

        // Check for conflicts with existing events (including buffer time)
        $conflictingEvents = Event::where('calendar_id', $pickupCalendarId)
            ->where('is_active', true)
            ->get()
            ->filter(function ($event) use ($requestedStart, $requestedEnd, $bufferTime, $bufferDirection) {
                $eventStart = Carbon::parse($event->start_date);
                $eventEnd = Carbon::parse($event->end_date);

                // For all-day events, block the entire day
                if ($event->all_day) {
                    // Check if the requested date matches the all-day event date
                    return $requestedStart->toDateString() === $eventStart->toDateString();
                } else {
                    // Apply buffer time based on direction for timed events
                    $bufferedStart = $eventStart->copy();
                    $bufferedEnd = $eventEnd->copy();

                    if ($bufferDirection === 'before' || $bufferDirection === 'both') {
                        $bufferedStart->subMinutes($bufferTime);
                    }
                    if ($bufferDirection === 'after' || $bufferDirection === 'both') {
                        $bufferedEnd->addMinutes($bufferTime);
                    }

                    // Check for overlap with buffered time
                    return $requestedStart < $bufferedEnd && $requestedEnd > $bufferedStart;
                }
            });

        if ($conflictingEvents->isNotEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Sorry, this time slot is no longer available. Please select a different time.',
                'errors' => ['preferred_pickup_date' => ['This time slot is no longer available.']]
            ], 422);
        }

        // Check for conflicts with ALL other pickup requests (including buffer time)
        $conflictingRequests = PickupRequest::all()
            ->filter(function ($pickupRequest) use ($requestedStart, $requestedEnd, $eventDuration, $bufferTime, $bufferDirection) {
                $existingStart = Carbon::parse($pickupRequest->preferred_pickup_date);
                $existingEnd = $existingStart->copy()->addMinutes($eventDuration);

                // Apply buffer time based on direction
                $bufferedStart = $existingStart->copy();
                $bufferedEnd = $existingEnd->copy();

                if ($bufferDirection === 'before' || $bufferDirection === 'both') {
                    $bufferedStart->subMinutes($bufferTime);
                }
                if ($bufferDirection === 'after' || $bufferDirection === 'both') {
                    $bufferedEnd->addMinutes($bufferTime);
                }

                // Check for overlap with buffered time
                return $requestedStart < $bufferedEnd && $requestedEnd > $bufferedStart;
            });

        if ($conflictingRequests->isNotEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Sorry, this time slot is no longer available. Please select a different time.',
                'errors' => ['preferred_pickup_date' => ['This time slot is no longer available.']]
            ], 422);
        }

        try {
            // Create the pickup request with status set to 'incoming' for guest submissions
            $pickupRequest = PickupRequest::create(array_merge($validated, [
                'status' => 'incoming', // Guest submissions start as 'incoming' until staff assigns customer
                'submitted_at' => now(),
            ]));
            Log::info('Guest pickup request created successfully', [
                'pickup_request_id' => $pickupRequest->id,
                'status' => $pickupRequest->status
            ]);

            // Log the pickup request creation
            $pickupRequest->logCreation('guest');

            // Store additional metadata and structured pickup details in JSON format
            $pickupRequest->updatePickupDetails([
                'submission' => [
                    'source' => 'guest_form',
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'submitted_at' => now()->toISOString(),
                ],
                'form_data' => [
                    'original_submission' => $validated,
                ],
                'structured_details' => [
                    'accessibility' => [
                        'level' => $validated['accessibility_level'],
                        'description' => $this->getAccessibilityDescription($validated['accessibility_level']),
                    ],
                    'load_info' => [
                        'size' => $validated['load_size'],
                        'description' => $this->getLoadSizeDescription($validated['load_size']),
                    ],
                    'items' => [
                        'types' => $validated['item_types'],
                        'type_labels' => $this->getItemTypeLabels($validated['item_types']),
                        'specifics' => $validated['item_specifics'],
                    ],
                    'instructions' => [
                        'driver_arrival' => $validated['driver_instructions'],
                        'property_location' => $validated['property_location_details'],
                    ]
                ]
            ]);

            Log::info('Guest pickup request metadata updated successfully');

            // Process uploaded images if any
            $sessionId = $request->input('session_id') ?: (session()->getId() . '_pickup');
            if ($sessionId) {
                $sessionKey = 'pickup_images_' . $sessionId;
                $tempImages = session($sessionKey, []);
                
                if (!empty($tempImages)) {
                    foreach ($tempImages as $index => $tempImage) {
                        try {
                            // Move image from temp to permanent location
                            $permanentPath = 'img/pickup-requests/' . $pickupRequest->id . '/' . basename($tempImage['path']);
                            Storage::disk('public')->move($tempImage['path'], $permanentPath);
                            
                            // Create Image record
                            $image = Image::create([
                                'image_path' => $permanentPath,
                                'og_filename' => $tempImage['name'],
                                'title' => 'Pickup Request #' . $pickupRequest->id . ' - Image ' . ($index + 1),
                                'description' => 'Image uploaded for pickup request',
                                'alt_text' => 'Pickup request item image',
                                'uploaded_by' => null, // Guest upload
                            ]);
                            
                            // Convert to WebP and generate thumbnails
                            $sourcePath = storage_path('app/public/' . $permanentPath);
                            $webpPath = preg_replace('/\.\w+$/', '.webp', $permanentPath);
                            $targetPath = storage_path('app/public/' . $webpPath);
                            
                            if (Image::convertToWebp($sourcePath, $targetPath, 85, 1250)) {
                                // Update image path to WebP version
                                $image->image_path = $webpPath;
                                $image->save();
                                
                                // Delete original if it's not already WebP
                                if ($permanentPath !== $webpPath) {
                                    Storage::disk('public')->delete($permanentPath);
                                }
                                
                                // Generate thumbnails
                                Image::generateThumbnails($webpPath);
                            }
                            
                            // Link image to pickup request
                            $pickupRequest->images()->attach($image->id, ['sort_order' => $index]);
                            
                        } catch (\Exception $e) {
                            Log::error('Error processing pickup request image', [
                                'error' => $e->getMessage(),
                                'temp_path' => $tempImage['path']
                            ]);
                        }
                    }
                    
                    // Clear session images
                    session()->forget($sessionKey);
                }
            }

            // Send notifications to configured user groups
            try {
                $notificationUserGroupIds = GlobalConfig::getPickupNotificationUserGroups();

                if (!empty($notificationUserGroupIds)) {
                    $userGroups = UserGroup::whereIn('id', $notificationUserGroupIds)->get();

                    if ($userGroups->isNotEmpty()) {
                        $title = 'New Pickup Request Submitted';
                        $message = "A new pickup request has been submitted by {$pickupRequest->contact_name}" .
                                  ($pickupRequest->business_name ? " from {$pickupRequest->business_name}" : '') .
                                  ". Pickup requested for " . $pickupRequest->preferred_pickup_date->format('l, M j, Y \a\t g:i A') .
                                  " at {$pickupRequest->pickup_address}.";

                        $linkUrl = route('pickup-requests.show', $pickupRequest->id);
                        $linkText = 'View Request';

                        NotificationHelper::notifyUserGroups(
                            $userGroups->all(), // Use ->all() instead of ->toArray() to preserve objects
                            $title,
                            $message,
                            'normal', // urgency
                            $linkUrl,
                            $linkText,
                            now()->addDays(7), // expires in 7 days
                            true, // is dismissible
                            false // auto dismiss
                        );

                        Log::info('Pickup request notifications sent successfully', [
                            'pickup_request_id' => $pickupRequest->id,
                            'user_groups' => $userGroups->pluck('name')->toArray(),
                            'user_group_count' => $userGroups->count()
                        ]);
                    } else {
                        Log::warning('No valid user groups found for pickup notifications', [
                            'configured_group_ids' => $notificationUserGroupIds
                        ]);
                    }
                } else {
                    Log::info('No user groups configured for pickup notifications');
                }
            } catch (\Exception $e) {
                // Log the error but don't fail the pickup request submission
                Log::error('Failed to send pickup request notifications', [
                    'pickup_request_id' => $pickupRequest->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            // Send email notifications to configured email addresses
            try {
                $notificationEmails = GlobalConfig::getPickupSchedulingNotificationEmails();

                if (!empty($notificationEmails)) {
                    $emailTemplateService = app(\App\Services\EmailTemplateService::class);
                    
                    // Check if we have a custom template
                    if ($emailTemplateService->templateExists('pickup_request_notification')) {
                        // Use the custom template
                        $templateData = [
                            'pickup_request' => $pickupRequest,
                            'title' => 'New Pickup Request Submitted',
                            'type' => 'new_request',
                        ];
                        
                        foreach ($notificationEmails as $email) {
                            Mail::to($email)->send(new \App\Mail\TemplatedMail('pickup_request_notification', $templateData));
                        }
                    } else {
                        // Fall back to the original mail class
                        $emailData = [
                            'pickupRequest' => $pickupRequest,
                            'title' => 'New Pickup Request Submitted',
                            'type' => 'new_request',
                        ];

                        foreach ($notificationEmails as $email) {
                            Mail::to($email)->send(new \App\Mail\PickupRequestNotification($emailData));
                        }
                    }

                    Log::info('Pickup request email notifications sent successfully', [
                        'pickup_request_id' => $pickupRequest->id,
                        'email_count' => count($notificationEmails)
                    ]);
                }
            } catch (\Exception $e) {
                // Log the error but don't fail the pickup request submission
                Log::error('Failed to send pickup request email notifications', [
                    'pickup_request_id' => $pickupRequest->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            // Check if this is an AJAX request or regular form submission
            if ($request->expectsJson()) {
                // Set session token for secure access to success page
                session(['pickup_success_access' => [
                    'pickup_request_id' => $pickupRequest->id,
                    'timestamp' => now()->timestamp,
                    'expires_at' => now()->addHours(24)->timestamp // Expires in 24 hours
                ]]);

                // Return JSON response for AJAX requests
                return response()->json([
                    'success' => true,
                    'message' => 'Your pickup request has been submitted successfully! We will contact you within 24 hours to confirm your pickup.',
                    'request_id' => $pickupRequest->id,
                    'redirect_url' => route('guest.pickup-request.success', $pickupRequest->id)
                ]);
            } else {
                // Set session token for secure access to success page
                session(['pickup_success_access' => [
                    'pickup_request_id' => $pickupRequest->id,
                    'timestamp' => now()->timestamp,
                    'expires_at' => now()->addHours(24)->timestamp // Expires in 24 hours
                ]]);

                // Redirect to success page for regular form submissions
                return redirect()->route('guest.pickup-request.success', $pickupRequest->id)
                    ->with('success', 'Your pickup request has been submitted successfully! We will contact you within 24 hours to confirm your pickup.');
            }

        } catch (\Exception $e) {
            Log::error('Error creating guest pickup request', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while submitting your pickup request. Please try again.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the pickup request success page.
     */
    public function success(PickupRequest $pickupRequest)
    {
        // Validate session-based access to success page
        $sessionAccess = session('pickup_success_access');
        
        if (!$sessionAccess) {
            Log::warning('Unauthorized access attempt to pickup success page', [
                'pickup_request_id' => $pickupRequest->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent()
            ]);
            return redirect()->route('guest.pickup-request')
                ->with('error', 'Access expired. Please submit a new pickup request.');
        }

        // Check if session has expired (24 hours)
        if (now()->timestamp > $sessionAccess['expires_at']) {
            Log::info('Expired session access attempt to pickup success page', [
                'pickup_request_id' => $pickupRequest->id,
                'session_expired_at' => $sessionAccess['expires_at'],
                'current_timestamp' => now()->timestamp
            ]);
            session()->forget('pickup_success_access');
            return redirect()->route('guest.pickup-request')
                ->with('error', 'Access expired. Please submit a new pickup request.');
        }

        // Verify the pickup request ID matches the session
        if ($sessionAccess['pickup_request_id'] !== $pickupRequest->id) {
            Log::warning('Invalid pickup request ID access attempt', [
                'requested_id' => $pickupRequest->id,
                'session_id' => $sessionAccess['pickup_request_id'],
                'ip_address' => request()->ip()
            ]);
            session()->forget('pickup_success_access');
            return redirect()->route('guest.pickup-request')
                ->with('error', 'Invalid access. Please submit a new pickup request.');
        }

        // Ensure the pickup request exists and was submitted by a guest
        if (!$pickupRequest || $pickupRequest->status !== 'incoming') {
            session()->forget('pickup_success_access');
            return redirect()->route('guest.pickup-request')
                ->with('error', 'Pickup request not found or invalid.');
        }

        // Load related data for display
        $pickupRequest->load(['customer', 'event']);

        // Get contact information from global config
        $contactEmail = GlobalConfig::getValue('site_email');
        $contactPhone = GlobalConfig::getValue('site_phone');
        $websiteLink = GlobalConfig::getValue('site_website');

        return view('guest.pickup-request-success', compact('pickupRequest', 'contactEmail', 'contactPhone', 'websiteLink'));
    }

    /**
     * Get lead time information for display to guests.
     */
    public function getLeadTimeInfo()
    {
        $leadTimeDays = GlobalConfig::getPickupLeadTimeDays();
        $leadTimeHours = GlobalConfig::getPickupLeadTimeHours();
        $businessDaysOnly = GlobalConfig::getPickupLeadTimeBusinessDaysOnly();
        $bufferTime = GlobalConfig::getPickupBufferTime();
        $minimumPickupDate = GlobalConfig::getMinimumPickupDate();

        // Build a user-friendly message
        $message = '';

        if ($leadTimeDays > 0 || $leadTimeHours > 0) {
            $parts = [];

            if ($leadTimeDays > 0) {
                $dayText = $leadTimeDays === 1 ? 'day' : 'days';
                $parts[] = "{$leadTimeDays} {$dayText}";
            }

            if ($leadTimeHours > 0) {
                $hourText = $leadTimeHours === 1 ? 'hour' : 'hours';
                $parts[] = "{$leadTimeHours} {$hourText}";
            }

            $timeText = implode(' and ', $parts);
            $dayTypeText = $businessDaysOnly ? 'business days' : 'days';

            if ($businessDaysOnly && $leadTimeDays > 0) {
                $message = "Pickup requests require at least {$timeText} advance notice ({$dayTypeText} only). ";
            } else {
                $message = "Pickup requests require at least {$timeText} advance notice. ";
            }

            $message .= "The earliest available pickup date is " . $minimumPickupDate->format('l, M j, Y') . ".";
        } else {
            $message = "Pickup requests can be scheduled for any available time slot.";
        }

        // Add buffer time information if configured
        if ($bufferTime > 0) {
            $bufferMinutes = $bufferTime;
            if ($bufferMinutes >= 60) {
                $hours = floor($bufferMinutes / 60);
                $minutes = $bufferMinutes % 60;
                $bufferText = $hours . ($hours === 1 ? ' hour' : ' hours');
                if ($minutes > 0) {
                    $bufferText .= ' and ' . $minutes . ($minutes === 1 ? ' minute' : ' minutes');
                }
            } else {
                $bufferText = $bufferMinutes . ($bufferMinutes === 1 ? ' minute' : ' minutes');
            }

            $message .= " Please note that a {$bufferText} buffer time is maintained between pickup appointments.";
        }

        return response()->json([
            'message' => $message,
            'minimum_date' => $minimumPickupDate->format('Y-m-d'),
            'minimum_datetime' => $minimumPickupDate->toISOString()
        ]);
    }

    /**
     * Get human-readable description for accessibility level.
     */
    private function getAccessibilityDescription(string $level): string
    {
        return match($level) {
            'easy' => 'Easy Access - First level storage room, garage, outside, front room, etc.',
            'moderate' => 'Moderate Access - Some stairs or obstacles, but manageable',
            'difficult' => 'Difficult Access - Multiple stairs, tight spaces, or heavy obstacles',
            default => 'Unknown accessibility level'
        };
    }

    /**
     * Get human-readable description for load size.
     */
    private function getLoadSizeDescription(string $size): string
    {
        return match($size) {
            'small' => 'Small Load - One or two flatscreen TVs, a few computers, small appliances, microwave, etc. (fits in midsize SUV or less)',
            'medium' => 'Medium Load - A few pallets, totes, gaylords that are ready to be picked up, but can still be reasonably moved by 1 person with handtruck, pallet jack, carts, etc.',
            'large' => 'Large or Heavy Load - Heavy objects like a piano, conference system, slot machines, etc. that likely require 2 or more people to pickup, or large loads like storage unit cleanouts, overflowing totes, or any other service requiring specialized handling',
            default => 'Unknown load size'
        };
    }

    /**
     * Get human-readable labels for item types.
     */
    private function getItemTypeLabels(array $types): array
    {
        $labels = [
            'small_electronics' => 'Small Electronics',
            'appliances' => 'Appliances',
            'peripheral_devices' => 'Peripheral Devices',
            'batteries' => 'Batteries',
            'crt_tvs' => 'CRT TVs',
            'flatscreen_tvs' => 'Flatscreen TV(s)',
            'tote_swap' => 'Tote Swap',
            'gaylord_swap' => 'Gaylord Swap',
            'servers' => 'Servers',
            'laptops' => 'Laptops',
            'desktops' => 'Desktops',
            'large_appliances' => 'Large Appliances',
            'other' => 'Other',
        ];

        return array_map(fn($type) => $labels[$type] ?? $type, $types);
    }

    /**
     * Handle image upload for pickup request
     */
    public function uploadImage(Request $request)
    {
        try {
            $request->validate([
                'image' => 'required|image|mimes:jpeg,jpg,png,webp|max:10240', // 10MB max
                'session_id' => 'required|string'
            ]);

            // Store the uploaded image temporarily
            $file = $request->file('image');
            $fileName = 'pickup_' . uniqid() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('temp/pickup-images', $fileName, 'public');

            // Store image info in session
            $sessionId = $request->input('session_id');
            $sessionKey = 'pickup_images_' . $sessionId;
            $images = session($sessionKey, []);
            
            $imageData = [
                'id' => uniqid(),
                'path' => $path,
                'name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime' => $file->getMimeType(),
                'uploaded_at' => now()->toISOString()
            ];
            
            $images[] = $imageData;
            
            // Limit to 10 images
            if (count($images) > 10) {
                // Remove the oldest image
                $removed = array_shift($images);
                Storage::disk('public')->delete($removed['path']);
            }
            
            session([$sessionKey => $images]);

            return response()->json([
                'success' => true,
                'image' => $imageData,
                'message' => 'Image uploaded successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error uploading pickup request image', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to upload image. Please try again.'
            ], 500);
        }
    }

    /**
     * Delete uploaded image
     */
    public function deleteImage(Request $request, $id)
    {
        try {
            $sessionId = $request->input('session_id');
            $sessionKey = 'pickup_images_' . $sessionId;
            $images = session($sessionKey, []);
            
            // Find and remove the image
            $images = array_filter($images, function($img) use ($id) {
                if ($img['id'] === $id) {
                    // Delete the file
                    Storage::disk('public')->delete($img['path']);
                    return false;
                }
                return true;
            });
            
            // Re-index array
            $images = array_values($images);
            session([$sessionKey => $images]);

            return response()->json([
                'success' => true,
                'message' => 'Image deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting pickup request image', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete image.'
            ], 500);
        }
    }
}
