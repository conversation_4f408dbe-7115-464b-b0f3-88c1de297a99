<?php

namespace App\Http\Controllers;

use App\Models\Form;
use App\Models\FormSubmission;
use App\Models\FormSubmissionHtmlContent;
use App\Models\Customer;
use App\Models\GlobalConfig;
use App\Models\Activity;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PublicFormController extends Controller
{
    /**
     * Display the public form
     */
    public function show($slug)
    {
        $form = Form::where('slug', $slug)
            ->where('is_active', true)
            ->with('fields')
            ->firstOrFail();

        return view('forms.public.show', compact('form'));
    }

    /**
     * Handle form submission
     */
    public function submit(Request $request, $slug)
    {
        $form = Form::where('slug', $slug)
            ->where('is_active', true)
            ->with('fields')
            ->firstOrFail();

        // Build validation rules dynamically
        $rules = [];

        // Add contact field validation based on form requirements
        if ($form->requires_customer_link) {
            $requiredFields = $form->getRequiredContactFields();
            
            // Full name is always required
            $rules['submitter_name'] = 'required|string|max:255';
            
            // Business name is optional
            $rules['business_name'] = 'nullable|string|max:255';
            
            if ($requiredFields['email'] ?? false) {
                $rules['submitter_email'] = 'required|email|max:255';
            }
            if ($requiredFields['phone'] ?? false) {
                $rules['submitter_phone'] = 'required|string|max:20';
            }
            if ($requiredFields['address'] ?? false) {
                $rules['address'] = 'required|string|max:500';
            }
        } else {
            // Minimal requirements for forms not requiring customer link
            $rules['submitter_name'] = 'nullable|string|max:255';
            $rules['submitter_email'] = 'nullable|email|max:255';
        }

        if ($form->requires_signature) {
            $rules['signature'] = 'required|string';
            $rules['signature_full_name'] = 'required|string|max:255';
            $rules['signature_title'] = 'required|string|max:255';
            $rules['signature_date'] = 'required|date';
        }

        // Add reCAPTCHA validation if enabled
        if ($form->recaptcha_enabled && config('services.recaptcha.secret_key')) {
            $rules['recaptcha_token'] = 'required|string';
        }

        // Add field-specific validation
        foreach ($form->fields as $field) {
            if ($field->isInput() && $field->name) {
                $fieldRules = $field->getValidationRulesString();
                // Add the field to validation rules (with rules if they exist, otherwise just allow it)
                $rules['fields.' . $field->name] = $fieldRules ?: 'nullable';
            }
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            // Handle AJAX requests with JSON response
            if ($request->expectsJson() || $request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please fix the following errors:',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $validated = $validator->validated();

        // Verify reCAPTCHA if enabled
        if ($form->recaptcha_enabled && config('services.recaptcha.secret_key')) {
            $recaptchaResponse = $this->verifyRecaptcha($validated['recaptcha_token'], $request->ip());
            
            if (!$recaptchaResponse['success'] || $recaptchaResponse['score'] < 0.5) {
                if ($request->expectsJson() || $request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Security verification failed. Please try again.',
                        'errors' => ['recaptcha' => ['reCAPTCHA verification failed']]
                    ], 422);
                }
                
                return redirect()->back()
                    ->withErrors(['recaptcha' => 'Security verification failed. Please try again.'])
                    ->withInput();
            }
        }

        DB::transaction(function () use ($form, $validated, $request, &$submission, &$htmlFieldsToProcess) {
            // Note: Customer linking is now handled manually by staff during review process
            // No automatic customer linking based on email

            // Prepare form data including business fields
            $formData = $validated['fields'] ?? [];
            
            // Store HTML fields for later processing (after submission is created)
            $htmlFieldsToProcess = [];
            if ($form->save_html_content) {
                $htmlFieldsToProcess = $form->fields()->where('type', 'html')->get();
            }
            
            // Add contact information to form data for forms requiring customer link
            if ($form->requires_customer_link) {
                $requiredFields = $form->getRequiredContactFields();
                
                // Determine customer type and names based on business name presence
                $hasBusiness = !empty($validated['business_name']);
                
                if ($hasBusiness) {
                    // Business customer: business_name becomes name, full name becomes contact
                    $formData['customer_type'] = 'Business';
                    $formData['business_name'] = $validated['business_name'];
                    $formData['contact_person'] = $validated['submitter_name']; // Full name becomes contact person
                } else {
                    // Residential customer: full name becomes name
                    $formData['customer_type'] = 'Residential';
                }
                
                if ($requiredFields['address'] ?? false || isset($validated['address'])) {
                    $formData['address'] = $validated['address'] ?? null;
                }
            }

            // Prepare signature metadata if signature is required
            $signatureMetadata = [];
            if ($form->requires_signature && isset($validated['signature'])) {
                $signatureMetadata = [
                    'signature_full_name' => $validated['signature_full_name'] ?? null,
                    'signature_title' => $validated['signature_title'] ?? null,
                    'signature_date' => $validated['signature_date'] ?? null,
                ];
            }

            // Determine the customer name and contact based on business name presence
            $hasBusiness = !empty($validated['business_name']);
            $customerName = $hasBusiness ? $validated['business_name'] : $validated['submitter_name'];
            $contactName = $hasBusiness ? $validated['submitter_name'] : null;

            // Create submission
            $submission = FormSubmission::create([
                'form_id' => $form->id,
                'customer_id' => null,  // Always null - staff must manually link customers
                'status' => FormSubmission::STATUS_SUBMITTED,
                'form_data' => $formData,
                'submitter_name' => $customerName,  // Business name OR full name
                'submitter_email' => $validated['submitter_email'],
                'submitter_phone' => $validated['submitter_phone'] ?? null,
                'submitter_contact' => $contactName,  // Full name if business, null if residential
                'signature_data' => $validated['signature'] ?? null,
                'signed_at' => isset($validated['signature']) ? now() : null,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'metadata' => array_merge([
                    'submitted_at' => now()->toIso8601String(),
                    'form_version' => $form->updated_at->toIso8601String(),
                ], $signatureMetadata),
            ]);

            // Process and save HTML content if enabled
            if ($form->save_html_content && !empty($htmlFieldsToProcess)) {
                foreach ($htmlFieldsToProcess as $field) {
                    // Process the HTML content with template variables
                    $processedContent = $submission->processHtmlContent($field->content);
                    
                    // Save to the dedicated HTML content table
                    FormSubmissionHtmlContent::create([
                        'form_submission_id' => $submission->id,
                        'form_field_id' => $field->id,
                        'field_name' => $field->name ?: 'html_field_' . $field->id,
                        'field_label' => $field->label ?: 'HTML Content',
                        'original_content' => $field->content,
                        'processed_content' => $processedContent,
                        'processed_at' => now(),
                    ]);
                }
            }

            // Log activity
            if (class_exists('App\\Models\\Activity')) {
                Activity::create([
                    'action' => 'form_submitted',
                    'description' => "Form '{$form->name}' submitted by {$validated['submitter_email']}",
                    'model_type' => 'FormSubmission',
                    'model_id' => $submission->id,
                ]);
            }
        });

        // Send notification emails if configured
        $this->sendNotifications($form, $submission);

        // Send user group notifications if configured
        $this->sendUserGroupNotifications($form, $submission);

        // Generate secure token for thank you page access
        $token = hash('sha256', $submission->id . $submission->created_at . config('app.key'));

        // Handle AJAX requests with JSON response
        if ($request->expectsJson() || $request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Form submitted successfully!',
                'redirect_url' => route('forms.public.thank-you', [
                    'slug' => $form->slug,
                    'submission' => $submission->id,
                    'token' => $token
                ])
            ]);
        }

        return redirect()->route('forms.public.thank-you', [
            'slug' => $form->slug,
            'submission' => $submission->id,
            'token' => $token
        ]);
    }

    /**
     * Show thank you page
     */
    public function thankYou(Request $request, $slug, $submissionId)
    {
        $form = Form::where('slug', $slug)->firstOrFail();
        $submission = FormSubmission::where('form_id', $form->id)
            ->where('id', $submissionId)
            ->firstOrFail();

        // Validate the security token
        $expectedToken = hash('sha256', $submission->id . $submission->created_at . config('app.key'));
        if (!$request->token || !hash_equals($expectedToken, $request->token)) {
            abort(403, 'Invalid access token');
        }

        return view('forms.public.thank-you', compact('form', 'submission'));
    }

    /**
     * Check if email exists (for AJAX)
     * Note: Always returns false to prevent automatic customer detection
     * Staff will manually link customers during review process
     */
    public function checkEmail(Request $request)
    {
        // Always return false - no automatic customer detection
        return response()->json(['exists' => false]);
    }

    /**
     * Send notification emails
     */
    protected function sendNotifications(Form $form, FormSubmission $submission)
    {
        // Check if form has email notifications enabled
        if (!$form->hasEmailNotificationsEnabled()) {
            return;
        }

        $notificationEmails = $form->getNotificationEmails();

        if (empty($notificationEmails)) {
            return;
        }

        // Determine which template to use based on form settings
        $templateName = $form->include_full_data_in_email
            ? 'form_submission_notification_full'
            : 'form_submission_notification';

        // Prepare template data
        $templateData = [
            'form' => $form,
            'submission' => $submission,
            'include_full_data' => $form->include_full_data_in_email,
        ];

        // Check if we have the custom template
        $emailTemplateService = app(\App\Services\EmailTemplateService::class);

        if ($emailTemplateService->templateExists($templateName)) {
            // Send using the templated email system
            foreach ($notificationEmails as $email) {
                try {
                    \Mail::to($email)->send(new \App\Mail\TemplatedMail($templateName, $templateData));

                    logger()->info("Form submission notification sent to: {$email}", [
                        'form_id' => $form->id,
                        'submission_id' => $submission->id,
                        'template' => $templateName,
                    ]);
                } catch (\Exception $e) {
                    logger()->error("Failed to send form submission notification to: {$email}", [
                        'form_id' => $form->id,
                        'submission_id' => $submission->id,
                        'template' => $templateName,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        } else {
            // Fallback: log that template is missing
            logger()->warning("Form submission email template not found: {$templateName}", [
                'form_id' => $form->id,
                'submission_id' => $submission->id,
            ]);
        }
    }

    /**
     * Send user group notifications based on form configuration
     */
    protected function sendUserGroupNotifications(Form $form, FormSubmission $submission)
    {
        try {
            $notificationService = app(\App\Services\NotificationService::class);

            // Prepare template data for notification messages
            $templateData = [
                'form' => $form,
                'submission' => $submission,
            ];

            // Default notification content
            $defaultTitle = 'New Form Submission';
            $defaultMessage = "A new submission for '{$form->name}' has been received from {$submission->submitter_name}.";
            $defaultLinkUrl = route('form-submissions.show', $submission->id);
            $defaultLinkText = 'View Submission';

            // Send notifications based on form's configuration
            $sentNotifications = $notificationService->sendConfiguredNotifications(
                $form,
                \App\Models\NotificationConfig::EVENT_FORM_SUBMITTED,
                $templateData,
                $defaultTitle,
                $defaultMessage,
                $defaultLinkUrl,
                $defaultLinkText
            );

            if ($sentNotifications->isNotEmpty()) {
                logger()->info("Form submission user group notifications sent", [
                    'form_id' => $form->id,
                    'submission_id' => $submission->id,
                    'notification_count' => $sentNotifications->count(),
                ]);
            }

        } catch (\Exception $e) {
            logger()->error("Failed to send form submission user group notifications", [
                'form_id' => $form->id,
                'submission_id' => $submission->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Verify reCAPTCHA token
     */
    protected function verifyRecaptcha(string $token, string $remoteIp): array
    {
        $secretKey = config('services.recaptcha.secret_key');
        
        $response = \Http::asForm()->post('https://www.google.com/recaptcha/api/siteverify', [
            'secret' => $secretKey,
            'response' => $token,
            'remoteip' => $remoteIp,
        ]);

        $result = $response->json();

        // Log reCAPTCHA response for debugging
        logger()->info('reCAPTCHA verification', [
            'success' => $result['success'] ?? false,
            'score' => $result['score'] ?? 0,
            'action' => $result['action'] ?? null,
            'challenge_ts' => $result['challenge_ts'] ?? null,
            'hostname' => $result['hostname'] ?? null,
            'error_codes' => $result['error-codes'] ?? [],
        ]);

        return $result;
    }
}
