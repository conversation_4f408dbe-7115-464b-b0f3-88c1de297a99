<?php

namespace App\Http\Controllers;

use App\Models\MicrosoftCalendarIntegration;
use App\Services\MicrosoftGraphService;
use App\Services\PickupCalendarSyncService;
use App\Jobs\SyncPickupEventsToMicrosoft;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;

class AdminMicrosoftIntegrationController extends Controller
{
    private MicrosoftOAuthController $oauthController;

    public function __construct(MicrosoftOAuthController $oauthController)
    {
        $this->oauthController = $oauthController;
    }

    /**
     * Show the Microsoft 365 integration management page.
     */
    public function index(): View
    {
        $sharedIntegration = MicrosoftCalendarIntegration::where('is_shared_calendar', true)->first();
        $syncService = new PickupCalendarSyncService($this->oauthController);
        $syncStatus = $syncService->getSyncStatus();

        return view('admin.microsoft-integration.index', compact('sharedIntegration', 'syncStatus'));
    }

    /**
     * Show the setup form for shared calendar integration.
     */
    public function create(): View
    {
        return view('admin.microsoft-integration.create');
    }

    /**
     * Redirect to Microsoft OAuth for shared calendar setup.
     */
    public function redirectToMicrosoft(): RedirectResponse
    {
        // Store session flag to indicate this is for shared calendar setup
        session(['microsoft_setup_type' => 'shared_calendar']);
        
        return redirect()->route('microsoft.connect');
    }

    /**
     * Show calendar selection page after OAuth.
     */
    public function showCalendarSelection(): View
    {
        // Check if we have OAuth data in session
        if (!session('microsoft_oauth_token') || !session('microsoft_user_info')) {
            return redirect()->route('admin.microsoft-integration.index')
                ->with('error', 'OAuth session expired. Please try connecting again.');
        }

        try {
            // Create a temporary integration to fetch calendars
            $tempIntegration = new MicrosoftCalendarIntegration();
            $tempIntegration->access_token = encrypt(session('microsoft_oauth_token'));
            
            $graphService = new MicrosoftGraphService($tempIntegration, $this->oauthController);
            $calendars = $graphService->getCalendars();
            $userInfo = session('microsoft_user_info');

            return view('admin.microsoft-integration.calendar-selection', compact('calendars', 'userInfo'));
            
        } catch (\Exception $e) {
            Log::error('Failed to fetch calendars for selection', [
                'error' => $e->getMessage()
            ]);
            
            return redirect()->route('admin.microsoft-integration.index')
                ->with('error', 'Failed to fetch calendars: ' . $e->getMessage());
        }
    }

    /**
     * Complete setup with selected calendar.
     */
    public function completeSetup(Request $request): RedirectResponse
    {
        $request->validate([
            'calendar_id' => 'required|string',
            'calendar_name' => 'required|string|max:255',
        ]);

        // Check if we have OAuth data in session
        if (!session('microsoft_oauth_token') || !session('microsoft_user_info')) {
            return redirect()->route('admin.microsoft-integration.index')
                ->with('error', 'OAuth session expired. Please try connecting again.');
        }

        try {
            $userInfo = session('microsoft_user_info');
            
            // Clear existing shared calendar integration if any
            MicrosoftCalendarIntegration::where('is_shared_calendar', true)->delete();
            
            // Create new shared calendar integration with selected calendar
            $integration = MicrosoftCalendarIntegration::create([
                'user_id' => auth()->id(),
                'access_token' => encrypt(session('microsoft_oauth_token')),
                'refresh_token' => encrypt(session('microsoft_oauth_refresh_token')),
                'token_expires_at' => session('microsoft_oauth_expires') ? 
                    now()->addSeconds(session('microsoft_oauth_expires') - time()) : null,
                'microsoft_user_id' => $userInfo['id'],
                'microsoft_user_email' => $userInfo['mail'] ?? $userInfo['userPrincipalName'],
                'outlook_calendar_id' => $request->input('calendar_id'),
                'sync_enabled' => true,
                'sync_status' => 'pending',
                'error_message' => null,
                'is_shared_calendar' => true,
                'calendar_name' => $request->input('calendar_name'),
            ]);
            
            // Clear OAuth session data
            session()->forget([
                'microsoft_oauth_token',
                'microsoft_oauth_refresh_token', 
                'microsoft_oauth_expires',
                'microsoft_user_info',
                'microsoft_setup_type'
            ]);
            
            Log::info('Shared Microsoft calendar integration created with selected calendar', [
                'user_id' => auth()->id(),
                'microsoft_user_email' => $integration->microsoft_user_email,
                'calendar_name' => $integration->calendar_name,
                'calendar_id' => $integration->outlook_calendar_id
            ]);

            return redirect()->route('admin.microsoft-integration.index')
                ->with('success', 'Shared Microsoft 365 calendar integration set up successfully!');
                
        } catch (\Exception $e) {
            Log::error('Failed to complete calendar setup', [
                'error' => $e->getMessage()
            ]);
            
            return redirect()->route('admin.microsoft-integration.index')
                ->with('error', 'Failed to complete setup: ' . $e->getMessage());
        }
    }

    /**
     * Show calendar change page for existing integration.
     */
    public function changeCalendar(): View
    {
        $sharedIntegration = MicrosoftCalendarIntegration::where('is_shared_calendar', true)->first();
        
        if (!$sharedIntegration) {
            return redirect()->route('admin.microsoft-integration.index')
                ->with('error', 'No shared calendar integration found.');
        }

        try {
            $graphService = new MicrosoftGraphService($sharedIntegration, $this->oauthController);
            $calendars = $graphService->getCalendars();
            
            return view('admin.microsoft-integration.change-calendar', compact('calendars', 'sharedIntegration'));
            
        } catch (\Exception $e) {
            Log::error('Failed to fetch calendars for change', [
                'integration_id' => $sharedIntegration->id,
                'error' => $e->getMessage()
            ]);
            
            return redirect()->route('admin.microsoft-integration.index')
                ->with('error', 'Failed to fetch calendars: ' . $e->getMessage());
        }
    }

    /**
     * Update the selected calendar for existing integration.
     */
    public function updateCalendar(Request $request): RedirectResponse
    {
        $request->validate([
            'calendar_id' => 'required|string',
            'calendar_name' => 'required|string|max:255',
        ]);

        $sharedIntegration = MicrosoftCalendarIntegration::where('is_shared_calendar', true)->first();
        
        if (!$sharedIntegration) {
            return redirect()->route('admin.microsoft-integration.index')
                ->with('error', 'No shared calendar integration found.');
        }

        try {
            $sharedIntegration->update([
                'outlook_calendar_id' => $request->input('calendar_id'),
                'calendar_name' => $request->input('calendar_name'),
            ]);
            
            Log::info('Shared calendar updated', [
                'integration_id' => $sharedIntegration->id,
                'old_calendar' => $sharedIntegration->calendar_name,
                'new_calendar' => $request->input('calendar_name'),
                'calendar_id' => $request->input('calendar_id')
            ]);

            return redirect()->route('admin.microsoft-integration.index')
                ->with('success', 'Calendar updated successfully!');
                
        } catch (\Exception $e) {
            Log::error('Failed to update calendar', [
                'integration_id' => $sharedIntegration->id,
                'error' => $e->getMessage()
            ]);
            
            return redirect()->route('admin.microsoft-integration.index')
                ->with('error', 'Failed to update calendar: ' . $e->getMessage());
        }
    }

    /**
     * Update shared calendar settings.
     */
    public function update(Request $request): RedirectResponse
    {
        $request->validate([
            'calendar_name' => 'required|string|max:255',
            'sync_enabled' => 'boolean',
            'sync_pickup_events' => 'boolean',
            'sync_completed_events' => 'boolean',
            'sync_cancelled_events' => 'boolean',
            'include_customer_details' => 'boolean',
            'include_item_details' => 'boolean',
            'event_category' => 'nullable|string|max:255',
        ]);

        $sharedIntegration = MicrosoftCalendarIntegration::where('is_shared_calendar', true)->first();

        if (!$sharedIntegration) {
            return back()->with('error', 'No shared calendar integration found. Please set up the integration first.');
        }

        $syncSettings = [
            'sync_pickup_events' => $request->boolean('sync_pickup_events', true),
            'sync_completed_events' => $request->boolean('sync_completed_events', false),
            'sync_cancelled_events' => $request->boolean('sync_cancelled_events', false),
            'include_customer_details' => $request->boolean('include_customer_details', true),
            'include_item_details' => $request->boolean('include_item_details', true),
            'event_category' => $request->input('event_category', 'ETRFlow Pickup'),
        ];

        $sharedIntegration->update([
            'calendar_name' => $request->input('calendar_name'),
            'sync_enabled' => $request->boolean('sync_enabled', true),
            'sync_settings' => $syncSettings,
        ]);

        Log::info('Shared calendar integration settings updated', [
            'integration_id' => $sharedIntegration->id,
            'calendar_name' => $request->input('calendar_name'),
            'sync_enabled' => $request->boolean('sync_enabled'),
        ]);

        return back()->with('success', 'Shared calendar settings updated successfully.');
    }

    /**
     * Trigger a manual sync of all pickup events.
     */
    public function manualSync(): RedirectResponse
    {
        $sharedIntegration = MicrosoftCalendarIntegration::getSharedCalendarIntegration();

        if (!$sharedIntegration) {
            return back()->with('error', 'No active shared calendar integration found.');
        }

        if (!$sharedIntegration->isActive()) {
            return back()->with('error', 'Shared calendar integration is not active. Please check the connection.');
        }

        try {
            // Update status and queue sync job
            $sharedIntegration->update(['sync_status' => 'syncing']);
            SyncPickupEventsToMicrosoft::forIntegration($sharedIntegration)->dispatch();

            Log::info('Manual sync initiated for shared calendar', [
                'integration_id' => $sharedIntegration->id,
                'user_id' => auth()->id()
            ]);

            return back()->with('success', 'Manual sync has been queued. Check the sync status in a few minutes.');
        } catch (\Exception $e) {
            Log::error('Failed to initiate manual sync for shared calendar', [
                'integration_id' => $sharedIntegration->id,
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Failed to initiate manual sync: ' . $e->getMessage());
        }
    }

    /**
     * Disconnect the shared calendar integration.
     */
    public function disconnect(): RedirectResponse
    {
        $sharedIntegration = MicrosoftCalendarIntegration::where('is_shared_calendar', true)->first();

        if (!$sharedIntegration) {
            return back()->with('error', 'No shared calendar integration found.');
        }

        try {
            $sharedIntegration->delete();

            Log::info('Shared calendar integration disconnected', [
                'integration_id' => $sharedIntegration->id,
                'user_id' => auth()->id()
            ]);

            return redirect()->route('admin.microsoft-integration.index')
                ->with('success', 'Shared calendar integration has been disconnected.');
        } catch (\Exception $e) {
            Log::error('Failed to disconnect shared calendar integration', [
                'integration_id' => $sharedIntegration->id,
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Failed to disconnect integration: ' . $e->getMessage());
        }
    }

    /**
     * Test the shared calendar connection.
     */
    public function testConnection(): RedirectResponse
    {
        $sharedIntegration = MicrosoftCalendarIntegration::getSharedCalendarIntegration();

        if (!$sharedIntegration) {
            return back()->with('error', 'No active shared calendar integration found.');
        }

        try {
            $graphService = new MicrosoftGraphService($sharedIntegration, $this->oauthController);
            $testResult = $graphService->testConnection();

            if ($testResult['success']) {
                return back()->with('success', 'Connection test successful! Connected to ' . $testResult['user']['email']);
            } else {
                return back()->with('error', 'Connection test failed: ' . $testResult['error']);
            }
        } catch (\Exception $e) {
            Log::error('Shared calendar connection test failed', [
                'integration_id' => $sharedIntegration->id,
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Connection test failed: ' . $e->getMessage());
        }
    }
}