<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class EmergencyContactController extends Controller
{
    /**
     * Show the emergency contact form.
     *
     * @return \Illuminate\View\View
     */
    public function edit()
    {
        return view('profile.emergency-contact', [
            'user' => Auth::user(),
        ]);
    }

    /**
     * Update the user's emergency contact information via AJAX.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'emergency_contact_name' => ['nullable', 'string', 'max:255'],
            'emergency_contact_relationship' => ['nullable', 'string', 'max:255'],
            'emergency_contact_phone' => ['nullable', 'string', 'max:255'],
            'additional_emergency_contacts' => ['nullable', 'string'],
            'allergies' => ['nullable', 'string'],
            'medications' => ['nullable', 'string'],
            'medical_conditions' => ['nullable', 'string'],
            'blood_type' => ['nullable', 'string', 'max:10'],
            'additional_health_info' => ['nullable', 'string'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user->update($request->only([
            'emergency_contact_name',
            'emergency_contact_relationship',
            'emergency_contact_phone',
            'additional_emergency_contacts',
            'allergies',
            'medications',
            'medical_conditions',
            'blood_type',
            'additional_health_info',
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Emergency contact information updated successfully.'
        ]);
    }
}
