<?php

namespace App\Http\Controllers;

use App\Models\HTMLField;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class HTMLFieldController extends Controller
{
    /**
     * Update HTML field content
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function update(Request $request): JsonResponse
    {
        $request->validate([
            'key' => 'required|string|max:255',
            'content' => 'nullable|string',
            'description' => 'nullable|string|max:255',
        ]);

        try {
            HTMLField::setContent(
                $request->input('key'),
                $request->input('content'),
                $request->input('description')
            );

            return response()->json([
                'success' => true,
                'message' => 'HTML field updated successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update HTML field: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get HTML field content
     *
     * @param string $key
     * @return JsonResponse
     */
    public function show(string $key): JsonResponse
    {
        try {
            $content = HTMLField::getContent($key);

            return response()->json([
                'success' => true,
                'content' => $content
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve HTML field: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all HTML fields
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            $fields = HTMLField::getAllFields();

            return response()->json([
                'success' => true,
                'fields' => $fields
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve HTML fields: ' . $e->getMessage()
            ], 500);
        }
    }
}
