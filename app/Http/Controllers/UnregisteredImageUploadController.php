<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Inventory;

class UnregisteredImageUploadController extends Controller
{
    public function index()
    {
        return view('imgup');
    }

    public function upload(Request $request)
    {
        $request->validate([
            'identifier' => 'required|string',
            'passcode' => 'required|string',
            'photo' => 'required|image|mimes:jpeg,png,jpg,webp|max:10240',
        ]);

        // Validate passcode
        if ($request->passcode !== config('app.img_upload_passcode')) {
            return back()->withErrors(['passcode' => 'Invalid passcode.']);
        }

        // Find inventory item by asset tag or numeric ID
        $inventory = Inventory::where('asset_tag', $request->identifier)
            ->orWhere('id', $request->identifier)
            ->first();
        if (!$inventory) {
            return back()->withErrors(['identifier' => 'Asset tag or ID not found.']);
        }

        // Process the file upload using the ImageController with context
        $photo = $request->file('photo');
        $newRequest = new Request();
        $newRequest->setMethod('POST');
        $newRequest->files->set('file', $photo);
        
        // Add context information for the new Image system
        $newRequest->merge([
            'context_type' => 'inventory',
            'context_id' => $inventory->id,
            'title' => 'Uploaded via public form',
            'is_public' => false,
        ]);

        $imageController = app(\App\Http\Controllers\ImageController::class);
        $response = $imageController->store($newRequest);

        if ($response->getStatusCode() === 200) {
            $imageData = $response->getData()->image;

            // The image is already associated with the inventory via context,
            // so we don't need to create a separate relationship
            
            return back()->with('success', "Successfully uploaded to asset tag: {$inventory->asset_tag} ({$inventory->name})");
        } else {
            return back()->withErrors(['photo' => 'Failed to process the image.']);
        }
    }
}
