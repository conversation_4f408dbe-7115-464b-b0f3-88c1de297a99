<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SystemStatsController extends Controller
{
    public function index()
    {
        $stats = $this->getSystemStats();
        
        return view('admin.tools.system-stats.index', compact('stats'));
    }

    private function getSystemStats()
    {
        $stats = [];
        
        // Disk usage for main directories
        $stats['disk_usage'] = [
            'storage' => $this->getDiskUsage(storage_path()),
            'public' => $this->getDiskUsage(public_path()),
            'app' => $this->getDiskUsage(app_path()),
            'database' => $this->getDiskUsage(database_path()),
            'resources' => $this->getDiskUsage(resource_path()),
            'vendor' => $this->getDiskUsage(base_path('vendor')),
            'node_modules' => $this->getDiskUsage(base_path('node_modules')),
            'total_project' => $this->getDiskUsage(base_path()),
        ];
        
        // System info
        $stats['system'] = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'N/A',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
        ];
        
        // Database stats
        try {
            $stats['database'] = [
                'connection' => config('database.default'),
                'database_name' => config('database.connections.' . config('database.default') . '.database'),
                'tables_count' => \DB::select("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ?", [config('database.connections.' . config('database.default') . '.database')])[0]->count ?? 0,
            ];
        } catch (\Exception $e) {
            $stats['database'] = [
                'connection' => 'Error connecting',
                'database_name' => 'N/A',
                'tables_count' => 0,
            ];
        }
        
        return $stats;
    }
    
    private function getDiskUsage($path)
    {
        if (!is_dir($path)) {
            return [
                'size_bytes' => 0,
                'size_human' => '0 B',
                'exists' => false
            ];
        }
        
        $size = $this->getDirectorySize($path);
        
        return [
            'size_bytes' => $size,
            'size_human' => $this->formatBytes($size),
            'exists' => true
        ];
    }
    
    private function getDirectorySize($directory)
    {
        $size = 0;
        
        try {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $size += $file->getSize();
                }
            }
        } catch (\Exception $e) {
            // If we can't read the directory, return 0
            return 0;
        }
        
        return $size;
    }
    
    private function formatBytes($size, $precision = 2)
    {
        if ($size === 0) {
            return '0 B';
        }
        
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, $precision) . ' ' . $units[$i];
    }
}