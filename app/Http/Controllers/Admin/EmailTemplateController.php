<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmailTemplate;
use App\Services\EmailTemplateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EmailTemplateController extends Controller
{

    /**
     * Display a listing of email templates
     */
    public function index()
    {
        $templates = EmailTemplate::orderBy('display_name')->get();
        
        // Get template definitions to check if we need to create any
        $definitions = EmailTemplate::templateDefinitions();
        $existingNames = $templates->pluck('name')->toArray();
        $missingTemplates = array_diff(array_keys($definitions), $existingNames);

        return view('admin.email-templates.index', compact('templates', 'missingTemplates', 'definitions'));
    }

    /**
     * Show the form for creating a new email template
     */
    public function create(Request $request)
    {
        $templateName = $request->get('template');
        $definitions = EmailTemplate::templateDefinitions();
        
        if (!$templateName || !isset($definitions[$templateName])) {
            return redirect()->route('admin.email-templates.index')
                ->with('error', 'Invalid template type');
        }

        $definition = $definitions[$templateName];

        return view('admin.email-templates.create', compact('templateName', 'definition'));
    }

    /**
     * Store a newly created email template
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|unique:email_templates,name',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'subject' => 'required|string|max:255',
            'from_name' => 'nullable|string|max:255',
            'from_email' => 'nullable|email|max:255',
            'body_html' => 'required|string',
            'is_active' => 'boolean',
        ]);

        $validated['is_active'] = $request->has('is_active');

        // Get available variables from definition
        $definitions = EmailTemplate::templateDefinitions();
        if (isset($definitions[$validated['name']])) {
            $validated['available_variables'] = $definitions[$validated['name']]['variables'];
        }

        EmailTemplate::create($validated);

        Log::info('Email template created', ['name' => $validated['name'], 'user' => auth()->user()->email]);

        return redirect()->route('admin.email-templates.index')
            ->with('success', 'Email template created successfully');
    }

    /**
     * Show the form for editing an email template
     */
    public function edit(EmailTemplate $emailTemplate)
    {
        $definition = $emailTemplate->getDefinition();
        return view('admin.email-templates.edit', compact('emailTemplate', 'definition'));
    }

    /**
     * Update the specified email template
     */
    public function update(Request $request, EmailTemplate $emailTemplate)
    {
        $validated = $request->validate([
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'subject' => 'required|string|max:255',
            'from_name' => 'nullable|string|max:255',
            'from_email' => 'nullable|email|max:255',
            'body_html' => 'required|string',
            'is_active' => 'boolean',
        ]);

        $validated['is_active'] = $request->has('is_active');

        $emailTemplate->update($validated);

        Log::info('Email template updated', ['name' => $emailTemplate->name, 'user' => auth()->user()->email]);

        return redirect()->route('admin.email-templates.index')
            ->with('success', 'Email template updated successfully');
    }

    /**
     * Preview an email template
     */
    public function preview(EmailTemplate $emailTemplate)
    {
        // Generate sample data for preview
        $sampleData = $this->getSampleData($emailTemplate->name);
        
        // Use EmailTemplateService to render so we get common variables like contact_email
        $emailService = new EmailTemplateService();
        $rendered = $emailService->renderTemplate($emailTemplate->name, $sampleData);
        
        // Add common variables to sample data for display in the preview
        $contactEmail = \App\Models\GlobalConfig::getValue('site_email') ?: config('mail.from.address');
        $sampleData['contact_email'] = $contactEmail;
        $sampleData['app_name'] = config('app.name');
        $sampleData['current_date'] = now()->format('F j, Y');
        $sampleData['current_datetime'] = now()->format('F j, Y g:i A');

        return view('admin.email-templates.preview', compact('emailTemplate', 'rendered', 'sampleData'));
    }

    /**
     * Toggle template active status
     */
    public function toggleStatus(EmailTemplate $emailTemplate)
    {
        $emailTemplate->is_active = !$emailTemplate->is_active;
        $emailTemplate->save();

        Log::info('Email template status toggled', [
            'name' => $emailTemplate->name, 
            'active' => $emailTemplate->is_active,
            'user' => auth()->user()->email
        ]);

        return redirect()->route('admin.email-templates.index')
            ->with('success', 'Template status updated');
    }

    /**
     * Delete an email template
     */
    public function destroy(EmailTemplate $emailTemplate)
    {
        $name = $emailTemplate->name;
        $emailTemplate->delete();

        Log::info('Email template deleted', ['name' => $name, 'user' => auth()->user()->email]);

        return redirect()->route('admin.email-templates.index')
            ->with('success', 'Email template deleted successfully');
    }

    /**
     * Get sample data for template preview
     */
    private function getSampleData($templateName)
    {
        switch ($templateName) {
            case 'daily_timeclock_report':
                return [
                    'report_date' => now()->subDay()->format('F j, Y'),
                    'total_hours_worked' => '42.5',
                    'total_break_hours' => '5.25',
                    'employee_count' => '5',
                    'app_name' => config('app.name'),
                    'current_date' => now()->format('F j, Y'),
                ];
                
            case 'pickup_request_notification':
                return [
                    'pickup_id' => '12345',
                    'status' => 'Pending',
                    'contact_name' => 'John Doe',
                    'business_name' => 'Acme Corporation',
                    'email' => '<EMAIL>',
                    'phone' => '(*************',
                    'pickup_date' => now()->addDays(3)->format('F j, Y'),
                    'pickup_address' => '123 Main Street, Anytown, ST 12345',
                    'load_size' => 'Medium Load',
                    'item_types' => 'Computers, Monitors, Printers',
                    'driver_instructions' => 'Please use side entrance',
                    'view_url' => route('pickup-requests.show', 12345),
                    'app_name' => config('app.name'),
                    'notification_type' => 'new',
                ];
                
            default:
                return [
                    'app_name' => config('app.name'),
                    'current_date' => now()->format('F j, Y'),
                ];
        }
    }
}