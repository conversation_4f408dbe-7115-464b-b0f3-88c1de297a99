<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Customer;
use App\Models\CustomerDiscount;
use App\Models\GlobalConfig;
use Carbon\Carbon;

class CustomerImportController extends Controller
{
    /**
     * Show the upload form
     */
    public function index()
    {
        return view('admin.tools.customer-import.index');
    }

    /**
     * Process the uploaded CSV file
     */
    public function upload(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:10240',
        ]);

        // Store the uploaded file
        $path = $request->file('csv_file')->store('temp');

        // Read the CSV file
        $csvData = $this->parseCSV(Storage::path($path));

        if (empty($csvData) || count($csvData) <= 1) { // Check if there's only headers or no data
            Storage::delete($path);
            return redirect()->route('admin.tools.customer-import.index')
                ->with('error', 'The uploaded CSV file is empty or contains only headers.');
        }

        // Store the file path in the session for the next step
        session(['csv_import_path' => $path]);

        // Pass the CSV data to the setup view
        return view('admin.tools.customer-import.setup', [
            'headers' => $csvData[0],
            'data' => array_slice($csvData, 1), // Skip the header row
            'rowCount' => count($csvData) - 1,
        ]);
    }

    /**
     * Process the import based on the mapping
     */
    public function process(Request $request)
    {
        // Validate the request
        $request->validate([
            'mapping' => 'required|array',
            'selected_rows' => 'required|array',
            'customer_type' => 'required|in:Residential Customer,Online Customer,Bulk Buyer,Business',
        ]);

        // Get the CSV file path from the session
        $path = session('csv_import_path');
        if (!$path || !Storage::exists($path)) {
            return redirect()->route('admin.tools.customer-import.index')
                ->with('error', 'CSV file not found. Please upload again.');
        }

        // Parse the CSV file
        $csvData = $this->parseCSV(Storage::path($path));
        $headers = $csvData[0];
        $data = array_slice($csvData, 1); // Skip the header row

        // Get the mapping, selected rows, and customer type
        $mapping = $request->input('mapping');
        $selectedRows = $request->input('selected_rows');
        $customerType = $request->input('customer_type');

        // Get the active customer contract discount ID
        $activeContractDiscountId = GlobalConfig::getValue('active_customer_contract_id');

        // Process the import
        $results = [
            'imported' => 0,
            'skipped' => 0,
            'errors' => [],
            'contracts_created' => 0,
            'customer_type' => $customerType,
            'duplicate_emails' => 0,
            'no_name' => 0,
            'derived_names' => 0,
            'invalid_start_dates' => 0,
        ];

        DB::beginTransaction();
        try {
            foreach ($selectedRows as $rowIndex) {
                $rowData = $data[$rowIndex];

                // Extract customer data based on mapping
                $customerData = [];
                foreach ($mapping as $field => $columnIndex) {
                    if ($columnIndex !== '' && isset($rowData[$columnIndex])) {
                        $customerData[$field] = $rowData[$columnIndex];
                    }
                }

                // Handle missing name by trying alternative fields
                $name = $customerData['name'] ?? null;
                $contact = $customerData['contact'] ?? null;
                $email = $customerData['email'] ?? null;

                // If name is empty, try to use contact name
                if (empty($name) && !empty($contact)) {
                    $name = $contact;
                    $results['derived_names']++;
                }

                // If name is still empty and we have an email, use part before @
                if (empty($name) && !empty($email) && strpos($email, '@') !== false) {
                    $name = explode('@', $email)[0];
                    // Capitalize the first letter and replace dots/underscores with spaces
                    $name = ucfirst(str_replace(['.', '_'], ' ', $name));
                    $results['derived_names']++;
                }

                // Skip if we still don't have a name
                if (empty($name)) {
                    $results['skipped']++;
                    $results['no_name']++;
                    continue;
                }

                // Check if we need to handle a duplicate email
                $email = $customerData['email'] ?? null;
                $existingCustomer = null;

                if ($email) {
                    $existingCustomer = Customer::where('email', $email)->first();
                }

                if ($existingCustomer) {
                    // Add to skipped count and continue to next row
                    $results['skipped']++;
                    $results['duplicate_emails']++;
                    continue;
                }

                // Create the customer
                $customer = new Customer();
                $customer->name = $name; // Use the derived name
                $customer->email = $email;
                $customer->contact = $contact;
                $customer->phone = $customerData['phone'] ?? null;
                $customer->type = $customerType; // Use the selected customer type
                $customer->save();

                $results['imported']++;

                // Create customer discount only if start date is provided and active contract discount is set
                if ($activeContractDiscountId && isset($customerData['contract_start_date'])) {

                    $customerDiscount = new CustomerDiscount();
                    $customerDiscount->customer_id = $customer->id;
                    $customerDiscount->discount_id = $activeContractDiscountId;

                    // Parse start date - required for contract creation
                    $startDate = $this->parseDate($customerData['contract_start_date']);

                    // Only proceed if we have a valid start date
                    if ($startDate) {
                        $customerDiscount->start_date = $startDate;

                        // Parse end date if provided
                        if (!empty($customerData['contract_end_date'])) {
                            $endDate = $this->parseDate($customerData['contract_end_date']);
                            if ($endDate) {
                                $customerDiscount->end_date = $endDate;
                            }
                        }

                        // Save the customer discount and increment counter
                        $customerDiscount->save();
                        $results['contracts_created']++;
                    } else {
                        // If start date parsing failed, add to a counter
                        $results['invalid_start_dates'] = ($results['invalid_start_dates'] ?? 0) + 1;
                    }
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $results['errors'][] = 'Error during import: ' . $e->getMessage();
        }

        // Clean up the temporary file
        Storage::delete($path);
        session()->forget('csv_import_path');

        // Show the results
        return view('admin.tools.customer-import.results', [
            'results' => $results
        ]);
    }

    /**
     * Parse a CSV file into an array
     */
    private function parseCSV($filePath)
    {
        $data = [];

        if (($handle = fopen($filePath, "r")) !== false) {
            while (($row = fgetcsv($handle, 1000, ",")) !== false) {
                $data[] = $row;
            }
            fclose($handle);
        }

        return $data;
    }

    /**
     * Parse a date string in various formats
     *
     * @param string $dateString The date string to parse
     * @return Carbon|null The parsed date or null if parsing failed
     */
    private function parseDate($dateString)
    {
        // Trim the date string
        $dateString = trim($dateString);

        // Return null if the date string is empty
        if (empty($dateString)) {
            return null;
        }

        // Try to parse the date using various formats
        try {
            // First try direct parsing (works for YYYY-MM-DD, MM/DD/YYYY, etc.)
            return Carbon::parse($dateString);
        } catch (\Exception $e) {
            // If direct parsing fails, try specific formats
            $formats = [
                'm/d/Y', // 1/7/2025
                'd/m/Y', // 7/1/2025
                'm-d-Y', // 1-7-2025
                'd-m-Y', // 7-1-2025
                'm.d.Y', // 1.7.2025
                'd.m.Y', // 7.1.2025
                'Y/m/d', // 2025/1/7
                'Y-m-d', // 2025-1-7
                'Y.m.d', // 2025.1.7
                'M d Y', // Jan 7 2025
                'd M Y', // 7 Jan 2025
                'F d Y', // January 7 2025
                'd F Y', // 7 January 2025
            ];

            foreach ($formats as $format) {
                try {
                    return Carbon::createFromFormat($format, $dateString);
                } catch (\Exception $e) {
                    // Continue to the next format if this one fails
                    continue;
                }
            }

            // If all parsing attempts fail, return null
            return null;
        }
    }
}
