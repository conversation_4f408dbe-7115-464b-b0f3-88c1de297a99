<?php

namespace App\Http\Controllers;

use App\Models\PickupRequest;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PickupRequestCustomerController extends Controller
{
    /**
     * Step 1: Customer Identification
     */
    public function step1(PickupRequest $pickupRequest)
    {
        // If customer is already linked, redirect to show page
        if ($pickupRequest->customer) {
            return redirect()->route('pickup-requests.show', $pickupRequest)
                ->with('info', 'Customer is already linked to this pickup request.');
        }

        // Get top 3 customer suggestions based on pickup request data
        $customerSuggestions = $this->getCustomerSuggestions($pickupRequest);

        return view('pickup-requests.step1', compact('pickupRequest', 'customerSuggestions'));
    }

    /**
     * Link customer to pickup request
     */
    public function linkCustomer(Request $request, PickupRequest $pickupRequest)
    {
        \Log::info('LinkCustomer called', [
            'pickup_request_id' => $pickupRequest->id,
            'request_data' => $request->all(),
            'user_id' => auth()->id(),
            'expects_json' => $request->expectsJson()
        ]);

        try {
            $request->validate([
                'customer_id' => 'required|exists:customers,id'
            ]);

            \Log::info('Validation passed', ['customer_id' => $request->customer_id]);

            // Check if customer exists
            $customer = Customer::find($request->customer_id);
            if (!$customer) {
                \Log::error('Customer not found', ['customer_id' => $request->customer_id]);
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found.'
                ], 404);
            }

            \Log::info('Customer found', ['customer' => $customer->toArray()]);

            // Update the pickup request with customer and change status to pending
            $pickupRequest->update([
                'customer_id' => $request->customer_id,
                'status' => 'pending'
            ]);

            // Log the customer linking
            $pickupRequest->logCustomerLinked($customer, auth()->user()->name);

            \Log::info('Pickup request updated', [
                'pickup_request_id' => $pickupRequest->id,
                'customer_id' => $request->customer_id,
                'status' => 'pending'
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => "Pickup request linked to customer: {$customer->name}. Status updated to 'pending'.",
                    'customer' => [
                        'id' => $customer->id,
                        'name' => $customer->name,
                        'email' => $customer->email,
                        'phone' => $customer->phone
                    ],
                    'status' => 'pending'
                ]);
            }

            return redirect()->route('pickup-requests.step2', $pickupRequest)
                ->with('success', "Customer linked successfully! Status updated to 'pending'. Now let's schedule the pickup.");

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Validation failed', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed: ' . implode(', ', $e->validator->errors()->all())
                ], 422);
            }

            throw $e;
        } catch (\Exception $e) {
            \Log::error('LinkCustomer error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'pickup_request_id' => $pickupRequest->id,
                'request_data' => $request->all()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while linking the customer: ' . $e->getMessage()
                ], 500);
            }

            throw $e;
        }
    }

    /**
     * Unlink customer from pickup request
     */
    public function unlinkCustomer(PickupRequest $pickupRequest)
    {
        if (!$pickupRequest->customer_id) {
            return response()->json([
                'success' => false,
                'message' => 'No customer is currently linked to this request.'
            ], 400);
        }

        // Get customer name before unlinking
        $previousCustomerName = $pickupRequest->customer?->name;

        // Reset customer and status back to incoming
        $pickupRequest->update([
            'customer_id' => null,
            'status' => 'incoming'
        ]);

        // Log the customer unlinking
        $pickupRequest->logCustomerUnlinked($previousCustomerName, auth()->user()->name);

        return response()->json([
            'success' => true,
            'message' => 'Customer unlinked successfully. Request status reset to incoming.',
            'status' => 'incoming'
        ]);
    }

    /**
     * Get customer suggestions based on pickup request data
     */
    private function getCustomerSuggestions(PickupRequest $pickupRequest)
    {
        $suggestions = collect();

        // Search by business name if provided
        if ($pickupRequest->business_name) {
            $businessMatches = Customer::where(function($query) use ($pickupRequest) {
                $query->where('name', 'LIKE', "%{$pickupRequest->business_name}%")
                      ->orWhere('contact', 'LIKE', "%{$pickupRequest->business_name}%");
            })
                ->distinct()
                ->limit(3)
                ->get();
            $suggestions = $suggestions->merge($businessMatches);
        }

        // Search by contact name
        if ($pickupRequest->contact_name && $suggestions->count() < 3) {
            $contactMatches = Customer::where(function($query) use ($pickupRequest) {
                $query->where('name', 'LIKE', "%{$pickupRequest->contact_name}%")
                      ->orWhere('contact', 'LIKE', "%{$pickupRequest->contact_name}%");
            })
                ->whereNotIn('id', $suggestions->pluck('id'))
                ->distinct()
                ->limit(3 - $suggestions->count())
                ->get();
            $suggestions = $suggestions->merge($contactMatches);
        }

        // Search by email if provided
        if ($pickupRequest->email && $suggestions->count() < 3) {
            $emailMatches = Customer::where('email', 'LIKE', "%{$pickupRequest->email}%")
                ->whereNotIn('id', $suggestions->pluck('id'))
                ->distinct()
                ->limit(3 - $suggestions->count())
                ->get();
            $suggestions = $suggestions->merge($emailMatches);
        }

        // Search by phone if provided
        if ($pickupRequest->phone && $suggestions->count() < 3) {
            $phoneMatches = Customer::where('phone', 'LIKE', "%{$pickupRequest->phone}%")
                ->whereNotIn('id', $suggestions->pluck('id'))
                ->distinct()
                ->limit(3 - $suggestions->count())
                ->get();
            $suggestions = $suggestions->merge($phoneMatches);
        }

        // Ensure no duplicate customers by ID and add license status
        $uniqueSuggestions = $suggestions->unique('id');
        foreach ($uniqueSuggestions as $customer) {
            $customer->hasLicenseOnFile = $customer->hasPhotoIdOnFile();
        }

        return $uniqueSuggestions->take(3);
    }

    /**
     * Search customers for AJAX requests
     */
    public function searchCustomers(Request $request, PickupRequest $pickupRequest)
    {
        $query = $request->input('query');
        
        if (empty($query)) {
            return response()->json([]);
        }

        $customers = Customer::where('name', 'LIKE', "%{$query}%")
            ->orWhere('contact', 'LIKE', "%{$query}%")
            ->orWhere('email', 'LIKE', "%{$query}%")
            ->orWhere('phone', 'LIKE', "%{$query}%")
            ->limit(10)
            ->get(['id', 'name', 'contact', 'email', 'phone', 'address', 'type']);

        // Add license status to each customer
        foreach ($customers as $customer) {
            $customer->hasLicenseOnFile = $customer->hasPhotoIdOnFile();
        }

        return response()->json($customers);
    }
}
