<?php

namespace App\Http\Controllers;

use App\Models\Calendar;
use App\Models\CalendarShare;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class CalendarController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Authentication is handled by the routes
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();

        // Get calendars owned by the user
        $ownedCalendars = $user->ownedCalendars()->with('owner')->get();

        // Get calendars shared with the user
        $sharedCalendars = $user->sharedCalendars()->with(['owner', 'shares' => function($query) use ($user) {
            $query->where('user_id', $user->id);
        }])->get();

        // Get public calendars
        $publicCalendars = Calendar::where('is_public', true)
            ->whereNotIn('id', $ownedCalendars->pluck('id'))
            ->whereNotIn('id', $sharedCalendars->pluck('id'))
            ->with('owner')
            ->get();

        return view('calendars.index', compact('ownedCalendars', 'sharedCalendars', 'publicCalendars'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $users = User::where('id', '!=', Auth::id())->get();
        return view('calendars.create', compact('users'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'color' => 'required|string|max:7',
            'is_public' => 'nullable|boolean',
            'shared_users' => 'nullable|array',
            'shared_users.*' => 'exists:users,id',
            'permissions' => 'nullable|array',
            'permissions.*' => 'in:view,edit,manage',
        ]);

        if ($validator->fails()) {
            return redirect()->route('calendars.create')
                ->withErrors($validator)
                ->withInput();
        }

        $user = Auth::user();

        // Create the calendar
        $calendar = Calendar::create([
            'title' => $request->title,
            'description' => $request->description,
            'color' => $request->color,
            'is_public' => $request->boolean('is_public', false),
            'owner_id' => $user->id,
            'creator_id' => $user->id,
        ]);

        // Handle shared users
        if ($request->has('shared_users') && is_array($request->shared_users)) {
            foreach ($request->shared_users as $index => $userId) {
                $permission = $request->permissions[$index] ?? 'view';

                CalendarShare::create([
                    'calendar_id' => $calendar->id,
                    'user_id' => $userId,
                    'permission' => $permission,
                    'shared_by' => $user->id,
                ]);
            }
        }

        return redirect()->route('calendars.index')
            ->with('success', 'Calendar created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id, Request $request)
    {
        $calendar = Calendar::with(['owner', 'events' => function($query) {
            $query->where('start_date', '>=', now()->subDays(30))
                  ->where('start_date', '<=', now()->addDays(30));
        }, 'events.categories'])->findOrFail($id);

        // Check if user has access to this calendar
        $user = Auth::user();
        if (!$calendar->is_public && $calendar->owner_id !== $user->id && !$calendar->shares()->where('user_id', $user->id)->exists()) {
            abort(403, 'You do not have permission to view this calendar.');
        }

        // Check if this is the designated pickup calendar
        $isPickupCalendar = \App\Models\GlobalConfig::getPickupCalendarId() == $calendar->id;

        // Get initial date for calendar navigation (if provided)
        $initialDate = $request->query('date');

        return view('calendars.show', compact('calendar', 'isPickupCalendar', 'initialDate'));
    }

    /**
     * Show the pickup calendar.
     */
    public function pickupCalendar(Request $request)
    {
        $pickupCalendarId = \App\Models\GlobalConfig::getPickupCalendarId();

        if (!$pickupCalendarId) {
            abort(404, 'Pickup calendar not configured. Please set up the pickup calendar in Global Config.');
        }

        $calendar = Calendar::with(['owner', 'events' => function($query) {
            $query->where('start_date', '>=', now()->subDays(30))
                  ->where('start_date', '<=', now()->addDays(30));
        }, 'events.categories'])->findOrFail($pickupCalendarId);

        // Check if user has access to this calendar
        $user = Auth::user();
        if (!$calendar->is_public && $calendar->owner_id !== $user->id && !$calendar->shares()->where('user_id', $user->id)->exists()) {
            abort(403, 'You do not have permission to view this calendar.');
        }

        // This is always the pickup calendar
        $isPickupCalendar = true;

        // Get initial date for calendar navigation (if provided)
        $initialDate = $request->query('date');

        return view('calendars.show', compact('calendar', 'isPickupCalendar', 'initialDate'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $calendar = Calendar::with(['shares.user'])->findOrFail($id);

        // Check if user has permission to edit this calendar
        $user = Auth::user();
        $hasEditPermission = $user->hasPermission('edit_calendars');
        $hasEditOwnPermission = $user->hasPermission('edit_own_calendars');

        // If user has edit_own_calendars permission but not edit_calendars, check if they own the calendar
        if (!$hasEditPermission && $hasEditOwnPermission && $calendar->owner_id !== $user->id) {
            abort(403, 'You only have permission to edit your own calendars.');
        }

        // If user doesn't have either permission, check if they have shared access with manage permission
        if (!$hasEditPermission && !$hasEditOwnPermission &&
            ($calendar->owner_id !== $user->id &&
            !$calendar->shares()->where('user_id', $user->id)->where('permission', 'manage')->exists())) {
            abort(403, 'You do not have permission to edit this calendar.');
        }

        $users = User::where('id', '!=', Auth::id())->get();
        $sharedUsers = $calendar->shares->pluck('user_id')->toArray();
        $permissions = $calendar->shares->pluck('permission', 'user_id')->toArray();

        return view('calendars.edit', compact('calendar', 'users', 'sharedUsers', 'permissions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $calendar = Calendar::findOrFail($id);

        // Check if user has permission to update this calendar
        $user = Auth::user();
        $hasEditPermission = $user->hasPermission('edit_calendars');
        $hasEditOwnPermission = $user->hasPermission('edit_own_calendars');

        // If user has edit_own_calendars permission but not edit_calendars, check if they own the calendar
        if (!$hasEditPermission && $hasEditOwnPermission && $calendar->owner_id !== $user->id) {
            abort(403, 'You only have permission to edit your own calendars.');
        }

        // If user doesn't have either permission, check if they have shared access with manage permission
        if (!$hasEditPermission && !$hasEditOwnPermission &&
            ($calendar->owner_id !== $user->id &&
            !$calendar->shares()->where('user_id', $user->id)->where('permission', 'manage')->exists())) {
            abort(403, 'You do not have permission to update this calendar.');
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'color' => 'required|string|max:7',
            'is_public' => 'nullable|boolean',
            'shared_users' => 'nullable|array',
            'shared_users.*' => 'exists:users,id',
            'permissions' => 'nullable|array',
            'permissions.*' => 'in:view,edit,manage',
        ]);

        if ($validator->fails()) {
            return redirect()->route('calendars.edit', $calendar->id)
                ->withErrors($validator)
                ->withInput();
        }

        // Update the calendar
        $calendar->update([
            'title' => $request->title,
            'description' => $request->description,
            'color' => $request->color,
            'is_public' => $request->boolean('is_public', false),
        ]);

        // Update shared users
        $calendar->shares()->delete(); // Remove all existing shares

        if ($request->has('shared_users') && is_array($request->shared_users)) {
            foreach ($request->shared_users as $index => $userId) {
                $permission = $request->permissions[$index] ?? 'view';

                CalendarShare::create([
                    'calendar_id' => $calendar->id,
                    'user_id' => $userId,
                    'permission' => $permission,
                    'shared_by' => $user->id,
                ]);
            }
        }

        return redirect()->route('calendars.index')
            ->with('success', 'Calendar updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $calendar = Calendar::findOrFail($id);

        // Check if user has permission to delete this calendar
        $user = Auth::user();
        $hasDeletePermission = $user->hasPermission('delete_calendars');
        $hasDeleteOwnPermission = $user->hasPermission('delete_own_calendars');

        // If user has delete_own_calendars permission but not delete_calendars, check if they own the calendar
        if (!$hasDeletePermission && $hasDeleteOwnPermission && $calendar->owner_id !== $user->id) {
            abort(403, 'You only have permission to delete your own calendars.');
        }

        // If user doesn't have either permission, check if they own the calendar
        if (!$hasDeletePermission && !$hasDeleteOwnPermission && $calendar->owner_id !== $user->id) {
            abort(403, 'You do not have permission to delete this calendar.');
        }

        // Delete the calendar (soft delete)
        $calendar->delete();

        return redirect()->route('calendars.index')
            ->with('success', 'Calendar deleted successfully!');
    }
}
