<?php

namespace App\Http\Controllers;

use App\Models\Inventory;
use App\Models\PickupRequest;
use App\Models\GlobalConfig;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        $statusCounts = [
            'intake' => Inventory::where('status', 'intake')->count(),
            'refurbishing' => Inventory::where('status', 'refurbishing')->count(),
            'commodity' => Inventory::where('status', 'commodity')->count(),
            'cleaning' => Inventory::where('status', 'cleaning')->count(),
            'ready-to-list' => Inventory::where('status', 'ready-to-list')->count(),
            'forsale' => Inventory::where('status', 'forsale')->count(),
            'sold' => Inventory::where('status', 'sold')->count(),
        ];

        $newestListings = Inventory::latest()->take(6)->get();

        // Get today's pickup requests
        $todaysPickups = $this->getTodaysPickups();

        return view('dashboard', compact('statusCounts', 'newestListings', 'todaysPickups'));
    }

    /**
     * Get today's pickup requests with distance and leave time calculations
     */
    private function getTodaysPickups()
    {
        $timezone = GlobalConfig::getTimeZone();
        $today = Carbon::now($timezone);
        $startOfDay = $today->copy()->startOfDay();
        $endOfDay = $today->copy()->endOfDay();

        // Build the query for pickup requests
        $pickupRequests = PickupRequest::with(['customer', 'event.assignedDriver'])
            ->where(function($q) use ($startOfDay, $endOfDay) {
                // Include requests with preferred pickup date on this day
                $q->whereBetween('preferred_pickup_date', [$startOfDay->utc(), $endOfDay->utc()])
                  // Also include requests with scheduled events on this day
                  ->orWhereHas('event', function($eventQuery) use ($startOfDay, $endOfDay) {
                      $eventQuery->whereBetween('start_date', [$startOfDay->utc(), $endOfDay->utc()]);
                  });
            })
            // Include confirmed, pending, and incoming pickups for the day
            ->whereIn('status', ['confirmed', 'pending', 'incoming'])
            ->get()
            ->sortBy(function($pickup) {
                if ($pickup->event && $pickup->event->start_date) {
                    return $pickup->event->start_date;
                }
                return $pickup->preferred_pickup_date;
            });

        // Add distance and leave time data if warehouse location is configured
        $warehouseLocation = GlobalConfig::getWarehouseLocation();
        if ($warehouseLocation && $pickupRequests->count() > 0) {
            $this->addDistanceData($pickupRequests, $warehouseLocation, $timezone);
        }

        return $pickupRequests;
    }

    /**
     * Add distance and leave time data to pickup requests
     */
    private function addDistanceData($pickupRequests, $warehouseLocation, $timezone)
    {
        $googleMapsService = app(\App\Services\GoogleMapsService::class);

        foreach ($pickupRequests as $pickup) {
            if ($pickup->pickup_address) {
                try {
                    $distanceData = $googleMapsService->getDistanceMatrix(
                        $warehouseLocation,
                        $pickup->pickup_address,
                        ['units' => 'imperial', 'mode' => 'driving']
                    );

                    if ($distanceData['success']) {
                        $pickup->distance_text = $distanceData['distance_text'];
                        $pickup->duration_text = $distanceData['duration_text'];

                        // Calculate suggested leave time if we have duration and pickup time
                        if (isset($distanceData['duration'])) {
                            $travelTimeMinutes = ceil($distanceData['duration'] / 60); // Convert seconds to minutes, round up
                            $bufferMinutes = 15; // Add 15 minute buffer
                            $totalMinutes = $travelTimeMinutes + $bufferMinutes;

                            if ($pickup->event && $pickup->event->start_date) {
                                $pickupTime = $pickup->event->start_date->setTimezone($timezone);
                                $pickup->leave_time = $pickupTime->copy()->subMinutes($totalMinutes);
                            } elseif ($pickup->preferred_pickup_date) {
                                $pickupTime = $pickup->preferred_pickup_date->setTimezone($timezone);
                                $pickup->leave_time = $pickupTime->copy()->subMinutes($totalMinutes);
                            }
                        }
                    } else {
                        $pickup->distance_text = 'Could not calculate';
                        $pickup->duration_text = 'Could not calculate';
                    }
                } catch (\Exception $e) {
                    \Log::error('Error calculating distance for pickup request in dashboard', [
                        'pickup_request_id' => $pickup->id,
                        'error' => $e->getMessage()
                    ]);

                    $pickup->distance_text = 'Could not calculate';
                    $pickup->duration_text = 'Could not calculate';
                }
            }
        }
    }
}
