<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class DiagnosticsController extends Controller
{
    public function checkSignatureStorage()
    {
        $results = [
            'gd_installed' => extension_loaded('gd'),
            'storage_public_exists' => Storage::disk('public')->exists(''),
            'storage_signatures_exists' => Storage::disk('public')->exists('signatures'),
            'storage_public_writable' => false,
            'storage_signatures_writable' => false,
            'storage_path' => storage_path('app/public'),
            'signatures_path' => storage_path('app/public/signatures'),
            'php_version' => phpversion(),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'post_max_size' => ini_get('post_max_size'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
        ];
        
        // Check if storage/app/public is writable
        if (is_writable(storage_path('app/public'))) {
            $results['storage_public_writable'] = true;
        }
        
        // Check if storage/app/public/signatures exists and is writable
        $signaturesPath = storage_path('app/public/signatures');
        if (!file_exists($signaturesPath)) {
            // Try to create the directory
            if (mkdir($signaturesPath, 0755, true)) {
                $results['signatures_directory_created'] = true;
                $results['storage_signatures_exists'] = true;
                $results['storage_signatures_writable'] = is_writable($signaturesPath);
            } else {
                $results['signatures_directory_created'] = false;
            }
        } else {
            $results['storage_signatures_writable'] = is_writable($signaturesPath);
        }
        
        // Test image creation
        if ($results['gd_installed']) {
            try {
                // Create a simple test image
                $image = imagecreatetruecolor(100, 100);
                $white = imagecolorallocate($image, 255, 255, 255);
                imagefill($image, 0, 0, $white);
                
                // Try to save it
                $testImagePath = $signaturesPath . '/test_' . time() . '.png';
                $saveResult = imagepng($image, $testImagePath);
                $results['test_image_created'] = $saveResult;
                
                // Clean up
                imagedestroy($image);
                if (file_exists($testImagePath)) {
                    unlink($testImagePath);
                    $results['test_image_deleted'] = true;
                }
            } catch (\Exception $e) {
                $results['image_creation_error'] = $e->getMessage();
            }
        }
        
        return response()->json($results);
    }
}
