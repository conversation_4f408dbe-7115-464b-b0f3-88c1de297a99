<?php

namespace App\Http\Controllers;

use App\Models\File;
use App\Models\Certificate;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\MobilePayout;
use App\Services\FileService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FileController extends Controller
{
    protected $fileService;

    public function __construct(FileService $fileService)
    {
        $this->fileService = $fileService;
        // Auth is handled by the web middleware group in routes
    }

    /**
     * Upload a file for a certificate
     */
    public function uploadForCertificate(Request $request, Certificate $certificate)
    {
        $request->validate([
            'file' => 'required|file|mimes:pdf,jpg,jpeg,png,gif|max:10240', // 10MB max
            'description' => 'nullable|string|max:255',
            'is_coc' => 'nullable|boolean',
        ]);

        $file = $this->fileService->uploadFile(
            $request->file('file'),
            'certificates/' . $certificate->id,
            $certificate,
            [
                'description' => $request->description,
                'is_public' => false, // Always private for certificate documents
                'metadata' => [
                    'certificate_number' => $certificate->certificate_number,
                    'upload_context' => 'certificate_attachment',
                    'is_coc' => $request->has('is_coc') && $request->is_coc ? true : false,
                    'uploaded_at' => now()->toDateTimeString(),
                ],
            ]
        );

        // If this file is marked as Chain of Custody document, update the certificate stats
        if ($request->has('is_coc') && $request->is_coc) {
            // Update the certificate stats to mark this file as the COC document
            $stats = $certificate->stats ?? [];
            $stats['coc_file_id'] = $file->id;
            $certificate->stats = $stats;
            $certificate->save();

            // Log the action for debugging
            \Illuminate\Support\Facades\Log::info('File marked as Chain of Custody', [
                'certificate_id' => $certificate->id,
                'file_id' => $file->id,
                'file_name' => $file->original_filename,
                'certificate_stats' => $certificate->stats
            ]);
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'file' => $file,
                'message' => 'File uploaded successfully',
            ]);
        }

        return redirect()->back()->with('success', 'File uploaded successfully');
    }

    /**
     * Upload a file for a customer
     *
     * @param Request $request
     * @param Customer $customer
     * @param int|null $max_width Optional max width to resize images (in pixels)
     * @return mixed
     */
    public function uploadForCustomer(Request $request, Customer $customer, ?int $max_width = null)
    {
        $request->validate([
            'file' => 'required|file|mimes:pdf,jpg,jpeg,png,gif,doc,docx,xls,xlsx,txt|max:10240', // 10MB max
            'description' => 'nullable|string|max:255',
            'file_type' => 'nullable|string|in:photo_id,contract,receipt,other',
        ]);

        $uploadedFile = $request->file('file');
        $options = [
            'description' => $request->description,
            'is_public' => false, // Always private for customer documents
            'metadata' => [
                'customer_id' => $customer->id,
                'customer_name' => $customer->name,
                'upload_context' => 'customer_attachment',
                'file_type' => $request->file_type ?? 'other',
                'uploaded_at' => now()->toDateTimeString(),
            ],
        ];

        // Check if the file is an image and max_width is set
        $mimeType = $uploadedFile->getMimeType();
        if ($max_width && strpos($mimeType, 'image/') === 0 && $mimeType !== 'image/webp') {
            // Process image - resize and convert to WebP using the Image model

            // Store the original file temporarily
            $tempPath = $uploadedFile->store('temp', 'public');
            $sourcePath = storage_path('app/public/' . $tempPath);

            // Prepare the target path for the WebP file
            $filename = pathinfo($uploadedFile->getClientOriginalName(), PATHINFO_FILENAME) . '.webp';
            $targetDir = storage_path('app/private/customers/' . $customer->id);
            if (!file_exists($targetDir)) {
                mkdir($targetDir, 0755, true);
            }
            $targetPath = $targetDir . '/' . $filename;

            // Convert to WebP with quality 80% and specified max width
            $webpQuality = 80;
            $success = \App\Models\Image::convertToWebp($sourcePath, $targetPath, $webpQuality, $max_width);

            // Clean up the temp file
            \Illuminate\Support\Facades\Storage::disk('public')->delete($tempPath);

            if (!$success) {
                // If conversion fails, use the original file
                return $this->fileService->uploadFile(
                    $uploadedFile,
                    'customers/' . $customer->id,
                    $customer,
                    $options
                );
            }

            // Create a file record for the WebP file
            $file = new File([
                'filename' => $filename,
                'original_filename' => pathinfo($uploadedFile->getClientOriginalName(), PATHINFO_FILENAME) . '.webp',
                'filepath' => 'customers/' . $customer->id . '/' . $filename,
                'mime_type' => 'image/webp',
                'size' => filesize($targetPath),
                'extension' => 'webp',
                'description' => $options['description'] ?? null,
                'uploaded_by' => Auth::id(),
                'is_public' => $options['is_public'] ?? false,
                'metadata' => $options['metadata'] ?? null,
            ]);

            // Associate with the customer
            $customer->files()->save($file);
        } else {
            // Simple file upload - no processing
            $file = $this->fileService->uploadFile(
                $uploadedFile,
                'customers/' . $customer->id,
                $customer,
                $options
            );
        }

        // Check if this is an AJAX request
        if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            // Load additional relationships for the response
            $file->load('uploader');

            return response()->json([
                'success' => true,
                'file' => $file,
                'message' => 'File uploaded successfully',
            ]);
        }

        return redirect()->back()->with('success', 'File uploaded successfully');
    }

    /**
     * Upload a file for an invoice
     *
     * @param Request $request
     * @param Invoice $invoice
     * @param int|null $max_width Optional max width to resize images (in pixels)
     * @return mixed
     */
    public function uploadForInvoice(Request $request, Invoice $invoice, ?int $max_width = null)
    {
        $request->validate([
            'file' => 'required|file|mimes:pdf,jpg,jpeg,png,gif|max:10240', // 10MB max
            'description' => 'nullable|string|max:255',
            'file_type' => 'nullable|string|in:photo_id,item_photo,receipt,other',
        ]);

        $uploadedFile = $request->file('file');
        $options = [
            'description' => $request->description,
            'is_public' => false, // Always private for invoice documents
            'metadata' => [
                'invoice_number' => $invoice->invoice_number,
                'upload_context' => 'invoice_attachment',
                'file_type' => $request->file_type ?? 'other',
                'uploaded_at' => now()->toDateTimeString(),
            ],
        ];

        // Check if the file is an image and max_width is set
        $mimeType = $uploadedFile->getMimeType();
        if ($max_width && strpos($mimeType, 'image/') === 0 && $mimeType !== 'image/webp') {
            // Process image - resize and convert to WebP using the Image model

            // Store the original file temporarily
            $tempPath = $uploadedFile->store('temp', 'public');
            $sourcePath = storage_path('app/public/' . $tempPath);

            // Prepare the target path for the WebP file
            $filename = pathinfo($uploadedFile->getClientOriginalName(), PATHINFO_FILENAME) . '.webp';
            $targetDir = storage_path('app/private/invoices/' . $invoice->id);
            if (!file_exists($targetDir)) {
                mkdir($targetDir, 0755, true);
            }
            $targetPath = $targetDir . '/' . $filename;

            // Convert to WebP with quality 80% and specified max width
            $webpQuality = 80;
            $success = \App\Models\Image::convertToWebp($sourcePath, $targetPath, $webpQuality, $max_width);

            // Clean up the temp file
            \Illuminate\Support\Facades\Storage::disk('public')->delete($tempPath);

            if (!$success) {
                // If conversion fails, use the original file
                return $this->fileService->uploadFile(
                    $uploadedFile,
                    'invoices/' . $invoice->id,
                    $invoice,
                    $options
                );
            }

            // Create a new file object from the WebP file
            $webpSize = filesize($targetPath);
            $relativePath = 'invoices/' . $invoice->id . '/' . $filename;

            // Create File record manually since we're not using the standard upload process
            $file = new \App\Models\File([
                'filename' => $filename,
                'original_filename' => $uploadedFile->getClientOriginalName(),
                'filepath' => $relativePath,
                'mime_type' => 'image/webp',
                'size' => $webpSize,
                'extension' => 'webp',
                'description' => $options['description'],
                'uploaded_by' => Auth::id(),
                'is_public' => $options['is_public'],
                'metadata' => $options['metadata'] + [
                    'image_processing' => [
                        'converted_to_webp' => true,
                        'quality' => $webpQuality,
                        'max_width' => $max_width,
                        'original_mime' => $mimeType
                    ]
                ],
            ]);

            // Associate with the invoice
            $invoice->files()->save($file);
        } else {
            // Simple file upload - no processing
            $file = $this->fileService->uploadFile(
                $uploadedFile,
                'invoices/' . $invoice->id,
                $invoice,
                $options
            );
        }

        // Check if this is an AJAX request
        if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            // Load additional relationships for the response
            $file->load('uploader');

            return response()->json([
                'success' => true,
                'file' => $file,
                'message' => 'File uploaded successfully',
            ]);
        }

        return redirect()->back()->with('success', 'File uploaded successfully');
    }

    /**
     * Upload a file for a mobile payout
     *
     * @param Request $request
     * @param MobilePayout $mobilePayout
     * @param int|null $max_width Optional max width to resize images (in pixels)
     * @return mixed
     */
    public function uploadForMobilePayout(Request $request, MobilePayout $mobilePayout, ?int $max_width = null)
    {
        $request->validate([
            'file' => 'required|file|mimes:pdf,jpg,jpeg,png,gif,webp|max:10240', // 10MB max
            'description' => 'nullable|string|max:255',
            'file_type' => 'nullable|string|in:photo_id,item_photo,receipt,other',
        ]);

        $uploadedFile = $request->file('file');

        $options = [
            'description' => $request->input('description'),
            'is_public' => false,
            'metadata' => [
                'file_type' => $request->input('file_type', 'other'),
                'uploaded_via' => 'mobile_payout',
                'mobile_payout_id' => $mobilePayout->id,
            ],
        ];

        // Check if the file is an image and max_width is set
        $mimeType = $uploadedFile->getMimeType();
        if ($max_width && strpos($mimeType, 'image/') === 0 && $mimeType !== 'image/webp') {
            // Process image - resize and convert to WebP using the Image model

            // Store the original file temporarily
            $tempPath = $uploadedFile->store('temp', 'public');
            $sourcePath = storage_path('app/public/' . $tempPath);

            // Prepare the target path for the WebP file
            $filename = pathinfo($uploadedFile->getClientOriginalName(), PATHINFO_FILENAME) . '.webp';
            $targetDir = storage_path('app/private/mobile_payouts/' . $mobilePayout->id);
            if (!file_exists($targetDir)) {
                mkdir($targetDir, 0755, true);
            }
            $targetPath = $targetDir . '/' . $filename;

            // Convert to WebP with quality 80% and specified max width
            $webpQuality = 80;
            $success = \App\Models\Image::convertToWebp($sourcePath, $targetPath, $webpQuality, $max_width);

            // Clean up the temp file
            \Illuminate\Support\Facades\Storage::disk('public')->delete($tempPath);

            if (!$success) {
                // If conversion fails, use the original file
                return $this->fileService->uploadFile(
                    $uploadedFile,
                    'mobile_payouts/' . $mobilePayout->id,
                    $mobilePayout,
                    $options
                );
            }

            // Create a file record for the WebP file
            $file = new File([
                'filename' => $filename,
                'original_filename' => pathinfo($uploadedFile->getClientOriginalName(), PATHINFO_FILENAME) . '.webp',
                'filepath' => 'mobile_payouts/' . $mobilePayout->id . '/' . $filename,
                'mime_type' => 'image/webp',
                'size' => filesize($targetPath),
                'extension' => 'webp',
                'description' => $options['description'] ?? null,
                'uploaded_by' => Auth::id(),
                'is_public' => $options['is_public'] ?? false,
                'metadata' => $options['metadata'] ?? null,
            ]);

            // Associate with the mobile payout
            $mobilePayout->files()->save($file);
        } else {
            // Simple file upload - no processing
            $file = $this->fileService->uploadFile(
                $uploadedFile,
                'mobile_payouts/' . $mobilePayout->id,
                $mobilePayout,
                $options
            );
        }

        // Check if this is an AJAX request
        if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            // Load additional relationships for the response
            $file->load('uploader');

            return response()->json([
                'success' => true,
                'file' => $file,
                'message' => 'File uploaded successfully',
            ]);
        }

        return $file;
    }

    /**
     * Download a file
     */
    public function download(File $file)
    {
        // Check if user has permission to access this file
        if (!$file->userCanAccess()) {
            abort(403, 'You do not have permission to access this file');
        }

        return $this->fileService->downloadFile($file);
    }

    /**
     * View a file (without forcing download)
     */
    public function view(Request $request, File $file)
    {
        // Check if user has permission to access this file
        if (!$file->userCanAccess()) {
            abort(403, 'You do not have permission to access this file');
        }

        // Check if this is an image and if we need to resize it
        if ($file->is_image && ($request->has('width') || $request->has('height'))) {
            $width = $request->input('width', null);
            $height = $request->input('height', null);

            return $this->fileService->getResizedImage($file, $width, $height);
        }

        return $this->fileService->downloadFile($file, false);
    }

    /**
     * Generate and serve thumbnail for image files
     */
    public function thumbnail(File $file, $width = 150, $height = 150)
    {
        // Check if user has permission to access this file
        if (!$file->userCanAccess()) {
            abort(403, 'You do not have permission to access this file');
        }

        // Check if this is an image file
        if (!$file->is_image) {
            abort(404, 'File is not an image');
        }

        // Set cache headers for better performance
        $response = $this->fileService->getResizedImage($file, $width, $height);
        
        // Add cache headers - cache for 1 hour
        $response->headers->set('Cache-Control', 'public, max-age=3600');
        $response->headers->set('Expires', gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
        
        return $response;
    }

    /**
     * Delete a file
     */
    public function destroy(File $file)
    {
        $user = Auth::user();
        $relatedModel = $file->fileable;
        
        // DEBUGGING: Log the start of file deletion
        \Illuminate\Support\Facades\Log::info('FILE DELETION START', [
            'file_id' => $file->id,
            'file_name' => $file->original_filename,
            'file_path' => $file->filepath,
            'user_id' => $user->id,
            'user_email' => $user->email,
            'related_model_type' => $relatedModel ? get_class($relatedModel) : 'none',
            'related_model_id' => $relatedModel ? $relatedModel->id : null,
            'request_route' => request()->route()->getName(),
            'request_method' => request()->method(),
            'request_url' => request()->url(),
        ]);
        
        // Context-aware permission checking based on what the file is attached to
        $hasPermission = false;
        $permissionContext = 'unknown';
        
        if ($relatedModel instanceof \App\Models\Customer) {
            $permissionContext = 'customer';
            // Customer files require edit_customer_accounts permission or being the uploader
            $hasPermission = $user->hasPermission('edit_customer_accounts') || 
                           ($file->uploaded_by === $user->id);
            
            \Illuminate\Support\Facades\Log::info('CUSTOMER FILE DELETION', [
                'customer_id' => $relatedModel->id,
                'customer_name' => $relatedModel->name,
                'customer_deleted_at' => $relatedModel->deleted_at,
                'has_edit_permission' => $user->hasPermission('edit_customer_accounts'),
                'is_uploader' => ($file->uploaded_by === $user->id),
                'final_permission' => $hasPermission,
            ]);
        } elseif ($relatedModel instanceof \App\Models\Invoice) {
            $permissionContext = 'invoice';
            // Invoice files require edit_invoices permission or being the uploader
            $hasPermission = $user->hasPermission('edit_invoices') || 
                           ($file->uploaded_by === $user->id);
        } elseif ($relatedModel instanceof \App\Models\Certificate) {
            $permissionContext = 'certificate';
            // Certificate files require delete_certificates permission or being the uploader
            $hasPermission = $user->hasPermission('delete_certificates') || 
                           ($file->uploaded_by === $user->id);
        } elseif ($relatedModel instanceof \App\Models\MobilePayout) {
            $permissionContext = 'mobile_payout';
            // Mobile payout files require appropriate permission or being the uploader
            $hasPermission = $user->hasPermission('edit_invoices') || 
                           ($file->uploaded_by === $user->id);
        } else {
            $permissionContext = 'orphaned';
            // For files not attached to a specific model, only uploader or admin can delete
            $hasPermission = ($file->uploaded_by === $user->id) || $user->isAdmin();
        }
        
        // Admin users can always delete files
        if ($user->isAdmin()) {
            $hasPermission = true;
        }
        
        \Illuminate\Support\Facades\Log::info('FILE DELETION PERMISSION CHECK', [
            'permission_context' => $permissionContext,
            'has_permission' => $hasPermission,
            'is_admin' => $user->isAdmin(),
        ]);
        
        if (!$hasPermission) {
            \Illuminate\Support\Facades\Log::warning('FILE DELETION PERMISSION DENIED', [
                'file_id' => $file->id,
                'user_id' => $user->id,
                'permission_context' => $permissionContext,
            ]);
            abort(403, 'You do not have permission to delete this file');
        }

        // Check if this file is marked as a Chain of Custody document
        $isCocDocument = false;

        if ($relatedModel && $relatedModel instanceof \App\Models\Certificate) {
            $certificate = $relatedModel;
            $stats = $certificate->stats ?? [];

            // If this file is the CoC document, remove it from the stats
            if (isset($stats['coc_file_id']) && $stats['coc_file_id'] == $file->id) {
                $isCocDocument = true;
                unset($stats['coc_file_id']);
                $certificate->stats = $stats;
                $certificate->save();
                
             
            }
        }




        // Clear the loaded relationship to prevent any cascading
        $file->unsetRelation('fileable');
        
        $this->fileService->deleteFile($file);



        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully',
                'was_coc_document' => $isCocDocument
            ]);
        }

        return redirect()->back()->with('success', 'File deleted successfully');
    }

    /**
     * Update file notes/description
     */
    public function updateNotes(Request $request, File $file)
    {
        // Validate the request
        $request->validate([
            'description' => 'nullable|string|max:1000',
        ]);

        // Update the file description
        $file->description = $request->description;
        $file->save();

        return response()->json([
            'success' => true,
            'message' => 'File notes updated successfully',
            'file' => $file,
        ]);
    }
}
