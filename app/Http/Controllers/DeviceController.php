<?php

namespace App\Http\Controllers;

use App\Models\Certificate;
use App\Models\Device;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DeviceController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'certificate_id' => 'required|exists:certificates,id',
            'serial_number' => 'required|string',
        ]);

        $device = Device::create([
            'certificate_id' => $request->certificate_id,
            'serial_number' => $request->serial_number,
            'device_type' => $request->device_type ?? 'unknown',
            'manufacturer' => $request->manufacturer ?? null,
            'model' => $request->model ?? null,
            'pc_manufacturer' => $request->pc_manufacturer ?? null,
            'pc_serial_number' => $request->pc_serial_number ?? null,
            'pc_form_factor' => $request->pc_form_factor ?? null,
        ]);

        // Device creation is automatically logged to both device and certificate via model events

        return response()->json([
            'success' => true,
            'device' => [
                'id' => $device->id,
                'serial_number' => $device->serial_number,
                'device_type' => $device->device_type,
                'manufacturer' => $device->manufacturer,
                'model' => $device->model,
                'pc_manufacturer' => $device->pc_manufacturer,
                'pc_serial_number' => $device->pc_serial_number,
                'pc_form_factor' => $device->pc_form_factor,
                'is_data_storage_device' => $device->is_data_storage_device,
                'created_at' => $device->created_at->format('Y-m-d H:i:s'),
            ]
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Device $device)
    {
        try {
            // Device deletion is automatically logged to both device and certificate via model events
            $device->delete();
            return response()->json([
                'success' => true,
                'device' => [
                    'id' => $device->id,
                    'serial_number' => $device->serial_number,
                    'device_type' => $device->device_type,
                    'manufacturer' => $device->manufacturer,
                    'model' => $device->model,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Device $device)
    {
        try {
            Log::info('DeviceController::show - Device ID', ['device_id' => $device->id]);

            if (!$device) {
                Log::warning('DeviceController::show - Device not found', ['device_id' => $device->id]);
                return response()->json(['error' => 'Device not found'], 404);
            }

            $data = [
                'id' => $device->id,
                'serial_number' => $device->serial_number,
                'device_type' => $device->device_type,
                'manufacturer' => $device->manufacturer,
                'model' => $device->model,
                'is_destroyed' => $device->is_destroyed,
                'destroyed_at' => $device->destroyed_at ? $device->destroyed_at->format('Y-m-d H:i:s') : null,
                'destroyed_by' => $device->destroyed_by,
                'destroyed_by_name' => $device->destroyedByUser ? $device->destroyedByUser->name : null,
                'notes' => $device->notes,
            ];

            Log::info('DeviceController::show - Returning data', ['data' => $data]);
            return response()->json($data);
        } catch (\Exception $e) {
            Log::error('Error in DeviceController::show', ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json(['error' => 'Failed to retrieve device details', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Toggle the destruction status of a device
     */
    public function toggleDestruction(Request $request, Device $device)
    {
        try {
            $request->validate([
                'is_destroyed' => 'required|boolean',
                'notes' => 'nullable|string',
            ]);

            $isDestroyed = $request->boolean('is_destroyed');

            // Update the device destruction status
            $device->is_destroyed = $isDestroyed;

            // Only update notes if they are present in the request
            if ($request->has('notes')) {
                $device->notes = $request->input('notes');
            }

            // If marking as destroyed, set the timestamp and user
            if ($isDestroyed) {
                $device->destroyed_at = now();
                $device->destroyed_by = Auth::id();
            } else {
                // If unmarking as destroyed, clear the timestamp and user
                $device->destroyed_at = null;
                $device->destroyed_by = null;
            }

            $device->save();

            // Log the destruction status change to both device and certificate
            $eventType = $isDestroyed ? 'device_marked_destroyed' : 'device_marked_not_destroyed';
            $description = $isDestroyed
                ? "Device marked as destroyed: ID #{$device->id}, {$device->getDeviceDescription()}"
                : "Device marked as not destroyed: ID #{$device->id}, {$device->getDeviceDescription()}";

            $additionalData = [
                'action' => $eventType,
                'destruction_status' => [
                    'is_destroyed' => $isDestroyed,
                    'destroyed_at' => $device->destroyed_at?->format('Y-m-d H:i:s'),
                    'destroyed_by' => Auth::user()?->name,
                    'notes' => $request->input('notes'),
                ],
                'device_specs' => [
                    'serial_number' => $device->serial_number,
                    'device_type' => $device->device_type,
                    'device_type_label' => \App\Models\Device::DEVICE_TYPES[$device->device_type] ?? $device->device_type,
                    'manufacturer' => $device->manufacturer,
                    'model' => $device->model,
                ]
            ];

            // Log to device (this will automatically log to certificate via the device's logActivity method)
            $device->logActivity($eventType, $description, $additionalData);

            // Load the user relationship for the response
            $device->load('destroyedByUser');

            return response()->json([
                'success' => true,
                'device' => [
                    'id' => $device->id,
                    'serial_number' => $device->serial_number,
                    'device_type' => $device->device_type,
                    'manufacturer' => $device->manufacturer,
                    'model' => $device->model,
                    'is_destroyed' => $device->is_destroyed,
                    'destroyed_at' => $device->destroyed_at ? $device->destroyed_at->format('Y-m-d H:i:s') : null,
                    'destroyed_by' => $device->destroyed_by,
                    'destroyed_by_name' => $device->destroyedByUser ? $device->destroyedByUser->name : null,
                    'notes' => $device->notes,
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error in DeviceController::toggleDestruction', ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Update the notes for a device
     */
    public function updateNotes(Request $request, Device $device)
    {
        try {
            $request->validate([
                'notes' => 'nullable|string',
            ]);

            $oldNotes = $device->notes;
            $newNotes = $request->input('notes');

            $device->notes = $newNotes;
            $device->save();

            // Log the notes update if there was a meaningful change
            if ($oldNotes !== $newNotes) {
                $description = "Device notes updated: ID #{$device->id}, {$device->getDeviceDescription()}";
                $additionalData = [
                    'action' => 'device_notes_updated',
                    'notes_change' => [
                        'old_notes' => $oldNotes,
                        'new_notes' => $newNotes,
                    ],
                    'device_specs' => [
                        'serial_number' => $device->serial_number,
                        'device_type' => $device->device_type,
                        'device_type_label' => \App\Models\Device::DEVICE_TYPES[$device->device_type] ?? $device->device_type,
                        'manufacturer' => $device->manufacturer,
                        'model' => $device->model,
                    ]
                ];

                // Log to device (this will automatically log to certificate via the device's logActivity method)
                $device->logActivity('device_notes_updated', $description, $additionalData);
            }

            return response()->json([
                'success' => true,
                'device' => [
                    'id' => $device->id,
                    'notes' => $device->notes,
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error in DeviceController::updateNotes', ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
