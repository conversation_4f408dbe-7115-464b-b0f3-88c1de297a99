<?php

namespace App\Http\Controllers;

use App\Models\Image;
use App\Models\Inventory;
use App\Models\InventoryImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use ZipArchive;
use Illuminate\Support\Facades\Storage;

class InventoryImageController extends Controller
{

    public function store(Request $request, Inventory $inventory)
    {
        // Handle both single file and array of files
        $photos = $request->file('photos');

        // Convert to array if it's a single file
        if ($photos instanceof \Illuminate\Http\UploadedFile) {
            $photos = [$photos];
        } else if (!is_array($photos)) {
            $photos = [];
        }

        $photoCount = count($photos);

        // Check if adding these photos would exceed the limit
        $currentCount = $inventory->images()->count();
        $remainingSlots = 20 - $currentCount;

        if ($photoCount > $remainingSlots) {
            if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
                return response()->json([
                    'success' => false,
                    'message' => "You can only upload {$remainingSlots} more photos. You selected {$photoCount}."
                ], 422);
            }
            return redirect()->back()->withErrors(['photos' => "You can only upload {$remainingSlots} more photos. You selected {$photoCount}."]);
        }

        // If no photos were provided, return early
        if ($photoCount === 0) {
            if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
                return response()->json([
                    'success' => false,
                    'message' => 'No photos were provided.'
                ], 422);
            }
            return redirect()->back()->withErrors(['photos' => 'No photos were provided.']);
        }

        $uploadedImages = [];
        $errors = [];

        // Process each photo
        foreach ($photos as $index => $photo) {
            try {
                // Validate the file
                if (!$photo->isValid()) {
                    $errors[] = "File {$index} is invalid.";
                    continue;
                }

                // Check file size (10MB limit)
                if ($photo->getSize() > 10 * 1024 * 1024) {
                    $errors[] = "File {$photo->getClientOriginalName()} exceeds the 10MB size limit.";
                    continue;
                }

                // Check file type
                $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                if (!in_array($photo->getMimeType(), $allowedTypes)) {
                    $errors[] = "File {$photo->getClientOriginalName()} is not a supported image type.";
                    continue;
                }

                // Create a new request object for the ImageController
                $newRequest = new Request();
                $newRequest->setMethod('POST');
                $newRequest->files->set('file', $photo);

                // Call the ImageController's store method
                $imageController = app(\App\Http\Controllers\ImageController::class);
                $response = $imageController->store($newRequest);

                if ($response->getStatusCode() === 200) {
                    $imageData = $response->getData()->image;

                    // Get the next order number
                    $nextOrder = $inventory->images()->max('order') + 1;

                    $inventoryImage = $inventory->images()->create([
                        'image_id' => $imageData->id,
                        'order' => $nextOrder,
                    ]);

                    $uploadedImages[] = [
                        'id' => $inventoryImage->id,
                        'image' => $imageData
                    ];
                } else {
                    $errors[] = "Failed to process file {$photo->getClientOriginalName()}.";
                }
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('Error uploading photo: ' . $e->getMessage());
                $errors[] = "Error processing file {$index}: " . $e->getMessage();
            }
        }

        // Return response based on request type
        if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            return response()->json([
                'success' => count($uploadedImages) > 0,
                'message' => count($uploadedImages) > 0
                    ? count($uploadedImages) . ' photos uploaded successfully!'
                    : 'Failed to upload photos.',
                'images' => $uploadedImages,
                'errors' => $errors
            ]);
        }

        // For regular form submissions
        if (count($errors) > 0) {
            return redirect()->back()
                ->with('success', count($uploadedImages) > 0 ? count($uploadedImages) . ' photos uploaded successfully!' : null)
                ->withErrors(['photos' => $errors]);
        }

        return redirect()->back()->with('success', count($uploadedImages) . ' photos uploaded successfully!');
    }



    public function reorderPhotos(Request $request, Inventory $inventory)
    {
        $orderedIds = $request->input('orderedIds');

        if (!is_array($orderedIds)) {
            return response()->json(['message' => 'Invalid data'], 400);
        }

        foreach ($orderedIds as $index => $id) {
            InventoryImage::where('inventory_id', $inventory->id)
                ->where('id', $id)
                ->update(['order' => $index + 1]);
        }

        return response()->json(['message' => 'Photo order updated successfully']);
    }

    public function destroy($id)
    {
        // Fetch the InventoryImage by its ID
        $inventoryImage = InventoryImage::find($id);

        // If the link doesn't exist, return an error
        if (!$inventoryImage) {
            return redirect()->back()->withErrors(['error' => 'Photo link not found.']);
        }

        // Log the image ID for debugging
        $id_to_delete = $inventoryImage->image_id;
        Log::info('Unlinking InventoryImage:', ['id' => $id, 'image_id' => $id_to_delete]);

        // Delete the link between the inventory and the image
        $inventoryImage->delete();

        return redirect()->back()->with('success', 'Photo with ID ' . $id_to_delete . ' unlinked successfully!');
    }


    public function downloadAll(Inventory $inventory)
    {
        // Get images using the new Image model system
        $images = Image::forContext('inventory', $inventory->id)
            ->get();

        if ($images->isEmpty()) {
            return response()->json(['error' => 'No images found.'], 404);
        }

        // Create filename using asset tag and item name
        $assetTag = $inventory->asset_tag ?? 'UNKNOWN';
        $itemName = $inventory->name ?? 'Item';

        // Sanitize filename by removing invalid characters
        $sanitizedName = preg_replace('/[^\w\s\-\.]/', '', $itemName);
        $sanitizedName = preg_replace('/\s+/', ' ', trim($sanitizedName));

        if ($images->count() === 1) {
            $image = $images->first();
            $filePath = public_path("storage/{$image->image_path}");

            if (!file_exists($filePath)) {
                return response()->json(['error' => 'Image file not found.'], 404);
            }

            // For single image, use asset tag + item name + original extension
            $extension = pathinfo($image->og_filename, PATHINFO_EXTENSION);
            $filename = "{$assetTag} {$sanitizedName}.{$extension}";

            return response()->download($filePath, $filename);
        }

        // For multiple images, create a zip file
        $zipFileName = "{$assetTag} {$sanitizedName}.zip";
        $zipFilePath = storage_path("app/public/{$zipFileName}");

        $zip = new ZipArchive();

        if ($zip->open($zipFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE)) {
            foreach ($images as $image) {
                $filePath = public_path("storage/{$image->image_path}");
                if (file_exists($filePath)) {
                    // Use original filename for files inside the zip
                    $zip->addFile($filePath, $image->og_filename);
                }
            }
            $zip->close();
        } else {
            Log::error('Failed to create ZIP file for inventory: ' . $inventory->id);
            return response()->json(['error' => 'Failed to create ZIP file.'], 500);
        }

        return response()->download($zipFilePath)->deleteFileAfterSend(true);
    }
}
