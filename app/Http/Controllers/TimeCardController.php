<?php

namespace App\Http\Controllers;

use App\Models\TimeCard;
use App\Models\TimePunch;
use App\Models\User;
use App\Models\GlobalConfig;
use App\Services\TimeCalculationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class TimeCardController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $viewAll = $user->hasPermission('view_timecards');
        $viewOwn = $user->hasPermission('view_own_timecards');

        if (!$viewAll && !$viewOwn) {
            return redirect()->route('dashboard')->with('error', 'You do not have permission to view time cards.');
        }

        // Get filter parameters
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));
        $userId = $request->input('user_id');
        $pagination = $request->input('pagination', 15);

        // Validate date range (max 90 days)
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);

        if ($end->diffInDays($start) > 90) {
            $end = $start->copy()->addDays(90);
            $endDate = $end->format('Y-m-d');
        }

        // Build query
        $query = TimeCard::with('user')
            ->whereBetween('date', [$startDate, $endDate])
            ->orderBy('date', 'desc');

        // If user can only view their own time cards, restrict query
        if (!$viewAll && $viewOwn) {
            $query->where('user_id', $user->id);
        } elseif ($userId) {
            $query->where('user_id', $userId);
        }

        // Only show time cards that have punches
        $query->whereHas('punches');

        // Get all time cards for calculating totals (without pagination)
        $allTimeCards = clone $query;
        $allTimeCardsCollection = $allTimeCards->get();

        // Calculate totals for the entire date range
        $totalWorkHours = $allTimeCardsCollection->sum(function($card) {
            return $card->timeToDecimalHours($card->total_hours);
        });

        $totalBreakHours = $allTimeCardsCollection->sum(function($card) {
            return $card->timeToDecimalHours($card->total_break_hours);
        });

        $totalSickHours = $allTimeCardsCollection->sum(function($card) {
            return $card->getTotalSickTimeDecimalAttribute();
        });

        $totalVacationHours = $allTimeCardsCollection->sum(function($card) {
            return $card->getTotalVacationTimeDecimalAttribute();
        });

        $totalPtoHours = $totalSickHours + $totalVacationHours;
        $totalBillableHours = $totalWorkHours + $totalPtoHours;
        $totalDays = $allTimeCardsCollection->count();

        // Get paginated time cards for display
        $timeCards = $query->paginate($pagination);

        // Get users for filter dropdown (if user has permission to view all)
        $users = $viewAll ? User::orderBy('name')->get() : collect([$user]);

        return view('timecards.index', compact(
            'timeCards',
            'users',
            'startDate',
            'endDate',
            'userId',
            'pagination',
            'totalWorkHours',
            'totalBreakHours',
            'totalSickHours',
            'totalVacationHours',
            'totalPtoHours',
            'totalBillableHours',
            'totalDays'
        ));
    }

    /**
     * Show the time clock page.
     */
    public function timeClock()
    {
        $user = Auth::user();
        $timeCalculationService = app(TimeCalculationService::class);

        $timeCard = $user->getTodayTimeCard(); // Creates one if it doesn't exist
        $punches = $timeCard->allValidPunches()->orderBy('punch_time', 'desc')->get();
        $currentStatus = $timeCard->getCurrentStatus();

        // Force a fresh calculation of hours to ensure we have the latest values
        $currentHours = $timeCard->getCurrentHours();
        Log::info("TimeCardController@timeClock - Current hours: " . json_encode($currentHours));

        $totalHours = $currentHours['total_hours'];
        $totalBreakHours = $currentHours['total_break_hours'];
        $formattedTotalHours = $currentHours['formatted_total_hours'];
        $formattedTotalBreakHours = $currentHours['formatted_total_break_hours'];

        $timezone = GlobalConfig::getTimeZone();

        // Calculate weekly summary using the centralized service
        $weeklySummary = $timeCalculationService->calculateWeeklyTimeSummary($user);
        $weeklyHours = $weeklySummary['total_hours'];
        $weeklyBreakHours = $weeklySummary['total_break_hours'];
        $weeklySickTime = $weeklySummary['total_sick_time'];
        $weeklyVacationTime = $weeklySummary['total_vacation_time'];
        $weeklyPtoTime = $weeklySummary['total_pto'];
        $weeklyBillableHours = $weeklySummary['total_billable_hours'];
        $weeklyOvertimeHours = $weeklySummary['total_overtime_hours'];
        $weeklyRegularHours = $weeklySummary['total_regular_hours'];
        $formattedWeeklyHours = $weeklySummary['formatted_total_hours'];
        $formattedWeeklyBreakHours = $weeklySummary['formatted_total_break_hours'];
        $formattedWeeklySickTime = $weeklySummary['formatted_total_sick_time'];
        $formattedWeeklyVacationTime = $weeklySummary['formatted_total_vacation_time'];
        $formattedWeeklyPtoTime = $weeklySummary['formatted_total_pto'];
        $formattedWeeklyBillableHours = $weeklySummary['formatted_total_billable_hours'];

        // Get minimum break duration from config
        $minBreakDuration = GlobalConfig::getMinBreakDuration();


        // Auto clock-out settings - ensure defaults are set if not configured
        if (GlobalConfig::getValue('timeclock_auto_clock_out_enabled') === null) {
            GlobalConfig::setValue('timeclock_auto_clock_out_enabled', '1');
        }
        
        if (GlobalConfig::getValue('timeclock_auto_clock_out_time') === null) {
            GlobalConfig::setValue('timeclock_auto_clock_out_time', '22:00');
        }
        
        $autoClockOutEnabled = GlobalConfig::isAutoClockOutEnabled();
        $autoClockOutTime = GlobalConfig::getAutoClockOutTime();
        
        // Fallback defaults if methods still return unexpected values
        if ($autoClockOutEnabled === null) {
            $autoClockOutEnabled = true;
        }
        
        if (empty($autoClockOutTime)) {
            $autoClockOutTime = '22:00';
        }

        return view('timecards.time-clock', compact(
            'timeCard',
            'currentStatus',
            'punches',
            'totalHours',
            'totalBreakHours',
            'formattedTotalHours',
            'formattedTotalBreakHours',
            'timezone',
            'weeklyHours',
            'weeklyBreakHours',
            'weeklySickTime',
            'weeklyVacationTime',
            'weeklyPtoTime',
            'weeklyBillableHours',
            'weeklyOvertimeHours',
            'weeklyRegularHours',
            'formattedWeeklyHours',
            'formattedWeeklyBreakHours',
            'formattedWeeklySickTime',
            'formattedWeeklyVacationTime',
            'formattedWeeklyPtoTime',
            'formattedWeeklyBillableHours',
            'minBreakDuration',
            'autoClockOutEnabled',
            'autoClockOutTime'
        ));
    }

    /**
     * Show the form for creating a time card for a specific date.
     */
    public function createForDate()
    {
        // Check if user has permission to edit time cards
        if (!Auth::user()->hasPermission('edit_timecards')) {
            return redirect()->route('timecards.index')->with('error', 'You do not have permission to create time cards.');
        }

        $users = User::orderBy('name')->get();
        return view('timecards.create-for-date', compact('users'));
    }

    /**
     * Store a time card for a specific date.
     */
    public function storeForDate(Request $request)
    {
        // Check if user has permission to edit time cards
        if (!Auth::user()->hasPermission('edit_timecards')) {
            return redirect()->route('timecards.index')->with('error', 'You do not have permission to create time cards.');
        }

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'date' => 'required|date',
            'notes' => 'nullable|string',
        ]);

        // Check if a time card already exists for this user and date
        $existingCard = TimeCard::where('user_id', $validated['user_id'])
            ->where('date', $validated['date'])
            ->first();

        if ($existingCard) {
            return redirect()->route('timecards.edit', $existingCard->id)
                ->with('info', 'A time card already exists for this user and date. You can edit it below.');
        }

        // Create new time card
        $timeCard = TimeCard::create($validated);

        return redirect()->route('timecards.edit', $timeCard->id)
            ->with('success', 'Time card created successfully. You can now add punches.');
    }

    /**
     * Display the specified resource.
     */
    public function show(TimeCard $timecard)
    {
        $user = Auth::user();
        $canViewAll = $user->hasPermission('view_timecards');
        $canViewOwn = $user->hasPermission('view_own_timecards');

        // Check if user has permission to view this time card
        if (!$canViewAll && (!$canViewOwn || $timecard->user_id !== $user->id)) {
            return redirect()->route('dashboard')->with('error', 'You do not have permission to view this time card.');
        }

        $timezone = GlobalConfig::getTimeZone();
        $punches = $timecard->allValidPunches()->orderBy('punch_time')->get();

        return view('timecards.show', compact('timecard', 'punches', 'timezone'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TimeCard $timecard)
    {
        $user = Auth::user();
        $canEditAll = $user->hasPermission('edit_timecards');
        $canEditOwn = $user->hasPermission('edit_own_timecards');

        // Check if user has permission to edit this time card
        if (!$canEditAll && (!$canEditOwn || $timecard->user_id !== $user->id)) {
            return redirect()->route('dashboard')->with('error', 'You do not have permission to edit this time card.');
        }

        $timezone = GlobalConfig::getTimeZone();
        $punches = $timecard->allValidPunches()->orderBy('punch_time')->get();

        return view('timecards.edit', compact('timecard', 'punches', 'timezone'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TimeCard $timecard)
    {
        $user = Auth::user();
        $canEditAll = $user->hasPermission('edit_timecards');
        $canEditOwn = $user->hasPermission('edit_own_timecards');

        // Check if user has permission to edit this time card
        if (!$canEditAll && (!$canEditOwn || $timecard->user_id !== $user->id)) {
            return redirect()->route('dashboard')->with('error', 'You do not have permission to edit this time card.');
        }

        $validated = $request->validate([
            'notes' => 'nullable|string',
        ]);

        $timecard->update($validated);

        return redirect()->route('timecards.edit', $timecard->id)
            ->with('success', 'Time card updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TimeCard $timecard)
    {
        // Only admins can delete time cards
        if (!Auth::user()->hasPermission('edit_timecards')) {
            return redirect()->route('timecards.index')->with('error', 'You do not have permission to delete time cards.');
        }

        $timecard->delete();

        return redirect()->route('timecards.index')
            ->with('success', 'Time card deleted successfully.');
    }

    public function clockIn()
    {
        $user = Auth::user();

        // Check if user already has a time card for today
        $timeCard = TimeCard::where('user_id', $user->id)
            ->whereDate('date', Carbon::today())
            ->first();

        if (!$timeCard) {
            // Create a new time card
            $timeCard = new TimeCard();
            $timeCard->user_id = $user->id;
            $timeCard->date = Carbon::today();
            $timeCard->save();
        }

        // Create a clock in punch
        $punch = new TimePunch();
        $punch->time_card_id = $timeCard->id;
        $punch->type = 'clock_in';
        $punch->punch_time = Carbon::now();
        $punch->save();

        // Update time card status
        $timeCard->is_clocked_in = true;
        $timeCard->is_on_break = false;
        $timeCard->save();

        return redirect()->route('timecards.time-clock')->with('success', 'Clocked in successfully!');
    }

    public function clockOut()
    {
        $user = Auth::user();

        // Get today's time card
        $timeCard = TimeCard::where('user_id', $user->id)
            ->whereDate('date', Carbon::today())
            ->first();

        if (!$timeCard || !$timeCard->isClockedIn()) {
            return redirect()->route('timecards.time-clock')->with('error', 'You are not clocked in!');
        }

        // If on break, end the break first
        if ($timeCard->isOnBreak()) {
            $this->endBreak();
        }

        // Create a clock out punch
        $punch = new TimePunch();
        $punch->time_card_id = $timeCard->id;
        $punch->type = 'clock_out';
        $punch->punch_time = Carbon::now();
        $punch->save();

        // Calculate final hours
        $timeCard->calculateHours(true);

        // Update time card status
        $timeCard->is_clocked_in = false;
        $timeCard->is_on_break = false;
        $timeCard->save();

        return redirect()->route('timecards.time-clock')->with('success', 'Clocked out successfully!');
    }

    public function startBreak()
    {
        $user = Auth::user();

        // Get today's time card
        $timeCard = TimeCard::where('user_id', $user->id)
            ->whereDate('date', Carbon::today())
            ->first();

        if (!$timeCard || !$timeCard->isClockedIn() || $timeCard->isOnBreak()) {
            return redirect()->route('timecards.time-clock')->with('error', 'You must be clocked in and not already on break!');
        }

        // Create a break in punch
        $punch = new TimePunch();
        $punch->time_card_id = $timeCard->id;
        $punch->type = 'break_in';
        $punch->punch_time = Carbon::now();
        $punch->save();

        // Update time card status
        $timeCard->is_on_break = true;
        $timeCard->save();

        return redirect()->route('timecards.time-clock')->with('success', 'Break started successfully!');
    }

    public function endBreak()
    {
        $user = Auth::user();

        // Get today's time card
        $timeCard = TimeCard::where('user_id', $user->id)
            ->whereDate('date', Carbon::today())
            ->first();

        if (!$timeCard || !$timeCard->isClockedIn() || !$timeCard->isOnBreak()) {
            return redirect()->route('timecards.time-clock')->with('error', 'You must be clocked in and on break!');
        }

        // Create a break out punch
        $punch = new TimePunch();
        $punch->time_card_id = $timeCard->id;
        $punch->type = 'break_out';
        $punch->punch_time = Carbon::now();
        $punch->save();

        // Calculate hours so far
        $timeCard->calculateHours(true);

        // Update time card status
        $timeCard->is_on_break = false;
        $timeCard->save();

        return redirect()->route('timecards.time-clock')->with('success', 'Break ended successfully!');
    }
}
