<?php

namespace App\Http\Controllers;

use App\Models\Permission;
use App\Models\UserGroup;
use Illuminate\Http\Request;

class PermissionController extends Controller
{
    /**
     * Display a listing of the permissions.
     */
    public function index()
    {
        $permissions = Permission::with('userGroups')->get();
        $userGroups = UserGroup::all();

        // Group permissions by component
        $groupedPermissions = [];
        foreach ($permissions as $permission) {
            $scope = $permission->scope;
            if (!isset($groupedPermissions[$scope])) {
                $groupedPermissions[$scope] = [];
            }
            $groupedPermissions[$scope][] = $permission;
        }

        // Sort the groups alphabetically
        ksort($groupedPermissions);

        return view('permissions.index', compact('groupedPermissions', 'userGroups'));
    }

    /**
     * Get permission details for AJAX request
     */
    public function getDetails(Permission $permission)
    {
        $permission->load('userGroups');
        return response()->json([
            'permission' => [
                'id' => $permission->id,
                'name' => $permission->name,
                'description' => $permission->description,
                'scope' => $permission->scope,
                'user_groups' => $permission->userGroups
            ]
        ]);
    }

    /**
     * Update permission groups via AJAX
     */
    public function updateGroups(Request $request, Permission $permission)
    {
        $request->validate([
            'group_id' => 'required|exists:user_groups,id',
            'action' => 'required|in:add,remove',
        ]);

        $groupId = $request->input('group_id');
        $action = $request->input('action');
        $adminGroup = UserGroup::where('name', 'Admin')->first();

        // Don't allow removing Admin group
        if ($adminGroup && $groupId == $adminGroup->id && $action === 'remove') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot remove Admin group from permissions'
            ], 422);
        }

        // Get current group IDs
        $currentGroupIds = $permission->userGroups->pluck('id')->toArray();

        if ($action === 'add' && !in_array($groupId, $currentGroupIds)) {
            // Add the group
            $permission->userGroups()->attach($groupId);
        } elseif ($action === 'remove' && in_array($groupId, $currentGroupIds)) {
            // Remove the group
            $permission->userGroups()->detach($groupId);
        }

        // Reload the permission with user groups
        $permission->load('userGroups');

        return response()->json([
            'success' => true,
            'user_groups' => $permission->userGroups
        ]);
    }
}
