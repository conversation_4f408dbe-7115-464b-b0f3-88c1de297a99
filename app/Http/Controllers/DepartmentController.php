<?php

namespace App\Http\Controllers;

use App\Models\Department;
use App\Models\User;
use Illuminate\Http\Request;

class DepartmentController extends Controller
{
    // Display all departments
    public function index()
    {
        $departments = Department::with('leader')->get();
        return view('departments.index', compact('departments'));
    }

    // Show form to create a new department
    public function create()
    {
        $users = User::all(); // Leaders
        return view('departments.create', compact('users'));
    }

    // Store a new department
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'user_id' => 'required|exists:users,id',
            'description' => 'nullable|string',
        ]);

        Department::create($validated);

        return redirect()->route('departments.index')->with('success', 'Department created successfully.');
    }

    // Show form to edit a department
    public function edit(Department $department)
    {
        $users = User::all(); // Leaders
        return view('departments.edit', compact('department', 'users'));
    }

    // Update a department
    public function update(Request $request, Department $department)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'user_id' => 'required|exists:users,id',
            'description' => 'nullable|string',
        ]);

        $department->update($validated);

        return redirect()->route('departments.index')->with('success', 'Department updated successfully.');
    }

    // Delete a department
    public function destroy(Department $department)
    {
        $department->delete();
        return redirect()->route('departments.index')->with('success', 'Department deleted successfully.');
    }
}
