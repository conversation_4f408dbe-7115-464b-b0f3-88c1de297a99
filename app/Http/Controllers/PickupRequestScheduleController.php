<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PickupRequest;
use App\Models\Event;
use App\Models\GlobalConfig;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PickupRequestScheduleController extends Controller
{
    /**
     * Step 2: Time Selection
     */
    public function step2(PickupRequest $pickupRequest)
    {
        // Ensure the pickup request has a customer linked
        if (!$pickupRequest->customer) {
            return redirect()->route('pickup-requests.show', $pickupRequest)
                ->with('error', 'Please link a customer to this pickup request before scheduling.');
        }

        // Ensure the pickup request is not already scheduled
        if ($pickupRequest->event) {
            return redirect()->route('pickup-requests.show', $pickupRequest)
                ->with('error', 'This pickup request is already scheduled.');
        }

        return view('pickup-requests.step2', compact('pickupRequest'));
    }

    /**
     * Save Step 2 data (Time selection)
     */
    public function saveStep2(Request $request, PickupRequest $pickupRequest)
    {
        $request->validate([
            'pickup_date' => 'required|date|after_or_equal:today',
            'pickup_time' => 'required|date_format:H:i'
        ]);

        // HTML date/time inputs work in the user's browser timezone, but we need to treat them
        // as if they're in the site's configured timezone for admin interface consistency.
        // This is the same approach used throughout the admin interface.
        $timezone = GlobalConfig::getTimeZone();
        $pickupDateTime = Carbon::parse($request->pickup_date . ' ' . $request->pickup_time, $timezone);

        // Store the selected time in session for step 3 (store as UTC for consistency)
        session([
            'pickup_schedule_' . $pickupRequest->id => [
                'pickup_datetime_utc' => $pickupDateTime->utc()->toDateTimeString(),
                'pickup_date' => $request->pickup_date,
                'pickup_time' => $request->pickup_time,
            ]
        ]);

        return redirect()->route('pickup-requests.step3', $pickupRequest);
    }

    /**
     * Step 3: Event Details
     */
    public function step3(PickupRequest $pickupRequest)
    {
        // Get the time selection from step 2
        $scheduleData = session('pickup_schedule_' . $pickupRequest->id);

        if (!$scheduleData) {
            return redirect()->route('pickup-requests.step2', $pickupRequest)
                ->with('error', 'Please select a pickup time first.');
        }

        // Prepare display data for the view
        if (isset($scheduleData['pickup_datetime_utc'])) {
            // Convert UTC time back to site timezone for display
            $timezone = GlobalConfig::getTimeZone();
            $pickupDateTime = Carbon::parse($scheduleData['pickup_datetime_utc'], 'UTC')->setTimezone($timezone);
            $scheduleData['pickup_date'] = $pickupDateTime->format('Y-m-d');
            $scheduleData['pickup_time'] = $pickupDateTime->format('H:i');
        }

        // Get available drivers
        $availableDrivers = User::where('enabled', true)
            ->orderBy('name')
            ->get(['id', 'name']);

        return view('pickup-requests.step3', compact('pickupRequest', 'scheduleData', 'availableDrivers'));
    }

    /**
     * Save Step 3 data (Event details) and create the event
     */
    public function saveStep3(Request $request, PickupRequest $pickupRequest)
    {
 

        $request->validate([
            'staff_needed' => 'required|integer|min:1|max:10',
            'assigned_driver_id' => 'nullable|exists:users,id',
            'internal_pickup_notes' => 'nullable|string|max:1000',
            'send_confirmation_email' => 'nullable|boolean'
        ]);

       

        // Get the time selection from step 2
        $scheduleData = session('pickup_schedule_' . $pickupRequest->id);

       

        if (!$scheduleData) {
            
            return redirect()->route('pickup-requests.step2', $pickupRequest)
                ->with('error', 'Session expired. Please start over.');
        }

        try {
            

            // Create the pickup event
            $this->createPickupEvent($pickupRequest, $scheduleData, $request);

            // Send pickup details email if requested
            if ($request->boolean('send_confirmation_email', false)) {
                try {
                    $pickupRequest->sendConfirmationEmail();
                } catch (\Exception $e) {
                    // Log the error but don't fail the event creation
                    Log::error('Failed to send pickup details email during event creation', [
                        'pickup_request_id' => $pickupRequest->id,
                        'error' => $e->getMessage(),
                        'created_by' => Auth::user()->name
                    ]);
                    
                    // Add a flash message about the email failure
                    session()->flash('warning', 'Pickup event created successfully, but pickup details email could not be sent. You can manually send it from the pickup details page.');
                }
            }

            // Clear the session data
            session()->forget('pickup_schedule_' . $pickupRequest->id);

            
            return redirect()->route('pickup-requests.success', $pickupRequest);

        } catch (\Exception $e) {
            
            return back()->withErrors(['error' => 'Failed to create pickup event: ' . $e->getMessage()]);
        }
    }

    /**
     * Success page
     */
    public function success(PickupRequest $pickupRequest)
    {
        // Ensure the pickup request has an event
        if (!$pickupRequest->event) {
            return redirect()->route('pickup-requests.show', $pickupRequest)
                ->with('error', 'No pickup event found for this request.');
        }

        return view('pickup-requests.success', compact('pickupRequest'));
    }

    /**
     * Create the pickup event
     */
    private function createPickupEvent(PickupRequest $pickupRequest, array $scheduleData, Request $request)
    {


        // Get pickup calendar
        $pickupCalendarId = GlobalConfig::getPickupCalendarId();
        
        if (!$pickupCalendarId) {
            throw new \Exception('Pickup calendar not configured.');
        }

        // Use the UTC datetime from session if available, otherwise parse from date/time
        if (isset($scheduleData['pickup_datetime_utc'])) {
            // Convert UTC back to site timezone for proper handling
            $timezone = GlobalConfig::getTimeZone();
            $pickupDateTime = Carbon::parse($scheduleData['pickup_datetime_utc'], 'UTC')->setTimezone($timezone);
        } else {
            // Fallback for older session data - parse in site timezone
            $timezone = GlobalConfig::getTimeZone();
            $pickupDateTime = Carbon::parse($scheduleData['pickup_date'] . ' ' . $scheduleData['pickup_time'], $timezone);
        }

        // Get event duration
        $eventDuration = GlobalConfig::getPickupEventDuration();
        $endDateTime = $pickupDateTime->copy()->addMinutes($eventDuration);

        

        // Create the event (Laravel will handle timezone conversion automatically)
        $event = Event::create([
            'title' => $pickupRequest->customer->name,
            'description' => $this->buildEventDescription($pickupRequest, $request),
            'start_date' => $pickupDateTime,
            'end_date' => $endDateTime,
            'all_day' => false,
            'location' => $pickupRequest->pickup_address,
            'calendar_id' => $pickupCalendarId,
            'is_pickup_event' => true,
            'customer_id' => $pickupRequest->customer_id,
            'assigned_driver_id' => $request->assigned_driver_id,
            'staff_needed' => $request->staff_needed,

            'creator_id' => Auth::id(),
        ]);

        
        // Handle internal pickup notes
        if ($request->internal_pickup_notes) {
            $pickupRequest->updateInternalStaffNotes($request->internal_pickup_notes);
        }

        // Link the event to the pickup request
        $pickupRequest->update([
            'event_id' => $event->id,
            'status' => 'pending'
        ]);

        // Ensure management token exists for customer appointment management
        $pickupRequest->ensureManagementToken();

        // Log the appointment scheduling
        $pickupRequest->logAppointmentScheduled($event, Auth::user()->name);

        
        return $event;
    }

    /**
     * Build the event description
     */
    private function buildEventDescription(PickupRequest $pickupRequest, Request $request): string
    {
        $description = "PICKUP REQUEST #{$pickupRequest->id}\n\n";
        $description .= "Customer: {$pickupRequest->customer->name}\n";
        $description .= "Contact: {$pickupRequest->contact_name}\n";
        $description .= "Phone: {$pickupRequest->phone}\n";
        $description .= "Email: {$pickupRequest->email}\n\n";
        
        if ($pickupRequest->business_name) {
            $description .= "Business: {$pickupRequest->business_name}\n";
        }
        
        $description .= "Address: {$pickupRequest->pickup_address}\n";
        $description .= "Items: {$pickupRequest->pickup_items}\n";
        $description .= "Quantity: {$pickupRequest->pickup_quantity}\n\n";
        
        if ($pickupRequest->property_location_details) {
            $description .= "Location Details: {$pickupRequest->property_location_details}\n";
        }
        
        if ($pickupRequest->other_notes) {
            $description .= "Customer Notes: {$pickupRequest->other_notes}\n";
        }
        
        if ($request->internal_pickup_notes) {
            $description .= "Internal Notes: {$request->internal_pickup_notes}\n";
        }
        
        $description .= "\nStaff Needed: {$request->staff_needed}";
        
        return $description;
    }

}
