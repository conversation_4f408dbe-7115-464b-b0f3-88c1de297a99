<?php

namespace App\Http\Controllers;

use App\Models\MobilePayout;
use App\Models\Customer;
use App\Models\Department;
use App\Models\InventoryCategory;
use App\Models\Inventory;
use App\Models\GlobalConfig;
use App\Models\TaxPolicy;
use App\Models\Invoice;
use App\Models\LineItem;
use App\Http\Controllers\FileController;
use App\Services\FileService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MobilePayoutController extends Controller
{
    /**
     * Display the EZ Payout starting page
     */
    public function index()
    {
        $userId = Auth::id();

        // Clean up old incomplete payouts (older than 1 hour)
        $deletedCount = MobilePayout::deleteOldIncompleteForUser($userId, 1);

        // Get current incomplete payouts for this user
        $incompletePayouts = MobilePayout::getIncompleteForUser($userId);

        return view('mobile.payouts.index', compact('incompletePayouts', 'deletedCount'));
    }

    /**
     * Start the payout creation process
     */
    public function start(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|exists:customers,id',
        ]);

        // Create the mobile payout record
        $mobilePayout = MobilePayout::create([
            'customer_id' => $request->customer_id,
            'created_by' => Auth::id(),
            'status' => 'in_progress',
        ]);

        // Redirect directly to step 1 (ID verification)
        return redirect()->route('mobile.payouts.step1', $mobilePayout);
    }

    /**
     * Step 1: Photo ID Verification
     */
    public function step1(MobilePayout $mobilePayout)
    {
        $customer = $mobilePayout->customer;
        $hasExistingId = $customer->hasPhotoIdOnFile();

        // Get existing photo IDs if any
        $existingPhotoIds = [];
        if ($hasExistingId) {
            $existingPhotoIds = $customer->files()
                ->where(function($query) {
                    $query->where('description', 'LIKE', '%photo_id%')
                          ->orWhere('description', 'LIKE', '%Customer ID%')
                          ->orWhere('description', 'LIKE', '%ID%')
                          ->orWhere('metadata->file_type', 'photo_id');
                })
                ->orderBy('created_at', 'desc')
                ->get();
        }

        return view('mobile.payouts.step1', compact('mobilePayout', 'hasExistingId', 'existingPhotoIds'));
    }

    /**
     * Save Step 1 data (Photo ID verification)
     */
    public function saveStep1(Request $request, MobilePayout $mobilePayout)
    {
        $customer = $mobilePayout->customer;
        $hasExistingId = $customer->hasPhotoIdOnFile();

        // If uploading new photo ID
        if ($request->hasFile('photo_id')) {
            $request->validate([
                'photo_id' => 'required|file|mimes:jpg,jpeg,png,pdf|max:10240',
            ]);

            // Upload the photo ID to customer
            $fileController = new FileController(app(FileService::class));
            $maxImageWidth = 1400;

            $photoIdRequest = new Request();
            $photoIdRequest->merge([
                'description' => 'Customer ID uploaded during EZ Payout on ' . now()->format('M j, Y'),
                'file_type' => 'photo_id'
            ]);
            $photoIdRequest->files->set('file', $request->file('photo_id'));

            $fileController->uploadForCustomer($photoIdRequest, $customer, $maxImageWidth);
        } elseif (!$hasExistingId) {
            // No existing ID and no new upload
            return back()->withErrors(['photo_id' => 'Photo ID is required to proceed.']);
        }

        // Save step data
        $mobilePayout->setStepData('step1', [
            'id_verified' => true,
            'verification_date' => now()->toDateTimeString(),
        ]);
        $mobilePayout->save();

        // Mark step 1 as completed
        $mobilePayout->markStepCompleted(1);

        return redirect()->route('mobile.payouts.step2', $mobilePayout);
    }

    /**
     * Step 2: Payout Details
     */
    public function step2(MobilePayout $mobilePayout)
    {
        $departments = Department::all();
        $categories = InventoryCategory::all();

        // Get payout products
        $payout_product_ids = GlobalConfig::decodeValue('payout_products') ?? [];
        $payout_products = Inventory::whereIn('id', $payout_product_ids)->get();

        // Get default tax policy
        $defaultTaxPolicy = TaxPolicy::where('name', 'Non-Taxable')->first() ?? TaxPolicy::first();

        return view('mobile.payouts.step2', compact('mobilePayout', 'departments', 'categories', 'payout_products', 'defaultTaxPolicy'));
    }

    /**
     * Save Step 2 data (Payout details)
     */
    public function saveStep2(Request $request, MobilePayout $mobilePayout)
    {
        $request->validate([
            'product_type' => 'required|string',
            'custom_product_name' => 'required_if:product_type,custom|nullable|string|max:255',
            'custom_product_category_id' => 'required_if:product_type,custom|nullable|exists:inventory_categories,id',
            'payout_amount' => 'required|numeric|min:0.01',
            'quantity' => 'required|integer|min:1',
            'department_id' => 'required|exists:departments,id',
            'description' => 'nullable|string|max:255',
            'tax_policy_id' => 'required|exists:tax_policies,id',
        ]);

        // Save step data
        $mobilePayout->setStepData('step2', [
            'product_type' => $request->product_type,
            'custom_product_name' => $request->custom_product_name,
            'custom_product_category_id' => $request->custom_product_category_id,
            'payout_amount' => $request->payout_amount,
            'quantity' => $request->quantity,
            'department_id' => $request->department_id,
            'description' => $request->description,
            'tax_policy_id' => $request->tax_policy_id,
        ]);
        $mobilePayout->save();

        // Mark step 2 as completed
        $mobilePayout->markStepCompleted(2);

        return redirect()->route('mobile.payouts.step3', $mobilePayout);
    }

    /**
     * Step 3: Item Photos
     */
    public function step3(MobilePayout $mobilePayout)
    {
        return view('mobile.payouts.step3', compact('mobilePayout'));
    }

    /**
     * Save Step 3 data (Item photos)
     */
    public function saveStep3(Request $request, MobilePayout $mobilePayout)
    {
        // Check if user is skipping photos
        if ($request->has('skip_photos')) {
            // Save step data indicating photos were skipped
            $mobilePayout->setStepData('step3', [
                'photos_uploaded' => false,
                'photos_skipped' => true,
                'photo_count' => 0,
                'skip_date' => now()->toDateTimeString(),
            ]);
            $mobilePayout->save();

            // Mark step 3 as completed
            $mobilePayout->markStepCompleted(3);

            // Complete the payout immediately
        return $this->completePayout($mobilePayout);
        }

        // Validate photos if they are being uploaded
        if ($request->hasFile('item_photos')) {
            $request->validate([
                'item_photos.*' => 'file|mimes:jpg,jpeg,png,webp|max:10240',
            ]);

            // Upload photos to the mobile payout record
            $fileController = new FileController(app(FileService::class));
            $maxImageWidth = 1400;
            $uploadedFileIds = [];

            foreach ($request->file('item_photos') as $photo) {
                $itemPhotoRequest = new Request();
                $itemPhotoRequest->merge([
                    'description' => 'Item photo for EZ payout - uploaded on ' . now()->format('M j, Y g:i A'),
                    'file_type' => 'item_photo'
                ]);
                $itemPhotoRequest->files->set('file', $photo);

                $uploadedFile = $fileController->uploadForMobilePayout($itemPhotoRequest, $mobilePayout, $maxImageWidth);
                if ($uploadedFile) {
                    $uploadedFileIds[] = $uploadedFile->id;
                }
            }

            // Save step data with photos
            $mobilePayout->setStepData('step3', [
                'photos_uploaded' => true,
                'photos_skipped' => false,
                'photo_count' => count($request->file('item_photos')),
                'photo_file_ids' => $uploadedFileIds,
                'upload_date' => now()->toDateTimeString(),
            ]);
        } else {
            // No photos uploaded and not explicitly skipped - treat as skipped
            $mobilePayout->setStepData('step3', [
                'photos_uploaded' => false,
                'photos_skipped' => true,
                'photo_count' => 0,
                'photo_file_ids' => [],
                'skip_date' => now()->toDateTimeString(),
            ]);
        }

        $mobilePayout->save();

        // Mark step 3 as completed
        $mobilePayout->markStepCompleted(3);

        // Complete the payout immediately
        return $this->completePayout($mobilePayout);
    }

    /**
     * Complete the payout by creating invoice and finalizing the transaction
     */
    private function completePayout(MobilePayout $mobilePayout)
    {
        $step3Data = $mobilePayout->getStepData('step3');

        DB::beginTransaction();
        try {
            $customer = $mobilePayout->customer;
            $step2Data = $mobilePayout->getStepData('step2');

            // Create the invoice (similar to PayoutController logic)
            $invoice = Invoice::create([
                'customer_id' => $customer->id,
                'invoice_date' => now(),
                'status' => 'Paid',
                'payment_method' => 'Cash',
                'notes' => 'This is a payout invoice created from the EZ Payout system.',
            ]);

            // Determine item details
            if ($step2Data['product_type'] === 'custom') {
                $itemId = null;
                $itemName = $step2Data['custom_product_name'];
            } else {
                $itemId = (int) $step2Data['product_type'];
                $product = Inventory::find($itemId);
                $itemName = $product ? $product->name : 'Unknown Product';
            }

            // Calculate pricing
            $price = -abs((float) $step2Data['payout_amount']); // Negative for payout
            $quantity = (int) $step2Data['quantity'];
            $subtotal = $price * $quantity;

            // Create line item
            $lineItem = new LineItem([
                'item_id' => $itemId,
                'quantity' => $quantity,
                'item_name' => $itemName,
                'price' => $price,
                'subtotal' => $subtotal,
                'tax' => 0, // Payouts typically non-taxable
                'description' => 'EZ PAYOUT: ' . ($step2Data['description'] ?? ''),
                'tax_policy_id' => $step2Data['tax_policy_id'],
                'department_id' => $step2Data['department_id'],
            ]);

            $invoice->lineItems()->save($lineItem);

            // Recalculate invoice totals
            $invoice->calculateTotals();
            $invoice->save();

            // Transfer photos from mobile payout to invoice (if photos were uploaded in step 3)
            if (!empty($step3Data['photos_uploaded']) && $step3Data['photos_uploaded'] && !empty($step3Data['photo_file_ids'])) {
                // Get the uploaded files from the mobile payout
                $uploadedFiles = $mobilePayout->files()->whereIn('id', $step3Data['photo_file_ids'])->get();

                foreach ($uploadedFiles as $file) {
                    // Create a copy of the file record for the invoice
                    $invoiceFile = $file->replicate();
                    $invoiceFile->fileable_type = 'App\Models\Invoice';
                    $invoiceFile->fileable_id = $invoice->id;
                    $invoiceFile->description = 'Item photo for EZ payout (transferred from mobile payout)';
                    $invoiceFile->save();

                    // Copy the actual file to the invoice directory
                    $sourcePath = storage_path('app/private/' . $file->filepath);
                    $targetDir = storage_path('app/private/invoices/' . $invoice->id);
                    if (!file_exists($targetDir)) {
                        mkdir($targetDir, 0755, true);
                    }
                    $targetPath = $targetDir . '/' . $file->filename;

                    if (file_exists($sourcePath)) {
                        copy($sourcePath, $targetPath);
                        // Update the file path for the invoice
                        $invoiceFile->filepath = 'invoices/' . $invoice->id . '/' . $file->filename;
                        $invoiceFile->save();
                    }
                }
            }

            // Update mobile payout record
            $mobilePayout->invoice_id = $invoice->id;
            $mobilePayout->status = 'completed';
            $mobilePayout->completed_at = now();
            $mobilePayout->save();

            DB::commit();

            // Refresh the model to ensure the invoice relationship is loaded
            $mobilePayout->refresh();
            $mobilePayout->load('invoice');

            return redirect()->route('mobile.payouts.success', $mobilePayout);

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to complete payout: ' . $e->getMessage()]);
        }
    }

    /**
     * Success page
     */
    public function success(MobilePayout $mobilePayout)
    {
        return view('mobile.payouts.success', compact('mobilePayout'));
    }
}
