<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\User;
use App\Models\UserGroup;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class NotificationController extends Controller
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Display notifications management page
     */
    public function index(Request $request): View
    {
        $pagination = $request->query('pagination', session('pagination', 10));
        session(['pagination' => $pagination]);

        $notifications = Notification::with('creator')
            ->orderBy('created_at', 'desc')
            ->paginate($pagination);

        return view('notifications.index', compact('notifications'));
    }

    /**
     * Show form for creating new notification
     */
    public function create(): View
    {
        $urgencyLevels = $this->notificationService->getUrgencyLevels();
        $targetTypes = $this->notificationService->getTargetTypes();
        $userGroups = UserGroup::orderBy('name')->get();
        $users = User::where('enabled', true)->orderBy('name')->get();

        return view('notifications.create', compact('urgencyLevels', 'targetTypes', 'userGroups', 'users'));
    }

    /**
     * Store new notification
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'message' => 'required|string',
                'urgency' => 'required|in:low,normal,high,critical',
                'target_type' => 'required|in:all_users,user_group,specific_users',
                'target_ids' => 'nullable|array',
                'target_ids.*' => 'integer',
                'link_url' => 'nullable|url',
                'link_text' => 'nullable|string|max:100',
                'expires_at' => 'nullable|date|after:now',
                'is_dismissible' => 'required|in:true,false',
                'auto_dismiss' => 'required|in:true,false',
            ]);

            // Convert string booleans to actual booleans
            $validated['is_dismissible'] = $validated['is_dismissible'] === 'true';
            $validated['auto_dismiss'] = $validated['auto_dismiss'] === 'true';

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        }

        try {
            $expiresAt = $validated['expires_at'] ? Carbon::parse($validated['expires_at']) : null;

            switch ($validated['target_type']) {
                case Notification::TARGET_ALL_USERS:
                    $notification = $this->notificationService->sendToAllUsers(
                        $validated['title'],
                        $validated['message'],
                        $validated['urgency'],
                        $validated['link_url'] ?? null,
                        $validated['link_text'] ?? null,
                        $expiresAt,
                        $validated['is_dismissible'] ?? true,
                        $validated['auto_dismiss'] ?? false
                    );
                    break;

                case Notification::TARGET_USER_GROUP:
                    if (empty($validated['target_ids'])) {
                        return response()->json(['error' => 'User groups must be selected'], 422);
                    }
                    $notification = $this->notificationService->sendToUserGroups(
                        $validated['target_ids'],
                        $validated['title'],
                        $validated['message'],
                        $validated['urgency'],
                        $validated['link_url'] ?? null,
                        $validated['link_text'] ?? null,
                        $expiresAt,
                        $validated['is_dismissible'] ?? true,
                        $validated['auto_dismiss'] ?? false
                    );
                    break;

                case Notification::TARGET_SPECIFIC_USERS:
                    if (empty($validated['target_ids'])) {
                        return response()->json(['error' => 'Users must be selected'], 422);
                    }
                    $notification = $this->notificationService->sendToUsers(
                        $validated['target_ids'],
                        $validated['title'],
                        $validated['message'],
                        $validated['urgency'],
                        $validated['link_url'] ?? null,
                        $validated['link_text'] ?? null,
                        $expiresAt,
                        $validated['is_dismissible'] ?? true,
                        $validated['auto_dismiss'] ?? false
                    );
                    break;

                default:
                    return response()->json(['error' => 'Invalid target type'], 422);
            }

            return response()->json([
                'success' => true,
                'message' => 'Notification sent successfully',
                'notification' => $notification
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to send notification: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get notifications for current user (API endpoint)
     */
    public function getUserNotifications(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'error' => 'User not authenticated',
                    'notifications' => [],
                    'unread_count' => 0
                ], 401);
            }

            $onlyUnread = $request->boolean('unread_only', false);
            $timestamp = $request->get('timestamp');

            $notifications = $this->notificationService->getNotificationsForUser($user, $onlyUnread);

            // Filter by timestamp if provided (for polling new notifications)
            if ($timestamp) {
                try {
                    $timestampDate = Carbon::createFromTimestamp($timestamp / 1000); // Convert from JS timestamp
                    $notifications = $notifications->filter(function ($notification) use ($timestampDate) {
                        return $notification->created_at->gt($timestampDate);
                    });
                } catch (\Exception $e) {
                    // If timestamp parsing fails, just ignore the filter
                    \Log::warning('Failed to parse notification timestamp filter', ['timestamp' => $timestamp, 'error' => $e->getMessage()]);
                }
            }

            $formattedNotifications = $notifications->map(function ($notification) use ($user) {
                try {
                    return [
                        'id' => $notification->id,
                        'title' => $notification->title ?? 'Untitled',
                        'message' => $notification->message ?? '',
                        'urgency' => $notification->urgency ?? 'normal',
                        'urgency_class' => $notification->getUrgencyClass(),
                        'urgency_icon' => $notification->getUrgencyIcon(),
                        'link_url' => $notification->link_url,
                        'link_text' => $notification->link_text,
                        'is_dismissible' => (bool) $notification->is_dismissible,
                        'created_at' => $notification->created_at->diffForHumans(),
                        'created_at_timestamp' => $notification->created_at->timestamp * 1000, // JS timestamp
                        'is_read' => $notification->isReadByUser($user),
                        'is_dismissed' => $notification->isDismissedByUser($user),
                    ];
                } catch (\Exception $e) {
                    \Log::error('Error formatting notification', ['notification_id' => $notification->id ?? 'unknown', 'error' => $e->getMessage()]);
                    return null;
                }
            })->filter(); // Remove null entries

            return response()->json([
                'success' => true,
                'notifications' => $formattedNotifications->values(), // Re-index array
                'unread_count' => $this->notificationService->getUnreadCountForUser($user)
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching user notifications', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch notifications',
                'notifications' => [],
                'unread_count' => 0
            ], 500);
        }
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Request $request, Notification $notification): JsonResponse
    {
        $user = auth()->user();

        try {
            $this->notificationService->markAsRead($notification, $user);

            return response()->json([
                'success' => true,
                'message' => 'Notification marked as read'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to mark notification as read'
            ], 500);
        }
    }

    /**
     * Mark notification as dismissed
     */
    public function markAsDismissed(Request $request, Notification $notification): JsonResponse
    {
        $user = auth()->user();

        try {
            $this->notificationService->markAsDismissed($notification, $user);

            return response()->json([
                'success' => true,
                'message' => 'Notification dismissed'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to dismiss notification'
            ], 500);
        }
    }

    /**
     * Dismiss all notifications for current user
     */
    public function dismissAll(Request $request): JsonResponse
    {
        $user = auth()->user();

        try {
            $count = $this->notificationService->dismissAllForUser($user);

            return response()->json([
                'success' => true,
                'message' => "Dismissed {$count} notifications",
                'dismissed_count' => $count
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to dismiss notifications'
            ], 500);
        }
    }

    /**
     * Delete notification (admin only)
     */
    public function destroy(Notification $notification): JsonResponse
    {
        try {
            $notification->delete();

            return response()->json([
                'success' => true,
                'message' => 'Notification deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to delete notification'
            ], 500);
        }
    }

    /**
     * Get notification count for current user
     */
    public function getNotificationCount(): JsonResponse
    {
        $user = auth()->user();
        $unreadCount = $this->notificationService->getUnreadCountForUser($user);

        return response()->json([
            'unread_count' => $unreadCount
        ]);
    }
}
