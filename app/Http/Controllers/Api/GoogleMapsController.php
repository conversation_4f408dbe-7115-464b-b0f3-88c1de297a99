<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\GoogleMapsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class GoogleMapsController extends Controller
{
    private $googleMapsService;

    public function __construct(GoogleMapsService $googleMapsService)
    {
        $this->googleMapsService = $googleMapsService;
    }

    /**
     * Get autocomplete suggestions for address input
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function autocomplete(Request $request): JsonResponse
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'input' => 'required|string|min:2|max:255',
                'sessionToken' => 'nullable|string|max:255',
                'restrictions' => 'nullable|array',
                'restrictions.*' => 'string|size:2', // Country codes should be 2 characters
                'origin' => 'nullable|array',
                'origin.lat' => 'required_with:origin|numeric|between:-90,90',
                'origin.lng' => 'required_with:origin|numeric|between:-180,180',
                'language' => 'nullable|string|max:10',
                'region' => 'nullable|string|size:2',
                'includedPrimaryTypes' => 'nullable|array',
                'includedPrimaryTypes.*' => 'string',
                'locationRestriction' => 'nullable|array',
                'locationRestriction.north' => 'required_with:locationRestriction|numeric|between:-90,90',
                'locationRestriction.south' => 'required_with:locationRestriction|numeric|between:-90,90',
                'locationRestriction.east' => 'required_with:locationRestriction|numeric|between:-180,180',
                'locationRestriction.west' => 'required_with:locationRestriction|numeric|between:-180,180',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid request parameters',
                    'details' => $validator->errors(),
                    'suggestions' => []
                ], 422);
            }

            $input = $request->input('input');
            $sessionToken = $request->input('sessionToken');
            
            // Prepare options
            $options = [];
            
            if ($request->has('restrictions')) {
                $options['restrictions'] = $request->input('restrictions');
            }
            
            if ($request->has('origin')) {
                $options['origin'] = $request->input('origin');
            }
            
            if ($request->has('language')) {
                $options['language'] = $request->input('language');
            }
            
            if ($request->has('region')) {
                $options['region'] = $request->input('region');
            }
            
            if ($request->has('includedPrimaryTypes')) {
                $options['includedPrimaryTypes'] = $request->input('includedPrimaryTypes');
            }
            
            if ($request->has('locationRestriction')) {
                $options['locationRestriction'] = $request->input('locationRestriction');
            }

            Log::info('Processing autocomplete request', [
                'input' => $input,
                'sessionToken' => $sessionToken ? 'present' : 'none',
                'options' => $options
            ]);

            // Call the Google Maps service
            $result = $this->googleMapsService->fetchAutocompleteSuggestions($input, $sessionToken, $options);

            // Transform the suggestions to match the expected frontend format
            $transformedSuggestions = [];
            if ($result['success'] && !empty($result['suggestions'])) {
                foreach ($result['suggestions'] as $suggestion) {
                    if (isset($suggestion['placePrediction'])) {
                        $placePrediction = $suggestion['placePrediction'];
                        
                        $transformedSuggestions[] = [
                            'placePrediction' => [
                                'placeId' => $placePrediction['placeId'] ?? null,
                                'text' => $placePrediction['text']['text'] ?? '',
                                'structuredFormat' => $placePrediction['structuredFormat'] ?? null,
                                'types' => $placePrediction['types'] ?? [],
                                'place' => $placePrediction['place'] ?? null
                            ]
                        ];
                    }
                }
            }

            return response()->json([
                'success' => $result['success'],
                'suggestions' => $transformedSuggestions,
                'sessionToken' => $result['sessionToken'] ?? $sessionToken,
                'error' => $result['error'] ?? null
            ]);

        } catch (\Exception $e) {
            Log::error('Autocomplete controller error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'An unexpected error occurred while processing your request',
                'suggestions' => []
            ], 500);
        }
    }

    /**
     * Get place details by place ID
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function placeDetails(Request $request): JsonResponse
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'placeId' => 'required|string|max:255',
                'sessionToken' => 'nullable|string|max:255',
                'fields' => 'nullable|array',
                'fields.*' => 'string|max:50',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid request parameters',
                    'details' => $validator->errors(),
                    'place' => null
                ], 422);
            }

            $placeId = $request->input('placeId');
            $sessionToken = $request->input('sessionToken');
            $fields = $request->input('fields', []);

            Log::info('Processing place details request', [
                'placeId' => $placeId,
                'sessionToken' => $sessionToken ? 'present' : 'none',
                'fields' => $fields
            ]);

            // Call the Google Maps service
            $result = $this->googleMapsService->fetchPlaceDetails($placeId, $sessionToken, $fields);

            return response()->json([
                'success' => $result['success'],
                'place' => $result['place'],
                'sessionToken' => $result['sessionToken'] ?? $sessionToken,
                'error' => $result['error'] ?? null
            ]);

        } catch (\Exception $e) {
            Log::error('Place details controller error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'An unexpected error occurred while processing your request',
                'place' => null
            ], 500);
        }
    }

    /**
     * Generate a new session token
     *
     * @return JsonResponse
     */
    public function generateSessionToken(): JsonResponse
    {
        try {
            $sessionToken = $this->googleMapsService->generateSessionToken();

            return response()->json([
                'success' => true,
                'sessionToken' => $sessionToken
            ]);

        } catch (\Exception $e) {
            Log::error('Session token generation error', [
                'message' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to generate session token'
            ], 500);
        }
    }
}
