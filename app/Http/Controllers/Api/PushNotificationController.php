<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\NotificationSubscription;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Minishlink\WebPush\VAPID;

class PushNotificationController extends Controller
{
    public function __construct(
        private NotificationService $notificationService
    ) {}

    /**
     * Get VAPID public key for client subscription
     */
    public function getVapidPublicKey(): JsonResponse
    {
        $publicKey = config('services.webpush.public_key');
        
        if (!$publicKey) {
            return response()->json([
                'error' => 'VAPID public key not configured'
            ], 500);
        }

        return response()->json([
            'publicKey' => $publicKey
        ]);
    }

    /**
     * Subscribe user to push notifications
     */
    public function subscribe(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'subscription.endpoint' => 'required|string',
            'subscription.keys.p256dh' => 'required|string',
            'subscription.keys.auth' => 'required|string',
            'device_info' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Invalid subscription data',
                'details' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            $subscriptionData = $request->input('subscription');
            
            // Check if subscription already exists
            $existingSubscription = NotificationSubscription::where('endpoint', $subscriptionData['endpoint'])
                ->where('user_id', $user->id)
                ->first();

            if ($existingSubscription) {
                // Update existing subscription
                $existingSubscription->update([
                    'public_key' => $subscriptionData['keys']['p256dh'],
                    'auth_token' => $subscriptionData['keys']['auth'],
                    'device_info' => $request->input('device_info'),
                    'is_active' => true,
                ]);

                return response()->json([
                    'message' => 'Subscription updated successfully',
                    'subscription_id' => $existingSubscription->id
                ]);
            }

            // Create new subscription
            $subscription = NotificationSubscription::create([
                'user_id' => $user->id,
                'endpoint' => $subscriptionData['endpoint'],
                'public_key' => $subscriptionData['keys']['p256dh'],
                'auth_token' => $subscriptionData['keys']['auth'],
                'device_info' => $request->input('device_info'),
                'is_active' => true,
            ]);

            Log::info('User subscribed to push notifications', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'endpoint' => substr($subscriptionData['endpoint'], 0, 50) . '...'
            ]);

            return response()->json([
                'message' => 'Subscription created successfully',
                'subscription_id' => $subscription->id
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to create push notification subscription', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to create subscription'
            ], 500);
        }
    }

    /**
     * Unsubscribe user from push notifications
     */
    public function unsubscribe(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'endpoint' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Endpoint is required'
            ], 422);
        }

        try {
            $user = auth()->user();
            $endpoint = $request->input('endpoint');

            $subscription = NotificationSubscription::where('endpoint', $endpoint)
                ->where('user_id', $user->id)
                ->first();

            if (!$subscription) {
                return response()->json([
                    'error' => 'Subscription not found'
                ], 404);
            }

            $subscription->deactivate();

            Log::info('User unsubscribed from push notifications', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id
            ]);

            return response()->json([
                'message' => 'Unsubscribed successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to unsubscribe from push notifications', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Failed to unsubscribe'
            ], 500);
        }
    }

    /**
     * Send test push notification
     */
    public function sendTest(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            
            $success = $this->notificationService->sendTestPushNotification($user);

            if ($success) {
                return response()->json([
                    'message' => 'Test notification sent successfully'
                ]);
            } else {
                return response()->json([
                    'error' => 'No active subscriptions found or failed to send'
                ], 404);
            }

        } catch (\Exception $e) {
            Log::error('Failed to send test push notification', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Failed to send test notification'
            ], 500);
        }
    }

    /**
     * Get user's notification subscriptions
     */
    public function getSubscriptions(): JsonResponse
    {
        try {
            $user = auth()->user();
            
            $subscriptions = $user->activeNotificationSubscriptions()
                ->select(['id', 'endpoint', 'device_info', 'created_at'])
                ->get()
                ->map(function ($subscription) {
                    return [
                        'id' => $subscription->id,
                        'endpoint' => substr($subscription->endpoint, 0, 50) . '...',
                        'device_info' => $subscription->device_info,
                        'created_at' => $subscription->created_at->format('Y-m-d H:i:s'),
                    ];
                });

            return response()->json([
                'subscriptions' => $subscriptions
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get user subscriptions', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Failed to get subscriptions'
            ], 500);
        }
    }

    /**
     * Generate VAPID keys (admin only)
     */
    public function generateVapidKeys(Request $request): JsonResponse
    {
        if (!auth()->user() || !auth()->user()->hasPermission('is_admin')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $keys = VAPID::createVapidKeys();
            
            return response()->json([
                'public_key' => $keys['publicKey'],
                'private_key' => $keys['privateKey'],
                'message' => 'Add these keys to your .env file as WEBPUSH_PUBLIC_KEY and WEBPUSH_PRIVATE_KEY'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to generate VAPID keys', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Failed to generate VAPID keys'
            ], 500);
        }
    }

    /**
     * Mark notification as dismissed (called from service worker)
     */
    public function markDismissed(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'notification_id' => 'required|integer|exists:notifications,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Invalid notification ID'
            ], 422);
        }

        try {
            // This endpoint can be called from service worker, so we might not have auth
            // We'll just log the dismissal for analytics
            Log::info('Notification dismissed via push notification', [
                'notification_id' => $request->input('notification_id'),
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'message' => 'Dismissal recorded'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to record notification dismissal', [
                'notification_id' => $request->input('notification_id'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Failed to record dismissal'
            ], 500);
        }
    }
}