<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\TimeCalculationService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class TimeClockController extends Controller
{
    /**
     * Get the current time card data for the authenticated user
     */
    public function getCurrentData()
    {
        try {
            $user = Auth::user();

            if (!$user) {
                Log::warning("Time clock API: Unauthenticated request");
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required',
                    'error_code' => 'UNAUTHENTICATED'
                ], 401);
            }

            $timeCalculationService = app(TimeCalculationService::class);

            $timeCard = $user->getTodayTimeCard(); // Creates one if it doesn't exist
            $currentStatus = $timeCard->getCurrentStatus();

            // Force a fresh calculation of hours to ensure we have the latest values
            $currentHours = $timeCard->getCurrentHours();

            // Get weekly summary using the centralized service
            $weeklySummary = $timeCalculationService->calculateWeeklyTimeSummary($user);

            // Log the refresh for debugging purposes
            Log::info("Time clock data refreshed for user {$user->id} ({$user->name}). Status: {$currentStatus}");
            Log::debug("Current hours: " . json_encode($currentHours));
            Log::debug("Weekly summary: " . json_encode($weeklySummary));

            return response()->json([
                'success' => true,
                'current_status' => $currentStatus,
                'total_hours' => $currentHours['total_hours'],
                'total_break_hours' => $currentHours['total_break_hours'],
                'formatted_total_hours' => $currentHours['formatted_total_hours'],
                'formatted_total_break_hours' => $currentHours['formatted_total_break_hours'],
                'weekly_hours' => $weeklySummary['total_hours'],
                'weekly_break_hours' => $weeklySummary['total_break_hours'],
                'weekly_sick_time' => $weeklySummary['total_sick_time'],
                'weekly_vacation_time' => $weeklySummary['total_vacation_time'],
                'weekly_pto' => $weeklySummary['total_pto'],
                'weekly_billable_hours' => $weeklySummary['total_billable_hours'],
                'weekly_overtime_hours' => $weeklySummary['total_overtime_hours'],
                'weekly_regular_hours' => $weeklySummary['total_regular_hours'],
                'formatted_weekly_hours' => $weeklySummary['formatted_total_hours'],
                'formatted_weekly_break_hours' => $weeklySummary['formatted_total_break_hours'],
                'formatted_weekly_sick_time' => $weeklySummary['formatted_total_sick_time'],
                'formatted_weekly_vacation_time' => $weeklySummary['formatted_total_vacation_time'],
                'formatted_weekly_pto' => $weeklySummary['formatted_total_pto'],
                'formatted_weekly_billable_hours' => $weeklySummary['formatted_total_billable_hours'],
                'timestamp' => now()->toISOString(),
                'user_agent' => request()->userAgent(),
                'ip_address' => request()->ip()
            ]);

        } catch (\Exception $e) {
            Log::error("Time clock API error for user " . (Auth::id() ?? 'unknown') . ": " . $e->getMessage(), [
                'exception' => $e,
                'user_agent' => request()->userAgent(),
                'ip_address' => request()->ip(),
                'stack_trace' => $e->getTraceAsString(),
                'request_headers' => request()->headers->all(),
                'request_method' => request()->method(),
                'request_url' => request()->fullUrl()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching time clock data',
                'error_code' => 'INTERNAL_ERROR',
                'timestamp' => now()->toISOString(),
                'debug_info' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }
}
