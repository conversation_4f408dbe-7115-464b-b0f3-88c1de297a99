<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\TimeCalculationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TimesheetController extends Controller
{
    /**
     * Show the form for generating a timesheet.
     */
    public function index()
    {
        // Check if user has permission to view timesheet reports
        if (!Auth::user()->hasPermission('view_timesheets')) {
            return redirect()->route('dashboard')->with('error', 'You do not have permission to view timesheet reports.');
        }

        // Get all users for the selection
        $users = User::orderBy('name')->get();

        // Default to current pay period (last two weeks)
        $endDate = Carbon::now()->format('Y-m-d');
        $startDate = Carbon::now()->subDays(13)->format('Y-m-d');

        return view('timesheets.index', compact('users', 'startDate', 'endDate'));
    }

    /**
     * Generate and display the timesheet.
     */
    public function generate(Request $request)
    {
        // Check if user has permission to view timesheet reports
        if (!Auth::user()->hasPermission('generate_timesheets')) {
            return redirect()->route('dashboard')->with('error', 'You do not have permission to view timesheet reports.');
        }

        // Validate the request
        $validated = $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $startDate = Carbon::parse($validated['start_date']);
        $endDate = Carbon::parse($validated['end_date']);
        $userIds = $validated['user_ids'];

        // Limit to 90 days maximum
        if ($endDate->diffInDays($startDate) > 90) {
            return redirect()->route('timesheets.index')
                ->with('error', 'Date range cannot exceed 90 days.')
                ->withInput();
        }

        // Get the users
        $users = User::whereIn('id', $userIds)->orderBy('name')->get();

        // Use the centralized time calculation service
        $timeCalculationService = app(TimeCalculationService::class);
        $timesheetData = [];

        foreach ($users as $user) {
            $userSummary = $timeCalculationService->calculateUserTimeSummary($user, $startDate, $endDate, true);
            $timesheetData[] = $this->formatTimesheetData($userSummary);
        }

        return view('timesheets.show', compact('timesheetData', 'startDate', 'endDate', 'users'));
    }

    /**
     * Format the timesheet data for display
     */
    private function formatTimesheetData($userSummary)
    {
        // Extract the user and weekly data
        $user = $userSummary['user'];
        $weeklyData = $userSummary['weekly_data'] ?? [];

        // Format the data for the view
        $formattedData = [
            'user' => $user,
            'weeks' => [],
            // Store both numeric and formatted values
            'total_hours' => $userSummary['total_hours'],
            'total_break_hours' => $userSummary['total_break_hours'],
            'total_overtime_hours' => $userSummary['total_overtime_hours'],
            'total_regular_hours' => $userSummary['total_regular_hours'],
            'total_sick_time' => $userSummary['total_sick_time'],
            'total_vacation_time' => $userSummary['total_vacation_time'],
            'total_pto' => $userSummary['total_pto'],
            'total_billable_hours' => $userSummary['total_billable_hours'],
            'formatted_total_hours' => $userSummary['formatted_total_hours'],
            'formatted_total_break_hours' => $userSummary['formatted_total_break_hours'],
            'formatted_total_sick_time' => $userSummary['formatted_total_sick_time'],
            'formatted_total_vacation_time' => $userSummary['formatted_total_vacation_time'],
            'formatted_total_pto' => $userSummary['formatted_total_pto'],
            'formatted_total_billable_hours' => $userSummary['formatted_total_billable_hours'],
        ];

        // Process each week
        foreach ($weeklyData as $weekData) {
            // Always create fresh Carbon instances for start and end dates
            $weekStart = $weekData['start_date']->copy()->startOfWeek(Carbon::SUNDAY);
            $weekEnd = $weekStart->copy()->endOfWeek(Carbon::SATURDAY);

            $weekSummary = [
                'start_date' => $weekStart,
                'end_date' => $weekEnd,
                'days' => [],
                'total_hours' => 0,
                'total_break_hours' => 0,
                'total_overtime_hours' => 0,
                'total_regular_hours' => 0,
                'total_sick_time' => 0,
                'total_vacation_time' => 0,
                'total_pto' => 0,
                'total_billable_hours' => 0,
                'in_range' => true,
            ];

            // Process time cards for this week
            foreach ($weekData['time_cards'] as $timeCard) {
                $dayDate = $timeCard->date->format('Y-m-d');

                // Calculate hours for this day
                $totalSeconds = $this->timeStringToSeconds($timeCard->total_hours);
                $breakSeconds = $this->timeStringToSeconds($timeCard->total_break_hours);
                $sickSeconds = $this->timeStringToSeconds($timeCard->total_sick_time);
                $vacationSeconds = $this->timeStringToSeconds($timeCard->total_vacation_time);

                // Add to day data
                $weekSummary['days'][$dayDate] = [
                    'date' => $timeCard->date,
                    'hours' => $this->formatSecondsToHoursMinutes($totalSeconds),
                    'break_hours' => $this->formatSecondsToHoursMinutes($breakSeconds),
                    'sick_time' => $this->formatSecondsToHoursMinutes($sickSeconds),
                    'vacation_time' => $this->formatSecondsToHoursMinutes($vacationSeconds),
                    'total_seconds' => $totalSeconds,
                    'break_seconds' => $breakSeconds,
                    'sick_seconds' => $sickSeconds,
                    'vacation_seconds' => $vacationSeconds,
                    'in_range' => true,
                    'time_card' => $timeCard,
                ];

                // Add to weekly totals
                $weekSummary['total_hours'] += $totalSeconds;
                $weekSummary['total_break_hours'] += $breakSeconds;
                $weekSummary['total_sick_time'] += $sickSeconds;
                $weekSummary['total_vacation_time'] += $vacationSeconds;
                $weekSummary['total_pto'] += ($sickSeconds + $vacationSeconds);
            }

            // Calculate weekly totals
            $weekTotalHours = $weekSummary['total_hours'] / 3600;
            // IMPORTANT: The total_hours from the time card already excludes breaks
            // So we should NOT subtract breaks again when calculating work hours
            $weekSickTimeHours = $weekSummary['total_sick_time'] / 3600;
            $weekVacationTimeHours = $weekSummary['total_vacation_time'] / 3600;
            $weekPtoHours = $weekSickTimeHours + $weekVacationTimeHours;

            // Calculate overtime
            if ($weekTotalHours > 40) {
                $weekSummary['total_overtime_hours'] = $weekTotalHours - 40;
                $weekSummary['total_regular_hours'] = 40;
            } else {
                $weekSummary['total_overtime_hours'] = 0;
                $weekSummary['total_regular_hours'] = $weekTotalHours;
            }

            // Calculate billable hours (work hours + PTO)
            $weekSummary['total_billable_hours'] = $weekTotalHours + $weekPtoHours;

            // Store numeric values for calculations in the view
            $weekSummary['total_hours_numeric'] = $weekTotalHours;
            $weekSummary['total_break_hours_numeric'] = $weekSummary['total_break_hours'] / 3600;
            $weekSummary['total_sick_time_numeric'] = $weekSickTimeHours;
            $weekSummary['total_vacation_time_numeric'] = $weekVacationTimeHours;
            $weekSummary['total_pto_numeric'] = $weekPtoHours;
            $weekSummary['total_billable_hours_numeric'] = $weekSummary['total_billable_hours'];
            $weekSummary['total_overtime_hours_numeric'] = $weekSummary['total_overtime_hours'];
            $weekSummary['total_regular_hours_numeric'] = $weekSummary['total_regular_hours'];

            // Format hours for display
            $weekSummary['total_hours'] = $this->formatSecondsToHoursMinutes($weekSummary['total_hours']);
            $weekSummary['total_break_hours'] = $this->formatSecondsToHoursMinutes($weekSummary['total_break_hours']);
            $weekSummary['total_sick_time'] = $this->formatSecondsToHoursMinutes($weekSummary['total_sick_time']);
            $weekSummary['total_vacation_time'] = $this->formatSecondsToHoursMinutes($weekSummary['total_vacation_time']);
            $weekSummary['total_pto'] = $this->formatSecondsToHoursMinutes($weekSummary['total_pto']);

            // Add week to formatted data
            $formattedData['weeks'][] = $weekSummary;
        }

        return $formattedData;
    }

    /**
     * Convert time string (HH:MM:SS) to seconds.
     */
    private function timeStringToSeconds($timeString)
    {
        if (empty($timeString)) {
            return 0;
        }

        if (!is_string($timeString)) {
            return floatval($timeString);
        }

        $parts = explode(':', $timeString);

        if (count($parts) < 2) {
            return 0;
        }

        $hours = (int) $parts[0];
        $minutes = (int) $parts[1];
        $seconds = isset($parts[2]) ? (int) $parts[2] : 0;

        return ($hours * 3600) + ($minutes * 60) + $seconds;
    }

    /**
     * Format seconds to HH:MM (no seconds).
     */
    private function formatSecondsToHoursMinutes($seconds)
    {
        $seconds = $this->ensureNumeric($seconds);
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        return sprintf('%02d:%02d', $hours, $minutes);
    }

    /**
     * Ensure a value is numeric, handling null values and strings.
     */
    private function ensureNumeric($value)
    {
        if ($value === null) {
            return 0;
        }

        if (is_string($value) && strpos($value, ':') !== false) {
            return $this->timeStringToSeconds($value);
        }

        return floatval($value);
    }

    /**
     * Show the print view for the timesheet.
     */
    public function print(Request $request)
    {
        // Check if user has permission to view timesheet reports
        if (!Auth::user()->hasPermission('generate_timesheets')) {
            return redirect()->route('dashboard')->with('error', 'You do not have permission to view timesheet reports.');
        }

        // Validate the request
        $validated = $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $startDate = Carbon::parse($validated['start_date']);
        $endDate = Carbon::parse($validated['end_date']);
        $userIds = $validated['user_ids'];

        // Limit to 90 days maximum
        if ($endDate->diffInDays($startDate) > 90) {
            return redirect()->route('timesheets.index')
                ->with('error', 'Date range cannot exceed 90 days.')
                ->withInput();
        }

        // Get the users
        $users = User::whereIn('id', $userIds)->orderBy('name')->get();

        // Use the centralized time calculation service
        $timeCalculationService = app(TimeCalculationService::class);
        $timesheetData = [];

        foreach ($users as $user) {
            $userSummary = $timeCalculationService->calculateUserTimeSummary($user, $startDate, $endDate, true);
            $timesheetData[] = $this->formatTimesheetData($userSummary);
        }

        return view('timesheets.print', compact('timesheetData', 'startDate', 'endDate', 'users'));
    }

    /**
     * Print the current user's timesheet for the specified date range.
     * This method is accessible to all authenticated users.
     */
    public function printMyTimesheet(Request $request)
    {
        $user = Auth::user();

        // Get date range from request or use defaults (current month)
        $startDate = $request->has('start_date')
            ? Carbon::parse($request->start_date)
            : Carbon::now()->startOfMonth();

        $endDate = $request->has('end_date')
            ? Carbon::parse($request->end_date)
            : Carbon::now()->endOfMonth();

        // Limit to 90 days maximum
        if ($endDate->diffInDays($startDate) > 90) {
            return redirect()->route('timecards.index')
                ->with('error', 'Date range cannot exceed 90 days.');
        }

        // Use the centralized time calculation service
        $timeCalculationService = app(TimeCalculationService::class);
        $userSummary = $timeCalculationService->calculateUserTimeSummary($user, $startDate, $endDate, true);
        $timesheetData = [$this->formatTimesheetData($userSummary)];

        // Create a collection with just the current user
        $users = collect([$user]);

        return view('timesheets.print', compact('timesheetData', 'startDate', 'endDate', 'users'));
    }
}
