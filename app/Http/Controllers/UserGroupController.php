<?php

namespace App\Http\Controllers;

use App\Models\UserGroup;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserGroupController extends Controller
{
    /**
     * Display a listing of the user groups.
     */
    public function index()
    {
        $userGroups = UserGroup::withCount(['users', 'permissions'])->get();
        return view('user_groups.index', compact('userGroups'));
    }

    /**
     * Show the form for creating a new user group.
     */
    public function create()
    {
        return view('user_groups.create');
    }

    /**
     * Store a newly created user group in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:user_groups,name',
            'description' => 'nullable|string|max:1000',
        ]);

        $userGroup = UserGroup::create($validated);

        return redirect()->route('user_groups.index')
            ->with('success', 'User group created successfully.');
    }

    /**
     * Display the specified user group.
     */
    public function show(UserGroup $userGroup)
    {
        $userGroup->load(['users', 'permissions']);

        // Group permissions by component
        $groupedPermissions = [];
        foreach ($userGroup->permissions as $permission) {
            $scope = $permission->scope;
            if (!isset($groupedPermissions[$scope])) {
                $groupedPermissions[$scope] = [];
            }
            $groupedPermissions[$scope][] = $permission;
        }

        // Sort the groups alphabetically
        ksort($groupedPermissions);

        return view('user_groups.show', compact('userGroup', 'groupedPermissions'));
    }

    /**
     * Show the form for editing the specified user group.
     */
    public function edit(UserGroup $userGroup)
    {
        // Get all permissions grouped by scope
        $allPermissions = Permission::all()->groupBy('scope');

        // Get the IDs of permissions already assigned to this group
        $assignedPermissionIds = $userGroup->permissions->pluck('id')->toArray();

        // Get all users for the members section
        $users = User::all();
        $groupMembers = $userGroup->users;

        return view('user_groups.edit', compact(
            'userGroup',
            'allPermissions',
            'assignedPermissionIds',
            'users',
            'groupMembers'
        ));
    }

    /**
     * Update the specified user group in storage.
     */
    public function update(Request $request, UserGroup $userGroup)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:user_groups,name,' . $userGroup->id,
            'description' => 'nullable|string|max:1000',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,id',
            'users' => 'nullable|array',
            'users.*' => 'exists:users,id',
        ]);

        // Check if this is the Admin group
        $isAdminGroup = $userGroup->name === 'Admin';

        // Update basic info
        $userGroup->update([
            'name' => $validated['name'],
            'description' => $validated['description'],
        ]);

        // Update permissions
        if (isset($validated['permissions'])) {
            // If this is the Admin group, make sure all permissions are assigned
            if ($isAdminGroup) {
                $allPermissionIds = Permission::pluck('id')->toArray();
                $userGroup->permissions()->sync($allPermissionIds);
            } else {
                $userGroup->permissions()->sync($validated['permissions']);
            }
        } elseif (!$isAdminGroup) {
            // Clear all permissions if none were selected (but not for Admin group)
            $userGroup->permissions()->sync([]);
        }

        // Update users
        if (isset($validated['users'])) {
            $userGroup->users()->sync($validated['users']);
        } else {
            $userGroup->users()->sync([]);
        }

        return redirect()->route('user_groups.show', $userGroup)
            ->with('success', 'User group updated successfully.');
    }

    /**
     * Remove the specified user group from storage.
     */
    public function destroy(UserGroup $userGroup)
    {
        // Don't allow deleting the Admin group
        if ($userGroup->name === 'Admin') {
            return redirect()->route('user_groups.index')
                ->with('error', 'The Admin group cannot be deleted.');
        }

        // Begin a transaction
        DB::beginTransaction();

        try {
            // Detach all users and permissions
            $userGroup->users()->detach();
            $userGroup->permissions()->detach();

            // Delete the group
            $userGroup->delete();

            DB::commit();

            return redirect()->route('user_groups.index')
                ->with('success', 'User group deleted successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->route('user_groups.index')
                ->with('error', 'Failed to delete user group: ' . $e->getMessage());
        }
    }
}
