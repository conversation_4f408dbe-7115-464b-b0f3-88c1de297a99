<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Models\TaskCompletion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class TaskCompletionController extends Controller
{
    public function updateStatus(Request $request, Task $task)
    {
        Log::info('Starting Task Completion Update', [
            'task_id' => $task->id,
            'request_data' => $request->all(),
            'user_id' => Auth::id(),
        ]);
    
        try {
            // Validate the input
            $validated = $request->validate([
                'status' => 'required|in:not_started,incomplete,under_review,complete',
            ]);
    
            Log::info('Validation Passed', ['validated_data' => $validated]);
    
            $userId = Auth::id();
    
            // Determine the due_time value
            $dueTime = $request->input('due_time') === 'Any time' ? null : $request->input('due_time');
    
            // Attempt to find or create the TaskCompletion record
            $completion = TaskCompletion::firstOrCreate(
                [
                    'task_id' => $task->id,
                    'user_id' => $userId,
                    'due_date' => $request->input('due_date'), // Pass due_date for the occurrence
                ],
                [
                    'due_time' => $dueTime, // Save as null if "Any time"
                ]
            );
    
            Log::info('TaskCompletion Record Found or Created', [
                'completion_id' => $completion->id,
                'task_id' => $completion->task_id,
                'user_id' => $completion->user_id,
                'due_date' => $completion->due_date,
                'due_time' => $completion->due_time,
                'existing_status' => $completion->status,
            ]);
    
            // Update the status
            $completion->status = $request->input('status');
            $completion->save();
    
            Log::info('TaskCompletion Status Updated', [
                'completion_id' => $completion->id,
                'updated_status' => $completion->status,
            ]);
    
            return response()->json(['success' => true, 'status' => $completion->status]);
        } catch (\Exception $e) {
            Log::error('Error Updating TaskCompletion Status', [
                'error_message' => $e->getMessage(),
                'task_id' => $task->id,
                'user_id' => Auth::id(),
                'due_date' => $request->input('due_date'),
                'due_time' => $request->input('due_time'),
            ]);
    
            return response()->json(['success' => false, 'error' => 'An error occurred while updating the task status.'], 500);
        }
    }
    
}
