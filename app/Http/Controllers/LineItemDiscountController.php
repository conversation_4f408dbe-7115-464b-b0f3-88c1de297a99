<?php

namespace App\Http\Controllers;

use App\Models\LineItemDiscount;
use App\Models\LineItem;
use App\Models\Discount;
use Illuminate\Http\Request;

class LineItemDiscountController extends Controller
{
    public function store(Request $request)
    {
        $validated = $request->validate([
            'line_item_id' => 'required|exists:line_items,id',
            'discount_id' => 'required|exists:discounts,id',
            'discount_amount' => 'required|numeric|min:0',
        ]);

        $lineItemDiscount = LineItemDiscount::create($validated);

        return response()->json(['success' => true, 'line_item_discount' => $lineItemDiscount]);
    }

    public function update(Request $request, LineItemDiscount $lineItemDiscount)
    {
        $validated = $request->validate([
            'discount_id' => 'required|exists:discounts,id',
            'discount_amount' => 'required|numeric|min:0',
        ]);

        $lineItemDiscount->update($validated);

        return response()->json(['success' => true, 'line_item_discount' => $lineItemDiscount]);
    }

    public function destroy(LineItemDiscount $lineItemDiscount)
    {
        $lineItemDiscount->delete();

        return response()->json(['success' => true]);
    }
}
