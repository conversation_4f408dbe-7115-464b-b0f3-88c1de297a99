<?php

namespace App\Http\Controllers;

use App\Models\Form;
use App\Models\FormField;
use App\Models\Discount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FormController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Form::with(['discount', 'submissions'])->withCount(['fields', 'submissions']);

        if ($search = $request->input('search')) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->has('requires_approval') && $request->input('requires_approval') !== '') {
            $query->where('requires_approval', $request->input('requires_approval'));
        }

        if ($request->has('requires_customer_link') && $request->input('requires_customer_link') !== '') {
            $query->where('requires_customer_link', $request->input('requires_customer_link'));
        }

        if ($request->has('is_active') && $request->input('is_active') !== '') {
            $query->where('is_active', $request->input('is_active'));
        }

        $forms = $query->latest()->paginate(10);

        return view('forms.index', compact('forms'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $discounts = Discount::where('is_customer_specific', true)->get();
        $userGroups = \App\Models\UserGroup::all();
        $users = \App\Models\User::where('enabled', true)->get();

        return view('forms.create', compact('discounts', 'userGroups', 'users'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'requires_signature' => 'boolean',
            'requires_customer_link' => 'boolean',
            'requires_approval' => 'boolean',
            'applies_discount_on_approval' => 'boolean',
            'generate_pdf_on_approval' => 'boolean',
            'required_contact_fields' => 'nullable|array',
            'required_contact_fields.name' => 'boolean',
            'required_contact_fields.email' => 'boolean',
            'required_contact_fields.phone' => 'boolean',
            'required_contact_fields.address' => 'boolean',
            'required_contact_fields.business_name' => 'boolean',
            'enable_email_notifications' => 'boolean',
            'notification_emails' => 'nullable|array',
            'notification_emails.*' => 'nullable|email',
            'include_full_data_in_email' => 'boolean',
            'discount_id' => 'nullable|exists:discounts,id',
            'agreement_duration_months' => 'nullable|integer|min:1',
            'notification_config' => 'nullable|array',
        ]);

        $form = Form::create($validated);

        // Handle notification configurations
        if (isset($validated['notification_config'])) {
            $notificationConfigService = app(\App\Services\NotificationConfigService::class);
            $notificationConfigService->saveConfigurationsFromFormData($form, $validated['notification_config']);
        }

        return redirect()->route('forms.builder', $form)->with('success', 'Form created successfully. Add fields to your form.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Form $form)
    {
        $form->load(['fields', 'submissions.customer']);
        return view('forms.show', compact('form'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Form $form)
    {
        $discounts = Discount::where('is_customer_specific', true)->get();
        $userGroups = \App\Models\UserGroup::all();
        $users = \App\Models\User::where('enabled', true)->get();

        // Load existing notification configurations
        $form->load('notificationConfigs');

        return view('forms.edit', compact('form', 'discounts', 'userGroups', 'users'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Form $form)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'requires_signature' => 'boolean',
            'requires_customer_link' => 'boolean',
            'requires_approval' => 'boolean',
            'applies_discount_on_approval' => 'boolean',
            'generate_pdf_on_approval' => 'boolean',
            'save_html_content' => 'boolean',
            'recaptcha_enabled' => 'boolean',
            'thankyou_show_submission_details' => 'boolean',
            'thankyou_show_html_message' => 'boolean',
            'thankyou_html_message' => 'nullable|string',
            'thankyou_show_need_help' => 'boolean',
            'thankyou_enable_print' => 'boolean',
            'thankyou_enable_close' => 'boolean',
            'required_contact_fields' => 'nullable|array',
            'required_contact_fields.name' => 'boolean',
            'required_contact_fields.email' => 'boolean',
            'required_contact_fields.phone' => 'boolean',
            'required_contact_fields.address' => 'boolean',
            'required_contact_fields.business_name' => 'boolean',
            'enable_email_notifications' => 'boolean',
            'notification_emails' => 'nullable|array',
            'notification_emails.*' => 'nullable|email',
            'include_full_data_in_email' => 'boolean',
            'discount_id' => 'nullable|exists:discounts,id',
            'agreement_duration_months' => 'nullable|integer|min:1',
            'notification_config' => 'nullable|array',
        ]);

        $form->update($validated);

        // Handle notification configurations
        if (isset($validated['notification_config'])) {
            $notificationConfigService = app(\App\Services\NotificationConfigService::class);
            $notificationConfigService->saveConfigurationsFromFormData($form, $validated['notification_config']);
        }

        return redirect()->route('forms.show', $form)->with('success', 'Form updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Form $form)
    {
        $form->delete();
        return redirect()->route('forms.index')->with('success', 'Form deleted successfully.');
    }

    /**
     * Show the form builder interface
     */
    public function builder(Form $form)
    {
        $form->load('fields');
        return view('forms.builder', compact('form'));
    }

    /**
     * Save form fields
     */
    public function saveFields(Request $request, Form $form)
    {
        try {
            // Simplified validation for debugging
            $request->validate([
                'fields' => 'required|array',
            ]);

            $fields = $request->input('fields', []);

            DB::transaction(function () use ($form, $fields) {
                $fieldIds = [];

                foreach ($fields as $fieldData) {
                    // Clean up the field data
                    $cleanFieldData = [
                        'form_id' => $form->id,
                        'type' => $fieldData['type'] ?? 'text',
                        'name' => $fieldData['name'] ?? null,
                        'label' => $fieldData['label'] ?? null,
                        'content' => $fieldData['content'] ?? null,
                        'placeholder' => $fieldData['placeholder'] ?? null,
                        'help_text' => $fieldData['help_text'] ?? null,
                        'required' => (bool) ($fieldData['required'] ?? false),
                        'options' => $fieldData['options'] ?? null,
                        'validation_rules' => $fieldData['validation_rules'] ?? null,
                        'order' => (int) ($fieldData['order'] ?? 0),
                    ];

                    // Handle existing vs new fields
                    if (isset($fieldData['id']) && $fieldData['id'] > 0) {
                        // Update existing field
                        $field = FormField::find($fieldData['id']);
                        if ($field) {
                            $field->update($cleanFieldData);
                            $fieldIds[] = $field->id;
                        }
                    } else {
                        // Create new field
                        $field = FormField::create($cleanFieldData);
                        $fieldIds[] = $field->id;
                    }
                }

                // Delete fields that are no longer in the form
                if (!empty($fieldIds)) {
                    $form->fields()->whereNotIn('id', $fieldIds)->delete();
                }
            });

            return response()->json([
                'success' => true, 
                'message' => 'Form fields saved successfully.',
                'field_count' => count($fields)
            ]);

        } catch (\Exception $e) {
            \Log::error('Form field save error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Error saving fields: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Preview the form
     */
    public function preview(Form $form)
    {
        $form->load('fields');
        return view('forms.preview', compact('form'));
    }

    /**
     * Clone a form
     */
    public function clone(Form $form)
    {
        DB::transaction(function () use ($form, &$newForm) {
            $newForm = $form->replicate();
            $newForm->name = $form->name . ' (Copy)';
            $newForm->slug = null; // Will be generated automatically
            $newForm->save();

            foreach ($form->fields as $field) {
                $newField = $field->replicate();
                $newField->form_id = $newForm->id;
                $newField->save();
            }
        });

        return redirect()->route('forms.builder', $newForm)->with('success', 'Form cloned successfully.');
    }
}
