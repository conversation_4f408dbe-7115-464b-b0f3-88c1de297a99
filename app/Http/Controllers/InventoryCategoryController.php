<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\InventoryCategory;
use App\Models\Inventory;
use App\Models\User;
use App\Models\TaxPolicy;
use App\Models\Department;



class InventoryCategoryController extends Controller
{
    //

    public function index(Request $request)
    {
        if ($request->has('trashed') && $request->input('trashed') == 'true') {
            $categories = InventoryCategory::onlyTrashed()->get();
        } else {
            $categories = InventoryCategory::whereNull('deleted_at')->get();
        }

        return view('inventory_categories.index', compact('categories'));
    }

    public function create()
    {
        $departments = Department::all();
        $tax_policies = TaxPolicy::all();

        return view('inventory_categories.create', compact('departments', 'tax_policies'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required',
            'code' => 'required',
            'quantity_type' => 'required',
            'tax_policy_id' => 'required',
            'department_id' => 'required',
            'ai_promptable' => 'nullable|boolean',
        ]);

        InventoryCategory::create($request->all());

        return redirect()->route('inventory_categories.index')
            ->with('success', 'Category created successfully.');
    }

    public function edit(InventoryCategory $inventoryCategory)
    {
        $departments = Department::all();
        $tax_policies = TaxPolicy::all();

        return view('inventory_categories.edit', compact('inventoryCategory', 'departments', 'tax_policies'));
    }
    

    public function update(Request $request, $id)
    {
        try {
            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'tax_policy_id' => 'required|integer',
                'department_id' => 'required|integer',
                'ai_promptable' => 'nullable|boolean',
            ]);
    
            $category = InventoryCategory::findOrFail($id);
            $category->update($validatedData);
    
            return response()->json(['success' => true]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    

    public function destroy(InventoryCategory $inventoryCategory)
    {
        $inventoryCategory->delete();

        return redirect()->route('inventory_categories.index')
            ->with('success', 'Category moved to trash successfully!');
    }

    public function trash()
    {
        // Fetch trashed categories using SoftDeletes
        $categories = InventoryCategory::onlyTrashed()->get();

        return view('inventory_categories.index', compact('categories'));
    }

    public function restore($id)
    {
        // Find the trashed category by ID
        $category = InventoryCategory::onlyTrashed()->findOrFail($id);

        // Restore the category
        $category->restore();

        return redirect()->route('inventory_categories.index')->with('success', 'Category restored successfully!');
    }


    public function getDefaultTaxPolicy($id)
    {
        $taxPolicy = InventoryCategory::findOrFail($id)->taxPolicy;

        return response()->json($taxPolicy);
    }


    public function getJson($id)
    {
        $category = InventoryCategory::with(['defaultTaxPolicy', 'listedItems'])->findOrFail($id);
    
        return response()->json([
            'id' => $category->id,
            'name' => $category->name,
            'default_tax_policy' => $category->defaultTaxPolicy,
            'listed_items' => $category->listedItems,
            'quantity_type' => $category->quantity_type,
            'unit' => $category->unit,
        ]);
    }
    
    
    

    

}
