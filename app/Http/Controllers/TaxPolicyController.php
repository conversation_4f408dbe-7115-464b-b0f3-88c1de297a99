<?php

namespace App\Http\Controllers;

use App\Models\TaxPolicy;
use Illuminate\Http\Request;

class TaxPolicyController extends Controller
{
    // Show all tax policies
    public function index()
    {
        $taxPolicies = TaxPolicy::all();
        return view('tax_policies.index', compact('taxPolicies'));
    }

    // Show create form
    public function create()
    {
        return view('tax_policies.create');
    }

    // Store a new tax policy
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'rate' => 'required|numeric|min:0|max:100',
            'description' => 'nullable|string',
        ]);

        TaxPolicy::create($validated);
        return redirect()->route('tax_policies.index')->with('success', 'Tax policy created successfully.');
    }

    // Show edit form
    public function edit(TaxPolicy $taxPolicy)
    {
        return view('tax_policies.edit', compact('taxPolicy'));
    }

    // Update a tax policy
    public function update(Request $request, TaxPolicy $taxPolicy)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);
    
        $taxPolicy->update($validated);
    
        return redirect()->route('tax_policies.index')->with('success', 'Tax policy updated successfully.');
    }
    

    // Delete a tax policy
    public function destroy(TaxPolicy $taxPolicy)
    {
        $taxPolicy->delete();
        return redirect()->route('tax_policies.index')->with('success', 'Tax policy deleted successfully.');
    }
}
