<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Department;
use App\Models\GlobalConfig;
use App\Models\Inventory;
use App\Models\InventoryCategory;
use App\Models\Invoice;
use App\Models\LineItem;
use App\Models\TaxPolicy;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\FileController;

class PayoutController extends Controller
{
    /**
     * Show the form for creating a new payout.
     */
    public function create()
    {
        $customers = Customer::all();
        $departments = Department::all();
        $categories = InventoryCategory::all();

        // Get payout products (now just an array of IDs)
        $payout_product_ids = GlobalConfig::decodeValue('payout_products') ?? [];
        $payout_products = Inventory::whereIn('id', $payout_product_ids)->get();

        // Get default tax policy for payouts
        $defaultTaxPolicy = TaxPolicy::where('name', 'Non-Taxable')->first() ?? TaxPolicy::first();

        return view('payouts.create', compact('customers', 'departments', 'categories', 'payout_products', 'defaultTaxPolicy'));
    }

    /**
     * Store a newly created payout invoice in storage.
     */
    public function store(Request $request)
    {
        // Check if customer has existing photo ID on file
        $customer = Customer::find($request->customer_id);
        $hasExistingId = $customer && $customer->hasPhotoIdOnFile();

        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'product_type' => 'required|string',
            'custom_product_name' => 'required_if:product_type,custom|nullable|string|max:255',
            'custom_product_category_id' => 'required_if:product_type,custom|nullable|exists:inventory_categories,id',
            'payout_amount' => 'required|numeric|min:0.01',
            'quantity' => 'required|integer|min:1',
            'department_id' => 'required|exists:departments,id',
            'description' => 'nullable|string|max:255',
            'tax_policy_id' => 'required|exists:tax_policies,id',
            'photo_id' => $hasExistingId ? 'nullable|file|mimes:jpg,jpeg,png,pdf|max:10240' : 'required|file|mimes:jpg,jpeg,png,pdf|max:10240',
            'item_photos.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:10240',
        ]);

        DB::beginTransaction();
        try {
            // Get the customer for file uploads
            $customer = Customer::findOrFail($validated['customer_id']);

            // Create the invoice
            $invoice = Invoice::create([
                'customer_id' => $validated['customer_id'],
                'invoice_date' => now(),
                'status' => 'Paid', // Payouts are marked as paid immediately
                'payment_method' => 'Cash', // Default payment method for payouts
                'notes' => 'This is a payout invoice created from the simplified payout form.',
            ]);

            // Determine which product to use
            if ($validated['product_type'] === 'custom') {
                // Create a custom inventory item for this payout
                $inventory = new Inventory();
                $inventory->name = $validated['custom_product_name'];
                $inventory->inv_category_id = $validated['custom_product_category_id'];
                $inventory->status = 'forsale';
                $inventory->quantity = 1; // Just create one
                $inventory->unit_price = 0; // No price as this is for payout
                $inventory->suggested_price = 0;
                $inventory->save();

                // Generate asset tag if needed
                if (empty($inventory->asset_tag)) {
                    $categoryCode = InventoryCategory::find($validated['custom_product_category_id'])->code ?? 'CUSTOM';
                    $inventory->asset_tag = $categoryCode . str_pad($inventory->id, 4, '0', STR_PAD_LEFT);
                    $inventory->save();
                }

                $itemId = $inventory->id;
                $itemName = $validated['custom_product_name'];
            } else {
                // Use inventory item directly since product_type is the inventory ID
                $inventory = Inventory::findOrFail($validated['product_type']);
                $itemId = $inventory->id;
                $itemName = $inventory->name;

                // No special pricing handling - just use the user-entered amount
                $payoutAmount = $validated['payout_amount'];
            }

            // Create the line item with negative price (payout)
            $price = -$validated['payout_amount']; // Negative price for payout

            // Calculate the tax (likely 0 for payouts, but using the tax policy to be consistent)
            $taxPolicy = TaxPolicy::findOrFail($validated['tax_policy_id']);
            $taxRate = $taxPolicy->rate ?? 0;

            // Calculate line item gross revenue (price * quantity)
            $lineItemTotal = $validated['quantity'] * $price;

            // Calculate tax amount (most payouts are non-taxable, so this will likely be 0)
            $lineItemTax = round($lineItemTotal * ($taxRate / 100), 2);

            // Calculate subtotal (gross revenue + tax)
            $subtotal = $lineItemTotal + $lineItemTax;

            $lineItem = new LineItem([
                'item_id' => $itemId,
                'quantity' => $validated['quantity'],
                'item_name' => $itemName,
                'price' => $price,
                'subtotal' => $subtotal,
                'tax' => $lineItemTax,
                'description' => 'PAYOUT: ' . ($validated['description'] ?? ''),
                'tax_policy_id' => $validated['tax_policy_id'],
                'department_id' => $validated['department_id'],
            ]);

            $invoice->lineItems()->save($lineItem);

            // Recalculate invoice totals
            $invoice->calculateTotals();
            $invoice->save();

            // Use the FileController for file uploads with image resizing
            $fileController = new FileController(app(\App\Services\FileService::class));
            $maxImageWidth = 1400; // Set maximum width for payout images to 1400px

            // Upload ID photo to customer (not invoice) with resizing
            if ($request->hasFile('photo_id')) {
                // Create a new request that properly includes the file
                $photoIdRequest = new Request();
                $photoIdRequest->merge([
                    'description' => 'Customer ID uploaded during payout on ' . now()->format('M j, Y'),
                    'file_type' => 'photo_id'
                ]);

                // Add the actual file to the request
                $photoIdRequest->files->set('file', $request->file('photo_id'));

                // Upload the file to the customer (not the invoice) using the FileController with image resizing
                $fileController->uploadForCustomer($photoIdRequest, $customer, $maxImageWidth);
            }

            // Upload item photos with resizing
            if ($request->hasFile('item_photos')) {
                foreach ($request->file('item_photos') as $index => $photo) {
                    // Create a new request for each item photo
                    $itemPhotoRequest = new Request();
                    $itemPhotoRequest->merge([
                        'description' => 'Item photo for payout',
                        'file_type' => 'item_photo'
                    ]);

                    // Add the actual file to the request
                    $itemPhotoRequest->files->set('file', $photo);

                    // Upload the file using the FileController with image resizing
                    $fileController->uploadForInvoice($itemPhotoRequest, $invoice, $maxImageWidth);
                }
            }

            DB::commit();

            return redirect()->route('invoices.show', $invoice->id)
                ->with('success', 'Payout created successfully and recorded as Invoice #' . $invoice->id);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payout creation failed: ' . $e->getMessage());

            return redirect()->back()
                ->withInput()
                ->withErrors('Failed to create payout: ' . $e->getMessage());
        }
    }
}