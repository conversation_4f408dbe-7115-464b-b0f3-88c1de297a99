<?php

namespace App\Http\Controllers;

use App\Models\Documentation;
use Illuminate\Http\Request;

class DocumentationController extends Controller
{
    public function index()
    {
        $documentation = Documentation::all();
        return view('documentation.index', compact('documentation'));
    }

    public function create()
    {
        return view('documentation.create');
    }

    public function edit(Documentation $documentation)
    {
        return view('documentation.edit', compact('documentation'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string|min:1', // Ensure content is not empty
            'short_desc' => 'nullable|string|max:1000',
        ]);
    
        // Save to the database
        $documentation = Documentation::create($validated);
    
        // Redirect to the documentation.show route with the created documentation
        return redirect()->route('documentation.show', ['documentation' => $documentation->id])
            ->with('success', 'Documentation created successfully!');
    }
    

    public function update(Request $request, Documentation $documentation)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string|min:1', // Ensure content is not empty
            'short_desc' => 'nullable|string|max:1000',
        ]);
    
        // Save to database or perform any other logic
        $documentation->update($validated);
    
        return redirect()->route('documentation.show', compact('documentation'))->with('success', 'Documentation updated successfully!');
    }

    //softdelete
    public function destroy(Documentation $documentation)
    {
        $documentation->delete();
        return redirect()->route('documentation.index')->with('success', 'Documentation deleted successfully!');
    }
    
    //show
    public function show(Documentation $documentation)
    {
        return view('documentation.show', compact('documentation'));
    }
    
}
