<?php

namespace App\Http\Controllers;

use App\Models\PickupRequest;
use App\Models\GlobalConfig;
use App\Models\UserGroup;
use App\Models\Image;
use App\Helpers\NotificationHelper;
use App\Mail\TemplatedMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class AppointmentManagementController extends Controller
{
    /**
     * Show the appointment management page for customers.
     */
    public function show(Request $request, $token)
    {
        // Find the pickup request by valid management token
        $pickupRequest = PickupRequest::findByValidToken($token);

        if (!$pickupRequest) {
            // Check if token exists but is expired
            $expiredRequest = PickupRequest::where('management_token', $token)->first();
            if ($expiredRequest && $expiredRequest->isManagementTokenExpired()) {
                abort(410, 'This appointment management link has expired. Please contact us for assistance.');
            }

            abort(404, 'Appointment not found or invalid link.');
        }

        // Load related data
        $pickupRequest->load(['customer', 'event.assignedDriver', 'images']);

        // No auto-prompt needed since email confirmation links go directly to confirmation
        $showConfirmPrompt = false;

        // Get contact information from global config
        $contactEmail = GlobalConfig::getValue('site_email');
        $contactPhone = GlobalConfig::getValue('site_phone');
        $websiteLink = GlobalConfig::getValue('site_website');

        return view('appointment-management.show', compact('pickupRequest', 'token', 'showConfirmPrompt', 'contactEmail', 'contactPhone', 'websiteLink'));
    }

    /**
     * Handle appointment management actions.
     */
    public function update(Request $request, $token)
    {
        // Find the pickup request by valid management token
        $pickupRequest = PickupRequest::findByValidToken($token);

        if (!$pickupRequest) {
            // Check if token exists but is expired
            $expiredRequest = PickupRequest::where('management_token', $token)->first();
            if ($expiredRequest && $expiredRequest->isManagementTokenExpired()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This appointment management link has expired. Please contact us for assistance.'
                ], 410);
            }

            return response()->json([
                'success' => false,
                'message' => 'Appointment not found or invalid link.'
            ], 404);
        }

        $action = $request->input('action');

        switch ($action) {
            case 'confirm':
                return $this->confirmAppointment($pickupRequest);
            case 'cancel':
                return $this->cancelAppointment($pickupRequest);
            default:
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid action specified.'
                ], 400);
        }
    }

    /**
     * Confirm the appointment.
     */
    protected function confirmAppointment(PickupRequest $pickupRequest)
    {
        try {
            if (!$pickupRequest->canCustomerConfirm()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This appointment cannot be confirmed at this time. The confirmation window is 48 hours before the scheduled pickup.'
                ], 400);
            }

            // Use the existing confirmByCustomer method which updates pickup_details
            $pickupRequest->confirmByCustomer();
            
            // Also update the new confirmed_at field
            $pickupRequest->confirmed_at = now();
            $pickupRequest->save();

            // Log the customer confirmation
            $pickupRequest->logCustomerConfirmation();

            // Send notification to configured user groups
            $userGroupIds = GlobalConfig::getPickupNotificationUserGroups();
            if (!empty($userGroupIds)) {
                $userGroups = UserGroup::whereIn('id', $userGroupIds)->get();
                if ($userGroups->isNotEmpty()) {
                    $timezone = GlobalConfig::getTimeZone();
                    $pickupDate = $pickupRequest->event && $pickupRequest->event->start_date 
                        ? $pickupRequest->event->start_date->setTimezone($timezone)->format('M j, Y \a\t g:i A')
                        : $pickupRequest->preferred_pickup_date->setTimezone($timezone)->format('M j, Y \a\t g:i A');
                    
                    NotificationHelper::notifyUserGroups(
                        $userGroups->toArray(),
                        'Pickup Appointment Confirmed',
                        "Customer {$pickupRequest->contact_name} has confirmed their pickup appointment for {$pickupDate}. Pickup Request #{$pickupRequest->id}.",
                        'normal',
                        route('pickup-requests.show', $pickupRequest),
                        'View Pickup Request'
                    );
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Your appointment has been confirmed successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while confirming your appointment. Please contact us for assistance.'
            ], 500);
        }
    }

    /**
     * Cancel the appointment.
     */
    protected function cancelAppointment(PickupRequest $pickupRequest)
    {
        try {
            if (!$pickupRequest->canBeCancelled()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This appointment cannot be cancelled.'
                ], 400);
            }

            // Get the cancellation reason from the request
            $reason = request()->input('reason', 'Customer initiated cancellation through online portal.');

            // Delete the associated calendar event if it exists
            if ($pickupRequest->event) {
                $pickupRequest->event->delete();
                $pickupRequest->event_id = null;
            }

            $pickupRequest->status = 'cancelled';
            
            // Record cancellation details in both JSON and new fields
            $currentDetails = $pickupRequest->pickup_details ?? [];
            if (!isset($currentDetails['cancellation'])) {
                $currentDetails['cancellation'] = [];
            }
            
            $currentDetails['cancellation'] = [
                'cancelled_at' => now()->toISOString(),
                'cancelled_by' => 'customer',
                'cancellation_method' => 'customer_portal',
                'status' => 'cancelled',
                'reason' => $reason
            ];
            
            $pickupRequest->pickup_details = $currentDetails;
            $pickupRequest->cancelled_at = now();
            $pickupRequest->cancellation_reason = $reason;
            $pickupRequest->save();

            // Log the cancellation
            $pickupRequest->logCancellation('customer', 'customer_portal', $reason);

            // Send cancellation email to customer
            if ($pickupRequest->email) {
                try {
                    Mail::to($pickupRequest->email)->queue(new TemplatedMail('pickup_cancellation', [
                        'pickup_request' => $pickupRequest,
                        'cancellation_reason' => $reason,
                        'cancelled_by' => 'Customer'
                    ]));
                } catch (\Exception $e) {
                    \Log::error('Failed to send pickup cancellation email', [
                        'pickup_request_id' => $pickupRequest->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Send notification to configured user groups
            $userGroupIds = GlobalConfig::getPickupNotificationUserGroups();
            if (!empty($userGroupIds)) {
                $userGroups = UserGroup::whereIn('id', $userGroupIds)->get();
                if ($userGroups->isNotEmpty()) {
                    $timezone = GlobalConfig::getTimeZone();
                    $pickupDate = $pickupRequest->event && $pickupRequest->event->start_date 
                        ? $pickupRequest->event->start_date->setTimezone($timezone)->format('M j, Y \a\t g:i A')
                        : $pickupRequest->preferred_pickup_date->setTimezone($timezone)->format('M j, Y \a\t g:i A');
                    
                    NotificationHelper::notifyUserGroups(
                        $userGroups->toArray(),
                        'Pickup Appointment Cancelled',
                        "Customer {$pickupRequest->contact_name} has cancelled their pickup appointment scheduled for {$pickupDate}. Pickup Request #{$pickupRequest->id}.",
                        'high',
                        route('pickup-requests.show', $pickupRequest),
                        'View Pickup Request'
                    );
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Your appointment has been cancelled successfully.'
            ]);

        } catch (\Exception $e) {
            \Log::error('Error cancelling appointment', [
                'pickup_request_id' => $pickupRequest->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while cancelling your appointment. Please contact us for assistance.'
            ], 500);
        }
    }

    /**
     * Handle direct confirmation from email links.
     */
    public function directConfirm($token)
    {
        // Find the pickup request by valid management token
        $pickupRequest = PickupRequest::findByValidToken($token);

        if (!$pickupRequest) {
            // Check if token exists but is expired
            $expiredRequest = PickupRequest::where('management_token', $token)->first();
            if ($expiredRequest && $expiredRequest->isManagementTokenExpired()) {
                abort(410, 'This appointment management link has expired. Please contact us for assistance.');
            }

            abort(404, 'Appointment not found or invalid link.');
        }

        // Check if the appointment can be confirmed
        if (!$pickupRequest->canCustomerConfirm()) {
            return redirect()->route('pickup.manage', $token)
                ->with('error', 'This appointment cannot be confirmed at this time. The confirmation window is 48 hours before the scheduled pickup.');
        }

        try {
            // Use the existing confirmByCustomer method which updates pickup_details
            $pickupRequest->confirmByCustomer();
            
            // Also update the new confirmed_at field
            $pickupRequest->confirmed_at = now();
            $pickupRequest->save();

            // Log the customer confirmation
            $pickupRequest->logCustomerConfirmation();

            // Send notification to configured user groups
            $userGroupIds = GlobalConfig::getPickupNotificationUserGroups();
            if (!empty($userGroupIds)) {
                $userGroups = UserGroup::whereIn('id', $userGroupIds)->get();
                if ($userGroups->isNotEmpty()) {
                    $timezone = GlobalConfig::getTimeZone();
                    $pickupDate = $pickupRequest->event && $pickupRequest->event->start_date 
                        ? $pickupRequest->event->start_date->setTimezone($timezone)->format('M j, Y \a\t g:i A')
                        : $pickupRequest->preferred_pickup_date->setTimezone($timezone)->format('M j, Y \a\t g:i A');
                    
                    NotificationHelper::notifyUserGroups(
                        $userGroups->toArray(),
                        'Pickup Appointment Confirmed',
                        "Customer {$pickupRequest->contact_name} has confirmed their pickup appointment for {$pickupDate}. Pickup Request #{$pickupRequest->id}.",
                        'normal',
                        route('pickup-requests.show', $pickupRequest),
                        'View Pickup Request'
                    );
                }
            }

            // Redirect to success page
            return redirect()->route('pickup.confirm.success', $token);

        } catch (\Exception $e) {
            return redirect()->route('pickup.manage', $token)
                ->with('error', 'An error occurred while confirming your appointment. Please contact us for assistance.');
        }
    }

    /**
     * Show confirmation success page.
     */
    public function confirmSuccess($token)
    {
        // Find the pickup request by valid management token
        $pickupRequest = PickupRequest::findByValidToken($token);

        if (!$pickupRequest) {
            abort(404, 'Appointment not found or invalid link.');
        }

        // Load related data
        $pickupRequest->load(['customer', 'event.assignedDriver']);

        // Get contact information from global config
        $contactEmail = GlobalConfig::getValue('site_email');
        $contactPhone = GlobalConfig::getValue('site_phone');
        $websiteLink = GlobalConfig::getValue('site_website');

        return view('appointment-management.confirm-success', compact('pickupRequest', 'token', 'contactEmail', 'contactPhone', 'websiteLink'));
    }

    /**
     * Show the edit form for the appointment.
     */
    public function edit(Request $request, $token)
    {
        // Find the pickup request by valid management token
        $pickupRequest = PickupRequest::findByValidToken($token);

        if (!$pickupRequest) {
            // Check if token exists but is expired
            $expiredRequest = PickupRequest::where('management_token', $token)->first();
            if ($expiredRequest && $expiredRequest->isManagementTokenExpired()) {
                abort(410, 'This appointment management link has expired. Please contact us for assistance.');
            }

            abort(404, 'Appointment not found or invalid link.');
        }

        // Load related data
        $pickupRequest->load(['customer', 'event.assignedDriver']);

        // Get pickup item types
        $pickupItemTypes = GlobalConfig::getPickupItemTypes();
        if (empty($pickupItemTypes)) {
            $pickupItemTypes = GlobalConfig::getDefaultPickupItemTypes();
        }

        return view('appointment-management.edit', compact('pickupRequest', 'token', 'pickupItemTypes'));
    }

    /**
     * Update the appointment details.
     */
    public function updateDetails(Request $request, $token)
    {
        // Find the pickup request by valid management token
        $pickupRequest = PickupRequest::findByValidToken($token);

        if (!$pickupRequest) {
            // Check if token exists but is expired
            $expiredRequest = PickupRequest::where('management_token', $token)->first();
            if ($expiredRequest && $expiredRequest->isManagementTokenExpired()) {
                return redirect()->back()->with('error', 'This appointment management link has expired. Please contact us for assistance.');
            }

            abort(404, 'Appointment not found or invalid link.');
        }

        // Validate the input
        $validator = Validator::make($request->all(), [
            'contact_name' => 'required|string|max:255',
            'business_name' => 'nullable|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'pickup_address' => 'required|string',
            'property_location_details' => 'required|string',
            'other_notes' => 'nullable|string',
            'load_size' => 'required|in:small,medium,large',
            'item_types' => 'required|array|min:1',
            'item_types.*' => 'string',
            'item_specifics' => 'required|string',
            'accessibility_level' => 'required|in:easy,moderate,difficult',
            'driver_instructions' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Update the pickup request
            $pickupRequest->update([
                'contact_name' => $request->contact_name,
                'business_name' => $request->business_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'pickup_address' => $request->pickup_address,
                'property_location_details' => $request->property_location_details,
                'other_notes' => $request->other_notes,
                'load_size' => $request->load_size,
                'item_types' => $request->item_types,
                'item_specifics' => $request->item_specifics,
                'accessibility_level' => $request->accessibility_level,
                'driver_instructions' => $request->driver_instructions,
            ]);

            // Update the related calendar event if it exists
            if ($pickupRequest->event) {
                $pickupRequest->event->update([
                    'title' => "Pickup: {$pickupRequest->contact_name}",
                    'description' => $this->generateEventDescription($pickupRequest),
                ]);
            }

            // Send notification to configured user groups about the update
            $userGroupIds = GlobalConfig::getPickupNotificationUserGroups();
            if (!empty($userGroupIds)) {
                $userGroups = UserGroup::whereIn('id', $userGroupIds)->get();
                if ($userGroups->isNotEmpty()) {
                    $timezone = GlobalConfig::getTimeZone();
                    $pickupDate = $pickupRequest->event && $pickupRequest->event->start_date 
                        ? $pickupRequest->event->start_date->setTimezone($timezone)->format('M j, Y \a\t g:i A')
                        : $pickupRequest->preferred_pickup_date->setTimezone($timezone)->format('M j, Y \a\t g:i A');
                    
                    NotificationHelper::notifyUserGroups(
                        $userGroups->toArray(),
                        'Pickup Appointment Updated',
                        "Customer {$pickupRequest->contact_name} has updated their pickup appointment details for {$pickupDate}. Pickup Request #{$pickupRequest->id}.",
                        'normal',
                        route('pickup-requests.show', $pickupRequest),
                        'View Pickup Request'
                    );
                }
            }

            return redirect()->route('pickup.manage', $token)
                ->with('success', 'Your appointment details have been updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'An error occurred while updating your appointment. Please try again or contact us for assistance.')
                ->withInput();
        }
    }

    /**
     * Generate event description from pickup request data.
     */
    private function generateEventDescription(PickupRequest $pickupRequest)
    {
        $description = "Pickup Request #{$pickupRequest->id}\n";
        $description .= "Contact: {$pickupRequest->contact_name}\n";
        
        if ($pickupRequest->business_name) {
            $description .= "Business: {$pickupRequest->business_name}\n";
        }
        
        $description .= "Email: {$pickupRequest->email}\n";
        $description .= "Phone: {$pickupRequest->phone}\n";
        $description .= "Address: {$pickupRequest->pickup_address}\n";
        
        if ($pickupRequest->load_size) {
            $description .= "Load Size: " . ucfirst(str_replace('_', ' ', $pickupRequest->load_size)) . "\n";
        }
        
        if ($pickupRequest->item_types) {
            $itemTypes = is_array($pickupRequest->item_types) 
                ? implode(', ', array_map(fn($type) => str_replace('_', ' ', $type), $pickupRequest->item_types))
                : $pickupRequest->item_types;
            $description .= "Items: {$itemTypes}\n";
        }
        
        if ($pickupRequest->item_specifics) {
            $description .= "Item Specifics: {$pickupRequest->item_specifics}\n";
        }
        
        if ($pickupRequest->accessibility_level) {
            $description .= "Accessibility: " . ucfirst(str_replace('_', ' ', $pickupRequest->accessibility_level)) . "\n";
        }
        
        if ($pickupRequest->property_location_details) {
            $description .= "Property Location: {$pickupRequest->property_location_details}\n";
        }
        
        if ($pickupRequest->driver_instructions) {
            $description .= "Driver Instructions: {$pickupRequest->driver_instructions}\n";
        }
        
        if ($pickupRequest->other_notes) {
            $description .= "Other Notes: {$pickupRequest->other_notes}\n";
        }
        
        return $description;
    }

    /**
     * Upload image for appointment management.
     */
    public function uploadImage(Request $request, $token)
    {
        // Find the pickup request by valid management token
        $pickupRequest = PickupRequest::findByValidToken($token);

        if (!$pickupRequest) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired appointment link.'
            ], 404);
        }

        try {
            $request->validate([
                'file' => 'required|image|mimes:jpeg,jpg,png,webp|max:10240', // 10MB max
            ]);

            // Check if pickup request already has 10 images
            if ($pickupRequest->images()->count() >= 10) {
                return response()->json([
                    'success' => false,
                    'message' => 'Maximum of 10 images allowed per pickup request.'
                ], 400);
            }

            $file = $request->file('file');
            $fileName = 'pickup_' . $pickupRequest->id . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
            
            // Store the image
            $path = $file->storeAs('pickup-images', $fileName, 'public');

            // Create image record
            $image = Image::create([
                'image_path' => $path,
                'og_filename' => $file->getClientOriginalName(),
                'title' => 'Pickup Request #' . $pickupRequest->id . ' - Customer Upload',
                'description' => 'Image uploaded by customer for pickup request',
                'alt_text' => 'Pickup request item image',
                'uploaded_by' => null, // Guest upload
                'context_type' => 'pickup_request',
                'context_id' => $pickupRequest->id,
            ]);

            // Attach image to pickup request
            $pickupRequest->images()->attach($image->id, [
                'sort_order' => $pickupRequest->images()->count() + 1
            ]);

            return response()->json([
                'success' => true,
                'image' => [
                    'id' => $image->id,
                    'image_path' => $image->image_path,
                    'og_filename' => $image->og_filename,
                    'title' => $image->title,
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                ],
                'thumbnail_url' => Storage::disk('public')->url($image->image_path),
                'full_url' => Storage::disk('public')->url($image->image_path),
                'message' => 'Image uploaded successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error uploading appointment image', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'pickup_request_id' => $pickupRequest->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while uploading the image. Please try again.'
            ], 500);
        }
    }

    /**
     * Delete image for appointment management.
     */
    public function deleteImage(Request $request, $token, $imageId)
    {
        // Find the pickup request by valid management token
        $pickupRequest = PickupRequest::findByValidToken($token);

        if (!$pickupRequest) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired appointment link.'
            ], 404);
        }

        try {
            // Find the image and verify it belongs to this pickup request
            $image = $pickupRequest->images()->where('images.id', $imageId)->first();

            if (!$image) {
                return response()->json([
                    'success' => false,
                    'message' => 'Image not found or does not belong to this pickup request.'
                ], 404);
            }

            // Delete the physical file
            if (Storage::disk('public')->exists($image->image_path)) {
                Storage::disk('public')->delete($image->image_path);
            }

            // Delete thumbnail if it exists
            if ($image->thumbnail_path && Storage::disk('public')->exists($image->thumbnail_path)) {
                Storage::disk('public')->delete($image->thumbnail_path);
            }

            // Detach from pickup request
            $pickupRequest->images()->detach($image->id);

            // Delete the image record
            $image->delete();

            return response()->json([
                'success' => true,
                'message' => 'Image deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting appointment image', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'pickup_request_id' => $pickupRequest->id,
                'image_id' => $imageId
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting the image. Please try again.'
            ], 500);
        }
    }
}
