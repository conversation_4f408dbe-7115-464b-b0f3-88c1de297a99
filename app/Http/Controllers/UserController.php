<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use App\Models\UserGroup;

class UserController extends Controller
{
    public function index(Request $request)
    {
        // Define default sorting and pagination
        $sort = $request->query('sort', 'name');
        $order = $request->query('order', 'asc');
        $pagination = $request->query('pagination', 10);

        // Define valid sorting columns
        $validColumns = ['name', 'email', 'enabled', 'created_at'];

        // Query users with user groups
        $query = User::with('groups');

        // Apply sorting
        if (in_array($sort, $validColumns)) {
            $query->orderBy($sort, $order);
        }

        // Paginate users
        $users = $query->paginate($pagination);

        return view('admin.users.index', compact('users', 'sort', 'order', 'pagination'));
    }

    public function create()
    {
        $userGroups = UserGroup::all(); // Fetch all available user groups
        return view('admin.users.create', compact('userGroups'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email',
            'password' => 'required|min:8|confirmed',
            'enabled' => 'required|boolean',
            'groups' => 'array',
        ]);

        // Create the user
        $user = User::create([
            'name' => $validatedData['name'],
            'email' => $validatedData['email'],
            'password' => bcrypt($validatedData['password']),
            'enabled' => $validatedData['enabled'],
        ]);

        // Sync user groups
        if (isset($validatedData['groups'])) {
            $user->groups()->sync($validatedData['groups']);
        }

        return redirect()->route('admin.users.index')->with('success', 'User created successfully.');
    }

    public function edit(User $user)
    {
        $userGroups = UserGroup::all(); // Fetch all available user groups
        return view('admin.users.edit', compact('user', 'userGroups'));
    }

    public function update(Request $request, User $user)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $user->id,
            'enabled' => 'required|boolean',
            'password' => 'nullable|min:8|confirmed',
            'groups' => 'array',
        ]);

        // Update basic user details
        $user->update([
            'name' => $validatedData['name'],
            'email' => $validatedData['email'],
            'enabled' => $validatedData['enabled'],
        ]);

        // Update password if provided
        if (!empty($validatedData['password'])) {
            $user->update([
                'password' => bcrypt($validatedData['password']),
            ]);
        }

        // Sync user groups
        $user->groups()->sync($validatedData['groups'] ?? []);

        return redirect()->route('admin.users.index')->with('success', 'User updated successfully.');
    }

    /**
     * Display the specified user.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function show(User $user)
    {
        // Load the user with their groups
        $user->load('groups');

        return view('admin.users.show', compact('user'));
    }
}
