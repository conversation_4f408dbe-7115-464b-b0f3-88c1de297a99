<?php

namespace App\Http\Controllers;

use App\Models\InvoiceDiscount;
use App\Models\Invoice;
use App\Models\Discount;
use Illuminate\Http\Request;

class InvoiceDiscountController extends Controller
{
    public function store(Request $request)
    {
        $validated = $request->validate([
            'invoice_id' => 'required|exists:invoices,id',
            'discount_id' => 'required|exists:discounts,id',
            'discount_amount' => 'required|numeric|min:0',
        ]);

        $invoiceDiscount = InvoiceDiscount::create($validated);

        return response()->json(['success' => true, 'invoice_discount' => $invoiceDiscount]);
    }

    public function update(Request $request, InvoiceDiscount $invoiceDiscount)
    {
        $validated = $request->validate([
            'discount_id' => 'required|exists:discounts,id',
            'discount_amount' => 'required|numeric|min:0',
        ]);

        $invoiceDiscount->update($validated);

        return response()->json(['success' => true, 'invoice_discount' => $invoiceDiscount]);
    }

    public function destroy(InvoiceDiscount $invoiceDiscount)
    {
        $invoiceDiscount->delete();

        return response()->json(['success' => true]);
    }

    public function removeById(Request $request)
    {
        $validated = $request->validate([
            'invoice_id' => 'required|exists:invoices,id',
            'discount_id' => 'required|exists:discounts,id',
        ]);
    
        $invoiceDiscount = InvoiceDiscount::where('invoice_id', $validated['invoice_id'])
            ->where('discount_id', $validated['discount_id'])
            ->first();
    
        if ($invoiceDiscount) {
            $invoiceDiscount->delete();
        }

        // Recalculate discounts and totals
        $invoice = Invoice::find($validated['invoice_id']);
        $invoice->calculateTotals();
        
        $invoice->save();

        $newTotals = [
            'total_discount' => $invoice->total_discount,
            'final_price' => $invoice->final_price,
        ];

        return response()->json(['success' => true, 'new_totals' => $newTotals]);
        
    }
    
}
