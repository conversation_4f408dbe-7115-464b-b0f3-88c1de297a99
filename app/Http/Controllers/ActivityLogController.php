<?php

namespace App\Http\Controllers;

use App\Models\ActivityLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ActivityLogController extends Controller
{
    /**
     * Display a listing of activity logs
     */
    public function index(Request $request)
    {
        $query = ActivityLog::with(['user', 'loggable']);

        // Apply filters
        if ($request->filled('component')) {
            $query->forComponent($request->component);
        }

        if ($request->filled('user_id')) {
            $query->forUser($request->user_id);
        }

        if ($request->filled('event_type')) {
            $query->forEventType($request->event_type);
        }

        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                  ->orWhere('component', 'like', "%{$search}%")
                  ->orWhere('event_type', 'like', "%{$search}%");
            });
        }

        // Order by most recent first
        $query->orderBy('created_at', 'desc');

        // Get pagination preference from session or request
        $pagination = $request->get('pagination', session('pagination', 25));
        session(['pagination' => $pagination]);

        // Paginate results
        $logs = $query->paginate($pagination)->withQueryString();

        // Get filter options
        $components = ActivityLog::select('component')
            ->distinct()
            ->orderBy('component')
            ->pluck('component');

        $eventTypes = ActivityLog::select('event_type')
            ->distinct()
            ->orderBy('event_type')
            ->pluck('event_type');

        $users = User::select('id', 'name')
            ->whereIn('id', ActivityLog::select('user_id')->distinct())
            ->orderBy('name')
            ->get();

        return view('activity-logs.index', compact('logs', 'components', 'eventTypes', 'users', 'pagination'));
    }

    /**
     * Show a specific activity log
     */
    public function show(ActivityLog $activityLog)
    {
        $activityLog->load(['user', 'loggable']);
        
        return view('activity-logs.show', compact('activityLog'));
    }

    /**
     * Get activity logs for a specific model (AJAX)
     */
    public function forModel(Request $request)
    {
        $request->validate([
            'model_type' => 'required|string',
            'model_id' => 'required|integer',
        ]);

        $logs = ActivityLog::where('loggable_type', $request->model_type)
            ->where('loggable_id', $request->model_id)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get();

        return response()->json($logs);
    }

    /**
     * Get activity statistics
     */
    public function statistics(Request $request)
    {
        $days = $request->get('days', 30);
        $startDate = now()->subDays($days);

        $stats = [
            'total_activities' => ActivityLog::where('created_at', '>=', $startDate)->count(),
            'activities_by_type' => ActivityLog::where('created_at', '>=', $startDate)
                ->select('event_type', DB::raw('count(*) as count'))
                ->groupBy('event_type')
                ->orderBy('count', 'desc')
                ->get(),
            'activities_by_component' => ActivityLog::where('created_at', '>=', $startDate)
                ->select('component', DB::raw('count(*) as count'))
                ->groupBy('component')
                ->orderBy('count', 'desc')
                ->get(),
            'most_active_users' => ActivityLog::where('created_at', '>=', $startDate)
                ->select('user_id', DB::raw('count(*) as count'))
                ->whereNotNull('user_id')
                ->groupBy('user_id')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->with('user')
                ->get(),
            'daily_activity' => ActivityLog::where('created_at', '>=', $startDate)
                ->select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
                ->groupBy(DB::raw('DATE(created_at)'))
                ->orderBy('date')
                ->get(),
        ];

        if ($request->expectsJson()) {
            return response()->json($stats);
        }

        return view('activity-logs.statistics', compact('stats', 'days'));
    }

    /**
     * Export activity logs
     */
    public function export(Request $request)
    {
        $query = ActivityLog::with(['user', 'loggable']);

        // Apply same filters as index
        if ($request->filled('component')) {
            $query->forComponent($request->component);
        }

        if ($request->filled('user_id')) {
            $query->forUser($request->user_id);
        }

        if ($request->filled('event_type')) {
            $query->forEventType($request->event_type);
        }

        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
        }

        $logs = $query->orderBy('created_at', 'desc')->get();

        $filename = 'activity_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($logs) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'ID',
                'Date/Time',
                'User',
                'Component',
                'Event Type',
                'Description',
                'Model Type',
                'Model ID',
                'IP Address',
            ]);

            // CSV data
            foreach ($logs as $log) {
                fputcsv($file, [
                    $log->id,
                    $log->created_at->format('Y-m-d H:i:s'),
                    $log->user?->name ?? 'System',
                    $log->component,
                    $log->event_type,
                    $log->description,
                    $log->loggable_type ? class_basename($log->loggable_type) : '',
                    $log->loggable_id ?? '',
                    $log->ip_address ?? '',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
