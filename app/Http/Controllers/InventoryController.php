<?php

namespace App\Http\Controllers;

use App\Models\Inventory;
use App\Models\InventoryCategory;
use App\Models\User;
use Illuminate\Http\Request;
use \Barryvdh\DomPDF\Facade\Pdf as PDF;
use App\Models\Customer;
use OpenAI;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Services\UserPreferenceService;


class InventoryController extends Controller
{
    public function index(Request $request, UserPreferenceService $userPreferenceService)
    {

        $defaultPagination = $userPreferenceService->get('pagination_inventory', 10);
        $defaultSort = $userPreferenceService->get('sort_inventory', 'created_at_desc');

        $pagination = $request->query('pagination', $defaultPagination);
        $sort = $request->query('sort', $defaultSort);


        if ($pagination !== $defaultPagination) {
            $userPreferenceService->set('pagination_inventory', $pagination);
        }

        if ($sort !== $defaultSort) {
            $userPreferenceService->set('sort_inventory', $sort);
        }



        $query = Inventory::query();

        // Trash filter
        if ($request->has('trashed') && $request->input('trashed') === 'true') {
            $query->onlyTrashed();
        }

        // Status filter
        if ($status = $request->input('status')) {
            $query->where('status', $status);
        }

        // Search filter
        if ($search = $request->input('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%$search%")
                    ->orWhere('location', 'like', "%$search%")
                    ->orWhereHas('lineItems.invoice.customer', function ($q) use ($search) {
                        $q->where('name', 'like', "%$search%");
                    })
                    ->orWhere('asset_tag', 'like', "%$search%")
                    ->orWhereHas('category', function ($q) use ($search) {
                        $q->where('code', 'like', "%$search%");
                    });
            });
        }

        // Category filter
        if ($category = $request->input('category')) {
            $query->where('inv_category_id', $category);
        }

        // Sorting
        $query->orderBy(
            $sort === 'created_at_asc' ? 'created_at' : 'created_at',
            $sort === 'created_at_asc' ? 'asc' : 'desc'
        );

        // Paginate inventories
        $inventories = $query
            ->with(['lineItems.invoice.customer', 'category'])
            ->paginate($pagination);

        // Fetch categories and statuses for filters
        $categories = InventoryCategory::all();
        $statuses = array_merge(['scrap' => 'Scrap'], Inventory::STATUS);

        return view('inventory.index', compact('inventories', 'categories', 'statuses', 'pagination'));
    }






    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = InventoryCategory::all();
        $technicians = User::all();

        return view('inventory.create', compact('categories', 'technicians'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'condition' => 'nullable|string',
            'status' => 'required|string',
            'technician_id' => 'nullable|exists:users,id',
            'inv_category_id' => 'required|exists:inventory_categories,id',
            'location' => 'nullable|string',
        ]);

        $category_quantity_type = InventoryCategory::find($request->inv_category_id)->quantity_type;

        // If it's 'weight' or 'individual', set quantity to 1
        if ($category_quantity_type === 'weight' || $category_quantity_type === 'individual') {
            $request->merge(['quantity' => 1]);
        } else {
            // Otherwise, validate quantity
            $request->validate([
                'quantity' => 'required|numeric|min:0',
            ]);
        }

        $inventory = $this->createInventoryItem($request->all());

        return redirect()->route('inventory.edit', $inventory->id)
            ->with('success', 'Inventory item created successfully!');
    }

    /**
     * Clone an inventory item
     */
    public function clone(Inventory $inventory)
    {
        // Prepare data for the new inventory item
        $clonedData = $inventory->toArray();
        unset($clonedData['id'], $clonedData['asset_tag'], $clonedData['created_at'], $clonedData['updated_at']);

        // Set status to 'Refurbishing'
        $clonedData['status'] = 'refurbishing';

        // Add 'Copy of' to the name
        $clonedData['name'] = $clonedData['name'] . ' Copy';
        // Create a new inventory item using the same logic as the store function
        $newInventory = $this->createInventoryItem($clonedData);

        // Copy checklist items
        foreach ($inventory->checklistItems as $checklistItem) {
            $newChecklistItem = $checklistItem->replicate();
            $newChecklistItem->inventory_id = $newInventory->id;
            $newChecklistItem->save();
        }

        // Copy inventory images
        foreach ($inventory->images as $inventoryImage) {
            $newInventoryImage = $inventoryImage->replicate();
            $newInventoryImage->inventory_id = $newInventory->id;
            $newInventoryImage->save();
        }

        return redirect()->route('inventory.edit', $newInventory->id)
            ->with('success', 'Inventory item cloned successfully!');
    }

    /**
     * Create a new inventory item and generate its asset tag.
     */
    private function createInventoryItem(array $data)
    {
        // Create inventory item
        $inventory = Inventory::create($data);

        // Generate the asset tag using the assigned id
        $categoryCode = InventoryCategory::find($data['inv_category_id'])->code;
        $assetTag = $categoryCode . str_pad($inventory->id, 4, '0', STR_PAD_LEFT);

        // Update the inventory item with the asset tag
        $inventory->update(['asset_tag' => $assetTag]);

        return $inventory;
    }





    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Inventory $inventory)
    {
        // Debug the resolved $inventory object

        $categories = InventoryCategory::all();
        $technicians = User::all();
        $customers = Customer::select('id', 'name')->get();
        $units = Inventory::UNITS;

        $inventorySoldAtFromLineItem = $inventory->lineItems->first()?->created_at ?? null;

        $inventory->load(['category.checklistFields', 'checklistItems']);

        return view('inventory.edit', compact('inventory', 'categories', 'technicians', 'customers', 'units', 'inventorySoldAtFromLineItem'));
    }


    /**
     * Update the specified resource in storage.
     */

    public function update(Request $request, Inventory $inventory)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|string',
            'condition' => 'nullable|string',
            'technician_id' => 'nullable|exists:users,id',
            'inv_category_id' => 'required|exists:inventory_categories,id',
            'location' => 'nullable|string',
            'quantity' => 'nullable|numeric|min:0',
            'suggested_price' => 'nullable|numeric|min:0',
            'unit_price' => 'nullable|numeric|min:0',
            'unit' => 'nullable|string',
        ]);

        // Check if trying to change status away from 'sold' when item has an invoice
        if ($inventory->status === 'sold' &&
            $validated['status'] !== 'sold' &&
            $inventory->getIndividualSoldItemInvoice() !== null) {

            return response()->json([
                'message' => 'Cannot change status from sold because this item is linked to an invoice. Remove the item from the invoice first.'
            ], 422);
        }

        info('Validated data:', $validated); // Ensure validated data is correct
        info('Inventory before update:', $inventory->toArray()); // Log inventory state before the update
        $updateResult = $inventory->update($validated);
        info('Update result:', ['success' => $updateResult, 'updated_data' => $inventory->toArray()]);

        return response()->json(['message' => 'Inventory updated successfully!'], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $inventory = Inventory::findOrFail($id);

        Log::info('Attempting to delete inventory item:', ['inventory' => $inventory->toArray()]);

        $deleteResult = $inventory->delete();

        Log::info('Delete result:', ['success' => $deleteResult]);
        Log::info('Inventory item after deletion attempt:', ['inventory' => $inventory->toArray()]);

        return redirect()->route('inventory.index')->with('success', 'Inventory item moved to trash successfully!');
    }

    public function trash()
    {
        $inventories = Inventory::onlyTrashed()->with(['technician', 'category'])->get();

        return view('inventory.trash', compact('inventories'));
    }

    public function restore($id)
    {
        $inventory = Inventory::onlyTrashed()->findOrFail($id);
        $inventory->restore();

        return redirect()->route('inventory.index')->with('success', 'Inventory item restored successfully!');
    }


    // Show a specific inventory item
    public function show($id)
    {
        $inventory = Inventory::with('category', 'checklistItems.checklistField')->findOrFail($id);


        $formattedChecklist = $inventory->getFormattedChecklistValues();

        // Return the show view
        return view('inventory.show', compact('inventory', 'formattedChecklist'));
    }



    private function generateDescription($inventory)
    {
        if (!$inventory->category) {
            throw new \Exception('Category is required for generating a description.');
        }

        // Check if the category supports AI-generated descriptions
        if (!$inventory->category->ai_promptable) {
            throw new \Exception('AI-generated descriptions are not supported for this category.');
        }

        // Load necessary relationships
        $inventory->load('checklistItems', 'category.checklistFields');

        // Load prompts from JSON
        $path = resource_path('data/prompts.json');
        if (!file_exists($path)) {
            throw new \Exception('Prompts JSON file not found.');
        }
        $prompts = json_decode(file_get_contents($path), true);

        // Get the category-specific prompts
        $categoryName = $inventory->category->name;

        $promptData = $prompts[$categoryName] ?? null;

        if (!$promptData) {
            throw new \Exception("No prompts defined for category: {$categoryName}");
            Log::info('No prompts defined for category: ' . $categoryName);
        }

        $titlePrompt = $promptData['title'] ?? 'Default title prompt.';
        $descriptionPrompt = $promptData['description'] ?? 'Default description prompt.';
        $excludeFields = $promptData['exclude'] ?? [];

        // Use the getFormattedChecklistValues method to retrieve checklist values
        $formattedChecklistData = $inventory->getFormattedChecklistValues();

        // Exclude fields based on the excludeFields array
        foreach ($excludeFields as $fieldToExclude) {
            unset($formattedChecklistData[$fieldToExclude]);
        }

        $data = [
            'name' => $inventory->name,
            'description' => $inventory->description,
            'category' => $inventory->category->name,
            'checklist' => $formattedChecklistData,
        ];

        // Build the full prompt
        $prompt = <<<PROMPT
    Use this item info to create a product listing title and description.
    Item: {$inventory->name}

    PROMPT;
        $prompt .= json_encode($formattedChecklistData, JSON_PRETTY_PRINT) . "\n";
        $prompt .= <<<PROMPT
    Format the title as:
    $titlePrompt
    Format the description as:
    $descriptionPrompt

    Return the result as basic formatted HTML with lists and headings if needed.

    PROMPT;

        // Log the prompt for debugging
        info('Prompt for OpenAI:', ['prompt' => $prompt]);

        try {
            // Initialize OpenAI client
            $openai = OpenAI::client(config('services.openai.api_key'));

            // Send request to OpenAI
            $response = $openai->chat()->create([
                'model' => 'gpt-4o',
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a helpful assistant for generating product descriptions.'],
                    ['role' => 'user', 'content' => $prompt],
                ],
            ]);

            // Extract the generated content
            $generatedContent = $response->choices[0]->message->content ?? 'No response received.';

            // Clean up the generated content
            $generatedContent = trim($generatedContent);
            $generatedContent = preg_replace('/^```/', '', $generatedContent); // Remove leading triple backticks
            $generatedContent = preg_replace('/```$/', '', $generatedContent); // Remove trailing triple backticks

            // Extract the title and description
            $generatedLines = explode("\n", $generatedContent);
            $generatedTitle = trim($generatedLines[0]) ?? 'No title generated.';
            $generatedDescription = implode("\n", array_slice($generatedLines, 1)) ?? 'No description generated.';

            // Save to the database
            $inventory->update([
                'generated_title' => $generatedTitle,
                'generated_description' => $generatedDescription,
            ]);

            return [$generatedTitle, $generatedDescription];
        } catch (\Exception $e) {
            info('OpenAI error:', ['message' => $e->getMessage()]);
            throw new \Exception('Failed to generate description: ' . $e->getMessage());
        }
    }




    // Fetch the generated description
    public function fetchDescription($id)
    {
        $inventory = Inventory::with('category', 'checklistItems.checklistField')->findOrFail($id);

        return response()->json([
            'generated_description' => $inventory->generated_description,
            'has_generated' => !is_null($inventory->generated_description),
        ]);
    }

    // Regenerate the description
    public function regenerateDescription(Request $request, $id)
    {
        $inventory = Inventory::with('category', 'checklistItems.checklistField')->findOrFail($id);

        // Generate the title and description
        [$generatedTitle, $generatedDescription] = $this->generateDescription($inventory);

        return response()->json([
            'generated_description' => $inventory->generated_description,
        ]);
    }



    public function generatePDF(Inventory $inventory)
    {
        $inventory->load('category');
        $inventoryChecklist = $inventory->getFormattedChecklistValues();

        $pdf = Pdf::loadView('inventory.pdf', compact('inventory', 'inventoryChecklist'))
            ->setPaper('a4', 'portrait');

        return $pdf->stream("Inventory_{$inventory->asset_tag}.pdf");
    }


    protected $casts = [
        'sold_time' => 'datetime',
    ];


    // Convert large storage values to TB if applicable
    private function formatStorage($sizeInGB)
    {
        if (is_numeric($sizeInGB) && $sizeInGB >= 1024) {
            return ($sizeInGB / 1024) . 'TB';
        }
        return "{$sizeInGB}GB";
    }


    // Get all details about 1 inventory item
    public function getJson($id)
    {
        $inventory = Inventory::with('category', 'checklistItems.checklistField')->findOrFail($id);

        return response()->json($inventory);
    }

    public function getSuggestedPrice($id)
    {
        $inventory = Inventory::findOrFail($id);

        // Suggested price calculation logic
        $suggestedPrice = $inventory->suggested_price ?? 0;

        return response()->json(['suggested_price' => $suggestedPrice]);
    }


    public function getListedItems($categoryId)
    {
        $items = Inventory::where('inv_category_id', $categoryId)
            ->where(function ($query) {
                $query->where('status', 'forsale')
                    ->orWhere('status', 'commodity');
            })
            ->get(['id', 'name', 'suggested_price', 'location']);

        return response()->json($items);
    }


    public function updateChecklist(Request $request, Inventory $inventory)
    {
        try {
            $validated = $request->validate([
                'checklist.*' => 'nullable',
            ]);

            // Log the incoming data for debugging
            \Log::info('Checklist update request', [
                'inventory_id' => $inventory->id,
                'checklist_data' => $validated['checklist'] ?? []
            ]);

            // Handle case where checklist data might be empty
            if (empty($validated['checklist'])) {
                if ($request->ajax() || $request->wantsJson()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'No checklist data to update'
                    ]);
                }
                return redirect()
                    ->route('inventory.edit', $inventory->id)
                    ->with('info', 'No checklist data to update');
            }

            foreach ($validated['checklist'] as $fieldId => $value) {
                $field = $inventory->category->checklistFields->firstWhere('id', $fieldId);

                if (!$field) {
                    \Log::warning('Checklist field not found', ['field_id' => $fieldId]);
                    continue; // Skip if field doesn't exist
                }

                if ($field->type === 'textarea') {
                    // Save value in long_value field for textarea
                    $inventory->checklistItems()->updateOrCreate(
                        ['checklist_field_id' => $fieldId],
                        ['long_value' => $value, 'value' => null]
                    );
                } elseif ($field->type === 'checkboxes') {
                    // Save checkboxes as JSON in long_value field
                    $inventory->checklistItems()->updateOrCreate(
                        ['checklist_field_id' => $fieldId],
                        ['long_value' => json_encode($value), 'value' => null]
                    );
                } else {
                    // Save other field types in value field
                    $inventory->checklistItems()->updateOrCreate(
                        ['checklist_field_id' => $fieldId],
                        ['value' => $value, 'long_value' => null]
                    );
                }
            }

            // Check if this is an AJAX request
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Checklist updated successfully!'
                ]);
            }

            return redirect()
                ->route('inventory.edit', $inventory->id)
                ->with('success', 'Checklist updated successfully!');

        } catch (\Exception $e) {
            \Log::error('Checklist update failed', [
                'inventory_id' => $inventory->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update checklist: ' . $e->getMessage()
                ], 500);
            }

            return redirect()
                ->route('inventory.edit', $inventory->id)
                ->with('error', 'Failed to update checklist: ' . $e->getMessage());
        }
    }


    public function search(Request $request)
    {


        $query = $request->query('query', '');
        $active = $request->query('active', false); // Default to false if not provided
        $id = $request->query('id', null);

        if (empty($query) && !$id) {
            return response()->json(['error' => 'Search query cannot be empty'], 400);
        }

        // if id is provided, search for a specific inventory item
        if ($id) {
            $results = Inventory::where('id', $id)
                ->with([
                    'category' => function ($query) {
                        $query->select('id', 'name', 'quantity_type', 'tax_policy_id', 'department_id');
                    },
                ])
                ->limit(10)
                ->get(['id', 'name', 'asset_tag', 'location', 'suggested_price', 'quantity', 'inv_category_id']);

            return response()->json($results);
        }

        $results = Inventory::where(function ($q) use ($query) {
            $q->where('name', 'like', "%$query%")
                ->orWhere('asset_tag', 'like', "%$query%")
                ->orWhere('location', 'like', "%$query%")
                ->orWhereHas('lineItems.invoice.customer', function ($q) use ($query) {
                    $q->where('name', 'like', "%$query%");
                });
        })
            ->when($active, function ($q) {
                $q->where('status', 'forsale'); // Filter for active listings
            })
            ->with([
                'category' => function ($query) {
                    $query->select('id', 'name', 'quantity_type', 'tax_policy_id', 'department_id');
                },
            ])
            ->limit(10)
            ->get(['id', 'name', 'asset_tag', 'location', 'suggested_price', 'quantity', 'inv_category_id']);
        return response()->json($results);
    }

    /**
     * Update a single field via AJAX for inline editing
     */
    public function updateField(Request $request, Inventory $inventory)
    {
        $field = $request->input('field');
        $value = $request->input('value');

        // Validate the field is allowed for inline editing
        $allowedFields = ['name', 'location', 'suggested_price', 'status'];
        if (!in_array($field, $allowedFields)) {
            return response()->json(['error' => 'Field not allowed for inline editing'], 400);
        }

        // Check if trying to change status away from 'sold' when item has an invoice
        if ($field === 'status' &&
            $inventory->status === 'sold' &&
            $value !== 'sold' &&
            $inventory->getIndividualSoldItemInvoice() !== null) {

            return response()->json([
                'error' => 'Cannot change status from sold because this item is linked to an invoice. Remove the item from the invoice first.'
            ], 422);
        }

        // Validate the value based on field type
        $rules = [
            'name' => 'required|string|max:255',
            'location' => 'nullable|string|max:255',
            'suggested_price' => 'nullable|numeric|min:0',
            'status' => 'required|string|in:' . implode(',', array_keys(Inventory::STATUS)),
        ];

        $validator = validator(['value' => $value], ['value' => $rules[$field]]);
        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()->first()], 400);
        }

        // Update the field
        $inventory->update([$field => $value]);

        // Return the formatted value for display
        $displayValue = $value;
        if ($field === 'suggested_price') {
            $displayValue = '$' . number_format($value, 2);
        } elseif ($field === 'status') {
            // Return status info for updating the badge
            $statusInfo = $this->getStatusInfo($value);
            return response()->json([
                'success' => true,
                'value' => $displayValue,
                'raw_value' => $value,
                'status_info' => $statusInfo
            ]);
        }

        return response()->json([
            'success' => true,
            'value' => $displayValue,
            'raw_value' => $value
        ]);
    }

    /**
     * Get status information including badge class and icon
     */
    private function getStatusInfo($status)
    {
        $statusMap = [
            'intake' => [
                'label' => 'Intake',
                'badge_class' => 'bg-neutral text-neutral-content',
                'icon' => 'fa-box-open'
            ],
            'commodity' => [
                'label' => 'Commodity',
                'badge_class' => 'bg-warning text-warning-content',
                'icon' => 'fa-cube'
            ],
            'refurbishing' => [
                'label' => 'Refurbishing',
                'badge_class' => 'bg-orange-500 text-white',
                'icon' => 'fa-tools'
            ],
            'cleaning' => [
                'label' => 'Cleaning',
                'badge_class' => 'bg-info text-info-content',
                'icon' => 'fa-broom'
            ],
            'ready-to-list' => [
                'label' => 'Ready to List',
                'badge_class' => 'bg-secondary text-secondary-content',
                'icon' => 'fa-check-circle'
            ],
            'forsale' => [
                'label' => 'For Sale',
                'badge_class' => 'bg-success text-success-content',
                'icon' => 'fa-store'
            ],
            'sold' => [
                'label' => 'Sold',
                'badge_class' => 'bg-green-600 text-white',
                'icon' => 'fa-dollar-sign'
            ],
            'scrap' => [
                'label' => 'Scrap',
                'badge_class' => 'bg-error text-error-content',
                'icon' => 'fa-trash'
            ]
        ];

        return $statusMap[$status] ?? [
            'label' => ucfirst($status),
            'badge_class' => 'bg-neutral text-neutral-content',
            'icon' => 'fa-question-circle'
        ];
    }

    public function fetchInventoryImages(Request $request, $id)
    {
        $inventory = Inventory::findOrFail($id);

        // Get images using the new Image model system
        $images = \App\Models\Image::forContext('inventory', $inventory->id)
            ->get()
            ->map(function ($image) {
                return [
                    'id' => $image->id,
                    'image_path' => $image->getImageSrc('sm'), // Thumbnail path
                    'alt_text' => $image->alt_text ?? '',
                    'title' => $image->title ?? '',
                    'description' => $image->description ?? '',
                    'webp_path' => $image->getImageSrc('full'), // Full-size path
                ];
            });

        return response()->json([
            'success' => true,
            'images' => $images,
        ]);
    }
}
