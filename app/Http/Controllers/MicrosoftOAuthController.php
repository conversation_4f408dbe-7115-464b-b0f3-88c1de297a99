<?php

namespace App\Http\Controllers;

use App\Models\MicrosoftCalendarIntegration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use League\OAuth2\Client\Provider\GenericProvider;
use League\OAuth2\Client\Token\AccessToken;

class MicrosoftOAuthController extends Controller
{
    /**
     * Redirect to Microsoft OAuth authorization URL.
     */
    public function redirectToMicrosoft()
    {
        $provider = $this->getOAuthProvider();
        
        $authorizationUrl = $provider->getAuthorizationUrl([
            'scope' => 'https://graph.microsoft.com/Calendars.ReadWrite https://graph.microsoft.com/User.Read offline_access'
        ]);

        // Store the state for security
        session(['microsoft_oauth_state' => $provider->getState()]);

        return redirect()->away($authorizationUrl);
    }

    /**
     * Handle Microsoft OAuth callback.
     */
    public function handleCallback(Request $request)
    {
        // Verify state parameter for security
        if ($request->get('state') !== session('microsoft_oauth_state')) {
            return redirect()->route('profile.show')
                ->with('error', 'Invalid state parameter. Please try again.');
        }

        // Clear the state from session
        session()->forget('microsoft_oauth_state');

        // Handle authorization errors
        if ($request->has('error')) {
            $error = $request->get('error_description', $request->get('error'));
            Log::warning('Microsoft OAuth error', ['error' => $error, 'user_id' => Auth::id()]);
            
            return redirect()->route('profile.show')
                ->with('error', 'Microsoft authorization failed: ' . $error);
        }

        // Exchange authorization code for access token
        try {
            $provider = $this->getOAuthProvider();
            $accessToken = $provider->getAccessToken('authorization_code', [
                'code' => $request->get('code')
            ]);

            // Get user info from Microsoft Graph
            $userInfo = $this->getUserInfo($accessToken);

            // For shared calendar setup, show calendar selection page
            $isSharedCalendarSetup = session('microsoft_setup_type') === 'shared_calendar';
            if ($isSharedCalendarSetup) {
                // Store OAuth data temporarily in session for calendar selection
                session([
                    'microsoft_oauth_token' => $accessToken->getToken(),
                    'microsoft_oauth_refresh_token' => $accessToken->getRefreshToken(),
                    'microsoft_oauth_expires' => $accessToken->getExpires(),
                    'microsoft_user_info' => $userInfo
                ]);

                return redirect()->route('admin.microsoft-integration.calendar-selection');
            } else {
                // For individual user integrations (legacy), store directly
                $this->storeIntegration($accessToken, $userInfo);
                return redirect()->route('profile.show')
                    ->with('success', 'Microsoft 365 calendar integration connected successfully!');
            }

        } catch (\Exception $e) {
            Log::error('Microsoft OAuth callback error', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return redirect()->route('profile.show')
                ->with('error', 'Failed to connect to Microsoft 365: ' . $e->getMessage());
        }
    }

    /**
     * Disconnect Microsoft integration.
     */
    public function disconnect()
    {
        $user = Auth::user();
        $integration = $user->microsoftCalendarIntegration;

        if ($integration) {
            $integration->delete();
            
            return redirect()->route('profile.show')
                ->with('success', 'Microsoft 365 calendar integration disconnected successfully.');
        }

        return redirect()->route('profile.show')
            ->with('error', 'No Microsoft 365 integration found.');
    }

    /**
     * Test the connection by fetching user calendars.
     */
    public function testConnection()
    {
        $user = Auth::user();
        $integration = $user->microsoftCalendarIntegration;

        if (!$integration || !$integration->isActive()) {
            return response()->json(['error' => 'No active Microsoft integration found'], 400);
        }

        try {
            // Refresh token if needed
            if ($integration->isTokenExpired()) {
                $this->refreshAccessToken($integration);
            }

            $calendars = $this->getUserCalendars($integration->access_token);

            return response()->json([
                'success' => true,
                'calendars' => $calendars,
                'user_email' => $integration->microsoft_user_email
            ]);

        } catch (\Exception $e) {
            Log::error('Microsoft connection test failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json(['error' => 'Connection test failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get the OAuth provider instance.
     */
    private function getOAuthProvider(): GenericProvider
    {
        $config = config('services.microsoft');
        
        return new GenericProvider([
            'clientId' => $config['client_id'],
            'clientSecret' => $config['client_secret'],
            'redirectUri' => $config['redirect_uri'],
            'urlAuthorize' => 'https://login.microsoftonline.com/' . $config['tenant_id'] . '/oauth2/v2.0/authorize',
            'urlAccessToken' => 'https://login.microsoftonline.com/' . $config['tenant_id'] . '/oauth2/v2.0/token',
            'urlResourceOwnerDetails' => 'https://graph.microsoft.com/v1.0/me',
        ]);
    }

    /**
     * Get user information from Microsoft Graph.
     */
    private function getUserInfo(AccessToken $accessToken): array
    {
        $provider = $this->getOAuthProvider();
        $request = $provider->getAuthenticatedRequest(
            'GET',
            'https://graph.microsoft.com/v1.0/me',
            $accessToken
        );

        $response = $provider->getParsedResponse($request);
        
        return $response;
    }

    /**
     * Get user calendars from Microsoft Graph.
     */
    private function getUserCalendars(string $accessToken): array
    {
        $provider = $this->getOAuthProvider();
        $token = new AccessToken(['access_token' => $accessToken]);
        
        $request = $provider->getAuthenticatedRequest(
            'GET',
            'https://graph.microsoft.com/v1.0/me/calendars',
            $token
        );

        $response = $provider->getParsedResponse($request);
        
        return $response['value'] ?? [];
    }

    /**
     * Store or update the Microsoft integration.
     */
    private function storeIntegration(AccessToken $accessToken, array $userInfo): void
    {
        $user = Auth::user();
        $isSharedCalendarSetup = session('microsoft_setup_type') === 'shared_calendar';
        
        if ($isSharedCalendarSetup) {
            // Clear existing shared calendar integration if any
            MicrosoftCalendarIntegration::where('is_shared_calendar', true)->delete();
            
            // Create new shared calendar integration
            $integration = MicrosoftCalendarIntegration::create([
                'user_id' => $user->id,
                'access_token' => encrypt($accessToken->getToken()),
                'refresh_token' => encrypt($accessToken->getRefreshToken()),
                'token_expires_at' => $accessToken->getExpires() ? 
                    now()->addSeconds($accessToken->getExpires() - time()) : null,
                'microsoft_user_id' => $userInfo['id'],
                'microsoft_user_email' => $userInfo['mail'] ?? $userInfo['userPrincipalName'],
                'sync_enabled' => true,
                'sync_status' => 'pending',
                'error_message' => null,
                'is_shared_calendar' => true,
                'calendar_name' => 'ETRFlow Shared Pickup Calendar',
            ]);
            
            // Clear the session flag
            session()->forget('microsoft_setup_type');
            
            Log::info('Shared Microsoft calendar integration created', [
                'user_id' => $user->id,
                'microsoft_user_email' => $integration->microsoft_user_email,
                'calendar_name' => $integration->calendar_name
            ]);
        } else {
            // Individual user integration (legacy support)
            $integration = MicrosoftCalendarIntegration::updateOrCreate(
                ['user_id' => $user->id, 'is_shared_calendar' => false],
                [
                    'access_token' => encrypt($accessToken->getToken()),
                    'refresh_token' => encrypt($accessToken->getRefreshToken()),
                    'token_expires_at' => $accessToken->getExpires() ? 
                        now()->addSeconds($accessToken->getExpires() - time()) : null,
                    'microsoft_user_id' => $userInfo['id'],
                    'microsoft_user_email' => $userInfo['mail'] ?? $userInfo['userPrincipalName'],
                    'sync_enabled' => true,
                    'sync_status' => 'pending',
                    'error_message' => null,
                ]
            );

            Log::info('Individual Microsoft integration stored', [
                'user_id' => $user->id,
                'microsoft_user_email' => $integration->microsoft_user_email
            ]);
        }
    }

    /**
     * Refresh the access token using the refresh token.
     */
    public function refreshAccessToken(MicrosoftCalendarIntegration $integration): void
    {
        $provider = $this->getOAuthProvider();
        
        $refreshToken = decrypt($integration->refresh_token);
        $newAccessToken = $provider->getAccessToken('refresh_token', [
            'refresh_token' => $refreshToken
        ]);

        $integration->update([
            'access_token' => encrypt($newAccessToken->getToken()),
            'refresh_token' => encrypt($newAccessToken->getRefreshToken() ?: $refreshToken),
            'token_expires_at' => $newAccessToken->getExpires() ? 
                now()->addSeconds($newAccessToken->getExpires() - time()) : null,
        ]);

        Log::info('Microsoft access token refreshed', ['user_id' => $integration->user_id]);
    }
}
