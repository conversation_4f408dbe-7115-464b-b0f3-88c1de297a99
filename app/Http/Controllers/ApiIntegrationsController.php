<?php

namespace App\Http\Controllers;

use App\Models\MicrosoftCalendarIntegration;
use Illuminate\View\View;

class ApiIntegrationsController extends Controller
{
    /**
     * Show the APIs & Integrations overview page.
     */
    public function index(): View
    {
        // Get status of various integrations
        $microsoftIntegration = MicrosoftCalendarIntegration::where('is_shared_calendar', true)->first();
        
        return view('admin.api-integrations.index', compact('microsoftIntegration'));
    }
}