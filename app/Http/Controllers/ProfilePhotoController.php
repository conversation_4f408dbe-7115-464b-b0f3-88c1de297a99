<?php

namespace App\Http\Controllers;

use App\Models\Image;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ProfilePhotoController extends Controller
{
    /**
     * Show the form for uploading a profile photo.
     *
     * @return \Illuminate\View\View
     */
    public function edit()
    {
        return view('profile.photo', [
            'user' => Auth::user(),
        ]);
    }

    /**
     * Update the user's profile photo.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $request->validate([
            'photo' => ['required', 'mimes:jpg,jpeg,png,webp', 'max:1024'],
        ]);

        $user = Auth::user();

        $file = $request->file('photo');
        $originalName = $file->getClientOriginalName();

        // Temporary path where the uploaded file is stored
        $tempPath = $file->store('temp', 'public');

        // Define the final path for the WebP file
        $webpPath = str_replace('temp/', 'img/', $tempPath);
        $webpPath = preg_replace('/\.\w+$/', '.webp', $webpPath);

        // Convert the uploaded file to WebP format
        $sourcePath = public_path("storage/{$tempPath}");
        $targetPath = public_path("storage/{$webpPath}");
        if (!Image::convertToWebp($sourcePath, $targetPath, 100, 2000)) {
            return redirect()->back()->withErrors(['photo' => 'Failed to save the image in WebP format.']);
        }

        // Delete the temporary file
        Storage::disk('public')->delete($tempPath);

        // Generate thumbnails
        Image::generateThumbnails($webpPath);

        // Save the image information to the database
        $image = Image::create([
            'image_path' => $webpPath,
            'og_filename' => $originalName,
            'title' => $user->name . ' Profile Photo',
            'description' => 'Profile photo for ' . $user->name,
            'alt_text' => 'Profile photo',
            'uploaded_by' => $user->id,
        ]);

        // Update the user's profile_photo_id
        $user->profile_photo_id = $image->id;
        $user->save();

        return redirect()->route('profile.show')
            ->with('status', 'Profile photo updated successfully.');
    }

    /**
     * Remove the user's profile photo.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy()
    {
        $user = Auth::user();

        // Clear the profile photo relationship
        $user->profile_photo_id = null;
        $user->save();

        return redirect()->route('profile.show')
            ->with('status', 'Profile photo removed successfully.');
    }
}
