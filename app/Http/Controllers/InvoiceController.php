<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Customer;
use Illuminate\Http\Request;
use App\Models\Inventory;
use App\Models\InventoryCategory;
use App\Models\TaxPolicy;
use App\Models\Discount;
use App\Models\InvoiceDiscount;

use App\Models\GlobalConfig;
use App\Models\Department;
use Barryvdh\DomPDF\Facade\Pdf;

use App\Services\UserPreferenceService;

class InvoiceController extends Controller
{
    public function index(Request $request, UserPreferenceService $userPreferenceService)
    {
        // Retrieve saved preferences or set defaults
        $defaultPagination = $userPreferenceService->get('pagination_invoices', 10);
        $defaultSort = $userPreferenceService->get('invoice_sort', 'id');
        $defaultOrder = $userPreferenceService->get('invoice_order', 'asc');

        // Get current request parameters or fall back to defaults
        $pagination = $request->query('pagination', $defaultPagination);
        $sort = $request->query('sort', $defaultSort);
        $order = $request->query('order', $defaultOrder);

        // Save updated preferences if they differ from defaults
        if ($pagination !== $defaultPagination) {
            $userPreferenceService->set('pagination_invoices', $pagination);
        }
        if ($sort !== $defaultSort) {
            $userPreferenceService->set('invoice_sort', $sort);
        }
        if ($order !== $defaultOrder) {
            $userPreferenceService->set('invoice_order', $order);
        }

        // Start building the query
        $query = Invoice::with('customer');

        // Filter by customer if customer_id is provided
        if ($customerId = $request->input('customer_id')) {
            $query->where('customer_id', $customerId);
        }

        // Define valid columns for sorting
        $validColumns = ['id', 'invoice_date', 'total_price', 'final_price', 'status'];

        if ($sort === 'customer') {
            // Sort by customer name
            $query->join('customers', 'invoices.customer_id', '=', 'customers.id')
                  ->select('invoices.*', 'customers.name as customer_name')
                  ->orderBy('customers.name', $order);
        } elseif (in_array($sort, $validColumns)) {
            // Sort by a valid column
            $query->orderBy($sort, $order);
        }

        $invoices = $query->paginate($pagination);
        $invoices->appends($request->except('page')); // Maintain query parameters in pagination links

        return view('invoices.index', compact('invoices', 'pagination', 'sort', 'order'));
    }





    public function create(Request $request)
    {
        $customers = Customer::select('id', 'name')->get();
        $selectedCustomer = null;
        $selectedCustomerId = null;

        // Check if customer_id is provided in the request
        if ($request->has('customer_id')) {
            $selectedCustomerId = (int) $request->input('customer_id');
            $selectedCustomer = Customer::find($selectedCustomerId);
        }

        return view('invoices.create', compact('customers', 'selectedCustomer', 'selectedCustomerId'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'invoice_date' => 'required|date',
            'status' => 'nullable',
            'payment_method' => 'nullable',
        ]);

        if (empty($validated['customer_id'])) {
            $generalCustomerId = GlobalConfig::getGeneralCustomerId();

            if (!$generalCustomerId) {
                return redirect()->route('invoices.create')->with('error', 'General customer not configured.');
            }

            $validated['customer_id'] = $generalCustomerId;
        }

        $invoice = Invoice::create($validated);
        return redirect()->route('invoices.edit', $invoice)->with('success', 'Invoice created successfully.');
    }

    public function storeGeneral()
    {
        $generalCustomerId = GlobalConfig::getGeneralCustomerId();

        if (!$generalCustomerId) {
            return redirect()->route('invoices.create')->with('error', 'General customer not configured.');
        }

        $invoice = Invoice::create([
            'customer_id' => $generalCustomerId,
            'invoice_date' => now(),
        ]);

        return redirect()->route('invoices.edit', $invoice)->with('success', 'General invoice created successfully.');
    }

    public function show(Invoice $invoice)
    {
        $invoice->load('customer', 'lineItems');
        return view('invoices.show', compact('invoice'));
    }

    public function edit(Invoice $invoice)
    {
        $customers = Customer::all();
        $inventory = Inventory::where('status', 'forsale')->get();
        $taxPolicies = TaxPolicy::all();
        $inventoryCategories = InventoryCategory::all();


        $popular_item_ids = GlobalConfig::decodeValue('invoices_popular_inventory_items') ?? [];

        $popular_items = Inventory::whereIn('id', $popular_item_ids)
            ->with('category:id,quantity_type,tax_policy_id') // Eager load the category relationship with id, quantity_type, and tax_policy_id fields
            ->get();

        $departments = Department::all();

        return view('invoices.edit', [
            'invoice' => $invoice,
            'customers' => $customers,
            'inventory' => $inventory,
            'standardTaxRate' => config('app.standard_tax_rate', 0.08), // Default to 8% if not set
            'taxPolicies' => $taxPolicies,
            'inventoryCategories' => $inventoryCategories,
            'popular_items' => $popular_items,
            'departments' => $departments,

        ]);
    }



    public function update(Request $request)
    {
        try {
            // Validate the incoming request
            $validated = $request->validate([
                'invoice_id' => 'required|exists:invoices,id',
                'customer_id' => 'required|exists:customers,id',
                'invoice_date' => 'required|date',
                'status' => 'nullable|string',
                'payment_method' => 'nullable|string',
                'notes' => 'nullable|string',
            ]);

            // Find the invoice by ID
            $invoice = Invoice::findOrFail($validated['invoice_id']);

            // Update the invoice with the validated data
            $invoice->update($validated);

            // Calculate updated totals
            $totals = [
                'total_price' => $invoice->total_price,
                'total_tax' => $invoice->total_tax,
                'total_discount' => $invoice->total_discount,
                'final_price' => $invoice->final_price,
            ];

            // Return success response as JSON
            return response()->json([
                'success' => true,
                'message' => 'Invoice updated successfully.',
                'new_totals' => $totals,
            ]);
        } catch (\Exception $e) {
            // Return an error response as JSON
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage(),
            ], 500);
        }
    }





    public function destroy(Invoice $invoice)
    {
        $invoice->delete();
        return redirect()->route('invoices.index')->with('success', 'Invoice deleted successfully.');
    }

    public function applyDiscount(Request $request)
    {
        $validated = $request->validate([
            'discount_id' => 'required|exists:discounts,id',
            'invoice_id' => 'required|exists:invoices,id',
        ]);

        $discount = Discount::findOrFail($validated['discount_id']);
        $invoice = Invoice::findOrFail($validated['invoice_id']);

        // Calculate discount amount
        $discountAmount = $discount->type === 'percent'
            ? $invoice->total_price * ($discount->amount / 100)
            : min($discount->amount, $invoice->total_price);

        if ($discount->maximum_discount) {
            $discountAmount = min($discountAmount, $discount->maximum_discount);
        }

        // Check if discount is already used for this customer
        if ($discount->once_per_customer) {
            $usedDiscount = InvoiceDiscount::where('discount_id', $discount->id)
                ->whereHas('invoice', function ($q) use ($invoice) {
                    $q->where('customer_id', $invoice->customer_id);
                })
                ->exists();

            if ($usedDiscount) {
                return response()->json(['success' => false, 'message' => 'Discount already used by this customer.']);
            }
        }

        // Create the invoice discount record
        InvoiceDiscount::create([
            'invoice_id' => $invoice->id,
            'discount_id' => $discount->id,
            'discount_amount' => $discountAmount,
        ]);

        // Recalculate invoice totals and redistribute discounts
        $invoice->calculateTotals();
        $invoice->save();

        return response()->json([
            'success' => true,
            'discountAmount' => $discountAmount,
            'invoiceId' => $invoice->id,
            'new_totals' => [
                'total_price' => $invoice->total_price,
                'total_tax' => $invoice->total_tax,
                'total_discount' => $invoice->total_discount,
                'final_price' => $invoice->final_price,
            ],
        ]);
    }

    public function getDiscountsUsed($id)
    {
        $invoice = Invoice::findOrFail($id);
        $discounts = $invoice->invoiceDiscounts()->with('discount')->get();
        return response()->json(['discounts' => $discounts]);
    }

    public function search(Request $request)
    {
        $query = $request->input('query');
        $pagination = $request->query('pagination', 10);
        $sort = $request->query('sort', 'id');
        $order = $request->query('order', 'asc');

        $invoices = Invoice::with('customer')
            ->where('id', $query)
            ->orWhereHas('customer', function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%");
            })
            ->orderBy($sort, $order)
            ->paginate($pagination);

        return view('invoices.index', compact('invoices', 'pagination', 'sort', 'order'));
    }

    public function generatePDF(Invoice $invoice)
    {
        $invoice->load(['customer', 'lineItems.inventory', 'invoiceDiscounts.discount']);

        // Get the site logo wide from GlobalConfig
        $siteLogoWide = GlobalConfig::getValue('site_logo_wide');
        $logoImageData = null;

        // Convert logo to base64 data URL for PDF compatibility (following CertificateController pattern)
        if ($siteLogoWide) {
            $imagePath = null;
            
            // Handle different path formats
            if (filter_var($siteLogoWide, FILTER_VALIDATE_URL)) {
                // It's a full URL
                if (strpos($siteLogoWide, url('/')) === 0) {
                    // It's a local URL, convert to file path
                    $relativePath = str_replace(url('/'), '', $siteLogoWide);
                    $imagePath = public_path($relativePath);
                }
            } else {
                // It's a relative path, try both with and without leading slash
                if (strpos($siteLogoWide, '/') === 0) {
                    $imagePath = public_path($siteLogoWide);
                } else {
                    $imagePath = public_path('/' . $siteLogoWide);
                }
            }

            // Convert to base64 if file exists
            if ($imagePath && file_exists($imagePath)) {
                $imageData = file_get_contents($imagePath);
                if ($imageData !== false) {
                    $base64 = base64_encode($imageData);
                    // Determine MIME type from file extension
                    $extension = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));
                    $mimeType = match($extension) {
                        'png' => 'image/png',
                        'jpg', 'jpeg' => 'image/jpeg',
                        'gif' => 'image/gif',
                        'svg' => 'image/svg+xml',
                        'webp' => 'image/webp',
                        default => 'image/png'
                    };
                    $logoImageData = 'data:' . $mimeType . ';base64,' . $base64;
                }
            }
        }

        // Get contact information from GlobalConfig
        $contactInfo = [
            'email' => GlobalConfig::getValue('site_email'),
            'phone' => GlobalConfig::getValue('site_phone'),
            'website' => GlobalConfig::getValue('site_website'),
        ];

        $pdf = Pdf::loadView('invoices.pdf', compact('invoice', 'logoImageData', 'contactInfo'))
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
                'defaultFont' => 'Arial',
                'dpi' => 150,
                'chroot' => public_path(),
            ]);

        return $pdf->stream("Invoice_{$invoice->id}.pdf");
    }
}
