<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\GlobalConfig;
use App\Models\Inventory;
use App\Models\Discount;
use App\Models\UserGroup;
use App\Models\HTMLField;
use App\Models\Calendar;

class GlobalConfigController extends Controller
{
    //
    public function edit()
    {
        $app_name = GlobalConfig::getValue('app_name');
        $popular_item_ids = GlobalConfig::decodeValue('invoices_popular_inventory_items') ?? [];
        $popular_items = Inventory::whereIn('id', $popular_item_ids)->get();
        $general_customer_id = GlobalConfig::getValue('general_customer_id');
        $tasks_enabled = GlobalConfig::getValue('tasks_enabled') ?? '1'; // Default to enabled
        $active_customer_contract_id = GlobalConfig::getValue('active_customer_contract_id'); // Get active customer contract discount ID
        $pickup_calendar_id = GlobalConfig::getValue('pickup_calendar_id'); // Get pickup calendar ID
        $min_break_duration = GlobalConfig::getValue('min_break_duration') ?? 30; // Default to 30 minutes

        // Timeclock report settings
        $timeclock_daily_report_enabled = GlobalConfig::getValue('timeclock_daily_report_enabled') ?? '0'; // Default to disabled
        $timeclock_report_email = GlobalConfig::getValue('timeclock_report_email');
        $timeclock_report_time = GlobalConfig::getValue('timeclock_report_time') ?? '07:00'; // Default to 7:00 AM

        // Auto clock-out settings - ensure defaults are set if not configured
        if (GlobalConfig::getValue('timeclock_auto_clock_out_enabled') === null) {
            GlobalConfig::setValue('timeclock_auto_clock_out_enabled', '1');
        }
        
        if (GlobalConfig::getValue('timeclock_auto_clock_out_time') === null) {
            GlobalConfig::setValue('timeclock_auto_clock_out_time', '22:00');
        }
        
        $timeclock_auto_clock_out_enabled = GlobalConfig::getValue('timeclock_auto_clock_out_enabled');
        $timeclock_auto_clock_out_time = GlobalConfig::getValue('timeclock_auto_clock_out_time');
        
        // Fallback defaults if values are still null
        if ($timeclock_auto_clock_out_enabled === null) {
            $timeclock_auto_clock_out_enabled = '1';
        }
        
        if ($timeclock_auto_clock_out_time === null) {
            $timeclock_auto_clock_out_time = '22:00';
        }

        // Get payout products - just like popular items, these are now just IDs
        $payout_product_ids = GlobalConfig::decodeValue('payout_products') ?? [];

        // Make sure we have a flat array of IDs
        if (is_array($payout_product_ids)) {
            // Flatten any nested arrays and ensure all values are integers
            $payout_product_ids = array_map('intval', array_filter(array_map(function($item) {
                return is_array($item) ? ($item['id'] ?? null) : $item;
            }, $payout_product_ids)));

            $payout_products = !empty($payout_product_ids) ? Inventory::whereIn('id', $payout_product_ids)->get() : collect([]);
        } else {
            $payout_products = collect([]);
        }

        // Get only customer-specific discounts for the dropdown
        $active_discounts = Discount::where('is_customer_specific', true)
            ->orderBy('name')
            ->get();

        // Log the count for debugging
        \Illuminate\Support\Facades\Log::info('Found ' . $active_discounts->count() . ' customer-specific discounts in the database');

        // Get public calendars for pickup calendar selection
        $public_calendars = \App\Models\Calendar::where('is_public', true)->get();

        // Get pickup availability windows
        $pickup_availability_windows = GlobalConfig::getPickupAvailabilityWindows();
        $pickup_event_duration = GlobalConfig::getPickupEventDuration();
        $pickup_scheduling_interval = GlobalConfig::getPickupSchedulingInterval();

        // Get pickup lead time settings
        $pickup_lead_time_days = GlobalConfig::getPickupLeadTimeDays();
        $pickup_lead_time_hours = GlobalConfig::getPickupLeadTimeHours();
        $pickup_lead_time_business_days_only = GlobalConfig::getPickupLeadTimeBusinessDaysOnly();

        // Get pickup buffer time settings
        $pickup_buffer_time = GlobalConfig::getPickupBufferTime();
        $pickup_buffer_direction = GlobalConfig::getPickupBufferDirection();

        // Get pickup notification user groups
        $pickup_notification_user_groups = GlobalConfig::getPickupNotificationUserGroups();

        // Get all user groups with user count for the notification settings
        $user_groups = UserGroup::withCount('users')->orderBy('name')->get();

        // Get pickup scheduling notification emails
        $pickup_scheduling_notification_emails = GlobalConfig::getValue('pickup_scheduling_notification_emails');

        // Get pickup contact information
        $pickup_contact_email = GlobalConfig::getPickupContactEmail();
        $pickup_contact_phone = GlobalConfig::getPickupContactPhone();
        $pickup_website_link = GlobalConfig::getPickupWebsiteLink();

        // Get warehouse location
        $warehouse_location = GlobalConfig::getWarehouseLocation();

        // Get pickup item types
        $pickup_item_types = GlobalConfig::getPickupItemTypes();
        if (empty($pickup_item_types)) {
            $pickup_item_types = GlobalConfig::getDefaultPickupItemTypes();
        }

        // Get HTML fields for pickup request messages
        $pickup_welcome_message = HTMLField::getContent('pickup_request_welcome_message');
        $pickup_item_details_message = HTMLField::getContent('pickup_request_item_details_message');
        $pickup_first_time_message = HTMLField::getContent('pickup_request_first_time_message');

        // Get site logos
        $site_logo_square = GlobalConfig::getValue('site_logo_square');
        $site_logo_wide = GlobalConfig::getValue('site_logo_wide');

        // Get contact settings
        $site_email = GlobalConfig::getValue('site_email');
        $site_phone = GlobalConfig::getValue('site_phone');
        $site_website = GlobalConfig::getValue('site_website');

        return view('global_config.edit', compact(
            'app_name',
            'popular_items',
            'general_customer_id',
            'tasks_enabled',
            'payout_products',
            'min_break_duration',
            'active_discounts',
            'active_customer_contract_id',
            'timeclock_daily_report_enabled',
            'timeclock_report_email',
            'timeclock_report_time',
            'timeclock_auto_clock_out_enabled',
            'timeclock_auto_clock_out_time',
            'pickup_calendar_id',
            'public_calendars',
            'pickup_availability_windows',
            'pickup_event_duration',
            'pickup_scheduling_interval',
            'pickup_lead_time_days',
            'pickup_lead_time_hours',
            'pickup_lead_time_business_days_only',
            'pickup_buffer_time',
            'pickup_buffer_direction',
            'pickup_notification_user_groups',
            'user_groups',
            'pickup_scheduling_notification_emails',
            'pickup_contact_email',
            'pickup_contact_phone',
            'pickup_website_link',
            'pickup_item_types',
            'warehouse_location',
            'pickup_welcome_message',
            'pickup_item_details_message',
            'pickup_first_time_message',
            'site_logo_square',
            'site_logo_wide',
            'site_email',
            'site_phone',
            'site_website'
        ));
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'app_name' => 'required|string|max:255',
            'invoices_popular_inventory_items' => 'nullable|string',
            'general_customer_id' => 'nullable|integer|exists:customers,id',
            'tasks_enabled' => 'required|in:0,1',
            'payout_products' => 'nullable|string',
            'min_break_duration' => 'required|integer|min:0',
            'active_customer_contract_id' => 'nullable|integer|exists:discounts,id',
            'timeclock_daily_report_enabled' => 'required|in:0,1',
            'timeclock_report_email' => 'nullable|email|required_if:timeclock_daily_report_enabled,1',
            'timeclock_report_time' => 'nullable|date_format:H:i|required_if:timeclock_daily_report_enabled,1',
            'timeclock_auto_clock_out_enabled' => 'required|in:0,1',
            'timeclock_auto_clock_out_time' => 'nullable|date_format:H:i|required_if:timeclock_auto_clock_out_enabled,1',
            'pickup_calendar_id' => 'nullable|integer|exists:calendars,id',
            'pickup_availability_windows' => 'nullable|string',
            'pickup_event_duration' => 'required|integer|min:15|max:480', // 15 minutes to 8 hours
            'pickup_scheduling_interval' => 'required|integer|min:5|max:120', // 5 minutes to 2 hours
            'pickup_lead_time_days' => 'required|integer|min:0|max:30', // 0 to 30 days
            'pickup_lead_time_hours' => 'required|integer|min:0|max:23', // 0 to 23 hours
            'pickup_lead_time_business_days_only' => 'required|in:0,1',
            'pickup_buffer_time' => 'required|integer|min:0|max:240', // 0 to 4 hours
            'pickup_buffer_direction' => 'required|in:after,before,both',
            'pickup_notification_user_groups' => 'nullable|string',
            'pickup_scheduling_notification_emails' => 'nullable|string|max:5000',
            'pickup_contact_email' => 'nullable|email|max:255',
            'pickup_contact_phone' => 'nullable|string|max:255',
            'pickup_website_link' => 'nullable|url|max:255',
            'pickup_item_types' => 'nullable|string',
            'warehouse_location' => 'nullable|string|max:500',
            'pickup_welcome_message' => 'nullable|string',
            'pickup_item_details_message' => 'nullable|string',
            'pickup_first_time_message' => 'nullable|string',
            'site_email' => 'nullable|email|max:255',
            'site_phone' => 'nullable|string|max:255',
            'site_website' => 'nullable|url|max:255',
        ]);

        GlobalConfig::updateOrCreate(['key' => 'app_name'], ['value' => $validated['app_name']]);
        GlobalConfig::updateOrCreate(
            ['key' => 'invoices_popular_inventory_items'],
            ['value' => $validated['invoices_popular_inventory_items']]
        );
        GlobalConfig::updateOrCreate(['key' => 'general_customer_id'], ['value' => $validated['general_customer_id']]);
        GlobalConfig::updateOrCreate(['key' => 'tasks_enabled'], ['value' => $validated['tasks_enabled']]);
        GlobalConfig::updateOrCreate(['key' => 'min_break_duration'], ['value' => $validated['min_break_duration']]);
        GlobalConfig::updateOrCreate(['key' => 'active_customer_contract_id'], ['value' => $validated['active_customer_contract_id'] ?? null]);

        // Update timeclock report settings
        GlobalConfig::updateOrCreate(['key' => 'timeclock_daily_report_enabled'], ['value' => $validated['timeclock_daily_report_enabled']]);
        GlobalConfig::updateOrCreate(['key' => 'timeclock_report_email'], ['value' => $validated['timeclock_report_email']]);
        GlobalConfig::updateOrCreate(['key' => 'timeclock_report_time'], ['value' => $validated['timeclock_report_time']]);

        // Update auto clock-out settings
        GlobalConfig::updateOrCreate(['key' => 'timeclock_auto_clock_out_enabled'], ['value' => $validated['timeclock_auto_clock_out_enabled']]);
        GlobalConfig::updateOrCreate(['key' => 'timeclock_auto_clock_out_time'], ['value' => $validated['timeclock_auto_clock_out_time']]);

        // Update pickup calendar setting
        GlobalConfig::updateOrCreate(['key' => 'pickup_calendar_id'], ['value' => $validated['pickup_calendar_id']]);

        // Update pickup availability windows
        if (isset($validated['pickup_availability_windows'])) {
            $windows = json_decode($validated['pickup_availability_windows'], true);
            if (json_last_error() === JSON_ERROR_NONE) {
                GlobalConfig::setPickupAvailabilityWindows($windows);
            }
        }

        // Update pickup event duration and scheduling interval
        GlobalConfig::updateOrCreate(['key' => 'pickup_event_duration'], ['value' => $validated['pickup_event_duration']]);
        GlobalConfig::updateOrCreate(['key' => 'pickup_scheduling_interval'], ['value' => $validated['pickup_scheduling_interval']]);

        // Update pickup lead time settings
        GlobalConfig::updateOrCreate(['key' => 'pickup_lead_time_days'], ['value' => $validated['pickup_lead_time_days']]);
        GlobalConfig::updateOrCreate(['key' => 'pickup_lead_time_hours'], ['value' => $validated['pickup_lead_time_hours']]);
        GlobalConfig::updateOrCreate(['key' => 'pickup_lead_time_business_days_only'], ['value' => $validated['pickup_lead_time_business_days_only']]);

        // Update pickup buffer time settings
        GlobalConfig::updateOrCreate(['key' => 'pickup_buffer_time'], ['value' => $validated['pickup_buffer_time']]);
        GlobalConfig::updateOrCreate(['key' => 'pickup_buffer_direction'], ['value' => $validated['pickup_buffer_direction']]);

        // Update pickup notification user groups
        if (isset($validated['pickup_notification_user_groups'])) {
            $userGroupIds = json_decode($validated['pickup_notification_user_groups'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($userGroupIds)) {
                // Validate that all user group IDs exist
                $validUserGroupIds = UserGroup::whereIn('id', $userGroupIds)->pluck('id')->toArray();
                GlobalConfig::setPickupNotificationUserGroups($validUserGroupIds);
            }
        } else {
            // If no groups selected, save empty array
            GlobalConfig::setPickupNotificationUserGroups([]);
        }

        // Update pickup scheduling notification emails
        GlobalConfig::updateOrCreate(['key' => 'pickup_scheduling_notification_emails'], ['value' => $validated['pickup_scheduling_notification_emails'] ?? '']);

        // Update pickup contact information
        GlobalConfig::setPickupContactEmail($validated['pickup_contact_email'] ?? null);
        GlobalConfig::setPickupContactPhone($validated['pickup_contact_phone'] ?? null);
        GlobalConfig::setPickupWebsiteLink($validated['pickup_website_link'] ?? null);

        // Update warehouse location
        GlobalConfig::setWarehouseLocation($validated['warehouse_location'] ?? null);

        // Update pickup item types
        if (isset($validated['pickup_item_types'])) {
            $itemTypes = json_decode($validated['pickup_item_types'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($itemTypes)) {
                GlobalConfig::setPickupItemTypes($itemTypes);
            }
        }

        // Update payout products using the same approach as popular items
        if (isset($validated['payout_products'])) {
            GlobalConfig::updateOrCreate(
                ['key' => 'payout_products'],
                ['value' => $validated['payout_products']]
            );
        }

        // Update HTML fields
        if (isset($validated['pickup_welcome_message'])) {
            HTMLField::setContent(
                'pickup_request_welcome_message',
                $validated['pickup_welcome_message'],
                'Welcome message displayed on pickup request form'
            );
        }

        if (isset($validated['pickup_item_details_message'])) {
            HTMLField::setContent(
                'pickup_request_item_details_message',
                $validated['pickup_item_details_message'],
                'Message displayed on pickup request item details page'
            );
        }

        if (isset($validated['pickup_first_time_message'])) {
            HTMLField::setContent(
                'pickup_request_first_time_message',
                $validated['pickup_first_time_message'],
                'Message displayed to first-time customers in pickup request modal'
            );
        }

        // Update contact settings
        GlobalConfig::updateOrCreate(['key' => 'site_email'], ['value' => $validated['site_email'] ?? null]);
        GlobalConfig::updateOrCreate(['key' => 'site_phone'], ['value' => $validated['site_phone'] ?? null]);
        GlobalConfig::updateOrCreate(['key' => 'site_website'], ['value' => $validated['site_website'] ?? null]);

        // Store the current tab in session to restore it after redirect
        if ($request->has('current_tab')) {
            session(['current_tab' => $request->input('current_tab')]);
        }

        return redirect()->route('globalconfig.edit')->with('success', 'Configuration updated successfully.');
    }

    public function uploadLogo(Request $request)
    {
        $request->validate([
            'logo_file' => 'required|image|mimes:jpeg,jpg,png,webp|max:5120', // 5MB max
            'logo_type' => 'required|in:square,wide'
        ]);

        try {
            $logoType = $request->input('logo_type');
            $file = $request->file('logo_file');
            
            // Generate a unique filename
            $filename = 'logo_' . $logoType . '_' . time() . '.' . $file->getClientOriginalExtension();
            
            // Store the file in the public logos directory
            $path = $file->storeAs('logos', $filename, 'public');
            
            // Generate the full URL
            $logoUrl = asset('storage/' . $path);
            
            // Update the global config
            GlobalConfig::updateOrCreate(
                ['key' => 'site_logo_' . $logoType],
                ['value' => $logoUrl]
            );

            return response()->json([
                'success' => true,
                'message' => 'Logo uploaded successfully',
                'logo_url' => $logoUrl
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error uploading logo: ' . $e->getMessage()
            ], 500);
        }
    }
}
