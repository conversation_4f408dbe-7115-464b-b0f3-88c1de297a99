<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class AdminToolsController extends Controller
{
    /**
     * Display the admin tools dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('admin.tools.index');
    }

    /**
     * Display PHP information.
     *
     * @return \Illuminate\Http\Response
     */
    public function phpinfo()
    {
        // Capture phpinfo output
        ob_start();
        phpinfo();
        $phpinfo = ob_get_clean();

        // Remove the HTML wrapper and keep only the body content
        $phpinfo = preg_replace('%^.*<body[^>]*>\s*%si', '', $phpinfo);
        $phpinfo = preg_replace('%\s*</body>.*$%si', '', $phpinfo);

        return view('admin.tools.phpinfo', compact('phpinfo'));
    }
}
