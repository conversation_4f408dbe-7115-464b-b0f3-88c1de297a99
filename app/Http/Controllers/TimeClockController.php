<?php

namespace App\Http\Controllers;

use App\Models\TimeCard;
use App\Models\TimePunch;
use App\Models\GlobalConfig;
use App\Services\TimeCalculationService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;


class TimeClockController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $timeCalculationService = app(TimeCalculationService::class);

        // Get today's time card
        $timeCard = $user->getTodayTimeCard(); // Creates one if it doesn't exist
        $punches = $timeCard->allValidPunches()->orderBy('punch_time', 'desc')->get();
        $currentStatus = $timeCard->getCurrentStatus();

        // Force a fresh calculation of hours to ensure we have the latest values
        $currentHours = $timeCard->getCurrentHours();
        Log::info("TimeClockController@index - Current hours: " . json_encode($currentHours));

        $totalHours = $currentHours['total_hours'];
        $totalBreakHours = $currentHours['total_break_hours'];
        $formattedTotalHours = $currentHours['formatted_total_hours'];
        $formattedTotalBreakHours = $currentHours['formatted_total_break_hours'];

        $timezone = GlobalConfig::getTimeZone();

        // Calculate weekly summary using the centralized service
        $weeklySummary = $timeCalculationService->calculateWeeklyTimeSummary($user);
        $weeklyHours = $weeklySummary['total_hours'];
        $weeklyBreakHours = $weeklySummary['total_break_hours'];
        $weeklySickTime = $weeklySummary['total_sick_time'];
        $weeklyVacationTime = $weeklySummary['total_vacation_time'];
        $weeklyPtoTime = $weeklySummary['total_pto'];
        $weeklyBillableHours = $weeklySummary['total_billable_hours'];
        $weeklyOvertimeHours = $weeklySummary['total_overtime_hours'];
        $weeklyRegularHours = $weeklySummary['total_regular_hours'];
        $formattedWeeklyHours = $weeklySummary['formatted_total_hours'];
        $formattedWeeklyBreakHours = $weeklySummary['formatted_total_break_hours'];
        $formattedWeeklySickTime = $weeklySummary['formatted_total_sick_time'];
        $formattedWeeklyVacationTime = $weeklySummary['formatted_total_vacation_time'];
        $formattedWeeklyPtoTime = $weeklySummary['formatted_total_pto'];
        $formattedWeeklyBillableHours = $weeklySummary['formatted_total_billable_hours'];

        // Get minimum break duration from config
        $minBreakDuration = GlobalConfig::getMinBreakDuration();


        


        // Auto clock-out settings - ensure defaults are set if not configured
        if (GlobalConfig::getValue('timeclock_auto_clock_out_enabled') === null) {
            GlobalConfig::setValue('timeclock_auto_clock_out_enabled', '1');
        }
        
        if (GlobalConfig::getValue('timeclock_auto_clock_out_time') === null) {
            GlobalConfig::setValue('timeclock_auto_clock_out_time', '22:00');
        }
        
        $autoClockOutEnabled = GlobalConfig::isAutoClockOutEnabled();
        $autoClockOutTime = GlobalConfig::getAutoClockOutTime();
        
        // Fallback defaults if methods still return unexpected values
        if ($autoClockOutEnabled === null) {
            $autoClockOutEnabled = true;
        }
        
        if (empty($autoClockOutTime)) {
            $autoClockOutTime = '22:00';
        }

        return view('timecards.time-clock', compact(
            'timeCard',
            'currentStatus',
            'punches',
            'totalHours',
            'totalBreakHours',
            'formattedTotalHours',
            'formattedTotalBreakHours',
            'timezone',
            'weeklyHours',
            'weeklyBreakHours',
            'weeklySickTime',
            'weeklyVacationTime',
            'weeklyPtoTime',
            'weeklyBillableHours',
            'weeklyOvertimeHours',
            'weeklyRegularHours',
            'formattedWeeklyHours',
            'formattedWeeklyBreakHours',
            'formattedWeeklySickTime',
            'formattedWeeklyVacationTime',
            'formattedWeeklyPtoTime',
            'formattedWeeklyBillableHours',
            'minBreakDuration',
            'autoClockOutEnabled',
            'autoClockOutTime'
        ));
    }

    public function action(Request $request)
    {
        // Initialize variables outside try block for proper scope
        $action = $request->input('action');
        $notes = $request->input('notes');
        $userAgent = $request->userAgent();
        $ipAddress = $request->ip();

        try {
            $user = Auth::user();

            if (!$user) {
                Log::warning("TimeClockController@action - Unauthenticated request");

                // Return JSON for AJAX requests
                if ($request->ajax() || $request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Please log in to continue.',
                        'error_code' => 'UNAUTHENTICATED'
                    ], 401);
                }

                return redirect()->route('login')->with('error', 'Please log in to continue.');
            }

            $timeCard = $user->getTodayTimeCard();
            $isMobile = preg_match('/Mobile|Android|iPhone|iPad/', $userAgent);

            Log::info("TimeClockController@action - User: {$user->id} ({$user->name}), Action: {$action}, Notes: {$notes}, Mobile: " . ($isMobile ? 'Yes' : 'No'), [
                'user_agent' => $userAgent,
                'ip_address' => $ipAddress,
                'is_ajax' => $request->ajax(),
                'expects_json' => $request->expectsJson()
            ]);

        // First, validate the action
        $validation = $timeCard->validatePunchAction($action);
        
        if (!$validation['allowed']) {
            Log::warning("Punch action validation failed", [
                'user_id' => $user->id,
                'action' => $action,
                'current_status' => $timeCard->getCurrentStatus(),
                'message' => $validation['message']
            ]);

            // Return JSON for AJAX requests
            if ($request->ajax() || $request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $validation['message']
                ], 400);
            }

            return redirect()->back()->with('error', $validation['message']);
        }

        switch ($action) {
            case 'clock_in':
                Log::info("Processing clock_in action for user {$user->id}");

                // If on break, this is a break end (break_in)
                $type = $timeCard->isOnBreak() ? 'break_in' : 'clock_in';

                try {
                    // Create the punch
                    $punch = TimePunch::createPunch(
                        $timeCard->id,
                        $type,
                        $notes
                    );

                    // Recalculate hours and refresh the timeCard
                    $timeCard->calculateHours();
                    $timeCard->refresh();

                    Log::info("Successfully created {$type} punch", [
                        'user_id' => $user->id,
                        'punch_id' => $punch->id,
                        'time_card_id' => $timeCard->id,
                        'is_mobile' => $isMobile
                    ]);

                    // Update component state
                    $message = $type === 'break_in' ? 'Break ended successfully.' : 'Clocked in successfully.';

                    // Return JSON for AJAX requests
                    if ($request->ajax() || $request->expectsJson()) {
                        // Get updated punches for the view
                        $punches = $timeCard->allValidPunches()->orderBy('punch_time', 'desc')->get();
                        $timezone = GlobalConfig::getTimeZone();
                        $punchesHtml = view('timecards.partials.punches-list', compact('punches', 'timezone'))->render();

                        return response()->json([
                            'success' => true,
                            'message' => $message,
                            'current_status' => $timeCard->getCurrentStatus(),
                            'punch_id' => $punch->id,
                            'punches_html' => $punchesHtml
                        ]);
                    }

                    return redirect()->route('time-clock')->with('success', $message);

                } catch (\Exception $e) {
                    Log::error("Failed to create {$type} punch", [
                        'user_id' => $user->id,
                        'time_card_id' => $timeCard->id,
                        'error' => $e->getMessage(),
                        'is_mobile' => $isMobile
                    ]);

                    // Return JSON for AJAX requests
                    if ($request->ajax() || $request->expectsJson()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Failed to clock in. Please try again.'
                        ], 500);
                    }

                    return redirect()->back()->with('error', 'Failed to clock in. Please try again.');
                }

            case 'clock_out':
                Log::info("Processing clock_out action for user {$user->id}");

                try {
                    // Create the punch
                    $punch = TimePunch::createPunch(
                        $timeCard->id,
                        'clock_out',
                        $notes
                    );

                    // Recalculate hours and refresh the timeCard
                    $timeCard->calculateHours();
                    $timeCard->refresh();

                    Log::info("Successfully created clock_out punch", [
                        'user_id' => $user->id,
                        'punch_id' => $punch->id,
                        'time_card_id' => $timeCard->id,
                        'is_mobile' => $isMobile
                    ]);

                    // Return JSON for AJAX requests
                    if ($request->ajax() || $request->expectsJson()) {
                        // Get updated punches for the view
                        $punches = $timeCard->allValidPunches()->orderBy('punch_time', 'desc')->get();
                        $timezone = GlobalConfig::getTimeZone();
                        $punchesHtml = view('timecards.partials.punches-list', compact('punches', 'timezone'))->render();

                        return response()->json([
                            'success' => true,
                            'message' => 'Clocked out successfully.',
                            'current_status' => $timeCard->getCurrentStatus(),
                            'punch_id' => $punch->id,
                            'punches_html' => $punchesHtml
                        ]);
                    }

                    return redirect()->route('time-clock')->with('success', 'Clocked out successfully.');

                } catch (\Exception $e) {
                    Log::error("Failed to create clock_out punch", [
                        'user_id' => $user->id,
                        'time_card_id' => $timeCard->id,
                        'error' => $e->getMessage(),
                        'is_mobile' => $isMobile
                    ]);

                    // Return JSON for AJAX requests
                    if ($request->ajax() || $request->expectsJson()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Failed to clock out. Please try again.'
                        ], 500);
                    }

                    return redirect()->back()->with('error', 'Failed to clock out. Please try again.');
                }

            case 'break_out':
                Log::info("Processing break_out action for user {$user->id}");

                try {
                    // Create the punch (break_out)
                    $punch = TimePunch::createPunch(
                        $timeCard->id,
                        'break_out',
                        $notes
                    );

                    // Recalculate hours and refresh the timeCard
                    $timeCard->calculateHours();
                    $timeCard->refresh();

                    Log::info("Successfully created break_out punch", [
                        'user_id' => $user->id,
                        'punch_id' => $punch->id,
                        'time_card_id' => $timeCard->id,
                        'is_mobile' => $isMobile
                    ]);

                    // Return JSON for AJAX requests
                    if ($request->ajax() || $request->expectsJson()) {
                        // Get updated punches for the view
                        $punches = $timeCard->allValidPunches()->orderBy('punch_time', 'desc')->get();
                        $timezone = GlobalConfig::getTimeZone();
                        $punchesHtml = view('timecards.partials.punches-list', compact('punches', 'timezone'))->render();

                        return response()->json([
                            'success' => true,
                            'message' => 'Break started successfully.',
                            'current_status' => $timeCard->getCurrentStatus(),
                            'punch_id' => $punch->id,
                            'punches_html' => $punchesHtml
                        ]);
                    }

                    return redirect()->route('time-clock')->with('success', 'Break started successfully.');

                } catch (\Exception $e) {
                    Log::error("Failed to create break_out punch", [
                        'user_id' => $user->id,
                        'time_card_id' => $timeCard->id,
                        'error' => $e->getMessage(),
                        'is_mobile' => $isMobile
                    ]);

                    // Return JSON for AJAX requests
                    if ($request->ajax() || $request->expectsJson()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Failed to start break. Please try again.'
                        ], 500);
                    }

                    return redirect()->back()->with('error', 'Failed to start break. Please try again.');
                }

            case 'break_in':
                Log::info("Processing break_in action for user {$user->id}");

                try {
                    // Create the punch (break_in)
                    $punch = TimePunch::createPunch(
                        $timeCard->id,
                        'break_in',
                        $notes
                    );

                    // Recalculate hours and refresh the timeCard
                    $timeCard->calculateHours();
                    $timeCard->refresh();

                    Log::info("Successfully created break_in punch", [
                        'user_id' => $user->id,
                        'punch_id' => $punch->id,
                        'time_card_id' => $timeCard->id,
                        'is_mobile' => $isMobile
                    ]);

                    // Return JSON for AJAX requests
                    if ($request->ajax() || $request->expectsJson()) {
                        // Get updated punches for the view
                        $punches = $timeCard->allValidPunches()->orderBy('punch_time', 'desc')->get();
                        $timezone = GlobalConfig::getTimeZone();
                        $punchesHtml = view('timecards.partials.punches-list', compact('punches', 'timezone'))->render();

                        return response()->json([
                            'success' => true,
                            'message' => 'Break ended successfully.',
                            'current_status' => $timeCard->getCurrentStatus(),
                            'punch_id' => $punch->id,
                            'punches_html' => $punchesHtml
                        ]);
                    }

                    return redirect()->route('time-clock')->with('success', 'Break ended successfully.');

                } catch (\Exception $e) {
                    Log::error("Failed to create break_in punch", [
                        'user_id' => $user->id,
                        'time_card_id' => $timeCard->id,
                        'error' => $e->getMessage(),
                        'is_mobile' => $isMobile
                    ]);

                    // Return JSON for AJAX requests
                    if ($request->ajax() || $request->expectsJson()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Failed to end break. Please try again.'
                        ], 500);
                    }

                    return redirect()->back()->with('error', 'Failed to end break. Please try again.');
                }

            default:
                Log::warning("TimeClockController@action - Invalid action: {$action}", [
                    'user_id' => $user->id,
                    'user_agent' => $userAgent,
                    'ip_address' => $ipAddress
                ]);

                // Return JSON for AJAX requests
                if ($request->ajax() || $request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid action.'
                    ], 400);
                }

                return redirect()->back()->with('error', 'Invalid action.');
        }

        } catch (\Exception $e) {
            Log::error("TimeClockController@action - Exception: " . $e->getMessage(), [
                'user_id' => Auth::id() ?? 'unknown',
                'action' => $action ?? 'unknown',
                'user_agent' => $request->userAgent(),
                'ip_address' => $request->ip(),
                'exception' => $e,
                'stack_trace' => $e->getTraceAsString()
            ]);

            // Return JSON for AJAX requests
            if ($request->ajax() || $request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while processing your request. Please try again.'
                ], 500);
            }

            return redirect()->back()->with('error', 'An error occurred while processing your request. Please try again.');
        }
    }
}