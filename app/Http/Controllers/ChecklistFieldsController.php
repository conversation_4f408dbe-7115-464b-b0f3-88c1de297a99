<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ChecklistFields;

class ChecklistFieldsController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string|max:255',
            'status' => 'required|boolean',
            'category_id' => 'required|exists:inventory_categories,id',
        ]);

        // Handle options based on field type
        $options = [];
        if ($request->type === 'numeric' && $request->filled('options.unit')) {
            $options['unit'] = $request->input('options.unit');
        } elseif ($request->type === 'select' && $request->filled('options.select_list')) {
            $options['select_list'] = array_map('trim', explode(',', $request->input('options.select_list')));
        } elseif ($request->type === 'checkboxes' && $request->filled('options.checkboxes')) {
            $options['checkboxes'] = array_map('trim', explode(',', $request->input('options.checkboxes')));
        }

        // Determine the maximum order for the current category
        $maxOrder = ChecklistFields::where('category_id', $request->category_id)->max('order');

        // Create the new checklist field
        ChecklistFields::create([
            'name' => $request->name,
            'description' => $request->description,
            'type' => $request->type,
            'status' => $request->status,
            'category_id' => $request->category_id,
            'order' => $maxOrder ? $maxOrder + 1 : 1,
            'options' => json_encode($options),
        ]);

        return redirect()
            ->route('inventory_categories.edit', $request->category_id)
            ->with('success', 'Checklist field added successfully!');
    }

    public function destroy($id)
    {
        $field = ChecklistFields::findOrFail($id);
        $categoryId = $field->category_id;

        $field->delete();

        return response()->json(['success' => true]);
    }

    public function updateAll(Request $request)
    {
        $validatedData = $request->validate([
            'fields.*.name' => 'required|string|max:255',
            'fields.*.description' => 'nullable|string',
            'fields.*.order' => 'required|integer|min:1',
            'fields.*.status' => 'required|boolean',
            'fields.*.options.unit' => 'nullable|string|max:255',
            'fields.*.options.select_list' => 'nullable|string', // CSV for select
            'fields.*.options.checkboxes' => 'nullable|string', // CSV for checkboxes
        ]);

        foreach ($validatedData['fields'] as $id => $fieldData) {
            $field = ChecklistFields::findOrFail($id);

            // Handle options field
            $options = [];
            if (isset($fieldData['options']['unit'])) {
                $options['unit'] = $fieldData['options']['unit'];
            }
            if (isset($fieldData['options']['select_list'])) {
                $options['select_list'] = explode(',', $fieldData['options']['select_list']);
            }
            if (isset($fieldData['options']['checkboxes'])) {
                $options['checkboxes'] = explode(',', $fieldData['options']['checkboxes']);
            }

            $field->update([
                'name' => $fieldData['name'],
                'description' => $fieldData['description'],
                'order' => $fieldData['order'],
                'status' => $fieldData['status'],
                'options' => json_encode($options), // Store options as JSON
            ]);
        }

        return response()->json(['success' => true]);
    }
}
