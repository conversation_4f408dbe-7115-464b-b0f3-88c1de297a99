<?php

namespace App\Http\Controllers;

use App\Models\Certificate;
use App\Models\Signature;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SignatureController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        Log::info('SignatureController::store - Request Data', ['data' => $request->all()]);

        $request->validate([
            'certificate_id' => 'required|exists:certificates,id',
            'signatory_name' => 'required|string',
            'role' => 'required|string',
            'signature_data' => 'required|string',
            'contract_text' => 'nullable|string',
            'signature_type' => 'nullable|string|in:draw,type',
        ]);

        // Default to 'draw' if signature_type is not provided
        $signatureType = $request->input('signature_type', 'draw');

        $signatureData = $request->input('signature_data');
        // Extract the base64 encoded data from the data URL
        $signatureData = substr($signatureData, strpos($signatureData, ',') + 1);
        $signatureData = base64_decode($signatureData);

        if ($signatureData === false) {
            Log::error('SignatureController::store - base64_decode failed');
            return response()->json(['success' => false, 'message' => 'Failed to decode signature data.'], 400);
        }

        // Create the signature image with a black box containing the timestamp and signatory name
        $image = imagecreatefromstring($signatureData);
        if (!$image) {
            Log::error('SignatureController::store - Failed to create image from signature data');
            return response()->json(['success' => false, 'message' => 'Failed to create image from signature data.'], 400);
        }

        $imageWidth = imagesx($image);
        $imageHeight = imagesy($image);

        // Text to add
        $timestamp = now()->format('M d, Y H:i:s T');
        $text = "Digitally signed by {$request->signatory_name}\nOn {$timestamp}";

        // Calculate the height of the black box based on the number of lines
        $font = 3; // Built-in font size
        $lineHeight = imagefontheight($font) + 5; // Add padding between lines
        $lines = explode("\n", $text);
        $boxHeight = count($lines) * $lineHeight + 10; // Add padding above and below the text
        $newHeight = $imageHeight + $boxHeight;

        // Create a new image with space for the black box
        $newImage = imagecreatetruecolor($imageWidth, $newHeight);

        // Fill the new image with white background
        $white = imagecolorallocate($newImage, 255, 255, 255);
        imagefill($newImage, 0, 0, $white);

        // Copy the original signature image onto the new image
        imagecopy($newImage, $image, 0, 0, 0, 0, $imageWidth, $imageHeight);

        // Add the black box
        $black = imagecolorallocate($newImage, 0, 0, 0);
        imagefilledrectangle($newImage, 0, $imageHeight, $imageWidth, $newHeight, $black);

        // Add the text to the black box
        $textColor = imagecolorallocate($newImage, 255, 255, 255);
        $yOffset = $imageHeight + 5; // Start 5px below the signature image
        foreach ($lines as $line) {
            $textWidth = imagefontwidth($font) * strlen($line);
            $textX = ($imageWidth - $textWidth) / 2; // Center the text
            imagestring($newImage, $font, $textX, $yOffset, $line, $textColor);
            $yOffset += $lineHeight; // Move to the next line
        }

        // Ensure the signatures directory exists
        $signaturesDir = storage_path('app/public/signatures');
        if (!file_exists($signaturesDir)) {
            if (!mkdir($signaturesDir, 0755, true)) {
                Log::error('SignatureController::store - Failed to create signatures directory', ['path' => $signaturesDir]);
                return response()->json(['success' => false, 'message' => 'Failed to create signatures directory.'], 500);
            }
        }

        // Save the new image
        $imageName = time() . '_' . Str::random(10) . '.png';
        $imagePath = 'signatures/' . $imageName;
        $imageFullPath = storage_path('app/public/' . $imagePath);

        try {
            if (!imagepng($newImage, $imageFullPath)) {
                Log::error('SignatureController::store - Failed to save signature image', ['path' => $imageFullPath]);
                return response()->json(['success' => false, 'message' => 'Failed to save signature image.'], 500);
            }
        } catch (\Exception $e) {
            Log::error('SignatureController::store - Exception while saving signature image', [
                'path' => $imageFullPath,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['success' => false, 'message' => 'Exception while saving signature image: ' . $e->getMessage()], 500);
        }

        // Clean up
        imagedestroy($image);
        imagedestroy($newImage);

        // Store the signature record in the database
        $signature = Signature::create([
            'certificate_id' => $request->certificate_id,
            'signatory_name' => $request->signatory_name,
            'role' => $request->role,
            'signature_date' => now(),
            'signature_data' => 'placeholder', // You can store a placeholder or remove this field
            'signature_image_path' => $imagePath,
            'contract_text' => $request->input('contract_text'),
            'ip_address' => $request->ip(), // Save the IP address
            'signature_type' => $signatureType, // Save the signature type (draw or type)
        ]);

        return response()->json([
            'success' => true,
            'signature' => [
                'id' => $signature->id,
                'signatory_name' => $signature->signatory_name,
                'role' => $signature->role,
            ]
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Signature $signature)
    {
        try {
            $signature->delete();
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
