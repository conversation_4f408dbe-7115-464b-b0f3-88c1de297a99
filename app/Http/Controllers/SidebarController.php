<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class SidebarController extends Controller
{
    /**
     * Update the sidebar collapsed state in the session
     */
    public function updateState(Request $request)
    {
        $collapsed = $request->input('collapsed', false);
        
        // Store in session
        session(['sidebar_collapsed' => $collapsed]);
        
        return response()->json(['success' => true]);
    }
}
