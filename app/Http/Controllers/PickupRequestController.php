<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Event;
use App\Models\GlobalConfig;
use App\Models\PickupRequest;
use App\Mail\TemplatedMail;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class PickupRequestController extends Controller
{
    /**
     * Display a listing of pickup requests.
     */
    public function index(Request $request)
    {
        $query = PickupRequest::with(['customer', 'event']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        } else {
            // Default to showing incoming, pending, and confirmed requests (active requests)
            $query->whereIn('status', ['incoming', 'pending', 'confirmed']);
        }

        // Filter out past pickup requests by default (only show today and future)
        // Allow override with a 'show_past' parameter
        if (!$request->boolean('show_past', false)) {
            $query->where(function ($q) {
                $today = now()->startOfDay();
                $q->where('preferred_pickup_date', '>=', $today)
                  ->orWhereNull('preferred_pickup_date');
            });
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('contact_name', 'LIKE', "%{$search}%")
                    ->orWhere('business_name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%")
                    ->orWhere('phone', 'LIKE', "%{$search}%")
                    ->orWhere('pickup_address', 'LIKE', "%{$search}%");
            });
        }

        // Handle sorting
        $sort = $request->get('sort', 'preferred_pickup_date'); // Default to preferred_pickup_date
        $direction = $request->get('direction', 'asc'); // Default to ascending for dates (earliest first)
        
        switch ($sort) {
            case 'id':
                $query->orderBy('id', $direction);
                break;
            case 'contact_name':
                $query->orderBy('contact_name', $direction);
                break;
            case 'submitted_at':
                $query->orderBy('submitted_at', $direction);
                break;
            case 'preferred_pickup_date':
                $query->orderBy('preferred_pickup_date', $direction);
                break;
            case 'status':
                $query->orderBy('status', $direction);
                break;
            default:
                // Default to pickup date (earliest first) - starting from today going forward
                $query->orderBy('preferred_pickup_date', 'asc');
        }

        // Handle pagination
        $pagination = $request->get('pagination', 25);
        $pickupRequests = $query->paginate($pagination);

        // Get status counts for filter badges
        $statusCounts = [
            'incoming' => PickupRequest::where('status', 'incoming')->count(),
            'pending' => PickupRequest::where('status', 'pending')->count(),
            'confirmed' => PickupRequest::where('status', 'confirmed')->count(),
            'completed' => PickupRequest::where('status', 'completed')->count(),
            'cancelled' => PickupRequest::where('status', 'cancelled')->count(),
        ];

        // Get today's count (pickups scheduled for today based on preferred_pickup_date or event start_date)
        // Laravel automatically handles timezone - no manual conversion needed
        $today = now()->startOfDay();
        $todayEnd = now()->endOfDay();

        $todayCount = PickupRequest::where(function ($query) use ($today, $todayEnd) {
            $query->whereBetween('preferred_pickup_date', [$today, $todayEnd])
                ->orWhereHas('event', function ($eventQuery) use ($today, $todayEnd) {
                    $eventQuery->whereBetween('start_date', [$today, $todayEnd]);
                });
        })->whereIn('status', ['incoming', 'pending', 'confirmed'])->count();

        return view('pickup-requests.index', compact('pickupRequests', 'statusCounts', 'pagination', 'todayCount'));
    }

    /**
     * Show the form for creating a new pickup request.
     */
    public function create(Request $request)
    {
        // Get pickup availability windows for display
        $availabilityWindows = GlobalConfig::getPickupAvailabilityWindows();
        $pickupEventDuration = GlobalConfig::getPickupEventDuration();
        $pickupSchedulingInterval = GlobalConfig::getPickupSchedulingInterval();

        // Get draft data from session
        $draftData = session('pickup_request_draft', []);

        // Check for start and end datetime parameters from calendar drag-to-create
        if ($request->has('start') && $request->has('end')) {
            try {
                // No manual timezone conversion needed - Laravel handles it automatically
                // Just parse the datetime strings
                $startDateTime = Carbon::parse($request->get('start'));
                $endDateTime = Carbon::parse($request->get('end'));

                \Log::info('Calendar drag-to-create times received', [
                    'start_param' => $request->get('start'),
                    'end_param' => $request->get('end'),
                    'parsed_start' => $startDateTime->toDateTimeString(),
                    'parsed_end' => $endDateTime->toDateTimeString(),
                ]);

                // Override draft data with calendar selection
                // Format for datetime-local input (Y-m-d\TH:i)
                $draftData['preferred_pickup_date'] = $startDateTime->format('Y-m-d\TH:i');

                // Store the end time for potential future use
                $draftData['preferred_pickup_end_date'] = $endDateTime->format('Y-m-d\TH:i');

                // Save to session so it persists during form editing
                session(['pickup_request_draft' => $draftData]);

            } catch (\Exception $e) {
                // If datetime parsing fails, continue without pre-filling
                \Log::warning('Failed to parse datetime parameters in pickup request create', [
                    'start' => $request->get('start'),
                    'end' => $request->get('end'),
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // If there's a customer_id in draft data, fetch the customer name for the component
        $selectedCustomerName = null;
        if (! empty($draftData['customer_id'])) {
            $customer = \App\Models\Customer::find($draftData['customer_id']);
            if ($customer) {
                $selectedCustomerName = $customer->name;
            }
        }

        // Get available drivers (users who can be assigned to pickup events)
        $drivers = \App\Models\User::where('enabled', true)
            ->orderBy('name')
            ->get(['id', 'name']);

        // Get the next available pickup slot as default if no draft data exists
        $nextAvailableSlot = null;
        if (empty($draftData['preferred_pickup_date'])) {
            $nextAvailableSlot = $this->getNextAvailableSlot();
        }

        // Get pickup item types
        $pickupItemTypes = GlobalConfig::getPickupItemTypes();
        if (empty($pickupItemTypes)) {
            $pickupItemTypes = GlobalConfig::getDefaultPickupItemTypes();
        }

        return view('pickup-requests.create', compact(
            'availabilityWindows',
            'pickupEventDuration',
            'pickupSchedulingInterval',
            'draftData',
            'selectedCustomerName',
            'drivers',
            'nextAvailableSlot',
            'pickupItemTypes'
        ));
    }

    /**
     * Store a newly created pickup request.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'contact_name' => 'required|string|max:255',
            'business_name' => 'nullable|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:255',
            'pickup_address' => 'required|string|max:1000',
            'property_location_details' => 'required|string|max:1000',
            'other_notes' => 'nullable|string|max:1000',
            'preferred_pickup_date' => 'required|date|after_or_equal:today',
            'staff_needed' => 'required|integer|min:1|max:10',
            'assigned_driver_id' => 'nullable|exists:users,id',
            // New guided pickup fields (required for new forms)
            'load_size' => 'required|in:small,medium,large',
            'item_types' => 'required|array|min:1',
            'item_types.*' => 'string|in:small_electronics,appliances,peripheral_devices,batteries,crt_tvs,flatscreen_tvs,tote_swap,gaylord_swap,servers,laptops,desktops,large_appliances,other',
            'item_specifics' => 'required|string|max:1000',
            'driver_instructions' => 'nullable|string|max:1000',
            'accessibility_level' => 'required|in:easy,moderate,difficult',
            // Legacy fields (optional for backward compatibility)
            'pickup_items' => 'nullable|string|max:1000',
            'pickup_quantity' => 'nullable|string|max:500',
            'internal_pickup_notes' => 'nullable|string|max:1000',
        ]);

        // Handle timezone conversion for preferred pickup date (following EventController pattern)
        if ($validated['preferred_pickup_date']) {
            // No manual timezone conversion needed - Laravel handles it automatically
            // Just parse the datetime string
            $validated['preferred_pickup_date'] = Carbon::parse($validated['preferred_pickup_date']);
        }

        try {
            DB::beginTransaction();

            // Get pickup calendar
            $pickupCalendarId = GlobalConfig::getPickupCalendarId();

            if (! $pickupCalendarId) {
                throw new \Exception('Pickup calendar not configured. Please configure the pickup calendar in global settings.');
            }

            // Extract event-specific fields from validated data
            $eventFields = [
                'staff_needed' => $validated['staff_needed'],
                'assigned_driver_id' => $validated['assigned_driver_id'],
            ];

            // Remove event fields and internal notes from pickup request data
            $pickupRequestData = array_diff_key($validated, array_flip(['staff_needed', 'assigned_driver_id', 'internal_pickup_notes']));

            // Create the pickup request with customer already linked and status as 'pending'
            $pickupRequest = PickupRequest::create(array_merge($pickupRequestData, [
                'status' => 'pending', // Set to pending since event is created but customer hasn't confirmed yet
                'submitted_at' => now(),
            ]));

            // Handle internal pickup notes separately
            if (isset($validated['internal_pickup_notes'])) {
                $pickupRequest->updateInternalStaffNotes($validated['internal_pickup_notes']);
            }

            // Store additional metadata in JSON format
            $pickupRequest->updatePickupDetails([
                'submission' => [
                    'source' => 'staff_backend',
                    'created_by_user_id' => Auth::id(),
                    'created_by_user_name' => Auth::user()->name,
                    'submitted_at' => now()->toISOString(),
                ],
                'form_data' => [
                    'original_submission' => $validated,
                ],
            ]);

            // Create pickup event automatically
            // Parse the pickup date/time in the site's timezone (admin interface uses local timezone)
            $timezone = GlobalConfig::getTimeZone();
            $pickupDateTime = Carbon::parse($validated['preferred_pickup_date'], $timezone);
            $eventDuration = GlobalConfig::getPickupEventDuration(); // in minutes
            $endDateTime = $pickupDateTime->copy()->addMinutes($eventDuration);

            // Load the customer relationship
            $pickupRequest->load('customer');

            // Create pickup event with comprehensive details
            $event = Event::create([
                'calendar_id' => $pickupCalendarId,
                'title' => "Pickup: {$pickupRequest->customer->name}",
                'description' => $pickupRequest->getPickupSummary(),
                'start_date' => $pickupDateTime,
                'end_date' => $endDateTime,
                'all_day' => false,
                'creator_id' => Auth::id(),
                'location' => $pickupRequest->pickup_address,
                'is_pickup_event' => true,
                'customer_id' => $pickupRequest->customer_id,
                'is_active' => true,
                // Pickup-specific fields
                'pickup_address' => $pickupRequest->pickup_address,
                'pickup_items' => $pickupRequest->pickup_items,
                'staff_needed' => $eventFields['staff_needed'],
                'assigned_driver_id' => $eventFields['assigned_driver_id'],
                'contact_name' => $pickupRequest->contact_name,
                'contact_phone' => $pickupRequest->phone,
                'contact_email' => $pickupRequest->email,

            ]);

            // Link the event to the pickup request
            $pickupRequest->update([
                'event_id' => $event->id,
            ]);

            // Log the pickup request creation (staff created with customer already linked)
            $pickupRequest->logCreation('staff', Auth::user()->name);

            // Log the customer linking (since customer was pre-selected)
            $pickupRequest->logCustomerLinked($pickupRequest->customer, Auth::user()->name);

            // Log the appointment scheduling
            $pickupRequest->logAppointmentScheduled($event, Auth::user()->name);

            DB::commit();

            // Clear the draft data from session
            session()->forget('pickup_request_draft');

            // Send email notifications to configured email addresses
            try {
                $notificationEmails = GlobalConfig::getPickupSchedulingNotificationEmails();

                if (! empty($notificationEmails)) {
                    $emailTemplateService = app(\App\Services\EmailTemplateService::class);

                    // Check if we have a custom template
                    if ($emailTemplateService->templateExists('pickup_request_notification')) {
                        // Use the custom template
                        $templateData = [
                            'pickup_request' => $pickupRequest,
                            'title' => 'New Pickup Request Created by Staff',
                            'type' => 'new_request',
                        ];

                        foreach ($notificationEmails as $email) {
                            \Mail::to($email)->queue(new \App\Mail\TemplatedMail('pickup_request_notification', $templateData));
                        }
                    } else {
                        // Fall back to the original mail class
                        $emailData = [
                            'pickupRequest' => $pickupRequest,
                            'title' => 'New Pickup Request Created by Staff',
                            'type' => 'new_request',
                        ];

                        foreach ($notificationEmails as $email) {
                            \Mail::to($email)->queue(new \App\Mail\PickupRequestNotification($emailData));
                        }
                    }

                    \Log::info('Pickup request email notifications sent successfully', [
                        'pickup_request_id' => $pickupRequest->id,
                        'email_count' => count($notificationEmails),
                        'created_by' => Auth::user()->name,
                    ]);
                }
            } catch (\Exception $e) {
                // Log the error but don't fail the pickup request creation
                \Log::error('Failed to send pickup request email notifications', [
                    'pickup_request_id' => $pickupRequest->id,
                    'error' => $e->getMessage(),
                ]);
            }

            return redirect()->route('pickup-requests.show', $pickupRequest)
                ->with('success', 'Pickup request and event created successfully! The pickup is now scheduled on the calendar.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create pickup request and event: '.$e->getMessage(), [
                'user_id' => Auth::id(),
                'validated_data' => $validated,
                'exception' => $e,
            ]);

            return back()->withInput()->withErrors([
                'general' => 'Failed to create pickup request and event: '.$e->getMessage(),
            ]);
        }
    }

    /**
     * Auto-save pickup request fields during creation.
     */
    public function autoSave(Request $request)
    {
        try {
            $field = $request->input('field');
            $value = $request->input('value');
            $sessionKey = 'pickup_request_draft';

            // Get existing draft data from session
            $draftData = session($sessionKey, []);

            // Validate based on field type
            switch ($field) {
                case 'customer_id':
                    // Allow empty value for deselection, otherwise must exist in customers table
                    $request->validate(['value' => 'nullable|exists:customers,id']);
                    break;
                case 'contact_name':
                    $request->validate(['value' => 'required|string|max:255']);
                    break;
                case 'business_name':
                    $request->validate(['value' => 'nullable|string|max:255']);
                    break;
                case 'email':
                    $request->validate(['value' => 'required|email|max:255']);
                    break;
                case 'phone':
                    $request->validate(['value' => 'required|string|max:255']);
                    break;
                case 'pickup_address':
                    $request->validate(['value' => 'required|string|max:1000']);
                    break;
                case 'pickup_items':
                    $request->validate(['value' => 'nullable|string|max:1000']);
                    break;
                case 'pickup_quantity':
                    $request->validate(['value' => 'nullable|string|max:500']);
                    break;
                case 'property_location_details':
                    $request->validate(['value' => 'required|string|max:1000']);
                    break;
                case 'other_notes':
                    $request->validate(['value' => 'nullable|string|max:1000']);
                    break;
                case 'preferred_pickup_date':
                    $request->validate(['value' => 'required|date|after_or_equal:today']);
                    break;
                case 'staff_needed':
                    $request->validate(['value' => 'required|integer|min:1|max:10']);
                    break;
                case 'assigned_driver_id':
                    $request->validate(['value' => 'nullable|exists:users,id']);
                    break;
                case 'internal_pickup_notes':
                    $request->validate(['value' => 'nullable|string|max:1000']);
                    break;
                    // New guided pickup fields
                case 'load_size':
                    $request->validate(['value' => 'nullable|in:small,medium,large']);
                    break;
                case 'item_types':
                    $request->validate(['value' => 'nullable|array']);
                    break;
                case 'item_specifics':
                    $request->validate(['value' => 'nullable|string|max:1000']);
                    break;
                case 'driver_instructions':
                    $request->validate(['value' => 'nullable|string|max:1000']);
                    break;
                case 'accessibility_level':
                    $request->validate(['value' => 'nullable|in:easy,moderate,difficult']);
                    break;
                default:
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid field name',
                    ], 400);
            }

            // Update the draft data
            $draftData[$field] = $value;
            session([$sessionKey => $draftData]);

            return response()->json([
                'success' => true,
                'message' => ucfirst(str_replace('_', ' ', $field)).' saved to draft',
                'field' => $field,
                'value' => $value,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get the next available pickup slot.
     */
    private function getNextAvailableSlot()
    {
        try {
            // Get pickup calendar and settings
            $pickupCalendarId = GlobalConfig::getPickupCalendarId();
            if (! $pickupCalendarId) {
                return null;
            }

            $availabilityWindows = GlobalConfig::getPickupAvailabilityWindows();
            $eventDuration = GlobalConfig::getPickupEventDuration();
            $bufferTime = GlobalConfig::getPickupBufferTime();
            $bufferDirection = GlobalConfig::getPickupBufferDirection();
            $minimumPickupDate = GlobalConfig::getMinimumPickupDate();

            // Start checking from the minimum pickup date
            $currentDate = $minimumPickupDate->copy();
            $maxDate = $currentDate->copy()->addDays(21); // Check up to 3 weeks ahead

            // Get existing events and pickup requests for the period
            $existingEvents = Event::where('calendar_id', $pickupCalendarId)
                ->where('is_active', true)
                ->whereBetween('start_date', [$currentDate->startOfDay(), $maxDate->endOfDay()])
                ->get();

            $existingPickupRequests = PickupRequest::whereIn('status', ['incoming', 'pending', 'confirmed'])
                ->whereBetween('preferred_pickup_date', [$currentDate->startOfDay(), $maxDate->endOfDay()])
                ->get();

            // Check each day until we find an available slot
            while ($currentDate->lte($maxDate)) {
                $dayOfWeek = $currentDate->dayOfWeek;
                $timeSlots = GlobalConfig::getPickupTimeSlots((string) $dayOfWeek, $availabilityWindows);

                foreach ($timeSlots as $timeSlot) {
                    $slotStart = $currentDate->copy()->setTimeFromTimeString($timeSlot);
                    $slotEnd = $slotStart->copy()->addMinutes($eventDuration);

                    $hasConflict = false;

                    // Check conflicts with existing events
                    foreach ($existingEvents as $event) {
                        $eventStart = Carbon::parse($event->start_date);
                        $eventEnd = Carbon::parse($event->end_date);

                        // For all-day events, block the entire day
                        if ($event->all_day) {
                            // Check if the slot date matches the all-day event date
                            if ($slotStart->toDateString() === $eventStart->toDateString()) {
                                $hasConflict = true;
                                break;
                            }
                        } else {
                            // Apply buffer time for timed events
                            $bufferedStart = $eventStart->copy();
                            $bufferedEnd = $eventEnd->copy();

                            if ($bufferDirection === 'before' || $bufferDirection === 'both') {
                                $bufferedStart->subMinutes($bufferTime);
                            }
                            if ($bufferDirection === 'after' || $bufferDirection === 'both') {
                                $bufferedEnd->addMinutes($bufferTime);
                            }

                            if ($slotStart < $bufferedEnd && $slotEnd > $bufferedStart) {
                                $hasConflict = true;
                                break;
                            }
                        }
                    }

                    // Check conflicts with pickup requests
                    if (! $hasConflict) {
                        foreach ($existingPickupRequests as $pickupRequest) {
                            $requestStart = Carbon::parse($pickupRequest->preferred_pickup_date);
                            $requestEnd = $requestStart->copy()->addMinutes($eventDuration);

                            // Apply buffer time
                            $bufferedStart = $requestStart->copy();
                            $bufferedEnd = $requestEnd->copy();

                            if ($bufferDirection === 'before' || $bufferDirection === 'both') {
                                $bufferedStart->subMinutes($bufferTime);
                            }
                            if ($bufferDirection === 'after' || $bufferDirection === 'both') {
                                $bufferedEnd->addMinutes($bufferTime);
                            }

                            if ($slotStart < $bufferedEnd && $slotEnd > $bufferedStart) {
                                $hasConflict = true;
                                break;
                            }
                        }
                    }

                    // If no conflict, this is our next available slot
                    if (! $hasConflict) {
                        return $slotStart->format('Y-m-d\TH:i:s');
                    }
                }

                $currentDate->addDay();
            }

            return null; // No available slots found
        } catch (\Exception $e) {
            \Log::error('Error getting next available pickup slot: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Clear draft data from session.
     */
    public function clearDraft()
    {
        try {
            session()->forget('pickup_request_draft');

            return response()->json([
                'success' => true,
                'message' => 'Draft data cleared successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear draft data: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get customer data for auto-import.
     */
    public function getCustomerData(Customer $customer)
    {
        return response()->json([
            'success' => true,
            'customer' => [
                'id' => $customer->id,
                'name' => $customer->name,
                'type' => $customer->type,
                'contact' => $customer->contact,
                'nickname' => $customer->nickname,
                'email' => $customer->email,
                'phone' => $customer->phone,
                'address' => $customer->address,
                'notes' => $customer->notes,
                'website' => $customer->website,
            ],
        ]);
    }

    /**
     * Display the specified pickup request.
     */
    public function show(PickupRequest $pickupRequest)
    {
        $pickupRequest->load(['customer', 'event.assignedDriver', 'images']);

        // Get available drivers (users who can be assigned to pickup events)
        $availableDrivers = \App\Models\User::where('enabled', true)
            ->orderBy('name')
            ->get(['id', 'name']);

        // Calculate distance and travel time from warehouse to pickup location
        $distanceData = null;
        $warehouseLocation = \App\Models\GlobalConfig::getWarehouseLocation();

        if ($warehouseLocation && $pickupRequest->pickup_address) {
            try {
                $googleMapsService = app(\App\Services\GoogleMapsService::class);
                $distanceData = $googleMapsService->getDistanceMatrix(
                    $warehouseLocation,
                    $pickupRequest->pickup_address,
                    ['units' => 'imperial', 'mode' => 'driving']
                );
            } catch (\Exception $e) {
                \Log::error('Error calculating distance for pickup request', [
                    'pickup_request_id' => $pickupRequest->id,
                    'warehouse_location' => $warehouseLocation,
                    'pickup_address' => $pickupRequest->pickup_address,
                    'error' => $e->getMessage(),
                ]);

                $distanceData = [
                    'success' => false,
                    'error' => 'Could not calculate distance',
                    'distance_text' => 'Unavailable',
                    'duration_text' => 'Unavailable',
                ];
            }
        }

        return view('pickup-requests.show', compact('pickupRequest', 'availableDrivers', 'distanceData', 'warehouseLocation'));
    }

    /**
     * Get distance data for a pickup request via AJAX.
     */
    public function getDistance(PickupRequest $pickupRequest)
    {
        // Calculate distance and travel time from warehouse to pickup location
        $distanceData = null;
        $warehouseLocation = \App\Models\GlobalConfig::getWarehouseLocation();

        if ($warehouseLocation && $pickupRequest->pickup_address) {
            try {
                $googleMapsService = app(\App\Services\GoogleMapsService::class);
                $distanceData = $googleMapsService->getDistanceMatrix(
                    $warehouseLocation,
                    $pickupRequest->pickup_address,
                    ['units' => 'imperial', 'mode' => 'driving']
                );
            } catch (\Exception $e) {
                \Log::error('Error calculating distance for pickup request', [
                    'pickup_request_id' => $pickupRequest->id,
                    'warehouse_location' => $warehouseLocation,
                    'pickup_address' => $pickupRequest->pickup_address,
                    'error' => $e->getMessage(),
                ]);

                $distanceData = [
                    'success' => false,
                    'error' => 'Could not calculate distance',
                    'distance_text' => 'Unavailable',
                    'duration_text' => 'Unavailable',
                ];
            }
        } else {
            $distanceData = [
                'success' => false,
                'error' => $warehouseLocation ? 'No pickup address' : 'Warehouse location not configured',
                'distance_text' => $warehouseLocation ? 'No address' : 'Warehouse not configured',
                'duration_text' => $warehouseLocation ? 'No address' : 'Warehouse not configured',
            ];
        }

        return response()->json([
            'distance_data' => $distanceData,
            'warehouse_location' => $warehouseLocation,
        ]);
    }

    /**
     * Link a pickup request to a customer.
     */
    public function linkCustomer(Request $request, PickupRequest $pickupRequest)
    {
        $request->validate([
            'customer_id' => 'required|exists:customers,id',
        ]);

        // Update the pickup request with customer and change status to pending
        $pickupRequest->update([
            'customer_id' => $request->customer_id,
            'status' => 'pending',
        ]);

        $customer = Customer::find($request->customer_id);

        return response()->json([
            'success' => true,
            'message' => "Pickup request linked to customer: {$customer->name}. Status updated to 'pending'.",
            'customer' => [
                'id' => $customer->id,
                'name' => $customer->name,
                'email' => $customer->email,
                'phone' => $customer->phone,
            ],
            'status' => 'pending',
        ]);
    }

    /**
     * Unlink a customer from a pickup request.
     */
    public function unlinkCustomer(PickupRequest $pickupRequest)
    {
        if (! $pickupRequest->customer_id) {
            return response()->json([
                'success' => false,
                'message' => 'No customer is currently linked to this request.',
            ], 400);
        }

        // Reset customer and status back to incoming
        $pickupRequest->update([
            'customer_id' => null,
            'status' => 'incoming',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Customer unlinked successfully. Request status reset to incoming.',
            'status' => 'incoming',
        ]);
    }

    /**
     * Finalize a pickup request by creating a pickup event.
     */
    public function finalize(Request $request, PickupRequest $pickupRequest)
    {
        $request->validate([
            'pickup_date' => 'required|date|after_or_equal:today',
            'pickup_time' => 'required|date_format:H:i',
            'staff_needed' => 'nullable|integer|min:1|max:10',
            'assigned_driver_id' => 'nullable|exists:users,id',
            'internal_pickup_notes' => 'nullable|string|max:1000',
        ]);

        if (! $pickupRequest->customer_id) {
            return response()->json([
                'success' => false,
                'message' => 'Customer must be linked before finalizing the request.',
            ], 400);
        }

        try {
            DB::beginTransaction();

            // Get pickup calendar
            $pickupCalendarId = GlobalConfig::getPickupCalendarId();
            if (! $pickupCalendarId) {
                throw new \Exception('Pickup calendar not configured.');
            }

            // Create pickup event
            // Parse the pickup date/time in the site's timezone (admin interface uses local timezone)
            $timezone = GlobalConfig::getTimeZone();
            $pickupDateTime = Carbon::parse($request->pickup_date.' '.$request->pickup_time, $timezone);
            $eventDuration = GlobalConfig::getPickupEventDuration(); // in minutes
            $endDateTime = $pickupDateTime->copy()->addMinutes($eventDuration);

            // Create pickup event with comprehensive details
            $event = Event::create([
                'calendar_id' => $pickupCalendarId,
                'title' => "Pickup: {$pickupRequest->customer->name}",
                'description' => $pickupRequest->getPickupSummary(),
                'start_date' => $pickupDateTime,
                'end_date' => $endDateTime,
                'all_day' => false,
                'creator_id' => Auth::id(),
                'location' => $pickupRequest->pickup_address,
                'is_pickup_event' => true,
                'customer_id' => $pickupRequest->customer_id,
                // Individual fields for backward compatibility
                'pickup_address' => $pickupRequest->pickup_address,
                'pickup_items' => $pickupRequest->pickup_items,
                'staff_needed' => $request->staff_needed,
                'assigned_driver_id' => $request->assigned_driver_id,
                'contact_name' => $pickupRequest->contact_name,
                'contact_phone' => $pickupRequest->phone,
                'contact_email' => $pickupRequest->email,

                'is_active' => true,
            ]);

            // No additional processing needed - all fields are set in the create call above

            // Handle internal pickup notes
            if ($request->internal_pickup_notes) {
                $pickupRequest->updateInternalStaffNotes($request->internal_pickup_notes);
            }

            // Update pickup request
            $pickupRequest->update([
                'status' => 'pending',
                'event_id' => $event->id,
            ]);

            // Log the appointment scheduling
            $pickupRequest->logAppointmentScheduled($event, Auth::user()->name);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Pickup request finalized and event created successfully.',
                'event_id' => $event->id,
                'status' => 'pending',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error finalizing pickup request: '.$e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while finalizing the pickup request.',
            ], 500);
        }
    }

    /**
     * Get week view data for a proposed pickup time (general - no specific pickup request).
     * Supports 3-month range for navigation.
     */
    public function getGeneralWeekView(Request $request)
    {
        $request->validate([
            'pickup_date' => 'required|date',
            'pickup_time' => 'required|date_format:H:i',
            'view_date' => 'nullable|date', // Optional date for which week to view
        ]);

        // Parse the pickup date/time in the site's timezone (admin interface uses local timezone)
        $timezone = GlobalConfig::getTimeZone();
        $pickupDateTime = Carbon::parse($request->pickup_date.' '.$request->pickup_time, $timezone);

        // Determine which week to view (for navigation)
        $viewDate = $request->view_date ? Carbon::parse($request->view_date, $timezone) : $pickupDateTime;
        $weekStart = $viewDate->copy()->startOfWeek(Carbon::MONDAY);
        $weekEnd = $weekStart->copy()->endOfWeek(Carbon::SUNDAY);

        // Calculate 3-month range boundaries for validation
        // Allow navigation back to the start of the current week
        $rangeStart = Carbon::now($timezone)->startOfWeek(Carbon::MONDAY);
        $rangeEnd = $rangeStart->copy()->addMonths(3)->endOfDay();

        // Validate that the requested week is within the 3-month range
        if ($weekStart->gt($rangeEnd) || $weekEnd->lt($rangeStart)) {
            return response()->json([
                'error' => 'Requested week is outside the available 3-month range.',
            ], 400);
        }

        // Get pickup calendar ID
        $pickupCalendarId = GlobalConfig::getPickupCalendarId();
        if (! $pickupCalendarId) {
            return response()->json([
                'error' => 'Pickup calendar not configured.',
            ], 400);
        }

        // Get events for the week (only active events)
        $events = Event::where('calendar_id', $pickupCalendarId)
            ->where('is_active', true)
            ->where(function ($query) use ($weekStart, $weekEnd) {
                $query->whereBetween('start_date', [$weekStart, $weekEnd])
                    ->orWhereBetween('end_date', [$weekStart, $weekEnd])
                    ->orWhere(function ($q) use ($weekStart, $weekEnd) {
                        $q->where('start_date', '<=', $weekStart)
                            ->where('end_date', '>=', $weekEnd);
                    });
            })
            ->with(['customer'])
            ->get();

        // Get pending pickup requests for the week (all non-completed statuses)
        $pendingPickupRequests = PickupRequest::whereIn('status', ['incoming', 'pending', 'confirmed'])
            ->whereBetween('preferred_pickup_date', [$weekStart, $weekEnd])
            ->get();

        // Get availability windows and settings for generating time slots
        $availabilityWindows = GlobalConfig::getPickupAvailabilityWindows();
        $eventDuration = GlobalConfig::getPickupEventDuration();

        // Format events for the calendar - normalize to 30-minute blocks for display
        $formattedEvents = $events->map(function ($event) {
            $startTime = $event->start_date->format('g:i A');
            $endTime = $event->end_date->format('g:i A');
            $displayTitle = $event->title." ({$startTime}-{$endTime})";

            // For calendar display, make all events 30 minutes to prevent overlaps
            $displayEnd = $event->start_date->copy()->addMinutes(30);

            return [
                'id' => $event->id,
                'title' => $displayTitle,
                'start' => $event->start_date->toISOString(),
                'end' => $displayEnd->toISOString(),
                'actual_end' => $event->end_date->toISOString(),
                'customer' => $event->customer ? $event->customer->name : null,
                'is_pickup_event' => $event->is_pickup_event ?? false,
                'is_blockout_event' => $event->is_blockout_event ?? false,
                'className' => $event->is_blockout_event ? 'blockout-event' : ($event->is_pickup_event ? 'pickup-event' : 'regular-event'),
            ];
        });

        // Add the proposed pickup event
        $proposedEndTime = $pickupDateTime->copy()->addMinutes($eventDuration);
        $proposedStartTime = $pickupDateTime->format('g:i A');
        $proposedActualEndTime = $proposedEndTime->format('g:i A');
        $proposedDisplayEnd = $pickupDateTime->copy()->addMinutes(30); // 30-minute display block

        $proposedTitle = "Proposed Pickup ({$proposedStartTime}-{$proposedActualEndTime})";

        $proposedEvent = [
            'id' => 'proposed',
            'title' => $proposedTitle,
            'start' => $pickupDateTime->toISOString(),
            'end' => $proposedDisplayEnd->toISOString(),
            'actual_end' => $proposedEndTime->toISOString(),
            'is_pickup_event' => true,
            'is_proposed' => true,
            'className' => 'proposed-event',
        ];

        // Generate available time slots for each day of the week
        $availableSlots = [];
        $currentDay = $weekStart->copy();

        while ($currentDay->lte($weekEnd)) {
            $dayOfWeek = $currentDay->dayOfWeek;
            $timeSlots = GlobalConfig::getPickupTimeSlots((string) $dayOfWeek, $availabilityWindows);

            foreach ($timeSlots as $timeSlot) {
                $slotStart = Carbon::parse($currentDay->format('Y-m-d').' '.$timeSlot, $timezone);
                $slotEnd = $slotStart->copy()->addMinutes($eventDuration);

                // Skip past time slots for today
                if ($currentDay->isToday() && $slotStart->isPast()) {
                    continue;
                }

                // Check for conflicts with existing events
                $hasConflict = false;
                foreach ($events as $event) {
                    $eventStart = Carbon::parse($event->start_date);
                    $eventEnd = Carbon::parse($event->end_date);

                    // For all-day events, block the entire day
                    if ($event->all_day) {
                        // Check if the slot date matches the all-day event date
                        if ($slotStart->toDateString() === $eventStart->toDateString()) {
                            $hasConflict = true;
                            break;
                        }
                    } else {
                        // Check for overlap with timed events
                        if ($slotStart < $eventEnd && $slotEnd > $eventStart) {
                            $hasConflict = true;
                            break;
                        }
                    }
                }

                // Check for conflicts with pending pickup requests
                if (! $hasConflict) {
                    foreach ($pendingPickupRequests as $pendingRequest) {
                        $requestStart = Carbon::parse($pendingRequest->preferred_pickup_date);
                        $requestEnd = $requestStart->copy()->addMinutes($eventDuration);

                        // Check for overlap
                        if ($slotStart < $requestEnd && $slotEnd > $requestStart) {
                            $hasConflict = true;
                            break;
                        }
                    }
                }

                // Don't show slot if it conflicts with the proposed event
                if ($slotStart < $proposedEndTime && $slotEnd > $pickupDateTime) {
                    $hasConflict = true;
                }

                if (! $hasConflict) {
                    $slotStartTime = $slotStart->format('g:i A');
                    $slotActualEndTime = $slotEnd->format('g:i A');
                    $slotDisplayEnd = $slotStart->copy()->addMinutes(30); // 30-minute display block

                    $availableSlots[] = [
                        'id' => 'slot_'.$slotStart->format('Y-m-d_H-i'),
                        'title' => "Available ({$slotStartTime}-{$slotActualEndTime})",
                        'start' => $slotStart->toISOString(),
                        'end' => $slotDisplayEnd->toISOString(),
                        'actual_end' => $slotEnd->toISOString(),
                        'is_available_slot' => true,
                        'time_slot' => $timeSlot,
                        'date' => $currentDay->format('Y-m-d'),
                        'className' => 'available-slot',
                    ];
                }
            }

            $currentDay->addDay();
        }

        // Calculate navigation boundaries
        $prevWeek = $weekStart->copy()->subWeek();
        $nextWeek = $weekStart->copy()->addWeek();
        $canGoPrev = $prevWeek->gte($rangeStart);
        $canGoNext = $nextWeek->lte($rangeEnd);

        return response()->json([
            'events' => $formattedEvents->concat([$proposedEvent])->concat($availableSlots),
            'week_start' => $weekStart->toISOString(),
            'week_end' => $weekEnd->toISOString(),
            'proposed_event' => $proposedEvent,
            'available_slots' => $availableSlots,
            'navigation' => [
                'can_go_prev' => $canGoPrev,
                'can_go_next' => $canGoNext,
                'prev_week_start' => $canGoPrev ? $prevWeek->toISOString() : null,
                'next_week_start' => $canGoNext ? $nextWeek->toISOString() : null,
                'range_start' => $rangeStart->toISOString(),
                'range_end' => $rangeEnd->toISOString(),
                'current_week_start' => $weekStart->format('Y-m-d'),
                'current_week_end' => $weekEnd->format('Y-m-d'),
            ],
        ]);
    }

    /**
     * Get week view data for a proposed pickup time.
     * Supports 3-month range for navigation.
     */
    public function getWeekView(Request $request, PickupRequest $pickupRequest)
    {
        $request->validate([
            'pickup_date' => 'required|date',
            'pickup_time' => 'required|date_format:H:i',
            'view_date' => 'nullable|date', // Optional date for which week to view
        ]);

        // Parse the pickup date/time in the site's timezone (admin interface uses local timezone)
        $timezone = GlobalConfig::getTimeZone();
        $pickupDateTime = Carbon::parse($request->pickup_date.' '.$request->pickup_time, $timezone);

        // Determine which week to view (for navigation)
        $viewDate = $request->view_date ? Carbon::parse($request->view_date, $timezone) : $pickupDateTime;
        $weekStart = $viewDate->copy()->startOfWeek(Carbon::MONDAY);
        $weekEnd = $weekStart->copy()->endOfWeek(Carbon::SUNDAY);

        // Calculate 3-month range boundaries for validation
        // Allow navigation back to the start of the current week
        $rangeStart = Carbon::now($timezone)->startOfWeek(Carbon::MONDAY);
        $rangeEnd = $rangeStart->copy()->addMonths(3)->endOfDay();

        // Get pickup calendar ID
        $pickupCalendarId = GlobalConfig::getPickupCalendarId();
        if (! $pickupCalendarId) {
            return response()->json([
                'error' => 'Pickup calendar not configured.',
            ], 400);
        }

        // Get events for the week (only active events)
        $events = Event::where('calendar_id', $pickupCalendarId)
            ->where('is_active', true)
            ->where(function ($query) use ($weekStart, $weekEnd) {
                $query->whereBetween('start_date', [$weekStart, $weekEnd])
                    ->orWhereBetween('end_date', [$weekStart, $weekEnd])
                    ->orWhere(function ($q) use ($weekStart, $weekEnd) {
                        $q->where('start_date', '<=', $weekStart)
                            ->where('end_date', '>=', $weekEnd);
                    });
            })
            ->with(['customer'])
            ->get();

        // Get pending pickup requests for the week (excluding the current one being scheduled)
        $pendingPickupRequests = PickupRequest::whereIn('status', ['incoming', 'pending', 'confirmed'])
            ->where('id', '!=', $pickupRequest->id) // Exclude the current request being scheduled
            ->whereBetween('preferred_pickup_date', [$weekStart, $weekEnd])
            ->get();

        // Get availability windows and settings for generating time slots
        $availabilityWindows = GlobalConfig::getPickupAvailabilityWindows();
        $eventDuration = GlobalConfig::getPickupEventDuration();

        // Format events for the calendar - normalize to 30-minute blocks for display
        $formattedEvents = $events->map(function ($event) {
            $startTime = $event->start_date->format('g:i A');
            $endTime = $event->end_date->format('g:i A');
            $displayTitle = $event->title." ({$startTime}-{$endTime})";

            // For calendar display, make all events 30 minutes to prevent overlaps
            $displayEnd = $event->start_date->copy()->addMinutes(30);

            return [
                'id' => $event->id,
                'title' => $displayTitle,
                'start' => $event->start_date->toISOString(),
                'end' => $displayEnd->toISOString(),
                'actual_end' => $event->end_date->toISOString(),
                'customer' => $event->customer ? $event->customer->name : null,
                'is_pickup_event' => $event->is_pickup_event ?? false,
                'is_blockout_event' => $event->is_blockout_event ?? false,
                'className' => $event->is_blockout_event ? 'blockout-event' : ($event->is_pickup_event ? 'pickup-event' : 'regular-event'),
            ];
        });

        // Add the proposed pickup event
        $proposedEndTime = $pickupDateTime->copy()->addMinutes($eventDuration);
        $proposedStartTime = $pickupDateTime->format('g:i A');
        $proposedActualEndTime = $proposedEndTime->format('g:i A');
        $proposedDisplayEnd = $pickupDateTime->copy()->addMinutes(30); // 30-minute display block

        $customerName = $pickupRequest->customer ? $pickupRequest->customer->name : $pickupRequest->contact_name;
        $proposedTitle = "Proposed Pickup: {$customerName} ({$proposedStartTime}-{$proposedActualEndTime})";

        $proposedEvent = [
            'id' => 'proposed',
            'title' => $proposedTitle,
            'start' => $pickupDateTime->toISOString(),
            'end' => $proposedDisplayEnd->toISOString(),
            'actual_end' => $proposedEndTime->toISOString(),
            'customer' => $customerName,
            'is_pickup_event' => true,
            'is_proposed' => true,
            'className' => 'proposed-event',
        ];

        // Generate available time slots for each day of the week
        $availableSlots = [];
        $currentDay = $weekStart->copy();

        while ($currentDay->lte($weekEnd)) {
            $dayOfWeek = $currentDay->dayOfWeek;
            $timeSlots = GlobalConfig::getPickupTimeSlots((string) $dayOfWeek, $availabilityWindows);

            foreach ($timeSlots as $timeSlot) {
                $slotStart = Carbon::parse($currentDay->format('Y-m-d').' '.$timeSlot, $timezone);
                $slotEnd = $slotStart->copy()->addMinutes($eventDuration);

                // Skip past time slots for today
                if ($currentDay->isToday() && $slotStart->isPast()) {
                    continue;
                }

                // Check for conflicts with existing events
                $hasConflict = false;
                foreach ($events as $event) {
                    $eventStart = Carbon::parse($event->start_date);
                    $eventEnd = Carbon::parse($event->end_date);

                    // For all-day events, block the entire day
                    if ($event->all_day) {
                        // Check if the slot date matches the all-day event date
                        if ($slotStart->toDateString() === $eventStart->toDateString()) {
                            $hasConflict = true;
                            break;
                        }
                    } else {
                        // Check for overlap with timed events
                        if ($slotStart < $eventEnd && $slotEnd > $eventStart) {
                            $hasConflict = true;
                            break;
                        }
                    }
                }

                // Check for conflicts with pending pickup requests
                if (! $hasConflict) {
                    foreach ($pendingPickupRequests as $pendingRequest) {
                        $requestStart = Carbon::parse($pendingRequest->preferred_pickup_date);
                        $requestEnd = $requestStart->copy()->addMinutes($eventDuration);

                        // Check for overlap
                        if ($slotStart < $requestEnd && $slotEnd > $requestStart) {
                            $hasConflict = true;
                            break;
                        }
                    }
                }

                // Don't show slot if it conflicts with the proposed event
                if ($slotStart < $proposedEndTime && $slotEnd > $pickupDateTime) {
                    $hasConflict = true;
                }

                if (! $hasConflict) {
                    $slotStartTime = $slotStart->format('g:i A');
                    $slotActualEndTime = $slotEnd->format('g:i A');
                    $slotDisplayEnd = $slotStart->copy()->addMinutes(30); // 30-minute display block

                    $availableSlots[] = [
                        'id' => 'slot_'.$slotStart->format('Y-m-d_H-i'),
                        'title' => "Available ({$slotStartTime}-{$slotActualEndTime})",
                        'start' => $slotStart->toISOString(),
                        'end' => $slotDisplayEnd->toISOString(),
                        'actual_end' => $slotEnd->toISOString(),
                        'is_available_slot' => true,
                        'time_slot' => $timeSlot,
                        'date' => $currentDay->format('Y-m-d'),
                        'className' => 'available-slot',
                    ];
                }
            }

            $currentDay->addDay();
        }

        // Calculate navigation boundaries
        $prevWeek = $weekStart->copy()->subWeek();
        $nextWeek = $weekStart->copy()->addWeek();
        $canGoPrev = $prevWeek->gte($rangeStart);
        $canGoNext = $nextWeek->lte($rangeEnd);

        return response()->json([
            'events' => $formattedEvents->concat([$proposedEvent])->concat($availableSlots),
            'week_start' => $weekStart->toISOString(),
            'week_end' => $weekEnd->toISOString(),
            'proposed_event' => $proposedEvent,
            'available_slots' => $availableSlots,
            'navigation' => [
                'can_go_prev' => $canGoPrev,
                'can_go_next' => $canGoNext,
                'prev_week_start' => $canGoPrev ? $prevWeek->toISOString() : null,
                'next_week_start' => $canGoNext ? $nextWeek->toISOString() : null,
                'range_start' => $rangeStart->toISOString(),
                'range_end' => $rangeEnd->toISOString(),
                'current_week_start' => $weekStart->format('Y-m-d'),
                'current_week_end' => $weekEnd->format('Y-m-d'),
            ],
        ]);
    }

    /**
     * Confirm a pickup request (staff confirms customer response).
     */
    public function confirm(PickupRequest $pickupRequest)
    {
        if ($pickupRequest->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Only pending pickup requests can be confirmed.',
            ], 400);
        }

        $pickupRequest->update([
            'status' => 'confirmed',
            'confirmed_at' => now()
        ]);

        // Log the staff confirmation
        $pickupRequest->logStaffConfirmation(auth()->user()->name);

        return response()->json([
            'success' => true,
            'message' => 'Pickup request confirmed successfully. Customer has confirmed the appointment.',
            'status' => 'confirmed',
        ]);
    }

    /**
     * Unconfirm a pickup request (change status back to pending).
     */
    public function unconfirm(PickupRequest $pickupRequest)
    {
        if ($pickupRequest->status !== 'confirmed') {
            return response()->json([
                'success' => false,
                'message' => 'Only confirmed pickup requests can be marked as not confirmed.',
            ], 400);
        }

        $pickupRequest->update(['status' => 'pending']);

        // Log the unconfirmation
        $pickupRequest->logUnconfirmed(auth()->user()->name);

        return response()->json([
            'success' => true,
            'message' => 'Pickup request status changed back to pending. Customer confirmation removed.',
            'status' => 'pending',
        ]);
    }

    /**
     * Mark a pickup request as completed.
     */
    public function complete(PickupRequest $pickupRequest)
    {
        if ($pickupRequest->status !== 'confirmed') {
            return response()->json([
                'success' => false,
                'message' => 'Only confirmed pickup requests can be marked as completed.',
            ], 400);
        }

        // Check if the pickup has an associated event and if it's before the pickup time
        if ($pickupRequest->event) {
            // Laravel automatically handles timezone - no manual conversion needed
            $now = now();
            $pickupTime = $pickupRequest->event->start_date;

            if ($now->lt($pickupTime)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot complete pickup before scheduled time. The pickup is scheduled for '.$pickupTime->format('M j, Y \a\t g:i A').'.',
                    'pickup_time' => $pickupTime->format('M j, Y \a\t g:i A'),
                    'current_time' => $now->format('M j, Y \a\t g:i A'),
                ], 400);
            }
        }

        $pickupRequest->update(['status' => 'completed']);

        // Log the completion
        $pickupRequest->logCompletion(auth()->user()->name);

        return response()->json([
            'success' => true,
            'message' => 'Pickup request marked as completed.',
            'status' => 'completed',
        ]);
    }

    /**
     * Cancel a pickup request.
     */
    public function cancel(Request $request, PickupRequest $pickupRequest)
    {
        if (in_array($pickupRequest->status, ['completed', 'cancelled'])) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot cancel a completed or already cancelled request.',
            ], 400);
        }

        // Get the cancellation reason from the request
        $reason = $request->input('reason', 'Appointment cancelled by staff');

        // Delete the associated calendar event if it exists
        if ($pickupRequest->event) {
            $pickupRequest->event->delete();
            $pickupRequest->update(['event_id' => null]);
        }

        // Update the pickup request with cancellation details
        $pickupRequest->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $reason
        ]);

        // Log the cancellation
        $pickupRequest->logCancellation(auth()->user()->name, 'staff_backend', $reason);

        // Send cancellation email to customer
        if ($pickupRequest->email) {
            try {
                Mail::to($pickupRequest->email)->queue(new TemplatedMail('pickup_cancellation', [
                    'pickup_request' => $pickupRequest,
                    'cancellation_reason' => $reason,
                    'cancelled_by' => 'Staff'
                ]));
            } catch (\Exception $e) {
                \Log::error('Failed to send pickup cancellation email', [
                    'pickup_request_id' => $pickupRequest->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Pickup request cancelled successfully. A cancellation email has been sent to the customer.',
            'status' => 'cancelled',
        ]);
    }

    /**
     * Send pickup confirmation email to customer.
     */
    public function sendConfirmationEmail(Request $request, PickupRequest $pickupRequest)
    {
        // Check if force resend is requested
        $forceResend = $request->hasHeader('X-Force-Resend');

        try {
            // Use the shared method from the model
            $pickupRequest->sendConfirmationEmail($forceResend);

            $messageText = $forceResend
                ? 'Pickup details resent successfully to '.$pickupRequest->email.'.'
                : 'Pickup details sent successfully to '.$pickupRequest->email.'.';

            return response()->json([
                'success' => true,
                'message' => $messageText,
                'sent_to' => $pickupRequest->email,
                'sent_at' => now()->format('M j, Y \a\t g:i A'),
                'is_resend' => $forceResend,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Update internal staff notes for a pickup request.
     */
    public function updateInternalNotes(Request $request, PickupRequest $pickupRequest)
    {
        $validated = $request->validate([
            'notes' => 'nullable|string|max:2000',
        ]);

        try {
            $pickupRequest->updateInternalStaffNotes($validated['notes'] ?? '');

            return response()->json([
                'success' => true,
                'message' => 'Internal notes updated successfully.',
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update internal notes: '.$e->getMessage(), [
                'user_id' => Auth::id(),
                'pickup_request_id' => $pickupRequest->id,
                'notes' => $validated['notes'] ?? '',
                'exception' => $e,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update internal notes: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified pickup request.
     */
    public function edit(PickupRequest $pickupRequest)
    {
        // Load the pickup request with its relationships
        $pickupRequest->load(['customer', 'event']);

        // Get available drivers for the dropdown (all enabled users)
        $drivers = \App\Models\User::where('enabled', true)
            ->orderBy('name')
            ->get(['id', 'name']);

        // Get draft data from session for this specific pickup request
        $sessionKey = 'pickup_request_edit_draft_'.$pickupRequest->id;
        $draftData = session($sessionKey, []);

        // Get selected customer name if customer is linked
        $selectedCustomerName = null;
        if ($pickupRequest->customer) {
            $selectedCustomerName = $pickupRequest->customer->name;
        }

        // Get pickup item types
        $pickupItemTypes = GlobalConfig::getPickupItemTypes();
        if (empty($pickupItemTypes)) {
            $pickupItemTypes = GlobalConfig::getDefaultPickupItemTypes();
        }

        return view('pickup-requests.edit', compact(
            'pickupRequest',
            'drivers',
            'draftData',
            'selectedCustomerName',
            'pickupItemTypes'
        ));
    }

    /**
     * Update the specified pickup request.
     */
    public function update(Request $request, PickupRequest $pickupRequest)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'contact_name' => 'required|string|max:255',
            'business_name' => 'nullable|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:255',
            'pickup_address' => 'required|string|max:1000',
            'property_location_details' => 'required|string|max:1000',
            'other_notes' => 'nullable|string|max:1000',
            'preferred_pickup_date' => 'required|date',
            'staff_needed' => 'nullable|integer|min:1|max:10',
            'assigned_driver_id' => 'nullable|exists:users,id',
            'internal_pickup_notes' => 'nullable|string|max:1000',
            // New guided pickup fields (required for new forms, optional for legacy)
            'load_size' => 'nullable|in:small,medium,large',
            'item_types' => 'nullable|array',
            'item_types.*' => 'string|in:small_electronics,appliances,peripheral_devices,batteries,crt_tvs,flatscreen_tvs,tote_swap,gaylord_swap,servers,laptops,desktops,large_appliances,other',
            'item_specifics' => 'nullable|string|max:1000',
            'driver_instructions' => 'nullable|string|max:1000',
            'accessibility_level' => 'nullable|in:easy,moderate,difficult',
            // Legacy fields (for backward compatibility)
            'pickup_items' => 'nullable|string|max:1000',
            'pickup_quantity' => 'nullable|string|max:500',
        ]);

        // Handle timezone conversion for preferred pickup date (following EventController pattern)
        if ($validated['preferred_pickup_date']) {
            // No manual timezone conversion needed - Laravel handles it automatically
            // Just parse the datetime string
            $validated['preferred_pickup_date'] = Carbon::parse($validated['preferred_pickup_date']);
        }

        try {
            DB::beginTransaction();

            // Separate pickup request data from event data
            $eventData = [
                'staff_needed' => $validated['staff_needed'] ?? null,
                'assigned_driver_id' => $validated['assigned_driver_id'] ?? null,
            ];

            // Remove event fields and internal notes from pickup request data
            $pickupRequestData = array_diff_key($validated, array_flip(['staff_needed', 'assigned_driver_id', 'internal_pickup_notes']));

            // Update the pickup request
            $pickupRequest->update($pickupRequestData);

            // Handle internal pickup notes separately
            if (isset($validated['internal_pickup_notes'])) {
                $pickupRequest->updateInternalStaffNotes($validated['internal_pickup_notes']);
            }

            // Update additional metadata in JSON format
            $pickupRequest->updatePickupDetails([
                'last_edit' => [
                    'edited_by_user_id' => Auth::id(),
                    'edited_by_user_name' => Auth::user()->name,
                    'edited_at' => now()->toISOString(),
                ],
                'form_data' => [
                    'last_edit_submission' => $validated,
                ],
            ]);

            // If there's a linked event, update it with the new information
            if ($pickupRequest->event) {
                $event = $pickupRequest->event;

                // Prepare event data
                $eventData = [
                    'staff_needed' => $validated['staff_needed'] ?? 1,
                    'assigned_driver_id' => $validated['assigned_driver_id'],
                ];

                // Update event with pickup request data and event-specific fields
                $event->update([
                    'title' => 'Pickup: '.$pickupRequest->customer->name,
                    'description' => $this->buildEventDescriptionFromPickupRequest($pickupRequest),
                    'start_date' => $pickupRequest->preferred_pickup_date,
                    'end_date' => Carbon::parse($pickupRequest->preferred_pickup_date)->addMinutes(GlobalConfig::getPickupEventDuration()),
                    'location' => $pickupRequest->pickup_address,
                    'customer_id' => $pickupRequest->customer_id,
                    'assigned_driver_id' => $eventData['assigned_driver_id'],
                    'staff_needed' => $eventData['staff_needed'],
                ]);

                // Set pickup details from the updated pickup request
                $event->setPickupDetailsFromRequest($pickupRequest, [
                    'event_details' => $eventData,
                    'updated_at' => now()->toISOString(),
                ]);
            }

            // Clear the edit draft from session
            $sessionKey = 'pickup_request_edit_draft_'.$pickupRequest->id;
            session()->forget($sessionKey);

            DB::commit();

            return redirect()->route('pickup-requests.show', $pickupRequest)
                ->with('success', 'Pickup request updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update pickup request: '.$e->getMessage(), [
                'user_id' => Auth::id(),
                'pickup_request_id' => $pickupRequest->id,
                'validated_data' => $validated,
                'exception' => $e,
            ]);

            return back()->withInput()->withErrors([
                'general' => 'Failed to update pickup request: '.$e->getMessage(),
            ]);
        }
    }

    /**
     * Auto-save pickup request fields during editing.
     */
    public function autoSaveEdit(Request $request, PickupRequest $pickupRequest)
    {
        try {
            $field = $request->input('field');
            $value = $request->input('value');
            $sessionKey = 'pickup_request_edit_draft_'.$pickupRequest->id;

            // Handle internal pickup notes separately
            if ($field === 'internal_pickup_notes') {
                $pickupRequest->updateInternalStaffNotes($value ?? '');

                return response()->json([
                    'success' => true,
                    'message' => 'Internal pickup notes auto-saved successfully.',
                ]);
            }

            // Get existing draft data from session
            $draftData = session($sessionKey, []);

            // Validate the field value based on the field type
            $validationRules = [
                'customer_id' => 'nullable|exists:customers,id',
                'contact_name' => 'nullable|string|max:255',
                'business_name' => 'nullable|string|max:255',
                'email' => 'nullable|email|max:255',
                'phone' => 'nullable|string|max:255',
                'pickup_address' => 'nullable|string|max:1000',
                'pickup_items' => 'nullable|string|max:1000',
                'pickup_quantity' => 'nullable|string|max:500',
                'property_location_details' => 'nullable|string|max:1000',
                'other_notes' => 'nullable|string|max:1000',
                'preferred_pickup_date' => 'nullable|date',
                'staff_needed' => 'nullable|integer|min:1|max:10',
                'assigned_driver_id' => 'nullable|exists:users,id',
                'internal_pickup_notes' => 'nullable|string|max:1000',
                // New guided pickup fields
                'load_size' => 'nullable|in:small,medium,large',
                'item_types' => 'nullable|array',
                'item_types.*' => 'string|in:small_electronics,appliances,peripheral_devices,batteries,crt_tvs,flatscreen_tvs,tote_swap,gaylord_swap,servers,laptops,desktops,large_appliances,other',
                'item_specifics' => 'nullable|string|max:1000',
                'driver_instructions' => 'nullable|string|max:1000',
                'accessibility_level' => 'nullable|in:easy,moderate,difficult',
            ];

            if (isset($validationRules[$field])) {
                $request->validate([$field => $validationRules[$field]]);
            }

            // Update the draft data
            $draftData[$field] = $value;
            session([$sessionKey => $draftData]);

            return response()->json([
                'success' => true,
                'message' => ucfirst(str_replace('_', ' ', $field)).' saved to draft',
                'field' => $field,
                'value' => $value,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Build event description from pickup request data for updates.
     */
    private function buildEventDescriptionFromPickupRequest(PickupRequest $pickupRequest)
    {
        // Use the pickup request's getPickupSummary method which handles both legacy and guided fields
        return $pickupRequest->getPickupSummary();
    }

    /**
     * Generate a daily pickup PDF report.
     */
    public function generateDailyPickupPdf(Request $request)
    {
        // Validate the date parameter
        $request->validate([
            'date' => 'required|date',
            'status' => 'nullable|in:incoming,pending,confirmed,completed,cancelled',
            'include_upcoming_unconfirmed' => 'nullable|boolean',
        ]);

        $date = Carbon::parse($request->date);

        // No manual timezone conversion needed - Laravel handles it automatically
        $startOfDay = $date->copy()->startOfDay();
        $endOfDay = $date->copy()->endOfDay();

        // Build the query for pickup requests
        $query = PickupRequest::with(['customer', 'event.assignedDriver'])
            ->where(function ($q) use ($startOfDay, $endOfDay) {
                // Include requests with preferred pickup date on this day
                $q->whereBetween('preferred_pickup_date', [$startOfDay->utc(), $endOfDay->utc()])
                  // Also include requests with scheduled events on this day
                    ->orWhereHas('event', function ($eventQuery) use ($startOfDay, $endOfDay) {
                        $eventQuery->whereBetween('start_date', [$startOfDay->utc(), $endOfDay->utc()]);
                    });
            });

        // Filter by status if provided
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        } else {
            // Default to confirmed, completed, and pending pickups for the day
            $query->whereIn('status', ['confirmed', 'completed', 'pending']);
        }

        // Order by pickup time (event start_date first, then preferred_pickup_date)
        $pickupRequests = $query->get()->sortBy(function ($pickup) {
            if ($pickup->event && $pickup->event->start_date) {
                return $pickup->event->start_date;
            }

            return $pickup->preferred_pickup_date;
        });

        // Get unconfirmed pickups for the next 2 days if requested
        $upcomingUnconfirmed = collect();
        if ($request->boolean('include_upcoming_unconfirmed', true)) { // Default to true
            $tomorrow = $date->copy()->addDay();
            $dayAfterTomorrow = $date->copy()->addDays(2);

            $upcomingUnconfirmed = PickupRequest::with(['customer', 'event.assignedDriver'])
                ->where(function ($q) use ($tomorrow, $dayAfterTomorrow) {
                    // No manual timezone conversion needed - Laravel handles it automatically
                    $tomorrowStart = $tomorrow->copy()->startOfDay();
                    $dayAfterEnd = $dayAfterTomorrow->copy()->endOfDay();

                    // Include requests with preferred pickup date in next 2 days
                    $q->whereBetween('preferred_pickup_date', [$tomorrowStart, $dayAfterEnd])
                      // Also include requests with scheduled events in next 2 days
                        ->orWhereHas('event', function ($eventQuery) use ($tomorrowStart, $dayAfterEnd) {
                            $eventQuery->whereBetween('start_date', [$tomorrowStart, $dayAfterEnd]);
                        });
                })
                ->whereIn('status', ['incoming', 'pending']) // Only unconfirmed statuses
                ->get()
                ->sortBy(function ($pickup) {
                    if ($pickup->event && $pickup->event->start_date) {
                        return $pickup->event->start_date;
                    }

                    return $pickup->preferred_pickup_date;
                });
        }

        // Get pickup item types for human-readable names
        $pickupItemTypes = GlobalConfig::getPickupItemTypes();
        if (empty($pickupItemTypes)) {
            $pickupItemTypes = GlobalConfig::getDefaultPickupItemTypes();
        }

        // Create a lookup array for item type values to names
        $itemTypeLookup = [];
        foreach ($pickupItemTypes as $itemType) {
            $itemTypeLookup[$itemType['value']] = $itemType['name'];
        }

        // Calculate distance and travel time for each pickup request
        $warehouseLocation = GlobalConfig::getWarehouseLocation();
        $distanceDataMap = [];

        if ($warehouseLocation) {
            $googleMapsService = app(\App\Services\GoogleMapsService::class);

            // Combine all pickup requests for distance calculation
            $allPickups = $pickupRequests->concat($upcomingUnconfirmed);

            foreach ($allPickups as $pickup) {
                if ($pickup->pickup_address) {
                    try {
                        $distanceData = $googleMapsService->getDistanceMatrix(
                            $warehouseLocation,
                            $pickup->pickup_address,
                            ['units' => 'imperial', 'mode' => 'driving']
                        );
                        $distanceDataMap[$pickup->id] = $distanceData;
                    } catch (\Exception $e) {
                        \Log::error('Error calculating distance for pickup request in PDF', [
                            'pickup_request_id' => $pickup->id,
                            'error' => $e->getMessage(),
                        ]);

                        $distanceDataMap[$pickup->id] = [
                            'success' => false,
                            'distance_text' => 'Could not calculate',
                            'duration_text' => 'Could not calculate',
                        ];
                    }
                }
            }
        }

        // Prepare data for the PDF
        $pdfData = [
            'date' => $date,
            'dateFormatted' => $date->format('l, F j, Y'),
            'pickupRequests' => $pickupRequests,
            'upcomingUnconfirmed' => $upcomingUnconfirmed,
            'generatedAt' => now(),
            'generatedBy' => auth()->user()->name ?? 'System',
            'statusFilter' => $request->status,
            'totalPickups' => $pickupRequests->count(),
            'upcomingUnconfirmedCount' => $upcomingUnconfirmed->count(),
            'itemTypeLookup' => $itemTypeLookup,
            'includeUpcoming' => $request->boolean('include_upcoming_unconfirmed', true),
            'warehouseLocation' => $warehouseLocation,
            'distanceDataMap' => $distanceDataMap,
        ];

        // Generate the PDF
        $pdf = Pdf::loadView('pickup-requests.daily-pdf', $pdfData)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
                'defaultFont' => 'Arial',
                'dpi' => 150,
                'defaultMediaType' => 'print',
                'enable_javascript' => false,
                'font_height_ratio' => 1.1,
            ]);

        // Create filename
        $filename = 'daily-pickups-'.$date->format('Y-m-d').'.pdf';

        return $pdf->stream($filename);
    }

    /**
     * Upload image for pickup request (staff use).
     */
    public function uploadImage(Request $request, PickupRequest $pickupRequest)
    {
        try {
            $request->validate([
                'file' => 'required|image|mimes:jpeg,jpg,png,webp|max:10240', // 10MB max
            ]);

            // Check if pickup request already has 10 images (staff + customer images combined)
            if ($pickupRequest->images()->count() >= 10) {
                return response()->json([
                    'success' => false,
                    'message' => 'Maximum of 10 images allowed per pickup request.',
                ], 400);
            }

            $file = $request->file('file');
            $fileName = 'pickup_'.$pickupRequest->id.'_staff_'.uniqid().'.'.$file->getClientOriginalExtension();

            // Store the image
            $path = $file->storeAs('pickup-images', $fileName, 'public');

            // Create image record
            $image = \App\Models\Image::create([
                'image_path' => $path,
                'og_filename' => $file->getClientOriginalName(),
                'title' => 'Pickup Request #'.$pickupRequest->id.' - Staff Upload',
                'description' => 'Image uploaded by staff for pickup request',
                'alt_text' => 'Pickup request staff image',
                'uploaded_by' => auth()->id(),
                'context_type' => 'pickup_request',
                'context_id' => $pickupRequest->id,
            ]);

            // Attach image to pickup request
            $pickupRequest->images()->attach($image->id, [
                'sort_order' => $pickupRequest->images()->count() + 1,
            ]);

            return response()->json([
                'success' => true,
                'image' => [
                    'id' => $image->id,
                    'image_path' => $image->image_path,
                    'og_filename' => $image->og_filename,
                    'title' => $image->title,
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                ],
                'thumbnail_url' => \Illuminate\Support\Facades\Storage::disk('public')->url($image->image_path),
                'full_url' => \Illuminate\Support\Facades\Storage::disk('public')->url($image->image_path),
                'message' => 'Image uploaded successfully',
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error uploading pickup request image', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'pickup_request_id' => $pickupRequest->id,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while uploading the image. Please try again.',
            ], 500);
        }
    }

    /**
     * Delete image for pickup request (staff use).
     */
    public function deleteImage(Request $request, PickupRequest $pickupRequest, \App\Models\Image $image)
    {
        try {
            // Verify the image belongs to this pickup request
            if (! $pickupRequest->images()->where('images.id', $image->id)->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Image not found or does not belong to this pickup request.',
                ], 404);
            }

            // Check if user has permission to delete this image
            // Staff can delete any image, but we could add more granular permissions later
            if (! auth()->user()->hasPermission('manage_pickup_requests')) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to delete images.',
                ], 403);
            }

            // Delete the physical file
            if (\Illuminate\Support\Facades\Storage::disk('public')->exists($image->image_path)) {
                \Illuminate\Support\Facades\Storage::disk('public')->delete($image->image_path);
            }

            // Delete thumbnail if it exists
            if ($image->thumbnail_path && \Illuminate\Support\Facades\Storage::disk('public')->exists($image->thumbnail_path)) {
                \Illuminate\Support\Facades\Storage::disk('public')->delete($image->thumbnail_path);
            }

            // Detach from pickup request
            $pickupRequest->images()->detach($image->id);

            // Delete the image record
            $image->delete();

            return response()->json([
                'success' => true,
                'message' => 'Image deleted successfully',
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error deleting pickup request image', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'pickup_request_id' => $pickupRequest->id,
                'image_id' => $image->id,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting the image. Please try again.',
            ], 500);
        }
    }

    /**
     * Get images for pickup request (for calendar modal).
     */
    public function getImages(Request $request, PickupRequest $pickupRequest)
    {
        try {
            $images = $pickupRequest->images()->get()->map(function ($image) {
                return [
                    'id' => $image->id,
                    'src' => \Illuminate\Support\Facades\Storage::disk('public')->url($image->image_path),
                    'thumbnail' => \Illuminate\Support\Facades\Storage::disk('public')->url($image->image_path),
                    'title' => $image->title,
                    'alt_text' => $image->alt_text,
                    'uploaded_by' => $image->uploaded_by,
                    'og_filename' => $image->og_filename,
                ];
            });

            return response()->json([
                'success' => true,
                'images' => $images,
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error fetching pickup request images', [
                'error' => $e->getMessage(),
                'pickup_request_id' => $pickupRequest->id,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching images.',
            ], 500);
        }
    }
}
