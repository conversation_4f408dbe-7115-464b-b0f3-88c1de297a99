<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Invoice;
use App\Models\Customer;
use App\Models\Commodity;
use App\Models\InventoryCategory;
use App\Models\Inventory;
use App\Models\LineItem;
use Illuminate\Support\Facades\Log;



class LineItemController extends Controller
{
    public function store(Request $request)
    {
        try {

            Log::info('Trying to add line item with props: ' . json_encode($request->all()));

            $validated = $request->validate([
                'invoice_id' => 'required|exists:invoices,id',
                'item_name' => 'nullable|string',
                'item_id' => 'nullable|integer',
                'quantity' => 'required|integer|min:1',
                'price' => 'required|numeric',
                'is_payout' => 'nullable|in:on,1,true,false,0', 
                'description' => 'nullable|string',
                'tax' => 'nullable|numeric|min:0',
                'subtotal' => 'nullable|numeric|min:0',
                'department_id' => 'required|integer|exists:departments,id',
                'category_id' => 'nullable',
            ]);

            Log::info('Validated data for line item creation:', $validated);

            if ($validated['item_id'] == null) {
                Log::info('item id is null, creating custom item');
                $inventory = new Inventory();
                $inventory->name = $validated['item_name'];
                $inventory->inv_category_id = $validated['category_id'];
                $inventory->quantity = $validated['quantity'];
                $inventory->unit_price = $validated['price'];
                $inventory->suggested_price = $validated['price'];
                $inventory->status = 'forsale';

                // Generate the asset tag using the assigned id
                $categoryCode = InventoryCategory::find($validated['category_id'])->code;
                $inventory->asset_tag = $categoryCode . str_pad($inventory->id, 4, '0', STR_PAD_LEFT);
                Log::info('Trying to save custom item with props: ' . json_encode($inventory));

                $inventory->save();
                $validated['item_id'] = $inventory->id;
            }
            else{
                Log::info('item id is not null, using existing item');
            }

            $inventory = Inventory::findOrFail($validated['item_id']);
            $category = $inventory->category;

            Log::info('Inventory details:', $inventory->toArray());
            Log::info('Category details:', $category->toArray());

            // Check quantity_type and enforce rules
            if ($category->quantity_type === 'individual') {
                $existingLineItem = LineItem::where('item_id', $inventory->id)
                    ->where('invoice_id', $validated['invoice_id'])
                    ->first();

                if ($existingLineItem) {

                    return redirect()->back()->withErrors('This inventory item is already added to this invoice.');
                }
            }

            // Handle payout (buying from customer)
            $isPayout = $request->boolean('is_payout', false);
            $price = $validated['price'];

            // If this is a payout, ensure the price is negative
            if ($isPayout && $price > 0) {
                $price = -$price;
            }

            Log::info('Final price after payout adjustment:', ['price' => $price]);

            // Create the line item
            $lineItem = new LineItem([
                'item_id' => $validated['item_id'],
                'quantity' => $validated['quantity'],
                'item_name' => $inventory->name,
                'price' => $price,
                'subtotal' => $validated['quantity'] * $price + ($validated['tax'] ?? 0),
                'tax' => $validated['tax'] ?? 0,
                'description' => ($isPayout ? 'PAYOUT: ' : '') . ($validated['description'] ?? ''),
                'tax_policy_id' => $request->input('tax_policy_id'),
                'department_id' => $validated['department_id'],
            ]);

            Log::info('Line item details before saving:', $lineItem->toArray());

            $invoice = Invoice::findOrFail($validated['invoice_id']);
            $invoice->lineItems()->save($lineItem);

            // Update the invoice total_price, total_tax, final_price, total_discount
            $invoice->calculateTotals();
            $invoice->save();

            // Set the inventory item status to sold and update the quantity

            // Only update inventory quantity for items that are not 'unit' or 'unlimited' type
            if ($inventory->quantity != 0 && $category->quantity_type !== 'unlimited' && $category->quantity_type !== 'unit') {
                $inventory->quantity -= $validated['quantity'];
                if ($inventory->quantity === 0) {
                    $inventory->status = 'sold';
                }
            }


            Log::info("Inventory ID {$inventory->id}: Quantity updated to {$inventory->quantity}");


            $inventory->save();



            return redirect()->route('invoices.edit', $invoice)->with('success', 'Line item added successfully.');
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Validation failed:', $e->errors());
            return redirect()->back()->withErrors($e->errors());
        } catch (\Exception $e) {

            return redirect()->back()->withErrors('An error occurred while adding the line item. Please try again.');
        }
    }



    public function update(Request $request, LineItem $lineItem)
    {
        $validated = $request->validate([
            'description' => 'nullable|string|max:255',
        ]);

        $lineItem->update($validated);

        return redirect()->route('invoices.edit', $lineItem->invoice_id)
            ->with('success', 'Line item updated successfully.');
    }



    public function destroy(LineItem $lineItem)
    {
        $invoice = $lineItem->invoice;


        // If the inventory item is in category 1 (Uncategorised Custom Items), delete it
        if ($lineItem->inventory->inv_category_id === 1) {
            $lineItem->inventory->delete();
        } else {
            // Set the inventory item status to forsale and update the quantity
            $inventory = $lineItem->inventory;
            $inventory->quantity += $lineItem->quantity;
            $inventory->status = 'forsale';
            $inventory->save();
        }

        $lineItem->delete();

        // Update the invoice total_price, total_tax, final_price, total_discount
        $invoice->calculateTotals();
        $invoice->save();

        return redirect()->route('invoices.edit', $invoice)->with('success', 'Line item removed successfully.');
    }
}
