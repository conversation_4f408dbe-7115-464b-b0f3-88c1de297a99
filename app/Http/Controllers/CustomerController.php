<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use Illuminate\Http\Request;

use App\Models\Discount;
use App\Services\UserPreferenceService;


class CustomerController extends Controller
{
    public function index(Request $request, UserPreferenceService $userPreferenceService)
    {
        // Default preferences
        $defaultPagination = $userPreferenceService->get('pagination_customers', 10);
        $defaultSort = $userPreferenceService->get('customer_sort', 'id');
        $defaultOrder = $userPreferenceService->get('customer_order', 'asc');

        // Retrieve preferences from request or defaults
        $pagination = $request->query('pagination', $defaultPagination);
        $sort = $request->query('sort', $defaultSort);
        $order = $request->query('order', $defaultOrder);

        // Save updated preferences if they differ from defaults
        if ($pagination !== $defaultPagination) {
            $userPreferenceService->set('pagination_customers', $pagination);
        }
        if ($sort !== $defaultSort) {
            $userPreferenceService->set('customer_sort', $sort);
        }
        if ($order !== $defaultOrder) {
            $userPreferenceService->set('customer_order', $order);
        }

        $query = Customer::query();

        // Handle trashed customers
        if ($request->has('trashed') && $request->input('trashed') === 'true') {
            $query->onlyTrashed();
        }

        // Filter by customer type
        if ($type = $request->input('type')) {
            $query->where('type', $type);
        }

        // Get the active customer contract discount ID from global config
        $activeContractDiscountId = \App\Models\GlobalConfig::getValue('active_customer_contract_id');

        // Define valid columns for sorting
        $validColumns = ['id', 'name', 'type', 'total_purchases_value', 'created_at'];

        // Handle special case for contact_info column (not directly sortable)
        if ($sort === 'contact_info') {
            // Default to sorting by email when contact_info is selected
            $sort = 'email';
        }

        // Apply sorting
        if (in_array($sort, $validColumns)) {
            $query->orderBy($sort, $order);
        }

        // Store the base query for contract filtering
        $baseQuery = clone $query;

        // Check if we need to filter by contract status
        $contractFilter = $request->input('contract_status');
        $hasContractFilter = $contractFilter === 'with_contract' || $contractFilter === 'without_contract';

        // Paginate results (we'll filter by contract status after pagination if needed)
        $customers = $query->paginate($pagination);

        // If there's an active contract discount set in global config, check which customers have it
        if ($activeContractDiscountId) {
            // Get all customer IDs from the current page
            $customerIds = [];
            foreach ($customers as $customer) {
                $customerIds[] = $customer->id;
            }

            // Get all active customer discounts for the contract discount
            $now = now();
            $activeCustomerDiscounts = \App\Models\CustomerDiscount::where('discount_id', $activeContractDiscountId)
                ->whereIn('customer_id', $customerIds)
                ->where(function($query) use ($now) {
                    $query->where(function($q) use ($now) {
                        $q->whereNull('start_date')
                          ->orWhere('start_date', '<=', $now);
                    })->where(function($q) use ($now) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', $now);
                    })->where(function($q) {
                        $q->whereNull('maximum_uses')
                          ->orWhereRaw('usage_count < maximum_uses');
                    });
                })
                ->get()
                ->keyBy('customer_id');

            // Add a hasActiveContract attribute to each customer
            foreach ($customers as $customer) {
                $customer->hasActiveContract = isset($activeCustomerDiscounts[$customer->id]);
            }

            // If we're filtering by contract status, we need to do a second query
            if ($hasContractFilter) {
                // Get all customer IDs with active contracts
                $customersWithContract = \App\Models\CustomerDiscount::where('discount_id', $activeContractDiscountId)
                    ->where(function($query) use ($now) {
                        $query->where(function($q) use ($now) {
                            $q->whereNull('start_date')
                              ->orWhere('start_date', '<=', $now);
                        })->where(function($q) use ($now) {
                            $q->whereNull('end_date')
                              ->orWhere('end_date', '>=', $now);
                        })->where(function($q) {
                            $q->whereNull('maximum_uses')
                              ->orWhereRaw('usage_count < maximum_uses');
                        });
                    })
                    ->pluck('customer_id')
                    ->toArray();

                // Create a new query with the contract filter
                $filteredQuery = clone $baseQuery;

                if ($contractFilter === 'with_contract') {
                    $filteredQuery->whereIn('id', $customersWithContract);
                } else { // without_contract
                    $filteredQuery->whereNotIn('id', $customersWithContract);
                }

                // Re-run the query with the contract filter
                $customers = $filteredQuery->paginate($pagination);
                $customers->appends($request->except('page')); // Maintain other query parameters

                // Re-apply the hasActiveContract attribute
                foreach ($customers as $customer) {
                    $customer->hasActiveContract = in_array($customer->id, $customersWithContract);
                }
            }
        } else {
            // If no active contract discount is set, no customers have active contracts
            foreach ($customers as $customer) {
                $customer->hasActiveContract = false;
            }

            // If filtering for customers with contracts, but no contract discount is set,
            // we should return an empty result set for 'with_contract'
            if ($contractFilter === 'with_contract') {
                $customers = new \Illuminate\Pagination\LengthAwarePaginator(
                    [], // Empty collection
                    0,  // Total items
                    $pagination, // Items per page
                    $request->input('page', 1) // Current page
                );
                $customers->setPath(request()->url());
                $customers->appends($request->except('page'));
            }
        }

        return view('customers.index', compact('customers', 'pagination', 'sort', 'order', 'activeContractDiscountId', 'contractFilter'));
    }



    public function create()
    {
        return view('customers.create');
    }

    public function quickCreate()
    {
        return view('customers.quick-create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'nickname' => 'nullable|string|max:255',
            'contact' => 'nullable|string|max:255',
            'website' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'type' => 'required|string|in:Residential Customer,Online Customer,Bulk Buyer,Business',
            'notes' => 'nullable|string',
        ]);

        $customer = Customer::create($validated);

        if ($request->expectsJson()) {
            // If the request expects a JSON response (e.g., from quick create modal)
            return response()->json(['success' => true, 'customer' => $customer]);
        } else {
            // Check if the request is coming from the main create page
            $referrer = $request->headers->get('referer');
            $isFromCreatePage = $referrer && str_contains($referrer, route('customers.create'));

            if ($isFromCreatePage) {
                // Redirect to the edit page for the newly created customer
                return redirect()->route('customers.edit', $customer)->with('success', 'Customer created successfully! You can now add additional details.');
            } else {
                // For other cases (like quick create page), redirect to customer index
                return redirect()->route('customers.index')->with('success', 'Customer created successfully.');
            }
        }
    }



    public function edit(Customer $customer)
    {
        // Load the files relationship with the uploader
        $customer->load(['files' => function($query) {
            $query->with('uploader');
        }]);

        return view('customers.edit', compact('customer'));
    }

    public function update(Request $request, Customer $customer)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'nickname' => 'nullable|string|max:255',
            'contact' => 'nullable|string|max:255',
            'website' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:255', // Allow any type of phone format
            'address' => 'nullable|string',
            'notes' => 'nullable|string',
            'type' => 'required|string|in:Residential Customer,Online Customer,Bulk Buyer,Business',
        ]);

        $customer->update($request->all());

        return redirect()->route('customers.index')->with('success', 'Customer updated successfully.');
    }

    /**
     * Auto-save a single field for a customer
     */
    public function autoSave(Request $request, Customer $customer)
    {
        try {
            // Validate the field based on its name
            $field = $request->input('field');
            $value = $request->input('value');

            // Validate based on field type
            switch ($field) {
                case 'name':
                    $request->validate([
                        'value' => 'required|string|max:255',
                    ]);
                    break;
                case 'nickname':
                case 'contact':
                case 'website':
                    $request->validate([
                        'value' => 'nullable|string|max:255',
                    ]);
                    break;
                case 'email':
                    $request->validate([
                        'value' => 'nullable|email|max:255',
                    ]);
                    break;
                case 'phone':
                    $request->validate([
                        'value' => 'nullable|string|max:255',
                    ]);
                    break;
                case 'address':
                case 'notes':
                    $request->validate([
                        'value' => 'nullable|string',
                    ]);
                    break;
                case 'type':
                    $request->validate([
                        'value' => 'required|string|in:Residential Customer,Online Customer,Bulk Buyer,Business',
                    ]);
                    break;
                default:
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid field name',
                    ], 400);
            }

            // Update the field
            $customer->$field = $value;
            $customer->save();

            return response()->json([
                'success' => true,
                'message' => ucfirst(str_replace('_', ' ', $field)) . ' updated successfully',
                'field' => $field,
                'value' => $value,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }


    public function destroy(Customer $customer)
    {
        // DEBUGGING: Log the request details to understand why customer is being deleted
        \Illuminate\Support\Facades\Log::warning('CUSTOMER DESTROY METHOD CALLED', [
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'request_method' => request()->method(),
            'request_url' => request()->url(),
            'request_referer' => request()->header('referer'),
            'request_user_agent' => request()->header('user-agent'),
            'request_ip' => request()->ip(),
            'request_ajax' => request()->ajax(),
            'request_expects_json' => request()->expectsJson(),
            'request_all' => request()->all(),
            'request_headers' => request()->headers->all(),
            'user_id' => auth()->id(),
            'user_email' => auth()->user()->email,
            'stack_trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10),
        ]);
        
        // SAFETY CHECK: Don't allow GET requests to delete customers
        if (request()->method() !== 'DELETE') {
            \Illuminate\Support\Facades\Log::error('CUSTOMER DELETE BLOCKED - Invalid HTTP method', [
                'customer_id' => $customer->id,
                'method' => request()->method(),
            ]);
            abort(405, 'Method not allowed. Customer deletion requires DELETE method.');
        }
        
        // Additional safety: Require explicit confirmation parameter for customer deletion
        // This prevents accidental deletion through misconfigured links or routes
        if (!request()->has('confirmed') || request()->input('confirmed') !== 'true') {
            \Illuminate\Support\Facades\Log::warning('CUSTOMER DELETE BLOCKED - Missing confirmation', [
                'customer_id' => $customer->id,
            ]);
            return redirect()->back()->with('error', 'Customer deletion requires explicit confirmation.');
        }
        
        $customer->delete();

        return redirect()->route('customers.index')->with('success', 'Customer soft deleted successfully!');
    }

    public function restore($id)
    {
        $customer = Customer::withTrashed()->findOrFail($id);
        $customer->restore();

        return redirect()->route('customers.index')->with('success', 'Customer restored successfully!');
    }


    public function search($query, Request $request = null)
    {
        // Return an empty result if the query is empty or invalid
        if (empty($query)) {
            return response()->json([], 200);
        }

        // Check if this is a pickup request context (limit to 3 results with enhanced data)
        $isPickupRequest = $request && $request->query('pickup_request') === 'true';
        $limit = $isPickupRequest ? 3 : 10;
        $fields = $isPickupRequest
            ? ['id', 'name', 'contact', 'email', 'phone', 'nickname', 'address', 'type']
            : ['id', 'name', 'email', 'phone', 'nickname'];

        // Build the query with enhanced matching for pickup requests
        $customersQuery = Customer::query();

        if ($isPickupRequest) {
            // For pickup requests, prioritize business name and contact name matches
            $customersQuery->where(function($q) use ($query) {
                $q->where('name', 'LIKE', "%{$query}%")
                  ->orWhere('contact', 'LIKE', "%{$query}%");
            })
            ->orWhere('email', 'LIKE', "%{$query}%")
            ->orWhere('phone', 'LIKE', "%{$query}%")
            ->orWhere('nickname', 'LIKE', "%{$query}%")
            // Order by relevance: exact matches first, then partial matches
            ->orderByRaw("CASE
                WHEN name = ? THEN 1
                WHEN contact = ? THEN 2
                WHEN name LIKE ? THEN 3
                WHEN contact LIKE ? THEN 4
                ELSE 5
            END", [$query, $query, "{$query}%", "{$query}%"]);
        } else {
            // Standard search for other contexts
            $customersQuery->where('name', 'LIKE', "%{$query}%")
                ->orWhere('email', 'LIKE', "%{$query}%")
                ->orWhere('nickname', 'LIKE', "%{$query}%")
                ->orWhere('phone', 'LIKE', "%{$query}%")
                ->orWhere('contact', 'LIKE', "%{$query}%");
        }

        $customers = $customersQuery->limit($limit)->get($fields);

        // Get the active customer contract discount ID from global config
        $activeContractDiscountId = \App\Models\GlobalConfig::getValue('active_customer_contract_id');

        // If there's an active contract discount set in global config, check which customers have it
        if ($activeContractDiscountId) {
            // Get all customer IDs from the search results
            $customerIds = $customers->pluck('id')->toArray();

            // Get all active customer discounts for the contract discount
            $now = now();
            $activeCustomerDiscounts = \App\Models\CustomerDiscount::where('discount_id', $activeContractDiscountId)
                ->whereIn('customer_id', $customerIds)
                ->where(function($query) use ($now) {
                    $query->where(function($q) use ($now) {
                        $q->whereNull('start_date')
                          ->orWhere('start_date', '<=', $now);
                    })->where(function($q) use ($now) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', $now);
                    })->where(function($q) {
                        $q->whereNull('maximum_uses')
                          ->orWhereRaw('usage_count < maximum_uses');
                    });
                })
                ->get()
                ->keyBy('customer_id');

            // Add a hasActiveContract attribute to each customer
            foreach ($customers as $customer) {
                $customer->hasActiveContract = isset($activeCustomerDiscounts[$customer->id]);
            }
        } else {
            // If no active contract discount is set, no customers have active contracts
            foreach ($customers as $customer) {
                $customer->hasActiveContract = false;
            }
        }

        // Add license status to each customer
        foreach ($customers as $customer) {
            $customer->hasLicenseOnFile = $customer->hasPhotoIdOnFile();
        }

        // Return the results as JSON
        return response()->json($customers);
    }







    /**
     * Check if customer has license/photo ID on file
     */
    public function getLicenseStatus(Customer $customer)
    {
        $hasLicense = $customer->hasPhotoIdOnFile();

        // Get the actual license files for display
        $licenseFiles = [];
        if ($hasLicense) {
            $files = $customer->files()
                ->where(function($query) {
                    $query->where('description', 'LIKE', '%photo_id%')
                          ->orWhere('description', 'LIKE', '%Customer ID%')
                          ->orWhere('description', 'LIKE', '%ID%')
                          ->orWhere('metadata->file_type', 'photo_id');
                })
                ->select(['id', 'original_filename', 'mime_type', 'description'])
                ->get();

            // Add URLs for viewing files
            foreach ($files as $file) {
                $licenseFiles[] = [
                    'id' => $file->id,
                    'original_filename' => $file->original_filename,
                    'mime_type' => $file->mime_type,
                    'description' => $file->description,
                    'is_image' => strpos($file->mime_type, 'image/') === 0,
                    'thumbnail_url' => route('files.view', ['file' => $file->id, 'width' => 150, 'height' => 150]),
                    'full_url' => route('files.view', $file->id),
                ];
            }
        }

        return response()->json([
            'hasLicense' => $hasLicense,
            'licenseFiles' => $licenseFiles
        ]);
    }

    public function show(Customer $customer)
    {
        // Log that the customer was viewed (only if not viewed in last 15 minutes)
        $this->logViewIfNotRecent($customer);

        // Eager load relationships to avoid N+1 queries
        $customer->load([
            'invoices.lineItems',
            'certificates.devices'
        ]);

        // Get the active customer contract discount ID from global config
        $activeContractDiscountId = \App\Models\GlobalConfig::getValue('active_customer_contract_id');

        // Check if the customer has an active contract
        $hasActiveContract = false;

        if ($activeContractDiscountId) {
            // Check if the customer has an active discount matching the contract discount
            $now = now();
            $activeContract = \App\Models\CustomerDiscount::where('customer_id', $customer->id)
                ->where('discount_id', $activeContractDiscountId)
                ->where(function($query) use ($now) {
                    $query->where(function($q) use ($now) {
                        $q->whereNull('start_date')
                          ->orWhere('start_date', '<=', $now);
                    })->where(function($q) use ($now) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', $now);
                    })->where(function($q) {
                        $q->whereNull('maximum_uses')
                          ->orWhereRaw('usage_count < maximum_uses');
                    });
                })
                ->first();

            $hasActiveContract = $activeContract !== null;
        }

        return view('customers.show', compact('customer', 'hasActiveContract', 'activeContractDiscountId'));
    }

    public function eligibleDiscounts(Customer $customer)
    {
        $allDiscounts = Discount::where(function ($query) use ($customer) {
            $query->whereNull('is_customer_specific') // General discounts
                ->orWhere('is_customer_specific', false) // Discounts for all customers
                ->orWhereHas('customerDiscounts', function ($q) use ($customer) {
                    $q->where('customer_id', $customer->id);
                }); // Customer-specific discounts
        })
            ->where(function ($query) {
                $query->whereNull('start_date')->orWhere('start_date', '<=', now());
            })
            ->where(function ($query) {
                $query->whereNull('end_date')->orWhere('end_date', '>=', now());
            })
            ->get();

        $result = $allDiscounts->map(function ($discount) use ($customer) {
            $customerDiscount = $discount->customerDiscounts()
                ->where('customer_id', $customer->id)
                ->first();

            $usageCount = $customerDiscount?->usage_count ?? 0;
            $maximumUses = $customerDiscount?->maximum_uses ?? ($discount->reusable ? null : 1);

            $isUsable = ($maximumUses === null || $usageCount < $maximumUses);
            $reason = $isUsable ? null : 'Maximum uses reached';

            return [
                'id' => $discount->id,
                'name' => $discount->name,
                'type' => $discount->type,
                'amount' => $discount->amount,
                'category_id' => $discount->category_id,
                'category_name' => $discount->category?->name,
                'usage_count' => $usageCount,
                'maximum_uses' => $maximumUses,
                'is_usable' => $isUsable,
                'reason' => $reason,
            ];
        });

        $usable = $result->where('is_usable', true)->values();
        $unusable = $result->where('is_usable', false)->values();

        return response()->json([
            'usable' => $usable,
            'unusable' => $unusable,
        ]);
    }

    /**
     * Get customer name by ID (used by discount checker)
     */
    public function getName(Customer $customer)
    {
        return response()->json([
            'name' => $customer->name,
            'id' => $customer->id
        ]);
    }

    /**
     * Get customer data as JSON (used by pickup events)
     */
    public function getJson(Customer $customer)
    {
        return response()->json([
            'id' => $customer->id,
            'name' => $customer->name,
            'email' => $customer->email,
            'phone' => $customer->phone,
            'address' => $customer->address,
            'nickname' => $customer->nickname,
            'contact' => $customer->contact,
            'type' => $customer->type,
            'notes' => $customer->notes
        ]);
    }

    /**
     * Log a view event only if the user hasn't viewed this customer in the last 15 minutes
     */
    private function logViewIfNotRecent(Customer $customer)
    {
        $user = auth()->user();
        if (!$user) {
            return; // Don't log views for unauthenticated users
        }

        // Check if this user has viewed this customer in the last 15 minutes
        $recentView = \App\Models\ActivityLog::where('loggable_type', get_class($customer))
            ->where('loggable_id', $customer->id)
            ->where('user_id', $user->id)
            ->where('event_type', 'viewed')
            ->where('created_at', '>=', now()->subMinutes(15))
            ->exists();

        if (!$recentView) {
            $customer->logActivity('viewed', "Customer '{$customer->name}' was viewed");
        }
    }

}
