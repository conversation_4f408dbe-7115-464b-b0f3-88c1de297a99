<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Writer\Csv;

class CustomerExportController extends Controller
{
    /**
     * Show the export configuration page
     */
    public function index()
    {
        $availableFields = [
            'name' => 'Customer Name',
            'contact' => 'Contact Person',
            'nickname' => 'Nickname',
            'email' => 'Email Address',
            'phone' => 'Phone Number',
            'address' => 'Address',
            'notes' => 'Notes',
            'type' => 'Customer Type',
            'website' => 'Website',
            'total_revenue' => 'Total Revenue',
            'total_purchases' => 'Total Purchases',
            'total_data_destruction_certificates' => 'Data Destruction Certificates',
            'signed_agreement' => 'Signed Service Agreement',
            'agreement_start_date' => 'Agreement Start Date',
            'agreement_end_date' => 'Agreement End Date'
        ];

        $totalCustomers = Customer::count();

        return view('admin.tools.customer-export.index', compact('availableFields', 'totalCustomers'));
    }

    /**
     * Export customers to Excel
     */
    public function exportExcel(Request $request)
    {
        $request->validate([
            'fields' => 'required|array|min:1',
            'fields.*' => 'string|in:name,contact,nickname,email,phone,address,notes,type,website,total_revenue,total_purchases,total_data_destruction_certificates,signed_agreement,agreement_start_date,agreement_end_date'
        ]);

        $selectedFields = $request->input('fields');
        $filename = 'customers_export_' . date('Y-m-d_H-i-s') . '.xlsx';

        return $this->generateExcelResponse($selectedFields, $filename);
    }

    /**
     * Export customers to CSV
     */
    public function exportCsv(Request $request)
    {
        $request->validate([
            'fields' => 'required|array|min:1',
            'fields.*' => 'string|in:name,contact,nickname,email,phone,address,notes,type,website,total_revenue,total_purchases,total_data_destruction_certificates,signed_agreement,agreement_start_date,agreement_end_date'
        ]);

        $selectedFields = $request->input('fields');
        $filename = 'customers_export_' . date('Y-m-d_H-i-s') . '.csv';

        return $this->generateCsvResponse($selectedFields, $filename);
    }

    /**
     * Quick export all customers with all fields to Excel
     */
    public function quickExportExcel()
    {
        $allFields = [
            'name', 'contact', 'nickname', 'email', 'phone', 'address',
            'notes', 'type', 'website', 'total_revenue', 'total_purchases',
            'total_data_destruction_certificates', 'signed_agreement',
            'agreement_start_date', 'agreement_end_date'
        ];

        $filename = 'customers_full_export_' . date('Y-m-d_H-i-s') . '.xlsx';

        return $this->generateExcelResponse($allFields, $filename);
    }

    /**
     * Quick export all customers with all fields to CSV
     */
    public function quickExportCsv()
    {
        $allFields = [
            'name', 'contact', 'nickname', 'email', 'phone', 'address',
            'notes', 'type', 'website', 'total_revenue', 'total_purchases',
            'total_data_destruction_certificates', 'signed_agreement',
            'agreement_start_date', 'agreement_end_date'
        ];

        $filename = 'customers_full_export_' . date('Y-m-d_H-i-s') . '.csv';

        return $this->generateCsvResponse($allFields, $filename);
    }

    /**
     * Generate Excel response using PhpSpreadsheet
     */
    private function generateExcelResponse(array $selectedFields, string $filename)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Get field labels
        $fieldLabels = $this->getFieldLabels();

        // Set headers
        $headers = [];
        foreach ($selectedFields as $field) {
            $headers[] = $fieldLabels[$field] ?? ucfirst(str_replace('_', ' ', $field));
        }

        $sheet->fromArray($headers, null, 'A1');

        // Style the header row
        $lastColumn = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($headers));
        $headerRange = 'A1:' . $lastColumn . '1';
        $sheet->getStyle($headerRange)->getFont()->setBold(true);
        $sheet->getStyle($headerRange)->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setRGB('E2E8F0');

        // Get customer data
        $customers = Customer::with(['invoices', 'certificates', 'customerDiscounts'])->get();
        $rowData = [];

        foreach ($customers as $customer) {
            $row = [];
            foreach ($selectedFields as $field) {
                $row[] = $this->getCustomerFieldValue($customer, $field);
            }
            $rowData[] = $row;
        }

        // Add data to sheet
        if (!empty($rowData)) {
            $sheet->fromArray($rowData, null, 'A2');
        }

        // Auto-size columns
        for ($i = 1; $i <= count($headers); $i++) {
            $column = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($i);
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // Create writer and generate response
        $writer = new Xlsx($spreadsheet);

        return response()->streamDownload(function() use ($writer) {
            $writer->save('php://output');
        }, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }

    /**
     * Generate CSV response using PhpSpreadsheet
     */
    private function generateCsvResponse(array $selectedFields, string $filename)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Get field labels
        $fieldLabels = $this->getFieldLabels();

        // Set headers
        $headers = [];
        foreach ($selectedFields as $field) {
            $headers[] = $fieldLabels[$field] ?? ucfirst(str_replace('_', ' ', $field));
        }

        $sheet->fromArray($headers, null, 'A1');

        // Get customer data
        $customers = Customer::with(['invoices', 'certificates', 'customerDiscounts'])->get();
        $rowData = [];

        foreach ($customers as $customer) {
            $row = [];
            foreach ($selectedFields as $field) {
                $row[] = $this->getCustomerFieldValue($customer, $field);
            }
            $rowData[] = $row;
        }

        // Add data to sheet
        if (!empty($rowData)) {
            $sheet->fromArray($rowData, null, 'A2');
        }

        // Create CSV writer and generate response
        $writer = new Csv($spreadsheet);

        return response()->streamDownload(function() use ($writer) {
            $writer->save('php://output');
        }, $filename, [
            'Content-Type' => 'text/csv',
        ]);
    }

    /**
     * Get field labels for headers
     */
    private function getFieldLabels(): array
    {
        return [
            'name' => 'Customer Name',
            'contact' => 'Contact Person',
            'nickname' => 'Nickname',
            'email' => 'Email Address',
            'phone' => 'Phone Number',
            'address' => 'Address',
            'notes' => 'Notes',
            'type' => 'Customer Type',
            'website' => 'Website',
            'total_revenue' => 'Total Revenue',
            'total_purchases' => 'Total Purchases',
            'total_data_destruction_certificates' => 'Data Destruction Certificates',
            'signed_agreement' => 'Signed Service Agreement',
            'agreement_start_date' => 'Agreement Start Date',
            'agreement_end_date' => 'Agreement End Date'
        ];
    }

    /**
     * Get the value for a specific customer field
     */
    private function getCustomerFieldValue(Customer $customer, string $field)
    {
        switch ($field) {
            case 'name':
                return $customer->name;
            case 'contact':
                return $customer->contact;
            case 'nickname':
                return $customer->nickname;
            case 'email':
                return $customer->email;
            case 'phone':
                return $customer->phone;
            case 'address':
                return $customer->address;
            case 'notes':
                return $customer->notes;
            case 'type':
                return $customer->type;
            case 'website':
                return $customer->website;
            case 'total_revenue':
                return number_format($customer->total_revenue, 2);
            case 'total_purchases':
                return $customer->total_purchases;
            case 'total_data_destruction_certificates':
                return $customer->certificates->count();
            case 'signed_agreement':
                return $customer->hasSignedServiceAgreement() ? 'Yes' : 'No';
            case 'agreement_start_date':
                $startDate = $customer->getServiceAgreementStartDate();
                return $startDate ? $startDate->format('Y-m-d') : '';
            case 'agreement_end_date':
                $endDate = $customer->getServiceAgreementEndDate();
                return $endDate ? $endDate->format('Y-m-d') : '';
            default:
                return '';
        }
    }
}
