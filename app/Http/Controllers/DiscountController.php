<?php

namespace App\Http\Controllers;

use App\Models\Discount;
use App\Models\InventoryCategory;
use Illuminate\Http\Request;
use App\Models\Customer;


class DiscountController extends Controller
{
    public function index(Request $request)
    {

        $pagination = $request->query('pagination', session('pagination', 10));
        session(['pagination' => $pagination]); // Save to session
        $discounts = Discount::latest()->paginate($pagination);
        $categories = InventoryCategory::all();
        return view('discounts.index', compact('discounts', 'categories'));
    }

    public function create()
    {
        $categories = InventoryCategory::all();
        return view('discounts.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:percent,fixed',
            'amount' => 'required|numeric|min:0',
            'scope' => 'required|in:invoice,line_item',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'minimum_purchase' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'category_id' => 'nullable|exists:inventory_categories,id',
            'reusable' => 'sometimes|boolean',
            'is_customer_specific' => 'sometimes|boolean',
        ]);

        // if dates are not set, set them to null
        if (!$validated['start_date']) {
            $validated['start_date'] = null;
        }
        if (!$validated['end_date']) {
            $validated['end_date'] = null;
        }


        Discount::create($validated);

        return redirect()->route('discounts.index')->with('success', 'Discount created successfully.');
    }

    public function edit(Discount $discount)
    {
        $categories = InventoryCategory::all();
        $customers = Customer::all();
        return view('discounts.edit', compact('discount', 'categories'));
    }

    public function update(Request $request, Discount $discount)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:percent,fixed',
            'amount' => 'required|numeric|min:0',
            'scope' => 'required|in:invoice,line_item',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'minimum_purchase' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'category_id' => 'nullable|exists:inventory_categories,id',
            'reusable' => 'sometimes|boolean',
            'is_customer_specific' => 'sometimes|boolean',
        ]);

        // if dates are not set, set them to null
        if (!$validated['start_date']) {
            $validated['start_date'] = null;
        }
        if (!$validated['end_date']) {
            $validated['end_date'] = null;
        }


        $discount->update($validated);

        return redirect()->route('discounts.index')->with('success', 'Discount updated successfully.');
    }

    public function destroy(Discount $discount)
    {
        $discount->delete();
        return redirect()->route('discounts.index')->with('success', 'Discount deleted successfully.');
    }
    public function checker()
    {
        return view('discounts.checker');
    }

    public function checkForCustomerDiscounts(Customer $customer)
    {
        $discounts = Discount::where(function ($query) use ($customer) {
            $query->where('is_customer_specific', false)
                ->orWhereHas('customerDiscounts', function ($q) use ($customer) {
                    $q->where('customer_id', $customer->id);
                });
        })->active()->get();

        return response()->json($discounts);
    }

    public function show(Discount $discount)
    {
        return view('discounts.show', compact('discount'));
    }

    public function fetchDiscounts(Request $request)
{
    $validated = $request->validate([
        'customer_id' => 'required|exists:customers,id',
    ]);

    $customerId = $validated['customer_id'];

    // Fetch applicable discounts
    $discounts = Discount::where('is_customer_specific', true)
        ->whereHas('customerDiscounts', function ($query) use ($customerId) {
            $query->where('customer_id', $customerId);
        })
        ->orWhere('is_customer_specific', false)
        ->active() // Assuming you have an "active" scope for discounts
        ->get();

    return response()->json(['discounts' => $discounts]);
}


}
