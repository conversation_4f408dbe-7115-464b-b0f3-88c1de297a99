<?php

namespace App\Http\Controllers;

use App\Models\Certificate;
use App\Models\CertificatePdf;
use App\Models\Device;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use \Barryvdh\DomPDF\Facade\Pdf as PDF;
use App\Services\UserPreferenceService;

class CertificateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, UserPreferenceService $userPreferenceService)
    {
        // Default preferences
        $defaultPagination = $userPreferenceService->get('pagination_certificates', 10);
        $defaultSort = $userPreferenceService->get('certificate_sort', 'created_at');
        $defaultOrder = $userPreferenceService->get('certificate_order', 'desc');

        // Retrieve preferences from request or defaults
        $pagination = $request->query('pagination', $defaultPagination);
        $sort = $request->query('sort', $defaultSort);
        $order = $request->query('order', $defaultOrder);

        // Save updated preferences if they differ from defaults
        if ($pagination !== $defaultPagination) {
            $userPreferenceService->set('pagination_certificates', $pagination);
        }
        if ($sort !== $defaultSort) {
            $userPreferenceService->set('certificate_sort', $sort);
        }
        if ($order !== $defaultOrder) {
            $userPreferenceService->set('certificate_order', $order);
        }

        // Check if we're viewing trashed certificates
        $viewTrashed = $request->input('status') === 'trashed';

        // Start with the appropriate query
        if ($viewTrashed) {
            $query = Certificate::onlyTrashed()->with(['customer', 'devices']);
        } else {
            $query = Certificate::query()->with(['customer', 'devices']);

            // Filter by status (only for non-trashed)
            if ($status = $request->input('status')) {
                $query->where('status', $status);
            }
        }

        // Filter by customer
        if ($customerId = $request->input('customer_id')) {
            $query->where('customer_id', $customerId);
        }

        // Define valid columns for sorting
        $validColumns = [
            'id', 'certificate_number', 'status', 'scheduled_destruction_date',
            'actual_destruction_date', 'pickup_dropoff_date', 'created_at', 'updated_at'
        ];

        // Apply sorting
        if (in_array($sort, $validColumns)) {
            $query->orderBy($sort, $order);
        } elseif ($sort === 'customer_name') {
            // Special case for sorting by customer name
            $query->join('customers', 'certificates.customer_id', '=', 'customers.id')
                  ->orderBy('customers.name', $order)
                  ->select('certificates.*');
        }

        // Paginate results
        $certificates = $query->paginate($pagination);

        return view('certificates.index', compact('certificates', 'pagination', 'sort', 'order'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        try {
            Log::info('CertificateController::create - Starting certificate creation form');

            // Certificate number will be auto-generated after creation based on ID
            // Pass a placeholder for the form
            $certificate_number = 'COC-XXXXX';

            Log::info('CertificateController::create - Certificate number will be auto-generated');

            // Return the view with the placeholder certificate number
            return view('certificates.create', compact('certificate_number'));
        } catch (\Exception $e) {
            // Log any unexpected errors
            Log::error('CertificateController::create - Unexpected error', [
                'error' => $e->getMessage(),
                'class' => get_class($e),
                'trace' => $e->getTraceAsString()
            ]);

            // Re-throw the exception to let Laravel handle it
            throw $e;
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            Log::info('CertificateController::store - Starting certificate creation process');
            Log::info('CertificateController::store - Request Data', ['data' => $request->all()]);

            // Log the validation rules we're about to apply
            Log::info('CertificateController::store - Validating request with rules', [
                'rules' => [
                    'customer_id' => 'required|exists:customers,id',
                    'scheduled_destruction_date' => 'nullable|date',
                ]
            ]);

            // Validate the request (certificate_number will be auto-generated)
            try {
                $validated = $request->validate([
                    'customer_id' => 'required|exists:customers,id',
                    'scheduled_destruction_date' => 'nullable|date',
                    'pickup_dropoff_date' => 'nullable|date',
                ]);

                Log::info('CertificateController::store - Validation passed', ['validated_data' => $validated]);
            } catch (\Illuminate\Validation\ValidationException $e) {
                Log::error('CertificateController::store - Validation failed', [
                    'errors' => $e->errors(),
                    'message' => $e->getMessage()
                ]);
                throw $e; // Re-throw to let Laravel handle the response
            }

            // Log the data we're about to use for certificate creation
            Log::info('CertificateController::store - Creating certificate with data', [
                'customer_id' => $request->customer_id,
                'scheduled_destruction_date' => $request->scheduled_destruction_date,
            ]);

            // Create the certificate (certificate_number will be auto-generated)
            try {
                $certificate = Certificate::create([
                    'customer_id' => $request->customer_id,
                    'scheduled_destruction_date' => $request->scheduled_destruction_date,
                    'pickup_dropoff_date' => $request->pickup_dropoff_date,
                    'status' => 'pending', // Explicitly set status to ensure it's initialized
                ]);

                Log::info('CertificateController::store - Certificate created successfully', [
                    'certificate_id' => $certificate->id,
                    'certificate_number' => $certificate->certificate_number
                ]);
            } catch (\Exception $e) {
                Log::error('CertificateController::store - Failed to create certificate', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }

            // Generate the redirect URL and log it
            $redirectUrl = route('certificates.edit', $certificate);
            Log::info('CertificateController::store - Redirecting to', ['url' => $redirectUrl]);

            // Return the redirect response
            return redirect()->route('certificates.edit', $certificate)
                ->with('success', 'Certificate created successfully.');

        } catch (\Exception $e) {
            // Catch any unexpected exceptions
            Log::error('CertificateController::store - Unexpected error', [
                'error' => $e->getMessage(),
                'class' => get_class($e),
                'trace' => $e->getTraceAsString()
            ]);

            // Re-throw the exception to let Laravel handle it
            throw $e;
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Certificate $certificate)
    {
        $certificate->load(['customer', 'devices.destroyedByUser', 'signatures.signatory', 'files.uploader']);
        $deviceCount = $certificate->devices->count();
        $certifyingTechnician = null;

        // Find the certifying technician (if any)
        $techSignature = $certificate->signatures->where('role', 'technician')->first();
        if ($techSignature && $techSignature->signatory && $techSignature->signatory instanceof \App\Models\User) {
            $certifyingTechnician = $techSignature->signatory;
        }

        // Load company data
        $companyData = $this->loadCompanyData();

        // Get contract data
        $contractData = [];
        foreach (['client', 'driver', 'warehouse', 'technician'] as $type) {
            $signature = $certificate->signatures->where('role', $type)->first();
            if ($signature) {
                $contractData[$type] = [
                    'signature' => $signature,
                    'contractText' => $signature->contract_text,
                ];
            }
        }

        // Get the current PDF if it exists
        $currentPdf = CertificatePdf::getCurrentForCertificate($certificate->id);

        return view('certificates.show', compact(
            'certificate',
            'deviceCount',
            'certifyingTechnician',
            'companyData',
            'contractData',
            'currentPdf'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Certificate $certificate)
    {
        // Load the relationships
        $certificate->load(['devices', 'files.uploader', 'signatures']);

        // Get unique manufacturers and models for device form
        $manufacturers = Device::distinct('manufacturer')->whereNotNull('manufacturer')->orderBy('manufacturer')->pluck('manufacturer');
        $models = Device::distinct('model')->whereNotNull('model')->orderBy('model')->pluck('model');

        // Get current PDF if it exists
        $currentPdf = CertificatePdf::getCurrentForCertificate($certificate->id);

        return view('certificates.edit', compact('certificate', 'manufacturers', 'models', 'currentPdf'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Certificate $certificate)
    {
        $request->validate([
            'certificate_number' => 'required|unique:certificates,certificate_number,' . $certificate->id,
            'customer_id' => 'required|exists:customers,id',
            'scheduled_destruction_date' => 'nullable|date',
            'pickup_dropoff_date' => 'nullable|date',
            'device_serials' => 'nullable|string',
            'status' => 'required|in:pending,verifying,completed',
            'destruction_method' => 'nullable|string|max:255',
            'destruction_location' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        // If status is changed to completed and there's no actual_destruction_date, set it to now
        $actual_destruction_date = $certificate->actual_destruction_date;
        if ($request->status === 'completed' && !$certificate->actual_destruction_date) {
            $actual_destruction_date = now();
        }

        $certificate->update([
            'certificate_number' => $request->certificate_number,
            'customer_id' => $request->customer_id,
            'scheduled_destruction_date' => $request->scheduled_destruction_date,
            'pickup_dropoff_date' => $request->pickup_dropoff_date,
            'status' => $request->status,
            'actual_destruction_date' => $actual_destruction_date,
            'destruction_method' => $request->destruction_method,
            'destruction_location' => $request->destruction_location,
            'notes' => $request->notes,
        ]);

        // Handle device serials input
        if ($request->has('device_serials')) {
            $serials = explode("\n", trim($request->device_serials));
            foreach ($serials as $serial) {
                $serial = trim($serial);
                if (!empty($serial)) {
                    Device::create([
                        'certificate_id' => $certificate->id,
                        'serial_number' => $serial,
                        'device_type' => 'unknown', // Set a default value
                    ]);
                }
            }
        }

        return redirect()->route('certificates.edit', $certificate)
            ->with('success', 'Certificate updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Certificate $certificate)
    {
        $certificate->delete();

        return redirect()->route('certificates.index')
            ->with('success', 'Certificate deleted successfully.');
    }

    /**
     * Restore a soft-deleted certificate.
     */
    public function restore($id)
    {
        $certificate = Certificate::onlyTrashed()->findOrFail($id);
        $certificate->restore();
        return redirect()->route('certificates.index')->with('success', 'Certificate restored successfully.');
    }

    // New method to return contract JSON based on the provided role
    public function getContract(Request $request)
    {
        Log::info('CertificateController::getContract - Request Data', ['data' => $request->all()]);
        $role = $request->query('role');
        $serviceSelection = $request->query('service_selection');

        if (!$role) {
            return response()->json(['error' => 'Role is required'], 400);
        }

        // Determine the contract file based on the role and service selection
        $fileSuffix = null;
        if ($role === 'client') {
            $fileSuffix = match ($serviceSelection) {
                'certified_destruction' => 'certification',
                'uncertified_destruction' => 'waiver',
                'decline_destruction' => 'no-destruction',
                default => null,
            };

            // Log the requirement and file suffix for debugging
            Log::info('CertificateController::getContract - Requirement mapping', [
                'serviceSelection' => $serviceSelection,
                'fileSuffix' => $fileSuffix
            ]);

            if (!$fileSuffix) {
                return response()->json(['error' => 'Invalid service selection: ' . $serviceSelection], 400);
            }
        }

        // Build the file path - try new version first, then fall back to old version
        $baseFileName = "cert-contract-{$role}" . ($fileSuffix ? "-{$fileSuffix}" : "");
        $filePath = resource_path("data/{$baseFileName}-new.json");

        // If the new file doesn't exist, fall back to the old file
        if (!file_exists($filePath)) {
            $filePath = resource_path("data/{$baseFileName}.json");
        }

        if (!file_exists($filePath)) {
            Log::error("Contract file not found: {$filePath}");
            return response()->json(['error' => 'Contract file not found'], 404);
        }

        $content = json_decode(file_get_contents($filePath), true);

        // Check if this is the new format (has 'text' field instead of 'paragraphs')
        $isNewFormat = isset($content['text']);

        // Log the content structure for debugging
        Log::info('CertificateController::getContract - Contract content structure', [
            'role' => $role,
            'serviceSelection' => $serviceSelection,
            'filePath' => $filePath,
            'isNewFormat' => $isNewFormat,
            'hasText' => isset($content['text']),
            'hasParagraphs' => isset($content['paragraphs']),
            'hasPlaceholders' => isset($content['placeholders']),
            'placeholders' => $content['placeholders'] ?? []
        ]);

        if ($isNewFormat) {
            // Convert new format to old format for backward compatibility
            $paragraphs = explode('<br><br>', $content['text']);
            $content['paragraphs'] = $paragraphs;

            // Add section information
            $content['section'] = $content['section'] ?? '';
        } else {
            // For old format, we still need to handle technician_drives_serialized
            if ($role === 'technician') {
                $certificate = Certificate::find($request->route('certificate'));
                $content['technician_drives_serialized'] = $certificate->stats['technician_drives_serialized'] ?? '';
            }
        }

        return response()->json($content);
    }

    public function saveStats(Request $request, Certificate $certificate)
    {
        Log::info('CertificateController::saveStats - Request Data', ['data' => $request->all()]);

        $stats = $request->input('stats', []);

        if(isset($stats['service_selection'])){
            $certificate->service_selection = $stats['service_selection'];
            unset($stats['service_selection']);
        }

        if(isset($stats['client_manifest_drive_count'])){
            $certificate->client_manifest_drive_count = $stats['client_manifest_drive_count'];
            unset($stats['client_manifest_drive_count']);
        }

        if(isset($stats['client_manifest_device_count'])){
            $certificate->client_manifest_device_count = $stats['client_manifest_device_count'];
            unset($stats['client_manifest_device_count']);
        }

        if(isset($stats['etech_verified_drive_count'])){
            $certificate->etech_verified_drive_count = $stats['etech_verified_drive_count'];
            unset($stats['etech_verified_drive_count']);
        }

        if(isset($stats['etech_verified_device_count'])){
            $certificate->etech_verified_device_count = $stats['etech_verified_device_count'];
            unset($stats['etech_verified_device_count']);
        }

        $certificate->stats = array_merge($certificate->stats ?? [], $stats);
        $certificate->save();

        return response()->json(['success' => true, 'message' => 'Stats saved successfully']);
    }

    /**
     * Generate a PDF for the certificate
     */
    public function generatePdf(Certificate $certificate)
    {
        // Define the roles that would normally be required
        $requiredRoles = ['client', 'technician'];

        // If it's not a drop-off, driver signature would normally be required
        if (($certificate->stats['driver_pickup_dropoff'] ?? '') !== 'No') {
            $requiredRoles[] = 'driver';
        }

        // Warehouse signature would normally be required
        $requiredRoles[] = 'warehouse';

        // Check if there's a Chain of Custody document
        $cocFileId = $certificate->stats['coc_file_id'] ?? null;

        // Get image files attached to the certificate
        $imageFiles = $certificate->files()
            ->where('is_public', false) // Only include private files for security
            ->whereIn('extension', ['jpg', 'jpeg', 'png', 'gif', 'webp'])
            ->where(function($query) use ($cocFileId) {
                // Exclude the CoC file if it's an image
                if ($cocFileId) {
                    $query->where('id', '!=', $cocFileId);
                }
            })
            ->orderBy('created_at')
            ->get();

        // Check which signatures are missing (for informational purposes)
        $missingSignatures = [];
        foreach ($requiredRoles as $role) {
            // Skip driver role if it's a drop-off
            if ($role === 'driver' && ($certificate->stats['driver_pickup_dropoff'] ?? '') === 'No') {
                continue;
            }

            if (!$certificate->signatures()->where('role', $role)->exists()) {
                $missingSignatures[] = $role;
            }
        }

        // Log missing signatures
        if (!empty($missingSignatures)) {
            Log::info('Generating PDF with missing signatures', [
                'certificate_id' => $certificate->id,
                'missing_signatures' => $missingSignatures
            ]);
        }

        // Load all necessary relationships
        $certificate->load(['customer', 'devices', 'signatures']);

        // Get contract content for each role
        $contractData = [];
        foreach ($certificate->signatures as $signature) {
            // Skip if no contract text is stored
            if (empty($signature->contract_text)) {
                continue;
            }

            // Convert signature image to data URL for PDF
            $signatureImageData = null;
            if ($signature->hasSignatureImage()) {
                $imagePath = storage_path('app/public/' . $signature->signature_image_path);
                if (file_exists($imagePath)) {
                    $imageData = file_get_contents($imagePath);
                    $base64 = base64_encode($imageData);
                    $signatureImageData = 'data:image/png;base64,' . $base64;
                }
            }

            // Process contract text to ensure HTML is properly rendered
            $contractText = $signature->contract_text;

            // Replace any remaining placeholders in the contract text
            if ($signature->role === 'technician') {
                $contractText = str_replace('[TECHNICIAN_DRIVES_SERIALIZED]', $certificate->stats['technician_drives_serialized'] ?? 'Yes', $contractText);
                $contractText = str_replace('[TECHNICIAN_TOTAL_DRIVES_FOUND]', $certificate->stats['technician_total_drives_found'] ?? 'N/A', $contractText);
            }

            // Replace warehouse placeholders with default values when no manifest provided
            if ($signature->role === 'warehouse') {
                $contractText = str_replace('[WAREHOUSE_MANIFEST_PROVIDED]', $certificate->stats['warehouse_manifest_provided'] ?? 'No manifest provided', $contractText);
                $contractText = str_replace('[WAREHOUSE_DEVICE_COUNT]', $certificate->stats['warehouse_device_count'] ?? 'Devices not counted on receipt', $contractText);
                $contractText = str_replace('[WAREHOUSE_DRIVE_COUNT]', $certificate->stats['warehouse_drive_count'] ?? 'Drives not counted on receipt', $contractText);
            }

            // If the contract text doesn't contain HTML tags, convert newlines to <br> tags
            if (strip_tags($contractText) === $contractText) {
                $contractText = nl2br($contractText);
            }

            $contractData[$signature->role] = [
                'signature' => $signature,
                'signatureImageData' => $signatureImageData,
                'contractText' => $contractText // Use the processed contract text
            ];
        }

        // Create a unique filename
        $deviceCount = $certificate->devices->count();
        $version = CertificatePdf::where('certificate_id', $certificate->id)->count() + 1;
        $customerName = \Illuminate\Support\Str::slug($certificate->customer->name);
        $filename = $certificate->certificate_number . '-v' . $version . '-' . $customerName . '.pdf';

        // Load the device relationships
        $certificate->load(['devices.destroyedByUser']);

        // Load the footer information
        $footerData = $this->loadFooterData();

        // Load the company information
        $companyData = $this->loadCompanyData();

        // Load the certifying technician if available
        $certifyingTechnician = null;
        if ($certificate->certifying_user_id) {
            $certificate->load('certifyingTechnician');
            $certifyingTechnician = $certificate->certifyingTechnician;
        }

        // Check if we need to include a Chain of Custody document
        $cocFileId = $certificate->stats['coc_file_id'] ?? null;

        // Log the certificate stats for debugging
        Log::info('Certificate Stats', [
            'certificate_id' => $certificate->id,
            'coc_file_id' => $cocFileId,
            'stats' => $certificate->stats
        ]);

        // Make sure the files relationship is loaded
        $certificate->load('files');
        $cocFile = $cocFileId ? $certificate->files->firstWhere('id', $cocFileId) : null;

        // Log the CoC file details
        Log::info('CoC File Details', [
            'cocFileId' => $cocFileId,
            'cocFileFound' => $cocFile ? true : false,
            'cocFileDetails' => $cocFile ? [
                'id' => $cocFile->id,
                'original_filename' => $cocFile->original_filename,
                'extension' => $cocFile->extension,
                'mime_type' => $cocFile->mime_type,
                'filepath' => $cocFile->filepath
            ] : null,
            'filesCount' => $certificate->files->count(),
            'allFiles' => $certificate->files->map(function($file) {
                return [
                    'id' => $file->id,
                    'original_filename' => $file->original_filename,
                    'metadata' => $file->metadata
                ];
            })
        ]);

        // Check if we have a valid CoC file to include
        $validCocFile = false;
        if ($cocFile) {
            // Check if the file is actually marked as a CoC in its metadata
            $isCocInMetadata = false;
            if (isset($cocFile->metadata['is_coc']) && $cocFile->metadata['is_coc']) {
                $isCocInMetadata = true;
            }

            Log::info('CoC File Validation', [
                'extension' => strtolower($cocFile->extension),
                'isPdf' => strtolower($cocFile->extension) === 'pdf',
                'isCocInMetadata' => $isCocInMetadata,
                'metadata' => $cocFile->metadata
            ]);

            $validCocFile = strtolower($cocFile->extension) === 'pdf';
        }

        if ($validCocFile) {
            // We need to use FPDI to merge PDFs
            // First, generate the first part of the PDF (certificate details)
            $firstPartPdf = PDF::loadView('certificates.pdf-first-part', [
                'certificate' => $certificate,
                'contractData' => $contractData,
                'deviceCount' => $deviceCount,
                'version' => $version,
                'footerData' => $footerData,
                'companyData' => $companyData,
                'certifyingTechnician' => $certifyingTechnician,
                'cocFile' => $cocFile
            ])->setOptions([
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
                'isPhpEnabled' => true,
                'defaultFont' => 'Arial',
                'dpi' => 150,
                'defaultPaperSize' => 'letter',
                'defaultMediaType' => 'print',
                'enable_javascript' => false,
                'enable_remote' => true,
                'font_height_ratio' => 1.1,
                'chroot' => public_path(),
            ]);

            // Generate the images part of the PDF (if there are images)
            $imagesPdf = null;
            if ($imageFiles->count() > 0) {
                $imagesPdf = PDF::loadView('certificates.pdf-images', [
                    'certificate' => $certificate,
                    'imageFiles' => $imageFiles,
                ])->setOptions([
                    'isHtml5ParserEnabled' => true,
                    'isRemoteEnabled' => true,
                    'isPhpEnabled' => true,
                    'defaultFont' => 'Arial',
                    'dpi' => 150,
                    'defaultPaperSize' => 'letter',
                    'defaultMediaType' => 'print',
                    'enable_javascript' => false,
                    'enable_remote' => true,
                    'font_height_ratio' => 1.1,
                    'chroot' => public_path(),
                ]);
            }

            // Generate the last part of the PDF (device list)
            $lastPartPdf = PDF::loadView('certificates.pdf-last-part', [
                'certificate' => $certificate,
                'deviceCount' => $deviceCount,
                'footerData' => $footerData,
                'companyData' => $companyData,
            ])->setOptions([
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
                'isPhpEnabled' => true,
                'defaultFont' => 'Arial',
                'dpi' => 150,
                'defaultPaperSize' => 'letter',
                'defaultMediaType' => 'print',
                'enable_javascript' => false,
                'enable_remote' => true,
                'font_height_ratio' => 1.1,
                'chroot' => public_path(),
            ]);

            // Save temporary files
            $tempDir = storage_path('app/temp');
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            $firstPartPath = $tempDir . '/first_part.pdf';
            file_put_contents($firstPartPath, $firstPartPdf->output());

            // Save images PDF if it exists
            $imagesPartPath = null;
            if ($imagesPdf) {
                $imagesPartPath = $tempDir . '/images_part.pdf';
                file_put_contents($imagesPartPath, $imagesPdf->output());
            }

            $lastPartPath = $tempDir . '/last_part.pdf';
            file_put_contents($lastPartPath, $lastPartPdf->output());

            // Get the CoC file path
            $cocFilePath = storage_path('app/private/' . $cocFile->filepath);

            // Use Python's PyPDF2 to merge the PDFs (better compatibility with compressed PDFs)
            $mergedPdfPath = $tempDir . '/merged.pdf';

            // Create a placeholder in case the Python script fails
            $placeholderPdf = PDF::loadView('certificates.pdf-coc-placeholder', [
                'certificate' => $certificate,
                'cocFile' => $cocFile,
                'error' => 'The Chain of Custody document could not be merged due to compatibility issues.'
            ])->setOptions([
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
                'defaultFont' => 'Arial',
            ]);
            $placeholderPath = $tempDir . '/coc_placeholder.pdf';
            file_put_contents($placeholderPath, $placeholderPdf->output());

            // Try to use the Python script to merge PDFs
            $pythonScript = base_path('scripts/merge_pdfs.py');
            $pythonPath = env('PYTHON_PATH', 'python'); // Get Python path from .env file, fallback to 'python'
            $pythonPackages = env('PYTHON_PACKAGES', ''); // Get Python packages path from .env file
            $envPrefix = $pythonPackages ? "PYTHONPATH=\"$pythonPackages\" " : "";
            // Build command with optional images part
            $commandParts = ["$envPrefix\"$pythonPath\"", "\"$pythonScript\"", "\"$mergedPdfPath\"", "\"$firstPartPath\"", "\"$cocFilePath\""];
            if ($imagesPartPath) {
                $commandParts[] = "\"$imagesPartPath\"";
            }
            $commandParts[] = "\"$lastPartPath\"";
            $command = implode(' ', $commandParts);

            // Log the command and CoC file details for debugging
            Log::info('PDF Merge Command', [
                'command' => $command,
                'cocFile' => [
                    'id' => $cocFile->id,
                    'path' => $cocFilePath,
                    'exists' => file_exists($cocFilePath),
                    'size' => file_exists($cocFilePath) ? filesize($cocFilePath) : 'N/A',
                    'extension' => $cocFile->extension,
                    'mime_type' => $cocFile->mime_type
                ]
            ]);

            // Execute the Python script with more detailed error capturing
            $descriptorspec = [
                0 => ["pipe", "r"],  // stdin
                1 => ["pipe", "w"],  // stdout
                2 => ["pipe", "w"]   // stderr
            ];

            $process = proc_open($command, $descriptorspec, $pipes);
            $output = '';
            $error = '';
            $returnCode = -1;

            if (is_resource($process)) {
                // Close stdin
                fclose($pipes[0]);

                // Read stdout
                $output = stream_get_contents($pipes[1]);
                fclose($pipes[1]);

                // Read stderr
                $error = stream_get_contents($pipes[2]);
                fclose($pipes[2]);

                // Get return code
                $returnCode = proc_close($process);
            }

            // Log the output, error, and return code
            Log::info('Python Script Result', [
                'returnCode' => $returnCode,
                'output' => $output,
                'error' => $error,
                'mergedFileExists' => file_exists($mergedPdfPath),
                'mergedFileSize' => file_exists($mergedPdfPath) ? filesize($mergedPdfPath) : 'N/A'
            ]);

            // Check if the merge was successful
            if ($returnCode !== 0 || !file_exists($mergedPdfPath)) {
                // If Python script failed, try an alternative approach with just our PDFs
                Log::error('Python PDF merge failed: ' . $output . '\nError: ' . $error);

                // Use a simpler approach - just concatenate our PDFs with the placeholder
                $pythonPath = env('PYTHON_PATH', 'python'); // Get Python path from .env file again to ensure it's available
                $pythonPackages = env('PYTHON_PACKAGES', ''); // Get Python packages path from .env file
                $envPrefix = $pythonPackages ? "PYTHONPATH=\"$pythonPackages\" " : "";
                $command = "$envPrefix\"$pythonPath\" \"$pythonScript\" \"$mergedPdfPath\" \"$firstPartPath\" \"$placeholderPath\" \"$lastPartPath\"";

                // Execute the alternative command
                $process = proc_open($command, $descriptorspec, $pipes);
                $output = '';
                $error = '';
                $returnCode = -1;

                if (is_resource($process)) {
                    fclose($pipes[0]);
                    $output = stream_get_contents($pipes[1]);
                    fclose($pipes[1]);
                    $error = stream_get_contents($pipes[2]);
                    fclose($pipes[2]);
                    $returnCode = proc_close($process);
                }

                if ($returnCode !== 0 || !file_exists($mergedPdfPath)) {
                    // If that still fails, just use the standard PDF without the CoC document
                    Log::error('Alternative PDF merge also failed: ' . $output . '\nError: ' . $error);

                    // Generate the PDF with the same templates but without the CoC document
                    // First, generate the first part of the PDF (certificate details)
                    $firstPartPdf = PDF::loadView('certificates.pdf-first-part', [
                        'certificate' => $certificate,
                        'contractData' => $contractData,
                        'deviceCount' => $deviceCount,
                        'version' => $version,
                        'footerData' => $footerData,
                        'companyData' => $companyData,
                        'certifyingTechnician' => $certifyingTechnician,
                        'cocFile' => $cocFile
                    ])->setOptions([
                        'isHtml5ParserEnabled' => true,
                        'isRemoteEnabled' => true,
                        'isPhpEnabled' => true,
                        'defaultFont' => 'Arial',
                        'dpi' => 150,
                        'defaultPaperSize' => 'letter',
                        'defaultMediaType' => 'print',
                        'enable_javascript' => false,
                        'enable_remote' => true,
                        'font_height_ratio' => 1.1,
                        'chroot' => public_path(),
                    ]);

                    // Generate the images part of the PDF (if there are images)
                    $imagesPdf = null;
                    if ($imageFiles->count() > 0) {
                        $imagesPdf = PDF::loadView('certificates.pdf-images', [
                            'certificate' => $certificate,
                            'imageFiles' => $imageFiles,
                        ])->setOptions([
                            'isHtml5ParserEnabled' => true,
                            'isRemoteEnabled' => true,
                            'isPhpEnabled' => true,
                            'defaultFont' => 'Arial',
                            'dpi' => 150,
                            'defaultPaperSize' => 'letter',
                            'defaultMediaType' => 'print',
                            'enable_javascript' => false,
                            'enable_remote' => true,
                            'font_height_ratio' => 1.1,
                            'chroot' => public_path(),
                        ]);
                    }

                    // Generate the last part of the PDF (device list)
                    $lastPartPdf = PDF::loadView('certificates.pdf-last-part', [
                        'certificate' => $certificate,
                        'deviceCount' => $deviceCount,
                        'footerData' => $footerData,
                        'companyData' => $companyData,
                    ])->setOptions([
                        'isHtml5ParserEnabled' => true,
                        'isRemoteEnabled' => true,
                        'isPhpEnabled' => true,
                        'defaultFont' => 'Arial',
                        'dpi' => 150,
                        'defaultPaperSize' => 'letter',
                        'defaultMediaType' => 'print',
                        'enable_javascript' => false,
                        'enable_remote' => true,
                        'font_height_ratio' => 1.1,
                        'chroot' => public_path(),
                    ]);

                    // Save images PDF if it exists
                    $imagesPartPath = null;
                    if ($imagesPdf) {
                        $imagesPartPath = $tempDir . '/images_part.pdf';
                        file_put_contents($imagesPartPath, $imagesPdf->output());
                    }

                    // Try one more time to merge the PDFs
                    $pythonPackages = env('PYTHON_PACKAGES', ''); // Get Python packages path from .env file
                    $envPrefix = $pythonPackages ? "PYTHONPATH=\"$pythonPackages\" " : "";

                    // Build command with optional images part
                    $commandParts = ["$envPrefix\"$pythonPath\"", "\"$pythonScript\"", "\"$mergedPdfPath\"", "\"$firstPartPath\""];
                    if ($imagesPartPath) {
                        $commandParts[] = "\"$imagesPartPath\"";
                    }
                    $commandParts[] = "\"$lastPartPath\"";
                    $command = implode(' ', $commandParts);

                    exec($command, $output, $returnCode);

                    if ($returnCode === 0 && file_exists($mergedPdfPath)) {
                        $pdfContent = file_get_contents($mergedPdfPath);
                    } else {
                        // If all else fails, just use the first part PDF
                        $pdfContent = $firstPartPdf->output();
                    }
                } else {
                    // The alternative merge worked
                    $pdfContent = file_get_contents($mergedPdfPath);
                }
            } else {
                // The Python script worked
                $pdfContent = file_get_contents($mergedPdfPath);
            }

            // Clean up temporary files
            @unlink($mergedPdfPath);
            @unlink($firstPartPath);
            @unlink($lastPartPath);
            if (isset($imagesPartPath) && file_exists($imagesPartPath)) {
                @unlink($imagesPartPath);
            }
            if (isset($placeholderPath) && file_exists($placeholderPath)) {
                @unlink($placeholderPath);
            }
        } else {
            // Generate the PDF with the same templates but without merging with a CoC document
            // First, generate the first part of the PDF (certificate details)
            $firstPartPdf = PDF::loadView('certificates.pdf-first-part', [
                'certificate' => $certificate,
                'contractData' => $contractData,
                'deviceCount' => $deviceCount,
                'version' => $version,
                'footerData' => $footerData,
                'companyData' => $companyData,
                'certifyingTechnician' => $certifyingTechnician,
                'cocFile' => $cocFile // Include CoC file if it exists
            ])->setOptions([
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
                'isPhpEnabled' => true,
                'defaultFont' => 'Arial',
                'dpi' => 150,
                'defaultPaperSize' => 'letter',
                'defaultMediaType' => 'print',
                'enable_javascript' => false,
                'enable_remote' => true,
                'font_height_ratio' => 1.1,
                'chroot' => public_path(),
            ]);

            // Generate the images part of the PDF (if there are images)
            $imagesPdf = null;
            if ($imageFiles->count() > 0) {
                $imagesPdf = PDF::loadView('certificates.pdf-images', [
                    'certificate' => $certificate,
                    'imageFiles' => $imageFiles,
                ])->setOptions([
                    'isHtml5ParserEnabled' => true,
                    'isRemoteEnabled' => true,
                    'isPhpEnabled' => true,
                    'defaultFont' => 'Arial',
                    'dpi' => 150,
                    'defaultPaperSize' => 'letter',
                    'defaultMediaType' => 'print',
                    'enable_javascript' => false,
                    'enable_remote' => true,
                    'font_height_ratio' => 1.1,
                    'chroot' => public_path(),
                ]);
            }

            // Generate the last part of the PDF (device list)
            $lastPartPdf = PDF::loadView('certificates.pdf-last-part', [
                'certificate' => $certificate,
                'deviceCount' => $deviceCount,
                'footerData' => $footerData,
                'companyData' => $companyData,
            ])->setOptions([
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
                'isPhpEnabled' => true,
                'defaultFont' => 'Arial',
                'dpi' => 150,
                'defaultPaperSize' => 'letter',
                'defaultMediaType' => 'print',
                'enable_javascript' => false,
                'enable_remote' => true,
                'font_height_ratio' => 1.1,
                'chroot' => public_path(),
            ]);

            // Save temporary files
            $tempDir = storage_path('app/temp');
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            $firstPartPath = $tempDir . '/first_part.pdf';
            file_put_contents($firstPartPath, $firstPartPdf->output());

            // Save images PDF if it exists
            $imagesPartPath = null;
            if ($imagesPdf) {
                $imagesPartPath = $tempDir . '/images_part.pdf';
                file_put_contents($imagesPartPath, $imagesPdf->output());
            }

            $lastPartPath = $tempDir . '/last_part.pdf';
            file_put_contents($lastPartPath, $lastPartPdf->output());

            // Use Python's PyPDF2 to merge the PDFs
            $mergedPdfPath = $tempDir . '/merged.pdf';
            $pythonScript = base_path('scripts/merge_pdfs.py');
            $pythonPath = env('PYTHON_PATH', 'python'); // Get Python path from .env file, fallback to 'python'
            $pythonPackages = env('PYTHON_PACKAGES', ''); // Get Python packages path from .env file
            $envPrefix = $pythonPackages ? "PYTHONPATH=\"$pythonPackages\" " : "";
            // Build command with optional images part
            $commandParts = ["$envPrefix\"$pythonPath\"", "\"$pythonScript\"", "\"$mergedPdfPath\"", "\"$firstPartPath\""];
            if ($imagesPartPath) {
                $commandParts[] = "\"$imagesPartPath\"";
            }
            $commandParts[] = "\"$lastPartPath\"";
            $command = implode(' ', $commandParts);

            // Execute the Python script
            $descriptorspec = [
                0 => ["pipe", "r"],  // stdin
                1 => ["pipe", "w"],  // stdout
                2 => ["pipe", "w"]   // stderr
            ];

            $process = proc_open($command, $descriptorspec, $pipes);
            $output = '';
            $error = '';
            $returnCode = -1;

            if (is_resource($process)) {
                // Close stdin
                fclose($pipes[0]);

                // Read stdout
                $output = stream_get_contents($pipes[1]);
                fclose($pipes[1]);

                // Read stderr
                $error = stream_get_contents($pipes[2]);
                fclose($pipes[2]);

                // Get return code
                $returnCode = proc_close($process);
            }

            // Check if the merge was successful
            if ($returnCode !== 0 || !file_exists($mergedPdfPath)) {
                // If Python script failed, just concatenate the PDFs using PHP
                Log::error('Python PDF merge failed for standard PDF: ' . $output . '\nError: ' . $error);

                // Fallback to using the first part PDF only
                $pdfContent = $firstPartPdf->output();

                // Just use the first part as a fallback
                Log::info('Using first part PDF only as fallback');
            } else {
                // The Python script worked
                $pdfContent = file_get_contents($mergedPdfPath);
            }

            // Clean up temporary files
            @unlink($mergedPdfPath);
            @unlink($firstPartPath);
            @unlink($lastPartPath);
            if (isset($imagesPartPath) && file_exists($imagesPartPath)) {
                @unlink($imagesPartPath);
            }
        }

        // Make sure the directory exists
        $directory = 'certificates/' . $certificate->id;
        Storage::disk('public')->makeDirectory($directory);

        // Save the PDF to storage
        $path = $directory . '/' . $filename;
        Storage::disk('public')->put($path, $pdfContent);

        // Create a record in the database
        $certificatePdf = CertificatePdf::create([
            'certificate_id' => $certificate->id,
            'file_path' => $path,
            'file_name' => $filename,
            'generated_by' => Auth::id(),
            'version' => $version,
            'device_count' => $deviceCount,
            'is_current' => true
        ]);

        // Mark this as the current PDF
        $certificatePdf->markAsCurrent();

        // Prepare success message
        $successMessage = 'Certificate PDF generated successfully.';

        // Add warning about missing signatures if applicable
        if (!empty($missingSignatures)) {
            $missingRoles = array_map(function($role) {
                return ucfirst($role);
            }, $missingSignatures);

            $successMessage .= ' Note: The following signatures were missing and their pages were excluded: ' . implode(', ', $missingRoles) . '.';
        }

        return redirect()->route('certificates.edit', $certificate)
            ->with('success', $successMessage);
    }

    /**
     * Download a specific PDF
     */
    public function downloadPdf(CertificatePdf $pdf)
    {
        if (!Storage::disk('public')->exists($pdf->file_path)) {
            return redirect()->back()->with('error', 'PDF file not found.');
        }

        return response()->download(storage_path('app/public/' . $pdf->file_path), $pdf->file_name, [
            'Content-Type' => 'application/pdf',
        ]);
    }

    /**
     * View a specific PDF in the browser
     */
    public function viewPdf(CertificatePdf $pdf)
    {
        if (!Storage::disk('public')->exists($pdf->file_path)) {
            return redirect()->back()->with('error', 'PDF file not found.');
        }

        return response()->file(storage_path('app/public/' . $pdf->file_path), [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $pdf->file_name . '"',
        ]);
    }

    /**
     * Load the footer data from the JSON file
     */
    private function loadFooterData()
    {
        $filePath = resource_path('data/cert-footer.json');

        if (!file_exists($filePath)) {
            // Return default footer data if file doesn't exist
            return [
                'certificate_info_line' => 'This certificate was generated on [GENERATION_DATE] | Certificate Number: [CERTIFICATE_NUMBER] | Version: [VERSION]',
                'record_line' => 'This document serves as an official record of destruction and should be retained for your records.',
                'guarantee_line' => 'These devices were certified and are guaranteed to be completely destroyed by E-Tech Recyclers & Asset Solutions LLC.',
                'data_line' => 'The data is irrecoverable and has been processed according to industry-standard destruction protocols.'
            ];
        }

        try {
            $content = json_decode(file_get_contents($filePath), true);
            return $content;
        } catch (\Exception $e) {
            Log::error('Error loading footer data', ['message' => $e->getMessage()]);
            // Return default footer data if there's an error
            return [
                'certificate_info_line' => 'This certificate was generated on [GENERATION_DATE] | Certificate Number: [CERTIFICATE_NUMBER] | Version: [VERSION]',
                'record_line' => 'This document serves as an official record of destruction and should be retained for your records.',
                'guarantee_line' => 'These devices were certified and are guaranteed to be completely destroyed by E-Tech Recyclers & Asset Solutions LLC.',
                'data_line' => 'The data is irrecoverable and has been processed according to industry-standard destruction protocols.'
            ];
        }
    }

    /**
     * Auto-save certificate fields via AJAX
     */
    public function autoSave(Request $request, Certificate $certificate)
    {
        $validatedData = $request->validate([
            'field' => 'required|string',
            'value' => 'required',
        ]);

        $field = $validatedData['field'];
        $value = $validatedData['value'];

        // Only allow specific fields to be auto-saved
        if (!in_array($field, ['status', 'scheduled_destruction_date', 'pickup_dropoff_date', 'destruction_method', 'destruction_location', 'notes'])) {
            return response()->json(['error' => 'Field not allowed for auto-save'], 422);
        }

        // If status is changed to completed and there's no actual_destruction_date, set it to now
        if ($field === 'status' && $value === 'completed' && !$certificate->actual_destruction_date) {
            $certificate->actual_destruction_date = now();
        }

        $certificate->$field = $value;
        $certificate->save();

        return response()->json([
            'success' => true,
            'message' => ucfirst(str_replace('_', ' ', $field)) . ' updated successfully',
            'certificate' => $certificate
        ]);
    }

    /**
     * Search certificates by query
     */
    public function search($query)
    {
        $certificates = Certificate::with('customer')
            ->where('certificate_number', 'like', "%{$query}%")
            ->orWhereHas('customer', function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%");
            })
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json($certificates);
    }

    /**
     * Load the company data from the JSON file
     */
    private function loadCompanyData()
    {
        $filePath = resource_path('data/cert-company-info.json');

        if (!file_exists($filePath)) {
            // Return default company data if file doesn't exist
            return [
                'name' => 'Enter Company Name Here',
                'website' => 'www.testsite.com',
                'phone' => '(*************',
                'address' => '123 Tech Way, Suite 100, Anytown, TX 12345',
                'email' => '<EMAIL>',
                'logo_path' => 'img/my.png'
            ];
        }

        try {
            $content = json_decode(file_get_contents($filePath), true);

            // Map the keys from the JSON file to the keys expected by the template
            return [
                'name' => $content['company_name'] ?? 'E-Tech Recyclers & Asset Solutions LLC',
                'website' => $content['company_website'] ?? 'www.etechrecyclers.com',
                'phone' => $content['company_phone'] ?? '(*************',
                'address' => $content['company_address'] ?? '2854 N Prospect St. Colorado Springs, CO 80907',
                'email' => $content['company_email'] ?? '<EMAIL>',
                'logo_path' => $content['company_logo_path'] ?? 'img/etech-cert-destruction-logo.png'
            ];
        } catch (\Exception $e) {
            Log::error('Error loading company data', ['message' => $e->getMessage(), 'file' => $filePath]);
            // Return default company data if there's an error
            return [
                'name' => 'E-Tech Recyclers & Asset Solutions LLC',
                'website' => 'www.etechrecyclers.com',
                'phone' => '(*************',
                'address' => '2854 N Prospect St. Colorado Springs, CO 80907',
                'email' => '<EMAIL>',
                'logo_path' => 'img/etech-cert-destruction-logo.png'
            ];
        }
    }

    /**
     * View all PDFs for a certificate
     */
    public function viewPdfs(Certificate $certificate)
    {
        $pdfs = CertificatePdf::getAllForCertificate($certificate->id);
        return view('certificates.pdfs', compact('certificate', 'pdfs'));
    }
}
