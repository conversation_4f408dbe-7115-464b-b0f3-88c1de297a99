<?php

namespace App\Http\Controllers;

use App\Models\TimeCard;
use App\Models\TimePunch;
use App\Models\GlobalConfig;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TimePunchController extends Controller
{
    /**
     * Clock in action
     */
    public function clockIn(Request $request)
    {
        $user = Auth::user();
        $timeCard = $user->getTodayTimeCard();
        $notes = $request->input('notes');

        // Check if already clocked in
        if ($timeCard->isClockedIn() && !$timeCard->isOnBreak()) {
            return response()->json([
                'success' => false,
                'message' => 'You are already clocked in.'
            ], 400);
        }

        // If on break, this is a break end (break_in)
        $type = $timeCard->isOnBreak() ? 'break_in' : 'clock_in';

        // Create the punch
        $punch = TimePunch::createPunch(
            $timeCard->id,
            $type,
            $notes
        );

        // Recalculate hours
        $hours = $timeCard->calculateHours();

        // Get updated punches for the view
        $punches = $timeCard->allValidPunches()->orderBy('punch_time', 'desc')->get();
        $timezone = GlobalConfig::getTimeZone();
        $punchesHtml = view('timecards.partials.punches-list', compact('punches', 'timezone'))->render();

        return response()->json([
            'success' => true,
            'message' => $type === 'break_in' ? 'Break ended successfully.' : 'Clocked in successfully.',
            'punch' => $punch,
            'status' => $timeCard->getCurrentStatus(),
            'total_hours' => $hours['total_hours'],
            'total_break_hours' => $hours['total_break_hours'],
            'punches_html' => $punchesHtml,
            'last_clock_in_time' => $type === 'break_in' ? $punch->punch_time->toIso8601String() : null,
            'last_break_start_time' => null
        ]);
    }

    /**
     * Clock out action
     */
    public function clockOut(Request $request)
    {
        $user = Auth::user();
        $timeCard = $user->getTodayTimeCard();
        $notes = $request->input('notes');

        // Check if already clocked out
        if (!$timeCard->isClockedIn() && !$timeCard->isOnBreak()) {
            return response()->json([
                'success' => false,
                'message' => 'You are not clocked in.'
            ], 400);
        }

        // Create the punch
        $punch = TimePunch::createPunch(
            $timeCard->id,
            'clock_out',
            $notes
        );

        // Recalculate hours
        $hours = $timeCard->calculateHours();

        // Get updated punches for the view
        $punches = $timeCard->allValidPunches()->orderBy('punch_time', 'desc')->get();
        $timezone = GlobalConfig::getTimeZone();
        $punchesHtml = view('timecards.partials.punches-list', compact('punches', 'timezone'))->render();

        return response()->json([
            'success' => true,
            'message' => 'Clocked out successfully.',
            'punch' => $punch,
            'status' => 'clock_out',
            'total_hours' => $hours['total_hours'],
            'total_break_hours' => $hours['total_break_hours'],
            'punches_html' => $punchesHtml
        ]);
    }

    /**
     * Break out action (start break)
     */
    public function breakOut(Request $request)
    {
        $user = Auth::user();
        $timeCard = $user->getTodayTimeCard();
        $notes = $request->input('notes');

        // Check if already on break or not clocked in
        if ($timeCard->isOnBreak()) {
            return response()->json([
                'success' => false,
                'message' => 'You are already on break.'
            ], 400);
        }

        if (!$timeCard->isClockedIn()) {
            return response()->json([
                'success' => false,
                'message' => 'You need to clock in before taking a break.'
            ], 400);
        }

        // Create the punch (break_out)
        $punch = TimePunch::createPunch(
            $timeCard->id,
            'break_out',
            $notes
        );

        // Recalculate hours
        $hours = $timeCard->calculateHours();

        // Get updated punches for the view
        $punches = $timeCard->allValidPunches()->orderBy('punch_time', 'desc')->get();
        $timezone = GlobalConfig::getTimeZone();
        $punchesHtml = view('timecards.partials.punches-list', compact('punches', 'timezone'))->render();

        return response()->json([
            'success' => true,
            'message' => 'Break started successfully.',
            'punch' => $punch,
            'status' => 'break',
            'total_hours' => $hours['total_hours'],
            'total_break_hours' => $hours['total_break_hours'],
            'punches_html' => $punchesHtml,
            'last_break_start_time' => $punch->punch_time->toIso8601String()
        ]);
    }

    /**
     * Break in action (end break)
     */
    public function breakIn(Request $request)
    {
        $user = Auth::user();
        $timeCard = $user->getTodayTimeCard();
        $notes = $request->input('notes');

        // Check if not on break
        if (!$timeCard->isOnBreak()) {
            return response()->json([
                'success' => false,
                'message' => 'You are not on break.'
            ], 400);
        }

        // Create the punch (break_in)
        $punch = TimePunch::createPunch(
            $timeCard->id,
            'break_in',
            $notes
        );

        // Recalculate hours
        $hours = $timeCard->calculateHours();

        // Get updated punches for the view
        $punches = $timeCard->allValidPunches()->orderBy('punch_time', 'desc')->get();
        $timezone = GlobalConfig::getTimeZone();
        $punchesHtml = view('timecards.partials.punches-list', compact('punches', 'timezone'))->render();

        return response()->json([
            'success' => true,
            'message' => 'Break ended successfully.',
            'punch' => $punch,
            'status' => 'clock_in',
            'total_hours' => $hours['total_hours'],
            'total_break_hours' => $hours['total_break_hours'],
            'punches_html' => $punchesHtml,
            'last_clock_in_time' => $punch->punch_time->toIso8601String(),
            'last_break_start_time' => null
        ]);
    }

    /**
     * Add a manual punch to a time card
     */
    public function addManualPunch(Request $request, TimeCard $timecard)
    {
        $user = Auth::user();
        $canEditAll = $user->hasPermission('edit_timecards');
        $canEditOwn = $user->hasPermission('edit_own_timecards');

        // Check if user has permission to edit this time card
        if (!$canEditAll && (!$canEditOwn || $timecard->user_id !== $user->id)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to edit this time card.'
                ], 403);
            }
            return redirect()->route('dashboard')->with('error', 'You do not have permission to edit this time card.');
        }

        try {
            // Check if this is a PTO punch (sick time or vacation time)
            $isPtoPunch = in_array($request->input('type'), ['sick_time', 'vacation_time']);

            // If this is a PTO punch, check if the user has permission to manage PTO
            if ($isPtoPunch && !$user->hasPermission('manage_paid_time_off')) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'You do not have permission to add paid time off.'
                    ], 403);
                }
                return redirect()->route('dashboard')->with('error', 'You do not have permission to add paid time off.');
            }

            $validated = $request->validate([
                'type' => 'required|in:clock_in,clock_out,break_in,break_out,sick_time,vacation_time',
                'punch_time' => 'required',
                'notes' => 'nullable|string',
            ]);

            // For PTO punches, notes field is required and should contain the hours in HH:MM:SS format
            if ($isPtoPunch) {
                $request->validate([
                    'notes' => 'required|regex:/^\d{1,2}:\d{2}(:\d{2})?$/'
                ], [
                    'notes.required' => 'For PTO entries, you must specify the hours in HH:MM:SS format.',
                    'notes.regex' => 'Hours must be in the format HH:MM:SS or HH:MM (e.g., 8:00:00 or 8:00).'
                ]);
            }

            // Parse the punch time
            try {
                $punchTime = Carbon::parse($validated['punch_time']);
                $validated['punch_time'] = $punchTime->format('Y-m-d H:i:s');

                // Check if the punch date matches the time card date
                $punchDate = $punchTime->format('Y-m-d');
                $timecardDate = Carbon::parse($timecard->date)->format('Y-m-d');

                if ($punchDate !== $timecardDate) {
                    if ($request->expectsJson()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'The punch date must match the time card date (' . $timecardDate . ').'
                        ], 422);
                    }
                    return redirect()->back()->withErrors(['punch_time' => 'The punch date must match the time card date (' . $timecardDate . ').'])->withInput();
                }
            } catch (\Exception $e) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid date format. Please use a valid date and time format.'
                    ], 422);
                }
                return redirect()->back()->withErrors(['punch_time' => 'Invalid date format'])->withInput();
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $e->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($e->errors())->withInput();
        }

        // Create the punch
        try {
            $punch = TimePunch::createPunch(
                $timecard->id,
                $validated['type'],
                $validated['notes'],
                Carbon::parse($validated['punch_time']),
                null,
                $user->id
            );

            // Recalculate hours
            $timecard->calculateHours();
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 422);
            }
            return redirect()->back()->withErrors(['punch_time' => $e->getMessage()])->withInput();
        }

        // If this is an AJAX request, return JSON
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Punch added successfully.',
                'punch' => $punch,
                'timecard' => [
                    'total_hours' => $timecard->total_hours,
                    'total_break_hours' => $timecard->total_break_hours,
                    'net_hours' => number_format($timecard->timeToDecimalHours($timecard->total_hours) - $timecard->timeToDecimalHours($timecard->total_break_hours), 2)
                ]
            ]);
        }

        // Otherwise, redirect
        return redirect()->route('timecards.edit', $timecard->id)
            ->with('success', 'Punch added successfully.');
    }

    /**
     * Show the form for editing a punch
     */
    public function edit(TimePunch $punch)
    {
        $user = Auth::user();
        $timecard = $punch->timeCard;
        $canEditAll = $user->hasPermission('edit_timecards');
        $canEditOwn = $user->hasPermission('edit_own_timecards');

        // Check if user has permission to edit this time card
        if (!$canEditAll && (!$canEditOwn || $timecard->user_id !== $user->id)) {
            return redirect()->route('dashboard')->with('error', 'You do not have permission to edit this punch.');
        }

        $timezone = GlobalConfig::getTimeZone();

        return view('timecards.edit-punch', compact('punch', 'timecard', 'timezone'));
    }

    /**
     * Update a punch
     */
    public function update(Request $request, TimePunch $punch)
    {
        $user = Auth::user();
        $timecard = $punch->timeCard;
        $canEditAll = $user->hasPermission('edit_timecards');
        $canEditOwn = $user->hasPermission('edit_own_timecards');

        // Check if user has permission to edit this time card
        if (!$canEditAll && (!$canEditOwn || $timecard->user_id !== $user->id)) {
            return redirect()->route('dashboard')->with('error', 'You do not have permission to edit this punch.');
        }

        // Check if this is a PTO punch (sick time or vacation time)
        $isPtoPunch = in_array($request->input('type'), ['sick_time', 'vacation_time']);

        // If this is a PTO punch, check if the user has permission to manage PTO
        if ($isPtoPunch && !$user->hasPermission('manage_paid_time_off')) {
            return redirect()->route('dashboard')->with('error', 'You do not have permission to add paid time off.');
        }

        // First validate without the date format constraint
        $validated = $request->validate([
            'type' => 'required|in:clock_in,clock_out,break_in,break_out,sick_time,vacation_time',
            'punch_time' => 'required',
            'notes' => 'nullable|string',
        ]);

        // For PTO punches, notes field is required and should contain the hours in HH:MM:SS format
        if ($isPtoPunch) {
            $request->validate([
                'notes' => 'required|regex:/^\d{1,2}:\d{2}(:\d{2})?$/'
            ], [
                'notes.required' => 'For PTO entries, you must specify the hours in HH:MM:SS format.',
                'notes.regex' => 'Hours must be in the format HH:MM:SS or HH:MM (e.g., 8:00:00 or 8:00).'
            ]);
        }

        // Parse the punch time
        try {
            $punchTime = Carbon::parse($validated['punch_time']);
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['punch_time' => 'The punch time field must be a valid date and time.'])
                ->withInput();
        }

        // Check if the punch date matches the time card date
        $punchDate = $punchTime->format('Y-m-d');
        $timecardDate = Carbon::parse($timecard->date)->format('Y-m-d');

        if ($punchDate !== $timecardDate) {
            return redirect()->back()
                ->withErrors(['punch_time' => 'The punch date must match the time card date (' . $timecardDate . ').'])
                ->withInput();
        }

        // Create a new punch that replaces the old one
        try {
            TimePunch::createPunch(
                $timecard->id,
                $validated['type'],
                $validated['notes'],
                $punchTime,
                $punch->id,
                $user->id
            );

            // Recalculate hours
            $timecard->calculateHours();
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['punch_time' => $e->getMessage()])
                ->withInput();
        }

        return redirect()->route('timecards.edit', $timecard->id)
            ->with('success', 'Punch updated successfully.');
    }

    /**
     * Delete a punch
     */
    public function destroy(TimePunch $punch)
    {
        $user = Auth::user();
        $timecard = $punch->timeCard;
        $canEditAll = $user->hasPermission('edit_timecards');
        $canEditOwn = $user->hasPermission('edit_own_timecards');

        // Check if user has permission to edit this time card
        if (!$canEditAll && (!$canEditOwn || $timecard->user_id !== $user->id)) {
            return redirect()->route('dashboard')->with('error', 'You do not have permission to delete this punch.');
        }

        // Soft delete the punch
        $punch->delete();

        // Recalculate hours
        $timecard->calculateHours();

        return redirect()->route('timecards.edit', $timecard->id)
            ->with('success', 'Punch deleted successfully.');
    }

    /**
     * Update a punch via AJAX
     */
    public function ajaxUpdate(Request $request, TimePunch $punch)
    {
        $user = Auth::user();
        $timecard = $punch->timeCard;
        $canEditAll = $user->hasPermission('edit_timecards');
        $canEditOwn = $user->hasPermission('edit_own_timecards');

        // Check if user has permission to edit this time card
        if (!$canEditAll && (!$canEditOwn || $timecard->user_id !== $user->id)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to edit this punch.'
            ], 403);
        }

        // Check if this is a PTO punch (sick time or vacation time)
        $isPtoPunch = in_array($request->input('type'), ['sick_time', 'vacation_time']);

        // If this is a PTO punch, check if the user has permission to manage PTO
        if ($isPtoPunch && !$user->hasPermission('manage_paid_time_off')) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to add paid time off.'
            ], 403);
        }

        // Validate the request with more flexible date format
        try {
            $validated = $request->validate([
                'type' => 'sometimes|required|in:clock_in,clock_out,break_in,break_out,sick_time,vacation_time',
                'punch_time' => 'sometimes|required',
                'notes' => 'nullable|string',
            ]);

            // For PTO punches, notes field is required and should contain the hours in HH:MM:SS format
            if ($isPtoPunch) {
                $request->validate([
                    'notes' => 'required|regex:/^\d{1,2}:\d{2}(:\d{2})?$/'
                ], [
                    'notes.required' => 'For PTO entries, you must specify the hours in HH:MM:SS format.',
                    'notes.regex' => 'Hours must be in the format HH:MM:SS or HH:MM (e.g., 8:00:00 or 8:00).'
                ]);
            }

            // Parse the punch time if it exists
            if (isset($validated['punch_time'])) {
                try {
                    $punchTime = Carbon::parse($validated['punch_time']);
                    $validated['punch_time'] = $punchTime->format('Y-m-d H:i:s');

                    // Check if the punch date matches the time card date
                    $punchDate = $punchTime->format('Y-m-d');
                    $timecardDate = Carbon::parse($timecard->date)->format('Y-m-d');

                    if ($punchDate !== $timecardDate) {
                        return response()->json([
                            'success' => false,
                            'message' => 'The punch date must match the time card date (' . $timecardDate . ').'
                        ], 422);
                    }
                } catch (\Exception $e) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid date format. Please use YYYY-MM-DD HH:MM:SS format.'
                    ], 422);
                }
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        }

        // Create a new punch that replaces the old one
        try {
            $newPunch = TimePunch::createPunch(
                $timecard->id,
                $validated['type'] ?? $punch->type,
                $validated['notes'] ?? $punch->notes,
                isset($validated['punch_time']) ? Carbon::parse($validated['punch_time']) : $punch->punch_time,
                $punch->id,
                $user->id
            );

            // Recalculate hours
            $timecard->calculateHours();
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }

        // Get updated punches for the response
        $updatedPunches = $timecard->allValidPunches()->orderBy('punch_time')->get();

        // Get timezone for formatting
        $timezone = GlobalConfig::getTimeZone();

        // Format the time for display
        $formattedTime = $newPunch->punch_time->setTimezone($timezone)->format('g:i:s A');

        return response()->json([
            'success' => true,
            'message' => 'Punch updated successfully.',
            'punch' => $newPunch,
            'punch_time' => $newPunch->punch_time->toIso8601String(),
            'formatted_time' => $formattedTime,
            'timecard' => [
                'total_hours' => $timecard->total_hours,
                'total_break_hours' => $timecard->total_break_hours,
                'total_sick_time' => $timecard->total_sick_time,
                'total_vacation_time' => $timecard->total_vacation_time,
                'net_hours' => number_format($timecard->timeToDecimalHours($timecard->total_hours) - $timecard->timeToDecimalHours($timecard->total_break_hours), 2)
            ],
            'punches' => $updatedPunches
        ]);
    }


}
