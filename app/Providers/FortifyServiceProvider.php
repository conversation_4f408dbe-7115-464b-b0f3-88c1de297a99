<?php

namespace App\Providers;

use App\Actions\Fortify\CreateNewUser;
use App\Actions\Fortify\ResetUserPassword;
use App\Actions\Fortify\UpdateUserPassword;
use App\Actions\Fortify\UpdateUserProfileInformation;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use Laravel\Fortify\Fortify;

class FortifyServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Fortify::createUsersUsing(CreateNewUser::class);
        Fortify::updateUserProfileInformationUsing(UpdateUserProfileInformation::class);
        Fortify::updateUserPasswordsUsing(UpdateUserPassword::class);
        Fortify::resetUserPasswordsUsing(ResetUserPassword::class);

        // Set the login view to our welcome page
        Fortify::loginView(function () {
            return redirect('/');
        });

        // Set the home path for successful logins
        config(['fortify.home' => '/dashboard']);

        // Handle failed login attempts
        app()->singleton(\Laravel\Fortify\Actions\AttemptToAuthenticate::class, function ($app) {
            return new class($app->make(\Illuminate\Contracts\Auth\StatefulGuard::class),
                            $app->make(\Laravel\Fortify\LoginRateLimiter::class))
                            extends \Laravel\Fortify\Actions\AttemptToAuthenticate {
                protected function throwFailedAuthenticationException($request) {
                    $this->limiter->increment($request);

                    return redirect('/')
                        ->withInput($request->only('email', 'remember'))
                        ->withErrors([
                            'email' => __('These credentials do not match our records.'),
                        ]);
                }
            };
        });

        RateLimiter::for('login', function (Request $request) {
            $throttleKey = Str::transliterate(Str::lower($request->input(Fortify::username())).'|'.$request->ip());

            return Limit::perMinute(5)->by($throttleKey);
        });

        RateLimiter::for('two-factor', function (Request $request) {
            return Limit::perMinute(5)->by($request->session()->get('login.id'));
        });
    }
}
