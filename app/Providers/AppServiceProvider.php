<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\Invoice;
use App\Models\GlobalConfig;
use App\Observers\InvoiceObserver;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\View;

use App\Services\UserPreferenceService;
use App\Services\NotificationService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
        $this->app->singleton(UserPreferenceService::class);
        $this->app->singleton(NotificationService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Set application time zone from global config if available
        try {
            if (class_exists('\App\Models\GlobalConfig') &&
                method_exists('\App\Models\GlobalConfig', 'getTimeZone')) {
                date_default_timezone_set(GlobalConfig::getTimeZone());
            }
        } catch (\Exception) {
            // If there's an error (like during migrations), use the default timezone
            // No need to log this as it's expected during initial setup
        }

        // Register invoice observer
        Invoice::observe(InvoiceObserver::class);
        Paginator::useTailwind();

        Blade::directive('perms', function ($expression) {
            return "<?php if (auth()->check() && collect(explode(',', {$expression}))->some(fn(\$perm) => auth()->user()->hasPermission(trim(\$perm)) )): ?>";
        });

        Blade::directive('endperms', function () {
            return "<?php endif; ?>";
        });

        // Quill Editor Assets - only include once per page
        Blade::directive('importQuill', function () {
            return "<?php echo view('components.quill-assets')->render(); ?>";
        });

        // Make the pagination preference available globally
        View::composer('*', function () {
            $pagination = session('pagination', 10); // Default to 10
            request()->merge(['pagination' => $pagination]);
        });
    }
}
