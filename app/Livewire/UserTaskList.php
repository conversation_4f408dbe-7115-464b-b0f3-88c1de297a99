<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\TaskCompletion;
use App\Models\GlobalConfig;

class UserTaskList extends Component
{

        /**
     * The tasks for today.
     */
    public $tasksForToday;

    /**
     * Create a new component instance.
     */
    public function __construct()
    {
        // Don't initialize if tasks feature is disabled
        if (!GlobalConfig::isTasksEnabled()) {
            $this->tasksForToday = collect([]);
            return;
        }

        // Get today's date
        $today = now()->toDateString();

        // Fetch today's tasks using the user's `getTaskOccurrences` method
        $taskOccurrences = auth()->user()->getTaskOccurrences($today, $today);

        // Extract tasks specifically for today
        $this->tasksForToday = collect($taskOccurrences[$today]['tasks'] ?? []);
    }


    public function render()
    {
        // Don't render if tasks feature is disabled
        if (!GlobalConfig::isTasksEnabled()) {
            return null;
        }

        return view('livewire.user-task-list');
    }

        /**
     * Get the gradient color for today's task completion based on the percentage of tasks completed.
     */
    public function getGradientColorForToday($completed, $total)
    {
        if ($total === 0) {
            return 'rgb(128, 128, 128)'; // Gray for no tasks
        }
        $percent = ($completed / $total) * 100;
        $startColor = ['r' => 113, 'g' => 0, 'b' => 0]; // Red
        $midColor = ['r' => 111, 'g' => 100, 'b' => 0]; // Yellow
        $endColor = ['r' => 0, 'g' => 121, 'b' => 16]; // Green

        if ($percent <= 50) {
            $ratio = $percent / 50;
            $r = $startColor['r'] + $ratio * ($midColor['r'] - $startColor['r']);
            $g = $startColor['g'] + $ratio * ($midColor['g'] - $startColor['g']);
            $b = $startColor['b'] + $ratio * ($midColor['b'] - $startColor['b']);
        } else {
            $ratio = ($percent - 50) / 50;
            $r = $midColor['r'] + $ratio * ($endColor['r'] - $midColor['r']);
            $g = $midColor['g'] + $ratio * ($endColor['g'] - $midColor['g']);
            $b = $midColor['b'] + $ratio * ($endColor['b'] - $midColor['b']);
        }

        return "rgb($r, $g, $b)";
    }


    public function changeTaskCompletionStatus($taskId)
    {

    }

    public function toggleTaskCompletion($taskId)
    {
        // Don't process if tasks feature is disabled
        if (!GlobalConfig::isTasksEnabled()) {
            return;
        }

        $today = now()->toDateString();
        $task = $this->tasksForToday->firstWhere('task_id', $taskId);

        if ($task) {
            $completedCount = $task['completed_count'] > 0 ? 0 : 1;
            TaskCompletion::updateOrCreate(
                [
                    'task_id' => $taskId,
                    'user_id' => auth()->id(),
                    'due_date' => $today,
                ],
                ['status' => $completedCount ? 'complete' : 'incomplete']
            );

            $task['completed_count'] = $completedCount;
            $this->tasksForToday = $this->tasksForToday->map(function ($t) use ($task) {
                return $t['task_id'] === $task['task_id'] ? $task : $t;
            });

            // Dispatch event to update task summary
            $this->dispatch('taskCompletionToggled');
        }
    }

}
