<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\On; 

class TaskSummary extends Component
{
    public $summary;
    public $user;

    public function mount($user)
    {
        $this->user = $user;
        $this->updateSummary();
    }

    #[On('taskCompletionToggled')] 
    public function updateSummary()
    {
        $this->summary = $this->user->getDailyTaskSummary();
    }

    public function render()
    {
        return view('livewire.task-summary');
    }
}
