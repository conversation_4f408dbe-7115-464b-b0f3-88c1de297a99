<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\TaskCompletion;

class TaskItem extends Component
{
    public $task;
    public $date;

    public function mount($task, $date)
    {
        $this->task = $task;
        $this->date = $date;
    }

    public function toggleTaskCompletion($taskId)
    {
        $completedCount = $this->task['completed_count'] > 0 ? 0 : 1;
        TaskCompletion::updateOrCreate(
            [
                'task_id' => $taskId,
                'user_id' => auth()->id(),
                'due_date' => $this->date,
            ],
            ['status' => $completedCount ? 'complete' : 'incomplete']
        );

        $this->task['completed_count'] = $completedCount;
        $this->dispatch('taskCompletionToggled', task: $this->task, date: $this->date);
    }

    public function render()
    {
        return view('livewire.task-item');
    }
}
