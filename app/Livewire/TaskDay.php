<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\On;

class TaskDay extends Component
{
    public $date;
    public $tasks;
    public $formattedDate;
    public $isToday;
    public $completed;
    public $total;

    public function mount($date, $tasks, $formattedDate, $isToday)
    {
        $this->date = $date;
        $this->tasks = $tasks;
        $this->formattedDate = $formattedDate;
        $this->isToday = $isToday;
        $this->completed = $tasks['total_complete'] ?? 0;
        $this->total = $tasks['total_tasks'] ?? 0;
    }

    #[On('taskCompletionToggled')]
    public function updateBadge($task, $date)
    {
        if ($this->date !== $date) {
            return;
        }

        $this->tasks['tasks'] = collect($this->tasks['tasks'])->map(function ($t) use ($task) {
            return $t['task_id'] === $task['task_id'] ? $task : $t;
        })->toArray();

        $this->completed = collect($this->tasks['tasks'])->sum('completed_count');
    }

    public function getGradientColor($completed, $total)
    {
        if ($total === 0) {
            return 'rgb(128, 128, 128)'; // Gray for no tasks
        }
        $percent = ($completed / $total) * 100;
        $startColor = ['r' => 113, 'g' => 0, 'b' => 0]; // Red
        $midColor = ['r' => 111, 'g' => 100, 'b' => 0]; // Yellow
        $endColor = ['r' => 0, 'g' => 121, 'b' => 16]; // Green

        if ($percent <= 50) {
            $ratio = $percent / 50;
            $r = $startColor['r'] + $ratio * ($midColor['r'] - $startColor['r']);
            $g = $startColor['g'] + $ratio * ($midColor['g'] - $startColor['g']);
            $b = $startColor['b'] + $ratio * ($midColor['b'] - $startColor['b']);
        } else {
            $ratio = ($percent - 50) / 50;
            $r = $midColor['r'] + $ratio * ($endColor['r'] - $midColor['r']);
            $g = $midColor['g'] + $ratio * ($endColor['g'] - $midColor['g']);
            $b = $midColor['b'] + $ratio * ($endColor['b'] - $midColor['b']);
        }

        return "rgb($r, $g, $b)";
    }

    public function render()
    {
        return view('livewire.task-day');
    }
}
