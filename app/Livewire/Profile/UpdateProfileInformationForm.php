<?php

namespace App\Livewire\Profile;

use App\Models\Image;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Livewire\Component;
use Livewire\WithFileUploads;

class UpdateProfileInformationForm extends Component
{
    use WithFileUploads;

    /**
     * The component's state.
     *
     * @var array
     */
    public $state = [];

    /**
     * The new profile photo.
     *
     * @var mixed
     */
    public $photo;

    /**
     * The user instance.
     *
     * @var \App\Models\User
     */
    public $user;

    /**
     * Indicates if the verification email was sent.
     *
     * @var bool
     */
    public $verificationLinkSent = false;

    /**
     * Prepare the component.
     *
     * @return void
     */
    public function mount()
    {
        $this->user = Auth::user();

        $this->state = $this->user->withoutRelations()->toArray();
    }

    /**
     * Update the user's profile information.
     *
     * @return void
     */
    public function updateProfileInformation()
    {
        $this->resetErrorBag();

        $this->validate([
            'state.name' => ['required', 'string', 'max:255'],
            'state.email' => ['required', 'email', 'max:255', Rule::unique('users', 'email')->ignore($this->user->id)],
            'photo' => ['nullable', 'mimes:jpg,jpeg,png,webp', 'max:1024'], // 1MB Max
        ]);

        // Handle profile photo upload if provided
        if ($this->photo) {
            // Store the image using the Image model
            $image = new Image();
            $image->name = $this->user->name . ' Profile Photo';
            $image->description = 'Profile photo for ' . $this->user->name;
            $image->type = 'profile';
            $image->save();

            // Store the image file
            $path = $this->photo->storeAs('img', $image->id . '.' . $this->photo->getClientOriginalExtension(), 'public');
            $image->image_path = $path;
            $image->save();

            // Generate thumbnails for the image
            \App\Models\Image::generateThumbnails($path);

            // Update the user's profile_photo_id
            $this->user->profile_photo_id = $image->id;

            // Reset the photo property to clear the file input
            $this->reset('photo');
        }

        // Update other user information
        $this->user->name = $this->state['name'];
        $this->user->email = $this->state['email'];
        $this->user->save();

        // Refresh the user instance to get the updated profile photo
        $this->user = $this->user->fresh(['profilePhoto']);

        // Show success message
        session()->flash('message', 'Profile information updated successfully.');

        $this->dispatch('saved');
    }

    /**
     * Send email verification notification.
     *
     * @return void
     */
    public function sendEmailVerification()
    {
        $this->user->sendEmailVerificationNotification();

        $this->verificationLinkSent = true;
    }

    /**
     * Render the component.
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('livewire.profile.update-profile-information-form');
    }
}
