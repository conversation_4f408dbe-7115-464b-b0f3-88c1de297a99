# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# ETRFlow2 - Laravel Business Management Platform

## Project Overview

**ETRFlow2** is a comprehensive business management platform built with Laravel 11, focusing on electronics recycling and destruction services. The application provides inventory management, customer relationships, pickup scheduling, certificate generation, time tracking, task management, and administrative tools for service-based businesses in the electronics destruction industry.

## Technology Stack

### Backend
- **Framework**: Laravel 11.31 (PHP 8.2+)
- **Authentication**: Laravel Jetstream with Fortify
- **Database**: MariaDB/MySQL with extensive migrations
- **Queue System**: Laravel Queue for background jobs
- **File Storage**: Laravel Storage with public/private disk support
- **PDF Generation**: DomPDF, TCPDF, FPDI for certificate generation
- **Python Integration**: PyPDF2 for PDF merging operations

### Frontend
- **CSS Framework**: TailwindCSS 4.1.7 with DaisyUI 5.0.37
- **JavaScript**: React 19 with Vite 6.2.6 build system
- **Rich Text Editor**: TipTap editor for content management
- **Interactive Components**: Livewire 3.5 for dynamic components
- **Icons**: FontAwesome Sharp icons (comprehensive icon library)
- **Image Handling**: PhotoSwipe for galleries, browser-image-compression
- **Date/Time**: Flatpickr for date selection
- **File Upload**: Dropzone for drag-and-drop uploads

# Additional Instructions
@~/docs/STYLE_GUIDE.md
@~/docs/NOTIFICATION_SYSTEM.md
@~/docs/header-system.md

### External Integrations
- **Maps**: Google Maps API for address autocomplete and location services
- **AI**: OpenAI GPT integration for inventory description generation
- **Communication**: Laravel Mail system for notifications
- **Push Notifications**: Browser push notifications with WebPush library and VAPID keys
- **Microsoft 365**: Outlook calendar integration for pickup event synchronization

## Project Structure

### Core Application Areas

#### 1. **Inventory Management**
- **Path**: `app/Models/Inventory.php`, `app/Http/Controllers/InventoryController.php`
- Comprehensive inventory tracking with categories, checklists, and images
- AI-powered description generation for items
- Asset tag generation and management
- Condition tracking and units management

#### 2. **Customer Relationship Management**
- **Path**: `app/Models/Customer.php`, `app/Http/Controllers/CustomerController.php`
- Customer accounts with residential/business classification
- Contact information and address management
- Customer-specific discounts and pricing
- Integration with pickup requests and invoicing

#### 3. **Pickup Request System**
- **Path**: `app/Models/PickupRequest.php`, `app/Http/Controllers/PickupRequestController.php`
- **Controllers**: `PickupRequestCustomerController.php`, `PickupRequestScheduleController.php`
- **Documentation**: `docs/pickups.md`, `docs/pickup-details-json.md`, `docs/PICKUP_NOTIFICATIONS_IMPLEMENTATION.md`
- **Features**: Request-first architecture with comprehensive guest and staff workflows
- **Guest Interface**: Multi-step public form with guided item collection and real-time availability
- **Staff Interface**: Customer assignment, scheduling, and status management workflows
- **Automation**: Automated reminder system with email notifications and token-based customer management
- **Integration**: Deep calendar integration with conflict prevention and availability checking

#### 4. **Certificate & Device Management**
- **Path**: `app/Models/Certificate.php`, `app/Models/Device.php`
- Chain of custody documentation
- Device destruction certificates
- PDF generation with contract templates
- Signature capture and validation

#### 5. **Time Tracking & Payroll**
- **Path**: `app/Models/TimeCard.php`, `app/Models/TimePunch.php`
- **Documentation**: `docs/timetracker.md`, `docs/mobile-timeclock-*.md`
- Employee time clock functionality
- Break tracking and overtime calculation
- Timesheet reporting and management
- Mobile-optimized interface

#### 6. **Task Management**
- **Path**: `app/Models/Task.php`, `app/Livewire/TaskSummary.php`
- Task assignment and tracking
- Recurring task patterns
- Task completion status and comments
- Livewire-powered interactive task lists

#### 7. **Invoicing & Financial**
- **Path**: `app/Models/Invoice.php`, `app/Models/LineItem.php`
- Invoice generation and management
- Line item tracking with department assignment
- Discount management (customer, invoice, line-item level)
- Tax policy integration

#### 8. **Calendar & Event Management**
- **Path**: `app/Models/Calendar.php`, `app/Models/Event.php`
- Multi-calendar support with sharing capabilities
- Recurring event patterns
- Event categorization and pickup integration
- Blockout event management

#### 9. **Custom Form System**
- **Path**: `app/Models/Form.php`, `app/Models/FormField.php`, `app/Models/FormSubmission.php`
- **Controllers**: `FormController.php`, `PublicFormController.php`, `FormSubmissionController.php`
- **Documentation**: `docs/custom-forms-system.md`
- Dynamic form builder with multiple field types
- Service agreement forms with automatic discount application
- Public submission interface with signature capture
- Admin review and approval workflow with PDF generation
- Customer account linking and creation
- Email PDF delivery to customers after approval
- Professional PDF generation with signatures and branding
- Configurable notification system with templated emails

### Key Configuration Files

#### Environment Configuration
- **File**: `.env` (copy from `.env.example`)
- Database connection (MariaDB recommended)
- Python executable path for PDF operations
- Google Maps API configuration
- OpenAI API settings for AI features
- Mail configuration for notifications

#### Build Configuration
- **Vite**: `vite.config.js` - Asset bundling with React and TailwindCSS
- **TailwindCSS**: `tailwind.config.js` - DaisyUI themes and custom utilities
- **Composer**: `composer.json` - PHP dependencies and scripts
- **NPM**: `package.json` - Frontend dependencies and build scripts

### Database Architecture

#### Key Tables & Relationships
- **users** → User accounts with role-based permissions
- **user_groups** → Permission groups (Admin, Staff, etc.)
- **customers** → Customer accounts and contact information
- **inventory** → Items with categories, images, and tracking
- **pickup_requests** → Pickup scheduling and management
- **certificates** → Document generation and device tracking
- **time_cards/time_punches** → Employee time tracking
- **tasks** → Task management with assignments and recurrence
- **calendars/events** → Calendar system with sharing
- **invoices/line_items** → Financial tracking and billing
- **forms/form_fields/form_submissions** → Custom form system with dynamic fields

#### Migration System
- **Path**: `database/migrations/`
- Comprehensive migration history from 2024-12-15 to 2025-06-21
- Foreign key relationships with cascade deletion
- Seeder files for initial data setup

#### Email Templates
- **Path**: `database/seeders/EmailTemplateSeeder.php`
- Pre-configured email templates for system notifications
- Form submission notifications (basic and full data)
- PDF delivery emails for customer communication
- Pickup request notifications and confirmations
- Daily timeclock reports for administrators

### Frontend Architecture

#### View Structure
- **Layouts**: `resources/views/layouts/app.blade.php` - Main application layout
- **Components**: `resources/views/components/` - Reusable UI components
- **Pages**: Organized by feature area (inventory, customers, pickups, etc.)

#### Styling System
- **Documentation**: `docs/STYLE_GUIDE.md` (comprehensive 800+ line guide)
- **Philosophy**: DaisyUI-first with mobile-responsive patterns
- **Patterns**: Card-based layouts, gradient headers, responsive grids
- **Components**: Pagination, search filters, data tables, forms

#### JavaScript Organization
- **React Components**: `resources/js/app.jsx` - Main React entry point
- **Utility Scripts**: Image galleries, timecard editing, mobile compression
- **Interactive Features**: TipTap rich text editor, Flatpickr date selection

### Permission System

#### Architecture
- **Models**: `app/Models/Permission.php`, `app/Models/UserGroup.php`
- **Middleware**: `app/Http/Middleware/CheckPermission.php`
- **Custom Directive**: `@perms()` Blade directive for view-level permissions

#### Permission Scopes
- **General**: `view_dashboard`, `is_admin`
- **Inventory**: `view_inventory_items`, `create_inventory_items`, etc.
- **Customers**: `view_customer_accounts`, `edit_customer_accounts`, etc.
- **Pickups**: `view_pickup_requests`, `manage_pickup_requests`, `assign_pickup_customers`, etc.
- **Timeclock**: `view_timecards`, `edit_own_timecards`, etc.
- **Forms**: `manage_forms`, `view_form_submissions`, `manage_form_submissions`, etc.

### File Storage & Assets

#### Storage Organization
- **Public Storage**: `storage/app/public/` - User-uploaded images and files
- **Private Storage**: `storage/app/private/` - Sensitive documents and certificates
- **Static Assets**: `public/img/`, `public/fontawesome/` - Application assets

#### Python Integration
- **Scripts**: `scripts/merge_pdfs.py` - PDF merging for certificates
- **Requirements**: `scripts/requirements.txt` - PyPDF2 dependency
- **Configuration**: Environment variables for Python path and packages

## Development Workflow

### Setup Commands
```bash
# Initial setup
composer install
npm install
cp .env.example .env
php artisan key:generate
php artisan storage:link

# Database setup
php artisan migrate
php artisan db:seed

# Development server (concurrent)
composer run dev  # Runs server, queue, and vite simultaneously
```

### Build Commands
```bash
# Development
npm run dev        # Vite development server
php artisan serve  # Laravel development server

# Production
npm run build      # Build optimized assets
```

### Testing Commands
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run with coverage (requires Xdebug)
php artisan test --coverage

# Run specific test file
php artisan test tests/Feature/ExampleTest.php

# Run tests using PHPUnit directly
vendor/bin/phpunit
```

### Code Quality Commands
```bash
# Laravel Pint (code formatting)
vendor/bin/pint

# Check code style without fixing
vendor/bin/pint --test
```

### Key Artisan Commands
```bash
# User management
php artisan users:add-user-to-group {email} {group}
php artisan users:enable-user
php artisan permissions:sync

# Database operations
php artisan migrate
php artisan migrate:fresh --seed  # Reset database with seed data
php artisan migrate:status
php artisan db:seed

# Email and notification system
php artisan pickups:send-reminders           # Send pickup confirmation reminders
php artisan pickups:send-confirmation-email {pickup_request_id}  # Send individual confirmation email
php artisan timeclock:send-daily-report     # Send daily timeclock reports
php artisan timeclock:auto-clock-out        # Auto clock-out inactive employees

# Maintenance
php artisan queue:listen
php artisan config:clear
php artisan storage:link          # Create storage symlink

# Production deployment
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## Important Architectural Patterns

### 1. **Request-First Architecture** (Pickup System)
- All pickup information captured in pickup requests first
- Calendar events created from pickup requests
- Ensures data consistency and audit trail

### 2. **Permission-Based Security**
- Route-level middleware protection
- View-level `@perms()` directive checks
- Granular permissions with scope-based organization

### 3. **Auto-Save Patterns**
- Real-time form saving with visual feedback
- Debounced input handling (500ms text, 1000ms rich text)
- Accessibility-compliant with manual save options

### 4. **Mobile-First Responsive Design**
- Two-layout system (mobile cards, desktop grids)
- Touch-friendly interfaces with large tap targets
- Progressive enhancement for desktop features

### 5. **Component-Based Views**
- Reusable Blade components for consistent UI
- Dynamic sidebar menu generation
- Pagination component with session persistence

### 6. **Email Template System Architecture**
- **Models**: `app/Models/EmailTemplate.php` with dynamic template definitions
- **Service**: `app/Services/EmailTemplateService.php` for template rendering and variable substitution
- **Templates**: 7 core email types (pickup reminders, form notifications, timeclock reports)
- **Scheduling**: Automated email sending via Laravel scheduler (pickup reminders, daily reports)
- **Integration**: Seamless integration with notification system and user group targeting
- **Administration**: Full CRUD interface for email template management
- **Rendering**: Variable substitution system with template-specific data processing

### 7. **Appointment Management System**
- **Public Interface**: Token-based appointment management for customers
- **Dual Routes**: Separate flows for management page vs direct email confirmation
- **Status Tracking**: Comprehensive tracking of reminder sent, confirmed, and cancelled states
- **Token Security**: Time-limited management tokens for customer access
- **Notification Integration**: Real-time staff notifications on customer actions

### 8. **Image Dropzone Component System**
- **Component**: `resources/views/components/image-dropzone.blade.php` - Reusable drag-and-drop image upload
- **JavaScript**: `public/js/image-dropzone.js` - Client-side compression and file handling
- **Documentation**: `docs/image-dropzone-component.md` - Detailed usage and configuration guide
- **Features**: Client-side WebP compression, mobile camera integration, thumbnail generation
- **Usage**: Mobile payouts, pickup requests, and any form requiring image uploads
- **Security**: Permission-based file access with dedicated thumbnail routes

## Pickup Request System - Comprehensive Architecture

### System Overview
The pickup request system is the cornerstone of ETRFlow2's service delivery, providing a complete end-to-end solution for managing electronics pickup operations. Built on a request-first architecture, it ensures data consistency, comprehensive tracking, and seamless integration across all business processes.

### Core Components

#### 1. **Data Models & Database Architecture**
- **PickupRequest Model**: Central data structure with comprehensive field validation
- **Database Fields**: 
  - Contact information (name, business, email, phone)
  - Location details (address, property location, accessibility level)
  - Guided item collection (load size, item types, specifics, driver instructions)
  - Scheduling (preferred pickup date, status, customer/event links)
  - Metadata (submission tracking, management tokens, reminder timestamps)
- **Dual Field System**: Modern database fields + JSON storage for backward compatibility
- **Status Workflow**: 5-stage progression (incoming → pending → confirmed → completed/cancelled)
- **Foreign Key Relationships**: Links to customers, events, and images with cascade handling

#### 2. **Guest Interface Architecture**
- **Public Access**: Available at `/pickup-request` without authentication
- **Multi-Step Process**: 6-step guided workflow with progressive disclosure
  1. **Welcome**: Customizable HTML introduction with process overview
  2. **Contact Info**: Real-time validation with required field indicators
  3. **Time Selection**: AJAX-powered availability checking with calendar integration
  4. **Pickup Details**: Google Maps autocomplete with property location assessment
  5. **Item Details**: Guided load size classification with multi-select item types
  6. **Agreement**: Optional terms acceptance with signature capture
- **Frontend Features**:
  - Mobile-responsive design with touch-friendly interfaces
  - Real-time availability checking and conflict prevention
  - Progressive enhancement with graceful JavaScript degradation
  - Form persistence across multi-step process
  - Comprehensive error handling with user-friendly messaging

#### 3. **Staff Management Interface**
- **Request Processing**: Complete workflow for guest request management
- **Customer Assignment**: Step-by-step wizard for linking to existing customers
- **Status Management**: Action-based workflow progression with validation
- **Scheduling Integration**: Calendar event creation with staff assignments
- **Auto-Save System**: Real-time form persistence with visual feedback
- **Bulk Operations**: Filtering, search, and batch management capabilities
- **PDF Generation**: Daily pickup reports with distance calculations

#### 4. **Automated Reminder System**
- **Scheduling**: Hourly cron job via Laravel scheduler
- **Dual Execution**: Both job-based and command-based for reliability
- **Email Templates**: Configurable templates with variable substitution
- **Token Management**: Secure, time-limited customer access tokens
- **Status Tracking**: Comprehensive reminder and confirmation tracking
- **Multi-Channel**: Email notifications + in-app notifications

#### 5. **Calendar Integration**
- **Event Creation**: Pickup requests convert to calendar events with full data linking
- **Conflict Prevention**: Multi-layer validation against events, requests, and buffer time
- **Availability Engine**: Real-time slot calculation with business rules
- **Visual Indicators**: Color-coded events with load size and customer information
- **Week View**: FullCalendar integration with 3-month navigation range
- **Blockout System**: Manual blocking of unavailable times

### Business Logic & Workflows

#### Guest Workflow
1. **Public Access**: Multi-step form with guided item collection
2. **Real-Time Validation**: Availability checking and conflict prevention
3. **Data Submission**: Comprehensive request creation with metadata tracking
4. **Status Assignment**: Automatically set to "incoming" for staff processing
5. **Confirmation**: Email notification with submission details

#### Staff Workflow
1. **Request Review**: Monitor incoming requests with filtering and search
2. **Customer Assignment**: Link to existing customers or create new profiles
3. **Scheduling**: Convert to calendar events with staff assignments
4. **Status Management**: Progress through workflow stages with action buttons
5. **Communication**: Send confirmation emails with customer management links
6. **Completion**: Mark as completed with validation and logging

#### Automated Processes
1. **Reminder System**: Hourly checks for pickups within 48-hour window
2. **Email Notifications**: Template-based emails with variable substitution
3. **Token Generation**: Secure customer access for appointment management
4. **Status Tracking**: Database field updates with legacy JSON support
5. **Activity Logging**: Comprehensive audit trail with detailed metadata

### Technical Implementation

#### Controllers & Routes
- **PickupRequestController**: Main CRUD operations and status management
- **PickupRequestCustomerController**: Customer assignment workflow
- **PickupRequestScheduleController**: Multi-step scheduling process
- **GuestPickupController**: Public form submission handling
- **API Endpoints**: RESTful endpoints for AJAX operations

#### Frontend Architecture
- **View Components**: Reusable partials for consistent UI/UX
- **JavaScript Classes**: PickupRequestForm for multi-step navigation
- **Calendar Integration**: FullCalendar with custom event rendering
- **Auto-Save System**: Debounced form persistence with visual feedback
- **Mobile Optimization**: Touch-friendly interface with gesture support

#### Background Services
- **Artisan Commands**: `pickups:send-reminders`, `pickups:send-confirmation-email`
- **Queue Jobs**: `SendPickupConfirmationReminder` for scalable processing
- **Email Service**: `EmailTemplateService` for template rendering
- **Notification Service**: Integration with user group targeting

### Configuration & Settings

#### Global Configuration
- **Pickup Calendar**: Dedicated calendar for pickup event management
- **Availability Windows**: Business hours per day of week
- **Scheduling Rules**: Lead time, buffer time, and conflict prevention
- **Event Settings**: Duration, intervals, and staff assignments
- **Notification Settings**: User group targeting for alerts

#### Email Templates
- **pickup_request_notification**: Staff notifications for new requests
- **pickup_confirmation**: Customer appointment confirmations
- **pickup_confirmation_reminder**: 48-hour advance reminders
- **pickup_final_reminder**: Final reminders for confirmed appointments

### Security & Validation

#### Access Control
- **Permission-Based**: Route and view-level permission checking
- **Token Security**: Time-limited customer access tokens
- **CSRF Protection**: Secure form submissions with token validation
- **Input Validation**: Comprehensive server-side validation rules

#### Data Protection
- **Sanitization**: Clean user input handling
- **File Upload Security**: Validated image uploads with size limits
- **Database Constraints**: Foreign key relationships with cascade protection
- **Activity Logging**: Comprehensive audit trail for compliance

### Performance & Optimization

#### Database Optimization
- **Indexing**: Proper indexes on frequently queried fields
- **Query Optimization**: Efficient availability checking algorithms
- **Relationship Loading**: Eager loading for reduced N+1 queries
- **Caching**: Session-based draft storage for form persistence

#### Frontend Performance
- **Lazy Loading**: Components load only when needed
- **Debounced Operations**: Prevent excessive API calls
- **Image Optimization**: Client-side compression and WebP conversion
- **Progressive Enhancement**: Core functionality works without JavaScript

### Integration Points

#### Calendar System
- **Event Linking**: Pickup requests create calendar events with full data binding
- **Conflict Detection**: Real-time checking against all events and requests
- **Driver Assignment**: Staff assignment with availability tracking
- **Visual Integration**: Custom event rendering with pickup-specific styling

#### Customer Management
- **Profile Linking**: Guest requests can be linked to existing customers
- **Data Population**: Customer information auto-fills contact fields
- **Address Integration**: Customer addresses available for pickup locations
- **Communication**: Integrated email and notification systems

#### Notification System
- **Multi-Channel**: Email, in-app, browser push, and user group notifications
- **Template System**: Configurable email templates with variable substitution
- **Real-Time Alerts**: Immediate staff notifications for customer actions
- **Scheduling**: Automated reminder system with comprehensive tracking
- **Browser Push**: Cross-platform push notifications with VAPID authentication
- **Device Management**: Multi-device subscription tracking and management

This comprehensive architecture ensures scalable, maintainable, and user-friendly pickup request management that supports both current operations and future enhancements.

## Key Business Rules

### Customer Types
- **Residential Customer**: Individual consumers
- **Business Customer**: Commercial accounts
- **Government Customer**: Government entities

### Load Classifications (Pickup System)
- **Small Load**: 1-2 items, fits in midsize SUV
- **Medium Load**: Pallets/totes, 1-person movable
- **Large Load**: Multiple pallets, requires truck/team

### Certificate Generation
- Chain of custody documentation
- Device destruction certificates
- Multi-step PDF generation with signatures
- Python-based PDF merging for complex documents

### Time Tracking Rules
- Break time tracking with automatic deductions
- Overtime calculation based on daily/weekly thresholds
- Sick and vacation time integration
- Mobile-optimized punch clock interface

### Form System & Service Agreements
- Dynamic form builder supporting multiple field types
- Service agreement forms linked to customer discounts
- 1-year discount auto-applied upon agreement approval
- Guest submissions matched to existing customers by email
- Signature capture required for agreements
- Admin review workflow before customer linking

### Pickup Request System Architecture
- **Request-First Pattern**: All pickup information captured in pickup requests before calendar events
- **Status Workflow**: incoming → pending → confirmed → completed/cancelled progression
- **Guest Interface**: 6-step public form with guided item collection and real-time availability
- **Staff Interface**: Customer assignment, scheduling, and comprehensive status management
- **Dual Field System**: Modern database fields + legacy JSON support for backward compatibility
- **Automated Reminders**: Hourly cron job with 48-hour advance notice system
- **Token-Based Management**: Secure customer appointment management with time-limited tokens
- **Comprehensive Logging**: Full activity tracking with detailed audit trail
- **Multi-Channel Notifications**: Email templates, in-app notifications, and user group targeting

## Documentation Files

### Existing Documentation
- **STYLE_GUIDE.md**: Comprehensive UI/UX patterns and component library
- **activity-logging-system.md**: Activity tracking and audit trail
- **header-system.md**: App layout header configuration
- **image-dropzone-component.md**: Image upload component usage and configuration
- **microsoft-365-integration.md**: Complete setup guide for Microsoft 365 Outlook calendar integration
- **mobile-timeclock-*.md**: Time tracking system documentation
- **pickup-details-json.md**: Pickup request data structure
- **pickups.md**: Pickup system architecture and workflows
- **timetracker.md**: Time tracking features and usage

### Development Guidelines

#### Code Style
- Follow Laravel conventions and PSR standards
- Use DaisyUI classes for all styling (no custom CSS)
- Implement mobile-first responsive design
- Use `@perms()` directive for permission checks

#### Database Practices
- Always use migrations for schema changes
- Include proper foreign key relationships
- Use model factories for testing data
- Implement soft deletes where appropriate

#### Frontend Patterns
- Card-based layouts with gradient headers
- Responsive mobile/desktop view toggles
- Consistent spacing with TailwindCSS utilities
- Auto-save forms with visual feedback

#### Security Considerations
- Route-level permission middleware
- CSRF protection on all forms
- File upload validation and security
- Sanitized user input handling

#### Adding New Permissions
- Add permissions to `config/permissions.php` under appropriate scope
- Run `php artisan permissions:sync` to sync to database
- Admin group automatically receives new permissions
- Use middleware in routes: `->middleware('permission:permission_name')`
- Use in views: `@perms('permission_name')`

#### Email Template Development
- Templates are stored in database via `EmailTemplate` model
- Use `EmailTemplateService` for rendering with variable substitution
- Template definitions in `EmailTemplate::templateDefinitions()` method
- All templates support common variables: `{app_name}`, `{current_date}`, `{current_datetime}`
- Template-specific processing handled in `EmailTemplateService::processTemplateSpecificData()`
- Use `TemplatedMail` mailable class for consistent email sending

#### Appointment Management Integration
- Use Event model's `start_date` field, not PickupRequest's `preferred_pickup_date` for actual scheduled times
- Customer confirmation updates both new fields (confirmed_at) and legacy JSON (pickup_details)
- Management tokens expire based on GlobalConfig settings
- Direct confirmation bypasses management page for better UX from email clicks

## Common Tasks & Troubleshooting

### Adding New Features
1. Create migration files for database changes
2. Generate models with appropriate relationships
3. Create controllers with permission checks
4. Design views following style guide patterns
5. Add route definitions with middleware
6. Update permission system if needed

### Permission Issues
- Run `php artisan permissions:sync` to update permissions
- Check middleware configuration in routes
- Verify user group assignments
- Use `@perms()` directive in views

### Asset Build Issues
- Clear browser cache and rebuild with `npm run build`
- Check Vite configuration for entry points
- Verify TailwindCSS purge settings
- Ensure FontAwesome assets are properly loaded

### PDF Generation Problems
- Verify Python executable path in `.env`
- Check PyPDF2 installation and version
- Review file permissions for storage directories
- Check Laravel logs for detailed error messages

### Database Migration Issues
- Foreign key constraints: Run customers table migration first
- User ID 1 requirement: Create admin user before running seeders
- Check migration status with `php artisan migrate:status`
- Use `php artisan migrate:fresh --seed` for clean reset

### Development Environment Issues
- Python path must use forward slashes in `.env` (even on Windows)
- Storage directory permissions: `chmod -R 775 storage/`
- Queue worker required for background jobs: `php artisan queue:listen`
- Use `composer run dev` for concurrent server/queue/vite development

## Browser Push Notifications System

### Architecture Overview
ETRFlow2 includes a comprehensive browser push notification system that delivers real-time notifications to users' desktop and mobile devices, even when the browser is closed or the site is not actively being viewed.

### Core Components

#### 1. **Service Worker** (`/public/service-worker.js`)
- Handles incoming push notifications from the server
- Manages notification display with custom styling and icons
- Supports click actions for navigation to relevant pages
- Implements vibration patterns for mobile devices
- Handles notification dismissal tracking

#### 2. **Browser Notifications Client** (`/public/js/browser-notifications.js`)
- **BrowserNotifications Class**: Main client-side interface
- **Permission Management**: Requests and manages browser notification permissions
- **Service Worker Registration**: Automatic registration and management
- **Subscription Handling**: Creates and manages push subscriptions
- **VAPID Integration**: Handles VAPID key exchange with server
- **Device Detection**: Identifies browser and platform for subscription metadata

#### 3. **NotificationService Integration** (`/app/Services/NotificationService.php`)
- **Automatic Push**: All notifications created via NotificationService automatically trigger push notifications
- **User Targeting**: Supports all notification targeting types (all users, specific users, user groups)
- **WebPush Library**: Uses `minishlink/web-push` for server-side push delivery
- **Error Handling**: Comprehensive error handling and subscription cleanup
- **Logging**: Detailed logging for debugging and monitoring

#### 4. **Database Schema**
- **NotificationSubscription Model**: Stores browser push subscriptions
- **Fields**: `user_id`, `endpoint`, `public_key`, `auth_token`, `device_info`, `is_active`
- **Relationships**: Links to User model with cascade deletion
- **Indexes**: Optimized for user lookups and active subscription filtering

#### 5. **API Endpoints** (`/app/Http/Controllers/Api/PushNotificationController.php`)
- **`GET /api/notifications/vapid-public-key`**: Returns VAPID public key for subscription
- **`POST /api/notifications/subscribe`**: Creates new push subscription
- **`POST /api/notifications/unsubscribe`**: Removes push subscription
- **`POST /api/notifications/test`**: Sends test notification to user
- **`GET /api/notifications/subscriptions`**: Lists user's active subscriptions
- **`POST /api/notifications/dismissed`**: Tracks notification dismissals (service worker)

### Configuration Requirements

#### Environment Variables
```env
# Web Push Notifications (VAPID Keys)
WEBPUSH_PUBLIC_KEY="your_vapid_public_key"
WEBPUSH_PRIVATE_KEY="your_vapid_private_key"
```

#### Generate VAPID Keys
```bash
php artisan webpush:generate-keys
```
This command generates VAPID (Voluntary Application Server Identification) keys required for browser push notifications. Add the generated keys to your `.env` file.

#### Services Configuration
VAPID keys are automatically configured in `config/services.php`:
```php
'webpush' => [
    'public_key' => env('WEBPUSH_PUBLIC_KEY'),
    'private_key' => env('WEBPUSH_PRIVATE_KEY'),
],
```

### User Interface Integration

#### Profile Page Integration
- **Settings Section**: Browser notification preferences in user profile (`/profile`)
- **Permission Status**: Real-time display of notification permission status
- **Device Management**: List of active notification subscriptions per device
- **Test Functionality**: Send test notifications to verify functionality
- **Browser Support Info**: Educational content about browser compatibility

#### Automatic Initialization
- **Global Loading**: JavaScript automatically loads on all pages via app layout
- **Service Worker Registration**: Automatic registration and permission checking
- **UI Updates**: Dynamic button states based on permission and subscription status

### Integration Points

#### Automatic Notification Delivery
All existing notification creation methods automatically trigger push notifications:
```php
// These all automatically send push notifications
NotificationHelper::notifyAllUsers($title, $message, $urgency, $linkUrl);
NotificationHelper::notifyUserGroups($userGroups, $title, $message);
NotificationHelper::notifyUsers($users, $title, $message);

// Direct service usage
$notificationService->sendToAllUsers($title, $message, $urgency);
```

#### Business Process Integration
- **Pickup Requests**: New pickup request notifications
- **Form Submissions**: Custom form submission alerts
- **Task Assignments**: Task notification delivery
- **System Alerts**: Administrative notifications
- **Invoice Notifications**: Financial transaction alerts

### Browser Compatibility

#### Supported Browsers
- **Desktop**: Chrome 50+, Firefox 44+, Safari 16+, Edge 17+
- **Mobile**: Chrome Android 50+, Firefox Android 44+, Safari iOS 16.4+
- **Features**: Notification display, click actions, icon support, vibration (mobile)

#### Progressive Enhancement
- **Graceful Degradation**: System works without push notifications enabled
- **Feature Detection**: Automatic detection of browser capabilities
- **Fallback Handling**: In-app notifications continue to work regardless

### Security & Privacy

#### VAPID Authentication
- **Server Identification**: VAPID keys identify the server to push services
- **Security**: Private key never exposed to client-side code
- **Standards Compliance**: Follows Web Push Protocol RFC standards

#### User Consent
- **Explicit Permission**: Users must explicitly grant notification permission
- **Revocable**: Users can disable notifications at any time
- **Privacy Compliant**: No personal data stored in push subscriptions

#### Subscription Management
- **Device Tracking**: Each browser/device creates separate subscription
- **Automatic Cleanup**: Expired subscriptions automatically deactivated
- **User Control**: Users can view and manage all active subscriptions

### Development Guidelines

#### Adding New Notification Types
```php
// Any notification created via NotificationService automatically gets push notifications
$notificationService = app(\App\Services\NotificationService::class);
$notification = $notificationService->sendToUserGroups(
    $userGroups,
    'Your Title',
    'Your message content',
    'high', // urgency level affects notification behavior
    route('your.route'), // click destination
    'Action Text' // action button text
);
```

#### Testing Push Notifications
```bash
# Send test notification to current user
# Available in user profile page UI, or via:
php artisan tinker
>>> $user = User::find(1);
>>> app(\App\Services\NotificationService::class)->sendTestPushNotification($user);
```

#### Debugging
- **Browser Console**: Comprehensive logging available in browser dev tools
- **Laravel Logs**: Server-side push delivery logged in `storage/logs/laravel.log`
- **Service Worker**: Use browser dev tools → Application → Service Workers for debugging

### Troubleshooting

#### Common Issues
- **Permission Denied**: User needs to grant notification permission in browser
- **No Subscriptions**: User must enable notifications in profile settings
- **Missing VAPID Keys**: Generate keys with `php artisan webpush:generate-keys`
- **Service Worker Errors**: Check browser console for registration issues

#### Browser Settings
- **Chrome**: Settings → Privacy and Security → Site Settings → Notifications
- **Firefox**: Preferences → Privacy & Security → Permissions → Notifications
- **Safari**: Preferences → Websites → Notifications

### Performance Considerations

#### Efficient Delivery
- **Batch Processing**: Multiple subscriptions processed efficiently
- **Error Handling**: Failed deliveries don't block other notifications
- **Subscription Cleanup**: Automatic removal of invalid subscriptions

#### Resource Usage
- **Minimal Client Impact**: Service worker runs independently
- **Server Efficiency**: Push delivery integrated into existing notification flow
- **Database Optimization**: Proper indexing for subscription queries

## Critical Setup Requirements

### Required Environment Variables
```env
# Database (MariaDB recommended)
DB_CONNECTION=mariadb
DB_HOST=127.0.0.1
DB_DATABASE=etrflow

# Python for PDF operations
PYTHON_PATH="/usr/bin/python3"  # or "C:/Python312/python.exe"
PYTHON_PACKAGES="/path/to/python/packages"

# API integrations
GOOGLE_MAPS_API_KEY=your_key
OPENAI_API_KEY=your_key

# Web Push Notifications (generate with: php artisan webpush:generate-keys)
WEBPUSH_PUBLIC_KEY="your_vapid_public_key"
WEBPUSH_PRIVATE_KEY="your_vapid_private_key"
```

### First-Time Setup Checklist
1. `composer install && npm install`
2. `cp .env.example .env` and configure database/API keys
3. `php artisan key:generate && php artisan storage:link`
4. `php artisan webpush:generate-keys` and add VAPID keys to `.env`
5. Create admin user with ID=1 via tinker (required for seeders)
6. `php artisan migrate && php artisan db:seed`
7. `php artisan users:add-user-to-group <EMAIL> Admin`
8. `php artisan permissions:sync`
9. `php artisan config:clear` to load new environment variables

This documentation provides a comprehensive foundation for understanding and working with the ETRFlow2 codebase. The application represents a mature, feature-rich business management platform with sophisticated architecture patterns and extensive customization capabilities.