import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite'
import postcss from '@tailwindcss/postcss';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.jsx',
                'resources/js/inventory-gallery.js',
                'resources/js/timecard-edit.js',
                'resources/js/edit-punch.js',
                'resources/js/mobile-payout-compression.js',
                'resources/js/monaco-email-editor.js',
                'resources/css/stars.css',
                'resources/css/flatpickr-custom.css',
            ],
            refresh: true,
        }),
        react(),
        tailwindcss({
            postcss: {
                plugins: [postcss],
            },
        }),
    ],
    build: {
        rollupOptions: {
            output: {
                manualChunks: undefined,
            },
        },
    },
    server: {
        host: '0.0.0.0',
        hmr: {
            host: 'localhost',
        },
    },
});
